'use strict'
require('./check-versions')()

process.env.NODE_ENV = 'production'
// npm run build -- stage   ,result: buildType=stage
let buildType = process.argv.splice(2)[0] || 'prod';

const ora = require('ora')
const rm = require('rimraf')
const path = require('path')
const chalk = require('chalk')
const webpack = require('webpack')
const config = require('../config')
// const webpackConfig = require('./webpack.prod.conf')
let webpackConfig;
if(buildType=='stage'){
  console.log('正在配置预生产打包文件…………');
  webpackConfig = require('./webpack.stage.conf')
}else{
  console.log('正在配置生产环境打包文件…………');
  webpackConfig = require('./webpack.prod.conf')
}

// const spinner = ora('building for production...')
// const spinner = ora('building for '+ process.env.NODE_ENV+ ' environment...')
const spinner = ora('building for '+ buildType+ ' environment...')
spinner.start()

rm(path.join(config.build.assetsRoot, config.build.assetsSubDirectory), err => {
  if (err) throw err
  webpack(webpackConfig, (err, stats) => {
    spinner.stop()
    if (err) throw err
    process.stdout.write(stats.toString({
      colors: true,
      modules: false,
      children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
      chunks: false,
      chunkModules: false
    }) + '\n\n')

    if (stats.hasErrors()) {
      console.log(chalk.red('  Build failed with errors.\n'))
      process.exit(1)
    }

    console.log(chalk.cyan('  Build complete.\n'))
    console.log(chalk.yellow(
      '  Tip: built files are meant to be served over an HTTP server.\n' +
      '  Opening index.html over file:// won\'t work.\n'
    ))
  })
})
