/*
 * @Author: your name
 * @Date: 2020-08-11 15:38:34
 * @LastEditTime: 2024-02-14 17:24:40
 * @LastEditors: SangShuaiKang
 * @Description: In User Settings Edit
 * @FilePath: \gove:\workspace\110.WHJK\trunk\ZhenHai\UI\gov\config\index.js
 */
"use strict";
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");

module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/",
    proxyTable: {
      "/apis": {
        // 预生产环境
        target: "https://stag-zhys3.dacyun.com/whjk-gov",
        // target: 'http://127.0.0.1:9879/whjk-gov',
        // 生产环境
        // target: "https://whp.zh.gov.cn/whjk-gov",
        // secure: false,  // 如果是https接口，需要配置这个参数
        changeOrigin: true, //是否跨域
        pathRewrite: {
          "^/apis": "" //需要rewrite重写的
        }
      },
      "/whjk-gov": {
        // 开发环境
        target: "https://stag-zhys3.dacyun.com/whjk-gov",
        // target: "https://whp.zh.gov.cn/whjk-gov",
        // target: 'http://127.0.0.1:9879/whjk-gov',
        // secure: true,  // 如果是https接口，需要配置这个参数
        // changeOrigin: true,  //是否跨域
        pathRewrite: {
          "^/whjk-gov": "" //需要rewrite重写的
        }
      }
    },

    // Various Dev Server settings
    host: "localhost", // can be overwritten by process.env.HOST
    port: 8001, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: "source-map",

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, "../dist/index.html"),

    // Paths
    assetsRoot: path.resolve(__dirname, "../dist"),
    assetsSubDirectory: "static",
    assetsPublicPath: "./",

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: "#source-map",

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ["js", "css"],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
};
