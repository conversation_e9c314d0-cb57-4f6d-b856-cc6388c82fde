{"name": "whjk-gov-fed", "version": "1.0.0", "description": "镇海区危险化学品道路运输监管系统结合了大数据、云计算、物联网等新技术，利用互联网+的思维，通过科技手段打通各部门之间管理局限，实现信息共享，实现对企业、运输车辆、人员、货物、装卸、运输等全过程监管，扫除监管死角和盲点，降低危险化学品装卸运输事故发生率。", "author": "guanghemm<gsj>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js", "prod": "npm run build -- prod", "stage": "npm run build -- stage"}, "dependencies": {"amfe-flexible": "^2.2.1", "animate.css": "~3.6.1", "axios": "~0.18.0", "blueimp-canvas-to-blob": "~3.14.0", "cropperjs": "~1.3.5", "dayjs": "~1.11.0", "echarts": "~4.1.0", "element-ui": "~2.12.0", "file-saver": "^2.0.5", "js-base64": "~2.4.9", "js-cookie": "~2.2.0", "lodash": "~4.17.10", "normalize.css": "~8.0.0", "nprogress": "~0.2.0", "qr-code-styling": "~1.6.0-rc.1", "qrcode": "~1.4.1", "qrcodejs2": "~0.0.2", "screenfull": "~3.3.2", "sm-crypto": "^0.3.12", "url-search-params-polyfill": "~5.0.0", "v-viewer": "~1.6.4", "viewerjs": "~1.0.0", "vue": "~2.5.16", "vue-json-editor": "^1.4.3", "vue-router": "~3.0.1", "vue-video-player": "~5.0.2", "vue2-transitions": "~0.3.0", "vuex": "~3.0.1", "wangeditor": "~4.7.15", "watermark-dom": "^2.3.0"}, "devDependencies": {"autoprefixer": "~7.1.2", "babel-core": "~6.22.1", "babel-helper-vue-jsx-merge-props": "~2.0.3", "babel-loader": "~7.1.1", "babel-plugin-syntax-jsx": "~6.18.0", "babel-plugin-transform-runtime": "~6.22.0", "babel-plugin-transform-vue-jsx": "~3.5.0", "babel-preset-env": "~1.3.2", "babel-preset-stage-2": "~6.22.0", "chalk": "~2.0.1", "copy-webpack-plugin": "~4.0.1", "css-loader": "~0.28.0", "extract-text-webpack-plugin": "~3.0.0", "file-loader": "~1.1.4", "friendly-errors-webpack-plugin": "~1.6.1", "html-webpack-plugin": "~2.30.1", "mockjs": "~1.1.0", "node-notifier": "~5.1.2", "node-sass": "~4.14.1", "optimize-css-assets-webpack-plugin": "~3.2.0", "ora": "~1.2.0", "portfinder": "~1.0.13", "postcss-import": "~11.0.0", "postcss-loader": "~2.0.8", "postcss-pxtorem": "~5.1.1", "postcss-url": "~7.2.1", "rimraf": "~2.6.0", "sass-loader": "~7.3.1", "semver": "~5.3.0", "shelljs": "~0.7.6", "svg-sprite-loader": "~3.8.0", "uglifyjs-webpack-plugin": "~1.1.1", "url-loader": "~0.5.8", "video.js": "~7.14.3", "vue-loader": "~13.7.2", "vue-style-loader": "~3.0.1", "vue-template-compiler": "~2.5.16", "webpack": "~3.6.0", "webpack-bundle-analyzer": "~2.9.0", "webpack-dev-server": "~2.9.1", "webpack-merge": "~4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}