import request from "@/utils/request";

export function list(params) {
  return request({
    url: "/notice/list",
    method: "get",
    params: params
  });
}
// 发函删除
export function noticeDelete(id) {
  return request({
    url: "/notice/delete/deleteById",
    method: "post",
    params: { id }
  });
}
// 收发函处理
export function deal(params) {
  return request({
    url: "/notice/opr",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

export function getBasic(params) {
  return request({
    url: "/aliDatav/getBasicData",
    method: "get",
    params: params
  });
}

// 发函撤回
export function noticeRecall(id) {
  return request({
    url: "/notice/recall/" + id,
    method: "get"
  });
}

// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get"
  });
}
