/*
 * @Author: your name
 * @Date: 2020-10-29 13:06:24
 * @LastEditTime: 2020-11-02 17:54:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \gov\src\api\amVehicle.js
 */
import request from "@/utils/request";
// 车辆锁定列表接口
export function getVehicleLock(params) {
  return request({
    url: "/dcveclock/list",
    method: "get",
    params: params
  });
}
// 异常详情列表接口
export function getVehicleAbnormalDetails(params) {
  return request({
    url: "/dcveclockdetail/list",
    method: "get",
    params: params
  });
}
// 备忘提交接口
export function setMemoSubmit(data) {
  return request({
    url: "/dcveclock/update",
    method: "post",
    data: data
  });
}
// 删除
export function lkDelete(data) {
  return request({
    url: "/dcveclock/delete",
    method: "post",
    data: data
  });
}
// 新增
export function lkAdd(data) {
  return request({
    url: "/dcveclock/save",
    method: "post",
    data: data
  });
}
// 编辑
export function lkEdit(data) {
  return request({
    url: "/dcveclock/update",
    method: "post",
    data: data
  });
}
// 车牌搜索
export function lkVehicle (params) {
  return request({
    url: "/vec/page",
    method: "get",
    params: params
  });
}