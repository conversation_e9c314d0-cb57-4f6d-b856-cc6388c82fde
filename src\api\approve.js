

import request from '@/utils/request'

//审核接口
export function approve(param){
	return request({
		url:'/appr/approve',
		method:'post',
		data:param,
		headers: {
			'Content-type':  'application/json;charset=UTF-8'
		}
	})
}

//审核日志接口
export function approveLog(params){
	return request({
		url:'/appr/appLog',
		method:'GET',
		params:params
	})
}


/**
 * 查看操作日志接口
 * @param {修改的表的主键} tabPk
 * @param {类型编码:主要是人员表,汽车表,企业表的类型编码,用于区分是对哪个表进行了修改操作} catCd
 */
export function oprLogInfo(tabPk,catCd){
	return request({
		url:'/opralog/info',
		method:'get',
		params:{
			tabPk:tabPk,
			catCd:catCd
		},
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
