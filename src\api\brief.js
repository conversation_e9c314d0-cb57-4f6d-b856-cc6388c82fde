import request from '@/utils/request'

//查询
export function getList(params) {
  return request({
    url: '/handleBrief/page',
    method: 'GET',
    params: params
  })
}
// 新增
export function add(data) {
  return request({
    url: "/handleBrief/save",
    method: "post",
    data: data
  });
}
// 编辑
export function upd(data) {
  return request({
    url: "/handleBrief/update",
    method: "post",
    data: data
  });
}
// 删除
export function del(id) {
  return request({
    url: "/handleBrief/delete?ids=" + id,
    method: "post",
  });
}
