import request from "@/utils/request";

// 根据手机号，获取手机验证码
export function getMobCode(data) {
  return request({
    url: "/code/getSmsCodeOnlyMob",
    method: "post",
    params: { mob: data },
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 验证手机号验证码
export function checkMobileCode(data) {
  return request({
    url: "/code/checkCode",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 修改密码
export function modifyPwd(data) {
  return request({
    url: "/sys/user/password",
    method: "post",
    data: data
  });
}

// 系统公告
export function noticeInfo() {
  return request({
    url: "/sys/notice/info",
    method: "get"
  });
}

// 获取服务器时间
export function latestTime() {
  return request({
    url: "/tool/now",
    method: "get"
  });
}

// 获取当前管理员信息
export function getUserInfo() {
  return request({
    url: "/sys/user/info",
    method: "get"
  });
}
// 绑定微信
export function bindWx(data) {
  return request({
    url: "/mp/bind",
    method: "post",
    data: data
  });
}

// 检查是否绑定微信
export function isbind(data) {
  return request({
    url: "/mp/isbind",
    method: "post",
    data: data
  });
}

/**
 * 解绑微信
 * @param {String}  openId
 */
export function unbind(data) {
  return request({
    url: "/mp/unbind",
    method: "post",
    params: data
  });
}

// 文件上传
export function uploadFile(contentfile, onUploadProgress) {
  return request({
    url: "/sys/oss/uploadFile",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    onUploadProgress
  });
}

// 获取秘钥publicKey
export function getKey() {
  return request({
    url: "/sys/smKey",
    method: "get"
  });
}

// 判断是否在黑名单
export function isExistBlackList(params) {
  return request({
    url: "/blacklist/selectInfoByIds",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
