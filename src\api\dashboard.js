import request from '@/utils/request'

// 今日车辆进出镇海数量
export function getComeInOutVecCnt(){
	return request({
		url:'/aliDatav/inOutDailyVec',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日车辆进出镇海车次详情---进镇海，出镇海
export function getComeInOutVecDetail(params){
	return request({
		url:'/aliDatav/inout/detail',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日车辆进出镇海车次详情---区内车
export function getVecDetail(params){
	return request({
		url:'/aliDatav/vecDetailNew',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 各道路卡口进出危险品车辆（6个）
export function getMajorRoadCnt(){
	return request({
		url:'/aliDatav/majorRoadCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日车辆情况
export function getVecStatusData(){
	return request({
		url:'/aliDatav/vehicleToday',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 今日车辆情况---明细
export function getVecDetailByType(params){
	return request({
		url:'/aliDatav/vecDetailNew',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日违章情况
export function getViolationData(){
	return request({
		url:'/aliDatav/dailyAlarmBar',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日违章情况---明细
export function getAlarmDetail(params){
	return request({
		url:'/aliDatav/alarmDetail',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日车辆进出镇海情况
export function getVecEntryAndExitData(){
	return request({
		url:'/aliDatav/getInOutVecByHour',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日货量情况---(今日装货总量,今日卸货总量)
export function getDailyLoadUnloadQty(){
	return request({
		url:'/aliDatav/dailyLoadUnloadGoods',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 今日货量情况---明细
export function getGoodsTodayDetail(params){
	return request({
		url:'/aliDatav/goodsToday/detail',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 今日货量情况---获取今日货物数据统计（今日装货车次，今日卸货车次，今日登记车次）
export function getGoodsToday(){
	return request({
		url:'/aliDatav/goodsToday',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 今日登记点登记情况
export function getRegPotStat(){
	return request({
		url:'/aliDatav/getRegPotStat',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 装货情况Top5
export function getLoadCnt(){
	return request({
		url:'/aliDatav/getLoadCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 卸货情况Top5
export function getUnloadCnt(){
	return request({
		url:'/aliDatav/getUnloadCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 货物类别情况
export function getRtePlanGoodsType(){
	return request({
		url:'/aliDatav/getRtePlanGoodsType',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 今日车辆登记情况 
export function getInOutAreaDailyVec(){
	return request({
		url:'/aliDatav/getRegVecByHour',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 企业装货情况
export function getEntpLoadGoodsCnt(){
	return request({
		url:'/aliDatav/getDailyLoadCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 企业卸货情况
export function getEntpUnloadGoodsCnt(){
	return request({
		url:'/aliDatav/getDailyUnLoadCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 车辆过登记点图片
export function getPassByVecPic(){
	return request({
		url:'/aliDatav/getPassByVecPic',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 镇海区当天货物流向统计图
export function getGoodsMapFlow(){
	return request({
		url:'/aliDatav/getTodayReAndDeCnt',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 镇海区当天货物流向统计图
export function getBasicData(){
	return request({
		url:'/aliDatav/getBasicData',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}