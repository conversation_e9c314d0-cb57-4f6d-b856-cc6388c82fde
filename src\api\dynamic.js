import request from '@/utils/request'


 // 相关违章报警的处置类型
export function getType(){
	return request({
		url:'/alarm/getDealTypeList',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// // 所有处置类型
export function getAllType(){
	return request({
		url:'/alarm/getDealTypeListByAlarmType',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

