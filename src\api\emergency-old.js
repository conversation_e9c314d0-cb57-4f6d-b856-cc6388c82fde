import request from "@/utils/request";

// 获取左侧列表
export function getList() {
  return request({
    url: "/dcEmergencyTeam/selectOne",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取企业列表
export function getEntpList(param) {
  return request({
    url: "/entp/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆列表
export function getVecList(param) {
  return request({
    url: "/vec/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取货物列表
export function getChemList(entpname) {
  return request({
    url: "/ench/querybyentpname",
    method: "get",
    params: { entpname: entpname },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 根据企业名称返回
export function getEntpnamepoi(param) {
  return request({
    url: "/gps/entpnamepoi",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 根据坐标返回
export function getpoi(param) {
  return request({
    url: "/gps/poi",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 根据车牌号返回
export function getVecnopoi(param) {
  return request({
    url: "/gps/vecnopoi",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取企业详情name
export function getEntpInfo(entpName) {
  return request({
    url: "/entp/portraitbyentpname",
    method: "get",
    params: { entpname: entpName },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取企业详情pk
export function getEntpInfoBypk(pk) {
  return request({
    url: "/entp/itm/" + pk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆电子运单详情
export function getVecInfo(vecNo) {
  return request({
    url: "/rtePlan/getLast",
    method: "get",
    params: { tracCd: vecNo },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 根据企业pk获取装卸记录
export function getStevedor(data) {
  return request({
    url: "argmwt/dayentp",
    method: "get",
    params: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 根据企业pk获取货物列表
export function getChem(ipPk) {
  return request({
    url: "entp/cp/goods?ipPk=" + ipPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取生产使用企业
export function getGmpEntp() {
  return request({
    url: "/dcEmergencyTeam/selectentploc",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取罐区
export function getTank(params) {
  return request({
    url: "/tankArea/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
