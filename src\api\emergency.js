import request from "@/utils/request";

// 获取应急救援专家列表
export function getEmergencyExpertList(param) {
  return request({
    url: "/emergencyexpert/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援专家类型
export function getEmergencyExpertTypeList(param) {
  return request({
    url: "/emergencyexpert/type-list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援专家详情
export function getEmergencyExpertByPk(pk) {
  return request({
    url: "/emergencyexpert/info/" + pk,
    method: "get"
  });
}

// 新增应急救援专家
export function addEmergencyExpert(data) {
  return request({
    url: "/emergencyexpert/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 保存应急救援专家
export function updEmergencyExpert(data) {
  return request({
    url: "/emergencyexpert/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除应急救援专家
export function delEmergencyExpert(param) {
  return request({
    url: "/emergencyexpert/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援力量列表
export function getEmergencyForceList(param) {
  return request({
    url: "/emergencypower/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援力量类型
export function getEmergencyForceTypeList(param) {
  return request({
    url: "/emergencypower/type-list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援力量详情
export function getEmergencyForceByPk(pk) {
  return request({
    url: "/emergencypower/info/" + pk,
    method: "get"
  });
}

// 新增应急救援力量
export function addEmergencyForce(data) {
  return request({
    url: "/emergencypower/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 保存应急救援力量
export function updEmergencyForce(data) {
  return request({
    url: "/emergencypower/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除应急救援力量
export function delEmergencyForce(param) {
  return request({
    url: "/emergencypower/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援物资列表
export function getEmergencyMaterialList(param) {
  return request({
    url: "/emergencysupplies/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援物资类型
export function getEmergencyMaterialTypeList(param) {
  return request({
    url: "/emergencysupplies/type-list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援物资类型的详细内容
export function getEmergencyMaterialTypeDetail(param) {
  return request({
    url: "/emergencysupplies/detail-list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急救援物资详情
export function getEmergencyMaterialByPk(pk) {
  return request({
    url: "/emergencysupplies/info/" + pk,
    method: "get"
  });
}

// 新增应急救援物资
export function addEmergencyMaterial(data) {
  return request({
    url: "/emergencysupplies/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 保存应急救援物资
export function updEmergencyMaterial(data) {
  return request({
    url: "/emergencysupplies/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除应急救援物资
export function delEmergencyMaterial(param) {
  return request({
    url: "/emergencysupplies/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急演练记录列表
export function getDrillRecordList(param) {
  return request({
    url: "/emergencydrill/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急演练记录类型
export function getDrillRecordTypeList(param) {
  return request({
    url: "/emergencydrill/type-list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取应急演练记录详情
export function getDrillRecordByPk(pk) {
  return request({
    url: "/emergencydrill/info/" + pk,
    method: "get"
  });
}

// 新增应急演练记录
export function addDrillRecord(data) {
  return request({
    url: "/emergencydrill/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 保存应急演练记录
export function updDrillRecord(data) {
  return request({
    url: "/emergencydrill/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除应急演练记录
export function delDrillRecord(param) {
  return request({
    url: "/emergencydrill/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 牵引车号联想
export function getFuzzyVecList(vecNo) {
  return request({
    url: "/vec/page",
    method: "get",
    params: {
      filters: {
        groupOp: "AND",
        rules: [
          {field: "vec_no", op: "cn", data: vecNo}
        ]
      },
      limit: 30,
      page: 1
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 危险品货物联想
export function getChemList(chemNm) {
  return request({
    url: "/chem/list",
    method: "get",
    params: {param: chemNm},
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取货物应急处置信息
export function getChemInfoByPk(prodPk) {
  return request({
    url: "/chemm/info/" + prodPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 事故车辆搜索电子运单(可能多个)
export function getEmergencyLastRteplan(tracCd) {
  return request({
    url: "/rtePlan/getEmergencyLast",
    method: "get",
    params: {tracCd: tracCd},
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取车辆电子运单详情
export function getVecInfo(vecNo) {
  return request({
    url: '/rtePlan/getLast',
    method: 'get',
    params: {tracCd: vecNo},
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取企业详情pk
export function getEntpInfoBypk(pk) {
  return request({
    url: '/entp/itm/' + pk,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 车辆轨迹(就是历史轨迹的接口。日期用电子运单的vecDespTm:YYYY-MM-dd)
export function getTrackPageList(vecNo, searchDate) {
  return request({
    url: "/gps/getTrackPageList",
    method: "get",
    params: {
      vehicleNo: vecNo,
      time: searchDate
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 车辆实时位置及周边poi及扩散范围
 *
 * 传值：
 vecno车牌
 prodPk危险品pk
 size 泄露量大小 大0 小1 默认为0
 time 时间 白天0 夜晚1 默认为0
 radius 半径

 location:车辆经纬度（手动拾取定位）
 若根据危险品查不到泄漏范围。且半径未传默认为1km

 * 返回值：（没有扩散范围的话:initial和diffusion就是0,返回的地物是1000m以内的）
 poi 周边地物（数组）
 nearcar 周围的车（数组）
 lnglat:百度地图经纬度
 tdlnglat：天地图经纬度
 initial初始距离
 diffusion疏散距离

 */
export function getVecEmergencyPoi(params) {
  return request({
    url: "/gps/emergencyPoi",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取车辆轨迹
export function getTrack(data) {
  return request({
    url: "/gps/history",
    method: "get",
    params: data
  });
}

// 根据企业名称返回
export function getEntpnamepoi(param) {
  return request({
    url: "/gps/entpnamepoi",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 根据车牌号返回
export function getVecnopoi(param) {
  return request({
    url: '/gps/vecnopoi',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取生产使用企业列表
export function getCplist(param) {
  return request({
    url: "/entpLoc/cplist",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取车辆附近的生产使用企业列表
export function getCplistNearbyVec(lng, lat, radius) {
  return request({
    url: "/entpLoc/cpNearby",
    method: "get",
    params: {
      lng: lng,
      lat: lat,
      radius: radius
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取企业列表
export function getEntpList(param) {
  return request({
    url: '/entp/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 获取车辆列表
export function getVecList(param) {
  return request({
    url: '/vec/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 获取货物列表
export function getEnchList(entpname) {
  return request({
    url: '/ench/querybyentpname',
    method: 'get',
    params: {entpname: entpname},
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 根据坐标返回
export function getpoi(param) {
  return request({
    url: '/gps/poi',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 获取罐区
// export function getTank(params) {
//   return request({
//     url: "/tankArea/list",
//     method: "get",
//     params: params,
//     headers: {
//       "Content-type": "application/json;charset=UTF-8"
//     }
//   });
// }

//
// 获取应急短信通知人列表
export function getNotifyList() {
  return request({
    url: '/emergency/getNotifyList',
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

//  发送应急救援短信
export function sendSMS(param) {
  return request({
    url: '/emergency/sms',
    method: 'post',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}
