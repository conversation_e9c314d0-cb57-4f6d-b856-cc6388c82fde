import request from "@/utils/request";

// 获取危化品列表
export function getenchList(param) {
  return request({
    url: "/chem/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取危化品详情
export function getenchByPk(pk) {
  return request({
    url: "/chem/info/" + pk,
    method: "get"
  });
}

// 根据危化品别名pk,获取危化品详情
export function getChemByEnchPk(pk) {
  return request({
    url: "/chem/findByEnchPk/" + pk,
    method: "get"
  });
}

// 根据危化品prokpk,获取危化品详情
export function getChemByProdPk(prodPk) {
  return request({
    url: "/chem/info/" + prodPk,
    method: "get"
  });
}

// 新增
export function addEnch(data) {
  return request({
    url: "/chem/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除
export function delench(param) {
  return request({
    url: "/chem/delete",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 保存
export function updEnch(data) {
  return request({
    url: "/chem/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

export function getChemList(params) {
  return request({
    url: "/chem/list",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取危化品详情
export function getMSDSByUn(un) {
  return request({
    url: "/chemmsds/dtl/" + un,
    method: "get"
  });
}
