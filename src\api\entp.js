import request from "@/utils/request";
// 根据企业pk获取相应企业信息
export function getEntpByEntpPk(entpPk) {
  return request({
    url: "/entp/itm/" + entpPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取企业列表
export function getEntpList(param) {
  return request({
    url: "/entp/queryEntpPage",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取当前企业信息
export function getEntpDetail() {
  return request({
    url: "/entp/detail",
    method: "get"
  });
}

// 保存
export function updEntp(data) {
  return request({
    url: "/entp/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 模糊搜索，获取装货/卸货单位
export function getFuzzyEntpAddr(nmCn) {
  return request({
    url: "/entp/fuzzy?nmCn=" + encodeURIComponent(nmCn),
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//企业完成度
export function countEntpDocComplete(entpPks) {
  return request({
    url: "/entp/countEntpDocComplete?entpPks=" + entpPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//车辆待审核比例

export function vecApprvCnt(entpPks) {
  return request({
    url: "/vec/vecApprvCnt?entpPks=" + entpPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//人员待审核比例
export function persApprvCnt(entpPks) {
  return request({
    url: "/pers/persApprvCnt?entpPks=" + entpPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//人员待审核比例
export function addmemo(params) {
  return request({
    url: "/entp/addmemo",
    method: "post",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 根据企业id获取企业审核状态结果
export function getEntpIsApprovedByIds(params) {
  return request({
    url: "/entp/isApprovedByIds",
    method: "post",
    data: params
  });
}
