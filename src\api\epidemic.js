import request from '@/utils/request'
// 获取列表
export function list (params) {
  return request({
    url: '/zhpersprevention/list',
    method: 'get',
    params: params
  })
}

// 获取信息
export function info (id) {
  return request({
    url: '/zhpersprevention/info/' + id,
    method: 'get'
  })
}

// 添加
export function add (params) {
  return request({
    url: '/zhpersprevention/save',
    method: 'post',
    data: params
  })
}

// 修改
export function update (params) {
  return request({
    url: '/zhpersprevention/update',
    method: 'post',
    data: params
  })
}
// 审核
export function audit (params) {
  return request({
    url: '/zhpersprevention/audit',
    method: 'post',
    data: params
  })
}
// 删除
export function del (params) {
  return request({
    url: '/zhpersprevention/delete',
    method: 'post',
    data: params
  })
}
// 下载
export function download(param){
  return request({
    url:'/zhpersprevention/download',
    method:'post',
    params:param,
    responseType: 'blob'
  })
}


// 获取镇疫码列表
export function zhzymregList (params) {
  return request({
    url: '/zhzymreg/list',
    method: 'get',
    params: params
  })
}

// 获取镇疫码详情
export function zhzymregInfo (id) {
  return request({
    url: '/zhzymreg/info/'+id,
    method: 'get'
  })
}

// 镇疫码解绑
export function zhzymregUnbind (id) {
  return request({
    url: '/zhzymreg/unbind?id=' + id,
    method: 'post'
  })
}

// 获取卡口或场所列表
export function zhzymcheckList (params) {
  return request({
    url: '/zhzymcheck/list',
    method: 'get',
    params: params
  })
}

// 获取卡口或场所详情
export function zhzymcheckInfo (id) {
  return request({
    url: '/zhzymcheck/info/'+id,
    method: 'get'
  })
}

// 获取来镇预报列表
export function zhzymforecastList (params) {
  return request({
    url: '/zhzymforecast/list',
    method: 'get',
    params: params
  })
}

// 获取来镇预报详情
export function zhzymalarmInfo (id) {
  return request({
    url: '/zhzymalarm/info/'+id,
    method: 'get'
  })
}

// 来镇预报处置
export function forecastHandle (data) {
  return request({
    url: '/zhzymforecast/handle',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 通过身份证号查询危运疫码通详情
export function zhzymregFindByIdCard (idCard) {
  return request({
    url: '/zhzymreg/findByIdCard?idCard='+idCard,
    method: 'get'
  })
}
