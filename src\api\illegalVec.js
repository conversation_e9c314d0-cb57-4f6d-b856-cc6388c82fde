import request from "@/utils/request";
// 违章车辆登记

// 删除
export function illegalVecDelete(ids) {
  return request({
    url: "/offsite/delete?ids=" + ids,
    method: "post"
  });
}

// 操作员列表
export function illegalVecOprlist(params) {
  return request({
    url: "/offsite/illegalVec/oprlist",
    method: "get",
    params: params
  });
}

// 新增
export function add(data) {
  return request({
    url: "/offsite/illegalVec/save",
    method: "post",
    data: data
  });
}

// 运输企业下拉
export function illegalVecSelectCarr(params) {
  return request({
    url: "/offsite/illegalVec/selectCarr",
    method: "get",
    params
  });
}

// 人员下拉
export function illegalVecSelectPers(params = {}) {
  return request({
    url: "/offsite/oprlist",
    method: "get",
    params: params
  });
}
// todo：违章类别功能待确认
// export function illegalVecSelectTrunk(params = {}) {
//   return request({
//     url: "/offsite/oprlist",
//     method: "get",
//     params: params
//   });
// }
// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get"
  });
}
// 更新
export function upd(offsiteEntity) {
  return request({
    url: "/offsite/illegalVec/update",
    method: "post",
    data: offsiteEntity
  });
}
//查询--分页
export function getList(params) {
  return request({
    url: "/offsite/illegalVec/page",
    method: "get",
    params: params
  });
}

// 详情
export function getDetail(id) {
  return request({
    url: `/offsite/illegalVec/info/${id}`,
    method: "GET"
  });
}

// 车牌搜索
export function lkVehicle(params) {
  return request({
    url: "/vec/page",
    method: "get",
    params: params
  });
}
// 获取扣分明细列表
export function getGov3AlarmLaw(param) {
  return request({
    url: "/alarmlaw/getGov3AlarmLaw",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 文件上传
export function uploadFile(contentfile, onUploadProgress) {
  return request({
    url: "/sys/oss/upload/multi",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    onUploadProgress
  });
}
