import request from '@/utils/request'
// 获取列表
export function getList(param){
	return request({
		url:'/zhcknoticesheet/page',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 新增/修改
export function saveOrUpdate(param){
	return request({
		url:'/zhcknoticesheet/saveOrUpdate',
		method:'post',
		data:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 详情
export function noticeSheetInfo(id){
	return request({
		url:'/zhcknoticesheet/info/'+id,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


/**
 * 删除
 * @param {Array} ids 
 * @returns 
 */
export function noticeSheetDelete(ids){
	return request({
		url:'/zhcknoticesheet/delete',
		method:'post',
		data:ids,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/**
 * 发送短信
 * @param {Array} ids 
 * @returns 
 */
export function officialSend(ids){
	return request({
		url:'/zhcknoticesheet/officialSend',
		method:'post',
		data:ids,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
