import request from "@/utils/request";

export function uploadLic<PERSON>mage(contentfile) {
  return request({
    url: "/sys/oss/upload/multi",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

export function uploadLicFile(contentfile) {
  return request({
    url: "/sys/oss/uploadFile",
    method: "post",
    timeout: 60000,
    data: contentfile,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

// 证照自动识别
export function licAutoCheck(data) {
  return request({
    url: "/lic/licAutoCheck",
    method: "post",
    timeout: 60000,
    data: data,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
}

// 获取证照配置
export function getLicConfig(params) {
  return request({
    url: "/sys/lic/licConfigArray",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

export function licInfo(entityPk, entityType, catCd) {
  return request({
    url: "/sys/lic/licInfo",
    method: "get",
    timeout: 60000,
    params: {
      entityPk: entityPk,
      entityType: entityType,
      catCd: catCd
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
