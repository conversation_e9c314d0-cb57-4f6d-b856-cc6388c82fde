import request from '@/utils/request'
export function getDayMonit(data){
    return request({
        url:'argmwt/dayMonit',
	    	method:'get'
    })
}
export function stevedorDownload(params){
    return request({
        url:'stevedor/download',
        method:'get',
        params:params,
        responseType: 'blob'
    })
}
//装卸记录详情
export function allListNoCount(params){
    return request({
        url:'/argmwt/allListNoCount',
        method:'get',
        params:params
    })
}
//进出企业gps
export function getEntpGps(params){
    return request({
        url:'/aliDatav/getTodayInoutByEntppk',
        method:'get',
        params:params
    })
}