import request from "@/utils/request";

// 获取装卸企业地图信息
export function oneMap(){
	return request({
		// url:'/loadandunload/entpLoc/oneMap',
		url: '/entpLoc/page',
		method: 'get',
		params: {
			groupOp: 'AND',
			rules: [{ field: 'cat_cd', op: 'eq', data: '1200.155' }],
			limit: 999,
		},
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取装卸企业摄像头坐标点
export function camera(params){
	return request({
		url: '/video/device2/page',
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取企业装卸统计数据
export function statis(){
	return request({
		url: '/argmwt/dayMonitStati',
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 获取安全检查记录
export function listSelfAll(params){
	return request({
		url: '/argmtbefcheck/listAll',
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取安全检查记录详情
export function getArgmtbefcheckDtl(id) {
	return request({
		url: '/argmtbefcheck/info/'+id,
		method: 'get',
	})
}
export function getArgmtaftcheckDtl(id) {
	return request({
		url: '/argmtaftcheck/info/'+id,
		method: 'get',
	})
}

// 获取装卸货记录
export function allListNoCount(params){
	return request({
		url: '/argmwt/allListNoCount',
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
//获取电子运单详情
export function getRtePlanDtlById(argmtPk) {
	return request({
		url : `/rtePlan/getDtlById?id=${argmtPk}`,
		method : 'get',
		headers: {
			"Content-type": "application/json;charset=UTF-8"
		}
	})
}
//装卸单详情
export function argmwtInfo(argmtWtPk) {
	return request({
		url : `/argmwt/info/${argmtWtPk}`,
		method : 'get',
		headers: {
			"Content-type": "application/json;charset=UTF-8"
		}
	})
}

/**
 * 历史装卸单详情
 * @param {*} id 
 * @param {*} year 
 * @returns 
 */
export function getArgmwtHisInfo(params) {
	return request({
		url: "/argmwt/getHisDtlById",
		params: params,
		method: "get",
		headers: {
			"Content-type": "application/json;charset=UTF-8",
		},
	});
}

// 获取装卸货报警
export function alarmPage(params){
	return request({
		url: '/alarm/pageCp',
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


//获取货物数据详情
export function goodsInfo(pk) {
	return request({
		url : `/chem/info/${pk}`,
		method : 'get',
		headers: {
			"Content-type": "application/json;charset=UTF-8"
		}
	})
}

