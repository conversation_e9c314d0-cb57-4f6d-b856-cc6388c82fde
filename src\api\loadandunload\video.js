import request from "@/utils/request";
// 获取视频地址
export function getVideoStreamUrl(number) {
  return request({
    url: "/video/device/getStreamUrl",
    method: "get",
    params: { number: number }
  });
}



// 获取视频地址，传递number=XXX&urlType=3
// urlType: 1表示rtsp格式的URL;2表示http格式的URL;3表示rtmp格式的URL
// urlType： 1：rtsp 2：http 3：rtmp 4：hls 5：http-flv 6：rtmp（对方接口） 默认为4
export function getLoadAndUnloadVideoStreamUrl(number, type) {
  return request({
    url: "/video/device2/getStreamUrl",
    method: "get",
    params: { number: number, urlType: type ? type : 3 }
  });
}




// 获取企业名称列表
export function getEntpList(params){
  return request({
    url: '/video/deviceHw/getEntpList',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取企业摄像头列表
export function getCamList(entpName){
  return request({
    url: '/video/deviceHw/getCamList?entpName=' + entpName,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取HUAWEI视频列表(直播)
export function getDeviceHwList(params,number,videoTime) {
  return request({
    url: "/video/deviceHw/list?number=" + number +"&videoTime=" + videoTime,
    params: params,
    method: "get"
  });
}


// 根据摄像头查列表(录播)
export function getCamVideoList(number,channelID,videoTime) {
  return request({
    url: "video/urlHw/list?number=" + number +"&channelID=" + channelID +"&videoTime=" + videoTime,
    method: "get"
  });
}

// 获取所有摄像头列表
export function getAllVideoList(params){
  return request({
    url: '/video/deviceHw/queryChannelList',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


// 触发停止直播接口
export function setExpire(str){
  return request({
    url: '/video/deviceHw/setExpire?flvStr=' + str,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


// 获取移动卡口
export function getTodayVecOcr(params){
	return request({
		url: '/video/deviceHw/todayVecOcr',
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 获取抓拍详情
export function getVecocr(cameraNo,params){
	return request({
		url: 'vecocr/allOcrList?cameraNo=' + cameraNo,
		method: 'get',
		params: params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 获取直播
export function queryDeviceByDeviceId(deviceId){
	return request({
		url: 'video/deviceHw/queryDeviceByDeviceId?deviceId='+deviceId,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 视频假死激活接口
export function getStreamStart(str){
  return request({
    url: '/video/deviceHw/streamStart?flvStr=' + str,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

