import request from "@/utils/request";

/**
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */

// 获取验证码
// export function getCodeKeySrc(codeKey){
//   return request({
// 		url:`/code/generate?codeKey=${codeKey}`,
// 		responseType: 'arraybuffer',
// 		transformResponse: [function (response) {
// 			let res = 'data:image/png;base64,' + btoa(
// 				new Uint8Array(response)
// 					.reduce((data, byte) => data + String.fromCharCode(byte), '')
// 				)
// 			return res;
// 		}],
// 	})
// }

// 用户名登录
export function loginByUsername(data) {
  return request({
    url: "/sys/smLogin",
    method: "post",
    data: data
  });
}

// 登出
export function logout() {
  return request({
    url: "/sys/logout",
    method: "post"
  });
}

// 获取当前管理员信息
export function getUserInfo() {
  return request({
    url: "/sys/user/info",
    method: "get"
  });
}

export function getSmsCodeOnlyMob(data) {
  return request({
    url: "/code/getSmsCodeForLogin",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 企业端用户通过手机号注册，获取手机验证码
export function getSmsCode(data) {
  return request({
    url: "/code/getSmsCode",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 验证手机号验证码
export function handleCheckMobileCode(data) {
  return request({
    url: "/code/checkCode",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 提交企业注册信息
export function handleRegister(data) {
  return request({
    url: "/entp/register",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 浙政钉校验绑定接口
export const checkDingInfo = data =>
  request({
    url: "/sys/smCheckDingInfo",
    method: "post",
    data: data
  });
// 浙政钉绑定账户发送验证码
export const getSmsCodeForDingCheck = data =>
  request({
    url: "/code/getSmsCodeForDingCheck",
    method: "get",
    params: data,
  });
export const getUserInfoByZZDCode = authCode =>
  request({
    url: "/sys/dingScan/login",
    method: "post",
    params: {
      code: authCode
    }
  });
