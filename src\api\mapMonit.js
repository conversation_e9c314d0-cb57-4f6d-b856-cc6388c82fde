import request from '@/utils/request'

// 在途可视左侧车辆列表接口
export function getGpsVecListByPage (param) {
  return request({
    url: '/gps/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

// 历史轨迹左侧车辆列表接口
export function getVecListOfGpsTraceByPage (param) {
  return request({
    url: '/gps/queryCarList',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

// 在途可视实时定位接口
export function getAllVecListWithGPS (type) {
  return request({
    url: '/gps/list',
    method: 'get',
    params: { type: type },
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

// 根据日期，获取车辆gsp的历史轨迹
export function getVecGpsTraceByDate (data) {
  return request({
    url: '/gps/today',
    method: 'get',
    params: {
      t: data.t,
      v: data.v
    },
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 获取车辆转向记录
export function getVecGpsTurnByDate (par) {
  return request({
    url: '/gps/todayTurn',
    method: 'get',
    params: par,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}
// 在途可视区内外车
export function getLocalCarList (type) {
  return request({
    url: '/gps/inoutlocalcarlist',
    method: 'get',
    params: { type: type },
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取装卸企业
export function findAreaByEntp (params) {
  return request({
    url: '/entp/cp/list',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 根据企业pk获取装卸企业信息
export function getEntpByCpPk (entpPk) {
  return request({
    url: 'entp/cp/info?ipPk=' + entpPk,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 根据企业pk获取装卸记录
export function getStevedor (data) {
  return request({
    url: 'argmwt/dayentp',
    method: 'get',
    params: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 根据企业pk获取货物列表
export function getChem (ipPk) {
  return request({
    url: 'entp/cp/goods?ipPk=' + ipPk,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 操作员列表
export function oprlist () {
  return request({
    url: '/offsite/oprlist',
    method: 'get'
  })
}

// 操作员列表
export function policeCar () {
  return request({
    url: '/gps/policeCar',
    method: 'get'
  })
}
//获取车辆类型
export function getGpsType (param) {
  return request({
    url: '/gps/getGpsType',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

//获取车辆 危化品信息
export function getControlDetail (param) {
  return request({
    url: '/chem/controlDetail',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

export function getOneMap () {
  return request({
    url: `/entpLoc/oneMap`,
    method: 'get',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

  /**
   * 获取车辆途径记录
   * @param {String} vecNo  车牌号
   * @param {Date} time  时间
   */
  export function getSchedule (params) {
    return request({
      url: `/vec/schedule`,
      method: 'get',
      params: params,
      headers: {
        'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }
    })
  }


  // 根据车牌号查询GPS信息
  export function findGpsByVecNo (vecNo) {
    return request({
      url: '/gps/findGpsByVecNo?vecNo=' + vecNo,
      method: 'get',
      headers: {
        'Content-type': 'application/json;charset=UTF-8'
      }
    })
  }

  // 获取列表(包含装货地和卸货地的经纬度信息)
  export function getRtePlanListByVec (param) {
    return request({
      url: '/rtePlan/listByVec',
      method: 'get',
      params: param,
      headers: {
        'Content-type': 'application/json;charset=UTF-8'
      }
    })
  }

// 获取过磅记录
export function getArgwtRecordList (param) {
  return request({
    url: '/argmwt/argwtRecord',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 高速出入口摄像头点位
export function highwayPosts() {
  return request({
    url: '/gps/highwayPosts',
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}