import request from "@/utils/request";

// 获取重点监管车辆列表
export function getSuperFocusVecList() {
  return request({
    url: "/zhZeroCount/listLevel5",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取gps车辆列表
export function getAllVecListWithGPS() {
  return request({
    url: "/zhZeroCount/listGps",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 显示区内运输企业车辆或区外运输企业车辆
export function getLocalCarList(type) {
  return request({
    url: "/gps/inoutlocalcarlist",
    method: "get",
    params: { type: type },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
/**
 * 处置等级5的报警
 * @param {object} params {id,处理人：person，处理结果：opContent}
 * @returns
 */
export function handle(params) {
  return request({
    url: "/zhZeroCount/handle",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
/**
 * 获取处置记录
 * @returns
 */
export function getPageOffsite(params) {
  return request({
    url: "/zhZeroCount/pageOffsite",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 操作员列表
export function getOprlist() {
  return request({
    url: "/offsite/oprlist",
    method: "get"
  });
}

// 下载车辆实时处置记录
export function realtimeZeroDownload(param) {
  return request({
    url: "/zhZeroCount/pageOffsiteExport",
    method: "get",
    params: param,
    responseType: "blob"
  });
}
