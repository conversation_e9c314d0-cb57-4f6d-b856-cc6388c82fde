import request from "@/utils/request";

// 特殊事项上报分页
export function reportPage(data) {
  return request({
    url: "/special/report/page",
    method: "get",
    params: data
  });
}


// 特殊事项上报详情
export function reportInfo(id) {
  return request({
    url: "/special/report/info/"+id,
    method: "get"
  });
}

// 查询待处理数量
export function notifyCount() {
  return request({
    url: "/special/report/notifyCount",
    method: "get"
  });
}

// 查询历史
export function reportHistory(id) {
  return request({
    url: "/special/report/history?id="+id,
    method: "get"
  });
}

// 特殊事项已读
export function readReport(params) {
  return request({
    url: "/special/report/read",
    method: "post",
    data:params
  });
}

// 特殊事项回复
export function replyReport(params) {
  return request({
    url: "/special/report/reply",
    method: "post",
    data:params
  });
}


// 事件上报类型
export function listNoPageForAll() {
  return request({
    url: "/sys/dict/listNoPageForAll?type=事件上报类型",
    method: "get"
  });
}