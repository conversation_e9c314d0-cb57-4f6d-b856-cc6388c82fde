import request from "@/utils/request";

// 获取未完成的数量
export function getUnFinishCount() {
  return request({
    url: "/menorandum/getUnFinishCount",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 判断当前用户无权限
export function judgeIsDljc() {
  return request({
    url: "/menorandum/judgeIsDljc",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取列表
export function getMenorandumList(param) {
  return request({
    url: '/menorandum/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}
// 新增
export function addMenorandum(data) {
  return request({
    url: '/menorandum/save',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 修改
export function updMenorandum(data) {
  return request({
    url: '/menorandum/update',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 删除
export function delMenorandum(param) {
  return request({
    url: '/menorandum/delete',
    method: 'post',
    data: param
  })
}
// 完结
export function finishMenorandum(param) {
  return request({
    url: '/menorandum/finish',
    method: 'post',
    data: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}