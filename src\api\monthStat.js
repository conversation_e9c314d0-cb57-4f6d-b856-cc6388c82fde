import request from '@/utils/request'

//查询车辆违章
export function queryVecList(params){

    return request({
        url : '/stat/month/vec/list',
        method : 'GET',
        params : params,
        headers : {
            'Content-type' : 'application/json;charset=UTF-8'
        }
    })

}
//查询运输企业违章统计
export function queryEntpList(params){

    return request({
        url : '/stat/month/entp/list',
        method : 'GET',
        params : params,
        headers : {
            'Content-type' : 'application/json;charset=UTF-8'
        }
    })

}

//查询装卸企业统计
export function queryRegPotList(params){

    return request({
        url : '/stat/month/regpot/list',
        method : 'GET',
        params : params,
        headers : {
            'Content-type' : 'application/json;charset=UTF-8'
        }
    })

}
//查询GPS质量统计
export function queryGpsList(params){

    return request({
        url : '/gpsUser/ceshi',
        method : 'GET',
        params : params,
        headers : {
            'Content-type' : 'application/json;charset=UTF-8'
        }
    })

}
//查询企业违章统计
export function entpList(params){

    return request({
        url : '/stat/month/entp/list',
        method : 'GET',
        params : params,
        headers : {
            'Content-type' : 'application/json;charset=UTF-8'
        }
    })

}
//车辆违章报表导出
export function downloadVecExcel(params){

    return request({
        url : '/stat/month/vec/download',
        method : 'GET',
        params : params,
        responseType: 'blob',
    })

}

//运输企业违章报表导出
export function downloadEntpExcel(params){

    return request({
        url : '/stat/month/entp/download',
        method : 'GET',
        params : params,
        responseType: 'blob'
    })

}

//装卸企业统计报表导出
export function downloadRegPotExcel(params){

    return request({
        url : '/stat/month/regpot/download',
        method : 'GET',
        params : params,
        responseType: 'blob'
    })

}
//GPS质量统计报表导出
export function downloadGpsExcel(params){

    return request({
        url : '/stat/month/regpot/download',
        method : 'GET',
        params : params,
        responseType: 'blob'
    })

}
//企业违章统计报表导出
export function downloadEntpListExcel(params){

    return request({
        url : '/stat/month/entp/download',
        method : 'GET',
        params : params,
        responseType: 'blob'
    })

}