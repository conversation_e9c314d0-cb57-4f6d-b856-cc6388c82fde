import request from '@/utils/request'

// 获取监管通报列表
export function getList(param) {
  return request({
    url: "/argmnotice/list",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取所有的装卸企业
export function getEntpList() {
  return request({
    url: "/entp/loadProdList",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取所有的运输企业
export function getTransportEntpList() {
  return request({
    url: "/entp/transportList",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}


// 新增监管通报
export function add(params) {
  return request({
    url: "/argmnotice/add",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 监管通报详情
export function info(id) {
  return request({
    url: "/argmnotice/info/" + id,
    method: "post",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 监管通报详情
export function detail(params) {
  return request({
    url: "/argmnotice/detail",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}


// 编辑监管通报
export function edit(params) {
  return request({
    url: "/argmnotice/upd",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 删除监管通报
export function del(id) {
  return request({
    url: "/argmnotice/del?id=" + id,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 完结企业回函
export function finish(params) {
  return request({
    url: "/argmnotice/finish",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 取消完结企业回函
export function unfinish(params) {
  return request({
    url: "/argmnotice/cancel",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 政府发送给企业信息
export function sendMessage(params) {
  return request({
    url: "/argmnotice/updateDetail",
    method: "post",
    data: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取通报类型
export function getTypeCdList() {
  return request({
    url: "/argmnotice/type-list",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}



