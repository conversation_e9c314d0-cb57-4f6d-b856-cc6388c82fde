import request from "@/utils/request";

export function offsiteList(params) {
  return request({
    url: "/offsite/list",
    method: "get",
    params: params
  });
}

export function offsiteDelete(ids) {
  return request({
    url: "/offsite/delete?ids=" + ids,
    method: "post"
  });
}

export function offsiteSave(data) {
  return request({
    url: "/offsite/save",
    method: "post",
    data: data
  });
}

export function offsiteUpdate(data) {
  return request({
    url: "/offsite/update",
    method: "post",
    data: data
  });
}

export function offsiteInfo(id) {
  return request({
    url: "/offsite/info/" + id,
    method: "post"
  });
}

export function offsiteRealtimeList(params) {
  return request({
    url: "/offsite/listOnlyVecMonit",
    method: "get",
    params: params
  });
}

// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get"
  });
}

// 报警类型ForOffsite
export function getAlarmTypeForOffsite() {
  return request({
    url: "/alarm/alarmlistdictForOffsite",
    method: "get"
  });
}

export function getTodayOffsiteList(params) {
  return request({
    url: "/offsite/realTime",
    method: "get",
    params: params
  });
}
// 违章预警导出Excel表格
export function exportExcel(params) {
  return request({
    url: "/offsite/export",
    method: "get",
    params: params,
    responseType: "blob"
  });
}

// 违章预警操作提示
export function getSelectOuterCityTips(params) {
  return request({
    url: "/alarm/selectOuterCityTips",
    method: "get",
    params: params
  });
}
