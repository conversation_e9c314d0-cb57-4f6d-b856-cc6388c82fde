import request from '@/utils/request'

// 获取列表
export function getpassportList (param) {
  return request({
    url: '/licppt/page',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}

// 获取详情
export function getpassportByPk (pk) {
  return request({
    url: '/licppt/itm/' + pk,
    method: 'get'
  })
}

// 删除
export function delpassport (param) {
  return request({
    url: '/licppt/del',
    method: 'delete',
    data: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 新增
export function addPassport (data) {
  return request({
    url: '/licppt/add',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 保存
export function updPassport (data) {
  return request({
    url: '/licppt/upd',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


// 获取通行证路线
export function getPassportRteLine (param) {
  return request({
    url: '/rteline/page',
    method: 'get',
    params: Object.assign({}, param)
  })
}

/*
 *新增通行证/围栏接口
 *@param {string} catCd 线路类型 1107.150:路线 1107.160:区域
 *@param {string} line 经纬度集合
 *@param {string} label 线路/区域名称
 */
export function addPassportRteLine (data) {
  return request({
    url: '/rteline/save',
    method: 'POST',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


/*
 *修改通行证线路/围栏接口
 *@param {string} catCd 线路类型 1107.150:路线 1107.160:区域
 *@param {string} line 经纬度集合
 *@param {string} label 线路/区域名称
 *@param {long} rteLinePk 线路主键
 */
export function updPassportRteLine (data) {
  return request({
    url: '/rteline/update',
    method: 'POST',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

/*
 *删除通行证线路/围栏接口
 *@param {string} catCd 线路类型 1107.150:路线 1107.160:区域
 *@param {string} line 经纬度集合
 *@param {string} label 线路/区域名称
 *@param {long} rteLinePk 线路主键
 */
export function delPassportRteLine (data) {
  return request({
    url: '/rteline/delete',
    method: 'POST',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


/*
 *根据通行证主键查询线路
 *@param {string} licPptPk 通行证主键
*/
export function findRouteByLicPptPk (params) {
  return request({
    url: '/rteline/findRouteByLicPptPk',
    method: 'GET',
    params: params
  })
}


/*
 *根据车辆主键查询线路
 *@param {string} vecPk 车辆主键
*/
export function findRouteByVecPk (params) {
  return request({
    url: '/rteline/findRouteByVecPk',
    method: 'GET',
    params: params
  })
}

/*
 *通行证续办
 *@param {String} validDate 通行证有效期
 *@param {String} licPptPk 通行证主键
*/
export function licpptGoOn (data) {
  return request({
    url: '/licppt/goOn',
    method: 'post',
    data: data
  })
}

/*
 *通行证审核
 */
export function licpptAudit (data) {
  return request({
    url: '/licppt/audit',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 通行证上传
export function uploadFile (data) {
  return request({
    url: "/licppt/impt",
    method: "post",
    data: data,
  });
}
// 通行证导入状态
export function imptStatus (data) {
  return request({
    url: "/licppt/imptStatus",
    method: "get",
    data: data,
  });
}
// 通行证错误信息
export function imptErr () {
  return request({
    url: "/licppt/imptErr",
    method: "get"
  });
}