import request from '@/utils/request'

// 保存异常人员信息
export function savePersrmks(data){
	return request({
		url:'/persrmks/save',
		method:'post',
		data:data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取列表
export function getPersrmksList(param){
	return request({
		url:'/persrmks/list',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 获取详情
export function getPersrmksInfo(id){
	return request({
		url:'/persrmks/info/'+id,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 删除
export function deletePersrmks(id){
	return request({
		url:'/persrmks/delete?id='+id,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 修改
export function updPersrmks(data){
	return request({
		url:'/persrmks/update',
		method:'post',
		data:data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 
export function getFuzzyPersonalType(){
	return request({
		url:'persrmks/fuzzyPersonalType',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}