import request from '@/utils/request'


//装卸企业围栏
export function getEntpLocList(params){
	return request({
		url:'/entp/page',
        method:'get',
        params:params,
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}


//装卸企业围栏详情
export function getListEntp(entpPk){
	return request({
		url:'/entpLoc/listEntp/'+entpPk,
        method:'post',
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}


//装卸企业围栏新增
export function getEntpLocAdd(data){
	return request({
		url:'/entpLoc/add',
        method:'post',
        data:data,
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}


//装卸企业围栏更新
export function getEntpLocUpd(data){
	return request({
		url:'/entpLoc/upd',
        method:'post',
        data:data,
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}

//装卸企业围栏删除
export function delEntpLoc(params){
	return request({
		url:'/entpLoc/del',
        method:'get',
        params:params,
        header:{
            'Content-type': 'application/x-www-form-urlencoded'
        }
	})
}