import request from '@/utils/request'

// 在途可视左侧车辆列表接口
export function getGpsVecListByPage(param){
	return request({
		url:'/gps/page',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
		}
	})
}

// 历史轨迹左侧车辆列表接口
export function getVecListOfGpsTraceByPage(param){
	return request({
		url:'/gps/queryCarList',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
		}
	})
}

// 在途可视实时定位接口
export function getAllVecListWithGPS(param){
	return request({
		url:'/gps/list',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据日期，获取车辆gsp的历史轨迹
export function getVecGpsTraceByDate(data){
	return request({
		url:'/gps/today',
		method:'get',
		params:{
			t:data.t,
			v:data.v
		},
		headers: {
			'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
		}
	})
}
// 在途可视区内外车
export function getLocalCarList(type){
	return request({
		url:'/gps/inoutlocalcarlist',
		method:'get',
		params:{type:type},
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 在途可视全国车辆
export function getNationwideVecWidthGps(params){
	return request({
		url:'/gps/list4Cn',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 在途可视市外车辆
export function getSuburbVecWidthGps(params){
	return request({
		url:'/gps/listshiwai',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 重点道路车辆
export function getLoadStress(){
	return request({
		url:'/aliDatav/keyRoadsvec',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 获取装卸企业
export function findAreaByEntp(params){
	return request({
		url:'/entp/cp/list',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 根据企业pk获取装卸企业信息
export function getEntpByCpPk(entpPk){
	return request({
		url:'entp/cp/info?ipPk='+entpPk,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据企业pk获取装卸记录
export function getStevedor(data){
	return request({
		url:'argmwt/dayentp',
		method:'get',
		params:data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 根据企业pk获取货物列表
export function getChem(ipPk){
	return request({
		url:'entp/cp/goods?ipPk='+ipPk,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据车牌号查询GPS信息
export function findGpsByVecNo(vecNo){
	return request({
		url:'gps/findGpsByVecNo?vecNo='+vecNo,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取加油站，化工园区等
export function findArea(params){
	return request({
		url:'/area/list',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据车辆类型查询市内粤K开头的GPS
export function getCityInVec(params){
	return request({
		url:'/gps/listbendishinei',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 根据车辆类型查询市内非粤K开头的GPS
export function getCityOutVec(params){
	return request({
		url:'/gps/listwaidishinei',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 获取化工园区内的车
export function getPetrochemicalvec(params){
	return request({
		url:'/aliDatav/petrochemicalvec',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
// 获取化工园区内的企业
export function findPetrochemicalEntp(params){
	return request({
		url:'/entp/petrochemical/list',
		method:'get',
		params:params,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// param {Number} areaPk
export function getHgyqinfo(areaPk){
	return request({
		url:'/area/hgyqinfo?areaPk='+areaPk,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/* 化工园区报警数量
	@param {Number} areaPk
	@param {Number} page
	@param {Number} limit
*/
export function getHgyqalarmpage(param){
	return request({
		url:'/alarm/hgyqalarmpage',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


// 操作员列表
export function oprlist() {
	return request({
	  url: '/alarm/oprlist',
	  method: 'get'
	})
}