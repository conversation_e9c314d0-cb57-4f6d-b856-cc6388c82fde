import request from '@/utils/request'

// 获取用户列表
export function getPersList(param){
	return request({
		url:'/pers/page',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 获取用户详情
export function getPersByPk(pk){
	return request({
		url:'/pers/itm/'+pk,
		method:'get'
	})
}

// 删除
export function delPers(param){
	return request({
		url:'/pers/del',
		method:'post',
		data:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 新增
export function addPers(data){
	return request({
		url:'/pers/add',
		method:'post',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 保存
export function updPers(data){
	return request({
		url:'/pers/upd',
		method:'post',
		data: data,
		headers: {
			'Content-type':  'application/json;charset=UTF-8'
		}
	})
}

// 解聘人员
export function firePers(param){
	return request({
		url:'/pers/fire',
		method:'post',
		data:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 根据人员类型catCd，模糊搜索人员
export function getFuzzyPers(catCd,nmCn){
    return request({
		url:'/pers/fuzzyBw?catCd='+catCd+'&nmCn='+encodeURIComponent(nmCn),
		method:'get'
	});
}

//人员证照完成度
export function getCountPersComplete(ipPks){
    return request({
		url:'/pers/countPersComplete?ipPks='+ipPks,
		method:'get',
		headers:{
			'Content-type':  'application/json;charset=UTF-8'
		}
	});
}

//人员信息验证接口
export function persValidInfo(ipPk){
	return request({
		url:"/pers/validPersInfo/"+ipPk,
		method:"get"
	});
}
