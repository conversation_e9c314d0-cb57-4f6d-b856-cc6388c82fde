import request from '@/utils/request'
// 获取列表
export function getRtePlanList(param){
	return request({
		url:'/rtePlan/listNoCount',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}

	})
}
// 根据rtePlanPk获取详情
export function getRtePlanByPk(pk){
  return request({
    url:'/rtePlan/detail/'+pk,
    method:'get'
  })
}
// 根据rtePlanPk获取详情 ---新接口
export function getRtePlanNewByPk(pk) {
	return request({
	  url: '/rtePlan/getDtlById?id=' + pk,
	  method: 'get'
	})
  }

// 根据rtePlanCd获取详情
export function getRtePlanByCd(cd){
	return request({
		url:'/rtePlan/history?rtePlanCd='+cd,
		method:'get'
	})
}
// 新增
export function addRtePlan(data){
	return request({
		url:'/rtePlan/add',
		method:'post',
		timeout:'10000',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 保存
export function updRtePlan(data){
	return request({
		url:'/rtePlan/upd',
		method:'post',
		timeout:'10000',
		data: data,
		headers: {
			'Content-type':  'application/json;charset=UTF-8'
		}
	})
}

// 删除
export function delRtePlane(param){
	return request({
		url:'/rtePlan/del',
		method:'delete',
		params:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 获取省份信息
export function getProvs(){
	return request({
		url:'/regCode/provs',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据省份获取市
export function getCitysByPk(pk){
	return request({
		url:'/regCode/citys?pk='+pk,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据市获取区
export function getDistsByPk(pk){
	return request({
		url:'/regCode/dists?pk='+pk,
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取省市区信息
export function getRegCode(){
	return request({
		url:'/regCode/all',
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据车牌号获取该车最近一次电子运单记录
export function getLastRtePlanByTracCd(tracCd){
	return request({
		url:'/rtePlan/currentRtePlan?tracCd='+encodeURIComponent(tracCd),
		method:'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取二维码
export function getQRCode(argmtPk) {
	return request({
	  url: '/rtePlan/rtePlanStr?rtePlanPk=' + argmtPk,
	  method: 'get',
	  headers: {
		'Content-type': 'application/json;charset=UTF-8'
	  }
	})
  }
  // 获取装货单
export function getOrderloadByCd(data){
	return request({
		url:'/orderload/page',
		params:data,
		method:'post'
	})
}

// 获取卸货单
export function getOrderunloadByCd(data){
	return request({
		url:'/orderunload/page',
		params:data,
		method:'post'
	})
}

// 获取回执单
export function getOrderreceiptByCd(cd){
	return request({
		url:'orderreceipt/info?rteplanCd='+cd,
		method:'get'
	})
}
