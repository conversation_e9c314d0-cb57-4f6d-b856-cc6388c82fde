import request from "@/utils/request";

// 获取列表
export function getVecList(param) {
  return request({
    url: "/vec/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取详情
export function getVecByPk(pk) {
  return request({
    url: "/vec/itm/" + pk,
    method: "get"
  });
}

// 删除
export function delVec(param) {
  return request({
    url: "/vec/del",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 模糊搜索关联车牌号（罐体新增需要）
export function getFuzzyTraiNo(vecNo) {
  return request({
    url: "/vec/tankFuzzy?catCd=1180.155&vecNo=" + vecNo,
    method: "get"
  });
}

// 根据车型catCd，模糊搜索车牌号  牵引车catCd：1180.154，挂车catCd：1180.155
export function getFuzzyTracCd(catCd, vecNo) {
  return request({
    url: "/vec/fuzzyBw?catCd=" + catCd + "&vecNo=" + vecNo,
    method: "get"
  });
}

// 解聘车辆
export function fireVec(param) {
  return request({
    url: "/vec/fire",
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/x-www-form-urlencoded"
    }
  });
}

// 新增
export function addVec(data) {
  return request({
    url: "/vec/add",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 保存
export function updVec(data) {
  return request({
    url: "/vec/upd",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//完成度
export function countVecComplete(vecPks) {
  return request({
    url: "/vec/countVecComplete?vecPks=" + vecPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//车辆gps更新时间
export function getLatestGpsTime(vecNos) {
  return request({
    url: "/gps/findGpsByVecNos?vecNos=" + vecNos,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
//验证车辆基本信息
export function validateVceInfo(vecNo) {
  return request({
    url: "/vec/validVecInfo?vecNo=" + vecNo,
    method: "POST",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 车辆信用分
export function getVecScore(vecPks) {
  return request({
    url: "/vec/vecScore?vecPks=" + vecPks,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 车辆扣分详情
export function getVecScoreInfo(vecNo) {
  return request({
    url: "/alarm/vecScore?vecNo=" + vecNo,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 车辆扣分描述
export function getScoreRemark(vecNo) {
  return request({
    url: "/vec/getScoreRemark",
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
