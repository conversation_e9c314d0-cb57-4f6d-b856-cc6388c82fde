import request from "@/utils/request";

// 获取列表
export function getViolationAlarmList(param) {
  return request({
    url: "/alarm/page",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
//获取重点车辆列表
export function getImportVecList(param) {
  return request({
    url: "/alarm/majorVec",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取车辆线路
export function getRouteByVecPk(param) {
  return request({
    url: "/rteline/findRouteByVecPk",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//
export function getUnfinishLast(param) {
  return request({
    url: "/rtePlan/getUnfinishLast",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get"
  });
}
// 违章报警处理类型
export function getDealType() {
  return request({
    url: "/aliDatav/handlertype",
    method: "get"
  });
}
// 违章报警处理
export function dealSubmit(data) {
  return request({
    url: "/alarm/handle",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}
// 违章报警审核
export function apprSubmit(data) {
  return request({
    url: "/alarm/audit",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取报警信息
export function getAlarmListBySimpleQuery(data) {
  return request({
    url: "/alarm/list",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 违章报警导出
export function downloadExcel(data) {
  return request({
    url: "/alarm/download",
    method: "post",
    params: data,
    responseType: "blob"
  });
}

// 获取列表
export function getRealtimeAlarmList(param) {
  return request({
    url: "/alarm/realtime",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警前后半小时轨迹
export function getRouteByHour(id) {
  return request({
    url: "/alarm/selectAlarmPre?id=" + id,
    method: "get"
  });
}
// 报警车辆信息
export function getAlarmVec(params) {
  return request({
    url: "/alarm/alarmtodaybyvecno",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取经过区域内的车辆信息
export function getCarListOfPassingArea(param) {
  return request({
    url: `/gps/polygon`,
    method: "post",
    data: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取车辆轨迹
export function getTrack(data) {
  return request({
    url: "/gps/history",
    method: "get",
    params: data
  });
}

// 获取所有围栏数据
export function getFenceList() {
  return request({
    url: "/entpLoc/cplist",
    method: "get"
  });
}
