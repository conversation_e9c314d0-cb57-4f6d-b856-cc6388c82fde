import request from '@/utils/request'
// 获取列表
export function getRecordList(param) {
  return request({
    url: '/argmtbefcheck/listAll',
    method: 'get',
    params: param,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }

  })
}
// 装卸前安全检查详情
/**
 * @param {String} rtePlanCd
 * @param {int} type
 */
export function getArgmtbefcheckDtl(recordId) {
  return request({
    url: "/argmtbefcheck/info/" + recordId,
    // params: param,
    method: "get",
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  });
}
// 装卸后安全检查详情
export function getArgmtaftcheckDtl(recordId) {
  return request({
    url: "/argmtaftcheck/info/" + recordId,
    method: "get",
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  });
}
export function getArgmtbefcheckDtlByCd(cd) {
  return request({
    url: '/argmtbefcheck/findByCd?cd=' + cd,
    method: 'get',
  })
}
// 装卸后安全检查详情
export function getArgmtaftcheckDtlByCd(cd) {
  return request({
    url: '/argmtaftcheck/findByCd?cd=' + cd,
    method: 'get',
  })
}
//装卸企业列表
export function getEntpList(params) {
  return request({
    url: '/entp/page',
    method: 'get',
    params: params,
    header: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 获取对应查验的查验项
 * @param {string} orderNo
 * @param {number} checkType
 */
export const checkConfigDtl = () =>
  request({
    url: "/cp/check/config/detail",
    method: "get"
  });

/**
 * 新版本获取证照信息
 * @param {string} pk
 * @param {string} catCd
 * @param {string} type
 */
export const checkLic = (param) =>
  request({
    url: "/cp/check/lic",
    method: "get",
    params: param
  });

/**
 * 获取对应查验的查验项
 * @param {string} orderNo
 * @param {number} checkType
 */
export const checkConfig = (param) =>
  request({
    url: "/cp/check/config",
    method: "get",
    params: param
  });

//导出列表
export function exportList(pks) {
  return request({
    url: '/argmtbefcheck/export/check?pks=' + pks,
    method: 'get',
    responseType: 'blob',

  })
}




/**
 * 装卸查验装货前历史详情
 * @param {String} id 
 * @param {String} year 
 * @returns 
 */
export function getBefHisDtlById(param){
	return request({
		url:'/argmtbefcheck/getHisDtlById',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


/**
 * 装卸查验装货后历史详情
 * @param {String} id 
 * @param {String} year 
 * @returns 
 */
export function getAftHisDtlById(param){
	return request({
		url:'/argmtaftcheck/getHisDtlById',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


//装卸企业列表
export function getArgmtbefcheckList(params) {
  return request({
    url: '/argmtbefcheck/listHis',
    method: 'get',
    params: params,
    header: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 获取对应历史查验的查验项
 * @param {string} orderNo
 * @param {number} checkType
 * @param {number} year
 */
export const hisCheckConfig = (param) =>
  request({
    url: "/cp/check/configHis",
    method: "get",
    params: param
  });