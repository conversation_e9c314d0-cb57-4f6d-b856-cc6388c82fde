import request from "@/utils/request";

// 获取登记点检查列表
export function getRegistrationList(params) {
  return request({
    url: "/zhckinsprecord/page",
    method: "get",
    params: params
  });
}

// 详情
export function getRegistrationInfo(id) {
  return request({
    url: "/zhckinsprecord/info/" + id,
    method: "get"
  });
}

// 获取登记点点位列表
export function getRegPots() {
  return request({
    url: "/zhckinsprecord/getRegPots",
    method: "get"
  });
}

// 保存
export function saveData(data) {
  return request({
    url: "/zhckinsprecord/save",
    method: "post",
    data: data
  });
}

// 修改
export function updateData(data) {
  return request({
    url: "/zhckinsprecord/update",
    method: "post",
    data: data
  });
}

// 删除
export function deleteData(data) {
  return request({
    url: "/zhckinsprecord/delete",
    method: "post",
    data: data
  });
}

// 导出
export function exportWord(id) {
  return request({
    url: "/zhckinsprecord/exportWord/" + id,
    method: "get",
    responseType: "blob"
  });
}
