import request from '@/utils/request'


//月违章统计接口
export function queryByMonthOfAlarm(month){
    return request({
        url:'/day/alarm/queryByMonth',
        method:'GET',
        params:month
    })
}

//月摄像头抓拍统计接口   
export function queryByMonthToCamera(month){
    return request({
        url:'/day/camera/queryByMonth',
        method:'GET',
        params:month
    })
}

//月进出园区车辆统计接口
export function queryByMonthOfGeo(month){
    return request({
        url:'/day/geo/queryByMonth',
        method:'GET',
        params:month
    })
}

//根据月份查询当月top10充装货物的装卸量
export function listTop10(month){
    return request({
        url:'/day/goods/listTop10GoodsByMonth',
        method:'GET',
        params:month
    })
}

//月装卸货量统计
export function queryByMonthOfLoadUnload(month){
    return request({
        url:'/day/loadunload/queryByMonth',
        method:'GET',
        params:month
    })
}


// 违章报警导出
export function downloadAlarmExcel(data){
	return request({
		url:'/day/alarm/download',
		method:'post',
		params: data,
		responseType: 'blob',
	})
}

//月摄像头抓拍统计导出
export function downloadCameraExcel(month){
    return request({
        url:'/day/camera/download',
        method:'GET',
        params:month,
        responseType: 'blob'
    })
}

//月进出园区车辆统计导出
export function downloadGeoExcel(month){
    return request({
        url:'/day/geo/download',
        method:'GET',
        params:month,
        responseType: 'blob'
    })
}

//根据月份查询当月top10充装货物的装卸量
export function downloadGoodsExcel(month){
    return request({
        url:'/day/goods/download',
        method:'GET',
        params:month,
        responseType: 'blob'
    })
}

//月装卸货量统计导出
export function downloadLoadunloadExcel(month){
    return request({
        url:'/day/loadunload/download',
        method:'GET',
        params:month,
        responseType: 'blob'
    })
}