import request from "@/utils/request";

// 获取列表
export function getTemplatePage(param) {
  return request({
    url: "/lic/ppt/v2/template/page",
    method: "get",
    params: param,
  });
}

// 获取详情及上次有效详情
export function getTemplateDetail(id) {
  return request({
    url: "/lic/ppt/v2/template/detail/" + id,
    method: "get",
  });
}


// 获取所有路线
export function getAllRouteLine(params) {
  return request({
    url: "/rteline/list",
    method: "get",
    params: params
  });
}

// 根据装卸企业获取默认线路
export function getDefaultRoute(params) {
  return request({
    url: "/lic/ppt/v2/template/defaultRoute",
    method: "get",
    params: params,
  });
}


// 新增路线
export function addRoute(data) {
  return request({
    url: "/lic/ppt/v2/template/addRoute",
    method: "post",
    data: data,
  });
}

// 提交
export function submit(data) {
  return request({
    url: "/lic/ppt/v2/template/submit",
    method: "post",
    data: data,
  });
}

//打印
export function print(params) {
  return request({
    url: "/lic/ppt/v2/template/export/summary",
    method: "get",
    params:params,
    responseType: "blob"
  });
}

//审批受理
export function receive(data) {
  return request({
    url: "/lic/ppt/v2/template/receive",
    method: "post",
    data: data
  });
}

//审批通过
export function approve(data) {
  return request({
    url: "/lic/ppt/v2/template/approve",
    method: "post",
    data: data
  });
}

//审批驳回
export function reject(data) {
  return request({
    url: "/lic/ppt/v2/template/reject",
    method: "post",
    data: data
  });
}