import request from "@/utils/request";

// 获取列表
export function getTemplatePage(param) {
  return request({
    url: "/lic/ppt/v2/template/page",
    method: "get",
    params: param,
  });
}

// 获取详情及上次有效详情
export function getTemplateDetail(id) {
  return request({
    url: "/lic/ppt/v2/template/detail/" + id,
    method: "get",
  });
}


// 创建模板
export function createOrUpdateTemplate(data) {
  return request({
    url: "/lic/ppt/v2/template/createOrUpdate",
    method: "post",
    data: data,
  });
}
// 创建模板
export function deleteTemplate(data) {
  return request({
    url: "/lic/ppt/v2/template/delete",
    method: "post",
    data: data,
  });
}


// 新增合同
export function addContract(data) {
  return request({
    url: "/lic/ppt/v2/template/addContract",
    method: "post",
    data: data,
  });
}
// 修改合同
export function updateContract(data) {
  return request({
    url: "/lic/ppt/v2/template/updContract",
    method: "post",
    data: data,
  });
}

// 删除合同
export function deleteContract(params) {
  return request({
    url: "/lic/ppt/v2/template/delContract",
    method: "post",
    data: params,
  });
}

// 新增车辆
export function addVehicle(data) {
  return request({
    url: "/lic/ppt/v2/template/addVehicle",
    method: "post",
    data: data,
  });
}

// 删除车辆
export function deleteVehicle(data) {
  return request({
    url: "/lic/ppt/v2/template/delVehicle",
    method: "post",
    data: data,
  });
}

// 获取所有路线
export function getAllRouteLine(params) {
  return request({
    url: "/rteline/list",
    method: "get",
    params: params
  });
}

// 根据装卸企业获取默认线路
export function getDefaultRoute(params) {
  return request({
    url: "/lic/ppt/v2/template/defaultRoute",
    method: "get",
    params: params,
  });
}


// 新增路线
export function addRoute(data) {
  return request({
    url: "/lic/ppt/v2/template/addRoute",
    method: "post",
    data: data,
  });
}

// 提交
export function submit(data) {
  return request({
    url: "/lic/ppt/v2/template/submit",
    method: "post",
    data: data,
  });
}

//打印
export function print(id) {
  return request({
    url: "/lic/ppt/v2/template/print/" + id,
    method: "get",
    responseType: "blob",
  });
}