import request from '@/utils/request'
// 获取列表
export function getRtePlanList(param) {
	return request({
		url: '/rtePlan/listNoCount',
		method: 'get',
		params: param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}

	})
}
// 根据rtePlanPk获取详情
export function getRtePlanByPk(pk) {
	return request({
		url: '/rtePlan/detail/' + pk,
		method: 'get'
	})
}
// 根据rtePlanPk获取详情 ---新接口
export function getRtePlanNewByPk(pk) {
	return request({
		url: '/rtePlan/getDtlById?id=' + pk,
		method: 'get'
	})
}

// 根据rtePlanCd获取详情
export function getRtePlanByCd(cd) {
	return request({
		url: '/rtePlan/history?rtePlanCd=' + cd,
		method: 'get'
	})
}

// 获取装货单
export function getOrderloadByCd(rteplanCd) {
	return request({
		// url:"orderload/page",
		url: '/argmwt/allListNoCount',
		params: {
			filters: {
				"groupOp": "AND",
				"rules": [
					{ "field": "argmt_cd", "op": "eq", "data": rteplanCd },
					{ "field": "ic_cd", "op": "eq", "data": "1" }
				]
			},
			page: 1,
			limit: 20,
		},
		method: 'get'
	})
}

// 获取卸货单
export function getOrderunloadByCd(rteplanCd) {
	// return request({
	// 	url:'/orderunload/page',
	// 	params:data,
	// 	method:'get'
	// })
	return request({
		url: '/argmwt/allListNoCount',
		params: {
			filters: {
				"groupOp": "AND",
				"rules": [
					{ "field": "argmt_cd", "op": "eq", "data": rteplanCd },
					{ "field": "ic_cd", "op": "eq", "data": "-1" }
				]
			},
			page: 1,
			limit: 20,
		},
		method: 'get'
	})
}

// 获取回执单
export function getOrderreceiptByCd(data) {
	return request({
		url: 'orderreceipt/page',
		params: data,
		method: 'post'
	})
}

// 新增
export function addRtePlan(data) {
	return request({
		url: '/rtePlan/add',
		method: 'post',
		timeout: '10000',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 保存
export function updRtePlan(data) {
	return request({
		url: '/rtePlan/upd',
		method: 'post',
		timeout: '10000',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 删除
export function delRtePlane(param) {
	return request({
		url: '/rtePlan/del',
		method: 'delete',
		params: param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 获取省份信息
export function getProvs() {
	return request({
		url: '/regCode/provs',
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据省份获取市
export function getCitysByPk(pk) {
	return request({
		url: '/regCode/citys?pk=' + pk,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据市获取区
export function getDistsByPk(pk) {
	return request({
		url: '/regCode/dists?pk=' + pk,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取省市区信息
export function getRegCode() {
	return request({
		url: '/regCode/all',
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据车牌号获取该车最近一次电子运单记录
export function getLastRtePlanByTracCd(tracCd) {
	return request({
		url: '/rtePlan/currentRtePlan?tracCd=' + encodeURIComponent(tracCd),
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取二维码
export function getQRCode(argmtPk) {
	return request({
		url: '/rtePlan/rtePlanStr?rtePlanPk=' + argmtPk,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 根据rtePlanCd获取事件id
export function getEventId(cd) {
	return request({
		url: "/rtePlan/getEventId?cd=" + cd,
		method: "get"
	});
}

// 根据rtePlanCd刷新运单四状态
export function getRtePlanStatus(cd) {
	return request({
		url: "/rtePlan/getRtePlanStatus/" + cd,
		method: "get"
	});
}

// 获取历史运单列表
export function getHistoryList(param) {
	return request({
		url: '/rtePlan/listHis',
		method: 'get',
		params: param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取历史运单年份列表
export function getHisYearList(param) {
	return request({
		url: '/rtePlan/listHisYear',
		method: 'get',
		params: param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 批量获取安全分接口
export function getSecurityCodeByIds(ipPks) {
	return request({
		url: '/securityCode/getByIds?ipPks=' + ipPks,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	});
}
// 根据运单argmtPk批量获取驾驶员安全分接口
export function getSecurityCodeByArgmtPks(argmtPks) {
	return request({
		url: '/securityCode/getByRtePlanPks?argmtPks=' + argmtPks,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	});
}
// 运单统计列表列表
export function getRteplanStatList(params) {
	return request({
		url: "/rteplanStatExport/list",
		params: params,
		method: "get",
		headers: {
			"Content-type": "application/json;charset=UTF-8",
		},
	});
}

// 运单统计导出
export function rteplanStatExport(params) {
	return request({
		url: "/rteplanStatExport/export",
		params: params,
		method: "get",
		headers: {
			"Content-type": "application/json;charset=UTF-8",
		},
	});
}


// 根据rtePlanPk获取历史运单详情 ---新接口
export function getHistoryRtePlanNewByPk(params) {
	return request({
	  url: "/rtePlan/getHisDtlById",
	  method: "get",
	  params: params,
	});
  }