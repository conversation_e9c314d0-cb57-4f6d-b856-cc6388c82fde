import request from "@/utils/request";

// 获取装卸记录
export function LoadAndUnloadList(param){
	return request({
		url:'/argmwt/allListNoCount',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}

	})
}


//装卸企业列表
export function getEntpList(params){
  return request({
    url:'/entp/page',
    method:'get',
    params:params,
    header:{
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

//导出列表
export function exportList(pks){
  return request({
    url:'/argmwt/export/wt?pks='+pks,
    method:'get',
    responseType: 'blob',

  })
}

// 获取历史装卸记录
export function LoadAndUnloadListHis(param){
	return request({
		url:'/argmwt/listHis',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}
