import request from '@/utils/request'


/* 基本信息统计 */
/*
 *人员年龄分布 
 */
export function getPersAgeStat(param){
	return request({
		url:'/dataStat/getPersAgeStat',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}

	})
}

/*
 *人员区域分布 
 */
export function gePersDis(param){
	return request({
		url:'/dataStat/getPersDis',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *基本信息统计 
 */
export function getBasicData(param){
	return request({
		url:'/dataStat/getBasicData',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *车辆区域分布统计 
 */
export function geVecDistribute(param){
	return request({
		url:'/dataStat/vecDistribute',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


/*
 *人员换岗次数情况 
 */
export function getEntpPersFlow(param){
	return request({
		url:'/dataStat/getEntpPersFlow',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


/*
 *牵引车类型 
 */
export function getTracTypeStat(param){
	return request({
		url:'/dataStat/getTracTypeStat',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/* 装卸统计 */

/*
 *装卸总量统计 
 */
export function getLoadAndUnloadCntByMonth(param){
	return request({
		url:'/sampledata/2017stat/getLoadAndUnloadCntByMonth',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *区内登记点统计 
 */
export function getRegPointVecCnt(param){
	return request({
		url:'/sampledata/2017stat/getRegPointVecCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *区内主要企业装货量统计 
 */
export function getEntpLoadGoodsCnt(param){
	return request({
		url:'/sampledata/2017stat/getEntpLoadGoodsCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *区内主要企业卸货量统计 
 */
export function getEntpUnloadGoodsCnt(param){
	return request({
		url:'/sampledata/2017stat/getEntpUnloadGoodsCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *区内装货种类统计 
 */
export function getLoadCnt(param){
	return request({
		url:'/sampledata/2017stat/getLoadCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *区内卸货种类统计 
 */
export function getUnloadCnt(param){
	return request({
		url:'/sampledata/2017stat/getUnloadCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/* 违章统计 */

/*
 *违章趋势图统计 
 */
export function getAlarmCntByMonth(param){
	return request({
		url:'/sampledata/2017stat/getAlarmCntByMonth',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *违章类型统计 
 */
export function getAlarmType(param){
	return request({
		url:'/dataStat/alarmType',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *主要超速路段 
 */
export function getOverSpeedAddress(param){
	return request({
		url:'/dataStat/overSpeedAddress',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *主要停车地点 
 */
export function getStopAlarmAddress(param){
	return request({
		url:'/dataStat/stopAlarmAddress',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


/* 运单统计 */

/*
 *车辆进出镇海统计 
 */
export function getInoutCnt(param){
	return request({
		url:'/sampledata/2017stat/getInoutCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *电子运单统计 
 */
export function getRteplanTotalCnt(param){
	return request({
		url:'/sampledata/2017stat/getRteplanTotalCnt',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *进出镇海统计 
 */
export function getInAndOutCntByMonth(param){
	return request({
		url:'/sampledata/2017stat/getInAndOutCntByMonth',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *镇海区货物类别统计 
 */
export function getRtePlanGoodsType(param){
	return request({
		url:'/sampledata/2017stat/getRtePlanGoodsType',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *镇海区装运货物情况统计 
 */
export function getMonthRteplanGoodsQty(param){
	return request({
		url:'/sampledata/2017stat/getMonthRteplanGoodsQty',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

/*
 *镇海区装运车次统计
 */
export function getMonthRteplanVecDesp(param){
	return request({
		url:'/sampledata/2017stat/getMonthRteplanVecDesp',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}