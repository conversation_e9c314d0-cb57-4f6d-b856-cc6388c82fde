import request from '@/utils/request'


//围栏
export function getList(){
	return request({
		url:'/entpLoc/entpcarstoplist',
        method:'get',
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}
//获取停车场内的车
export function getentpcarbyareapk(param){
	return request({
		url:'/entpLoc/getentpcarbyareapk',
        method:'get',
        params:param,
        header:{
            'Content-type': 'application/json;charset=UTF-8'
        }
	})
}