import request from "@/utils/request";

// 列表
export function getConfigList(param) {
  return request({
    url: "/sysconfig/list",
    method: "get",
    params: param,
  });
}

export function deleteConfig(data) {
  return request({
    url: "/sysconfig/delete",
    method: "post",
    data: data,
  });
}

export function getConfigInfo(id) {
  return request({
    url: "/sysconfig/info/" + id,
    method: "get",
  });
}

export function addConfig(data) {
  return request({
    url: "/sysconfig/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}

export function updConfig(data) {
  return request({
    url: "/sysconfig/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8",
    },
  });
}
