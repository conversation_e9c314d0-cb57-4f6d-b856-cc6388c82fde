import request from "@/utils/request";

// 列表
export function getMenuList(param) {
  return request({
    url: '/sys/menu/list',
    method: 'get',
    params: param
  })
}
// 所属模块
export function getModuleList() {
  return request({
    url: '/sys/menu/getModule',
    method: 'get',
  })
}
// 所属项目列表
export function getSysList(id) {
  return request({
    url: '/sys/menu/sysNbr?areaId=' + id,
    method: 'get',
  })
}

export function deleteMenu(id) {
  return request({
    url: '/sys/menu/delete/' + id,
    method: 'post',
  })
}

export function getMenuInfo(id) {
  return request({
    url: '/sys/menu/info/' + id,
    method: 'get'
  })
}

export function addMenu(data) {
  return request({
    url: '/sys/menu/save',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

export function updMenu(data) {
  return request({
    url: '/sys/menu/update',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
