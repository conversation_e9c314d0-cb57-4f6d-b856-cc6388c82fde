import request from "@/utils/request";

// 微信用户列表
export function getUserwechatList(param) {
  return request({
    url: '/userwechat/list',
    method: 'get',
    params: param
  })
}

// 用户列表
export function getUserList(param) {
  return request({
    url: '/sys/user/list',
    method: 'get',
    params: param
  })
}

export function deleteUser(data) {
  return request({
    url: '/sys/user/delete',
    method: 'post',
    data: data
  })
}

export function disableUser(data) {
  return request({
    url: '/sys/user/disable',
    method: 'post',
    data: data
  })
}

export function resetPassword(id) {
  return request({
    url: '/sys/user/resetPwd?userId=' + id,
    method: 'post'
  })
}

export function getUserInfo(id) {
  return request({
    url: '/sys/user/info/' + id,
    method: 'get'
  })
}

export function addUser(data) {
  return request({
    url: '/sys/user/save',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

export function updUser(data) {
  return request({
    url: '/sys/user/update',
    method: 'post',
    data: data,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
// 微信解绑
export function unbind(param) {
  return request({
    url: '/userwechat/delete',
    method: 'post',
    params: param,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    }
  })
}

export function LogOutRole(name) {
  return request({
    url: '/sys/user/force-logout?username=' + name,
    method: 'post'
  })
}

export function unlockRole(name) {
  return request({
    url: '/sys/user/unlock?username=' + name,
    method: 'post',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
