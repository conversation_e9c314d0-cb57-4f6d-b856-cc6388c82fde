import request from '@/utils/request'

// 获取列表
export function getTankList(param){
	return request({
		url:'/tank/page',
		method:'get',
		params:param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
		
	})
}

// 获取详情
export function getTankByPk(pk){
	return request({
		url:'/tank/itm/'+pk,
		method:'get'
	})
}

// 删除
export function delTank(param){
	return request({
		url:'/tank/del',
		method:'post',
		data:param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 新增
export function addTank(data){
	return request({
		url:'/tank/add',
		method:'post',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 保存
export function updTank(data){
	return request({
		url:'/tank/upd',
		method:'post',
		data: data,
		headers: {
			'Content-type':  'application/json;charset=UTF-8'
		}
	})
}

// 获取完成度
export function getTankDocComplete(pks){
	return request({
		url:'/tank/countTankComplete?cntrPks='+pks,
		method:'get'
	});
}


// 模糊搜索罐体编号
export function getFuzzyTankNum(tankNum){
    return request({
		url:'/tank/fuzzy?tankNum='+tankNum,
		method:'get'
	});
}




