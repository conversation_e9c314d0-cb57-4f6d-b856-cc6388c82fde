import request from "@/utils/request";

// 获取危化运输企业检查列表
export function getTransChencList(params) {
  return request({
    url: "/zhentpinterview/page",
    method: "get",
    params: params
  });
}

// 详情
export function getTransChencInfo(id) {
  return request({
    url: "/zhentpinterview/info/" + id,
    method: "get"
  });
}

// 保存
export function saveData(data) {
  return request({
    url: "/zhentpinterview/save",
    method: "post",
    data: data
  });
}

// 修改
export function updateData(data) {
  return request({
    url: "/zhentpinterview/update",
    method: "post",
    data: data
  });
}

// 删除
export function deleteData(data) {
  return request({
    url: "/zhentpinterview/delete",
    method: "post",
    data: data
  });
}

// 导出
export function exportExcel(id) {
  return request({
    url: "/zhentpinterview/exportExcel/" + id,
    method: "get",
    responseType: "blob"
  });
}

// 上传签名
export function uploadSign(data) {
  return request({
    url: "/zhentpinterview/uploadSign",
    method: "post",
    data: data
  });
}

//查询企业列表
export function getEntpList(params) {
  return request({
    url: "/zhentpinterview/entpList",
    method: "get",
    params: params
  });
}

// 扫码上传
export function getPicsInfo(params) {
  return request({
    url: "/zhckinsprecord/getImgs",
    method: "get",
    params: params
  });
}

// 登记点人员接口列表
export function getPersList() {
  return request({
    url: "/sys/dict/listByType",
    method: "get",
    params: {
      type: "登记点检查人员"
    }
  });
}
