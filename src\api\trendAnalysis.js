import request from "@/utils/request";


//  趋势分析-超速
export function alarmspeedthermal(params) {
  return request({
    url: "/public/alarmspeedthermal",
    method: "get",
    params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//  趋势分析-停车
export function alarmstopthermal(params) {
  return request({
    url: "/public/alarmstopthermal",
    method: "get",
    params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

export function getAllOutList(params) {
  return request({
    url: `/entpLoc/yizhangtulist`,
    method: "get"
  });
}