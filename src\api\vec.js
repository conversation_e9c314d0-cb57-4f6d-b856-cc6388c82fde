import request from '@/utils/request'

// 获取列表
export function getVecList(param) {
	return request({
		url: '/vec/page',
		method: 'get',
		params: param,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}

	})
}

// 获取详情
export function getVecByPk(pk) {
	return request({
		url: '/vec/itm/' + pk,
		method: 'get'
	})
}

// 删除
export function delVec(param) {
	return request({
		url: '/vec/del',
		method: 'post',
		data: param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 模糊搜索关联车牌号（罐体新增需要）
export function getFuzzyTraiNo(vecNo) {
	return request({
		url: '/vec/tankFuzzy?catCd=1180.155&vecNo=' + vecNo,
		method: 'get'
	});
}

// 根据车型catCd，模糊搜索车牌号  牵引车catCd：1180.154，挂车catCd：1180.155
export function getFuzzyTracCd(catCd, vecNo) {
	return request({
		url: '/vec/fuzzyBw?catCd=' + catCd + '&vecNo=' + vecNo,
		method: 'get'
	});
}

// 解聘车辆
export function fireVec(param) {
	return request({
		url: '/vec/fire',
		method: 'post',
		data: param,
		headers: {
			'Content-type': 'application/x-www-form-urlencoded'
		}
	})
}

// 新增
export function addVec(data) {
	return request({
		url: '/vec/add',
		method: 'post',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 保存
export function updVec(data) {
	return request({
		url: '/vec/upd',
		method: 'post',
		data: data,
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

//完成度
export function countVecComplete(vecPks) {
	return request({
		url: '/vec/countVecComplete?vecPks=' + vecPks,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

//车辆gps更新时间
export function getLatestGpsTime(vecNos) {
	return request({
		url: '/gps/findGpsByVecNos?vecNos=' + vecNos,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}


//验证车辆基本信息
export function validateVceInfo(vecNo) {
	return request({
		url: '/vec/validVecInfo?vecNo=' + vecNo,
		method: 'POST',
		headers: {
			'Content-type': 'application/json;charset=UTF-8'
		}
	})
}

// 获取车辆类型分类
export function getVecCategory() {
	return request({
		url: '/vec/getVecCategory',
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8',
		}
	})
}

// 获取设备类型字典
export function getEquipDict(vecPk) {
	return request({
		url: '/vec/equipDict/' + vecPk,
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8',
		}
	})
}

// 获取车辆类型
export function getVecType() {
	return request({
		url: '/vec/getVecType',
		method: 'get',
		headers: {
			'Content-type': 'application/json;charset=UTF-8',
		}
	})
}

// 运力申请列表
export function capacityPage(data) {
	return request({
	  url: "/capacityApplication/page",
	  method: "get",
	  params: data,
	  headers: {
		"Content-type": "application/json;charset=UTF-8",
	  },
	});
  }

// 运力申请审核
export function capacityAudit(data) {
	return request({
	  url: "/capacityApplication/audit",
	  method: "post",
	  data: data,
	  headers: {
		"Content-type": "application/json;charset=UTF-8",
	  },
	});
  }

// 需要审核的数量
export function needAuditCnt(data) {
	return request({
	  url: "/capacityApplication/needAuditCnt",
	  method: "get",
	  params: data,
	  headers: {
		"Content-type": "application/json;charset=UTF-8",
	  },
	});
  }

  // 查询登记车辆总数、登记人员总数、审核人员总数、审核车辆总数
export function capacityApplicationInfo(id) {
	return request({
	  url: "/capacityApplication/info/"+id,
	  method: "get",
	//   params: data,
	  headers: {
		"Content-type": "application/json;charset=UTF-8",
	  },
	});
  }
  