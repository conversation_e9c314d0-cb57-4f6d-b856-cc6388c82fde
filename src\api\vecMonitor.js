import request from "@/utils/request";
// 重点监控车辆列表接口
export function getList(params) {
    return request({
      url: "/vecMonitor/list",
      method: "get",
      params: params
    });
  }
// 删除
export function del(data) {
    return request({
      url: "/vecMonitor/delete",
      method: "post",
      data: data
    });
  }
  // 新增
  export function add(data) {
    return request({
      url: "/vecMonitor/save",
      method: "post",
      data: data
    });
  }
  // 编辑
  export function upd(data) {
    return request({
      url: "/vecMonitor/update",
      method: "post",
      data: data
    });
  }
  // 获取详情
export function info(id){
	return request({
		url:'/vecMonitor/info/'+id,
		method:'get'
	})
}
// 来镇预警
export function getAlarmList(param){
	return request({
		url:'/vecMonitor/alarmList',
		method:'get',
    params: param
	})
}