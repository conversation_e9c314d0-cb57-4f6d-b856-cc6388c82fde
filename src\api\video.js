import request from "@/utils/request";

// 获取视频列表
export function getVideoList(params) {
  return request({
    url: "/video/device/list",
    method: "get",
    params: params
  });
}


// 获取码头查验列表
export function getWharfList(params) {
  return request({
    url: "/zhWharfRecord/page",
    method: "get",
    params: params
  });
}

// 获取视频地址
/* export function getVideoUrl(number){
	return request({
		url:'/video/device/getStreamUrl',
		method:'get',
		params:{number:number}
	})
} */

// 获取卡口视频监控地址
export function getVideoStreamUrl(number) {
  return request({
    url: "/video/device/getStreamUrl",
    method: "get",
    params: { number: number }
  });
}

// 获取装卸货视频列表(分页)
export function getLoadAndUnloadVideoPage(params) {
  return request({
    url: "/video/device2/page",
    method: "get",
    params: params
  });
}

// 获取视频地址，传递number=XXX&urlType=3
// urlType: 1表示rtsp格式的URL;2表示http格式的URL;3表示rtmp格式的URL
// urlType： 1：rtsp 2：http 3：rtmp 4：hls 5：http-flv 6：rtmp（对方接口） 默认为4
export function getLoadAndUnloadVideoStreamUrl(number, type) {
  return request({
    url: "/video/device2/getStreamUrl",
    method: "get",
    params: { number: number, urlType: type ? type : 3 }
  });
}

// 获取装卸企业视频地址
export function getVideoEntpList() {
  return request({
    url: "/video/device2/getEntpList",
    method: "get"
  });
}


// 获取企业名称列表
export function getEntpList(params){
  return request({
    url: '/video/deviceHw/getEntpList',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取所有摄像头列表(直播)
export function getAllVideoList(params){
  return request({
    url: '/video/deviceHw/queryChannelList',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 触发停止直播接口
export function setExpire(str){
  return request({
    url: '/video/deviceHw/setExpire?flvStr=' + str,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取企业摄像头列表
export function getCamList(entpName){
  return request({
    url: '/video/deviceHw/getCamList?entpName=' + entpName,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取HUAWEI视频列表(直播)
export function getDeviceHwList(params,number,videoTime) {
  return request({
    url: "/video/deviceHw/list?number=" + number +"&videoTime=" + videoTime,
    params: params,
    method: "get"
  });
}


// 根据摄像头查列表(录播)
export function getCamVideoList(number,channelID,videoTime) {
  return request({
    url: "video/urlHw/list?number=" + number +"&channelID=" + channelID +"&videoTime=" + videoTime,
    method: "get"
  });
}

// 视频假死激活接口
export function getStreamStart(str){
  return request({
    url: '/video/deviceHw/streamStart?flvStr=' + str,
    method: 'get',
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取车辆经过的卡口信息,plateNo为车牌号
export function getPassByDeviceOfVec(startTime,endTime,plateNo) {
  let params = {
    startTime:startTime,
    plateNo:plateNo
  }
  if(endTime){
    params.endTime = endTime;
  }
  return request({
    url: "/video/device/queryOcrDevice",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取卡口摄像头列表
export function getCheckVideo(params){
  return request({
    url: '/video/deviceCheckPoint/list',
    method: 'get',
    params: params,
    headers: {
      'Content-type': 'application/json;charset=UTF-8'
    }
  })
}
