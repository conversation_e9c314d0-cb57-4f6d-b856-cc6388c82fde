import request from "@/utils/request";

// 获取列表
export function getViolationAlarmList(param) {
  return request({
    url: "/alarm/page",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取卡口列表
export function getRegpotAlarmList(param) {
  return request({
    url: "/alarm/pageRegpot",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取列表
export function getRealtimeAlarmList(param) {
  return request({
    url: "/alarm/realtime",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆线路
export function getRouteByVecPk(param) {
  return request({
    url: "/rteline/findRouteByVecPk",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆线路
export function getRouteByVecNo(param) {
  return request({
    url: "/rteline/findRouteByVecNoV2",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取禁行线路
export function getNoEntryRouteByRteLinePk(rteLinePk) {
  return request({
    url: "/rteline/info/" + rteLinePk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//
export function getUnfinishLast(param) {
  return request({
    url: "/rtePlan/getUnfinishLast",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/alarmlistdict",
    method: "get"
  });
}

// 卡口报警类型
export function getRegpotAlarmDict() {
  return request({
    url: "/alarm/regpotAlarmDict",
    method: "get"
  });
}

// 违章报警处理
export function dealSubmit(data) {
  return request({
    url: "/alarm/handle",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 获取报警信息
export function getAlarmListBySimpleQuery(data) {
  return request({
    url: "/alarm/list",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 违章报警导出
export function downloadExcel(data) {
  return request({
    url: "/alarm/download",
    method: "post",
    params: data,
    responseType: "blob"
  });
}

// 预警处置
export function feedbackSave(data) {
  return request({
    url: "/alarm/alarmHandle",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警车辆信息
export function getAlarmByVecNo(params) {
  return request({
    url: "/alarm/alarmTodayByVecNo",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警前后半小时轨迹
export function getRouteByHour(id) {
  return request({
    url: "/alarm/selectAlarm?id=" + id,
    method: "get"
  });
}

// 根据报警pk，获取报警信息
export function getAlarmByPk(pk) {
  return request({
    url: "/alarm/info/" + pk,
    method: "get"
  });
}

// 疲劳驾驶导出
export function exportTiredDrive(alarmPk) {
  return request({
    url: "/alarm/exportovertimebyday",
    method: "get",
    params: { id: alarmPk },
    responseType: "blob"
  });
}

// 超速导出
export function exportOverSpeed(alarmPk) {
  return request({
    url: "/alarm/overSpeed/export",
    method: "get",
    params: { id: alarmPk },
    responseType: "blob"
  });
}

// 抄送函
export function noticeSave(data) {
  return request({
    url: "/notice/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
//获取附近围栏列表
export function getNearbyFence(lng, lat, radius) {
  return request({
    url: "/entpLoc/nearby?lng=" + lng + "&lat=" + lat + "&radius=" + radius,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取预警信息
export function getViolationAlarmPerList(data) {
  return request({
    url: "/alarm/pagePre",
    method: "get",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 下发举证
export function downAlarmInfo(pk) {
  return request({
    url: "/alarm/downAlarmInfo/" + pk,
    method: "get",
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 预警信息 举证回函审核
export function apprDowningAlarmInfo(params) {
  return request({
    url: "/alarm/apprDowningAlarmInfo",
    method: "get",
    params: params
  });
}

// 预警信息 举证回函审核
export function getAlarmHanderDetail(params) {
  return request({
    url: "/alarm/getAlarmHanderDetail",
    method: "get",
    params: params
  });
}

// 查询其他预警信息
export function checkHisNoHandleAlarm(vecNo) {
  return request({
    url: "/offsite/checkHisNoHandleAlarm?vecNo=" + vecNo,
    method: "get"
  });
}

/*
 *偏离路线
 */
export function alarmRoute(pk) {
  return request({
    url: "/rteline/alarmRoute/" + pk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/*
 *夜间停车预警-报警详情接口
 */
export function alarmPreInfo(pk) {
  return request({
    url: "/alarmPre/info/" + pk,
    method: "get"
  });
}

/*
 *夜间停车预警-预警前后30分钟gps接口
 */
export function getSelectAlarmPre(params) {
  return request({
    url: "/alarmPre/selectAlarmPre",
    method: "get",
    params: params
  });
}

/*
 *夜间停车预警-偏离路线
 */
export function alarmPreRoute(pk) {
  return request({
    url: "/rteline/alarmPreRoute/" + pk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 夜间停车预警-预警处置
export function alarmPreFeedbackSave(data) {
  return request({
    url: "/alarmPre/alarmHandle",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 车辆关注列表接口
 * @param {*} filters vecState = 1
 * @returns
 */
export function vecFocusPage(param) {
  return request({
    url: "/vecFocus/page",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 加入关注列表
 * @param {*} alarmPk 报警表主键
 * @returns
 */
export function addToFollowList(alarmPk) {
  return request({
    url: "/vecFocus/joinFocus?alarmPk=" + alarmPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 移除关注列表
 * @param {*} alarmPk 报警表主键
 * @returns
 */
export function removeFocus(alarmPk) {
  return request({
    url: "/vecFocus/removeFocus?alarmPk=" + alarmPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

/**
 * 修改关注列表
 * @param {*} alarmPk 报警表主键
 * @param {*} content 修改内容
 * @returns
 */
export function updFocus(alarmPk, content) {
  return request({
    url: "/vecFocus/updFocus?alarmPk=" + alarmPk + "&content=" + content,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取列表
export function getEntpAlarmAddrPage(param) {
  return request({
    url: "/entpAlarmAddr/page",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取详情
// export function getEntpAlarmAddrInfo(id) {
//   return request({
//     url: "/entpAlarmAddr/info/" + id,
//     method: "get",
//     headers: {
//       "Content-type": "application/json;charset=UTF-8"
//     }
//   });
// }
// 删除
export function deleteEntpAlarmAddr(id) {
  return request({
    url: "/entpAlarmAddr/del",
    method: "get",
    params: {
      id
    },
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 新增
export function addEntpAlarmAddr(data) {
  return request({
    url: "/entpAlarmAddr/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 修改
export function updEntpAlarmAddr(data) {
  return request({
    url: "/entpAlarmAddr/update",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
