import request from "@/utils/request";

// 获取列表
export function getViolationAlarmList(param) {
  return request({
    url: "/alarm/pagePre",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取列表
export function getRealtimeAlarmList(param) {
  return request({
    url: "/alarm/realtimePre",
    method: "post",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆线路
export function getRouteByVecPk(param) {
  return request({
    url: "/rteline/findRouteByVecPk",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取车辆线路
export function getRouteByVecNo(param) {
  return request({
    url: "/rteline/findRouteByVecNoV2",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}
// 获取禁行线路
export function getNoEntryRouteByRteLinePk(rteLinePk) {
  return request({
    url: "/rteline/info/" + rteLinePk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

//
export function getUnfinishLast(param) {
  return request({
    url: "/rtePlan/getUnfinishLast",
    method: "get",
    params: param,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警类型
export function getAlarmType() {
  return request({
    url: "/alarm/prealarmlistdict",
    method: "get"
  });
}

// 违章报警处理
export function dealSubmit(data) {
  return request({
    url: "/alarm/handlePre",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 获取报警信息
export function getAlarmListBySimpleQuery(data) {
  return request({
    url: "/alarm/list",
    method: "post",
    params: data,
    headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=utf-8"
    }
  });
}

// 违章报警导出
export function downloadExcel(data) {
  return request({
    url: "/alarm/downloadPre",
    method: "post",
    params: data,
    responseType: "blob"
  });
}

// 预警处置
export function feedbackSave(data) {
  return request({
    url: "/alarm/alarmHandle",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警车辆信息
export function getAlarmByVecNo(params) {
  return request({
    url: "/alarm/alarmTodayByVecNo",
    method: "get",
    params: params,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 报警前后半小时轨迹
export function getRouteByHour(id) {
  return request({
    url: "/alarm/selectAlarmPre?id=" + id,
    method: "get"
  });
}

// 根据报警pk，获取报警信息
export function getAlarmByPk(pk) {
  return request({
    url: "/alarm/info/" + pk,
    method: "get"
  });
}

// 疲劳驾驶导出
export function exportTiredDrive(alarmPk) {
  return request({
    url: "/alarm/exportovertimebyday",
    method: "get",
    params: { id: alarmPk },
    responseType: "blob"
  });
}

// 超速导出
export function exportOverSpeed(alarmPk) {
  return request({
    url: "/alarm/overSpeed/export",
    method: "get",
    params: { id: alarmPk },
    responseType: "blob"
  });
}

// 抄送函
export function noticeSave(data) {
  return request({
    url: "/notice/save",
    method: "post",
    data: data,
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 违章报警导出
export function downloadPreWeight(data) {
  return request({
    url: "/alarm/downloadPreWeight",
    method: "post",
    params: data,
    responseType: "blob"
  });
}
