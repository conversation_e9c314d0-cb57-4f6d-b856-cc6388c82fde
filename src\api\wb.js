import request from "@/utils/request";

//查询old
// export function queryList(params){

//     return request({
//         url : '/argmwt/allListNoCount',
//         method : 'GET',
//         params : params
//     })

// }
// 登记点查验列表
export function queryList(params) {
  return request({
    url: "/zh/ck/record/page",
    method: "GET",
    params: params
  });
}

// 获取易制毒装卸单
export function allListNoCount(params) {
  return request({
    url: "argmwt/allListNoCount",
    method: "GET",
    params: params
  });
}

// 登记点卡口名称列表列表
export function getRegPots() {
         return request({
           url: "/zh/ck/record/getRegPots",
           method: "GET",
         });
       }
//易制毒货物下拉选项
export function goodsList() {
  return request({
    url: "/chemControl/list?type=yzd",
    method: "GET"
  });
}

//充装单详情
export function getArgmtWtDetail(pk) {
  return request({
    url: "/argmwt/info/" + pk,
    method: "get"
  });
}

export function getIpLicByPk(pk, licCatCd) {
  return request({
    url: "/pers/getIpLic?licCatCd=" + licCatCd + "&ipPk=" + pk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

export function getGoodsInfo(goodsPk) {
  return request({
    url: "/ench/info/" + goodsPk,
    method: "get",
    headers: {
      "Content-type": "application/json;charset=UTF-8"
    }
  });
}

// 获取查验配置项
export function checkConfig() {
  return request({
    url: "/zh/ck/config/page",
    method: "GET",
    params: {
      page: 1,
      limit: 999,
    },
  });
}

// 获取查验配置项结果描述
export function checkDescr() {
  return request({
    url: "/zh/ck/config/page",
    method: "GET",
    params: {
      page: 1,
      limit: 999,
    },
  });
}

// 电子运单
export function getDtlById(param) {
  return request({
    url: "/rtePlan/getDtlById",
    method: "get",
    params: param,
  });
}

// 通行证详情
export function getPassport(param) {
  return request({
    url: "/licppt/vecLic",
    method: "get",
    params: param,
  });
}

// 智能查验 车牌和人脸识别
export function getVecAndPersDetect(param) {
  return request({
    url: "/zh/ck/record/detect",
    method: "post",
    params: param,
    headers: {
      "Content-type": "multipart/form-data",
    },
  });
}


// 获取智能地磅登记信息
export function getCKList(params) {
  return request({
    url: "/zh/ck/record/digital/page",
    method: "get",
    params: params
  });
}
// 获取智能地磅登记信息
export function getCKInfo(id) {
  return request({
    url: `/zh/ck/record/digital/info/${id}`,
    method: "get"
  });
}

// 获取过磅记录详情
export function recordDtl(id) {
  return request({
    url: `/zh/ck/record/info/${id}`,
    method: "get"
  });
}

/** 获取白名单的货物 */
export const getWhiteGoods = () => {
  return request({
    url: `/zh/ck/record/white/goods`,
    method: "get"
  });
};

/** 获取地磅场景配置 */
export const getWbSceneConfig = regNumber => {
  return request({
    url: `/zh/ck/record/wb/scene?regNumber=` + regNumber,
    method: "get"
  });
};

/** 获取展示项 */
export const getCollectData = () => {
  return request({
    url: `/zh/ck/record/collect/data/show`,
    method: "get"
  });
};

/** 获取查验成功配置项 */
export const getSuccessCheckRes = () => {
  return request({
    url: `/zh/ck/record/check/descr`,
    method: "get"
  });
};
