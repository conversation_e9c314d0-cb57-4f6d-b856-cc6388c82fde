<template>
  <div class="map-comp-wrapper">
    <slot v-if='initComplete'></slot>
    <!-- <keep-alive>
          <component v-if='initComplete' :is="mapComp" v-on:handledisp="handleDisp" :vmap ="map"></component>
        </keep-alive> -->
    <div class="view-map" ref="viewMap" :style="{'height':mapHeight?mapHeight:mapConfig.mapHeight}"></div>
    <el-button-group class="tool-btns" :style="mapConfig.toolStyle">
      <el-button size="mini" v-if="mapConfig.isShowSatellite" :class="[satellite? 'active':'']" @click="satelliteMap">卫星地图</el-button>
      <el-button size="mini" v-if="mapConfig.isShowDistance" :class="[distance? 'active':'']" @click="distanceMap">测距工具</el-button>
      <el-button size="mini" v-if="mapConfig.isShowTraffic" :class="[traffic? 'active':'']" @click="routeMap">路况信息</el-button>
    </el-button-group>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
export default {
  name: "baseMap",
  data() {
    return {
      map: null,
      myDrawingManagerObject: null,
      bdary: null,
      driving: null,
      path: [],
      entpName: "",
      overlay: null,
      loading: true,
      tableLoading: false,
      tableHeight: 170,
      mapHeight: null,
      list: [],
      // comments:[],
      mapConfig: {
        type:"BMAP_SATELLITE_MAP",  // BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP，BMAP_HYBRID_MAP
        drawingModes: [],
        scrollWheelZoom: false, //是否开启滚动
        navigationControl: false, //缩放控件
        centerAndZoom: {
          point: [121.681275, 29.973008],
          zoom: 13,
          city: "宁波市"
        },
        mapHeight: '768px',
        mapDrawing: false,
        isShowSatellite: false, // 是否显示卫星地图
        isShowDistance: false, // 是否显示测距
        isShowTraffic: false, // 是否显示路况
        toolStyle:{}        // 工具栏样式
      },
      satellite: false, //卫星地图
      distance: false, //测距
      distanceControl: null,
      traffic: false, //路况
      trafficControl: null,
      initComplete: false, //子组件加载锁
      geoc: null // 经纬度解析工具
    };
  },
  watch: {
    "mapConfig.mapHeight": {
      handler(newMapHeightFun) {
        if (
          Object.prototype.toString.call(newMapHeightFun) ===
          "[object Function]"
        ) {
          this.mapHeight = newMapHeightFun();
          window.removeEventListener("resize", this.resizeHeight);
          window.addEventListener("resize", this.resizeHeight);
        } else if (
          Object.prototype.toString.call(newMapHeightFun) === "[object Number]"
        ) {
          this.mapHeight = newMapHeightFun;
        }
      },
      immediate: true
    }
  },
  props: {
    param: {
      // type:'Object',
      required: false
    }
  },
  created() {
    let param = this.$props.param;
    this.mapConfig = Object.assign({}, this.mapConfig, param);
  },
  destroyed() {
    window.removeEventListener("resize", this.resizeHeight);
  },
  mounted() {
    this.$nextTick(() => {
      this.loadMap();
      this.loading = false;
    });
  },
  methods: {
    resizeHeight() {
      if (
        Object.prototype.toString.call(this.mapConfig.mapHeight) ===
        "[object Function]"
      ) {
        this.mapHeight = this.mapConfig.mapHeight();
      }
    },
    //处理地图的子组件分发的数据
    handleDisp(payload) {
      let data = payload.data;
      let handleFn = payload.handleFn;
      let lastHandleFn = payload.lastHandleFn;
      let getBoundary = this.getBoundary;

      Object.prototype.toString.call(handleFn) == "[object Function]" &&
        handleFn.call(this, data);
      if (lastHandleFn) {
        this.$emit("handledisp", data);
      }
    },
    //初始化地图及地图相关控价
    loadMap() {
      let _this = this;
      let map = new BMap.Map(this.$refs.viewMap);

      this.map = map;

      //初始化地图设置
      this.initMapSetting(map);

      //加载地图控件
      this.addMapControl(map);

      //获取区域围栏
      this.getBoundary();

      //绘制覆盖物插件
      this.addDrawingManager(map);

      //路线导航服务
      this.drivingRoute(map);

      //区域搜索
      this.localSearch(map);

      this.initComplete = true; //子组件加载锁
      //保存地图到 store管理器
      this.commitMapComp("SET_MAP", map);
    },
    //保存地图到 store管理器
    commitMapComp(name, comp) {
      this.$store.commit(name, comp);
    },
    //初始化地图设置
    initMapSetting(map) {
      let mapConfig = this.mapConfig;
      let centerZoomPoint = mapConfig.centerAndZoom.point;
      let zoom = mapConfig.centerAndZoom.zoom;
      let city = mapConfig.centerAndZoom.city;
      // 初始化地图,设置中心点坐标和地图级别
      map.centerAndZoom(
        new BMap.Point(centerZoomPoint[0], centerZoomPoint[1]),
        zoom
      );
      // 设置地图显示的城市 此项是必须设置的
      map.setCurrentCity(city);
      //开启鼠标滚轮缩放
      if (mapConfig.scrollWheelZoom) {
        map.enableScrollWheelZoom();
      }
      let type = mapConfig.type;
      if(type && (type===BMAP_NORMAL_MAP || type===BMAP_SATELLITE_MAP || type===BMAP_HYBRID_MAP)){  // BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP，BMAP_HYBRID_MAP
        map.setMapType(type);
      }else{
        map.setMapType(BMAP_NORMAL_MAP);
      }
      this.$emit("ready",map);
    },
    //统一加载地图控件
    addMapControl(map) {
      let _this = this;
      let mapConfig = this.mapConfig;
      if (!this.mapConfig.isShowSatellite) {
        //添加地图类型控件
        map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP, BMAP_HYBRID_MAP]
          })
        );
      }

      if (mapConfig.navigationControl) {
        // 添加带有定位的导航控件
        let navigationControl = new BMap.NavigationControl({
          // 靠左上角位置
          anchor: BMAP_ANCHOR_TOP_RIGHT,
          // LARGE类型
          type: BMAP_NAVIGATION_CONTROL_LARGE,
          offset: new BMap.Size(0, 40)
        });
        map.addControl(navigationControl);
      }

      if (this.mapConfig.isShowDistance) {
        this.distanceControl = new BMapLib.DistanceTool(this.map, {
          lineStroke: 2
        });
        this.distanceControl.addEventListener("drawend", function(e) {
          _this.distanceControl.close();
          _this.distance = false;
        });
      }

      if (this.mapConfig.isShowTraffic) {
        //添加路况控件
        this.trafficControl = new BMapLib.TrafficControl({
          showPanel: true //是否显示路况提示面板
        });
        this.map.addControl(this.trafficControl);
        this.trafficControl.setAnchor(BMAP_ANCHOR_BOTTOM_RIGHT);
      }
    },
    //区域搜索
    localSearch(map) {
      //区域搜索
      var local = new BMap.LocalSearch(map);
      this.local = local;
    },
    //路线导航
    drivingRoute(map) {
      //路线导航
      var driving = new BMap.DrivingRoute(map, {
        renderOptions: {
          map: map,
          autoViewport: false,
          enableDragging: true
        }
      });
      this.driving = driving;
      this.commitMapComp("SET_MAP_DRIVING", driving);
    },
    // 卫星地图
    satelliteMap() {
      this.satellite = !this.satellite;
      if (this.satellite) {
        this.map.setMapType(BMAP_SATELLITE_MAP);
      } else {
        this.map.setMapType(BMAP_NORMAL_MAP);
      }
    },
    //测距
    distanceMap() {
      this.distance = !this.distance;
      if (this.distance) {
        this.distanceControl.open();
      }
    },
    //路况
    routeMap() {
      this.traffic = !this.traffic;
      if (this.traffic) {
        this.trafficControl.showTraffic();
      } else {
        this.trafficControl.hideTraffic();
      }
    },
    //加载地多边形绘制工具
    addDrawingManager(map) {
      //地图配置参数
      let mapConfig = this.mapConfig;
      let polyOption = {
        strokeWeight: 2,
        strokeColor: "#078aff"
      };
      if (mapConfig.mapDrawing) {
        let myDrawingManagerObject = new BMapLib.DrawingManager(map, {
          isOpen: false,
          enableDrawingTool: true,
          drawingToolOptions: {
            anchor: BMAP_ANCHOR_TOP_RIGHT,
            offset: new BMap.Size(10, 40),
            enableCalculate: true,
            drawingModes: mapConfig.drawingModes
          },
          polygonOptions: polyOption,
          circleOptions: polyOption,
          rectangleOptions: polyOption
        });
        this.myDrawingManagerObject = myDrawingManagerObject;
        this.commitMapComp("SET_MAP_DRAWING", myDrawingManagerObject);
      }
    },
    //获取行政区域
    getBoundary(callback) {
      let _this = this;
      var bdary = new BMap.Boundary();
      var map = this.map;

      this.bdary = bdary;
      this.commitMapComp("SET_MAP_BDARY", bdary);
      
      // bdary.get("宁波市镇海区", function(rs) {
      //   //获取行政区域
      //   map.clearOverlays(); //清除地图覆盖物
      //   var count = rs.boundaries.length; //行政区域的点有多少个
      //   if (count === 0) {
      //     this.$message({
      //       type: "error",
      //       message: "未能获取当前输入行政区域"
      //     });
      //     return;
      //   }
      // TODO 客服反映镇海区围栏不出现，改为静态围栏数据
        var boundaries = '121.47439624709929, 30.03867325173173;121.4740015316582, 30.03541773591859;121.47426473517262, 30.034423707315817;121.47463645399266, 30.033875685824484;121.47622879033965, 30.033324534607658;121.47841391849266, 30.032252004693913;121.48042782961548, 30.030581082752153;121.4808081721684, 30.029392988039003;121.48073513993045, 30.02749313224276;121.48079999758826, 30.0265918135309;121.48110191807018, 30.02552710548061;121.48249285432182, 30.024948624898354;121.48332630217398, 30.02449052839649;121.48467924010255, 30.022875525737682;121.48586491354517, 30.020851946731764;121.48697701576653, 30.020343485166876;121.48761822623955, 30.019841118801736;121.488145711235, 30.019117584761787;121.48888834040011, 30.017556828237623;121.48927568973602, 30.01627475967318;121.48977074590256, 30.015397183118502;121.49050187675714, 30.014884549692376;121.49131852629624, 30.014681121746847;121.49201040120003, 30.01424776096337;121.49230872845989, 30.01248443272157;121.4922458470742, 30.011734105606973;121.4920392368069, 30.010779554886312;121.49175869599613, 30.010181128398692;121.49165880442342, 30.00961326879728;121.49207759445217, 30.009031566998818;121.49260346249773, 30.008248609702825;121.49354201209455, 30.005907441019584;121.49433270060443, 30.004313791220206;121.49528059257855, 30.003673180813724;121.49734696474317, 30.003030846033234;121.49925415717139, 30.002467480178577;121.49911258422301, 30.002094429338385;121.49826467365214, 30.001851800877954;121.49789600907086, 30.00189887225358;121.49756444450712, 30.002052753270984;121.4972020680644, 30.002458019045037;121.49674734581525, 30.002736301096288;121.49572094193954, 30.002889398893412;121.49495450767844, 30.00244433558582;121.49366867317146, 30.001217664366497;121.49280162869323, 30.00078017816018;121.49183253670905, 30.000857666922435;121.49093252441861, 30.00085493018617;121.49009943588865, 30.00065264606823;121.48919385410404, 30.000330882880966;121.48856917245242, 30.000072298676052;121.48796468545524, 29.999872254552397;121.48778071248685, 29.999610072197676;121.48823139236107, 29.998515748491617;121.48887925029466, 29.997831623821256;121.49073622744427, 29.995948047367072;121.49212698403434, 29.99483898178704;121.49354963047004, 29.993946981512615;121.49445188852395, 29.99351939404433;121.4950171921812, 29.993272130654184;121.49600200451147, 29.993603926388896;121.49831864459038, 29.994157177449853;121.50669696041808, 29.994224740334186;121.50842431208264, 29.993927353818442;121.51007315184567, 29.99257944577588;121.51276007345567, 29.991433118993022;121.51689281778351, 29.991275076711606;121.51808783360303, 29.991445552788637;121.51849943718757, 29.991098969921495;121.5195590783668, 29.989230044536562;121.52017109391053, 29.9890224969249;121.52138111143209, 29.98891919216658;121.5243248585873, 29.98930167729551;121.5258790169496, 29.98967430756293;121.5268106495937, 29.990191764711742;121.52878826917325, 29.991705019938298;121.52954455258187, 29.99244463049498;121.5307734345191, 29.9929949947298;121.53205621478693, 29.99377283446363;121.53351389513749, 29.994369327878488;121.534544071896, 29.994323895038658;121.535546940167, 29.99388004402154;121.53650992367338, 29.993411402108904;121.53777428867825, 29.99254542917663;121.53840678558761, 29.9919014575417;121.53947478100808, 29.99061052998808;121.54023887967462, 29.990096906895207;121.54111661398812, 29.989816242959545;121.54200243305121, 29.989608618384363;121.54290424195239, 29.989394033428418;121.54395229499053, 29.98895868415741;121.54433245788223, 29.988571349154007;121.54630208254275, 29.98688490306701;121.54881617017271, 29.98479684486666;121.55034697259175, 29.98350238291986;121.55126360353383, 29.982847168460214;121.55160037826941, 29.982479673979324;121.55333545536136, 29.980862728868345;121.55404026586426, 29.980296029260558;121.55485781370861, 29.979577209201704;121.55520590709361, 29.979299878243012;121.55562775136097, 29.97871127080188;121.55602165832696, 29.97817818932391;121.55763393705574, 29.97678204337049;121.55790136260596, 29.976550302028095;121.55816187120378, 29.97632434782752;121.5586229714219, 29.97592397979832;121.55959933968036, 29.974547980900642;121.5597511533115, 29.97407197293452;121.55975420755023, 29.973699987451838;121.55982113131071, 29.973241417204893;121.55992210084999, 29.972714093974158;121.56007894499199, 29.972293140650194;121.56036370783855, 29.971556898184584;121.56061433507575, 29.971429328150084;121.5610167759441, 29.971513566627962;121.56169490677335, 29.97164598592784;121.56230027485633, 29.971848564429074;121.56267082587907, 29.971992011655985;121.56314935322409, 29.972324661372127;121.56392270443737, 29.972891797995192;121.56402466211271, 29.972872087881928;121.56422758932734, 29.972642527343307;121.56443051654199, 29.972415938442918;121.56492745914994, 29.971910823992733;121.56551036959517, 29.971344542088353;121.5657254239342, 29.971075791690087;121.56599850880914, 29.970718968868237;121.56611259360886, 29.97052499186779;121.56635360897711, 29.970415019253252;121.56640660900219, 29.970330075883613;121.566603697231, 29.97009104516063;121.56708294322041, 29.969545871475805;121.56725407042002, 29.969353769382813;121.56761923160971, 29.96913436893574;121.56851681847498, 29.96844792786998;121.5688533237189, 29.96769030287357;121.568740676208, 29.966879639229536;121.5683852167178, 29.965544579601914;121.56846444726376, 29.96516747751171;121.56891575595183, 29.965078462121685;121.56947522062325, 29.9648459109907;121.56986858860593, 29.9647826300799;121.57048904822148, 29.964941340717413;121.57130587742142, 29.96514362014355;121.57170724032316, 29.96529662008287;121.57246316440954, 29.9655073466036;121.57296585617277, 29.965722218355754;121.57334026990921, 29.96601765647112;121.5735616123868, 29.966121141732994;121.57481699433657, 29.96600967801103;121.575933408424, 29.965878033326216;121.57612591529472, 29.965765317802493;121.57627889672304, 29.964729048559928;121.57632722555944, 29.96430461931375;121.57646870867723, 29.964094359547104;121.57867270124522, 29.963563232614508;121.57974806277086, 29.963268569352078;121.58031121049488, 29.962554709326707;121.58110540239599, 29.961635585405297;121.58145448391704, 29.960338158930835;121.58172963489467, 29.958757772437586;121.58223645886324, 29.958554775847293;121.5834949848823, 29.958485623926695;121.58474470750737, 29.958442677743395;121.5853781925528, 29.95609922708293;121.58540397392092, 29.955259992672914;121.58518173313782, 29.953538945210536;121.58524111113202, 29.953406032156938;121.58535663322056, 29.95326122791369;121.58548437226403, 29.953064243733454;121.58564112657547, 29.95290473163223;121.58582698598542, 29.95273684858243;121.5862178387127, 29.95243855427449;121.58655353548166, 29.952112095817213;121.58682284747347, 29.951867701094493;121.58700780857791, 29.951690663181076;121.58727514429759, 29.951467233526063;121.58759368343131, 29.951189901543653;121.58783999880208, 29.950962715608274;121.58813347521209, 29.95064681363245;121.58843907874648, 29.95038058837181;121.58853052624737, 29.95029398481856;121.58868332801455, 29.95015934626153;121.58966948780308, 29.949290176014312;121.59025662028422, 29.948743791797213;121.59074916119518, 29.94832304884851;121.59090402906504, 29.948153280358913;121.5909684375701, 29.948018091232463;121.5910579986294, 29.947938526711877;121.59112833595081, 29.947876251978343;121.59121978345169, 29.947812568981455;121.5913454563925, 29.947727293159424;121.59169031587767, 29.947405043766807;121.59235901449907, 29.946800833092016;121.59342036245876, 29.94631412903485;121.59437355443508, 29.946487108360518;121.59580302799264, 29.945707251629393;121.59623421463729, 29.945542094580798;121.59648053000804, 29.94530949728221;121.59681326236883, 29.945148173168803;121.59778729503299, 29.9446807855206;121.59863475645082, 29.94417388569546;121.5996821806751, 29.943588354079946;121.60034063861369, 29.943150141943676;121.60066627436095, 29.9429701152893;121.60142498319443, 29.942663185123532;121.60178089183736, 29.94243167636908;121.60190234274228, 29.94251648734467;121.60194644954281, 29.942616633052044;121.60198534617138, 29.942826860467367;121.6020012461789, 29.94305555170869;121.60210464114306, 29.944083364800726;121.6020858665579, 29.944792508113032;121.60200726482582, 29.94503285208382;121.60195489361459, 29.94518275382061;121.60193665801275, 29.94530910609894;121.60192551902443, 29.945417229096286;121.60196243938087, 29.945653503250334;121.6020437360295, 29.945750985732765;121.60225070561893, 29.94570326157606;121.60276408721772, 29.94557698804001;121.60324333320713, 29.945463466897547;121.60371144020822, 29.94535612631715;121.60470802034067, 29.945088791432955;121.60642773640839, 29.944510776038342;121.60720279440214, 29.94353241387834;121.60754379117363, 29.942931074263463;121.60779747264957, 29.942224264717883;121.60776127093752, 29.9412220949111;121.6073280181902, 29.939730361638166;121.60720629779364, 29.938973532691982;121.60723405543389, 29.938238214556876;121.6084932102668, 29.936862705794645;121.61016342970095, 29.93560704825894;121.61040767896905, 29.935219659655182;121.6102414026192, 29.934852065505403;121.6094616734368, 29.93359473917172;121.60882603245815, 29.932670493536197;121.6087848900658, 29.931331448728894;121.60886699518937, 29.930222358742583;121.6094761361555, 29.923044192976946;121.6096071090988, 29.921376422343133;121.6113915929938, 29.920365197443775;121.61317248366674, 29.91951916313036;121.61480524376113, 29.9191691183198;121.61838750647263, 29.91955594397948;121.61979595968127, 29.920227622475945;121.62044678202302, 29.920695517444198;121.62092342292647, 29.921131716572873;121.62104747891736, 29.92128095038481;121.62123576375218, 29.92140498565696;121.62185128268742, 29.92193680957149;121.62248782197159, 29.92239201913865;121.62317924772238, 29.922929393791414;121.62365148692882, 29.923291789401716;121.62423772110444, 29.923674216664608;121.62515435204652, 29.923336550762297;121.62683400368853, 29.922133621254886;121.62749228196601, 29.920968005177887;121.62805138731524, 29.919871789174106;121.62892202501523, 29.919534423271553;121.63121405152317, 29.919668321127062;121.63533233313231, 29.920317147960333;121.63680797959306, 29.920648563709225;121.63812669208127, 29.92044721018875;121.63956380123567, 29.91969273731489;121.64044521860176, 29.919203629835234;121.64040452536217, 29.917942190878623;121.64039760840976, 29.91756608032664;121.64074543230309, 29.9175655325158;121.64272179425494, 29.917645512866127;121.64398822536249, 29.91770021563197;121.64448831203973, 29.917750848923216;121.64492668512847, 29.917881697122652;121.64516374795245, 29.91794649509012;121.64649836044819, 29.918088142672595;121.64670748597085, 29.918073586632065;121.64724826588768, 29.91822971179334;121.64739864223, 29.918310239411426;121.64750814567162, 29.918403131732685;121.64761971521592, 29.91845603414442;121.64771430678611, 29.918493597969828;121.64872391234843, 29.918924172296062;121.64942575844314, 29.91922859393978;121.64973046367203, 29.918872052712143;121.65000732143012, 29.918487102559375;121.65016668082754, 29.918249980655787;121.65028714359637, 29.91803015327256;121.65051513353474, 29.91760333145658;121.65088757099906, 29.917004181322696;121.65110154737145, 29.916645127746968;121.65133753222885, 29.916196465457613;121.65166604255373, 29.91565553238402;121.65193077318744, 29.915197474322216;121.65202338848549, 29.91510019638362;121.65232396150903, 29.91501379658935;121.65248915989226, 29.914486865770037;121.65259075824541, 29.914342786913657;121.65246032428539, 29.91398536713592;121.65230392929617, 29.913628415633177;121.65224221570764, 29.913438239233546;121.65218948517423, 29.913312237383998;121.65212875972178, 29.913254088650053;121.65179027820572, 29.91302180642047;121.65155725775651, 29.912865438223186;121.65138002207945, 29.91266320791455;121.6512317118398, 29.912500421606246;121.65108331176961, 29.912280659663647;121.65090706422862, 29.91208946324457;121.65070494548894, 29.911872752651178;121.65060738951057, 29.91183096008758;121.65035047413481, 29.911700260382975;121.6501444028509, 29.911527454759046;121.65006571128825, 29.911441051833496;121.64999303837251, 29.911265741317088;121.64994713496095, 29.911033923985944;121.64986143661534, 29.910813297869815;121.64975588571795, 29.910704432513185;121.64944713811427, 29.910313893688294;121.64920118206572, 29.909986434691678;121.64896313110566, 29.909687228257052;121.64839432405692, 29.908901132005596;121.64830961384737, 29.90871063342418;121.64819004938403, 29.908561537112536;121.64779937631786, 29.907865751343927;121.64760911521091, 29.90751644781843;121.6472962354019, 29.907174108813514;121.64654758759012, 29.907795546365453;121.64609448229105, 29.907950122478244;121.64524450561778, 29.906987285448782;121.64424379327998, 29.9059125941919;121.64295490453472, 29.90480220064567;121.64480209015426, 29.901622634977386;121.64585643133098, 29.900879289195398;121.64711908955537, 29.90060017107408;121.6492221125841, 29.900736755895622;121.6496024551369, 29.900738477880708;121.65223763434955, 29.902288095723087;121.65341504338109, 29.903596462050118;121.65678306022853, 29.909782554915218;121.6582260981993, 29.913283358676537;121.660825255361, 29.916209691359303;121.66328598364368, 29.91851684057963;121.66490176576393, 29.919659399826163;121.6661523866945, 29.920330686335074;121.66825469107881, 29.921456790878977;121.67098149745345, 29.922366038521968;121.67620792873933, 29.923312213731354;121.68315344727908, 29.923862025703972;121.68932193155297, 29.926645861466287;121.69508528004198, 29.932277381351785;121.69844664942865, 29.940988392046084;121.70128996602789, 29.94528931222332;121.7028813142383, 29.947284640044987;121.70376425872377, 29.948580282757817;121.70595926823667, 29.949977533066008;121.70809400144988, 29.950980552539523;121.7088069865329, 29.951035158563343;121.7111249740701, 29.951212510583595;121.71358444472504, 29.950974841592586;121.72011395798326, 29.950723246670044;121.72329647474297, 29.95048706291596;121.7260086387378, 29.95089191547648;121.72803000579567, 29.951598897491284;121.73014066442123, 29.95276774972447;121.73156115492368, 29.954211565433173;121.73440986135599, 29.958910156500718;121.73562472972729, 29.962147323374854;121.73605097569163, 29.963410777535287;121.73711852195933, 29.967231157071932;121.74066278634778, 29.970773407506066;121.74574845316027, 29.97416629871848;121.75203883749182, 29.97692775153528;121.75915189000914, 29.978892561643583;121.76466434176929, 29.97996457964591;121.76892275903795, 29.979700779991717;121.77237315050063, 29.979492899421665;121.77441293282146, 29.980953606972406;121.77642217275496, 29.983190339337753;121.7785234890032, 29.985983915304196;121.78132350727688, 29.98799304178046;121.78966229766216, 29.993265249184713;121.8015505799309, 30.001131652933775;121.79529504985108, 30.00460293635283;121.78847250933335, 30.0057753024599;121.78108466515819, 30.006259601141206;121.77073277212232, 30.00680934058322;121.75391891840039, 30.00715399147598;121.73616929982873, 30.008053612177502;121.72784057046242, 30.009233677490098;121.70710767929214, 30.024913212395195;121.71527219841123, 30.028832353049165;121.72855723859828, 30.035213647042855;121.73228736239791, 30.037020494960572;121.73794596846636, 30.039786589897698;121.74281155043062, 30.042174706097722;121.7494140060984, 30.045470092164045;121.74985354698444, 30.04569143234403;121.75674840109572, 30.049187009062777;121.75645779926323, 30.049680159366126;121.74969535538413, 30.04637178453624;121.73867709932094, 30.040890614318034;121.7319381910462, 30.03756505970247;121.72832538594616, 30.03581520384327;121.70652952986592, 30.025348871286266;121.70326050628455, 30.028702355939707;121.69105972084802, 30.042963098387613;121.6702506533705, 30.06687954902302;121.64555362981521, 30.094460990104245;121.63151365367757, 30.07546010225861;121.63115478062633, 30.07577927511964;121.63100691953939, 30.07581060635299;121.63083112115109, 30.075780915907668;121.62933912552957, 30.074910903731013;121.62900639316868, 30.071714724405293;121.62836060133755, 30.07076763329592;121.62549931862718, 30.067240554523117;121.62401388063586, 30.06506379621184;121.6224988883933, 30.061844633902194;121.62045641115525, 30.057684009251414;121.61963940229393, 30.05619959203966;121.61837530678028, 30.053690449934326;121.61727173846128, 30.052766942556072;121.61394127078303, 30.049989959651068;121.61221850047599, 30.049237418359734;121.61122901695677, 30.049654603143516;121.61040095893767, 30.048466348667468;121.61007056217109, 30.047185309180232;121.60971249759481, 30.04672958302549;121.60824825961355, 30.046230868783393;121.60649153335824, 30.04590315876478;121.60482050544857, 30.04595833729915;121.6042739763763, 30.046378818595144;121.60339767935132, 30.047168114954566;121.60197224816808, 30.047461823177038;121.5999247404192, 30.047610708994252;121.59568222315669, 30.04717108486649;121.59481437020348, 30.04690457399121;121.59340106614464, 30.046259317681592;121.59163733310633, 30.045634221582667;121.5887475741113, 30.045433123928508;121.58587470325983, 30.045333630066835;121.58329081729093, 30.045546451456314;121.58128247566228, 30.046312542109078;121.57993573604172, 30.04714271438841;121.57914810177056, 30.04773911794532;121.57848694891523, 30.048239700229978;121.57748785352703, 30.048886350283574;121.57559808822569, 30.049373484255163;121.57199130177257, 30.049977142504304;121.56901602409299, 30.050445123581675;121.56839044413583, 30.050799468489263;121.5666555467044, 30.053926776395496;121.56516094599691, 30.056109565066148;121.56423928454367, 30.057230754471668;121.56298408225457, 30.05840991585892;121.56145211203786, 30.059009376719835;121.55963726541606, 30.059490755750254;121.5579002120514, 30.059747776800076;121.55636716386809, 30.06016593274423;121.55523556841716, 30.060593386144877;121.55437526123026, 30.06118384704471;121.55396248984842, 30.062145878087684;121.55272022315867, 30.063128607545117;121.5506294170842, 30.062851668879784;121.54331927633565, 30.061208306240292;121.54183222139443, 30.060905339991006;121.5406278631972, 30.060788982572827;121.53887985050534, 30.060898932131412;121.53721726666747, 30.06131106605281;121.53654210024617, 30.06168701757414;121.53566238966025, 30.062594890399982;121.53474539939566, 30.062910276168267;121.53425905679255, 30.062557069045443;121.53316680712297, 30.06186151297832;121.53174478950069, 30.06145297600458;121.53140576900122, 30.061995607757602;121.53129752318726, 30.062737423638016;121.53109863834736, 30.064545327244137;121.53046605160723, 30.065909437826445;121.52923573238077, 30.06666403904499;121.52811635388478, 30.066874313652086;121.52708303305664, 30.066587774341677;121.5257093442708, 30.065927175749273;121.52426675545232, 30.065116697864855;121.52283692257208, 30.06517733547041;121.51984736183489, 30.065535299745775;121.51861497650575, 30.065496776257437;121.51773508625872, 30.064822339264488;121.51615379906951, 30.063915971777213;121.51436051177994, 30.063974890997894;121.51310692644076, 30.0626654537559;121.51206776662681, 30.06138061445793;121.51078938805558, 30.0602378263168;121.50978786724251, 30.05966228553147;121.50882793797453, 30.059192785535068;121.50784833581591, 30.059165043703917;121.5060886451524, 30.05904047887799;121.50430209515417, 30.05898898057559;121.50317328445033, 30.058979212305776;121.50217769245363, 30.0597108920937;121.50123743607632, 30.05977043900686;121.49958006241042, 30.05864669982049;121.49836941607461, 30.056995061157874;121.49719371382317, 30.05500852536489;121.49724024604858, 30.05452016899658;121.49896642991655, 30.052343518899608;121.49868265520595, 30.051419842528603;121.49784282938467, 30.05021582251125;121.49539171297015, 30.049274228853232;121.49381545629178, 30.04915488777631;121.49002388839527, 30.049574808289385;121.48671210547164, 30.049795045055212;121.48558563036212, 30.049544875433124;121.48481964525376, 30.04918802506436;121.48563979818438, 30.04420175327892;121.48539635739117, 30.04279099227402;121.48472271808923, 30.04196461298156;121.48378991764767, 30.041282979649935;121.48231858305289, 30.040654412757778;121.48071438907317, 30.040513801433114;121.47845586935998, 30.04076618259412;121.47710652465344, 30.040886706288905;121.47617417336464, 30.04083840303601;121.47561354089585, 30.040804090516676;121.47476563032498, 30.04040711177212;121.47439624709929, 30.03867325173173';
        var pointArray = [];
        // for (var i = 0; i < count; i++) {
          // rs.boundaries[i]
          var ply = new BMap.Polygon(boundaries, {
            strokeWeight: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.strokeWeight || 2
              : 2,
            strokeColor: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.strokeColor || "#FF6919"
              : "#FF6919",
            strokeOpacity: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.strokeOpacity || "0.8"
              : 0.8,
            strokeStyle: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.strokeStyle || "dashed"
              : "dashed",
            enableMassClear: false,
            fillColor: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.fillColor || ""
              : "",
            fillOpacity: _this.mapConfig.boundary
              ? _this.mapConfig.boundary.fillOpacity || ""
              : ""
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        // }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      // });
    },
    //显示多边形
    showOverlay(lnglat) {
      if (lnglat) {
        let lnglatArr = lnglat
          .split(",")
          .join(";")
          .split(";");
        let polygon;
        let pointArr = [];

        this.path.length = 0;

        for (var i = 0, len = lnglatArr.length; i < len; i += 2) {
          pointArr.push(new BMap.Point(lnglatArr[i], lnglatArr[i + 1]));
        }

        polygon = new BMap.Polygon(pointArr, {
          strokeWeight: 2,
          strokeColor: "#e12828"
        });

        if (this.overlay) {
          this.map.removeOverlay(this.overlay);
        }

        this.map.addOverlay(polygon); //添加覆盖物
        this.map.setViewport(pointArr); //调整地图视口
        this.overlay = polygon;
      } else {
        this.$message({
          message: "无围栏坐标数据",
          type: "error"
        });
      }
    },
    //清除地图覆盖物
    clear(overlay) {
      this.map.removeOverlay(overlay);
    },
    //获取多边形中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    //获取地图缩放级别
    getCurrentMapZoom() {
      return this.map.getZoom();
    },
    // 设置zoom
    setCenter(pt, zoom) {
      if (zoom) this.map.setZoom(zoom);
      this.map.panTo(pt);
    },
    // 设置zoom
    setZoom(val) {
      this.map.setZoom(val);
    },
    setViewport(pointArray) {
      if (!pointArray) return;
      this.map.setViewport(pointArray);
    },
    addOverlay(overlays, name) {
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach(overlay => {
          _this.map.addOverlay(overlay);
          if (name) {
            overlay.name = name;
          }
        });
      } else {
        this.map.addOverlay(overlays);
        if (name) {
          overlays.name = name;
        }
      }
    },
    removeOverlay(overlays) {
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach(overlay => {
          _this.map.removeOverlay(overlay);
        });
      } else {
        this.map.removeOverlay(overlays);
      }
    },
    removeOverlayByName(name) {
      var allOverlay = this.map.getOverlays();
      allOverlay.map(item => {
        if (item.name === name) {
          this.map.removeOverlay(item);
        }
      });
    },
    geocoder(point, cb) {
      if (!this.geoc) {
        this.geoc = new BMap.Geocoder();
      }
      this.geoc.getLocation(point, function(rs) {
        let addComp = rs.addressComponents;
        let str = "";
        if (addComp.district) {
          str += addComp.district;
        }
        if (addComp.street) {
          str += addComp.street;
        }
        if (addComp.streetNumber) {
          str += addComp.streetNumber;
        }
        cb(str);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.map-comp-wrapper {
  position: relative;
  background: #fff;
}
/* 地图样式重置 */
.anchorBL {
  display: none;
}
.tangram-suggestion-main {
  z-index: 10;
}

.tool-btns {
  position: absolute;
  z-index: 3;
  right: 10px;
  bottom: 5px;
  color: #333;
  font-size: 14px;
  cursor: pointer;

  /deep/ {
    .el-button {
      color: #333;
      background-color: #fff;
      &.active {
        color: #fff;
        background-color: #409eff;
      }
    }
  }
}
</style>
