<template>
  <div class="lic-panel" ref="licwape" v-loading="loading">
    <div class="lic-panel-header" @click="headerClickHandle" :class="licStatus">
      <div class="lic-panel-title"><strong>{{tpltData.licNm}}</strong> <i class="el-icon-arrow-down
"></i>
      </div>
    </div>
    <!-- 证件过期提示 -->
    <div v-if="isExpire && operType=='read'" class="lic-notice-expired"></div>
    <!-- 证照的图片数据展示 -->
    <collapse-transition>
      <div class="lic-panel-body clearfix" v-show="collapse">
        <div class="" style="padding-right: 8px;flex: 1 1 auto;">
          <div class="lic-upload-msg" v-html="tpltData.title"></div>
          <div class="clearfix">
            <template v-for="(jitem,jkey,jindex) in tpltData.list">
              <upload-img-item :key="jitem.rsrcCd" :title="jitem.licNm" :exampleUrl="jitem.exampleUrl" :oper-type="operType" :data-source="licImgItemsData[jindex]" :isAllowPdf="jitem.isAllowPdf || false"
                @preview="previewHandle" @openPdf="openPdf" @del="delHandle(jitem.rsrcCd)" @cropperHandle="cropperHandle">
              </upload-img-item>
            </template>
          </div>
        </div>
        <div v-if="tpltData.header.length>0 || tpltData.hasFormSlot" style="flex-grow:0;flex-shrink:0;padding-left: 8px;padding-right:8px;border-left:1px dashed #ccc;flex:0 0 380px;">
          <el-form v-if="operType=='edit' && (tpltData.header.length>0 || tpltData.hasFormSlot)" :model="dataSource" ref="licForm" :label-width="tpltData.headerLabelWidth" style="padding:0 10px 0 0;">
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="24" v-for="(iitem,iindex) in tpltData.header" :key="iitem.field+iindex">
                <el-form-item v-if="!iitem.readonly" :prop="iitem.field" :label="iitem.name+':'" :rules="$rulesFilter({required:iitem.required,type:(iitem.validateType || '')})">
                  <template v-if="iitem.type=='input'">
                    <el-input v-model="dataSource[iitem.field]" size="small" :placeholder="'请输入'+iitem.name" @change="submitModifyHandle" clearable></el-input>
                  </template>
                  <template v-else-if="iitem.type=='number'">
                    <el-input v-model="dataSource[iitem.field]" size="small" :placeholder="'请输入'+iitem.name" @change="submitModifyHandle" type="number" clearable></el-input>
                  </template>
                  <template v-else-if="iitem.type=='date'">
                    <el-date-picker type="date" align="right" :placeholder="'请选择'+iitem.name" size="small" v-model="dataSource[iitem.field]" :picker-options="datePickerOptions"
                      @input="submitModifyHandle" value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </template>
                  <template v-else-if="iitem.type=='select'">
                    <el-select v-model="dataSource[iitem.field]" :placeholder='"请选择"+iitem.name' size="small" @change="submitModifyHandle" clearable>
                      <el-option v-for="(ist,istindex) in iitem.options" :key="ist.value+istindex" :label="ist.label" :value="ist.value">
                      </el-option>
                    </el-select>
                  </template>
                  <template v-else-if="iitem.type=='tree'">
                    <el-popover ref="treeListPopover" width="600" trigger="click">
                      <el-tree :data="iitem.treeOptions" v-model="dataSource[iitem.field]" :node-key="iitem.treeKey" show-checkbox :ref='"treeNode"+iindex'
                        @check-change="treeCheckChangeHandle($event,`${iitem.field}`,`treeNode${iindex}`)" :default-expand-all="true" :highlight-current="true" :expand-on-click-node="false"
                        style="height:300px;overflow-y:auto;">
                      </el-tree>
                    </el-popover>
                    <el-input v-model="dataSource[iitem.field]" v-popover:treeListPopover :readonly="true" :placeholder='"请选择"+iitem.name' size="small"></el-input>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
            <slot :name="tpltData.licCatCd" :data="dataSource" :changeHandle="submitModifyHandle" v-if="tpltData.hasFormSlot"></slot>
          </el-form>
          <template v-if="operType=='read'">
            <ul v-if="tpltData.header.length>0" class="lic-ul clearfix">
              <li v-for="iitem in tpltData.header" class="lic-item" :key="iitem.rsrcCd">
                <div class="lic-item-desc"><span class="not-null" v-if="iitem.required">*</span>{{iitem.name}}:</div>
                <div class="lic-item-area">
                  <template v-if="dataSource">
                    <template v-if="iitem.type==='select'">
                      {{iitem.options.find(item=>item.value==dataSource[iitem.field])?iitem.options.find(item=>item.value==dataSource[iitem.field]).label:''}}
                    </template>
                    <template v-else-if="iitem.type==='date'">
                      <!--有效期-->
                      <span v-if="iitem.field==='licVldTo'">{{dataSource[iitem.field]}}
                        <span v-if="!isExpire && isWillExpire"
                              style="color:#e6a23c">(该证件将过期)
                        </span>
                      </span>
                      <!--等级评定有效期-->
                      <span v-if="iitem.field==='licVldDate'">{{dataSource[iitem.field]}}
                        <span v-if="!isExpireDate && isWillExpireDate"
                              style="color:#e6a23c">(该证件将过期)
                        </span>
                      </span>
                      <!--注册日期-->
                      <span v-if="iitem.field==='regDate'">{{dataSource[iitem.field]}}</span>
                    </template>
                    <template v-else>
                      {{dataSource[iitem.field]}}
                    </template>
                  </template>
                </div>
              </li>
            </ul>
            <slot :name="tpltData.licCatCd" :data="dataSource" v-if="tpltData.hasFormSlot"></slot>
          </template>
        </div>
      </div>
    </collapse-transition>
    <!-- 证照的图片审核结果展示 -->
    <collapse-transition>
      <div class="lic-panel-footer" v-if="tpltData.aprvAprvOfGongguan && operType=='read'" v-show="collapse">
        <div class="text-right">审核状态：
          <span class="lic-status">
            <template v-if="dataSource.handleFlag ==''">未提交</template>
            <template v-else-if="dataSource.handleFlag ==='1'">审核通过</template>
            <template v-else-if="dataSource.handleFlag ==='2'">
              审核未通过
              <template v-if="dataSource.handleRemark">，原因：{{dataSource.handleRemark}}</template>
              <template v-else>，原因：无</template>
            </template>
            <template v-else-if="dataSource.handleFlag ==='0'">
              待受理
            </template>
          </span>
        </div>
        <!-- 审核操作按钮 -->
        <approve-bar v-permission="'appr:update'" v-if="rejectReasons.length" :approve-info="approveInfo" :reject-reasons="rejectReasons" @getPassStatus="getPassStatus"
          @getRjectStatus="getRjectStatus" @getHandleStat="getHandleStat"></approve-bar>
      </div>
    </collapse-transition>
    <!--  pdf预览  -->
    <pdf-view v-show="pdfViewVisible" :src="pdfSrc" @filePreviewCancel="filePreviewCancel"></pdf-view>
  </div>
</template>

<script>
import collapseTransition from "@/components/CollapseTransition";
import uploadImgItem from "@/components/Certificates/components/uploadImgItem";
import approveBar from "@/components/approveBar";
import { uploadLicImage } from "@/api/lic";
import { mapGetters } from "vuex";
import watermark from "watermark-dom";
import pdfView from "@/components/pdf-view";
export default {
  name: "certificatesItem",
  props: {
    // 组件类型：read(只读) , edit(可读可写)
    operType: {
      type: String,
      required: true
    },
    // 证件模板数据
    tpltData: {
      type: Object,
      required: true
    },
    // 证件数据结果，默认是空
    dataSource: {
      type: Object
    },
    //自定义驳回理由
    rejectReasons: {
      type: Array,
      default: []
    }
  },
  components: {
    collapseTransition,
    uploadImgItem,
    approveBar,
    pdfView
  },
  data() {
    let rejectReason = this.getRejectReaons() || [];
    return {
      approveLogTitle: "",
      dialogVisible: false,
      loading: false,
      btnLoading: false,
      currentDate: new Date().getTime(),
      licApproveLogList: [],
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },
      collapse: true,
      watermark:watermark,
      pdfViewVisible:false,//pdf显示
      pdfSrc:"",//pdf地址
      rejectReason: rejectReason
    };
  },
  computed: {
    ...mapGetters(["username","mobile"]),
    licImgItemsData() {
      let _this = this,
        res = []; // 证照数据结果集
      Object.keys(this.tpltData.list).forEach(key => {
        let temp = _this.getLicImgItemData(key);

        if (temp) {
          res.push(temp);
        } else {
          res.push(
            Object.assign(
              {},
              {
                rsrcCd: key,
                url: null,
                thumbnailUrl: null,
                waterMarkUrl: null
              }
            )
          );
        }
      });
      return res;
    },
    licStatus() {
      // 需要审核的
      if (this.tpltData.aprvAprvOfGongguan) {
        if (this.dataSource.licVldTo && this.isExpired(this.dataSource)) {
          // 已过期
          return "deepred";
        } else if (this.dataSource.handleFlag === "1") {
          // 审核通过
          return "green";
        } else if (this.dataSource.handleFlag === "2") {
          // 未通过
          return "red";
        } else {
          // 待受理
          return "gray";
        }
      } else {
        // 不需要审核的
        if (this.dataSource.licVldTo && this.isExpired(this.dataSource)) {
          // 已过期
          return "deepred";
        } else {
          return "gray";
        }
      }
    },
    approveInfo() {
      let data = this.dataSource;
      let licApproveInfo = {
        entityPk: data.ipPk || data.vecPk || data.licPptPk || data.cntrPk,
        catCd: data.licCatCd,
        licPk: data.licPk,
        statCd: 1,
        desc: "",
        catNmCn: data.licCatNmCn
      };
      return licApproveInfo;
    },
    isExpire() {
      // 证件是否过期
      if (this.dataSource && this.dataSource.licVldTo) {
        let res = this.dataSource.licVldTo.match(/^\d{4}-\d{2}-\d{2}/),
          licVldTo = null;
        if (res.length > 0) {
          licVldTo = res[0];
          licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        }

        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    isWillExpire() {
      if (this.dataSource && this.dataSource.licVldTo) {
        let bssLicenceValidity = new Date(
          this.dataSource.licVldTo.replace(/-/g, "/")
        ).getTime();
        let bssLicenceValidityLast30 =
          bssLicenceValidity - 60 * 60 * 24 * 30 * 1000; //30天将到期提醒
        if (
          new Date(bssLicenceValidityLast30).getTime() < new Date().getTime()
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    isExpireDate() {
      // 证件是否过期
      if (this.dataSource && this.dataSource.licVldDate) {
        let res = this.dataSource.licVldDate.match(/^\d{4}-\d{2}-\d{2}/),
          licVldTo = null;
        if (res.length > 0) {
          licVldTo = res[0];
          licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        }

        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    isWillExpireDate() {
      if (this.dataSource && this.dataSource.licVldDate) {
        let bssLicenceValidity = new Date(
          this.dataSource.licVldDate.replace(/-/g, "/")
        ).getTime();
        let bssLicenceValidityLast30 =
          bssLicenceValidity - 60 * 60 * 24 * 30 * 1000; //30天将到期提醒
        if (
          new Date(bssLicenceValidityLast30).getTime() < new Date().getTime()
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    }
  },
  methods: {
    //获取驳回原因列表
    getRejectReaons() {
      return this.$props.rejectReasons || [];
    },
    treeCheckChangeHandle(event, field, refNodeStr) {
      this.dataSource[field] = this.$refs[refNodeStr][0]
        .getCheckedKeys()
        .join(",");
      this.submitModifyHandle();
    },

    // 折叠效果
    headerClickHandle() {
      this.collapse = !this.collapse;
    },

    //获取通过审核操作的返回结果
    getPassStatus(payload) {
      this.dataSource.handleFlag = payload.handleFlag;
      this.loading = false;
    },
    //获取驳回审核操作的返回结果
    getRjectStatus(payload) {
      this.dataSource.handleFlag = payload.handleFlag;
      this.dataSource.handleRemark = payload.remark;
      this.loading = false;
    },
    //点击通过或驳回审核操作的监听
    getHandleStat(type) {
      this.loading = true;
    },
    // 判断证件是否过期
    isExpired(data) {
      let licVldTo = data.licVldTo || null;
      if (!licVldTo) {
        return false;
      }
      let res = licVldTo.match(/^\d{4}-\d{2}-\d{2}/);

      if (res.length > 0) {
        licVldTo = res[0];
        licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },

    // 获取证照图片项数据
    getLicImgItemData(rsrcCd) {
      if (!this.dataSource || this.dataSource.subItems.length == 0) return null;
      let arr = this.dataSource.subItems.filter(item => {
        return item.rsrcCd == rsrcCd;
      });
      return arr.length > 0 ? arr[0] : null;
    },

    // 图片预览
    previewHandle() {
      let mobile = "";
      if (this.mobile){
        mobile = this.mobile.slice(-4);
      }
      watermark.init({
        watermark_txt: `${this.username}-${mobile}`,
        watermark_width:200,                //水印宽度
        // watermark_color: "#5579ee",            //水印字体颜色
        // watermark_fontsize: "24px",          //水印字体大小
        // watermark_alpha:0.5,               //水印透明度，要求设置在大于等于0.005
        // watermark_angle:135,                 //水印倾斜度数
        // watermark_height:200,
      });
      this.$emit("preview");
    },

    //pdf预览
    openPdf(src) {
      let mobile = "";
      if (this.mobile){
        mobile = this.mobile.slice(-4);
      }
      watermark.init({
        watermark_txt: `${this.username}-${mobile}`,
        watermark_width:200,
      });
      if (src) {
        this.pdfSrc = src;
        this.pdfViewVisible = true;
      } else {
        this.$message.error("暂无文件");
      }
      // window.open(src, "_blank");
    },

    //关闭pdf预览
    filePreviewCancel(){
      this.pdfViewVisible = false;
      watermark.load({
        watermark_txt: " " ,
      });
    },

    // 向父组件提交证件修改信息，触发父组件方法
    submitModifyHandle(data) {
      this.$nextTick(() => {
        this.$emit("modify", this.dataSource);
      });
    },

    // 删除证件操作
    delHandle(rsrcCd) {
      let parentKey = this.dataSource.licCatCd,
        childKey = rsrcCd;
      this.$nextTick(() => {
        this.$emit("delHandle", parentKey, childKey);
      });
    },

    // 裁剪证件操作，继承父组件方法
    cropperHandle(data) {
      let _this = this;
      this.$emit(
        "cropperHandle",
        Object.assign({}, data, {
          parentKey: _this.dataSource.licCatCd
        })
      );
    },

    // 验证表单信息
    validateForm() {
      let _this = this;
      if (this.$refs.licForm) {
        return new Promise((resolve, reject) => {
          this.$refs.licForm.validate(valid => {
            if (valid) {
              resolve({ code: 1, msg: _this.tpltData.licNm + "验证通过" });
            } else {
              resolve({
                code: 0,
                msg: _this.tpltData.licNm + "：信息填写不正确"
              });
            }
          });
        });
      } else {
        return new Promise((resolve, reject) => {
          resolve({ code: 1, msg: _this.tpltData.licNm + "不需要验证" });
        });
      }
    }
  }
};
</script>

<style scoped>
.lic-oper-bar {
  position: absolute;
  z-index: 1;
  text-align: center;
  display: block;
  bottom: 0;
  left: 0;
  right: 0;
}
.lic-uploader .el-upload-list--picture .el-upload-list__item {
  margin: 0;
  padding: 0;
  height: auto;
  border: none;
}
.viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px;
}
.lic-notice-expired {
  background: url("~@/assets/lic-imgs/licExpired.png") no-repeat;
  width: 160px;
  height: 160px;
  position: absolute;
  top: -8px;
  right: -8px;
}
</style>
