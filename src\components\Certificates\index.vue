<template>
  <div>
    <div ref="licListWape">
      <certificates-item v-for="(item, key, index) in certTeplData" ref="certificates" :key="item.rsrcCd" :reject-reasons="rejectReasons" :data-source="dataSourceSorted[index]" :oper-type="operType"
        :tplt-data="item" @cropperHandle="cropperHandle" @delHandle="delHandle" @modify="updateCertHandle" @preview="previewHandle">
        <template :slot="item.licCatCd" slot-scope="slotProps" v-if="item.hasFormSlot">
          <slot :name="item.licCatCd" :data="slotProps.data" :changeHandler="slotProps.changeHandle"></slot>
        </template>
      </certificates-item>
    </div>
    <!-- 证照的图片裁剪 -->
    <div class="cropper-container" v-show="startImageCrop">
      <!--[if lt IE 9]>
                <el-alert
                    type="error"
                    show-icon
                    title="信息提示"
                    description="You are using an <strong>outdated</strong> browser. Please <a href='http://browsehappy.com/'>upgrade your browser</a> to improve your experience."
                    :closable="false">
                </el-alert>
            <![endif]-->
      <div style="width:69vw;height:100vh;padding:10px;box-sizing:border-box;float:left;position:relative;">
        <p>裁剪前效果：</p>
        <div class="btn-group">
          <a class="btn" href="javascript:void(0)" title="移动" @click="move">
            <svg-icon icon-class="move" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="裁剪" @click="crop">
            <svg-icon icon-class="crop" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="左旋30度" @click="rotateLeft(30)">
            <svg-icon icon-class="rotate-left" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="右旋30度" @click="rotateRight(30)">
            <svg-icon icon-class="rotate-right" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="左右翻转" @click="scaleX">
            <svg-icon icon-class="left-right" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="上下翻转" @click="scaleY">
            <svg-icon icon-class="up-down" class-name="svg-icon"></svg-icon>
          </a>
          <a class="btn" href="javascript:void(0)" title="重置" @click="reset">
            <svg-icon icon-class="reset" class-name="svg-icon"></svg-icon>
          </a>

        </div>
        <div>
          <img ref="imageCropper" :src="imgCropperData.previewSrc" alt="剪裁前效果图">
        </div>
      </div>
      <div style="width:29vw;height:100vh;padding:10px;margin-right:0.5vw;box-sizing:border-box;float:right;position:relative;">
        <p>裁剪后效果：</p>
        <div style="width:100%;height:100%;max-width:100vw;overflow:hidden;" ref="previewImageCropper"></div>
        <div class="img_btn" style="position:absolute;bottom:0;left:0;right:0;text-align:center;padding:20px;">
          <el-button size="small" type="primary" @click="doCrop">提交剪裁</el-button>
          <el-button size="small" @click="cancelCrop">不剪裁</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import canvasToBlob from "blueimp-canvas-to-blob";
import certificatesItem from "@/components/Certificates/components/certificatesItem";
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.min.css";
import * as $httpLic from "@/api/lic";
import watermark from "watermark-dom";
export default {
  name: "Certificates",
  props: {
    // 组件类型：read(只读) , edit(可读可写)
    operType: {
      type: String,
      default: "read"
    },
    // 证件数据结果集（数组），默认是空
    dataSource: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 证件模板数据
    certTeplData: {
      type: Object,
      required: true
    },
    //自定义驳回理由
    rejectReasons: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  components: {
    certificatesItem
  },
  data() {
    return {
      imgCropperData: {
        file: null, // 上传的文件
        previewSrc: "" // 读取的img文件base64数据流
      },
      postCropperData: null, // 裁剪时子组件传递过来的数据
      cropper: null,
      startImageCrop: false,
      cropperLoading: false,
      scaleXFlag: 1,
      scaleYFlag: 1
    };
  },
  computed: {
    // 根据模板数据顺序排序证照数据dataSource，若证照数据不存在则生成对应证件数据格式
    dataSourceSorted() {
      let _this = this,
        res = []; // 证照数据结果集

      Object.keys(this.certTeplData).forEach(key => {
        let teplItem = _this.certTeplData[key],
          temp = _this.getCertData(key);

        if (temp) {
          res.push(temp);
        } else {
          let headers = teplItem.header,
            createDataItem = {},
            createSubItems = [];
          headers.forEach(function(item) {
            // createDataItem[item.field] = null;            // 存入证件文本资料数据，例如***号码，有效期等

            if (_this.operType == "read") {
              _this.$set(createDataItem, item.field, null);
            } else if (_this.operType == "edit") {
              if (!item.readonly) {
                _this.$set(createDataItem, item.field, null);
              }
            }
          });
          Object.keys(teplItem.list).forEach(key => {
            // 证件图片信息
            createSubItems.push({
              rsrcCd: key,
              url: null, // 原图地址
              thumbnailUrl: null, // 缩略图地址
              waterMarkUrl: null // 水印图片地址
            });
          });
          res.push(
            Object.assign({}, createDataItem, {
              licCatCd: key,
              subItems: createSubItems
            })
          );
        }
      });
      return res;
    }
  },
  methods: {
    createNodeLoading(targetNode, loadingText) {
      const loading = this.$loading({
        lock: true,
        text: loadingText ? loadingText : "加载中...",
        target: targetNode.$el
      });
      return loading;
    },

    // 根据cd获取证照真实数据
    getCertData(rsrcCd) {
      if (!this.dataSource || this.dataSource.length == 0) return null;
      let arr = this.dataSource.filter(it => {
        return it.licCatCd === rsrcCd;
      });
      return arr.length > 0 ? arr[0] : null;
    },

    // 设置证照图片数据
    setLicImgData(parentCd, childCd, resultData) {
      let parentIndex = null,
        childIndex = null;
      let arr = this.dataSourceSorted.filter((it, index) => {
        if (it.licCatCd === parentCd) {
          parentIndex = index;
        }
        return it.licCatCd === parentCd;
      });
      if (arr.length > 0) {
        arr[0].subItems.filter((it, index) => {
          if (it.rsrcCd === childCd) {
            childIndex = index;
          }
          return it.rsrcCd === childCd;
        });
        let temp = null;
        if (parentIndex != null && childIndex != null) {
          temp = Object.assign({}, this.dataSourceSorted[parentIndex], {
            isModify: 1
          });
          temp.subItems[childIndex] = Object.assign(
            {},
            this.dataSourceSorted[parentIndex].subItems[childIndex],
            resultData,
            { isModify: 1 }
          );
          this.$set(this.dataSourceSorted, parentIndex, temp);
        } else if (childIndex == null) {
          temp = Object.assign({}, this.dataSourceSorted[parentIndex], {
            isModify: 1
          });
          temp.subItems.push(
            Object.assign({ rsrcCd: childCd }, resultData, { isModify: 1 })
          );
          this.$set(this.dataSourceSorted, parentIndex, temp);
        }
      } else {
        this.dataSourceSorted.push({
          licCatCd: parentCd,
          subItems: [
            {
              rsrcCd: childCd,
              url: resultData.url,
              thumbnailUrl: resultData.thumbnailUrl,
              waterMarkUrl: resultData.waterMarkUrl,
              isModify: 1
            }
          ]
        });
      }
      this.$nextTick(() => {
        this.$emit("updateCertHandle", this.dataSourceSorted);
      });
    },

    // 获取dataSourceSorted数据的下标
    getDataSourceSortedIndex(parentCd) {
      let parentIndex = null;
      let arr = this.dataSourceSorted.filter((it, index) => {
        if (it.licCatCd === parentCd) {
          parentIndex = index;
        }
        return it.licCatCd === parentCd;
      });
      return parentIndex;
    },

    // 设置证照右侧基本数据信息
    setLicBaseData(parentCd, childCd, parentIndex, resultData) {
      let filterArr = null;
      if (parentIndex != null) {
        if (parentCd == "8010.400" && childCd == "8010.400.151") {
          // 人员身份证背面
          filterArr = resultData.filter(item => {
            return item.desc == "有效期至";
          });
          if (filterArr && filterArr.length > 0) {
            let vldTo = filterArr[0].content;
            if (/^\d{4}-\d{2}-\d{2}$/g.test(vldTo)) {
              this.$set(this.dataSourceSorted[parentIndex], "licVldTo", vldTo);
              this.$set(this.dataSourceSorted[parentIndex], "isModify", 1);
            }
          }
        } else if (parentCd == "8010.402" && childCd == "8010.402.150") {
          // 人员驾驶证有效期
          filterArr = resultData.filter(item => {
            return item.desc == "有效截止日期";
          });
          if (filterArr && filterArr.length > 0) {
            let vldTo = filterArr[0].content;
            if (/^\d{4}-\d{2}-\d{2}$/g.test(vldTo)) {
              this.$set(this.dataSourceSorted[parentIndex], "licVldTo", vldTo);
              this.$set(this.dataSourceSorted[parentIndex], "isModify", 1);
            }
          }
        } else if (parentCd == "8010.200" && childCd == "8010.200.150") {
          // 企业营业执照
          if (resultData && resultData.length > 0) {
            let item = resultData[0],
              vldDate = item.enddate.split("至")[1];
            if (vldDate == "长期") {
              vldDate = "2099-12-31";
            } else {
              vldDate = vldDate.match(/\d+/g).join("-");
            }
            this.$set(
              this.dataSourceSorted[parentIndex],
              "licCd",
              item.creditno
            );
            if (/^\d{4}-\d{2}-\d{2}$/g.test(vldDate)) {
              this.$set(
                this.dataSourceSorted[parentIndex],
                "licVldTo",
                vldDate
              );
            } else {
              this.$set(this.dataSourceSorted[parentIndex], "licVldTo", null);
            }
            this.$set(this.dataSourceSorted[parentIndex], "isModify", 1);
          }
        }
      }
      this.$nextTick(() => {
        this.$emit("updateCertHandle", this.dataSourceSorted);
      });
    },

    // 初始化剪切
    initCropper() {
      let _this = this;
      this.cropper = new Cropper(this.$refs.imageCropper, {
        aspectRatio: NaN, // 容器的比例
        viewMode: 1,
        preview: this.$refs.previewImageCropper,
        checkCrossOrigin: false,
        checkOrientation: false,
        autoCrop: false
        // ready: function () {
        //     console.log('cropper is ready');
        //     // var image = new Image();
        //     // image.src = _this.cropper.getCroppedCanvas().toDataURL('image/jpeg');
        //     // _this.$refs.previewImageCropper.appendChild(image);
        // }
      });
    },

    //让浏览器执行createObjectURL方法，实现本地图片预览
    getObjectURL(file) {
      var url = null;
      if (window.createObjectURL != undefined) {
        // basic
        url = window.createObjectURL(file);
      } else if (window.URL != undefined) {
        // firefox
        url = window.URL.createObjectURL(file);
      } else if (window.webkitURL != undefined) {
        // chrome
        url = window.webkitURL.createObjectURL(file);
      }
      return url;
    },
    // var reader = new FileReader();
    // // 将图片将转成 base64 格式
    // reader.readAsDataURL(_this.file);
    // reader.onload = function () {
    //     _this.imgCropperData.imgSrc = this.result;
    //     _this.initCropper();
    // }

    // 裁剪证件操作
    cropperHandle(data) {
      let _this = this;

      this.imgCropperData.file = data.file;
      this.postCropperData = data;

      this.startImageCrop = true;

      //每次替换图片要重新得到新的url
      if (!this.cropper) {
        this.initCropper();
      }
      let fileSrc = this.getObjectURL(this.imgCropperData.file);
      this.imgCropperData.previewSrc = fileSrc;
      //每次替换图片要重新得到新的url
      this.cropper.replace(fileSrc);

      this.scaleXFlag = 1;
      this.scaleYFlag = 1;
    },

    // 构造上传图片的数据
    submitImageUploadHandle() {
      const _this = this;
      let type = this.imgCropperData.file.type || "image/jpeg";

      let parentKey = this.postCropperData.parentKey,
        childKey = this.postCropperData.childKey,
        certItemTepl = this.certTeplData[parentKey].list[childKey];
      if (certItemTepl && certItemTepl.zjsb) {
        // 证件识别
        this.identifyImg(type, certItemTepl);
      }

      this.cropper.getCroppedCanvas().toBlob(function(blob) {
        // FormData 对象
        let formData = new FormData();
        formData.append("file", blob, _this.imgCropperData.file.name);

        $httpLic
          .uploadLicImage(formData)
          .then(response => {
            if (response.code === 0) {
              let res = response.data[0];
              _this.setLicImgData(parentKey, childKey, {
                url: res.fileUrl,
                thumbnailUrl: res.thumbUrl,
                waterMarkUrl: res.waterMarkUrl,
                isModify: 1
              });
            } else {
              _this.setLicImgData(parentKey, childKey, {
                url: "",
                thumbnailUrl: "",
                waterMarkUrl: "",
                isModify: 1
              });
              _this.$message({
                message: response.message,
                type: "error"
              });
            }
          })
          .catch(error => {
            _this.setLicImgData(parentKey, childKey, {
              url: "",
              thumbnailUrl: "",
              waterMarkUrl: "",
              isModify: 1
            });
            console.log(error);
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              console.log(
                "配时文件上传失败(" +
                  error.response.status +
                  ")，" +
                  error.response.data
              );
            } else if (error.request) {
              // The request was made but no response was received
              // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
              // http.ClientRequest in node.js
              console.log("配时文件上传失败，服务器端无响应");
            } else {
              // Something happened in setting up the request that triggered an Error
              console.log("配时文件上传失败，请求封装失败");
            }
          });
      }, type);
    },

    // 证件识别
    // 人员：身份证正面，驾驶证，行驶证
    // 车辆：
    // 企业：营业执照
    /**
     * imgType: 上传图片类型
     * certItemTepl: 证件模板数据
     */
    identifyImg(imgType, certItemTepl) {
      let _this = this,
        parentKey = this.postCropperData.parentKey,
        childKey = this.postCropperData.childKey;
      let imgBase64Data = this.cropper.getCroppedCanvas().toDataURL(imgType);

      // let postData = {
      //     typeId:certItemTepl.zjsbTypeId,
      //     type:certItemTepl.zjsbLicType,
      //     img:imgBase64Data.split(',')[1]
      // }
      const params = new URLSearchParams();
      params.append("typeId", certItemTepl.zjsbTypeId);
      params.append("type", certItemTepl.zjsbLicType);
      params.append("img", imgBase64Data.split(",")[1]);

      let parentIndex = this.getDataSourceSortedIndex(parentKey);

      let loading = this.createNodeLoading(
        this.$refs.certificates[parentIndex],
        "证件正在识别中，请稍等..."
      );
      $httpLic
        .licAutoCheck(params)
        .then(res => {
          if (res.code === 0) {
            if (res.data.message.indexOf("识别失败") >= 0) {
              console.log("识别失败");
            } else {
              _this.setLicBaseData(
                parentKey,
                childKey,
                parentIndex,
                res.data.items
              );
            }
            loading.close();
          } else {
            console.log("识别失败");
            loading.close();
          }
        })
        .catch(error => {
          console.log(error);
          loading.close();
        });
    },

    // 移动
    move() {
      this.cropper.setDragMode("move");
    },

    // 裁剪
    crop() {
      this.cropper.setDragMode("crop");
    },

    // 左旋
    rotateLeft(dataRotate) {
      this.cropper.rotate(-dataRotate);
    },

    // 右旋
    rotateRight(dataRotate) {
      this.cropper.rotate(dataRotate);
    },

    // 左右翻转
    scaleX() {
      this.scaleXFlag = -this.scaleXFlag;
      this.cropper.scaleX(this.scaleXFlag);
    },

    // 上下翻转
    scaleY() {
      this.scaleYFlag = -this.scaleYFlag;
      this.cropper.scaleY(this.scaleYFlag);
    },

    // 重置
    reset() {
      this.cropper.reset();
    },

    // 清空裁剪
    clearCrop() {
      this.cropper.clear();
    },

    // 销毁
    destroyCrop() {
      if (this.cropper) {
        this.cropper.destroy();
      }
    },

    // 确认剪切操作
    doCrop() {
      this.startImageCrop = false;
      this.submitImageUploadHandle();
    },

    // 取消剪切操作
    cancelCrop() {
      this.startImageCrop = false;
      this.clearCrop();
      this.submitImageUploadHandle();
    },

    // 图片预览
    previewHandle() {
      var viewer = new Viewer(this.$refs.licListWape, {
        zIndex: 2099,
        url(image) {
          // return image.src.replace(/\@\w+\.src$/, "");
          return image.alt;
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container";
        },
        viewed() {
          let viewCanvas = viewer.viewer.getElementsByClassName(
            "viewer-canvas"
          );
          if (viewCanvas.length > 0) {
            let imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft =
                parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
            }
          }
        },
        hidden() {
          watermark.load({
            watermark_txt: " " ,
          });
          viewer.destroy();
        }
      });
    },

    // 删除证件方法
    delHandle(parentCd, childCd) {
      let resultData = {
        rsrcCd: childCd,
        url: "",
        thumbnailUrl: "",
        waterMarkUrl: "",
        isModify: 1
      };
      this.setLicImgData(parentCd, childCd, resultData);
    },

    // 修改证件
    updateCertHandle(data) {
      let licIndex = null;
      let arr = this.dataSourceSorted.filter((it, index) => {
        if (it.licCatCd === data.licCatCd) {
          licIndex = index;
        }
        return it.licCatCd === data.licCatCd;
      });
      if (licIndex !== null) {
        data.isModify = 1;
        this.$set(this.dataSourceSorted, licIndex, Object.assign({}, data));
      } else {
        data.isModify = 1;
        this.dataSourceSorted.push(data);
      }
      this.$nextTick(() => {
        this.$emit("updateCertHandle", this.dataSourceSorted);
      });
    },

    // 验证表单信息
    validateForm() {
      let _this = this,
        promises = [],
        temp = null;

      if (this.$refs.certificates && this.$refs.certificates.length > 0) {
        this.$refs.certificates.forEach(item => {
          temp = new Promise((resolve, reject) => {
            item.validateForm().then(res => {
              if (res && res.code == 1) {
                resolve({ code: 1, msg: res.msg });
              } else {
                resolve({ code: 0, msg: res.msg });
              }
            });
          });
          promises.push(temp);
        });
        let isValid = true,
          msg = "";
        return Promise.all(promises)
          .then(results => {
            for (let i = 0, len = results.length; i < len; i++) {
              if (results[i].code == 0) {
                isValid = false;
                msg += results[i].msg + "<br />";
                // break;
              }
            }
            if (isValid) {
              return true;
            } else {
              _this.$message({
                type: "error",
                dangerouslyUseHTMLString: true,
                message: msg
              });
              return false;
            }
          })
          .catch(() => {
            return false;
          });
      } else {
        return new Promise((resolve, reject) => {
          resolve(1);
        });
      }
    }
  },

  destroyed() {
    this.destroyCrop();
  }
};
</script>

<style scoped>
.cropper-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  background-color: #797979;
  font-size: 16px;
  color: #fff;
}
.btn-group {
  display: table;
  margin-bottom: 8px;
  width: 100%;
  text-align: center;
}
.btn {
  display: inline-block;
  background: #409eff;
  padding: 5px 10px;
  margin: 0;
  color: #fff;
  font-size: 15px;
  border-right: 1px solid rgba(255, 255, 255, 0.5);
}
.btn:first-child {
  border-radius: 5px 0 0 5px;
}
.btn:last-child {
  border-radius: 0 5px 5px 0;
  border-right: none;
}
.svg-icon {
  font-size: 20px;
}
</style>
