<template>
  <div>
    <template v-if="readOnly">
      <file-preview v-model="modelVal"></file-preview>
    </template>
    <template v-else>
      <el-upload :drag="drag" class="el-upload-files" ref="upload" :action="actionUrl" :file-list="filesArray" :multiple="multiple" :limit="limit" :http-request="upload" :on-preview="handleFilePreview"
        :on-exceed="handleExceed" :on-remove="handleRemove"
        :before-upload="beforeUpload">
        <template v-if="drag">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </template>
        <template v-else>
          <el-button size="small" type="primary" :v-loading="loading">
            <span v-if="loading">正在上传，请稍等...</span>
            <span v-else>点击上传</span>
          </el-button>
        </template>
        <div class="el-upload__tip" slot="tip">
          <!-- <el-button round v-if="readClipBord" size="mini" type="primary" :loading="loading" @click="uploadByClipBoard" icon="el-icon-document-copy">从剪切板上传</el-button> -->
          <span v-if="acceptFileType.length>0">只能上传{{acceptFileType.join(',')}}格式文件<span v-if="limit!=999">，最多上传{{limit}}个</span></span>
          {{ tip }}
          <el-button round v-if="readClipBord" size="mini" type="text" :loading="loading" @click="uploadByClipBoard" icon="el-icon-scissors">从剪切板上传</el-button>
        </div>
      </el-upload>
    </template>
  </div>
</template>
<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import * as $http from "@/api/common";
import filePreview from "@/components/FilesPreview";
import {Base64} from 'js-base64'
import {getUUID} from "@/utils/tool"
export default {
  name: "fileUpload",
  components: {
    filePreview
  },
  model: {
    prop: "modelVal",
    event: "modelChangeEvent"
  },
  props: {
    modelVal: {
      type: [Array, String],
      default: function() {
        return [];
      }
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    actionUrl: {
      type: String,
      default: "/sys/oss/uploadFile"
    },
    acceptFileType: {
      type: Array,
      default: function() {
        return [];
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    drag:{
      type:Boolean,
      default:false
    },
    readClipBord:{
      type:Boolean,
      default:false
    },
    tip: {
      type: String,
      default: ""
    },
    outPut: {
      type: String,
      default: "string" // 可以选择string, array  string 逗号分隔
    },
    postData: {
      type: Object,
      default: null
    },
    max: {
      type: Number,
      default: 1024 * 1024 * 100
    },
    limit: {
      type: Number,
      default: 999
    }
  },
  data() {
    return {
      loading: false
    };
  },
  computed: {
    filesArray() {
      let urls = [];

      let files = this.modelVal;
      if (Object.prototype.toString.apply(files) === "[object Array]") {
        urls = files;
      } else if (
        Object.prototype.toString.apply(files) === "[object String]" &&
        files.length > 0
      ) {
        urls = files.split(",");
      }
      return urls.map(function(value, index, array) {
        return {
          name: value.slice(value.indexOf("_") + 1),

          url: value
        };
      });
    }
  },
  methods: {
    handleExceed(file, fileList) {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    handleRemove(file, fileList) {
      let urlMap = fileList.map(item => {
        return item.url;
      });
      this.submitModifyHandle(urlMap);
    },
    // 上传列表文件预览
    handleFilePreview(file) {
      var src = file.url;
      if (/.pdf$/.test(src)) {
        window.open(src, "_blank");
      } else if (/.(doc|docx|docm|dot|ppt|pptx|pptm|xls|xlsx|xlsb|xlsm)$/.test(src)) {
        window.open(
          "https://fileview.dacyun.com/preview/onlinePreview?url=" +
            encodeURIComponent(Base64.encode(src)),
          "_blank"
        );
      } else if (/.(jpg|jpeg|png|gif)$/.test(src)) {
        this.showImage(src);
      } else if (/.(wmv|asf|asx|mp3|mp4)$/.test(src)) {
        window.open(src, "_blank");
      } else {
        this.downLoadFile(src);
      }
    },
    // 图片预览
    showImage(url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 99999,
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    // 下载非doc,excel,图片的其他类型文件
    downLoadFile(url) {
      // console.log(url)
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      let fileName = url.slice(url.lastIndexOf("/") + 1);
      let fileType = url.slice(url.lastIndexOf(".") + 1);
      link.setAttribute("download", `${fileName}.${fileType}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    },
    beforeUpload(file){
      this.validateFileHandle(file);
    },
    // 文件格式和大小验证
    validateFileHandle(file) {
      let isOverMax = file.size < this.max;
      if (this.$props.acceptFileType.length > 0) {
        let name = file.name
          .substring(file.name.lastIndexOf(".")+1)
          .toLowerCase();
        if (!this.acceptFileType.includes(name)) {
          this.$message.error(
            `上传文件格式不正确,请上传 ${this.acceptFileType.join(
              "，"
            )} 格式的文件!`
          );
          return false;
        }
      }
      if (!isOverMax) {
        this.$message.error(
          `上传文件大小不能超过 ${this.max / (1024 * 1024)} M!`
        );
        return false;
      }
      return true;
    },
    // 通过获取剪切板内容上传文件
    async uploadByClipBoard(){
      let file = null;
      let {onProgress, onError, onSuccess} = this.$refs.upload
      let dataItem = {
        onProgress, 
        onError, 
        onSuccess
      }

      try{
        const clipboardItems = await navigator.clipboard.read();
        for (const clipboardItem of clipboardItems) {
          for (const type of clipboardItem.types) {
            const blob = await clipboardItem.getType(type);
            const fileName = getUUID()+'.'+(type.split('/')[1]);
            file = new window.File([blob], fileName, {type:type})
            dataItem.file = file;
            this.upload(dataItem)
          }
        }
      }catch(err){
        console.error(err.name, err.message);
      }
    },
    // 自定义上传
    upload(item) {
      let _this = this;
      let file = item.file;
      let formData = new FormData();

      // 判断文件格式
      let isValidate = this.validateFileHandle(file);
      if (!isValidate) {
        item.onError();
        return false;
      }

      formData.append("file", file, file.name);
      if (this.postData) {
        Object.keys(this.postData).forEach(key => {
          formData.append("key", _this.postData.key);
        });
      }
      const uploadProgressEvent = event => {
        item.file.percent = (event.loaded / event.total) * 100;
        item.onProgress(item.file);
      };
      this.loading = true;
      $http
        .uploadFile(formData, uploadProgressEvent)
        .then(res => {
          _this.loading = false;
          if (res && res.code === 0) {
            _this.addFileUrl(res.data);
            item.onSuccess("上传成功");
          } else {
            _this.$message({
              message: res.msg,
              type: "error"
            });
            item.onError("上传失败");
          }
        })
        .catch(error => {
          _this.loading = false;
          item.onError("上传失败");
          console.log(error);
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.log(
              "配时文件上传失败(" +
                error.response.status +
                ")，" +
                error.response.data
            );
          } else if (error.request) {
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
            // http.ClientRequest in node.js
            console.log("配时文件上传失败，服务器端无响应");
          } else {
            // Something happened in setting up the request that triggered an Error
            console.log("配时文件上传失败，请求封装失败");
          }
        });
    },

    addFileUrl(data) {
      if (!data || !data.length) return;
      let oldImagesUrl = this.modelVal;
      let newImageArr = [];
      if (Object.prototype.toString.apply(oldImagesUrl) === "[object Array]") {
        newImageArr = oldImagesUrl;
      } else if (
        Object.prototype.toString.apply(oldImagesUrl) === "[object String]" &&
        oldImagesUrl.length > 0
      ) {
        newImageArr = oldImagesUrl.split(",");
      }
      data.forEach(it => {
        if (it.isSuccess) {
          newImageArr.push(it.fileUrl);
        }
      });
      this.submitModifyHandle(newImageArr);
    },
    // 向父组件提交证件修改信息，触发父组件方法
    submitModifyHandle(dataArr) {
      this.$nextTick(() => {
        if (this.outPut === "string") {
          this.$emit("change", dataArr.join(","));
          this.$emit("modelChangeEvent", dataArr.join(","));
        } else {
          this.$emit("change", dataArr);
          this.$emit("modelChangeEvent", dataArr);
        }
      });
    }
  }
};
</script>