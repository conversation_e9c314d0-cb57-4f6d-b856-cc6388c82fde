<template>
	<div>
	    <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg" @click="toggleClick" class=" hamburger" :class="{'is-active':isActive}">
			<path stroke="#fff" d="m14.85681,16.69049l20.33971,0.15872l-20.33971,-0.15872z" fill-opacity="null" stroke-opacity="null" stroke-width="2" />
			<path stroke="#fff" d="m14.85681,24l20.33971,0.15872l-20.33971,-0.15872z" fill-opacity="null" stroke-opacity="null" stroke-width="2" />
			<path stroke="#fff" d="m14.85681,31.25569l20.33971,0.15872l-20.33971,-0.15872z" fill-opacity="null" stroke-opacity="null" stroke-width="2"/>
		</svg>
	</div>	
</template>

<script>

export default {
	name:'hamburger',
	props: {
		isActive: {
			type: Boolean,
			default: false
		},
		toggleClick: {
			type: Function,
			default: null
		}
	}
}

</script>

<style scoped>

.hamburger {
    display: inline-block;
    cursor: pointer;
    width: 50px;
    height: 50px;
    transform: rotate(0deg);
    transition: .38s;
    transform-origin: 50% 50%;
}
.hamburger.is-active {
    transform: rotate(90deg);
}

</style>	
