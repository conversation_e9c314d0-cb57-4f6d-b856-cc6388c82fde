<template>
  <div class="multiple-editor">
    <el-form-item v-if="isTextEditor" label="详情描述：" prop="oprContent" :rules="$rulesFilter({ required: true })">
      <wangeditor ref="wangeditor" v-model="formData.oprContent" placeholder="请输入详情描述" class="wangeditor"></wangeditor>
    </el-form-item>
    <template v-else>
      <el-form-item label="详情描述：" prop="oprContent" :rules="$rulesFilter({ required: true })">
        <el-input v-model="formData.oprContent" placeholder="请输入详情描述" rows="4" type="textarea" />
      </el-form-item>
      <el-form-item label="证据上传：">
        <file-upload v-model="formData.oprFiles" :drag="isDrag" :readClipBord="readClipBord"
          :acceptFileType="acceptFileType" />
      </el-form-item>
    </template>
  </div>
</template>

<script>
import FileUpload from "@/components/FileUpload"
import wangeditor from "@/components/editor/wangeditor";
export default {
  name: "",
  model: {
    prop: "modelVal",
    event: "change"
  },
  props: {
    modelVal: {
      type: String,
      default: ''
    }
  },
  components: {
    FileUpload,
    wangeditor
  },
  data() {
    return {
      isDrag: true,
      readClipBord: true,
      isTextEditor: null,
      formData: {
        oprContent: "",
        oprFiles: ""
      },
      acceptFileType: ['bmp', 'jpg', 'jpeg', 'png', 'gif']
    };
  },
  watch: {
    "formData": {
      handler(formData) {
        if (formData) {
          let formDataCopy = null;
          if (this.isTextEditor) {
            formDataCopy = formData.oprContent
          } else {
            formDataCopy = JSON.parse(JSON.stringify(formData));
            if (formData.oprFiles) {
              formDataCopy.oprFiles = formData.oprFiles.split(",");
            } else {
              formDataCopy.oprFiles = formData.oprFiles;
            }
            formDataCopy = JSON.stringify(formDataCopy)
          }

          this.$emit("change", formDataCopy)
        }
      },
      deep: true
    },
    "modelVal": {
      handler(val) {
        if (val) {
          let parseVal = null
          try {
            parseVal = JSON.parse(val);
          }
          catch (err) {
            parseVal = null
          }

          if (parseVal !== null) {
            this.isTextEditor = false;
            this.$nextTick(() => {
              this.$set(this.formData, 'oprContent', parseVal.oprContent)
              parseVal.oprFiles && this.$set(this.formData, 'oprFiles', parseVal.oprFiles.join(','))
            })
          } else {
            this.isTextEditor = true;
            this.$nextTick(() => {
              this.$set(this.formData, 'oprContent', val)
            })
          }
        } else {
          this.isTextEditor = false;
          this.$nextTick(() => {
            this.$set(this.formData, 'oprContent', '')
            this.$set(this.formData, 'oprFiles', '')
          })
        }
      }
    }
  },
  // computed:{
  // isTextEditor(){
  //   if(this.formData.oprContent){
  //     if(/^{.*}$/.test(this.formData.oprContent)){
  //       return false;
  //     }else{
  //       return true;
  //     }
  //   }else{
  //     return false;
  //   }
  // }
  // },
  methods: {

  }
};
</script>

<style lang="scss" scoped>
.multiple-editor {
  overflow: hidden;
}
</style>
