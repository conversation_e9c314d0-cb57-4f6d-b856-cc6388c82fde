<template>
  <div>
    <el-cascader
      v-model="regionVal"
      :options="regionOptions"
      :props="cascaderProps"
      filterable clearable size="small"
      @change="regionDistCdChange"
      :disabled="disable"/>
  </div>
</template>

<script>
  import * as $http from '@/api/region'

  export default {
    name: 'RegionPicker',
    data() {
      return {
        regionOptions: [], // 省市区信息
        cascaderProps: {
          value: 'code',
          label: 'name',
          children: 'cell'
        }
      }
    },
    model: {
      prop: 'modelVal',
      event: 'modelEventChange'
    },
    props: {
      modelVal: {
        type: Array,
      },
      disable: {
        type: Boolean,
        default: false
      }
    },
    created() {
      this.getRegionVersion();//获取行政区域版本
    },
    computed: {
      regionVal: {
        get() {
          return this.modelVal && this.modelVal.length ? this.modelVal : []
        },
        set(newVal) {
          return newVal
        }
      },
      regionNm: {
        get() {
          let valArr = this.modelVal
          let regionDist
          if (valArr.length === 3) {
            const res = this.getCascaderNm(valArr, this.regionOptions)
            if (res) {
              regionDist = `${res[0].name}${res[1].name}${res[2].name}`
            } else {
              regionDist = ''
            }
          } else {
            regionDist = ''
          }
          return regionDist
        },
        set(newVal) {
          return newVal
        }
      }
    },
    methods: {
      //获取行政区域版本
      getRegionVersion() {
        $http.getRegionVersion().then().then(res => {
          if (res && res.code === 0) {
            let lastVersion = res.data;
            let regionVersion = sessionStorage.getItem("regionVersion");
            if (regionVersion && (lastVersion === regionVersion)) {
              let storageRegion = JSON.parse(sessionStorage.getItem("regionOptions"));
              if (storageRegion && storageRegion.length) {
                this.regionOptions = storageRegion;
              } else {
                this.getRegionList();
              }
            } else {
              sessionStorage.setItem("regionVersion", lastVersion);
              this.getRegionList();
            }
          }
        });
      },
      //获取行政区域列表
      getRegionList() {
        $http.getRegionList().then(res => {
          if (res && res.code === 0) {
            this.regionOptions = this.toTreeData(res.data, "0");
            sessionStorage.setItem("regionOptions", JSON.stringify(this.regionOptions));
          }
        });
      },
      //转化为树形结构
      toTreeData(data, parentId) {
        const itemArr = []
        data.forEach(item => {
          if (item.pid == parentId) {
            const newNode = {}
            newNode.code = item.id.toString()
            newNode.name = item.name
            if (item.leaf) {
              newNode.cell = null
            } else {
              newNode.cell = this.toTreeData(data, item.id)
            }
            itemArr.push(newNode)
          }
        })
        return itemArr
      },
      getValueName() {
        return this.regionNm
      },
      //改变区域
      regionDistCdChange(valArr) {
        let regionDistCd, regionDist
        if (valArr.length === 3) {
          const res = this.getCascaderNm(valArr, this.regionOptions)
          if (res) {
            regionDistCd = valArr
            regionDist = `${res[0].name}${res[1].name}${res[2].name}`
          } else {
            regionDistCd = []
            regionDist = ''
          }
        } else {
          regionDistCd = []
          regionDist = ''
        }
        this.$emit('modelEventChange', regionDistCd)
        this.$emit('change', regionDist)
      },
      // 获取级联选择器的值
      getCascaderNm(valArr, regionOptions) {
        return valArr.map(function(value, index, array) {
          for (var itm of regionOptions) {
            if (itm.code === value) {
              regionOptions = itm.cell
              return itm
            }
          }
          return null
        })
      }
    }
  }
</script>

<style scoped>

</style>
