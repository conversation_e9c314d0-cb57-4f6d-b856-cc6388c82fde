<template>
  <div style="padding-bottom: 40px">
    <div v-html="tableTitle"></div>
    <el-table
      class="el-table"
      :data="tablePage.list"
      highlight-current-row
      style="width: 100%"
      :max-height="maxHeight"
      border
      @sort-change="handleSort"
      :size="size"
    >
      <el-table-column type="index" width="50"></el-table-column>
      <template v-for="(item, index) in tableHeader">
        <el-table-column
          v-if="item.operations"
          :label="item.name"
          :key="index"
          :width="item.width"
          :formatter="item.formatter"
        >
          <template slot-scope="scope">
            <template v-for="(operate, operateIndex) in item.operations">
              <el-button
                v-if="operate.isShow"
                size="mini"
                :type="operate.type"
                @click="operate.func(scope.row)"
                :key="operateIndex"
                v-show="operate.isShow(scope.row)"
                >{{ operate.label }}</el-button
              >
              <el-button
                v-else
                size="mini"
                :type="operate.type"
                @click="operate.func(scope.row)"
                :key="operateIndex"
                >{{ operate.label }}</el-button
              >
            </template>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :label="item.name"
          :key="index"
          :width="item.width"
          :formatter="item.formatter"
          :sortable="item.sortable ? 'custom' : false"
          :prop="item.field"
        >
          <template slot-scope="scope">
            <template v-if="item.formatter">
              <span
                v-html="item.formatter(scope.row[item.field], scope.row, index)"
              ></span>
            </template>
            <template v-else>
              <span v-html="scope.row[item.field]"></span>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!--工具条-->
    <div class="toolbar">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="tablePage.pageSize"
        :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="tablePage.totalCount"
        style="float: right" :size="size"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "SimpleTable",
  props: {
    tableTitle: {
      type: String,
      default: "",
    },
    tableHeader: {
      type: Array,
      required: true,
    },
    tablePage: {
      type: Object,
      default() {
        return {
          list: [],
          currPage: 0,
          pageSize: 20,
          totalPage: 0,
        };
      },
    },
    maxHeight: {
      type: Number,
      default: 400,
    },
    size:{
      type: String,
      default: 'small',
    }
  },
  methods: {
    // 分页跳转
    handleCurrentChange: function (val) {
      this.tablePage.currPage = val;
      let params = Object.assign(
        {},
        {
          page: this.tablePage.currPage > 0 ? this.tablePage.currPage : 1,
          limit: this.tablePage.pageSize,
        }
      );
      this.$emit("tableRefreshByPagination", params);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.tablePage.pageSize = val;
      let params = Object.assign(
        {},
        {
          page: this.tablePage.currPage > 0 ? this.tablePage.currPage : 1,
          limit: val,
        }
      );
      this.$emit("tableRefreshByPagination", params);
    },
    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.$emit("tableRefreshByPagination", sortParam);
    },
  },
};
</script>
