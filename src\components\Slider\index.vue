<template>
<div class="container">
	<div class="slider-wape clearfix" :style="{left:left+'px'}" @mouseover="mouseover" @mouseleave="mouseleave">
		<div ref="slider" style="display:inline-block;" class="">
			<slot :list="dataSource">

			</slot>
		</div>
	</div>
</div>
</template>

<script>
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.min.css'
export default {
	name: 'Slider',
	props: {
		dataSource:{
			type:Array,
			default:true
		}
	},
	data(){
		return {
			left:0,
			intervalTimers:[],
			viewer:null
		}
	},
	destroyed(){
		this.clearMove();
		if(this.viewer){
			this.viewer.destroy();
		}
	},
	mounted(){
		
	},
	watch:{
		dataSource:{
            handler(){
				this.previewHandle();
            },
            deep:true
        }
	},
	methods: {
		init(){
			this.startMove();
			this.$nextTick(()=>{
				this.previewHandle();
			})
		},

		clearMove(){
			this.intervalTimers.forEach(item=>{
				clearTimeout(item);
			})
		},

		startMove(){
			let _this = this;
			let interval = setInterval(function(){
				_this.left = _this.left-1;
				if(_this.left<(-_this.$refs.slider.offsetWidth)){
					_this.left = 0;
				}
			},100);
			this.intervalTimers.push(interval);
		},

		mouseover(){
			this.clearMove();
		},

		mouseleave(){
			this.startMove();
		},

		previewHandle(){
			let _this = this;
			if(this.viewer){
				this.viewer.destroy();
			}
			this.viewer = new Viewer(this.$refs.slider, {
                url(image) {
                    return image.src.replace(/\@\w+\.src$/, '');
                },
                // hidden(){
                //     _this.viewer.destroy();
                // }
            })
		}
	}
}
</script>
<style scoped>
.container{
	padding:1vw 0;
	width:95%;
	margin:0 auto;
	overflow: hidden;
}
.slider-wape{
	width: 999999px;
    position:relative;
}
</style>
