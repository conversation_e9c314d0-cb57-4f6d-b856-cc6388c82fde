<template>
    <li class="time-line-item">
        <div class="time-line-item_node">

        </div>
        <div class="time-line-item_body">
            <div class="time-line-item_timestamp">{{timeStamp}}</div>
            <div  class="time-line-item_content">
                <slot/>
            </div>
        </div>
    </li>
</template>
<script>
export default {
    name:"TimelineItem",
    props:{
        timeStamp:{
            type:String,
            default:""
        }
    },
    data(){
        return {

        }
    }
}
</script>

<style lang="scss" scoped>
    .time-line-item{
        position: relative;
        padding-left: 20px;
        padding-bottom: 10px;

        &::after{
            content: "";
            position: absolute;
            top:1px;
            left:10.5px;
            width: 1px;
            height: 100%;
            background-color: #ccc;
        }

        &:last-child::after{
            display: none;
        }

        .time-line-item_body{
            position: relative;
            top: -3px;

            .time-line-item_timestamp{
                line-height: 1;
                font-size: 13px;
                color: rgb(175, 175, 175);
            }
        }

       

        .time-line-item_node{
            position: absolute;
            left: 5px;
            top:0px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            line-height: 1;
            background-color: #ccc;
        }
    }
</style>