<template>
  <div >
    <slot name="prepend"></slot>
    <div style="position:relative;text-align: center;background: #212224;">
      <template v-if="dataSource.isOnLine === '在线'">
        <div class="video-wrapper" v-show="isShowVideo" :style="{ width: '100%', height: posterHeight + 'px'}">
          <video
              :id="playerId"
              :ref="playerId"
              class="video-js vjs-default-skin prism-player"
              controls
              preload="auto"
              :height="posterHeight"
          >
            <source :src="playerOption.url" type="application/x-mpegURL"/>
          </video>

          <div v-show="videoMaskLoading" v-loading="videoMaskLoading"
               :style="{ width: posterWidth + 'px', height: posterHeight + 'px', position: 'absolute', background: '#000' }"></div>
        </div>
        <div v-show="!isShowVideo"
             :style="{ width: '100%', height: posterHeight + 'px', margin: '0 auto', 'background-color': '#212224',overflow:'hidden'}">
          <img :src="dataSource.playerOption.poster" :style="{height:posterHeight + 'px',maxWidth:'100%'}"/>
          <div class="video-play-btn-wape" title="点击播放" @click="loadVideoPlayer">
            <svg-icon icon-class="play" class-name="video-play-btn"></svg-icon>
          </div>
        </div>
      </template>
      <div v-else :style="{ width: '100%', height: posterHeight + 'px',overflow:'hidden'}">
        <img src="static/img/video-img/no-signal.jpg" :style="{height:posterHeight + 'px',maxWidth:'100%'}"/>
      </div>
    </div>
    <slot name="append"></slot>
  </div>

</template>

<script>
  export default {
    name: "VideoPlayer",

    props: {
      dataSource: {
        type: Object
      },
      posterHeight: {
        type: Number,
        default: 260
      },
      posterWidth: {
        type: Number,
        default: 356
      },
    },
    data() {
      return {
        playerId: "playerId" + new Date().getTime(),
        videoMaskLoading: false,
        videoPlayer: null,
        playerOption: {
          id: "",
          url: "",
          cover: ""
        },
        isShowVideo: false,
        playType: ''
      };
    },
    mounted() {

    },
    methods: {
      loadVideoPlayer() {
        if (this.dataSource.videoUrl || this.dataSource.streamingAddressHLS) {
          this.playerOption.id = this.playerId;
          this.playerOption.cover = this.dataSource.playerOption.poster;
          //录播
          if (this.dataSource.videoUrl) {
            this.playType = 'playBack'
            this.playerOption.url = this.dataSource.videoUrl;
          }
          //直播
          if (this.dataSource.streamingAddressHLS) {
            this.playerOption.url = this.dataSource.streamingAddressHLS;
          }
          this.isShowVideo = true;
          this.$nextTick(() => {
            this.initVideoPlayer();
          })
        }
      },

      initVideoPlayer(type) {
        let _this = this;
        let playbackRates = []
        if (this.playType === 'playBack') {
          playbackRates = [1, 2, 4, 8]
        }
        if (!this.videoPlayer) {
          this.videoPlayer = this.$video(this.playerId, {
            bigPlayButton: false,
            textTrackDisplay: false,
            posterImage: false,
            errorDisplay: false,
            controlBar: true,
            loadingSpinner: false,//隐藏加载动画
            playbackRates: playbackRates, // 倍速播放配置
          })
          this.videoPlayer.play()
        } else {
          this.onPlayerPlay();
        }
      },

      onPlayerPlay(event) {
        this.videoPlayer.play();
      },

      onPlayerPause(event) {
        this.videoPlayer.pause();
      },
      destroyPlayer(){
        let player = this.videoPlayer
        if (player) {
          player.pause()
          player.dispose()
          this.videoPlayer = null
        }
      }
    },
    beforeDestroy() {
      let player = this.videoPlayer
      if (player) {
        player.pause()
        player.dispose()
        this.videoPlayer = null
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video-play-btn-wape {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -30px;
    margin-left: -30px;
    display: inline-block;

    .video-play-btn {
      font-size: 60px;
      color: #f5f5f5;
      cursor: pointer;

      &:hover {
        // color: #00aeff;
        color: #fff;
      }
    }
  }

  .video-wrapper {
    // width: 100%;
    margin: 0 auto;
    overflow: hidden;
    text-align: center;
    // background-color: #f8f8f8;
    background-color: #212224;

    .prism-player {
      max-width: 100%;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
</style>
