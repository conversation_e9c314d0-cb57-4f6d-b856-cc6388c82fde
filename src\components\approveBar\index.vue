<template>
  <div>
    <div v-permission="permission" v-if="isGovBsCatcdAudit">
      <div class="approvalOper text-right">
        <el-button-group>
          <el-button type="success" size="mini" @click="approved" title="通过">
            通过
          </el-button>
          <el-button type="danger" size="mini" @click="rejective" title="驳回">
            驳回
          </el-button>
          <!-- <el-button
            type="primary"
            size="mini"
            @click="basicApproveLog"
            :loading="btnLoading"
          >
            审核日志
          </el-button> -->
          <!-- <el-button v-if="show" type="warning" size="mini" @click="searchLog" :loading="btnLoading2">
            操作日志
          </el-button> -->
        </el-button-group>
      </div>
      <!-- 审核日志弹窗 -->
      <el-dialog
        :title="getApproveType()"
        :visible.sync="dialogVisible"
        width="40%"
      >
        <div style="max-height:400px;overflow-y:auto;">
          <el-steps direction="vertical" :active="1">
            <template v-for="(item, index) in basicApproveLogList">
              <el-step
                v-if="item.statCd == 2"
                status="error"
                :key="index"
                :title="item.apprTm"
              >
                <div slot="description">
                  <div v-if="item.entityDesc">
                    <div style="float:left">审核图片:</div>
                    <img
                      style="margin-left:5px"
                      :src="pic"
                      alt=""
                      :data-index="imgindex"
                      @click="previewPic(item.entityDesc)"
                      width="45"
                      height="45"
                      v-for="(pic, imgindex) in (item.entityDesc + '').split(
                        ','
                      )"
                      :key="imgindex"
                    />
                  </div>
                  <div>审核人: {{ item.apprUserNm }}</div>
                  <div>审核结果: {{ item.statNmCn }}</div>
                  <div>审核备注: {{ item.desc }}</div>
                </div>
              </el-step>
              <el-step
                v-else
                status="success"
                :key="index"
                :title="item.apprTm"
              >
                <div slot="description">
                  <div v-if="item.entityDesc">
                    <div style="float:left">审核图片:</div>
                    <img
                      style="margin-left:5px"
                      :src="pic"
                      alt=""
                      :data-index="imgindex"
                      @click="previewPic(item.entityDesc)"
                      width="45"
                      height="45"
                      v-for="(pic, imgindex) in (item.entityDesc + '').split(
                        ','
                      )"
                      :key="imgindex"
                    />
                  </div>
                  <div>审核人: {{ item.apprUserNm }}</div>
                  <div>审核结果: {{ item.statNmCn }}</div>
                </div>
              </el-step>
            </template>
          </el-steps>
        </div>
      </el-dialog>
      <!-- 操作日志弹窗 -->
      <el-dialog
        v-if="show"
        :title="dialogTitle"
        :visible.sync="oprLogDialogVisible"
        width="75%"
      >
        <simple-table
          :tableHeader="simpleTableHeader"
          :tablePage="simpleTablePageList"
          :hasPagination="false"
        ></simple-table>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/approve";
import SimpleTable from "@/components/SimpleTable";

export default {
  name: "approveBar",
  props: {
    permission: {
      type: String,
      default: ""
    },
    approveInfo: {
      type: Object
    },
    rejectReasons: {
      type: Array
    }
  },
  components: {
    SimpleTable,
    tagsSelect: {
      template: `<el-row >
                    <el-col :sm="24" >
                        <el-input type="text" ref="input" placeholder="驳回原因"  v-model="tagVal" ></el-input>
                    </el-col>
                    <el-col :sm="24" style="border:1px solid #d6d6d6;border-radius:5px;padding:6px;margin-top:10px;">
                        <el-checkbox-group v-model="checkList">
                            <el-col v-for="(item,index) in rejectReasons" :key="item.index" class="checkboxe-label">
                                <el-checkbox   @change="checkedTag($event, item.reason)" :key="item.index"  :label="item.reason"></el-checkbox>
                            </el-col>
                        </el-checkbox-group>
                    </el-col>
                </el-row>
                `,
      props: {
        rejectReasons: {
          type: Array
        }
      },
      data() {
        return {
          tagVal: "",
          selected: [],
          checkList: []
        };
      },
      methods: {
        //选中驳回原因列表选项
        checkedTag(checked, tag) {
          if (checked) {
            this.selected.push(tag);
          } else {
            let index = this.selected.indexOf(tag);
            this.selected.splice(index, 1);
          }
          this.$emit("getreason", this.selected);
        }
      }
    }
  },
  created() {
    this.ipPk = this.approveInfo.entityPk;
    this.rejectReasonList = this.$props.rejectReasons;
  },
  watch: {
    rejectReasons: {
      handler(newName, oldName) {
        if (newName) {
          this.rejectReasonList = newName;
        }
      },
      deep: true
    }
  },
  data() {
    return {
      ipPk: "",
      reason: "",
      dialogVisible: false,
      oprLogDialogVisible: false,
      dialogTitle: "",
      btnLoading: false,
      btnLoading2: false,
      basicApproveLogList: [],
      rejectReasonList: [],
      simpleTablePageList: {
        list: [],
        currPage: 0,
        pageSize: 20,
        totalPage: 0
      },
      simpleTableHeader: [
        { name: "操作时间", field: "updTm" },
        { name: "操作内容", field: "opraItm" },
        { name: "操作详情", field: "opraInfo", imgBol: true }
      ]
    };
  },
  computed: {
    show() {
      let catCd = this.approveInfo.catCd;
      let type = {
        "8010.207": true,
        "8010.200": false,
        "8010.201": false,
        "8010.202": false,
        "8010.203": false,
        "8010.204": false,
        "8010.307": true,
        "8010.300": false,
        "8010.302": false,
        "8010.303": false,
        "8010.304": false,
        "8010.305": false,
        "8010.406": true,
        "8010.400": false,
        "8010.401": false,
        "8010.403": false,
        "8010.404": false,
        "8010.405": false,
        "8010.504": true,
        "8010.506": false,
        "8010.500": false,
        "8010.501": false
      };

      return type[catCd];
    },
    // 判断是否为移交审核人员
    // 针对车辆移交审核，审核人员需要审核基本信息、道路运输证及保险单
    isGovBsCatcdAudit(){
      let roleNameList = localStorage.getItem('roleName');
      let notNecessary = ['8010.301', '8010.302', '8010.303', '8010.305']

      if(roleNameList){
        roleNameList = localStorage.getItem('roleName').split(',');
        if(roleNameList.includes('gov_bscatcd_audit') && notNecessary.includes(this.approveInfo.catCd)){
          return false;
        }
      }
      return true
    }
  },
  methods: {
    getReason(payload) {
      this.reason = payload.join("；");
    },
    //审核日志
    basicApproveLog() {
      let _this = this;
      let param = {
        entityPk: _this.approveInfo.entityPk,
        catCd: _this.approveInfo.catCd
      };

      _this.btnLoading = true;
      new Promise(function(resolve, reject) {
        $http
          .approveLog(param)
          .then(response => {
            if (response.code == 0 && response.data.length) {
              resolve(response.data);
            } else {
              _this.$message("暂无审核日志");
            }
            _this.btnLoading = false;
          })
          .catch(error => {
            _this.btnLoading = false;
            reject(error);
          });
      })
        .then(result => {
          _this.dialogVisible = true;
          _this.basicApproveLogList = result;
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    approved() {
      let approveInfo = this.approveInfo;
      let _this = this;

      approveInfo.statCd = 1;
      approveInfo.desc = "";

      this.$confirm("你确定要通过审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$emit("getHandleStat", "approve");
          $http
            .approve(approveInfo)
            .then(response => {
              if (response.code == 0) {
                this.$message({
                  type: "success",
                  message: "审核操作成功"
                });
                _this.$emit("getPassStatus", {
                  handleFlag: "1"
                });
              } else {
                this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
                _this.$emit("getPassStatus", {
                  handleFlag: "fail"
                });
              }
            })
            .catch(error => {
              _this.$emit("getPassStatus", {
                handleFlag: "fail"
              });
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消审核操作"
          });
        });
    },
    rejective() {
      let approveInfo = this.approveInfo;
      let _this = this;
      let timeStamp = new Date().getTime();
      const vElm = this.$createElement;

      this.$msgbox({
        title: "请选择驳回原因",
        message: vElm("tagsSelect", {
          on: { getreason: this.getReason },
          ref: "selectMsgbox" + timeStamp,
          props: { rejectReasons: _this.rejectReasonList }
        }),
        customClass: "msgbox",
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose(action, instance, done) {
          let custReason =
            _this.$refs["selectMsgbox" + timeStamp] &&
            _this.$refs["selectMsgbox" + timeStamp].tagVal;
          if (action === "confirm") {
            if (!_this.reason && !custReason) {
              return _this.$message({
                type: "info",
                message: "驳回原因不能为空"
              });
            } else {
              if (_this.reason) {
                _this.reason += "；" + custReason;
              } else {
                _this.reason = custReason;
              }
              //清空选中的驳回理由
              _this.$refs["selectMsgbox" + timeStamp] &&
                ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
                (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
                (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
              done();
            }
          } else if (action === "cancel") {
            done();
          }
        }
      })
        .then(action => {
          //驳回审核操作
          this.$emit("getHandleStat", "reject");
          approveInfo.desc = _this.reason;
          approveInfo.statCd = 2;

          $http
            .approve(approveInfo)
            .then(response => {
              if (response.code == 0) {
                _this.$message({
                  type: "success",
                  message: "操作成功"
                });
                _this.$emit("getRjectStatus", {
                  handleFlag: "2",
                  remark: _this.reason
                });
              } else {
                _this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
                _this.$emit("getRjectStatus", {
                  handleFlag: "fail"
                });
              }
              _this.reason = "";
            })
            .catch(error => {
              _this.$message({
                type: "error",
                message: "操作失败"
              });
              _this.$emit("getRjectStatus", {
                handleFlag: "fail"
              });
              _this.reason = "";
            });
          //清空选中的驳回理由
          _this.$refs["selectMsgbox" + timeStamp] &&
            ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
            (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
            (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
        })
        .catch(error => {
          this.$message({
            type: "info",
            message: "已取消驳回操作"
          });
          //清空选中的驳回理由
          _this.$refs["selectMsgbox" + timeStamp] &&
            ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
            (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
            (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
          _this.reason = "";
        });
    },
    getApproveType(catCd) {
      catCd = catCd || this.approveInfo.catCd;
      let type = {
        "8010.207": "企业基本信息",
        "8010.200": "企业营业执照",
        "8010.201": "企业组织机构代码证",
        "8010.202": "企业税务登记证",
        "8010.203": "企业道路运输经营许可证",
        "8010.204": "企业安全责任承诺书",
        "8010.307": "车辆基本信息",
        "8010.300": "车辆道路运输证",
        "8010.302": "车辆行驶",
        "8010.303": "卫星定位终端安装证书",
        "8010.304": "道路危险货物承运人责任保险单",
        "8010.305": "车辆安全设备配备照片",
        "8010.406": "人员基本信息",
        "8010.400": "个人身份证",
        "8010.401": "劳动合同",
        "8010.403": "驾驶员从业资格",
        "8010.404": "押运员从业资格",
        "8010.405": "安全责任状",
        "8010.506": "罐体基本信息",
        "8010.500": "罐体检验报告",
        "8010.501": "压力罐容器登记证"
      };
      return type[catCd] ? type[catCd] + "审核日志" : "审核日志";
    },
    // 操作日志
    searchLog() {
      let _this = this;
      let pk = _this.approveInfo.entityPk;
      let catCd;
      //操作日志类型 catCd
      let logType = {
        "8010.406": { catCd: "8031.160", title: "人员信息操作日志" }, //人员
        "8010.307": { catCd: "8031.170", title: "车辆信息操作日志" }, //车辆
        "8010.207": { catCd: "8031.180", title: "企业信息操作日志" }, //企业
        "8010.504": { catCd: "8031.190", title: "罐体信息操作日志" } //罐体
      };
      let item = logType[_this.approveInfo.catCd];
      this.btnLoading2 = true;
      catCd = item.catCd;
      this.dialogTitle = item.title;
      this.oprLogDialogVisible = true;
      $http
        .oprLogInfo(pk, catCd)
        .then(res => {
          if (res.code == 0) {
            let opraLog = [];
            res.opraLog.forEach(item => {
              // if(item.type == 1){
              // let infoItem = item.opraInfo.split("||");
              // infoItem.forEach( info =>{
              //     let opt = /([^:]+):(\d{4}-\d{1,2}-\d{1,2}\s*\d{1,2}:\d{1,2}:\d{1,2}|[^:]+):(\d{4}-\d{1,2}-\d{1,2}\s*\d{1,2}:\d{1,2}:\d{1,2}|[^:]+)/.exec(info)
              //     opraLog.push({"updTm":item.updTm,"updBy":item.updBy,"opraItm":opt[1]||'',"opraBefor":opt[2]||'',"opraAfter":opt[3]||''})
              // })
              // }else{
              let opraInfo = item.opraInfo.split("||").join("<br/>");
              if (opraInfo.indexOf("https") > -1) {
                opraInfo = item.opraInfo.split("||");
                opraInfo = opraInfo.map(item => {
                  return item.split("$$");
                });
              } else {
                opraInfo = opraInfo.split("$$").join(":");
              }
              opraLog.push({
                updTm: item.updTm,
                opraItm: item.opraItm ? item.opraItm : "基础信息",
                opraInfo: opraInfo
              });
              // }
            });
            _this.simpleTablePageList.list = opraLog;
          } else {
            _this.$message.error(res.msg);
          }
          _this.btnLoading2 = false;
        })
        .catch(err => {
          console.log(err);
          _this.btnLoading2 = false;
        });
    },
    //查看图片
    previewPic(url) {
      Tool.previewPic.call(this, url);
    }
  }
};
</script>
<style>
.checkboxe-label {
  margin-bottom: 10px;
}
.msgbox {
  width: 640px;
}
.msgbox .el-tag {
  box-sizing: border-box;
  border-color: transparent;
  margin: 2px 0 2px 6px;
  background-color: #f0f2f5;
}
.msgbox .el-tag__close.el-icon-close {
  background-color: #c0c4cc;
  right: -7px;
  top: 0;
  color: #fff;
}
</style>
