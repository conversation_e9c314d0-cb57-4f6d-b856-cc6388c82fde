<template>
  <div>
    <el-popover trigger="hover" placement="top">
      <template v-for="(item, index) in resultArr">
        <p v-if="!item.hide" :class="['audit-info', item.auditStatus]" :key="item.licType + index">{{
          item.licType }}：{{ item.auditResult }}</p>
      </template>
      <div slot="reference" class="name-wrapper">
        <template v-for="(rcd, index) in resultCdArr">
          <!-- 证件简称统一处理 -->
          <!-- 排除驾驶员和押运员这两类特殊证件 -->
          <template
            v-if="!['8010.403', '8010.407', '8010.408', '8010.402', '8010.404', '8010.409', '8010.410'].includes(rcd.licCatCd) && licConfigObj[rcd.licCatCd]">
            <el-tag close-transition :type="rcd.auditStatus" :key="rcd + index"
              :class="[aiCdArr.includes(rcd.licCatCd) ? 'is-ai' : '']">{{
                licConfigObj[rcd.licCatCd] ? licConfigObj[rcd.licCatCd].shortName : "" }}</el-tag>
          </template>
        </template>
        <!-- 驾驶员相关证件特殊处理 -->
        <template v-if="isInArr(Object.keys(resultCdObj), ['8010.403', '8010.407', '8010.408', '8010.402'])">
          <span class="father-tag" style="white-space: nowrap">
            <el-tag type="info" close-transition>驾</el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.403')" :type="getType(resultCdObj['8010.403'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.403') ? 'is-ai' : '']">
              危
            </el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.407')" :type="getType(resultCdObj['8010.407'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.407') ? 'is-ai' : '']">
              毒
            </el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.408')" :type="getType(resultCdObj['8010.408'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.408') ? 'is-ai' : '']">
              爆
            </el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.402')" :type="getType(resultCdObj['8010.402'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.402') ? 'is-ai' : '']">
              驶
            </el-tag>
          </span>
        </template>
        <!-- 押运员相关证件特殊处理 -->
        <template v-if="isInArr(Object.keys(resultCdObj), ['8010.404', '8010.409', '8010.410'])">
          <span class="father-tag" style="white-space: nowrap">
            <el-tag type="info" close-transition>押</el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.404')" :type="getType(resultCdObj['8010.404'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.404') ? 'is-ai' : '']">
              危
            </el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.409')" :type="getType(resultCdObj['8010.409'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.409') ? 'is-ai' : '']">
              毒
            </el-tag>
            <el-tag v-if="Object.keys(resultCdObj).includes('8010.410')" :type="getType(resultCdObj['8010.410'])"
              close-transition :class="['children-tag', aiCdArr.includes('8010.410') ? 'is-ai' : '']">
              爆
            </el-tag>
          </span>
        </template>
      </div>
    </el-popover>
  </div>
</template>

<script>
const baseLicInfo = {
  "8010.207": { "licNm": "企业基础信息", "shortName": "基", "order": 1 },
  "8010.200": { "licNm": "企业营业执照", "shortName": "营", "order": 2 },
  "8010.203": { "licNm": "企业道路运输经营许可证", "shortName": "道", "order": 3 },
  // "8010.204": { "licNm": "企业安全责任承诺书", "shortName": "安", "order": 4 },
  "8010.204": { "licNm": "企业补充材料", "shortName": "补", "order": 4 },

  "8010.307": { "licNm": "车辆基础信息", "shortName": "基", "order": 1 },
  "8010.300": { "licNm": "车辆道路运输证", "shortName": "运", "order": 2 },
  "8010.301": { "licNm": "机动车登记证", "shortName": "机", "order": 3 },
  "8010.302": { "licNm": "车辆行驶证", "shortName": "行", "order": 4 },
  "8010.303": { "licNm": "卫星定位终端安装证书", "shortName": "卫", "order": 5 },
  "8010.304": { "licNm": "道路危险货物承运人责任保险单", "shortName": "险", "order": 6 },
  "8010.305": { "licNm": "车辆安全设备配备照片", "shortName": "安", "order": 7 },

  "8010.406": { "licNm": "人员基础信息", "shortName": "基", "order": 1 },
  "8010.400": { "licNm": "个人身份证", "shortName": "身", "order": 2 },
  "8010.402": { "licNm": "驾驶证", "shortName": "驾", "order": 3 },
  "8010.403": { "licNm": "驾驶员危运从业资格证", "shortName": "危", "order": 4 },
  "8010.408": { "licNm": "驾驶员爆炸品从业资格证", "shortName": "爆", "order": 5 },
  "8010.407": { "licNm": "驾驶员剧毒从业资格证", "shortName": "毒", "order": 6 },
  "8010.404": { "licNm": "押运员危运从业资格证", "shortName": "危", "order": 7 },
  "8010.410": { "licNm": "押运员爆炸品从业资格证", "shortName": "爆", "order": 8 },
  "8010.409": { "licNm": "押运员剧毒从业资格证", "shortName": "毒", "order": 9 },
  "8010.401": { "licNm": "劳动合同", "shortName": "劳", "order": 10 },
  "8010.405": { "licNm": "安全责任状", "shortName": "安", "order": 11 },

  "8010.504": { "licNm": "罐体基础信息", "shortName": "基", "order": 1 },
  "8010.508": { "licNm": "产品质量证明书", "shortName": "质", "order": 1 },
  "8010.506": { "licNm": "罐体合格证", "shortName": "合", "order": 1 },
  "8010.500": { "licNm": "罐体检验报告", "shortName": "检", "order": 1 },
  "8010.507": { "licNm": "安全合格铭牌", "shortName": "安", "order": 1 },

  "8010.509": { "licNm": "压力容器产品合格证", "shortName": "合", "order": 1 },
  "8010.510": { "licNm": "移动式压力容器电子铭牌", "shortName": "电", "order": 1 },
  "8010.501": { "licNm": "特种设备使用登记证", "shortName": "特", "order": 1 },
}
// import { getLicConfig } from "@/utils/getLicConfig";
// import { setStore, getStore, removeStore } from "@/utils/store";
export default {
  props: {
    // 证件类型
    licType: {
      type: String,
      required: true
    },
    result: {
      type: [String, Object],
      required: true
    },
    resultCd: {
      type: [String, Object],
      required: true
    },
    aiRemark: {
      type: [String, Object],
      required: true
    },
  },
  computed: {
    aiCdArr() {
      try {
        let result = this.aiRemark || {};
        // 若类型是object则不需要处理，若是string则需要转成对象进行处理
        if (typeof result === 'string') {
          if (result.length > 0) {
            result = JSON.parse(this.aiRemark);
          } else {
            result = {}
          }
        }
        return Object.keys(result).filter(key => {
          return result[key].indexOf('自动') >= 0 || result[key].indexOf('ai') >= 0;
          // return result[key];
        });
      } catch (error) {
        return [];
      }
    },
    resultObj() {
      try {
        let result = this.result || {};
        // 若类型是object则不需要处理，若是string则需要转成对象进行处理
        if (typeof result === 'string') {
          if (result.length > 0) {
            result = JSON.parse(this.result);
          } else {
            result = {}
          }
        }
        return result;
      } catch (error) {
        return {};
      }
    },
    resultArr() {
      let result = this.resultObj;
      if (!result) {
        return []
      }

      return Object.keys(result).map(key => {

        let ret = null;

        let val = result[key];
        ret = {
          licType: key,
          auditResult: val,
          auditStatus: val.indexOf('待审核') >= 0 ? 'warning' : (val.indexOf('审核通过') >= 0 ? 'success' : 'danger')  // warning:待受理，success：审核通过，danger：审核驳回
        }

        if (!this.isGovCheckLic && key == "企业补充材料") {
          ret.hide = true;
        }

        return ret;
      })
    },
    resultCdObj() {
      try {
        let resultCd = this.resultCd || {};
        // 若类型是object则不需要处理，若是string则需要转成对象进行处理
        if (typeof resultCd === 'string') {
          if (resultCd.length > 0) {
            resultCd = JSON.parse(this.resultCd);
          } else {
            resultCd = {}
          }
        }
        return resultCd;
      } catch (error) {
        return {};
      }
    },
    resultCdArr() {
      let resultCd = this.resultCdObj;
      if (!resultCd) {
        return []
      }
      return Object.keys(resultCd).map(key => {
        let val = resultCd[key];
        return {
          licCatCd: key,
          auditResult: val,  // 0:待受理，1：审核通过，2：审核驳回
          auditStatus: val === '0' ? 'warning' : (val === '1' ? 'success' : 'danger')  // warning:待受理，success：审核通过，danger：审核驳回
        }
      })
    },
    // 判断是否为证照审核人员
    isGovCheckLic() {
      let roleNameList = localStorage.getItem("roleName");
      if (roleNameList) {
        return roleNameList.split(',').includes('gov_check_lic')
      }
      return false
    }
  },
  created() {
    // 根据胡总要求，只有证照审核人员账号才显示企业补充材料
    if (!this.isGovCheckLic) {
      delete baseLicInfo['8010.204'];
    }
  },
  data() {
    return {
      licConfigObj: baseLicInfo
    }
  },
  // watch: {
  //   licType: {
  //     handler(val) {
  //       if (val) {
  //         this.getLicConfigByLicType(val)
  //       }
  //     },
  //     immediate: true,
  //   }
  // },
  methods: {
    // 判断数组中是否包含某个元素
    isInArr(fatherArr, sonArr) {
      let flag = false;
      for (let i = 0; i < sonArr.length; i++) {
        if (fatherArr.includes(sonArr[i])) {
          flag = true;
          break;
        }
      }
      return flag;
    },
    getType(auditVal) {
      return auditVal === '0' ? 'warning' : (auditVal === '1' ? 'success' : 'danger')  // warning:待受理，success：审核通过，danger：审核驳回
    },
    // 证件类型
    // { label: "运输企业", value: "2100.202.210" },
    // { label: "牵引车", value: "1180.154" },
    // { label: "挂车", value: "1180.155" },
    // { label: "驾驶员", value: "2100.205.150" },
    // { label: "押运员", value: "2100.205.190" },
    // { label: "驾驶员/押运员", value: "2100.205.191" },
    // { label: "常压罐", value: "1180.156.151" },
    // { label: "压力罐", value: "1180.156.150" },
    // { label: "通行证", value: "6015.230.170" },
    // { label: "危险品安全卡", value: "6015.240.190" },
    // async getLicConfigByLicType(type) {
    //   let catCd = []
    //   if (!type) {
    //     return this.$set(this, 'licConfigObj', {})
    //   } else if (type === 'entp') {
    //     catCd = ["2100.202.210"]
    //   } else if (type === 'vec') {
    //     catCd = ["1180.154", "1180.155"];
    //   } else if (type === 'pers') {
    //     catCd = ["2100.205.191"]
    //   } else if (type === 'tank') {
    //     catCd = ["1180.156.151", "1180.156.150"]
    //   }
    //   let storyName = "licConfigObj-" + type
    //   let store = getStore({ name: storyName }) || null
    //   if (store) {
    //     return this.$set(this, 'licConfigObj', store)
    //   }
    //   let obj = {};
    //   let self = this;
    //   let index = 1;
    //   let promiseAll = catCd.map(async cd => {
    //     return new Promise(resolve => {
    //       getLicConfig(cd).then(res => {
    //         if (res && res.length) {
    //           res.forEach(item => {
    //             let key = item.licCatCd;
    //             obj[key] = {
    //               licNm: item.licNm,
    //               shortName: item.shortName,
    //               order: index++
    //             }
    //           })
    //         }
    //         resolve();
    //       }).catch(e => { console.log(e); resolve(); });
    //     })
    //   })
    //   Promise.all(promiseAll).then(() => {
    //     console.log(JSON.stringify(obj))
    //     if (Object.keys(obj).length) {
    //       setStore({
    //         name: storyName,
    //         content: { ...baseLicInfo, ...obj },
    //         type: "session",
    //       });
    //       self.$set(self, 'licConfigObj', { ...baseLicInfo, ...obj })
    //     }
    //   })
    // }
  },
}
</script>

<style lang="scss" scoped>
.audit-info {
  &.warning {
    color: #e6a23c;
  }

  &.success {
    color: green;
  }

  &.danger {
    color: red;
  }
}

/deep/ {
  .el-tag {
    position: relative;

    &.children-tag {
      margin-right: 0px !important;
      margin-left: -5px !important;
      height: 20px !important;
      line-height: 10px !important;
      border-radius: 0 !important;
    }

    // &.children-tag {
    //   margin-right: 0px;
    //   margin-left: -5px;
    //   height: 20px !important;
    //   line-height: 20px;
    //   border-radius: 0;

    //   &:first-child {
    //     margin-right: 1px;
    //   }

    //   &:last-child {
    //     border-radius: 0 2px 2px 0;
    //     margin-right: 6px;
    //   }
    // }
    // 2024-07-29 根据胡总要求去除AI审核标识
    // &.is-ai {
    //   &::after {
    //     display: block;
    //     content: "AI";
    //     position: absolute;
    //     left: 50%;
    //     transform: translateX(-50%);
    //     top: -5px;
    //     width: 10px;
    //     height: 10px;
    //     border-radius: 4px;
    //     line-height: 8px;
    //     background: #d00;
    //     color: #fff;
    //     font-size: 8px;
    //     text-align: center;
    //   }
    // }
  }

  .father-tag {
    white-space: nowrap;
    margin-right: 5px;

    .el-tag:first-child {
      margin-right: 1px;
    }

    .children-tag:first-child {
      margin-right: 1px;
    }

    .children-tag:last-child {
      border-radius: 0 2px 2px 0;
      margin-right: 6px;
    }
  }

  .father-tag span:nth-child(2) {
    border-left: 0px;
  }
}
</style>
