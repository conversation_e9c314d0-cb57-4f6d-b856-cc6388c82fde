<template>
  <div>
    <el-input type="text" v-model="modelVal" laceholder="请标注经纬度" readonly>
      <el-button slot="append" type="primary" icon="el-icon-position" size="small" @click="init" class="custom-btn"></el-button>
    </el-input>
    <el-dialog title="点击地图设置位置" :close-on-click-modal="false" :visible.sync="visible" append-to-body @opened="showPoint">
      <el-alert title="操作步骤：1、点击地图设置点位，2、拖拽点位调整具体位置，3、点击提交按钮" type="warning" :closable="false"></el-alert>
      <div class="map-wrapper" :style="{height:mapHeight+'px'}">
        <div ref="mapContainer" class="map-container"></div>
        <div class="map-search-wape">
          <el-input v-model="searchInput" placeholder="搜索地名、路名、位置" size="small" style="width:170px;" @keyup.enter.native="showLocation"></el-input>
          <el-button type="primary" size="small" style="margin-left:10px;" @click="showLocation">查找</el-button>
          <el-button type="danger" size="small" style="margin-left:10px;" @click="del" v-show="pointMarker">删除定点</el-button>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible=false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  model: {
    prop: "modelVal",
    event: "input"
  },
  props: {
    modelVal: {
      type: String,
      default: ""
    },
    mapHeight: {
      type: Number,
      default: 350
    }
  },
  data() {
    return {
      visible: false,
      map: null,
      LocalSearch: null, // 地图检索
      searchInput: "", // 搜索地名、路名、位置
      drawingManager: null,
      pointMarker: null
    };
  },
  destroyed() {},
  methods: {
    init() {
      this.visible = true;
      this.$nextTick(function() {
        this._initBMap();
      });
    },
    _dragHandle(e) {
      this.map.removeOverlay(e.target.getLabel());
      let msg = "当前位置经纬度：" + e.point.lng + "," + e.point.lat;
      let label = new BMap.Label(msg, {
        offset: new BMap.Size(20, -10)
      });
      // label.setStyle({
      //   display: "none"
      // }); //对label 样式隐藏
      this.pointMarker.setLabel(label);
    },

    _initBMap() {
      let _this = this;
      if (!this.map) {
        let map = new BMap.Map(this.$refs.mapContainer); // 创建Map实例
        var mapType1 = new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP],
          anchor: BMAP_ANCHOR_TOP_LEFT
        });
        map.enableAutoResize(); //地图自适应容器宽高变化，手动启用
        map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
        map.addControl(mapType1); //添加地图类型控件
        // map.setDefaultCursor("url('bird.cur')");   //设置地图默认的鼠标指针样式
        this.map = map;
        this.map.addEventListener("click", function(e) {
          if (!_this.pointMarker) {
            // this.point = new BMap.Point(116.400244, 39.92556);
            let marker = new BMap.Marker(e.point); // 创建标注
            let msg = "当前位置经纬度：" + e.point.lng + "," + e.point.lat;
            let label = new BMap.Label(msg, {
              offset: new BMap.Size(20, -10)
            });
            marker.setLabel(label);
            _this.pointMarker = marker;
            _this.map.addOverlay(marker); // 将标注添加到地图中
            // marker.disableDragging();// 不可拖拽
            _this.pointMarker.enableDragging(); // 可拖拽
            _this.pointMarker.addEventListener("dragend", _this._dragHandle);
          }
        });
      }
    },
    showPoint() {
      let latAndLonStr = this.modelVal;
      this.clearMap();
      // this._initDrawing(this.map);
      if (latAndLonStr) {
        let pointlatAndLon = latAndLonStr.split(",");
        if (pointlatAndLon.length === 2) {
          let pointNode = new BMap.Point(pointlatAndLon[0], pointlatAndLon[1]);
          let marker = new BMap.Marker(pointNode); // 创建标注
          this.pointMarker = marker;
          this.map.addOverlay(marker); // 将标注添加到地图中
          this.pointMarker.enableDragging(); // 可拖拽
          this.pointMarker.addEventListener("dragend", this._dragHandle);
          this.map.centerAndZoom(pointNode, 13);
        }
      } else {
        let point = new BMap.Point(121.580251, 29.874588);
        this.map.centerAndZoom(point, 13);
      }
    },
    clearMap() {
      if (this.pointMarker) {
        this.pointMarker.removeEventListener("dragend");
        this.pointMarker = null;
      }
      if (this.map) {
        this.map.clearOverlays();
      }
    },

    // 定位
    showLocation() {
      if (!this.LocalSearch) {
        this.LocalSearch = new BMap.LocalSearch(this.map, {
          renderOptions: { map: this.map }
        });
      }
      this.LocalSearch.search(this.searchInput);
    },

    // 初始化绘制
    // _initDrawing(map) {
    //   let _this = this;
    //   if (!this.drawingManager) {
    //     var styleOptions = {
    //       strokeColor: "#d00", //边线颜色。
    //       fillColor: "#d00", //填充颜色。当参数为空时，圆形将没有填充效果。
    //       strokeWeight: 2, //边线的宽度，以像素为单位。
    //       strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
    //       fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
    //       strokeStyle: "solid" //边线的样式，solid或dashed。
    //     };
    //     //实例化鼠标绘制工具
    //     this.drawingManager = new BMapLib.DrawingManager(map, {
    //       isOpen: false, //是否开启绘制模式
    //       enableDrawingTool: true, //是否显示工具栏
    //       drawingToolOptions: {
    //         anchor: BMAP_ANCHOR_TOP_RIGHT, //位置
    //         offset: new BMap.Size(10, 10), //偏离值
    //         drawingModes: [
    //           BMAP_DRAWING_MARKER // 点
    //         ]
    //       },
    //       polygonOptions: styleOptions
    //     });
    //     this.drawingManager.addEventListener("overlaycomplete", function(e) {
    //       console.log(e);
    //       // let overlay = e.overlay;
    //       // // overlay.disableMassClear();
    //       // let path = overlay.getPath();

    //       // if (_this.overlay) {
    //       //   _this.map.removeOverlay(_this.overlay);
    //       // }
    //       // _this.overlay = overlay;
    //       // _this.path = JSON.stringify(path);
    //       // _this.closeDrawing();
    //       // _this.$message({
    //       //   message: "矩形绘制完成请开始查询",
    //       //   type: "success"
    //       // });
    //     });
    //   }
    //   if (this.drawingManager) {
    //     this.drawingManager.open();
    //     this.drawingManager.setDrawingMode(BMAP_DRAWING_MARKER);
    //   }
    // },
    // openDrawing() {
    //   if (this.overlay) {
    //     this.clearDrawing();
    //   }
    //   if (this.drawingManager) {
    //     this.drawingManager.open();
    //     this.drawingManager.setDrawingMode(BMAP_DRAWING_MARKER);
    //   } else {
    //     this._initDrawing(this.map);
    //   }
    // },
    // clearDrawing() {
    //   if (this.overlay) {
    //     this.$confirm("是否删除上次绘制的查询区域?", "提示", {
    //       confirmButtonText: "确定",
    //       cancelButtonText: "取消",
    //       type: "warning"
    //     })
    //       .then(() => {
    //         // this.overlay.enableMassClear();
    //         this.map.removeOverlay(this.overlay);
    //         this.clearOverlayTemporary();
    //         this.overlay = null;
    //         this.path = null;

    //         this.selectedVehicleOfClearCar = null;
    //         this.clearCardList = [];
    //       })
    //       .catch(() => {});
    //   }
    // },
    // closeDrawing() {
    //   if (this.drawingManager) {
    //     this.drawingManager.close();
    //   }
    // },
    del() {
      if (this.pointMarker) {
        this.$confirm("是否确定删除当前定点位置?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.clearMap();
          })
          .catch(() => {});
      }
    },
    submit() {
      if (this.pointMarker) {
        let position = this.pointMarker.getPosition();
        // this.modelVal = position.lng + "," + position.lat;
        this.$emit("input", position.lng + "," + position.lat);
        // this.$emit("modelChangeEvent", position.lng + "," + position.lat);
      } else {
        this.modelVal = "";
        this.$emit("input", "");
        // this.$emit("modelChangeEvent", "");
      }
      this.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.map-wrapper {
  width: 100%;
  position: relative;
  .map-container {
    height: 100%;
    width: 100%;
  }
  .map-search-wape {
    position: absolute;
    z-index: 10;
    left: 10px;
    top: 40px;
    font: bold 12px/1.3em arial, sans-serif;
    color: #fff;
  }
}
.el-button.custom-btn {
  background-color: #409eff;
  color: #fff;
}
</style>
