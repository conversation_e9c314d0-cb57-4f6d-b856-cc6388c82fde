<template>
  <div class="simple-bar">
    <!-- <button class="custom-btn" @click="goBack">
      <i class="el-icon-back"></i>
    </button> -->
    <button
      class="custom-btn"
      :class="{'active':currentMenu === index}"
      v-for="(item,index) in simpleBarItem"
      :key="index"
      @click="menuClickHandle(index,item)"
    >
      <span>{{item.name}}</span>
    </button>
  </div>
</template>
<script>
export default {
  props: {
    currentMenu: {
      type: Number,
    },
    simpleBarItem: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
    };
  },
  methods: {
    menuClickHandle(index, item) {
      this.$emit("clickHandle", item);
    },
  },
};
</script>
<style scoped>
.simple-bar {
    display: flex;
  width: 100%;

}
.custom-btn {
  display: inline-block;
  height: 28px;
  line-height: 28px;
  text-align: center;
  background: #08306A;
  flex: 1;
  font-size: 16px;
  font-family: 'shs-bold';
    border: none;
  -webkit-appearance: none;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  color: #fff;
}
.custom-btn.active {
  background: #104F9F;
  color: #fff;
}
.custom-btn:first-child{
  border-radius:4px 0 0 4px;
}
.custom-btn:last-child{
  border-radius:0 4px 4px 0;
}
.simple-bar svg {
  vertical-align: middle;
}
</style>
