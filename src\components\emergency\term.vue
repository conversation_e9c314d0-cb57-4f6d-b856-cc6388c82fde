<template>
  <div>
    <div class="term" :style="posiStyle">
      <div class="top"></div>
      <div class="cnt">
        <el-form
          ref="termForm"
          :model="termForm"
          :rules="rules"
          label-width="80px"
          style="font-size:12px"
        >
          <el-form-item label="事故定位">
            <el-radio-group v-model="termForm.type" @change="typeHandle()">
              <el-radio label="0">企业</el-radio>
              <el-radio label="1">车牌号</el-radio>
              <el-radio label="2">自动拾取</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="18">
              <template v-if="termForm.type == '0'">
                <el-form-item label="企业名称" :rules="$rulesFilter({required:true})">
                  <!-- <el-input v-model="termForm.entpnName" size="mini" placeholder="请输入企业名称"></el-input> -->
                  <el-select
                    v-model="termForm.name"
                    value-key="ipPk"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入企业名称"
                    :remote-method="getEntpList"
                    size="mini"
                    @change="handleEntpSelect"
                  >
                    <el-option
                      v-for="(item,index) in entpList"
                      :key="index"
                      :label="item.entpName"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '1'">
                <el-form-item label="车牌号" :rules="$rulesFilter({required:true})">
                  <el-select
                    v-model="termForm.name"
                    value-key="vecPk"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入车牌号"
                    :remote-method="getVecList"
                    size="mini"
                    @change="handleVecSelect"
                  >
                    <el-option
                      v-for="(item,index) in vecList"
                      :key="index"
                      :label="item.vecNo"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '0' || termForm.type == '1'">
                <el-form-item label="事故介质" :rules="$rulesFilter({required:true})">
                  <el-autocomplete
                    size="mini"
                    class="inline-input"
                    v-model="termForm.media"
                    :fetch-suggestions="queryChemSearch"
                    value-key="name"
                    placeholder="请输入货物名称"
                    @select="handleChemSelect"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      title="查看MSDS信息"
                      @click="showDetails()"
                    ></el-button>
                  </el-autocomplete>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '2'">
                <div style="color:#27d1ea;margin-left:40px;">注：双击地图拾取点</div>
                <el-form-item label="经度" :rules="$rulesFilter({required:true})">
                  <el-input v-model="termForm.lng" size="mini" placeholder="请输入经度" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="纬度" :rules="$rulesFilter({required:true})">
                  <el-input v-model="termForm.lat" size="mini" placeholder="请输入纬度" :disabled="true"></el-input>
                </el-form-item>
              </template>
              <el-form-item label="范围" :rules="$rulesFilter({required:true})">
                <el-select size="mini" v-model="termForm.distance" placeholder="请选择附近范围">
                  <el-option label="1km" value="1"></el-option>
                  <el-option label="3km" value="3"></el-option>
                  <el-option label="5km" value="5"></el-option>
                  <el-option label="10km" value="10"></el-option>
                  <el-option label="15km" value="15"></el-option>
                  <el-option label="20km" value="20"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div class="btn" @click="submit()">
                <img src="~@/assets/emergency-img/download.png" alt />
                <span>确定</span>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="btm"></div>
    </div>
  </div>
</template>
<script>
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import * as $http from "@/api/emergency";
import { getLastRtePlanByTracCd } from "@/api/rtePlan";
export default {
  props: {
    posiStyle: {
      type: Object,
      default: () => {
        return {
          position: "fixed",
          right: "20px",
          top: "60px",
        };
      },
    },
  },
  data() {
    return {
      termForm: {
        type: "0",
        name: "",
        media: "",
        distance: "1",
        lng: "",
        lat: "",
        pk: "",
      },
      entpList: [],
      vecList: [],
      chemList: [],
      chem: {},

      rules: {
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        media: [{ required: true, message: "请输入货物名称", trigger: "blur" }],
        distance: [
          { required: true, message: "请输入附近距离", trigger: "blur" },
        ],
      },
    };
  },
  mounted: function () {},
  methods: {
    //选择类型
    typeHandle(e) {
      this.termForm.media = "";
      this.termForm.name = "";
      this.termForm.lng = "";
      this.termForm.lat = "";
    },
    // 选择介质
    handleEntpSelect(item) {
      this.termForm.media = "";
      this.termForm.name = item.entpName;
      this.termForm.pk = item.ipPk;
      this.getChemList(item.entpName);
    },
    handleVecSelect(item) {
      this.termForm.name = item.vecNo;
      this.termForm.media = "";
      this.getChemList(item.ownedCompany);
      getLastRtePlanByTracCd(item.vecNo).then((response) => {
        if (response.code === 0 && response.data) {
          this.$set(this.chem, 'value', response.data.prodPk);
          this.termForm.media = response.data.goodsNm;
        }
      });
    },
    handleChemSelect(item) {
      this.chem = item || {};
      this.chemName = item.name;
    },
    // 从货物列表中查询
    queryChemSearch(queryString, cb) {
      // 调用 callback 返回建议列表的数据
      cb(this.queryByChemList(queryString));
    },
    queryByChemList(queryString) {
      return this.chemList
        .filter((item) => {
          return item.chem_nm.indexOf(queryString) > -1;
        })
        .map((item) => {
          return { name: item.chem_nm, value: item.prod_pk };
        });
    },
    showDetails() {
      if (this.chem.value) {
        this.$emit("messageDialog", this.chem.value);
      } else {
        this.$message({
          message: "请先选择企业/车辆，再关联事故介质",
          type: "error",
        });
      }
    },
    submit(bol) {
      // 目标事物
      if(!bol){
        this.$emit("loadStop");
      }
      if (this.termForm.type == 0) {
        //企业
        this.submitEntp();
      }
      if (this.termForm.type == 1) {
        //'车辆'
        this.submitVec();
      }
      if (this.termForm.type == 2) {
        this.submitPoint();
      }
    },
    //根据企业提交
    submitEntp() {
      if(!this.termForm.name) {
        return
      }
      let radius = Number(this.termForm.distance * 1000);
      let param = {
        entpname: this.termForm.name,
        radius: radius,
      };
      this.$refs.termForm.validate((valid) => {
        if (valid) {
          $http.getEntpnamepoi(param).then((response) => {
            if (response.code === 0 && response.data.lnglat) {
              this.$emit(
                "loadAreaEntp",
                response.data.lnglat,
                this.termForm.name,
                radius
              );
              this.$emit("nearbyVec", response.data.nearcar, radius);
              this.$emit("poiList", response.data.poi);
            } else {
              this.$message({
                message: "对不起，无法找到该企业",
                type: "error",
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //根据车辆提交
    submitVec() {
      if(!this.termForm.name) {
        return
      }
      let radius = Number(this.termForm.distance) * 1000;
      let param = {
        vecno: this.termForm.name,
        radius: radius,
      };
      this.$refs.termForm.validate((valid) => {
        if (valid) {
          $http.getVecnopoi(param).then((response) => {
            if (response.code === 0 && response.data.lnglat) {
              this.$emit(
                "loadAreaVec",
                response.data.lnglat,
                this.termForm.name,
                response.data.poi
              );
              this.$emit("nearbyVec", response.data.nearcar, radius);
              this.$emit("poiList", response.data.poi);
            } else {
              this.$message({
                message: "对不起，无法找到该车辆",
                type: "error",
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //根据坐标提交
    submitPoint() {
      let lng = this.termForm.lng;
      let lat = this.termForm.lat;
      this.$emit("addPonit", lng, lat);
      let radius = Number(this.termForm.distance) * 1000;
      let param = {
        lng: lng,
        lat: lat,
        radius: radius,
      };
      this.$refs.termForm.validate((valid) => {
        if (valid) {
          $http.getpoi(param).then((response) => {
            if (response.code === 0) {
              this.$emit("poiList", response.data.poi);
              this.$emit("nearbyVec", response.data.nearcar, radius);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //获取企业列表
    getEntpList: function (name) {
      if (name) {
        $http
          .getEntpList({
            filters: {
              groupOp: "AND",
              rules: [
                { field: "cat_cd", op: "eq", data: "2100.185.150.150" },
                { field: "entp_name", op: "cn", data: name },
              ],
            },
            page: 1,
            limit: 999,
          })
          .then((response) => {
            if (response.code === 0) {
              this.entpList = response.page.list;
            } else {
              this.entpList.length = 0;
            }
          });
      }
    },
    //获取车辆列表
    getVecList: function (name) {
      if (name.length > 1) {
        $http
          .getVecList({
            filters: {
              groupOp: "AND",
              rules: [{ field: "vec_no", op: "cn", data: name }],
            },
            page: 1,
            limit: 999,
          })
          .then((response) => {
            if (response.code === 0) {
              let list = [];
              response.page.list.forEach((item) => {
                if (item.vecNo.indexOf("挂") < 0) {
                  list.push(item);
                }
              });
              this.vecList = list;
            } else {
              this.vecList.length = 0;
            }
          });
      }
    },
    //获取货物列表
    getChemList: function (entpname) {
      $http.getEnchList(entpname).then((response) => {
        if (response.code === 0) {
          this.chemList = response.data;
        } else {
          this.chemList.length = 0;
        }
      });
    },
  },
};
</script>
<style scoped>
.term {
  width: 320px;
  z-index: 1000;
  padding: 10px;
}
.el-form-item__label {
  font-size: 12px !important;
}
.term .top,
.term .btm {
  width: 320px;
  height: 3px;
}
.term .top {
  background: url("~@/assets/emergency-img/panel_box_top.png");
  background-size: 100% 100%;
}
.term .btm {
  background: url("~@/assets/emergency-img/panel_box_btm.png");
  background-size: 100% 100%;
}
.term .cnt {
  width: 320px;
  background: #102556;
  border-left: solid 1px #073252;
  border-right: solid 1px #073252;
  position: relative;
}
.term .btn {
  background: url("~@/assets/emergency-img/btn_bg3.png");
  width: 50px;
  height: 80px;
  float: left;
  cursor: pointer;
  margin: 6px 0 0 2px;
}
.term .btn img {
  display: block;
  margin: 10px auto 6px;
}
.term .btn span {
  display: block;
  width: 100%;
  text-align: center;
  color: #27d1ea;
  font-size: 12px;
}
</style>
<style lang="scss">
.term {
  .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 8px;
    border: 1px solid #4b72a9;
    background-color: #0a264e;
    color: #27d1ea;
  }
  .maplibTc {
    z-index: 2000;
  }
  .tdt-touch .tdt-control-copyright {
    display: none;
  }
}
</style>
