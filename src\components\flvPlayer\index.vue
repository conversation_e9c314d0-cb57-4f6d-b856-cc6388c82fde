<template>
  <div>
    <slot name="prepend"></slot>
    <div style="position:relative;text-align: center;background: #212224;">
      <template v-if="dataSource.isOnLine === '在线'">
        <!--添加class vjs-fluid 则视频自适应宽高  -->
        <div class="video-wrapper" v-show="isShowVideo" :style="{ width: '100%', height: posterHeight + 'px'}">
          <video :class="[isLoading ? 'loading' : '']" class="prism-player" :ref="playerId" controls autoplay :height="posterHeight"
                 :width="videoWidth"></video>
          <div v-show="videoMaskLoading" v-loading="videoMaskLoading"
               :style="{ width: posterWidth + 'px', height: posterHeight + 'px', position: 'absolute', background: '#000' }">
          </div>
        </div>
        <div v-show="!isShowVideo"
             :style="{ width: '100%', height: posterHeight + 'px', margin: '0 auto', 'background-color': '#212224',overflow:'hidden'}">
          <img :src="dataSource.playerOption.poster" :style="'height:'+posterHeight + 'px;'" style="maxWidth:100%;" />
          <div class="video-play-btn-wape" title="点击播放" @click="loadVideoPlayer">
            <svg-icon icon-class="play" class-name="video-play-btn"></svg-icon>
          </div>
        </div>
      </template>
      <div v-else :style="{ width: '100%', height: posterHeight + 'px',overflow:'hidden'}">
<!--        <img src="static/img/video-img/video-offline.jpg" :style="{height:posterHeight + 'px',maxWidth:'100%'}" />-->
        <img :src="dataSource.playerOption.poster" :style="{height:posterHeight + 'px',maxWidth:'100%'}" />
      </div>
    </div>
    <slot name="append"></slot>
  </div>
</template>

<script>
  import * as $http from "@/api/video";
  import * as $dashboard from "@/api/dashboard.js";
  export default {
    name: "flvPlayer",
    props: {
      //加载动画状态
      loadingSpinner:{
        type:Boolean,
        default: true
      },
      dataSource: {
        type: Object,
      },
      API: {
        type: [Object],
        default() {
          return $http;
        },
      },
      // ajax接口名
      ajaxName: {
        type: String,
        default: "getLoadAndUnloadVideoStreamUrl",
      },
      // 视频流ajax返回的数据的属性名称
      getStreamDataFieldName: {
        type: String,
        default: "url",
      },
      posterHeight: {
        type: Number,
        default: 260,
      },
      posterWidth: {
        type: Number,
        default: 356,
      },
      realVideoHeight: {
        type: Number,
      },
      realVideoWidth: {
        type: Number,
      },
    },
    computed: {
      videoHeight() {
        let height = this.realVideoHeight || this.posterHeight;
        return height;
      },
      videoWidth() {
        let width = this.realVideoWidth || "auto";
        return width;
      },
    },
    data() {
      return {
        playerId: "J_prismPlayer" + new Date().getTime(),
        videoMaskLoading: false,
        // posterWidth: 356,
        // posterHeight: 260,
        videoPlayer: null,
        playerOption: {
          id: "",
          url: "",
          cover: "",
        },
        isShowVideo: false,
        isLoading:false
      };
    },
    created() {},
    methods: {
      loadVideoPlayer() {
        let _this = this;
        if (this.dataSource.number != null) {
          this.playerOption.id = this.playerId;
          this.playerOption.cover = this.dataSource.playerOption.poster;
          //录播 mp4
          if (this.dataSource.videoUrl) {
            this.playerOption.url = this.dataSource.videoUrl;
            this.isShowVideo = true;
            this.initVideoPlayer();
            return;
          }
          //直播 flv
          if (this.dataSource.streamingAddress) {
            this.playerOption.url = this.dataSource.streamingAddress;
            this.isShowVideo = true;
            this.initVideoPlayer();
            return;
          }
          //直播(兼容旧版)
          this.API[this.ajaxName](_this.dataSource.number, 5)
            .then((res) => {
              if (res && res.code == 0) {
                _this.playerOption.url = res[_this.getStreamDataFieldName];
                _this.isShowVideo = true;
                _this.initVideoPlayer();
              } else {
                _this.$message.error("视频数据地址获取失败，请联系管理员xxx");
              }
            })
            .catch((error) => {
              console.log(error);
            });
        }
      },

      initVideoPlayer() {
        let _this = this;
        let videoType = this.dataSource.videoUrl ? "mp4" : "flv";
        if (!this.videoPlayer) {
          this.videoPlayer = flvjs.createPlayer({
            type: videoType,
            url: this.playerOption.url,
          });
          this.videoPlayer.attachMediaElement(this.$refs[this.playerId]);
          if(this.loadingSpinner === false){
            // waiting：视频加载等待。当视频由于需要缓冲下一帧而停止，等待时触发
            this.$refs[this.playerId].addEventListener('waiting', (e) => {
              _this.isLoading = true
            })
            // canplaythrough：可流畅播放。当浏览器预计能够在不停下来进行缓冲的情况下持续播放指定的音频/视频时触发
            this.$refs[this.playerId].addEventListener('canplaythrough', (e) => {
              _this.isLoading = false
            })
          }
          this.videoPlayer.load();
          this.videoPlayer.play();
        } else {
          this.onPlayerPlay();
        }
      },

      onPlayerPlay(event) {
        this.videoPlayer.play();
      },

      onPlayerPause(event) {
        this.videoPlayer.pause();
      },
      destroyPlayer() {
        let player = this.videoPlayer;
        if (player) {
          player.pause();
          player.unload();
          player.detachMediaElement();
          player.destroy();
          this.videoPlayer = null;
        }
      },
    },
    beforeDestroy() {
      let player = this.videoPlayer;
      if (player) {
        player.pause();
        player.unload();
        player.detachMediaElement();
        player.destroy();
        this.videoPlayer = null;
      }
    },
  };
</script>

<style lang="scss" scoped>
  .video-play-btn-wape {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -30px;
    margin-left: -30px;
    display: inline-block;

    .video-play-btn {
      font-size: 60px;
      color: #f5f5f5;
      cursor: pointer;

      &:hover {
        // color: #00aeff;
        color: #fff;
      }
    }
  }

  .video-wrapper {
    // width: 100%;
    margin: 0 auto;
    overflow: hidden;
    text-align: center;
    // background-color: #f8f8f8;
    background-color: #212224;

    .prism-player {
      max-width: 100%;
    }
  }

  video.loading::-webkit-media-controls { /* Works only on Chrome-based browsers */
    display: none;
  }
</style>
