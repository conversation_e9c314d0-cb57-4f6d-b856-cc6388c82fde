<template>
  <div :class="'container-' + size">
    <el-tag :key="tag" v-for="tag in currentVal" :closable="edit" :disable-transitions="false" @close="handleClose(tag)"
      :size="size">
      {{ tag }}
    </el-tag>
    <span v-if="edit">
      <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" :size="size"
        @keyup.enter.native="handleInputAdd" @blur="handleInputConfirm"></el-input>
      <el-button v-else class="button-new-tag" :size="size" type="primary" @click="showInput">+ 新增{{ placeholder
      }}</el-button>
    </span>
  </div>
</template>

<script>
export default {
  name: "formTag",
  model: {
    prop: "modelVal",
    event: "modelChangeEvent",
  },
  props: {
    modelVal: {
      type: [Array, String],
    },
    separator: {
      type: String,
      default: ",",
    },
    size: {
      type: String,
      default: "mini",
    },
    placeholder: {
      type: String,
      default: "",
    },
    edit: {
      type: Boolean,
      default: true,
    },
    valType: {
      type: String,
      default: "string",
      validator: function (value) {
        return ["string", "array"].includes(value);
      }
    }
  },
  computed: {
    currentVal() {
      let res = [];
      let modelVal = this.modelVal;
      if (modelVal && modelVal.length) {
        if (Object.prototype.toString.apply(modelVal) === "[object Array]") {
          res = [...modelVal];
        } else if (Object.prototype.toString.apply(modelVal) === "[object String]") {
          res = modelVal.split(this.separator);
        }
      }
      return res;
    },
  },
  data() {
    return {
      loading: false,
      inputVisible: false,
      inputValue: "",
    };
  },
  methods: {
    // 向父组件提交证件修改信息，触发父组件方法
    modifyHandle(dataArr) {
      let valType = this.valType;
      let modelVal = this.modelVal || null;
      if ((modelVal && Object.prototype.toString.apply(modelVal) === "[object Array]") || (valType && valType === "array")) {
        this.$emit("change", dataArr);
        this.$emit("modelChangeEvent", dataArr);
      } else if ((modelVal && Object.prototype.toString.apply(modelVal) === "[object String]") || (valType && valType === "string")) {
        this.$emit("change", dataArr.join(this.separator));
        this.$emit("modelChangeEvent", dataArr.join(this.separator));
      }
    },
    handleClose(tag) {
      this.$confirm(`您确认删除 ${tag} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        beforeClose(action, instance, done) {
          if (action == "confirm") {
            instance.$refs["confirm"].$el.onclick = a();
            function a(e) {
              e = e || window.event;
              if (e.detail != 0) {
                done();
              }
            }
          } else {
            done();
          }
        },
      })
        .then(() => {
          let modelVal = this.currentVal;
          modelVal.splice(modelVal.indexOf(tag), 1);
          this.modifyHandle(modelVal);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputAdd() {
      let inputValue = this.inputValue;
      if (inputValue) {
        let res = new Set(this.currentVal);
        res.add(inputValue);
        let modelVal = Array.from(res);
        this.modifyHandle(modelVal);
      }
      this.inputValue = "";
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        let res = new Set(this.currentVal);
        res.add(inputValue);
        let modelVal = Array.from(res);
        this.modifyHandle(modelVal);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  &-mini {
    line-height: 28px;
  }

  &-small {
    line-height: 30px;
  }

  &-middle {
    line-height: 36px;
  }

  &-medium {
    line-height: 36px;
  }
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;

  &.el-button--mini {
    padding: 4px 15px;
  }

  &.el-button--small {
    padding: 5px 15px;
  }

  &.el-button--middle {
    padding: 8px 20px;
  }

  &.el-button--medium {
    padding: 8px 20px;
  }
}

.input-new-tag {
  width: 100px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
