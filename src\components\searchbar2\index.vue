<template>
  <!--
    desc：搜索栏
    author：gsj
    @lastestUpdateDate 2023-02-09 实现组件双向绑定，组件外部可通过change改变搜索栏的其他项的设置

    使用方法：
    searchItems下的数组对象传参：
    {
      name: "企业名称",            // (必填)
      field: "entpName",          // (必填)
      type: "text",               // (必填)
      dbfield: "entp_name",       // (必填)
      dboper: "cn",               // (必填)

      default:"",                 // (选填)默认的初始值
      options:[],                 // (必填 or 选填)type是单选或者多选的则【必填】,注意options内的value必须是字符串，不能是数组或者对象或者number类型
      api:function(){},           // (必填 or 选填)更新触发的回调函数，type:fuzzy【el-autocomplete】模糊搜索,selectSearch【el-select】下拉远程搜索时需要
      allowCreate:function(){},   // (必填 or 选填),type:selectSearch【el-select】下拉远程搜索组件需要自建条目时需要
      valueFormat:string,         // (必填 or 选填),年月日组件【el-date-picker】需要格式化日期

      slot:true,                  // (选填)是否开启插槽，默认关闭
      slotName:"",                // (选填)不填默认插槽名称是field名称，填写上可以选择自定义插槽名称
      change:function(field, option, data){},        // (选填)更新触发的回调函数
      placeholder:"",             // (选填)提示信息
    }

    当type：select,radio,checkbox时即options存在时，且postdifferent为true时，则dboper为options内的dboper,data为dbvalue
    例如：option内：dboper为后端的dboper,dbvalue为后端接收的数据
    {
      name: "状态",
      field: "xx",
      type: "select",
      dbfield: "xx",
      postdifferent: true,
      options: [
        { label: "aa", value: "aa", dboper: "in", dbvalue:[1,2]},
        { label: "bb", value: "bb", dboper: "eq", dbvalue:3},
      ],
    },
   -->
  <div class="grid-searchbar clearfix">
    <el-form :model="searchForm" @submit.native.prevent ref="searchForm" :size="size" :inline="true">
      <template v-if="searchItems.normal">
        <template v-for="(item, index) in searchItems.normal">
          <el-form-item :prop="item.field" :label="item.name + ' '" :key="'normal-' + item.field + index">
            <slot v-if="item.slot" :name="item.slotName || item.field" :field="item.field" :option="item"
              :done="outsideFormItemChange.bind(null, item.field, item)"></slot>
            <dc-form-item v-else v-model="searchForm[item.field]" :type="item.type" :config="item"
              @change="insideFormItemChange"></dc-form-item>
          </el-form-item>
        </template>
      </template>
      <template v-if="searchItems.more">
        <span v-for="(item, index) in searchItems.more" :key="item.field + index" v-show="showMoreFlag">
          <el-form-item :prop="item.field" :label="item.name + ' '" :key="'more-' + item.field + index">
            <slot v-if="item.slot" :name="item.slotName || item.field + 'More' || item.field" :field="item.field"
              :option="item" :done="outsideFormItemChange.bind(null, item.field, item)"></slot>
            <dc-form-item v-else v-model="searchForm[item.field]" :type="item.type" :config="item"
              @change="insideFormItemChange"></dc-form-item>
          </el-form-item>
        </span>
      </template>
      <template>
        <div style="float: right; line-height: 2;margin-bottom: 5px;">
          <a class="show-more-btn" href="javascript:void(0)" @click="toggleShowMore"
            v-if="searchItems.more && searchItems.more.length">
            <span v-if="!showMoreFlag">
              更多
              <span class="triangle-down"></span>
            </span>
            <span v-else>
              收起
              <span class="triangle-up"></span>
            </span>
          </a>
          <el-button type="primary" icon="el-icon-search" :size="size" @click="search()">查询</el-button>
          <el-button icon="el-icon-delete" :size="size" @click="reset">重置</el-button>
          <slot name="button" />
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
import dcFormItem from "./components/dcFormItem";
import debounce from "lodash/debounce";
export default {
  model: {
    prop: "modelVal",
    event: "modelChangeEvent",
  },
  props: {
    size: {
      type: String,
      default: "small",
      validator: function (value) {
        return ["medium", "small", "mini"].indexOf(value) !== -1;
      },
    },
    modelVal: {
      type: Object,
      default() {
        return {};
      },
    },
    // 表单元素大小
    searchItems: {
      type: Object,
      default() {
        return {
          normal: [], // 常规搜索
          more: [], // 隐藏搜索
        };
      },
    },
    // 分页信息
    pagination: {
      // table的分页数据，用于点击搜索是url带上分页数据
      type: Object,
      validator: function (value) {
        if (value === null || value === undefined) {
          return true;
        } else {
          // 若以下属性不存在，则不符合要求
          // let notHave = ["pageNo", "pageSize"].filter(key => {
          //   return !value.hasOwnProperty(key);
          // });
          // return !notHave.length;
          return (value.hasOwnProperty("pageNo") && value.hasOwnProperty("pageSize")) || (value.hasOwnProperty("page") && value.hasOwnProperty("limit"));
        }
      },
    },
    // 是否开启搜索时同步更新url，默认开启
    isUpdateUrl: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    dcFormItem,
  },
  computed: {
    searchForm: {
      get() {
        return this.modelVal;
      },
      set(e) {
        this.$emit("modelChangeEvent", e);
      },
    },
    pageNo() {
      return this.pagination.pageNo || this.pagination.page;
    },
    pageSize() {
      return this.pagination.pageSize || this.pagination.limit;
    },
    allSearchItems() {
      let normal = this.searchItems.normal || [];
      let more = this.searchItems.more || [];
      return [...normal, ...more];
    },
  },
  watch: {
    pagination: {
      handler() {
        this._updateUrl(); // 更新url
      },
      deep: true,
    },
    searchItems: {
      handler: debounce(
        function () {
          if (this.queryCache) {
            let keys = Object.keys(this.queryCache);
            if (keys.length) {
              this._setQuery(this.queryCache, true);
            }
          }
        },
        500,
        { leading: false, trailing: true }
      ),
      deep: true,
    },
  },
  data() {
    return {
      showMoreFlag: false, // 是否显示更多查询项
      searchObj: [], // 前端的查询条件，里面的field对应前端的字段
      rules: [], // 后端的查询条件，里面的field对应后端的字段
      queryCache: {}, // 用于缓存搜索的数据
    };
  },
  created() {
    this._initData(); // 初始化属性
  },
  activated() {
    this.showMoreFlag = false;
  },
  deactivated() { },
  methods: {
    // 判断是否是对象
    isObject(obj) {
      return typeof obj === "object" && obj !== null;
    },
    // 判断2个变量是否相等
    isEqual(a, b) {
      let _this = this;
      if (a === b) return true;
      let isObjectA = this.isObject(a);
      let isObjectB = this.isObject(b);
      if (isObjectA && isObjectB) {
        try {
          let isArrayA = Array.isArray(a);
          let isArrayB = Array.isArray(b);
          if (isArrayA && isArrayB) {
            // a b都是数组
            return a.length === b.length && a.every((el, index) => _this.isEqual(el, b[index]));
          } else if (a instanceof Date && b instanceof Date) {
            // a b都是Date对象
            return a.getTime() === b.getTime();
          } else if (!isArrayA && !isArrayB) {
            // 此时a b都是纯对象
            let keyA = Object.keys(a);
            let keyB = Object.keys(b);
            return keyA.length === keyB.length && keyA.every(key => _this.isEqual(a[key], b[key]));
          } else {
            return false;
          }
        } catch (e) {
          console.log(e);
          return false;
        }
      } else if (!isObjectA && !isObjectB) {
        // a b 可能是string，number，boolean，undefined中的一种
        return String(a) === String(b);
      } else {
        return false;
      }
    },
    // btn显示更多
    toggleShowMore() {
      this.showMoreFlag = !this.showMoreFlag;
      this.$emit("resize");
    },
    // 时间格式化
    formatDate(time, cFormat) {
      if (arguments.length === 0) {
        return null;
      }
      if (!time) {
        return "";
      }

      let fmt = cFormat || "yyyy-MM-dd HH:mm:ss";

      let date;
      if (typeof time === "object") {
        date = time;
      } else if (typeof time === "string") {
        date = new Date(time);
      } else {
        date = new Date(parseInt(time));
      }

      let o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, //小时
        "H+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds(), //毫秒
      };
      let week = {
        0: "\u65e5",
        1: "\u4e00",
        2: "\u4e8c",
        3: "\u4e09",
        4: "\u56db",
        5: "\u4e94",
        6: "\u516d",
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      }
      if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? (RegExp.$1.length > 2 ? "\u661f\u671f" : "\u5468") : "") + week[date.getDay() + ""]);
      }
      for (let k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
        }
      }
      return fmt;
    },
    /**
     * 判空
     * @param {*} val
     * @return true or false
     */
    _checkEmpty(val) {
      return val === null || val === undefined;
    },
    /**
     * 设置searchForm的属性值(若itemVal是空的，则默认赋default或者置空)
     * @param {object} item searchItem的normal或more数据中的对象
     * @param {*} itemVal 赋值,若为undefined则设置为默认值
     *
     * @return 返回赋值的结果值
     */
    _setSearchFormVal(item, itemVal) {
      let field = item.field;
      let type = item.type;
      let isEmpty = itemVal === undefined;
      let output;
      if (item.slot) {
        output = this.nullish(itemVal,item.default || "");
      } else {
        if (type === "select" || type === "radio" || type === "checkbox") {
          // 需要判断值是否在options里
          if (item.options && item.options.length && itemVal !== null) {
            if (isEmpty) {
              output = item.default || "";
            } else {
              // 若是这些类型，则需要判断是否存在下拉框或者是否该值在options里面
              let val = decodeURIComponent(itemVal);
              let selectedOption = item.options.filter(it => {
                if (val && (typeof val === "string")) {
                  return (it.value === val) || ("" + it.value) === val;
                } else {
                  return (it.value === val);
                }
              });
              if (selectedOption.length) {
                output = selectedOption[0].value;
              } else {
                // 说明不在options里面
                output = "";
              }
            }
          } else {
            output = "";
          }
        } else if (type === "daterange" || type === "monthrange") {
          // 时间间隔
          if (isEmpty) {
            output = item.default && item.default.length == 2 && item.default[0] && item.default[1] ? [this.formatDate(item.default[0], "yyyy-MM-dd") + " 00:00:00", this.formatDate(item.default[1], "yyyy-MM-dd") + " 23:59:59"] : "";
          } else {
            if (itemVal && itemVal.length) {
              let val = decodeURIComponent(itemVal);
              let valArr = val.split(",");
              if (valArr.length == 2) {
                let startDate = valArr[0].trim();
                let endDate = valArr[0].trim();
                if (type === "daterange") {
                  let lPattern = /^([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/;
                  let sPattern = /^([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))$/;
                  if (!lPattern.test(startDate)) {
                    if (sPattern.test(startDate)) {
                      startDate = valArr[0] + " 00:00:00";
                    } else {
                      startDate = this.formatDate(valArr[0], (item.valueFormat || "yyyy-MM-dd HH:mm:ss"));
                    }
                  } else {
                    startDate = this.formatDate(valArr[0], (item.valueFormat || "yyyy-MM-dd HH:mm:ss"));
                  }
                  if (!lPattern.test(endDate)) {
                    if (sPattern.test(endDate)) {
                      endDate = valArr[1] + " 00:00:00";
                    } else {
                      endDate = this.formatDate(valArr[1], (item.valueFormat || "yyyy-MM-dd HH:mm:ss"));
                    }
                  } else {
                    endDate = this.formatDate(valArr[1], (item.valueFormat || "yyyy-MM-dd HH:mm:ss"));
                  }
                }
                // output = [this.formatDate(valArr[0], "yyyy-MM-dd") + " 00:00:00", this.formatDate(valArr[1], "yyyy-MM-dd") + " 23:59:59"];
                output = [startDate, endDate];
              } else {
                output = "";
              }
            } else {
              output = "";
            }
          }
        } else {
          output = this.nullish(itemVal,item.default || "");;
        }
      }
      if (output && output.trim) {
        output = output.trim(); // 前后去空
      }
      this.$set(this.searchForm, field, output);
      return output;
    },
    /**
     * 获取searchForm的属性值
     * @param {object} item searchItem的normal或more数据中的对象
     * @return null or {field,dbfield,op,data,dbdata}
     */
    _getSearchFormVal(item) {
      let field = item.field;
      let type = item.type;
      let data = this.nullish(this.searchForm[field],"");
      let dbdata = data;
      let dboper = item.dboper;
      // postdifferent为true的，则前端url显示的值和传给后端搜索的值是不同的，dboper也不同
      if (item.postdifferent && (type === "select" || type === "radio" || type === "checkbox")) {
        if (data && item.options && item.options.length) {
          let selectedOption = [];
          selectedOption = item.options.filter(it => {
            if (data && (typeof data === "string")) {
              return (it.value === data) || ("" + it.value) === data;
            } else {
              return (it.value === data);
            }
          });
          if (selectedOption.length) {
            dboper = selectedOption[0].dboper || item.dboper;
            dbdata = selectedOption[0].dbvalue || selectedOption[0].postData || selectedOption[0].value || "";
          }
        }
      }
      if (data !== null && data !== undefined) {
        if (typeof data === "string") {
          data = data.trim();
          data = data.replace(/\s/g, "");
        }
        if ((typeof data === "string" && data.length === 0) || (Array.isArray(data) && data.length === 0)) {
          return null;
        }
        return {
          field: item.field,
          dbfield: item.dbfield,
          op: dboper,
          data: data,
          dbdata: dbdata,
        };
      } else {
        return null;
      }
    },
    /**
     * 通过传入query给searchForm赋值
     * @param {Object} query 搜索的数据
     */
    _setQuery(query) {
      let _this = this;
      let itemsArr = this.allSearchItems;
      if (!itemsArr.length) {
        // 搜索条件不存在
        return;
      }
      this.$set(this, "queryCache", query);
      if (query && Object.keys(query).length > 0) {
        itemsArr.forEach(it => {
          let key = it.field;
          let val = query[key];
          if (val !== undefined) {
            let temp = this.nullish(query[key],null);
            _this._setSearchFormVal(it, temp); // searchForm赋予值
          } else {
            _this._setSearchFormVal(it, null); // searchForm赋予值
          }
        });
        // this.showMoreFlag = true;
      }
    },
    // 初始化属性
    _initData() {
      let _this = this;
      let itemsArr = this.allSearchItems;
      let searchModel = {}; // slot插槽的值
      if (itemsArr.length) {
        // 重新赋值
        itemsArr.forEach(item => {
          searchModel[item.field] = _this._setSearchFormVal(item);
        });
      }
      // this.$emit("modelChangeEvent", searchModel); // v-model赋值
    },
    // 将数组转成url参数
    _urlParams2Str(obj) {
      if (!obj) {
        return "";
      }
      let keys = Object.keys(obj || {});
      let str = window.location.href,
        i = str.lastIndexOf("?");
      let newStr = null;
      if (i >= 0) {
        newStr = str.slice(0, i);
      } else {
        newStr = str;
      }
      if (keys.length) {
        newStr = newStr + "?";
        keys.forEach(key => {
          newStr += key + "=" + encodeURIComponent(obj[key]) + "&";
        });
        newStr = newStr.slice(0, newStr.length - 1);
      }

      if (this.pagination) {
        // 存放分页数据
        if (keys.length) {
          newStr = newStr + "&pageNo" + "=" + (parseInt(this.pageNo) || 1);
          newStr = newStr + "&pageSize" + "=" + (parseInt(this.pageSize) || 20);
        } else {
          newStr = newStr + "?";
          newStr = newStr + "pageNo" + "=" + (parseInt(this.pageNo) || 1);
          newStr = newStr + "&pageSize" + "=" + (parseInt(this.pageSize) || 20);
        }
      }
      return newStr;
    },
    // 更新url
    _updateUrl: debounce(
      function () {
        if (this.isUpdateUrl) {
          this.get(); // 更新searchObj数据
          let locationStr = this._urlParams2Str(this.searchObj);
          window.history.pushState("", "", locationStr);
        }
      },
      500,
      { leading: false, trailing: true }
    ),

    // 内部搜索项更新数据触发搜索
    insideFormItemChange: debounce(
      async function (field, itemOption, data) {
        if (field) {
          if (itemOption.change) {
            // 若searchItem的option有change方法则调用
            // itemOption.change(field, itemOption, data);
            try {
              await itemOption.change(field, itemOption, data);
            } catch (err) {
              console.error(err);
            }
          }
        }
        this.search();
      },
      500,
      { leading: false, trailing: true }
    ),

    // 外部插槽数据更新触发搜索功能
    outsideFormItemChange: debounce(
      async function (field, itemOption) {
        if (field) {
          if (itemOption.change) {
            // 若searchItem的option有change方法则调用
            // itemOption.change(field, itemOption, output);
            try {
              await itemOption.change(field, itemOption, this.searchForm[field]);
            } catch (err) {
              console.error(err);
            }
          }
        }
        this.search();
      },
      500,
      { leading: false, trailing: true }
    ),

    /**
     * 用于外部：初始化searchbar
     * @param {Object} query
     */
    init(query) {
      this._setQuery(query); // 通过外层query传入的数据则需要双向绑定初始值
      this.$nextTick(() => {
        // 如下区别于search的是不做url的更新
        let postData = this.get(); // 获取searchbar数据
        // this.$emit("modelChangeEvent", this.searchObj);
        this.$emit("search", postData, false); // false是指不重置分页数据
      });
    },
    /**
     * 用于外部：获取搜索栏的结果（默认是按格式封装的),否则以map形式返回
     * @param {boolean} isNotFormat，是否不格式化，默认false,默认需要格式化返回数据
     *
     * 默认封装的格式
     {
        groupOp: "AND",
        rules: [{field,op,data},{field,op,data}....],
      }
     */
    get(isNotFormat = false) {
      let _this = this;
      let rules = [],
        searchObj = {},
        resSearchObj = {};
      let itemsArr = this.allSearchItems;
      itemsArr.forEach(item => {
        let v = _this._getSearchFormVal(item);
        if (v) {
          rules.push({
            field: v.dbfield,
            op: v.op,
            data: v.dbdata,
          });
          searchObj[v.field] = v.data;
          resSearchObj[v.field] = Array.isArray(v.data) ? v.data.join(",") : v.data; // 若是数组[a,b]则需要转换成字符串"a,b"
        }
      });
      this.$set(this, "searchObj", searchObj); // 前端查询条件数据
      this.$set(this, "rules", rules); // 后端查询条件数据
      if (isNotFormat) {
        return resSearchObj; // map形式返回{key:value}
      } else {
        return {
          groupOp: "AND",
          rules: rules,
        };
      }
    },

    /**
     * 搜索操作，需要传值使外层的pageNo设置为1，默认为是需要重置分页显示为第一页
     * @param {boolean} isNotFormat，默认false，默认是封装后的结果返回，若为true则以map的方式返回
     * notResetPageNo为true时表示重置不pageNo，用于外层分页方法调用
     */
    search(isNotFormat = false) {
      let postData = this.get(isNotFormat); // 获取searchbar数据
      this._updateUrl(); // 更新url

      this.$nextTick(() => {
        // 外层数据不做更新
        // this.$emit("modelChangeEvent", this.searchObj);
        this.$emit("search", postData, true); // true是指重置分页数据
      });
    },

    // 重置、清空搜索操作（需要重置到默认的搜索项）
    reset() {
      let _this = this;
      let itemsArr = this.allSearchItems;
      // let searchModel = {}; // slot插槽的值

      // 重新赋值
      itemsArr.forEach(item => {
        // 设置初始值
        // searchModel[item.field] =
        _this._setSearchFormVal(item);
      });
      this.$nextTick(() => {
        this.search();
      });
    },
    nullish(val, fallback) {
      return (val !== null && val !== undefined) ? val : fallback;
    }
  },
};
</script>

<style lang="scss" scoped>
.grid-searchbar {
  .show-more-btn {
    position: relative;
    font-size: 12px;
    color: #2288d0;
    text-decoration: none;
    margin: 0 8px 0 5px;

    &:hover {
      text-decoration: underline;
    }
  }

  .triangle-down:after {
    content: "";
    position: relative;
    top: 1px;
    display: inline-block;
    margin-left: 5px;
    border: 4px solid transparent;
    border-top-color: #2288d0;
  }

  .triangle-up:after {
    content: "";
    position: relative;
    top: -3px;
    display: inline-block;
    margin-left: 5px;
    margin-top: -5px;
    border: 4px solid transparent;
    border-bottom-color: #2288d0;
  }

  ::v-deep .el-form-item {
    margin-bottom: 5px;
  }
}
</style>
