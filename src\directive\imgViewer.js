import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import Vue from "vue"

const imgViewer = {};
let imgViewerObj = null;
imgViewer.install = Vue => {
  /** 权限指令 v-imgviewer **/
  Vue.directive("imgViewer", {
    componentUpdated(el, binding) {
      Vue.nextTick(() => {
        let imgArr = el.getElementsByTagName("img");
      if (imgArr.length) {
        imgViewerObj && imgViewerObj.destroy();
        var t = new Date().getTime();
        imgViewerObj = new Viewer(el, {
          zIndex: t,
          url(image) {
            return image.src.replace(/\@\w+\.src$/, "");
          }
          //   hidden() {
          //     viewer.destroy();
          //   }
        });
      }
      })
    },
    unbind() {
      imgViewerObj && imgViewerObj.destroy();
    }
  });
};
export default imgViewer;
