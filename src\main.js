// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import Router from "vue-router";

import Element from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import App from "./App";
import router from "@/router";
import store from "@/store";

import * as filters from "./filters"; // global filters
import hasPermission from "@/directive/hasPermission";
import fixedBar from "@/directive/fixedBar";
import imgViewer from "@/directive/imgViewer";
import elFormRules from "@/plugins/elFormRules";
import globalMethods from "@/plugins/globalMethods";

import "@/icons";
import "@/mock/index";

import "normalize.css/normalize.css";
import "animate.css/animate.css";
import "@/styles/app.default.css";

import echarts from "echarts";
import "echarts/lib/chart/map.js";
import "echarts/map/js/china.js";
import "url-search-params-polyfill";

// 图片预览插件
import Viewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import "amfe-flexible";
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 9999
  }
});
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: "data-source"
  }
});

Vue.use(Element);
// global directive register
Vue.use(hasPermission);
Vue.use(fixedBar);
Vue.use(imgViewer);
Vue.config.productionTip = false;
Vue.use(elFormRules);
Vue.use(globalMethods);

// register global utility filters.
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]);
});

// 挂载全局
Vue.prototype.$echarts = echarts;

import Video from "video.js";
import "video.js/dist/video-js.css";
Vue.prototype.$video = Video;

/* eslint-disable no-new */
new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>"
});
