export default {
  props: {
    theme: {
      type: String,
      default: "dark",
    },
  },
  data() {
    return {
      timers: [], // 定时器
      loading: false,
      chart: null,
      options: null,
      defaultOption: {
        backgroundColor: "rgba(255, 255, 255, 0)",
      },

      isChartResizeInterval: false,
      chartResizeTimer: null,
      chartResizeIntervalTime: 2000,
    };
  },
  mounted() {
    this.startResizeListener();
    this.startResizeChart();
  },
  destroyed() {
    this.destroy();
  },
  methods: {
    startResizeListener() {
      window.addEventListener("resize", this.resizeHandle, false);
    },
    startResizeChart() {
      if (this.isChartResizeInterval) {
        this.chartResizeTimer = setInterval(this.resizeHandle, this.chartResizeIntervalTime);
      }
    },
    resizeHandle() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    set(options, clickFun) {
      let op = (this.options = { ...options, ...this.defaultOption });
      if (this.chart) {
        this.chart.setOption(op);
      } else {
        let mainChart = (this.chart = this.$echarts.init(this.$refs.chart, this.theme));
        mainChart.setOption(op, true);
      }
      if (clickFun && this.chart) {
        this.chart.on("click", clickFun);
      }
    },
    destroy() {
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
      if (this.chartResizeTimer) {
        clearInterval(this.chartResizeTimer);
      }
      // 清除resize listener
      window.removeEventListener("resize", this.resizeHandle, false);
    },
  },
};
