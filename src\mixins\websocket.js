"use strict";
import { getToken } from "@/utils/auth";
// 混入页面要求：需要有长链接处理函数:websocketCallback
export default {
  data() {
    return {
      token: getToken(),
      websock: null, //建立的连接
      lockReconnect: false, //是否真正建立连接
      timeout: 28 * 1000, //30秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      iswebsocketclose: false
    };
  },
  destroyed() {
    this.destroy();
  },
  created() {
    this.iswebsocketclose = false;
    if (this.websocketCallback) {
      //页面刚进入时开启长连接
      this.initWebSocket();
    } else {
      this.$message.error(
        "很抱歉，websocket无websocketCallback函数，无法开启长链接！"
      );
    }
  },
  methods: {
    destroy() {
      this.iswebsocketclose = true;
      this.timeoutnum && clearTimeout(this.timeoutnum);
      this.timeoutObj && clearTimeout(this.timeoutObj);
      this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj);
      if (this.websock) {
        // 关闭
        this.websock.close();
        this.websock = null;
      }
    },
    // 连接websocket
    initWebSocket() {
      if ("WebSocket" in window) {
        this.token = getToken();
        if (!this.token) {
          console.log(
            "%cwebsocket has no token ,can not connect.....",
            "color:#d00"
          );
          return;
        }
        //    console.log("您的浏览器支持 WebSocket!");
        this.websock = new WebSocket(
          "wss://whp.zh.gov.cn/webSocket?token=" + this.token
          // "wss://stag-zhys3.dacyun.com/webSocket?token=" + this.token
        );
        this.websock.onopen = this.websocketonopen;
        this.websock.onerror = this.websocketonerror;

        this.websock.onmessage = this.websocketonmessage;
        this.websock.onclose = this.websocketclose;
      } else {
        // 浏览器不支持 WebSocket
        console.log("您的浏览器不支持 WebSocket!因此无法获取报警列表");
      }
    },
    websocketonopen() {
      console.log("WebSocket连接成功");
      //开启心跳
      this.start();
    },
    websocketonerror(e) {
      //错误
      console.log("WebSocket连接发生错误");
      //重连
      this.reconnect();
    },
    websocketonmessage(e) {
      //数据接收
      // 　　　　　　 const redata = JSON.parse(e.data);
      //注意：长连接我们是后台直接1秒推送一条数据，
      //但是点击某个列表时，会发送给后台一个标识，后台根据此标识返回相对应的数据，
      //这个时候数据就只能从一个出口出，所以让后台加了一个键，例如键为1时，是每隔1秒推送的数据，为2时是发送标识后再推送的数据，以作区分
      //收到服务器信息，心跳重置
      this.reset();
      if (e.data) {
        let data = JSON.parse(e.data);
        // let txt = data.tractorNo + data.catNmCn;
        this.websocketCallback(data);
      }
    },
    websocketsend(agentData) {
      //数据发送
      this.websock.send(agentData);
    },
    websocketclose(e) {
      //关闭
      //重连
      if (!this.iswebsocketclose) {
        this.reconnect();
      }
      if (e && e.code) {
        console.log("connection closed (" + e.code + ")");
      }
    },
    reconnect() {
      //重新连接
      var that = this;
      if (that.lockReconnect) {
        return;
      }
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      that.timeoutnum && clearTimeout(that.timeoutnum);
      that.timeoutnum = setTimeout(function() {
        //新连接
        that.initWebSocket();
        that.lockReconnect = false;
      }, 5000);
    },
    reset() {
      //重置心跳
      var that = this;
      //清除时间
      clearTimeout(that.timeoutObj);
      clearTimeout(that.serverTimeoutObj);
      //重启心跳
      that.start();
    },
    start() {
      //开启心跳
      var self = this;
      self.timeoutObj && clearTimeout(self.timeoutObj);
      self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
      self.timeoutObj = setTimeout(function() {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        if (self.websock.readyState == 1) {
          //如果连接正常
          self.websock.send("heartCheck");
        } else {
          //否则重连
          self.reconnect();
        }
        self.serverTimeoutObj = setTimeout(function() {
          //超时关闭
          self.websock.close();
        }, self.timeout);
      }, self.timeout);
    }
  }
};
