import Vue from "vue";
import Vuex from "vuex";
import app from "./modules/app";
import errorLog from "./modules/errorLog";
import tagsView from "./modules/tagsView";
import user from "./modules/user";
import getters from "./getters";
import permission from "./modules/permission";
import maps from "./modules/maps";
import dynamicData from "./modules/dynamicData";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    errorLog,
    tagsView,
    permission,
    user,
    maps,
    dynamicData
  },
  getters
});

export default store;
