import Cookies from 'js-cookie'
import { getMenuList } from '@/api/menu'

const app = {
  state: {
    sidebar: {
      opened: !+Cookies.get('sidebarStatus'),
      withoutAnimation: false
    },
    menuList: [],
    device: 'desktop' // desktop 桌面pc端，mobile 移动端
  },
  mutations: {
    TOGGLE_SIDEBAR: state => {
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1)
      } else {
        Cookies.set('sidebarStatus', 0)
      }
      state.sidebar.opened = !state.sidebar.opened;
    },
    SET_MENULIST: (state, menuList) => {
      state.menuList = menuList
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 1)
      state.sidebar.opened = false
      state.sidebar.withoutAnimation = withoutAnimation
    },
    OPEN_SIDEBAR: (state, withoutAnimation) => {
      Cookies.set('sidebarStatus', 0)
      state.sidebar.opened = true
      state.sidebar.withoutAnimation = withoutAnimation
    },
  },
  actions: {
    getMenuList ({ dispatch, commit }) {
      return new Promise((resolve, reject) => {
        getMenuList().then(response => {
          if (response && response.code == 0) { // 菜单获取成功
            let username = sessionStorage.getItem("WHJK-USERNAME") || Cookies.get('WHJK-USERNAME')
            if (username === "admin") {
              response.menuList.forEach(item => {
                if (item.name === '信息登记') {
                  item.name = '承运监控'
                } else if (item.name === '量化考评') {
                  item.name = '闭环处置'
                }
              }
              )
            }

            // console.log(response.menuList)
            commit('SET_MENULIST', response.menuList);
            dispatch('setPermissions', response.permissions);
          }
          resolve(response);
        }).catch(error => {
          reject(error)
        })
      })
    },
    ToggleDevice ({ commit }, device) {
      commit('TOGGLE_DEVICE', device)
    },
    CloseSideBar ({ commit }, { withoutAnimation }) {
      commit('CLOSE_SIDEBAR', withoutAnimation)
    },
    OpenSideBar ({ commit }, { withoutAnimation }) {
      commit('OPEN_SIDEBAR', withoutAnimation)
    },
    ToggleSideBar ({ commit }) {
      commit('TOGGLE_SIDEBAR')
    },
  }
}

export default app
