import {
  getType,getAllType
} from "@/api/dynamic";
const dynamicData = {
  state: {
    // 所有的违章处置操作类型
    alarmDealActions: {},
    // 所有的违章处置操作类型
    allAlarmDealOptions: [],
    // 对应违章的操作类型
    alarmDealOptions: {}
  },
  mutations: {

    UPDATE_ALARM_DEAL_ACTIONS: (state, alarmDealActions) => {
      state.alarmDealActions = Object.assign({}, alarmDealActions);
    },
    UPDATE_ALL_ALARM_DEAL_OPTIONS: (state, allAlarmDealOptions) => {
      state.allAlarmDealOptions = allAlarmDealOptions
    },
    UPDATE_ALARM_DEAL_OPTIONS: (state, alarmDealOptions) => {
      state.alarmDealOptions = Object.assign({}, alarmDealOptions);
    }
  },
  actions: {
    dealDynamicData({
      commit
    }) {
      getType().then(res => {
        if (res.code == 0 && res.data) {
          let alarmDealActions = {}
          // 所有的违章处置操作类型
          for (let i = 0; i < res.data.length; i++) {
            alarmDealActions[res.data[i].cd] = {
              label: res.data[i].nmCn,
              value: res.data[i].cd,
            }
          }
          // 所有的违章处置操作类型
          let allAlarmDealOptions = Object.keys(alarmDealActions).map(function (key) {
            return alarmDealActions[key]
          });
          commit("UPDATE_ALARM_DEAL_ACTIONS", alarmDealActions)
          commit("UPDATE_ALL_ALARM_DEAL_OPTIONS", allAlarmDealOptions)
        }
      })

    },
    dealDynamicDataSec({
      commit
    }) {
      getAllType().then(res => {
        if (res.code == 0 && res.data) {
          let alarmDealOptions={}
          let obj=JSON.parse(res.data)
          for(var key in obj){
            alarmDealOptions[key]=obj[key].catcds
          }
          commit("UPDATE_ALARM_DEAL_OPTIONS", alarmDealOptions)
        }
      })

    },
  }
}

export default dynamicData
