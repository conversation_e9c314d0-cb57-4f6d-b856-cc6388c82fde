// import { asyncRouterMap, constantRouterMap } from '@/router'


// const permission = {
//   state: {
//     routers: constantRouterMap,
//     addRouters: []
//   },
//   mutations: {
//     SET_ROUTERS: (state, routers) => {
//       state.addRouters = routers
//       state.routers = constantRouterMap.concat(routers)
//     }
//   },
//   actions: {
//     GenerateRoutes({ commit }, data) {
//       return new Promise(resolve => {
//         // const { roles } = data
//         // let accessedRouters
//         // if (roles.indexOf('admin') >= 0) {
//         //   accessedRouters = asyncRouterMap
//         // } else {
//         //   accessedRouters = filterAsyncRouter(asyncRouterMap, roles)
//         // }
//         // commit('SET_ROUTERS', accessedRouters)
//         commit('SET_ROUTERS',asyncRouterMap)
//         resolve()
//       })
//     }
//   }
// }

// export default permission