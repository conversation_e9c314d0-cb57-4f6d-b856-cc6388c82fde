import {
  loginByUsername,
  logout,
  getUserInfo,
  getUserInfoByZZDCode,
  checkDingInfo
} from "@/api/login";
import { getToken, setToken, removeToken, getUserName } from "@/utils/auth";

const user = {
  state: {
    token: getToken(),
    username: sessionStorage.getItem("WHJK-USERNAME") || getUserName(),
    mobile: sessionStorage.getItem("WHJK-MOBILE"),
    name: sessionStorage.getItem("WHJK-NAME"),
    isFirstLogin: null,
    permissions: []
  },
  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_USERNAME: (state, name) => {
      state.username = name;
      if (name) {
        sessionStorage.setItem("WHJK-USERNAME", name);
      } else {
        sessionStorage.removeItem("WHJK-USERNAME");
      }
    },
    SET_MOBILE: (state, mobile) => {
      state.mobile = mobile;
      if (mobile) {
        sessionStorage.setItem("WHJK-MO<PERSON>LE", mobile);
      } else {
        sessionStorage.removeItem("WHJK-MO<PERSON>LE");
      }
    },
    SET_NAME: (state, name) => {
      state.name = name;
      if (name) {
        sessionStorage.setItem("WHJK-NAME", name);
      } else {
        sessionStorage.removeItem("WHJK-NAME");
      }
    },
    SET_ISFIRSTLOGIN: (state, flag) => {
      state.isFirstLogin = flag;
    },
    SET_PERMISSIONS: (state, permissionArr) => {
      state.permissions = permissionArr;
    },
    SET_ROLENAME: (state, rolename) => {
      // 记录指挥中心账号登录
      localStorage.setItem("roleName", rolename);
      // console.log(rolename);
    },
    CLEAR_ROLENAME: state => {
      localStorage.removeItem("roleName");
    }
  },
  actions: {
    // 用户登录
    LoginByUsername({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        loginByUsername(userInfo)
          .then(response => {
            if (response && response.code == 0) {
              //登录成功
              // console.log(response);
              commit("SET_TOKEN", response.token);
              commit("SET_USERNAME", response.username);
              commit("SET_MOBILE", response.mobile);
              commit("SET_NAME", response.name);
              setToken(response.token);

              commit("SET_ROLENAME", response.roleNameList.join(","));

              // 设置是否是第一次登录标识
              if (response.isFirstLogin) {
                commit("SET_ISFIRSTLOGIN", response.isFirstLogin);
              }
            }
            resolve(response);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 获取用户信息

    // 登出
    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        logout()
          .then(response => {
            let result = response;
            if (result.code == 0) {
              commit("SET_TOKEN", "");
              commit("SET_USERNAME", "");
              commit("SET_MOBILE", "");
              commit("SET_NAME", "");
              commit("SET_PERMISSIONS", []);
              removeToken();

              // 删除指挥中心账号登录记录
              commit("CLEAR_ROLENAME");
            }
            resolve(result);
          })
          .catch(error => {
            commit("SET_TOKEN", "");
            commit("SET_USERNAME", "");
            commit("SET_MOBILE", "");
            commit("SET_NAME", "");
            commit("SET_PERMISSIONS", []);
            removeToken();
            location.reload();
            // 删除指挥中心账号登录记录
            commit("CLEAR_ROLENAME");
            reject(error);
          });
      });
    },
    ClearCache({ commit }) {
      return new Promise(resolve => {
        commit("SET_TOKEN", "");
        commit("SET_USERNAME", "");
        commit("SET_MOBILE", "");
        commit("SET_NAME", "");
        commit("SET_PERMISSIONS", []);
        removeToken();
        // 删除指挥中心账号登录记录
        commit("CLEAR_ROLENAME");
        resolve();
      });
    },

    // 浙政钉登录
    getUserInfoByZZDCode({ commit }, authCode) {
      return new Promise((resolve, reject) => {
        getUserInfoByZZDCode(authCode)
          .then(function(response) {
            if (response && response.code == 0) {
              // //登录成功
              commit("SET_TOKEN", response.token);
              commit("SET_USERNAME", response.username);
              commit("SET_MOBILE", response.mobile);
              commit("SET_NAME", response.name);
              setToken(response.token);

              commit("SET_ROLENAME", response.roleNameList.join(","));

              // 设置是否是第一次登录标识
              if (response.isFirstLogin) {
                commit("SET_ISFIRSTLOGIN", response.isFirstLogin);
              }
            }
            resolve(response);
          })
          .catch(function(error) {
            reject(error);
          });
      });
    },
    // 浙政钉绑定手机登录
    checkDingInfo({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        checkDingInfo(userInfo)
          .then(function(res) {
            let response = res.data;
            if (response && response.code == 0) {
              // //登录成功
              commit("SET_TOKEN", response.token);
              commit("SET_USER_INFO", {
                name: response.name || "",
                mobile: response.mobile || "",
                username: response.username || "",
                roleType: response.roleType || "",
                areaId: response.areaId || "",
                roleNameList: response.roleNameList || []
              });
              commit("tags/DEL_ALL_TAG");
              commit("CLEAR_LOCK");
              // 设置是否是第一次登录标识
              if (response.isFirstLogin) {
                commit("SET_ISFIRSTLOGIN", response.isFirstLogin);
              }
            }
            resolve(response);
          })
          .catch(function(error) {
            reject(error);
          });
      });
    },
    // 设置用户权限permission
    setPermissions({ commit }, permissionArr) {
      return new Promise(resolve => {
        commit("SET_PERMISSIONS", permissionArr);
        resolve();
      });
    }
  }
};

export default user;
