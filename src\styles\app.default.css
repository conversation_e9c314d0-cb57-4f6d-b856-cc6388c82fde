/**************************\
	name: app.default.css
	author: gsj
	date: 2018-03-14
	desc: 默认app的皮肤样式
\**************************/

@import './base.css';
@import './animate.css';
@import './element-ui.css';
@import './flex.scss';

.iconfont {
  font-family: 'FontF<PERSON>ly';
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0px;
}

::-webkit-scrollbar-track-piece {
  background-color: #fff;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #dedee0;
  border-radius: 20px;
}

.not-null {
  position: relative;
  /* top: -10px; */
  color: #e00;
  font-size: 12px;
}

/* layout  >>>>>>*/
.app-wapper {
  position: relative;
  min-height: 100vh;
  height: auto;
  min-width: 290px;
  overflow: hidden;
  background-color: #ecf0f5;
}

.app-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}

.app-wapper.hide-sidebar .app-sidebar-wrapper {
  display: none;
}

.app-wapper.hide-sidebar .app-main-container {
  left: 0;
  width: 100%;
}

.app-content .sidebar-container {
  background: -webkit-linear-gradient(0deg, #0a284a, #063f86);
  background: -o-linear-gradient(0deg, #0a284a, #063f86);
  background: -moz-linear-gradient(0deg, #0a284a, #063f86);
  background: linear-gradient(0deg, #0a284a, #063f86);
}

.app-content .app-main-container {
  position: relative;
  padding-top: 50px;
  background: transparent;
}

.app-main-container .app-main-component {
  background-color: #ecf0f5;
}

/* layout  <<<<<<*/

/* Navbar >>>>>> */
.navbar-header {
  background-color: #063f86;
}

.navbar-header:hover {
  color: #fff;
}

.navbar-content {
  background: -webkit-linear-gradient(0deg,
      #0a284a,
      #063f86);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(0deg,
      #0a284a,
      #063f86);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(0deg,
      #0a284a,
      #063f86);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(0deg, #0a284a, #063f86);
}

.navbar-content .navbar-right-menu .el-dropdown {
  color: #fff;
}

/* Sidebar >>>>>> */
.left-side .el-menu-item.is-active {
  color: #063f86;
}

.left-side .el-menu-item.is-active:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  display: block;
  width: 7px;
  background-color: #05a1f9;
}

.left-side .el-menu-item:focus,
.left-side .el-menu-item:hover {
  color: #063f86;
}

.left-side .el-menu-item,
.left-side .el-submenu__title {
  height: 45px;
  line-height: 45px;
  color: #fff;
}

.left-side .el-menu .el-menu-item {
  -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
}

.left-side .el-menu-item i {
  color: inherit;
}

.left-side .el-submenu__title:hover {
  color: #25476a;
}

.left-side .el-submenu__title i {
  color: #fff;
}

.left-side .el-submenu__title:hover i {
  color: #25476a;
}

.left-side .sidebar-submenu .el-menu {
  background: rgb(23, 83, 158);
}

.left-side .sidebar-submenu .el-menu>li {
  padding-left: 50px !important;
}

.left-side .el-menu-item.is-active {
  background: #ecf0f5;
}

/* Sidebar <<<<<< */

/* 折叠侧边栏 >>>>>> */
.sidebar-collapse .app-content .sidebar-container .el-submenu__title {
  text-align: center;
}

/* 折叠侧边栏 <<<<<<*/

/* 顶部菜单栏 >>>>>> */
.topbar-container .el-menu-item.is-active {
  /* background-color: #092e55; */
  border-left: 0;
  background: url("~static/img/header-hover.png") no-repeat center center;
  background-size: 100% 100%;
}

.topbar-menu.el-menu--horizontal {
  background: transparent;
  border-bottom: 0;
}

.topbar-menu.el-menu--horizontal li.el-submenu .el-submenu__title {
  padding: 0 15px;
}

.topbar-menu.el-menu--horizontal li.el-submenu:hover .el-submenu__title {
  /* background: #1065bd; */
  background: url("~static/img/header-hover.png") no-repeat center center;
  background-size: 100% 100%;
}

.topbar-menu.el-menu--horizontal li.el-submenu .el-submenu__title>span[slot='title'] {
  color: #fff;
}

.topbar-menu.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.topbar-menu.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  /* background: #1065bd; */
  background: url("~static/img/header-hover.png") no-repeat center center !important;
  background-size: 100% 100%;
}

.navbar-right-menu.el-menu--horizontal {
  background: transparent;
  border-bottom: 0;
}

.navbar-right-menu.el-menu--horizontal>.el-menu-item {
  padding: 0 8px;
}

.navbar-right-menu.el-menu--horizontal li.el-submenu .el-submenu__title {
  padding: 0 8px;
  font-size: 13px;
  color: #d3d3d3;
}

.navbar-right-menu.el-menu--horizontal li.el-submenu .el-submenu__title i {
  color: #d3d3d3;
}

.navbar-right-menu.el-menu--horizontal li.el-submenu:hover .el-submenu__title {
  background-color: rgba(16, 101, 189, 0.9);
  color: #fff;
}

.navbar-right-menu.el-menu--horizontal li.el-submenu .el-submenu__title>span[slot='title'] {
  color: #fff;
}

.navbar-right-menu.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.navbar-right-menu.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  background-color: rgba(16, 101, 189, 0.9);
}

.el-menu--horizontal .el-menu .navbar-right-menu-item:hover .el-submenu__title {
  background-color: rgba(16, 101, 189, 0.9);
  color: #fff;
}

/* 顶部菜单栏 <<<<<<*/

/* app-main-content 内容块 >>>>>> */
.app-main-content {
  padding: 20px;
  margin: 10px;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  border-image: initial;
  overflow: hidden;
}

.grid-searchbar {
  margin-bottom: 5px;
}

.grid-searchbar>.el-form .el-form-item {
  margin-bottom: 5px;
}

/* app-main-content 内容块 <<<<<< */

/* 注册页面 >>>>>> */
.code-wape .code-input .el-input-group__prepend {
  padding: 0;
  width: 100px;
}

.code-wape .code-input .el-input-group__append .el-button--primary {
  color: #fff;
  background-color: #409eff;
  border-radius: 0 4px 4px 0;
}

/* 注册页面 <<<<<< */

/* 列表页面样式 >>>>>> */
.el-table tbody .cell .el-button,
.el-table tbody .cell .el-dropdown-link {
  font-size: 13px;
}

.el-table tbody .cell .el-button--text {
  padding: 0;
}

/*缩小版的el-table*/
.simple-small-table tbody td,
.simple-small-table tbody th {
  padding: 0;
  line-height: 30px;
}

.simple-small-table tbody .cell {
  line-height: 30px;
}

.simple-small-table tbody .cell .el-button {
  padding: 8px;
}

.simple-small-table .el-table__expanded-cell[class*='cell'] {
  padding: 10px 5px 10px 50px;
}

.simple-small-table tbody .cell .el-tag {
  padding: 4px;
  line-height: 14px;
}

/*缩小版的搜索栏*/
.simple-small-searchbar .el-input,
.simple-small-searchbar .el-date-editor.el-input,
.simple-small-searchbar .el-select {
  width: 150px;
}

.simple-small-searchbar .el-form--inline .el-form-item {
  margin-bottom: 5px;
}

/* 列表页面样式 <<<<<< */

/* 详情页面样式 >>>>>> */
.panel,
.panel-simple {
  border-radius: 2px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  box-sizing: border-box;
}

.panel-simple {
  min-height: 200px;
}

.panel .panel-header,
.panel-simple .panel-header {
  position: relative;
  display: block;
  box-sizing: border-box;
}

.panel .panel-header:before,
.panel .panel-header:after,
.panel-simple .panel-header:before,
.panel-simple .panel-header:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.panel .panel-header {
  min-height: 55px;
  padding: 0 15px;
  /* border-bottom: 1px solid #ecf0f5; */
  color: #333;
  border-radius: 2px 2px 0 0;
  font-size: 15px;
  padding-left: 100px;
  margin-bottom: 15px;
}

.panel-simple .panel-header {
  box-sizing: border-box;
  height: 45px;
  line-height: 45px;
  padding: 0 15px;
  color: #4d627b;
  border-radius: 2px 2px 0 0;
  font-size: 15px;
  margin: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.07);
}

.panel-simple .panel-header .panel-heading-inner {
  float: left;
}

.panel-simple .panel-header .panel-heading-inner>svg {
  font-size: 25px;
  margin-right: 8px;
  margin-top: -4px;
  color: #5dc3ba;
  vertical-align: middle;
}

.panel .panel-header:before,
.panel .panel-header:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.panel .panel-header .panel-heading-inner {
  position: absolute;
  left: -8px;
  display: inline-block;
  padding-right: 20px;
  color: #fff;
  white-space: nowrap;
  background-color: #297ace;
  border-radius: 5px 5px 5px 0;
  padding: 10px 20px;
  margin-top: 10px;
}

.panel .panel-header .panel-heading-inner:after {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 0;
  content: '';
  border: 4px dashed transparent;
  border-top-color: #297ace;
  border-right-color: #297ace;
}

.panel .panel-header .panel-heading-right {
  float: right;
  text-align: right;
  margin-top: 20px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e8e3e3;
}

.panel-simple .panel-header .panel-heading-right {
  float: right;
  text-align: right;
  cursor: pointer;
}

.panel-simple .panel-header .panel-heading-right>svg {
  vertical-align: middle;
  margin-top: -4px;
  margin-left: 5px;
}

.panel .panel-header .panel-heading-right .lic-status-info {
  display: inline-block;
  font-size: 12px;
  margin-right: 8px;
}

.panel .panel-body,
.panel-simple .panel-body {
  overflow-y: auto;
  line-height: 28px;
  font-size: 13px;
  font-family: '\5FAE\8F6F\96C5\9ED1', 'Microsoft Yahei', 'Hiragino Sans GB',
    tahoma, arial, '\5B8B\4F53';
  box-sizing: border-box;
}

.panel .panel-body {
  padding: 0;
  padding-bottom: 15px;
}

.panel-simple .panel-body {
  padding: 15px;
}

.panel .panel-footer,
.panel-simple .panel-footer {
  padding: 10px 15px;
  font-size: 13px;
  color: #9a9a9a;
  border-top: 1px dashed #ccc;
  line-height: 24px;
  box-sizing: border-box;
}

.panel .panel-footer .lic-status {
  font-size: 15px;
  color: red;
}

.badge-item-move-down>.el-badge__content {
  top: 6px;
}

.detail-container {
  position: relative;
  padding: 15px;
  margin: 0 auto;
}

.detail-container .detail-ul {
  padding: 15px 15px;
  padding-top: 0;
  margin: 0;
  display: block;
}

.detail-container .detail-ul:before,
.detail-container .detail-ul:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.detail-container .detail-ul>li {
  width: 33.333%;
  float: left;
}

.detail-container .detail-ul>li .detail-desc {
  padding-right: 5px;
  min-width: 85px;
  width: auto;
  color: #9a9a9a;
  float: left;
  text-align: left;
}

.detail-container .detail-ul>li .detail-area {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lic-wape {
  position: relative;
}

.lic-wape:before {
  display: block;
  content: '';
  width: 2px;
  height: 100%;
  /* background-color: #57b382; */
  background-color: #dcd9d9;
  position: absolute;
  left: 20px;
  z-index: 1;
}

/*证照步骤条*/
.custom-lic-steps {
  padding: 13px;
}

.custom-lic-steps .el-step.is-simple .el-step__title {
  font-size: 12px;
}

.lic-panel {
  position: relative;
  margin: 0 0 15px;
  background: #fff;
  padding: 0;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.lic-panel:first-child {
  /* padding-top: 10px; */
}

.lic-panel-header {
  /* margin-bottom: 10px; */
  position: relative;
  border-radius: 3px;
  margin: 0;
  padding-left: 34px;
  cursor: pointer;
}

.lic-panel-header .lic-notice-expired {
  width: 160px;
  height: 160px;
  position: absolute;
  top: -8px;
  right: -8px;
  background-repeat: no-repeat;
}

.lic-panel-header .lic-panel-title {
  padding: 8px;
}

.lic-panel-header .lic-panel-title:before {
  content: '';
  position: absolute;
  left: 10px;
  top: 11px;
  display: inline-block;
  width: 15px;
  height: 15px;
  -webkit-border-radius: 3em;
  -moz-border-radius: 3em;
  border-radius: 3em;
  border: 3px solid #fff;
  background-color: #57b382;
  -webkit-box-shadow: 0 0 0 1px #57b382;
  -moz-box-shadow: 0 0 0 1px #57b382;
  box-shadow: 0 0 0 1px #57b382;
  z-index: 2;
}

.lic-panel-header.red,
.lic-panel-header.deepred,
.lic-panel-header.yellow,
.lic-panel-header.green,
.lic-panel-header.blue,
.lic-panel-header.gray {
  color: #fff;
}

.lic-panel-header.red {
  /* background-color: red; */
  background-color: #ea6291;
}

.lic-panel-header.deepred {
  /* background-color: red; */
  background-color: #f13939;
}

.lic-panel-header.yellow {
  /* background-color: #FEC13E; */
  background-color: #fbb12d;
}

.lic-panel-header.green {
  /* background-color: green; */
  background-color: #8fc155;
}

.lic-panel-header.blue {
  /* background-color: #1F6FF4; */
  background-color: #2facf1;
}

.lic-panel-header.gray {
  background-color: #8e8e8e;
}

.lic-panel-header.gray .lic-panel-title:before {
  background-color: #8e8e8e;
  -webkit-box-shadow: 0 0 0 1px #8e8e8e;
  box-shadow: 0 0 0 1px #8e8e8e;
}

.lic-panel-header.red .lic-panel-title:before {
  /* background-color: red;
    -webkit-box-shadow: 0 0 0 1px #ea5959;
    box-shadow: 0 0 0 1px #ea5959; */
  background-color: #ea6291;
  -webkit-box-shadow: 0 0 0 1px #ea6291;
  box-shadow: 0 0 0 1px #ea6291;
}

.lic-panel-header.deepred .lic-panel-title:before {
  /* background-color: red;
    -webkit-box-shadow: 0 0 0 1px #ea5959;
    box-shadow: 0 0 0 1px #ea5959; */
  background-color: #f13939;
  -webkit-box-shadow: 0 0 0 1px #f13939;
  box-shadow: 0 0 0 1px #f13939;
}

.lic-panel-header.yellow .lic-panel-title:before {
  /* background-color: #FD9E06;
    -webkit-box-shadow: 0 0 0 1px #FEC13E;
    box-shadow: 0 0 0 1px #FEC13E; */
  background-color: #fbb12d;
  -webkit-box-shadow: 0 0 0 1px #fbb12d;
  box-shadow: 0 0 0 1px #fbb12d;
}

.lic-panel-header.green .lic-panel-title:before {
  /* background-color: green;
    -webkit-box-shadow: 0 0 0 1px #29a229;
    box-shadow: 0 0 0 1px #29a229; */
  background-color: #8fc155;
  -webkit-box-shadow: 0 0 0 1px #8fc155;
  box-shadow: 0 0 0 1px #8fc155;
}

.lic-panel-header.blue .lic-panel-title:before {
  /* background-color: #1F6FF4;
    -webkit-box-shadow: 0 0 0 1px #548ff3;
    box-shadow: 0 0 0 1px #548ff3; */
  background-color: #2facf1;
  -webkit-box-shadow: 0 0 0 1px #2facf1;
  box-shadow: 0 0 0 1px #2facf1;
}

.lic-panel-body {
  margin-left: 34px;
  margin-top: 15px;
  padding-bottom: 15px;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

/* .lic-panel-body>:last-child {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
} */

.lic-panel-footer {
  padding: 8px;
  margin: 0 10px 0 0;
  line-height: 24px;
  border-top: 1px dashed #ccc;
  color: #9a9a9a;
  margin-left: 34px;
}

.lic-panel-footer .lic-status {
  font-size: 15px;
  color: red;
}

.required:after {
  content: '*';
  position: relative;
  top: -10px;
  color: #e00;
  font-size: 12px;
}

.lic-ul,
.lic-mod-ul {
  margin: 0;
  padding: 0;
}

.lic-ul>li.lic-item {
  line-height: 32px;
}

.lic-mod-ul>li.lic-item {
  line-height: 32px;
  margin-bottom: 8px;
}

.lic-ul>li.lic-item,
.lic-mod-ul>li.lic-item {
  display: block;
}

.lic-ul>li.lic-item:before,
.lic-mod-ul>li.lic-item:before,
.lic-ul>li.lic-item:after,
.lic-mod-ul>li.lic-item:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.lic-ul>li .lic-item-desc,
.lic-mod-ul>li .lic-mod-desc {
  padding-right: 5px;
  min-width: 120px;
  width: auto;
  color: #9a9a9a;
  float: left;
  text-align: right;
}

.lic-ul>li .lic-item-area {
  padding-left: 120px;
}

.lic-ul>li .lic-item-area,
.lic-mod-ul>li .lic-mod-area {
  /* overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; */
}

.lic-mod-ul>li .lic-mod-area {
  /* padding-left:145px; */
}

.lic-mod-ul>li .lic-mod-area .el-input--small {
  width: 220px;
}

.lic-mod-ul>li .lic-mod-area .el-select--small {
  width: 220px;
}

.lic-upload-msg {
  color: #f69e45;
  font-size: 12px;
  line-height: 24px;
  text-align: left;
  padding-left: 10px;
}

.lic-upload-item {
  width: 188px;
  background-color: #fff;
  float: left;
  padding-left: 10px;
  margin-bottom: 25px;
}

.lic-upload-item .lic-upload-main-msg {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  font-size: 12px;
  color: #9a9a9a;
  text-align: left;
}

.lic-upload-item .lic-upload-main-uploadArea {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  width: 180px;
  height: 130px;
  /* background: url(../img/add_img.png) no-repeat scroll center 25px rgba(0,0,0,0); */
}

.lic-uploader .el-upload {
  display: block;
}

.lic-uploader .lic-oper-bar {
  position: absolute;
  z-index: 1;
  text-align: center;
  display: block;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 5px;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item {
  margin: 0;
  padding: 0;
  height: auto;
  border: none;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-thumbnail {
  float: inherit;
  float: initial;
  width: 100%;
  height: auto;
  position: inherit;
  position: initial;
  margin-left: 0;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-name {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
}

.lic-uploader .el-upload-list--picture .el-upload-list__item-name i {
  display: none;
}

.lic-uploader .el-icon-upload-btn {
  padding: 5px;
  border-radius: 100%;
}

.lic-uploader .el-icon-upload-btn i {
  font-size: 16px;
}

.custom-lic-viewer-container {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 400px !important;
}

.viewer-open {
  overflow: auto !important;
}

/* .viewer-container{
    right:400px
} */

.detail-dialog .el-dialog__body {
  background-color: #ecf0f5;
}

.dialog-no-padding .el-dialog__body {
  padding: 0;
}

/* 详情页面样式 <<<<<< */

/* 编辑页面样式  >>>>>>*/
.mod-container {
  position: relative;
  padding: 15px;
  margin: 0 auto;
}

.mod-container-oper {
  padding: 10px 15px;
  font-size: 13px;
  color: #9a9a9a;
  background-color: #fff;
  line-height: 24px;
  text-align: right;
  margin-bottom: 10px;
}

.mod-container .mod-detail-ul {
  padding: 15px 15px;
  margin: 0;
  display: block;
}

.mod-container .mod-detail-ul:before,
.mod-container .mod-detail-ul:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.mod-container .mod-detail-ul>li {
  width: 50%;
  float: left;
  margin-bottom: 4px;
}

@media screen and (max-width: 830px) {
  .mod-container .mod-detail-ul>li {
    width: 100%;
  }
}

.mod-container .mod-detail-ul>li .mod-desc {
  float: left;
  /* height: 34px; */
  width: 145px;
  /* padding-top: 6px; */
  padding-right: 10px;
  text-align: right;
  /* font-size: 14px; */
  white-space: nowrap;
  overflow: hidden;
  line-height: 28px;
  color: #9a9a9a;
}

.mod-container .mod-detail-ul>li .mod-area {
  /* height: 38px; */
  padding-left: 145px;
  padding-right: 10px;
  text-align: left;
  /* line-height: 34px; */
  line-height: 28px;
}

/* 编辑页面样式 <<<<<< */

/* 清除百度地图logo */
.BMap_cpyCtrl {
  display: none;
}

.anchorBL {
  display: none;
}

.el-dialog__body {
  padding: 5px 10px;
  color: #606266;
  font-size: 14px;
}

.print-panel {
  border-radius: 2px;
  background-color: #fff;
  margin-bottom: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

.print-panel .print-panel-header {
  position: relative;
  padding: 0 20px;
  color: #333;
  width: 100%;
}

.print-panel .print-panel-header .panel-heading-content {
  text-align: center;
  padding: 10px;
  font-size: 22px;
  font-weight: bold;
  /* border-bottom: 1px dashed #ccc; */
}

.print-panel .print-panel-header .panel-heading-right {
  float: right;
  margin-top: -38px;
  text-align: right;
}

.print-panel .print-panel-body {
  width: 100%;
  position: relative;
  line-height: 28px;
  font-size: 13px;
  font-family: '\5FAE\8F6F\96C5\9ED1', 'Microsoft Yahei', 'Hiragino Sans GB',
    tahoma, arial, '\5B8B\4F53';
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 30px;
}

.print-panel .print-panel-body:after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  border: none;
  background: #fff;
  background-repeat: repeat-x;
  background-position: 0 100%;
  background-size: 20px 25px;
  height: 24px;
  background-image: -webkit-linear-gradient(45deg, #ecf0f5 25%, transparent 25%),
    linear-gradient(-45deg, #ecf0f5 25%, transparent 25%);
  background-image: linear-gradient(45deg, #ecf0f5 25%, transparent 25%),
    linear-gradient(-45deg, #ecf0f5 25%, transparent 25%);
}

.print-panel .print-panel-body .custom-table {
  width: 100%;
  border: 1px solid #dee2e6;
  border-collapse: collapse;
  line-height: 32px;
  font-size: 13px;
  color: #000;
}

.print-panel .print-panel-body .custom-table thead th,
.print-panel .print-panel-body .custom-table thead td {
  border: 1px solid #dee2e6;
  padding: 4px 6px;
}

.print-panel .print-panel-body .custom-table tbody th {
  text-align: left;
  font-weight: bold;
  padding: 0 5px;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  background-color: #f5f5f5;
}

.print-panel .print-panel-body .custom-table tbody th.title {
  background-color: #e1e1e1;
  vertical-align: middle;
  text-align: center;
  width: 24px;
  line-height: 16px;
  writing-mode: tb-rl;
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl;
  border-bottom: 1px solid #ccc;
}

.print-panel .print-panel-body .custom-table tbody tr.subtitle {
  background-color: #eceaea;
  vertical-align: middle;
  text-align: center;
  width: 24px;
  line-height: 16px;
  writing-mode: tb-rl;
  -webkit-writing-mode: tb-rl;
  -ms-writing-mode: tb-rl;
  border-bottom: 1px solid #ccc;
}

.print-panel .print-panel-body .custom-table tbody td {
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  padding: 0 5px;
}