/**************************\
	name: element-ui.css
	author: gsj
	date: 2018-03-04
	desc: 覆盖element-ui样式
\**************************/

/* el-table样式  >>>>>>*/
.el-table {
  margin-bottom: 10px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 1px;
  border-top: 1px solid #ebeef5;
}
.el-table thead th {
  background-color: #edf2fe;
}
.el-table tr:nth-child(2n + 1) {
  background-color: rgb(251, 251, 252);
}
.el-table .el-table__body-wrapper > table > tbody > tr:nth-child(2n + 1) {
  background-color: rgb(251, 251, 252);
}
.el-table tbody .cell {
  font-size: 13px !important;
  line-height: 1.2 !important;
}
.el-table th > .cell {
  color: #4d627b;
}

.el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.el-table__body tr.current-row > td,
.el-table__body tr.hover-row.current-row > td,
.el-table__body tr.hover-row.el-table__row--striped.current-row > td,
.el-table__body tr.hover-row.el-table__row--striped > td,
.el-table__body tr.hover-row > td {
  background: #f5f5f5;
}
.el-table--border td,
.el-table--border th,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right: 1px solid #f4f4f4;
}
.el-table--border.el-loading-parent--relative {
  border-top: 3px solid rgb(210, 214, 222);
}
/* .el-table td, .el-table th.is-leaf {
    border-bottom: 1px solid #f4f4f4;
} */
.el-table__header-wrapper::-webkit-scrollbar-track-piece {
  background-color: #fff;
}
.el-table__header-wrapper::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}
.el-table__header-wrapper::-webkit-scrollbar-thumb {
  background: #dedee0;
  border-radius: 20px;
}

.el-table__body-wrapper::-webkit-scrollbar-track-piece {
  background-color: #fff;
}
.el-table__body-wrapper::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #dedee0;
  border-radius: 20px;
}
.el-table tbody .cell {
  font-size: 12px;
  line-height: 1.5;
}
.el-table tbody .overflow-visble .cell{
  overflow: visible !important;
}
.el-table tbody .cell .el-tag {
  background-color: #409eff;
  color: #fff;
  height: inherit;
  line-height: inherit;
  padding: 4px;
}
.el-table tbody .cell .el-tag--success {
  background-color: #67c23a;
  color: #fff;
}
.el-table tbody .cell .el-tag--danger {
  background-color: #f56c6c;
  color: #fff;
}
.el-table tbody .cell .el-tag--info {
  background-color: #909399;
  color: #fff;
}
.el-table tbody .cell .el-tag--warning {
  background-color: #e6a23c;
  color: #fff;
}

/* table中展开行form的样式 */
.form-expand-in-table .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}
.form-expand-in-table .el-form-item .el-form-item__label {
  line-height: 24px;
  font-size: 12px;
}
.form-expand-in-table .el-form-item .el-form-item__label:after {
  content: ':';
}
.form-expand-in-table .el-form-item .el-form-item__content {
  line-height: 24px;
  font-size: 12px;
}

/* el-table样式  <<<<<<*/

/* el-form样式  >>>>>>*/
/* .el-form--inline .el-form-item {
    margin-bottom:10px;
} */
.el-form .el-select,
.el-form .el-date-editor.el-input,
.el-form .el-date-editor.el-input__inner,
.el-form .el-autocomplete,
.el-form .el-cascader {
  width: 100%;
}
.el-form .el-form-item {
  margin-bottom: 15px;
}
.el-form .el-form-item__error {
  padding-top: 0;
}

.detail-area
  .el-checkbox__input.is-disabled.is-indeterminate
  .el-checkbox__inner,
.detail-area .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}

/* el-form样式  <<<<<<*/

/* el-menu样式  >>>>>>*/
.el-menu-item:focus,
.el-menu-item:hover {
  background-color: #ecf0f5;
}
.el-menu--horizontal > .el-submenu .el-submenu__title {
  height: 50px;
  line-height: 50px;
}
.el-menu--horizontal .right-menu > .el-submenu .el-submenu__icon-arrow {
  position: static;
  vertical-align: middle;
  margin-left: 8px;
  margin-top: -3px;
}
.el-submenu__title:hover {
  background-color: #edf0f5;
}
.el-submenu__title:focus,
.el-submenu__title:hover {
  outline: 0;
  background-color: #edf0f5;
}
.el-menu--horizontal > .el-menu-item {
  height: 50px;
  line-height: 50px;
  padding: 0 10px;
  /* color: #d9d9d9; */
  color:#9CB4D3;
  font-size: 13px;
}
.el-menu--horizontal > .el-menu-item.is-active {
  color: #fff;
}
.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  outline: 0;
  color: #fff;
  background-color: rgba(16, 101, 189, 0.9);
}
/* el-menu样式  <<<<<<*/

/* 按钮样式  >>>>>>*/
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

/* 按钮样式  <<<<<<*/

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type='file'] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.cell .el-tag {
  margin-right: 0px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}

.status-col .cell .el-tag {
  margin-right: 0px;
}

/*暂时性解决dialog 问题 https://github.com/ElemeFE/element/issues/2461*/
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

/*文章页textarea修改样式*/
.article-textarea textarea {
  padding-right: 40px;
  resize: none;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #bfcbd9;
}

/* element ui upload */
.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

/* element ui steps 步骤条 */
.el-step.is-simple .el-step__icon {
  vertical-align: middle;
}

.effect-page .el-carousel__arrow {
  background-color: rgb(117, 186, 234);
  height: 46px;
  width: 46px;
}


.term .el-form-item__label {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #fff;
  line-height: 40px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.term .el-radio-button__inner, .term .el-radio-group {
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.term .el-radio, .term .el-radio__label {
  font-size: 12px;
}

.term .el-radio {
  color: #89defe;
  margin-right: 0;
}
.term .el-radio+.el-radio {
  margin-left: 10px;
}

.term .el-radio__inner {
  background-color: #102556;
  border: 1px solid #064d9a;
}

.term .el-date-editor .el-range-input,
.term .el-input.is-disabled .el-input__inner,
.term .el-input__inner,
.term .el-textarea__inner {
  background-color: #0a264e;
  color: #fff;
  border-color: #4b72a9;
}

.el-image-viewer__close{
  color: #fff;
  background-color: #606266;
}