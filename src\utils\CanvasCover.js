

var CanvasCover = (function(){

  //工具类
  var utils = {
      guid:function(){
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
              var r = Math.random() * 16 | 0,
                  v = c == 'x' ? r : (r & 0x3 | 0x8);
              return v.toString(16);
          });
      },
      isString:function(source){
          return '[object String]' == Object.prototype.toString.call(source);
      },
      isFunction:function(source){
          return '[object Function]' == Object.prototype.toString.call(source);
      },
      _inherit:function(subClass, superClass){
          var selfProto = subClass.prototype;
          var proto;
          var clazz = new Function();
          clazz.prototype = superClass.prototype;
          proto = subClass.prototype = new clazz();

          for(var k in selfProto){
              proto[k] = selfProto[k]
          }

          subClass.prototype.constructor = subClass;
          subClass.superClass = superClass.prototype;
      },
      getRotatedPoint:function(beforeX, beforeY, centerX, centerY, radian){
          /* 
              如果是逆时针旋转：
              x2 = (x1 - x0) * cosa - (y1 - y0) * sina + x0
              y2 = (y1 - y0) * cosa + (x1 - x0) * sina + y0
              如果是顺时针旋转：
              x2 = (x1 - x0) * cosa + (y1 - y0) * sina + x0
              y2 = (y1 - y0) * cosa - (x1 - x0) * sina + y0
          */
          return {
              x: Math.cos(radian)*(beforeX-centerX) -  Math.sin(radian)*(beforeY-centerY) + centerX, 
              y: Math.sin(radian)*(beforeX-centerX) + Math.cos(radian)*(beforeY-centerY) + centerY
          }
      },

      dotInRect(dot,x1, y1, x2, y2){
          // 点在矩形内
          var x = dot.x,
              y = dot.y;
          return x > x1 && x < x2 && y > y1 && y < y2;
      },
      
      dotInPolygon: function(dot,coordinates,noneZeroMode) {
          // 默认启动none zero mode
          noneZeroMode=noneZeroMode||1;
          var x = dot.x,y=dot.y;
          var crossNum = 0;
          // 点在线段的左侧数目
          var leftCount = 0;
          // 点在线段的右侧数目
          var rightCount = 0;
          for(var i=0;i<coordinates.length-1;i++){
              var start = coordinates[i];
              var end = coordinates[i+1];
              
              // 起点、终点斜率不存在的情况
              if(start.x===end.x) {
                  // 因为射线向右水平，此处说明不相交
                  if(x>start.x) continue;
                  
                  // 从左侧贯穿
                  if((end.y>start.y&&y>=start.y && y<=end.y)){
                      leftCount++;
                      crossNum++;
                  }
                  // 从右侧贯穿
                  if((end.y<start.y&&y>=end.y && y<=start.y)) {
                      rightCount++;
                      crossNum++;
                  }
                  continue;
              }
              // 斜率存在的情况，计算斜率
              var k=(end.y-start.y)/(end.x-start.x);
              // 交点的x坐标
              var x0 = (y-start.y)/k+start.x;
              // 因为射线向右水平，此处说明不相交
              if(x>x0) continue;
              
              if((end.x>start.x&&x0>=start.x && x0<=end.x)){
                  crossNum++;
                  if(k>=0) leftCount++;
                  else rightCount++;
              }
              if((end.x<start.x&&x0>=end.x && x0<=start.x)) {
                  crossNum++;
                  if(k>=0) rightCount++;
                  else leftCount++;
              }
          }

          return noneZeroMode===1?leftCount-rightCount!==0:crossNum%2===1;
      }
  }


  
  function Event(type, target) {
      this.type = type;
      this.returnValue = true;
      this.target = target || null;
      this.currentTarget = null;
  };
  // 自定义事件
  function AttachEvent(){
      
  };

  AttachEvent.prototype.addEventListener = function(type,handler){
      if(!utils.isFunction(handler)){
          return false;
      }
      !this._listeners && (this._listeners = {});
      var l = this._listeners,uid;
      
      type.indexOf("on") != 0 && (type = "on"+type);
      typeof l[type] != 'object' && (l[type] = {});
      uid = utils.guid();
      handler.hashCode = uid;
      l[type][uid] = handler;
  }

  AttachEvent.prototype.removeEventListener = function(type,handler){
      if(utils.isFunction(handler)){
          handler = handler.hashCode;
      }else if(!utils.isString(handler)){
          return false;
      }
      !this._listeners && (this._listeners = {});
      type.indexOf("on") != 0 && (type = "on"+type);
      var l=this._listeners;
      if(!l[type]){
          return false;
      }
      l[type][handler] && delete l[type][handler];
  }

  AttachEvent.prototype.dispatchEvent = function(event,options){
      if(utils.isString(event)){
          event = new Event(event);
      }
      !this._listeners && (this._listeners = {});
      options = options || {};
      var l = this._listeners,t=event.type;
      event.target = event.target || this;
      event.currentTarget = this;
      t.indexOf('on') != 0 && (t = "on"+t);
      utils.isFunction(l[t]) && l[t].apply(this,arguments);
      if(typeof l[t] == 'object'){
          for(var i in l[t]){
              l[t][i].apply(this, arguments);
          }
      }
      return event.returnValue;
  }

  AttachEvent.prototype.stop = function(){
      var evt = window.event;
      evt.stopPropagation();
  }

  AttachEvent.prototype.default = function(){
      var evt = window.event;
      evt.preventDefault();
  }

  // 资源管理类
  function Store(imgUrls){
      this.imgUrls = imgUrls;
      this.iconList = {complete:false, icons:{}};
  }

  Store.prototype.preLoad = function(callback){
      var urls = this.imgUrls,
          len = urls.length,
          iconList = this.iconList;
      var self = this;
      if(!len) throw new Error("未传入icon");
      if(this.iconList.complete){
          callback();
      }else{
          var cnt = 1;
          for(var i=0;i<len;i++){
              
              iconList[i] = new Image();
              iconList[i].src = urls[i];
              iconList[i].onload = function(){
                  var imgNm = this.src.replace(/(.*\/)*([^.]+).*/ig,"$2");
                  cnt ++;
                  self.iconList.icons[imgNm] = this;
                  if(cnt == len){
                      self.iconList.complete = true;
                      callback();
                  }
              }

              iconList[i].onerror = function(){
                cnt ++;
                if(cnt == len){
                    self.iconList.complete = true;
                    callback();
                }
              }
          }
      }

  }

  // 提供控制单元
  function OverlaySetControl(overlaySet){
      this.overlaySet = overlaySet;
      this.store = overlaySet.store;
  }

  OverlaySetControl.prototype.dataSet = function(sourceData){
      if(!sourceData) return false;
      var overlayPixel =null;
      var list = [];
      var bmap = this.overlaySet.config.bmap;
      var iconList = this.store.iconList.icons;
     
      for(var i=0,len=sourceData.length;i<len;i++){
          var item = sourceData[i];
          
          overlayPixel = bmap.pointToPixel( new BMap.Point(item.lonBd,item.latBd));
          list.push({
              x:overlayPixel.x,
              y:overlayPixel.y,
              item:item,
              rotate:item.direction,
              width:24,
              height:24,
              vecNo:item._id,
              lng:item.lonBd,
              lat:item.latBd,
              imgOrigin:iconList[item.type]
          })
      }
   
      return list;
  }

  OverlaySetControl.prototype.fresh = function(sourceData){
      this.sourceData = sourceData;
      if(!this.loading)
          this._update(sourceData);
  }

  OverlaySetControl.prototype._update = function(sourceData){
      this.loading = true;
      this.overlaySet.clear();
      sourceData = this.dataSet(sourceData);
      if(!sourceData)
          return false;
      for(var i=0,len=sourceData.length;i<len;i++){
          var size = sourceData.length-1;

          if(size < 0) break;
              var obj = this.overlaySet.drawImg(
                  sourceData[i].x, 
                  sourceData[i].y, 
                  sourceData[i].imgOrigin,
                  sourceData[i].rotate, 
                  sourceData[i].width, 
                  sourceData[i].height,
                  i
                  );
              obj.item = sourceData[i];
              obj.vecNo = sourceData[i].vecNo;
              obj.lng = sourceData[i].lng;
              obj.lat = sourceData[i].lat;
      }
      this.loading = false;
  }

  // canvas类
  var OverlaySetInstnce = null;
  function OverlaySet(config){
      var self = this;
      this.cnvs = null;
      this.ctx = null;
      this.config = config || {};
      this.elems = {};
      this.store = new Store(config.urls);
      // 图标预加载
      this.store.preLoad(function(){
          self.init();
      })
  }

  // 继承自定义事件
  utils._inherit(OverlaySet,AttachEvent);

  OverlaySet.prototype.init = function(){
      var self = this;
      var cnvs = this.cnvs = document.createElement("canvas"),
          ctx = this.ctx = cnvs.getContext("2d");
      var isDrag = false;
      var initPointer = null;

      this.control = new OverlaySetControl(this);

      cnvs.width = this.config.width;
      cnvs.height = this.config.height;
      cnvs.className = 'canvas-cover';

      this.config.elm.appendChild(cnvs);

      this.cnvsOffset = this.cnvs.getBoundingClientRect();
      
      var clickCnv = function(event){
          initPointer = null;
          if(isDrag) return false;
          var cnvsOffset = self.cnvsOffset;
          var _clientx = event.clientX - cnvsOffset.left;
          var _clienty = event.clientY - cnvsOffset.top;
          var elems = self.elems.drawImg.reverse();
          var mode;
          var drawimg = null;
         
          for(var i=0,len=elems.length;i<len;i++){
              if(!elems[i].cornerList) continue;
              mode = utils.dotInPolygon({x:_clientx,y:_clienty},elems[i].cornerList)
              if(mode){
                  drawimg = elems[i];
                  break;
              }
          }
          
          if(drawimg)
              self.emit('openInfowindow',drawimg);
          self.stop();
      }

      cnvs.addEventListener('click',clickCnv);

      var mousedownHandle = function(){
          // isDrag 防止mousedown/mouseup事件和click冲突
          isDrag = false;
          initPointer = {x:event.screenX,y:event.screenY};
      }

      cnvs.addEventListener('mousedown',mousedownHandle);

      var mousemoveHandle = function(event){
          if(initPointer != null && ( Math.abs(event.screenX - initPointer.x) > 0 || Math.abs(event.screenY - initPointer.y) > 0) ){
              isDrag = true;
          }
          var elems = self.elems.drawImg;
          if(!elems) return false;
          var mode;
          var cnvsOffset = self.cnvsOffset;
          var _clientx = event.clientX - cnvsOffset.left;
          var _clienty = event.clientY - cnvsOffset.top;
          for(var i=0,len=elems.length;i<len;i++){
              if(!elems[i].cornerList) continue;
              mode = utils.dotInPolygon({x:_clientx,y:_clienty},elems[i].cornerList);
              if(mode){
                  !this.style.cursor && (this.style.cursor = "pointer");
                  break;
              }else{
                  this.style.cursor && (this.style.cursor = "");
              }
          }
      }
      cnvs.addEventListener('mousemove',mousemoveHandle);
  }

  OverlaySet.prototype.set = function(key,obj){
      if(!this.elems[key]){
          this.elems[key] = [];
      }
      this.elems[key].push(obj);
  }

  OverlaySet.prototype.get = function(key){
      return this.elems[key] || false;
  }

  OverlaySet.prototype.drawImg = function(xPixel, yPixel, img, rotateAng, imgWidth, imgHeight){
      var ctx = this.ctx;
      var _index = Array.prototype.slice.call(arguments,-1)[0];
     
      rotateAng = rotateAng*Math.PI/180 || 0;

      var drawImgs = this.elems.drawImg;
      var oldDrawimg = drawImgs && drawImgs[_index] || null;
      
      if(oldDrawimg && oldDrawimg.available){
          oldDrawimg.draw(ctx, xPixel, yPixel, img, rotateAng, imgWidth, imgHeight);
      }else{
          oldDrawimg =  new DrawImg(ctx, xPixel, yPixel, img, rotateAng, imgWidth, imgHeight);
          this.set('drawImg',oldDrawimg);
      }
      
      return oldDrawimg;
  }

  OverlaySet.prototype.clear = function(){
      var drawImage = this.elems.drawImg;
      this.ctx.clearRect(0, 0, this.cnvs.width,this.cnvs.height);
      if(!drawImage) return false;
      var len = drawImage.length;

      for(var i=0;i<len;i++){
          if(drawImage[i].available) continue;
          drawImage[i].reset();
      }
  }

  OverlaySet.prototype.update = function(data){
      let _this = this;
      data = data || this.control && this.control.sourceData;
      if(!data) return false;
      if(this.control){
        this.control.fresh(data);
      }else{
        var timer = setInterval(function(){
            if(_this.control != undefined && document.querySelector('.canvas-cover') != null){
                clearInterval(timer)
                _this.control.fresh(data);
                return
            }
        },13)
      }
  }

  // 绘制图片对象
  function DrawImg(ctx, xPixel, yPixel, img, rotateAng, imgWidth, imgHeight){
      this.type = "drawImage";
      this.available = true;
      this.draw(ctx, xPixel, yPixel, img, rotateAng, imgWidth, imgHeight);
  }
  //绘制图片
  DrawImg.prototype.draw = function(ctx, xPixel, yPixel, img, rotateAng, imgWidth, imgHeight){
      if(!ctx || !xPixel || !yPixel || !img) return false;
      this.available = false;
      this.calculateCorner(xPixel, yPixel, rotateAng, imgWidth, imgHeight,ctx);

      ctx.save();
      ctx.translate(xPixel, yPixel);
      ctx.rotate(rotateAng);
      ctx.drawImage(img,0,0,imgWidth,imgHeight,-imgWidth/2,-imgHeight/2,imgWidth,imgHeight);
      ctx.restore();
      
  }
  //计算四个角点坐标
  DrawImg.prototype.calculateCorner = function(xPixel, yPixel, rotateAng, imgWidth, imgHeight,ctx){
      var p1 = {x:xPixel-imgWidth/2,y:yPixel-imgHeight/2};
      var p2 = {x:xPixel+imgWidth-imgWidth/2,y:yPixel-imgHeight/2};
      var p3 = {x:xPixel+imgWidth-imgWidth/2,y:yPixel+imgHeight-imgHeight/2};
      var p4 = {x:xPixel-imgWidth/2,y:yPixel+imgHeight-imgHeight/2};

      var centerPt = {x:xPixel, y:yPixel};
      var pt1 = utils.getRotatedPoint(p1.x, p1.y, centerPt.x, centerPt.y, rotateAng);
      var pt2 = utils.getRotatedPoint(p2.x, p2.y, centerPt.x, centerPt.y, rotateAng);
      var pt3 = utils.getRotatedPoint(p3.x, p3.y, centerPt.x, centerPt.y, rotateAng);
      var pt4 = utils.getRotatedPoint(p4.x, p4.y, centerPt.x, centerPt.y, rotateAng);

      if(this.cornerList){
          this.cornerList.push(pt1);
          this.cornerList.push(pt2);
          this.cornerList.push(pt3);
          this.cornerList.push(pt4);
          this.cornerList.push(pt1);
      }else{
          this.cornerList = [pt1,pt2,pt3,pt4,pt1];
      }
  }

  //获取该对象的属性
  DrawImg.prototype.getData = function(){
      return this.cornerList;
  }

  DrawImg.prototype.reset = function(){
      this.cornerList && (this.cornerList.length = 0);
      this.available = true;
      this.vecNo = null;
      this.lng = null;
      this.lat = null;
  }

  // 复杂的自定义覆盖物
  function CanvasCover(bmap,opts){
      this.map = bmap;
      this.bounds = null;
      this.conf = opts || {}
  }

  CanvasCover.prototype = new BMap.Overlay();
  //实现 BMap.Overlay initialize方法
  CanvasCover.prototype.initialize = function(map){
      var size = this.map.getSize();
      var conf = this.conf;
      var elm = this.elm = document.createElement("div");
      var self = this;
      elm.style.position = 'absolute';
      elm.style.zIndex = new Date().getTime();
      elm.style.height = size.height+'px';
      elm.style.width = size.width+'px';

      conf.elm = elm;
      conf.width = size.width;
      conf.height = size.height;
      conf.bmap = this.map;
      // 保存实例
      OverlaySetInstnce = new OverlaySet(conf);
      OverlaySetInstnce.emit = function(name,data){
          self.dispatchEvent(name,data);
      }
      this.map.getPanes().mapPane.appendChild(elm);
      
      return elm;
  }
  // 实现 BMap.Overlay draw方法
  CanvasCover.prototype.draw = function(){
      var currentBounds = this.map.getBounds();
      
      if (currentBounds.equals(this.bounds)) {
          return;
      }
      this.bounds = currentBounds;
      var ne = this.map.pointToOverlayPixel(currentBounds.getNorthEast()),
          sw = this.map.pointToOverlayPixel(currentBounds.getSouthWest()),
          topY = ne.y,
          leftX = sw.x,
          h = sw.y - ne.y,
          w = ne.x - sw.x;
          
      this.elm.style.left = leftX + 'px';
      this.elm.style.top = topY + 'px';
      this.elm.style.width = w + 'px';
      this.elm.style.height = h + 'px';
  }

  CanvasCover.prototype.addEventListener = AttachEvent.prototype.addEventListener;
  CanvasCover.prototype.dispatchEvent = AttachEvent.prototype.dispatchEvent;

  CanvasCover.prototype.fresh = function(data){
      OverlaySetInstnce.update(data);
  }

  /*
      清空画布方法 
   */
  CanvasCover.prototype.clear = function(){
      OverlaySetInstnce.clear();
  }

  CanvasCover.prototype.track = function(vecNo){
      if(!utils.isString(vecNo)) return false;
      var elems = OverlaySetInstnce.elems.drawImg;
      var res = null;
      for(var i=0,len = elems.length;i<len;i++){
          if(elems[i].vecNo == vecNo){
              res = elems[i];
              break;
          }
          continue;
      }
      this.dispatchEvent("openInfowindow",res);
  }
  return CanvasCover;
})();

export default CanvasCover;