import Cookies from "js-cookie";

const TokenKey = "ZJDC-ZHYS-GOV-TOKEN";
const UserNameKey = "ZJDC-ZHYS-GOV-USER";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 2 });
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function getUserName() {
  return Cookies.get(UserNameKey);
}
