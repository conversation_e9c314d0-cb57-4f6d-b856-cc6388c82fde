/* 
	name:所属模块
	check:true展开/false隐藏
	message:展示内容 
*/
// type 0显示数据,1显示图片,2数据图片都显示
//装货前检查
const before =[{
	name: '车辆证照信息',
	text: "vecLic",
	check: true,
	message: [{
		name: "车牌号码",
		text:"vecNo",
		children: [{
			name: "牵引车号",
			text: "tracCd",
			type: 0
		}, {
			name: "挂车号",
			text: "traiCd",
			type: 0
		}]
  }, 
  {
		name: "车辆道路运输证",
		text:"opraLicCd",
		children: [{
			name: "牵引车道路运输证",
			text: "tracOpraLicCd",
			type: 2,
			licName: "8010.300-1"
		}, {
			name: "挂车道路运输证",
			text: "traiOpraLicCd",
			type: 2,
			licName: "8010.300-2"
		}]
	}, {
		name: "车辆行驶证",
		text:"drivLicCd",
		children: [{
			name: "牵引车行驶证",
			text: "tracDrivLicCd",
			type: 2,
			licName: '8010.302-1'
		}, {
			name: "挂车行驶证",
			text: "traiDrivLicCd",
			type: 2,
			licName: '8010.302-2'
		}]
	}, {
		name: "车载卫星定位系统",
		text: "satellite",
		desc:"卫星定位终端安装证书",
		type: 1,
		licName: '8010.303-1',
		children: null
  }
]
},   {
  name: '车辆检验信息',
  text: 'Date',
  check: true,
  message: [
    {
      name: "牵引车检验信息",
      text: "vecDate",
      children: [{
        name: "道路运输证审验有效期",
        text: "tracOpraLicExpiDt",
        type: 2,
        licName: '8010.300-1'
      }, {
        name: "道路运输证等级评定有效期",
        text: "tracOpraLicLvlExpiDt",
        type: 2,
        licName: "8010.300-1"
      }, {
        name: "行驶证检验有效期",
        text: "tracDrivLicExpiDt",
        type: 2,
        licName: "8010.302-1"
      }]
    },
    {
      name: "挂车检验信息",
      text: "traiDate",
      children: [{
        name: "道路运输证审验有效期",
        text: "traiOpraLicExpiDt",
        type: 2,
        licName: '8010.300-2'
      }, {
        name: "行驶证检验有效期",
        text: "traiDrivLicExpiDt",
        type: 2,
        licName: "8010.302-2"
      }]
    },
    {
      name: "罐体检验合格有效期",
      text: "cntrQualLicExpiDt",
      desc: "",
      type: 2,
      licName: "8010.500"
    }]
}, {
  name: '人员资质信息',
  text: 'pers',
  check: true,
  message: [
    {
      name: "驾驶员浙运安全码",
      text: "dvSecurityCode",
      desc: "",
      type: 4,
      licName: '',
      children: null
    },
    {
      name: "驾驶员从业资格证",
      text: "dvCd",
      desc: "",
      type: 2,
      licName: '8010.403-3',
      children: null
    }, {
      name: "押运员从业资格证",
      text: "scCd",
      desc: "",
      type: 2,
      licName: '8010.404-4',
      children: null
    }]
}, {
	name: '危险货物信息',
	text: 'rtePlan',
	check: true,
	message: [{
		name: "货物名称",
		text: "enchNm",
		desc:"",
		type: 0,
		children: null
	},{
		name: "货物类别",
		text: "enchCat",
		desc:"",
		type: 0,
		children: null
	},{
		name: "货物重量（KG）",
		text: "enchQty",
		desc:"",
		type: 0,
		children: null
	}, {
		name: "车辆罐体核定载质量/最大允许充装量",
		text: "cntrLoadQty",
		desc:"",
		type: 0,
		children: null
	}]
}, {
	name: '罐体检查',
	text: 'tank',
	check: true,
	message: [{
		name: "所装载的危险货物在罐式车辆罐体的适装介质列表范围内，或者满足可移动罐柜导则、罐箱适用代码的要求",
		text: "cntrMediaCheck",
		desc:"罐体检验报告",
		type: 1,
		licName: '8010.500',
		children: null
	}]
}, {
	name: '企业资质信息',
	text: "entp",
	check: true,
	message: [{
		name: "运输公司的道路运输经营许可证",
		text: "transLicCd",
		desc:"",
		type: 2,
		licName: '8010.203',
		children: null
  }, 
  // {
	// 	name: "外地驻甬企业经营登记备案记录",
	// 	text: "transRegiCert",
	// 	desc:"3个月内在宁波市交通运输局备案登记证明",
	// 	type: 1,
	// 	licName: '',
	// 	children: null
	// }, {
	// 	name: "剧毒化学品、民用爆炸物品、烟花爆竹、放射性物品以及危险废物",
	// 	text: "hazaWasteCd",
	// 	desc:"运输许可通行证及危废转移联单等",
	// 	type: 1,
	// 	licName: '',
	// 	children: null
  // }
]
}, {
	name: '现场安全检查',
	text: 'other',
	check: true,
	message: [{
		name: "现场安全检查完毕，符合充装要求",
		text: "onSite",
		desc:"",
		type: 1,
		children: null
	}]
}];
const after = [
  {
		name: "牵引车牌号码清晰无遮挡",
		text: "tracCdClear"
	}, {
		name: "挂车号码清晰无遮挡",
		text: "traiCdClear"
	}, {
		name: "车辆三角形标志灯及编号牌完好",
		text: "vecSignLight"
	}, {
		name: "车辆菱形标志牌图形和悬挂位置正确",
		text: "vecSignage"
	}, {
		name: "罐/车体外部无产品残留且无滴漏",
		text: "vecExtSafe"
	}, {
		name: "货物包装容器没有损坏或者泄漏",
		text: "cntrMar"
	}, {
		name: "罐体与装卸台的所有连接件已分离",
		text: "cntrDock"
	}, {
		name: "操作阀门已经置于闭止状态",
		text: "valveClose"
	}, {
		name: "罐式车辆罐体、可移动罐柜、罐箱的关闭装置处于关闭状态",
		text: "vecCntrInfo"
	}, {
		name: "罐车所有密封面、阀门、接管等无泄漏",
		text: "vecInfo"
	}, {
		name: "所有安全附件、装卸附件完好",
		text: "vecAnnex"
	}, {
		name: "没有其他影响安全的事项",
		text: "otherSafe"
	}
];
const unload = [
  {
    name: '货物运单信息',
    text: 'rtePlan',
    check: true,
    message: [{
      name: "收货人名称",
      text: "csneeWhseCt",
      desc:"",
      type: 0,
      children: null
    },{
      name: "收货人联系电话",
      text: "csneeWhseTel",
      desc:"",
      type: 0,
      children: null
    },{
      name: "收货人地址",
      text: "csneeWhseAddr",
      desc:"",
      type: 0,
      children: null
    },{
      name: "货物品名",
      text: "enchNm",
      desc:"",
      type: 0,
      children: null
    },{
      name: "货物UN编码",
      text: "enchUn",
      desc:"",
      type: 0,
      children: null
    },{
      name: "货物类别",
      text: "enchCat",
      desc:"",
      type: 0,
      children: null
    }, {
      name: "货物质量（KG）",
      text: "enchQty",
      desc:"",
      type: 0,
      children: null
    }]
  },{
    name: '车辆证照信息',
    text: "vecLic",
    check: true,
    message: [{
      name: "车牌号码",
      text:"vecNo",
      children: [{
        name: "牵引车牌号码",
        text: "tracCd",
        type: 0
      }, {
        name: "挂车号码",
        text: "traiCd",
        type: 0
      }]
    }, {
      name: "车辆道路运输证",
      text:"opraLicCd",
      children: [{
        name: "牵引车道路运输证",
        text: "tracOpraLicCd",
        type: 2,
        licName: "8010.300-1"
      }, {
        name: "挂车道路运输证",
        text: "traiOpraLicCd",
        type: 2,
        licName: "8010.300-2"
      }]
    }, {
      name: "车辆行驶证",
      text:"drivLicCd",
      children: [{
        name: "牵引车行驶证",
        text: "tracDrivLicCd",
        type: 2,
        licName: '8010.302-1'
      }, {
        name: "挂车行驶证",
        text: "traiDrivLicCd",
        type: 2,
        licName: '8010.302-2'
      }]
    }]
  }, {
    name: '车辆检验信息',
    text: 'Date',
    check: true,
    message: [{
      name: "牵引车检验信息",
      text:"tracDate",
      children: [{
        name: "道路运输证审验有效期",
        text: "tracOpraLicExpiDt",
        type: 2,
        licName: '8010.300-1'
      }, {
        name: "道路运输证等级评定有效期",
        text: "tracOpraLicLvlExpiDt",
        type: 2,
        licName: "8010.300-1"
      }, {
        name: "行驶证检验有效期",
        text: "tracDrivLicExpiDt",
        type: 2,
        licName: "8010.302-1"
      }]
    },{
      name: "挂车检验信息",
      text:"traiDate",
      children: [{
        name: "道路运输证审验有效期",
        text: "traiOpraLicExpiDt",
        type: 2,
        licName: '8010.300-2'
      }, {
        name: "行驶证检验有效期",
        text: "traiDrivLicExpiDt",
        type: 2,
        licName: "8010.302-2"
      }]
    }, {
      name: "罐体检验信息",
      text: "cntrQualLicExpiDt",
      desc:"",
      type: 2,
      licName: "8010.500"
    }]
  }, {
    name: '人员资质信息',
    text: 'pers',
    check: true,
    message: [{
      name: "驾驶员从业资格证",
      text: "dvCd",
      desc:"",
      type: 2,
      licName: '8010.403-3',
      children: null
    }, {
      name: "押运员从业资格证",
      text: "scCd",
      desc:"",
      type: 2,
      licName: '8010.404-4',
      children: null
    }]
  }, {
    name: '罐体介质信息',
    text: 'tank',
    check: true,
    message: [{
      name: "所装载的危险货物在罐式车辆罐体的适装介质列表范围内，或者满足可移动罐柜导则、罐箱适用代码的要求",
      text: "cntrMediaCheck",
      desc:"罐体检验报告",
      type: 1,
      licName: '8010.500',
      children: null
    }]
  }, {
    name: '企业资质信息',
    text: "entp",
    check: true,
    message: [{
      name: "运输公司的道路运输经营许可证",
      text: "carrierBssCd",
      desc:"",
      type: 2,
      licName: '8010.203',
      children: null
    }, {
      name: "外地驻甬企业经营登记备案记录",
      text: "transRegiCert",
      desc:"3个月内在宁波市交通运输局备案登记证明",
      type: 1,
      licName: '',
      children: null
    }, {
      name: "剧毒化学品、民用爆炸物品、烟花爆竹、放射性物品以及危险废物",
      text: "hazaWasteCd",
      desc:"运输许可通行证及危废转移联单等",
      type: 1,
      licName: '',
      children: null
    }]
  }, {
    name: '现场安全检查',
    text: 'other',
    check: true,
    message: [{
      name: "现场安全检查完毕，符合充装要求",
      text: "onSite",
      desc:"",
      type: 1,
      children: null
    }]
  }
];
export { before, after, unload };
