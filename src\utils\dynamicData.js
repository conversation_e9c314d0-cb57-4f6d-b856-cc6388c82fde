// 所有的违章处置操作类型
const alarmDealActions = {
  13: { label: "等待处罚", value: 13 },
  14: { label: "已经处罚", value: 14 },
  15: { label: "卫星定位故障", value: 15 },
  16: { label: "车辆故障维修", value: 16 },
  17: { label: "等待装卸", value: 17 },
  18: { label: "通报公司", value: 18 },
  19: { label: "警告教育", value: 19 },
  20: { label: "双驾双押", value: 20 },

  21: { label: "交通管制", value: 21 },
  22: { label: "道路维修", value: 22 },
  23: { label: "交通拥堵", value: 23 },
  24: { label: "临时休整", value: 24 },
  25: { label: "核实撤销", value: 25 },

  30: { label: "立即劝离", value: 30 },
  31: { label: "等红绿灯", value: 31 },
  32: { label: "等待登记", value: 32 },

  33: { label: "卫星定位信号干扰", value: 33 },
  34: { label: "卫星定位推送延迟", value: 34 },
  35: { label: "高速/高架行驶", value: 35 },
  36: { label: "超出管辖范围", value: 36 },
  37: { label: "通行证未更新", value: 37 },
  39: { label: "已处理", value: 39 },

  40: { label: "其他", value: 40 },
  50: { label: "巡逻车现场处置", value: 50 },

  51: { label: "短信通知", value: 51 },
  52: { label: "电话劝离", value: 52 },
};
// 所有的违章处置操作类型
const allAlarmDealOptions = Object.keys(alarmDealActions).map(function(key){return alarmDealActions[key]});

// 对应违章的操作类型
const alarmDealOptions = {
  default: [13, 14, 15, 16, 17, 18, 19, 20, 33, 34, 35, 36, 39, 40],
  // 偏离路线
  "2550.170.170": [13, 14, 15, 16, 19, 21, 22, 18, 33, 34, 36,37,40],
  // 疲劳驾驶 value: "2550.7120.180"
  "2550.7120.180": [13, 14, 15, 19, 20, 23, 18, 33, 34, 40],
  // 超速报警", value: "2550.160.150"
  "2550.160.150": [13, 14, 15, 19, 18, 33, 34, 35, 40],
  // 异常停车预警", value: "2550.170.275"
  "2550.170.275": [13, 14, 15, 16, 17, 19, 21, 22, 23, 24, 33, 34, 36, 40],
  // 无GPS报警", value: "2550.7120.160"
  "2550.7120.160": [15, 18, 19, 33, 34, 39, 40],
  // 牵引车未备案报警", value: "2550.7120.155"
  "2550.7120.155.100": [13, 14, 25, 18, 33, 34, 40],
  // 挂车未备案报警
  "2550.7120.155.105": [13, 14, 25, 18, 33, 34, 40],
  // GPS轨迹缺失报警
  "2550.7120.165": [18, 19, 13, 14, 33, 34, 15, 40],
  // 临江互通闯禁报警,高速口闯禁报警
  "2550.7120.200": [13, 14, 19, 18, 40, 21],
  // 实时处置
  realtime: [30, 23, 21, 22, 31, 16, 17, 32, 15, 24, 33, 34, 36, 50, 51, 52, 40],

  // 动态清零
  zeroCount: [52, 50]

  // 实时处置事后
  // realtimeAfter: [13, 14,19,30, 23, 21, 22, 31, 16, 17, 32, 15, 24, 33, 34, 36, 40]
};

export { alarmDealActions, allAlarmDealOptions, alarmDealOptions };
