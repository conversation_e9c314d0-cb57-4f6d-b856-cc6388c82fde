import axios from "axios";
import { Message } from "element-ui";
import store from "@/store";
import router from "@/router";
import * as $auth from "@/utils/auth";

// create an axios instance
const service = axios.create({
  baseURL: process.env.BASE_API, // api的base_url
  timeout: 1000 * 30, // request timeout
  // withCredentials: true,         // 表示跨域请求时是否需要使用凭证
  headers: {
    "Content-Type": "application/json; charset=utf-8"
  }
});

// request interceptor 请求前
service.interceptors.request.use(
  config => {
    // Do something before request is sent
    const token = store.getters.token;
    if (token) {
      config.headers["token"] = token; // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    }
    // if(config.method=='post'){
    // 	if (config.data instanceof FormData || config.data instanceof URLSearchParams) {
    // 		config.data.append('_t', Date.parse(new Date()) / 1000);
    // 	}else{
    // 		config.data = {
    // 			...config.data,
    // 			_t: Date.parse(new Date())/1000,
    // 		}
    // 	}
    // }else if(config.method=='get'){
    // 	if(config.params instanceof FormData || config.params instanceof URLSearchParams){
    // 		config.params.append('_t', Date.parse(new Date()) / 1000);
    // 	}else{
    // 		config.params = {
    // 			_t: Date.parse(new Date())/1000,
    // 			...config.params
    // 		}
    // 	}
    // }
    return config;
  },
  error => {
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// respone interceptor  响应前
service.interceptors.response.use(
  response => {
    /**
     * 下面的注释为 ，当code返回如下情况为权限有问题，登出并返回到登录页
     * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
     */
    if (response.data && response.data.code === 401) {
      // 401, token失效
      Message({
        message: "401,登录超时，请重新登录",
        type: "error"
      });
      store.dispatch("ClearCache").then(response => {
        $auth.removeToken();
        router.replace({
          path: "login",
          query: { redirect: router.currentRoute.fullPath }
        });
      });
      return Promise.reject("401,登录超时，请重新登录");
    } else if (response.data && response.data.code === 404) {
      Message({
        message: response.data.msg,
        type: "error"
      });
      return Promise.reject(response.data.msg);
    } else if (response.data && response.data.code === 500) {
      Message({
        message: response.data.msg,
        type: "error"
      });
      return Promise.reject(response.data.msg);
    }
    return response.data;
  },
  error => {
    console.log("err" + error); // for debug

    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 返回 401 清除token信息并跳转到登录页面
          store.dispatch("ClearCache").then(response => {
            $auth.removeToken();
            router.replace({
              path: "login",
              query: { redirect: router.currentRoute.fullPath }
            });
          });
          break;
        case 404:
          // router.push("/404");
          break;
        case 500:
          // router.push("/500");
          break;
        case 504:
          // router.push("/500");
          break;
        default:
      }
    }
    return Promise.reject(error);
  }
);

export default service;
