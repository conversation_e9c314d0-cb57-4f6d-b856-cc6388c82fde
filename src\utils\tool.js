import store from "@/store";
// import Vue from 'vue'
// import { Message } from 'element-ui'
// Vue.component(Message.name, Message)

function getWindowClientHeight() {
  let winHeight = 0;
  if (window.innerHeight) winHeight = window.innerHeight;
  else if (document.body && document.body.clientHeight)
    winHeight = document.body.clientHeight;
  return winHeight;
}

function getWindowClientWidth() {
  let winWidth = 0;
  if (window.innerHeight) winWidth = window.innerWidth;
  else if (document.body && document.body.clientWidth)
    winWidth = document.body.clientWidth;
  return winWidth;
}

export function getClientHeight() {
  return getWindowClientHeight();
}

export function getTableHeight() {
  return getWindowClientHeight() - 220;
}

export function getClientWidth() {
  return getWindowClientWidth();
}

//权限判断
export function hasPermission(permission) {
  var permissions = store.state.user.permissions;
  if (permissions && permissions.indexOf(permission) > -1) {
    return true;
  } else {
    return false;
  }
}

/**
 * 获取uuid
 */
export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, c => {
    return (c === "x" ? (Math.random() * 16) | 0 : "r&0x3" | "0x8").toString(
      16
    );
  });
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
  return (
    JSON.parse(sessionStorage.getItem("permissions") || "[]").indexOf(key) !==
      -1 || false
  );
}

//ajax错误处理
export const catchError = function(error) {
  let _this = this;
  if (error.response) {
    switch (error.response.status) {
      case 400:
        _this.$message({
          message: error.response.data.msg || "请求参数异常",
          type: "error"
        });
        break;
      case 401:
        //   sessionStorage.removeItem('user');
        _this.$message({
          message: error.response.data.msg || "密码错误或账号不存在！",
          type: "warning",
          onClose: function() {
            location.reload();
          }
        });
        break;
      case 403:
        _this.$message({
          message: error.response.data.msg || "无访问权限，请联系企业管理员",
          type: "warning"
        });
        break;
      default:
        _this.$message({
          message: error.response.data.msg || "服务端异常，请联系技术支持",
          type: "error"
        });
    }
  }
  return Promise.reject(error);
};

//比较2个对象是否相同
// var obj1 = {id:1,name:"张三"}
// var obj2 = {id:2,name:"李四"}
// var obj3 = {id:1,name:"张三",age:25}
// var obj4 = {id:1,name:"张三"}
// console.log(isObjEqual(obj1,obj2));//false
// console.log(isObjEqual(obj1,obj3));//false
// console.log(isObjEqual(obj1,obj4));//true
export function isObjEqual(o1, o2) {
  if (!o2 || !o1) {
    return false;
  }
  delete o1.__ob__;
  delete o2.__ob__;
  // delete o1.subItems;
  // delete o2.subItems;
  var props1 = Object.getOwnPropertyNames(o1);
  var props2 = Object.getOwnPropertyNames(o2);
  for (var i = 0, max = props1.length; i < max; i++) {
    var propName = props1[i];
    if (
      propName != "subItems" &&
      o2[propName] &&
      o1[propName] !== o2[propName]
    ) {
      return false;
    }
  }
  return true;
}

/*
 *获取 object 类型
 */
export function getObjType(obj) {
  //tostring会返回对应不同的标签的构造函数
  var toString = Object.prototype.toString;
  var map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object"
  };
  if (obj instanceof Element) {
    return "element";
  }
  return map[toString.call(obj)];
}

/*
 *将一个或多个对象的内容合并到目标对象
 */
export function extendObj() {
  /*
   *target被扩展的对象
   *length参数的数量
   *deep是否深度操作
   */
  var options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false; // target为第一个参数，如果第一个参数是Boolean类型的值，则把target赋值给deep // deep表示是否进行深层面的复制，当为true时，进行深度复制，否则只进行第一层扩展 // 然后把第二个参数赋值给target

  if (typeof target === "boolean") {
    deep = target;
    target = arguments[1] || {}; // 将i赋值为2，跳过前两个参数

    i = 2;
  } // target既不是对象也不是函数则把target设置为空对象。

  if (typeof target !== "object" && getObjType(target) !== "function") {
    target = {};
  } // 开始遍历需要被扩展到target上的参数

  for (; i < length; i++) {
    // 处理第i个被扩展的对象，即除去deep和target之外的对象
    if ((options = arguments[i]) != null) {
      // 遍历第i个对象的所有可遍历的属性
      for (name in options) {
        // 根据被扩展对象的键获得目标对象相应值，并赋值给src
        src = target[name]; // 得到被扩展对象的值
        copy = options[name]; // 这里为什么是比较target和copy？不应该是比较src和copy吗？

        if (target === copy) {
          continue;
        } // 当用户想要深度操作时，递归合并 // copy是纯对象或者是数组

        if (
          deep &&
          copy &&
          (getObjType(copy) === "object" ||
            (copyIsArray = getObjType(copy) === "array"))
        ) {
          // 如果是数组
          if (copyIsArray) {
            // 将copyIsArray重新设置为false，为下次遍历做准备。
            copyIsArray = false; // 判断被扩展的对象中src是不是数组
            clone = src && getObjType(src) == "array" ? src : [];
          } else {
            // 判断被扩展的对象中src是不是纯对象
            clone = src && getObjType(src) == "object" ? src : {};
          } // 递归调用extend方法，继续进行深度遍历

          target[name] = extendObj(deep, clone, copy); // 如果不需要深度复制，则直接把copy（第i个被扩展对象中被遍历的那个键的值）
        } else if (copy !== undefined) {
          target[name] = copy;
        }
      }
    }
  } // 原对象被改变，因此如果不想改变原对象，target可传入{}

  return target;
}

export function copy(obj) {
  var newobj = {};
  for (var attr in obj) {
    newobj[attr] = obj[attr];
  }
  return newobj;
}

export function copyArr(arr) {
  let res = [];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] instanceof Array) {
      var newArr = [];
      var subArr = arr[i];
      for (var item in subArr) {
        newArr.push(subArr[item]);
      }
      res.push(newArr);
    } else {
      res.push(arr[i]);
    }
  }
  return res;
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = "id", pid = "parentId") {
  var res = [];
  var temp = {};
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i];
  }
  for (var k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]]["children"]) {
        temp[data[k][pid]]["children"] = [];
      }
      if (!temp[data[k][pid]]["_level"]) {
        temp[data[k][pid]]["_level"] = 1;
      }
      data[k]["_level"] = temp[data[k][pid]]._level + 1;
      temp[data[k][pid]]["children"].push(data[k]);
    } else {
      res.push(data[k]);
    }
  }
  return res;
}

export function formatDate(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  if (!time) {
    return "";
  }

  let fmt = cFormat || "yyyy-MM-dd HH:mm:ss";

  let date;
  if (typeof time === "object") {
    date = time;
  } else if (typeof time === "string") {
    date = new Date(time);
  } else {
    date = new Date(parseInt(time));
  }

  var o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours() % 12 == 0 ? 12 : date.getHours() % 12, //小时
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds() //毫秒
  };
  var week = {
    "0": "\u65e5",
    "1": "\u4e00",
    "2": "\u4e8c",
    "3": "\u4e09",
    "4": "\u56db",
    "5": "\u4e94",
    "6": "\u516d"
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "\u661f\u671f"
          : "\u5468"
        : "") + week[date.getDay() + ""]
    );
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
}
// 通行证季度
export function getQuartDate() {
  let validDateOptions = [];
  let now = new Date(),
    nowYear = now.getFullYear(),
    nowMon = now.getMonth() + 1,
    nowDate = now.getDate();

  //显示两个季度
  let start, end, nextStart, nextEnd, nextYear;

  //第一个季度时间跨度
  start = nowYear + "-" + nowMon + "-" + nowDate;
  end =
    nowYear +
    "-" +
    parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3))) +
    "-" +
    getDateLen(
      nowYear,
      parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)))
    );

  start = formatDate(new Date(start), "yyyy-MM-dd");
  end = formatDate(new Date(end), "yyyy-MM-dd");

  validDateOptions.push({
    label: "当前季度：" + start + "~" + end,
    value: start + "," + end
  });

  //第二个时间跨度,需要判断是否跨年份
  if (3 < nowMon / 3 && nowMon / 3 <= 4) {
    nextYear = nowYear + 1;
    nextStart = nextYear + "-" + "01" + "-" + "01";
    nextEnd =
      nextYear +
      "-" +
      parseInt(nowMon + (3 - nowMon)) +
      "-" +
      getDateLen(nextYear, 3);
  } else {
    nextStart =
      nowYear +
      "-" +
      (nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 1) +
      "-" +
      "01";
    nextEnd =
      nowYear +
      "-" +
      parseInt(nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3) +
      "-" +
      getDateLen(
        nowYear,
        nowMon + (3 - (nowMon % 3 == 0 ? 3 : nowMon % 3)) + 3
      );
  }
  nextStart = formatDate(new Date(nextStart), "yyyy-MM-dd");
  nextEnd = formatDate(new Date(nextEnd), "yyyy-MM-dd");
  validDateOptions.push({
    label: "下一季度：" + nextStart + "~" + nextEnd,
    value: nextStart + "," + nextEnd
  });
  //计算指定月份的天数
  function getDateLen(y, m) {
    var allDay = new Date(y, m, 0).getDate();
    return allDay;
  }
  return validDateOptions;
}

// 字符串格式的时间转换成几秒前、几分钟前、几小时前等格式
export function getDateDiff(dateStr) {
  //字符串转换为时间戳
  function getDateTimeStamp(dateStr) {
    return Date.parse(dateStr.replace(/-/gi, "/"));
  }
  var publishTime = getDateTimeStamp(dateStr) / 1000,
    d_seconds,
    d_minutes,
    d_hours,
    d_days,
    timeNow = parseInt(new Date().getTime() / 1000),
    d,
    date = new Date(publishTime * 1000),
    Y = date.getFullYear(),
    M = date.getMonth() + 1,
    D = date.getDate(),
    H = date.getHours(),
    m = date.getMinutes(),
    s = date.getSeconds();
  //小于10的在前面补0
  if (M < 10) {
    M = "0" + M;
  }
  if (D < 10) {
    D = "0" + D;
  }
  if (H < 10) {
    H = "0" + H;
  }
  if (m < 10) {
    m = "0" + m;
  }
  if (s < 10) {
    s = "0" + s;
  }

  d = timeNow - publishTime;
  d_days = parseInt(d / 86400);
  d_hours = parseInt(d / 3600);
  d_minutes = parseInt(d / 60);
  d_seconds = parseInt(d);

  // if(d_days > 0 && d_days < 3){
  //     return d_days + '天前';
  // }else if(d_days <= 0 && d_hours > 0){
  //     return d_hours + '小时前';
  // }else
  if (d_hours <= 0 && d_minutes > 0) {
    return d_minutes + "分钟前";
  } else if (d_seconds < 60) {
    if (d_seconds <= 0) {
      return "刚刚";
    } else {
      return d_seconds + "秒前";
    }
  } else if (d_days >= 3 && d_days < 30) {
    return M + "-" + D + "&nbsp;" + H + ":" + m;
  } else if (d_days >= 30) {
    return Y + "-" + M + "-" + D + "&nbsp;" + H + ":" + m;
  }
}

/**
 * 根据json数据绘制线路
 *
 * @param {*} map 地图对象
 * @param {*} lines 路线json,多条线路：[[{...},{...},{...}],[{...},{...},{...}]]二维数组，单条线路:[{...},{...},{...}]一维数组
 * @param {*} polylineName 绘制线路的name
 * @param {*} style 线路样式
 * @param {*} isSetViewport 是否设置线路为可视区域
 * @param {*} callback 回调函数
 * @returns null 或 {
 *  pointes:[]，所有线路点集合
 *  lines:[]，所有线路
 * }
 */
export const createPolylineByJSON = ({
  map,
  lines,
  polylineName,
  style,
  isSetViewport = false,
  callback
}) => {
  if (!map || !lines || !lines.length) {
    return;
  }

  let linesArr = JSON.parse(lines);
  if (!Array.isArray(linesArr)) {
    return;
  }
  // 若是一维数组则统一转成二维数组
  if (!Array.isArray(linesArr[0])) {
    linesArr = [linesArr];
  }

  let allPoints = [];
  let drawLines = [];
  linesArr.forEach(lineArrTp => {
    let points = [];
    lineArrTp.forEach(point => {
      let po = new BMap.Point(point.lng, point.lat);
      points.push(po);
      allPoints.push(po);
    });
    let polyline = new BMap.Polyline(points, style || {});
    polylineName && (polyline.name = polylineName);
    drawLines.push(polyline);
    // map.addOverlay(polyline);
    if (callback) {
      callback(polyline);
    }
  });
  isSetViewport && map.setViewport(allPoints);
  return {
    pointes: allPoints,
    lines: drawLines
  };
};

// 浏览器判断是否全屏
export const fullscreenToggel = ele => {
  if (fullscreenEnable()) {
    exitFullScreen();
  } else {
    reqFullScreen(ele);
  }
};

// esc监听全屏
export const listenfullscreen = callback => {
  function listen() {
    callback && callback();
  }
  document.addEventListener("fullscreenchange", function() {
    listen();
  });
  document.addEventListener("mozfullscreenchange", function() {
    listen();
  });
  document.addEventListener("webkitfullscreenchange", function() {
    listen();
  });
  document.addEventListener("msfullscreenchange", function() {
    listen();
  });
};

// 取消全屏监听
export const removeListenfullscreen = callback => {
  function listen() {
    callback && callback();
  }
  document.removeEventListener("fullscreenchange", function() {
    listen();
  });
  document.removeEventListener("mozfullscreenchange", function() {
    listen();
  });
  document.removeEventListener("webkitfullscreenchange", function() {
    listen();
  });
  document.removeEventListener("msfullscreenchange", function() {
    listen();
  });
};

// 浏览器判断是否全屏
export const fullscreenEnable = () => {
  var isFullscreen =
    document.isFullScreen ||
    document.mozIsFullScreen ||
    document.webkitIsFullScreen;
  return isFullscreen;
};

// 浏览器全屏
export const reqFullScreen = ele => {
  if (ele) {
    if (ele.requestFullscreen) {
      ele.requestFullscreen();
    } else if (ele.webkitRequestFullScreen) {
      ele.webkitRequestFullScreen();
    } else if (ele.mozRequestFullScreen) {
      ele.mozRequestFullScreen();
    } else if (ele.msRequestFullscreen) {
      // IE11
      ele.msRequestFullscreen();
    }
  } else {
    if (document.documentElement.requestFullScreen) {
      document.documentElement.requestFullScreen();
    } else if (document.documentElement.webkitRequestFullScreen) {
      document.documentElement.webkitRequestFullScreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    }
  }
};

// 浏览器退出全屏
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen();
  }
};



/**
* 加法运算，避免数据相加小数点后产生多位数和计算精度损失。
*
* @param num1加数1 | num2加数2
*/
export function numAdd(num1, num2) {
  var baseNum, baseNum1, baseNum2;
  try {
      baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
      baseNum1 = 0;
  }
  try {
      baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
      baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  return (num1 * baseNum + num2 * baseNum) / baseNum;
};


/**
* 减法运算，避免数据相减小数点后产生多位数和计算精度损失。
*
* @param num1被减数  |  num2减数
*/
export function numSub(num1, num2) {
  var baseNum, baseNum1, baseNum2;
  var precision;// 精度
  try {
      baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
      baseNum1 = 0;
  }
  try {
      baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
      baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;
  return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);
};

/**
* 乘法运算，避免数据相乘小数点后产生多位数和计算精度损失。
*
* @param num1被乘数 | num2乘数
*/
export function numMulti(num1, num2) {
  var baseNum = 0;
  try {
      baseNum += num1.toString().split(".")[1].length;
  } catch (e) {
  }
  try {
      baseNum += num2.toString().split(".")[1].length;
  } catch (e) {
  }
  return Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", "")) / Math.pow(10, baseNum);
};


/*
* 除法运算，避免数据相除小数点后产生多位数和计算精度损失。
*
* @param num1被除数 | num2除数
*/
export function numDiv(num1, num2) {
  var baseNum1 = 0, baseNum2 = 0;
  var baseNum3, baseNum4;
  try {
      baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
      baseNum1 = 0;
  }
  try {
      baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
      baseNum2 = 0;
  }

  baseNum3 = Number(num1.toString().replace(".", ""));
  baseNum4 = Number(num2.toString().replace(".", ""));
  return (baseNum3 / baseNum4) * Math.pow(10, baseNum2 - baseNum1);
  
};