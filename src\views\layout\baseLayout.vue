<template>
	<div class="app-wapper clearfix">
		<header class="app-header">
			<navbar></navbar>
		</header>
		<div class="app-content">
			<div class="app-main-container">
				<!-- <tags-view></tags-view> -->
				<div v-if="$route.meta.isIframe" class="app-main-component">
					<iframe :src="$route.meta.iframeUrl"
						width="100%" height="100%" frameborder="0" scrolling="yes">
					</iframe>
				</div>
				<app-main v-else class="app-main-component"></app-main>
			</div>
		</div>
		<back-to-top></back-to-top>
	</div>
</template>

<script>
import { Navbar,Sidebar,AppMain,TagsView} from '@/views/layout/components'
import BackToTop from '@/components/BackToTop'
import { mapGetters } from 'vuex'

export default {
	name:'Layout',
	components:{
		Sidebar,
		Navbar,
		TagsView,
		App<PERSON>ain,
		BackToTop
	},
	computed:{
		...mapGetters([
            'sidebar'
        ])
	}
}

</script>

<style scoped>
.sidebar-container, .app-main-container{
	transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
}
.sidebar-header{
    display: block;
    height: 50px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    font-size: 18px;
    font-weight: 600;
    background-color: #25476a;
    color: #fff;
}
</style>

