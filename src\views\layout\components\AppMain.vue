<template>
	<section class="app-main">
		<transition name="fade" mode="out-in">
			<template v-if="$route.meta.keepAlive">
				<keep-alive>
					<router-view></router-view>
				</keep-alive>
			</template>
			<router-view v-else></router-view>
		</transition>
	</section>
</template>

<script>
export default {
	name:'AppMain',
	data (){
		return {
			
		}
	}
}
</script>

<style scoped>
.app-main{
	padding:0;
	min-height: 100%;
}
</style>