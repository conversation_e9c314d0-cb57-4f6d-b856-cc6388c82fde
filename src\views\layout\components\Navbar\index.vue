<template>
  <div class="navbar-container clearfix">
    <router-link to="/" class="topbar-logo">
      <strong>镇海区危险化学品道路运输监管系统</strong>
      <sup style="font-size: 12px">v6.0</sup>
    </router-link>
    <Topbar :top-menu-list="topMenuList"></Topbar>
    <div class="navbar-right-menu-wape">
      <el-menu class="navbar-right-menu" mode="horizontal">
        <el-menu-item v-if="memorandumFlag" index="1" @click="showMemorandum()">
          备忘录
        </el-menu-item>
        <el-menu-item index="6" style="position: relative" @mouseover.native="showMiniProgams = true"
          @mouseout.native="showMiniProgams = false">
          <svg-icon icon-class="mini-programs" class-name="navbar-right-menu-icon" />
          <div v-show="showMiniProgams" class="navbar-right-menu-popup">
            <div>
              <img src="static/img/login/mini-progams.jpg" width="180" />
            </div>
            <p style="
                text-align: center;
                font-size: 12px;
                color: #939393;
                line-height: 15px;
                padding: 0;
                margin: 0;
              ">
              请用微信扫描二维码
            </p>
          </div>
        </el-menu-item>
        <el-submenu index="2" style="flex:1 0 120px;text-overflow: ellipsis;overflow: hidden;text-align: center;">
          <template slot="title">{{ name }}</template>
          <!-- <el-menu-item index="bindWeixin" @click="bindWeixin()"><i class="el-icon-connection"></i> 微信绑定</el-menu-item> -->
          <el-menu-item index="modifyPwd" @click="handleCommand('modifyPwd')"><i
              class="el-icon-edit"></i>修改密码</el-menu-item>
          <el-menu-item index="logout" @click="handleCommand('logout')"><i class="el-icon-back"></i>退出</el-menu-item>
          <el-menu-item index="try" @click="handleCommand('try')"><i class="el-icon-thumb"></i>试试新版</el-menu-item>
        </el-submenu>
        <el-menu-item index="1">
          <screenfull class="navbar-right-menu-item" title="全屏"></screenfull>
        </el-menu-item>
      </el-menu>
    </div>
    <el-dialog title="修改密码" :visible.sync="dialogVisibleOfPwd" width="50%" append-to-body>
      <div>
        <el-form :model="psdModifyForm" :rules="psdModifyFormRules" ref="psdModifyForm" label-width="100px"
          class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="name" label="账号名">
                <span>{{ $store.state.user.username }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="password" label="旧密码">
                <el-input v-model="psdModifyForm.password" size="small" type="password"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="newPassword" label="新密码">
                <el-input v-model="psdModifyForm.newPassword" size="small" placeholder="请输入新密码"
                  type="password"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item prop="newPasswordRepeat" label="确认密码">
                <el-input v-model="psdModifyForm.newPasswordRepeat" type="password" size="small"
                  placeholder="请再次输入新密码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleOfPwd = false">取 消</el-button>
        <el-button type="primary" @click="dialogOfPwdSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 绑定微信 -->
    <el-dialog :title="dialogVisibleOfBindWxTitle" @close="bindWxDialogCloseHandle" :visible.sync="dialogVisibleOfBindWx"
      width="40%" append-to-body>
      <div v-loading="bindWxLoading">
        <div v-if="!avatarUrlList">
          <div class="qrcode" ref="qrcode"></div>
        </div>
        <div v-else>
          <div v-show="showBindOrlist">
            <div class="qrcode" ref="qrcode"></div>
          </div>
          <div v-show="!showBindOrlist">
            <el-row :gutter="20">
              <el-col :sm="6" v-for="(item, index) in avatarUrlList" :key="index">
                <div class="avatar">
                  <img :src="item.wechatAvatarurl" :title="item.wechatNick" :alt="item.wechatNick" width="60"
                    height="60" />
                </div>
                <div class="unbindbtn">
                  <el-button type="text" size="mini"
                    @click="unbindWxHandle(item.wechatOpenId, item.wechatNick)">微信解绑</el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div slot="footer" class="text-right">
          <el-button type="primary" size="mini" @click="switchListOrBind">{{
            showBindOrlist ? "已绑定用户列表" : "绑定微信"
          }}</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="备忘录" :visible.sync="memorandumVisible" width="80%" append-to-body>
      <memorandum></memorandum>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Topbar from "./topbar";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SimpleTable from "@/components/SimpleTable";
import { getUserInfo } from "@/api/login";
import * as Tool from "@/utils/tool";
import * as $httpCommon from "@/api/common";
import memorandum from './memorandum';
import QRCode from "qrcodejs2";
import * as $httpM from "@/api/menorandum";

let Base64 = require("js-base64").Base64;
export default {
  name: "Navbar",
  data() {
    return {
      topMenuList: [],
      bindWxLoading: false,
      bindWxQRCodeUrl: "",
      showBindOrlist: false,
      avatarUrlList: null,
      showMiniProgams: false, //微信小程序
      dialogVisibleOfPwd: false,
      dialogVisibleOfBindWx: false,
      dialogVisibleOfBindWxTitle: "绑定微信",
      psdModifyForm: {
        password: "",
        newPassword: "",
        newPasswordRepeat: ""
      },
      psdModifyFormRules: {
        password: [
          { required: true, message: "请输入旧密码", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { validator: this.validatePwd, trigger: "blur" }
        ],
        newPasswordRepeat: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          {
            validator: this.validateNewPwd,
            message: "两次密码不一样！",
            trigger: "blur"
          },
          {
            validator: this.validateSamePwd,
            message: "两次密码不一样！",
            trigger: "blur"
          }
        ]
      },
      memorandumVisible: false,
      memorandumFlag: false
    };
  },
  components: {
    Hamburger,
    Topbar,
    Screenfull,
    SimpleTable,
    memorandum
  },
  computed: {
    ...mapGetters(["username", "name"]),
  },
  created() {
    //存放在session中退出浏览器会导致username和name删除，若删除的话重新根据token请求一次放到vuex中
    //gov3试试旧版的时候若本地存在缓存，用户信息不会更新，为了解决这个问题，需要每次都去请求用户信息
    // if (this.username == undefined || this.name == undefined) {
    getUserInfo().then(res => {
      if (res && res.code == 0) {
        this.$store.commit("SET_USERNAME", res.user.username);
        this.$store.commit("SET_MOBILE", res.user.mobile);
        this.$store.commit("SET_NAME", res.user.ipName);
      }
    });
    // }
    this.topMenuList = JSON.parse(
      sessionStorage.getItem("topMenuList") || "[]"
    );
  },
  mounted() {
    $httpM
      .judgeIsDljc()
      .then(res => {
        if (res.code == 0 && res.data) {
          this.memorandumFlag = true
        }
      })
      .catch(error => {
        console.log(error);
      });
  },
  methods: {
    showMemorandum() {
      this.memorandumVisible = true
    },
    // 右侧菜单事件
    handleCommand: function (command) {
      if (command == "logout") {
        this.$store.dispatch("LogOut").then(response => {
          if (response.code == 0) {
            location.reload();
          } else {
            this.$message({
              showClose: true,
              message: "退出失败",
              type: "error"
            });
          }
        });
      } else if (command == "modifyPwd") {
        this.dialogVisibleOfPwd = true;
        if (this.$refs["psdModifyForm"] !== undefined) {
          this.$refs["psdModifyForm"].resetFields();
        }
      } else if (command == 'try') {
        window.location.href = process.env.NEW_APP_URL
      }
    },

    // 验证密码是否符合标准
    validatePwd(rule, value, callback) {
      let ruleNum = 0;
      // 是数字
      let isDigit = /[0-9]+/;
      // isLowerCase 小写字母
      let isLowerCase = /[a-z]+/;
      // isUpperCase 大写字母
      let isUpperCase = /[A-Z]+/;
      // 特殊字符
      let isSpecial = /[^a-zA-Z0-9]+/;

      if (isDigit.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isLowerCase.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isUpperCase.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }
      if (isSpecial.test(this.psdModifyForm.newPassword)) {
        ruleNum++;
      }

      if (/\s+/.test(this.psdModifyForm.newPassword)) {
        return callback(new Error("密码不能含有空格"));
      } else if (this.psdModifyForm.newPassword.length < 8) {
        return callback(new Error("密码长度必须大于等于8位"));
      } else if (ruleNum < 3) {
        return callback(
          new Error(
            "密码至少包含数字、大写字母、小写字母及特殊字符的三种或三种以上，长度大于等于8位"
          )
        );
      } else {
        return callback();
      }
    },

    // 验证两次密码
    validateNewPwd(rule, value, callback) {
      if (
        this.psdModifyForm.newPassword !== this.psdModifyForm.newPasswordRepeat
      ) {
        return callback(new Error("两次密码不一样"));
      } else {
        return callback();
      }
    },

    //验证新密码和旧密码是否相同
    validateSamePwd(rule, value, callback) {
      if (this.psdModifyForm.password == this.psdModifyForm.newPasswordRepeat) {
        return callback(new Error("新密码和旧密码不能相同！"));
      } else {
        return callback();
      }
    },

    // 修改密码提交
    dialogOfPwdSubmit() {
      let _this = this;
      let postData = {};
      postData.password = Base64.encode(
        Base64.encode(Base64.encode(this.psdModifyForm.password))
      );
      postData.newPassword = Base64.encode(
        Base64.encode(Base64.encode(this.psdModifyForm.newPassword))
      );

      this.$refs.psdModifyForm.validate(valid => {
        if (valid) {
          $httpCommon
            .modifyPwd(postData)
            .then(response => {
              if (response.code == 0) {
                _this.$store.dispatch("LogOut").then(response => {
                  if (response.code == 0) {
                    location.reload();
                    _this.$message({
                      showClose: true,
                      message: "修改成功，请重新登录！",
                      type: "success"
                    });
                  } else {
                    _this.$message({
                      showClose: true,
                      message: "退出失败",
                      type: "error"
                    });
                  }
                });
              } else {
                this.$message({
                  showClose: true,
                  message: "修改失败：" + response.msg,
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          _this.$message.error("密码修改相关信息填写错误!");
        }
      });
    },
    // 绑定微信
    bindWeixin() {
      this.bindWxLoading = true;
      this.dialogVisibleOfBindWx = true;
      if (this.showBindOrlist) {
        this.dialogVisibleOfBindWxTitle = "绑定微信";
      } else {
        this.dialogVisibleOfBindWxTitle = "已绑定用户列表";
      }
      this.bindWxHandle();
    },
    // 绑定处理
    bindWxHandle() {
      $httpCommon.bindWx().then(res => {
        // 绑定微信二维码
        this.bindWxQRCodeUrl = res.data;
        // 绑定微信列表
        this.isbindWxHandle();
      });
    },
    // 绑定查询
    isbindWxHandle() {
      $httpCommon
        .isbind()
        .then(res => {
          let data = res.data;

          if (res.code == 0 && data.length) {
            this.avatarUrlList = data;
            this.dialogVisibleOfBindWxTitle = "已绑定用户列表";
            this.showBindOrlist = false;
            this.existUser = data.map(item => {
              return item.wechatUnionId;
            });
          } else {
            this.avatarUrlList = null;
            // 一个用户都没绑定，开启轮询
            this.refreshBind();
          }
          this.bindWxLoading = false;
          this.createdQRCode(this.bindWxQRCodeUrl);
        })
        .catch(err => {
          this.bindWxLoading = false;
          this.createdQRCode(this.bindWxQRCodeUrl);
        });
    },
    // 解绑处理
    unbindWxHandle(openId, wechatNick) {
      let params = { openId: openId };
      this.$confirm("是否解绑 " + wechatNick + " 微信用户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $httpCommon.unbind(params).then(res => {
            if (res.code == 0) {
              this.$message.success("解绑成功!");
              this.bindWeixin();
            } else {
              this.$message.error(res.msg || "解绑失败!");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消解绑！"
          });
        });
    },

    createdQRCode(url) {
      this.$nextTick(() => {
        if (!this.QRcode) {
          this.QRcode = new QRCode(this.$refs.qrcode, {
            text: url,
            width: 160,
            height: 160,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.L
          });
        } else {
          this.QRcode.clear();
          this.QRcode.makeCode(url);
        }
      });
    },
    switchListOrBind() {
      this.showBindOrlist = !this.showBindOrlist;
      if (this.showBindOrlist) {
        this.dialogVisibleOfBindWxTitle = "绑定微信";
        // 显示绑定二维码时，开启轮询
        this.refreshBind();
      } else {
        this.dialogVisibleOfBindWxTitle = "已绑定用户列表";
      }
    },
    // 关闭dialog时终止微信是否绑定轮询
    bindWxDialogCloseHandle() {
      clearTimeout(this.timer);
    },
    refreshBind() {
      // 二维码绑定是否成功轮询
      let dur = 3000;
      let isBind = false;
      let _refresh = () => {
        clearTimeout(this.timer);
        $httpCommon.isbind().then(res => {
          // 绑定微信二维码
          let data = res.data;
          let existUser = this.existUser;
          let len1 = data.length,
            i = 0;
          let unionId = null;

          for (i; i < len1; i++) {
            let item = data[i];
            unionId = item["wechatUnionId"];

            if (!existUser.includes(unionId)) {
              this.$message.success("绑定成功!");
              this.avatarUrlList = data;
              this.existUser = data.map(item => {
                return item.wechatUnionId;
              });
              this.switchListOrBind();
              isBind = true;
              break;
            }
            continue;
          }
        });
        if (isBind) return false;
        this.timer = setTimeout(_refresh, dur);
      };
      this.timer = setTimeout(_refresh, dur);
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar-container {
  line-height: 50px;
  border-radius: 0 !important;
  color: #fff;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;

  /* background: -webkit-gradient(linear,
      left bottom,
      left top,
      from(#0a284a),
      to(#063f86));
  background: linear-gradient(0deg, #0a284a, #063f86); */
  background: #000822 url("~static/img/dashboard/top.png") no-repeat center center;
  background-size: 100% 100%;
  display: -webkit-flex;
  display: flex;
  flex-wrap: nowrap;
}

.topbar-logo {
  display: block;
  height: 50px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-size: 20px;
  color: #fff;
  padding: 0 15px;
  white-space: nowrap;
  background: url("~static/img/dashboard/top-header.png") no-repeat center center;
  background-size: 100% 100%;
  /* background: -webkit-gradient(linear, left bottom, left top, from(#0A284A), to(#063F86));
    background: linear-gradient(0deg, #0A284A, #063F86); */
}

@media screen and (max-width: 800px) {
  .topbar-logo {
    display: none;
  }
}

.navbar-right-menu-wape {
  height: 50px;
  margin-right: 10px;
  line-height: 50px;
  flex: 0 0 220px;
  width: 220px;

  .navbar-right-menu {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    >* {
      flex: 0 40px;
      text-align: center;
    }

    // .navbar-right-menu-item {
    //   display: inline-block;
    //   vertical-align: top;
    //   padding: 0;
    //   cursor: pointer;
    //   height: 50px;
    //   box-sizing: border-box;
    //   flex: 0 40px;
    //   text-align: center;

    //   .el-dropdown-link {
    //     color: #fff;
    //   }
    // }
  }

  .navbar-right-menu-popup {
    position: absolute;
    top: 100%;
    left: -83px;
    padding: 8px;
    background-color: #fff;
    text-align: center;
    border: 1px solid rgb(236, 240, 245);
  }
}

.navbar-right-menu-icon {
  font-size: 20px;
}

.avatar img {
  border-radius: 50%;
}

.qrcode {
  width: 180px;
  height: 180px;
  padding: 10px;
  margin: 0 auto;
  background-color: #fff;
  border: 1px solid #3fefc6;
}
</style>
