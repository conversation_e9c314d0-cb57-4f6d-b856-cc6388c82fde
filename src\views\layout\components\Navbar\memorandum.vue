<!--  -->
<template>
  <div style="padding: 10px;">
    <!-- <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar> -->
    <div style="margin-bottom: 10px;">
      <div class="el-form-item__label">完成状态</div>
      <el-select style="margin-top: 4px;" size="small" @change="getList()" v-model="type" placeholder="请选择完成状态" clearable>
        <el-option key="" label="所有" value=""></el-option>
        <el-option key="0" label="未完成" value="0"></el-option>
        <el-option key="1" label="已完成" value="1"></el-option>
      </el-select>
    </div>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
      :max-height="tableHeight" border @sort-change="handleSort" tooltip-effect="dark">
      <el-table-column prop="index" label="序号" width="50" align="center">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column prop="messageContent" label="备忘内容">
      </el-table-column>
      <el-table-column prop="crtBy" label="创建人">
      </el-table-column>
      <el-table-column prop="crtTm" label="创建时间">
      </el-table-column>
      <el-table-column prop="finisher" label="完成者">
      </el-table-column>
      <el-table-column prop="finishType" label="完成状态" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.finishType == 0" :type="'info'">未完成</el-tag>
          <el-tag size="mini" v-else :type="'success'">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="finishTm" label="完成时间">
      </el-table-column>
      <el-table-column prop="finishRemark" label="完成备注">
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="detail(scope.row)">查看</el-button>
          <el-button type="text" @click="modifyNote(scope.row)">修改</el-button>
          <el-button type="text" @click="finish(scope.row)" :disabled="scope.row.finishType == 1">完成</el-button>
          <el-button type="text" @click="deleteRow(scope.row)" :disabled="scope.row.finishType == 1">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="warning" size="small" class="link_btn" title="新增备忘录" @click="add">
          新增备忘录
        </el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <el-dialog :title="formData.flag ? '完结备忘录' : formData.messageContent ? '修改备忘录' : '新增备忘录'"
      :visible.sync="modifyVisible" width="30%" append-to-body>
      <el-form ref="form" :rules="formData.flag ? rules1 : rules" size="small" :model="formData">
        <el-form-item v-if="formData.flag" label="完结备注" prop="finishRemark">
          <el-input v-model="formData.finishRemark"></el-input>
        </el-form-item>
        <el-form-item v-else label="备忘内容" prop="messageContent">
          <el-input type="textarea" :rows="5" v-model="formData.messageContent"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="modifyFun" type="primary">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="备忘录" :visible.sync="detailVisible" width="30%" append-to-body>
      <div style="padding: 20px;line-height: 30px">
        <div style="max-height: 400px;overflow-y: scroll;">备忘内容：{{ currentRow.messageContent }}</div>
        <div style="margin-top: 10px;">创建人：{{ currentRow.crtBy }}</div>
        <div style="">创建时间：{{ currentRow.crtTm }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from '@/api/menorandum'
export default {
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 410,
      type: "0",
      searchItems: {
        normal: [
          {
            name: "完成状态",
            field: "finishType",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "已完成", value: "1" },
              { label: "未完成", value: "0" },
            ],
            dbfield: "finish_type",
            dboper: "eq",
            default: "0"
          }]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      listLoading: false,
      list: [],
      modifyVisible: false,
      rules: {
        messageContent: [
          { required: true, message: '请输入备忘内容', trigger: 'blur' },
        ]
      },
      rules1: {
        finishRemark: [
          { message: '请输入完结内容', trigger: 'blur' },
        ]
      },
      formData: {
      },
      detailVisible: false,
      currentRow: {}
    };
  },
  components: {
    Searchbar
  },

  computed: {},
  mounted() {
    window.addEventListener("resize", this.setTableHeight);

    this.setTableHeight();

    this.getList();

  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    deleteRow(row) {
      this.$confirm("你确定要删除该条备忘录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          $http.delMenorandum([row.id])
            .then((response) => {
              if (response.code == 0) {
                this.$message.success('删除成功')
                this.getList()
              }
            })
            .catch(error => {
              console.log(error);
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除操作",
          });
        });

    },
    detail(row) {
      this.detailVisible = true
      this.currentRow = row
    },
    modifyNote(row) {
      this.modifyVisible = true
      this.formData = {
        messageContent: row.messageContent,
        id: row.id
      }
    },
    finish(row) {
      this.modifyVisible = true

      this.formData = {
        finishRemark: '',
        id: row.id,
        flag: 'finishRemark'
      }
    },
    modifyFun() {
      let func = ""
      if (this.formData.flag) {
        func = 'finishMenorandum'
        delete this.formData.flag
      } else if (!this.formData.id) {
        func = 'addMenorandum'
      } else {
        func = 'updMenorandum'
      }
      let param = this.formData

      $http[func](param)
        .then(response => {
          if (response.code == 0) {
            this.$message.success(func == 'finishMenorandum' ? '已完成备忘！' : !this.formData.id ? '留言成功！' : '修改成功！')
            this.modifyVisible = false
            this.getList()
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    add() {
      this.modifyVisible = true
      this.formData = {
        messageContent: ''
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 410;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;

      let param = {
        filters: { "groupOp": "AND", "rules": [{ "field": "finish_type", "op": "eq", "data": this.type }] },
        page: 1,
        limit: 20
      }

      this.listLoading = true;
      $http
        .getMenorandumList(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.page.list;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
  }
}

</script>
<style lang='scss' scoped></style>
<style>
.el-tooltip__popper {
  max-width: 20%
}
</style>