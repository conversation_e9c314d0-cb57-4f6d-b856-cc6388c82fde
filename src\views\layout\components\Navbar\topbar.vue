<template>
  <div class="topbar-container">
    <el-menu :default-active="getActivedTopMenuId()" class="topbar-menu" mode="horizontal" background-color="transparent"
      text-color="#fff" active-text-color="#9DE6FF">
      <template v-for="(m, i) in topMenuList">
        <el-menu-item :class="[{ active: activeMenu == m[pathKey] }, [i >= hiddenIndex && 'hidden']]" :key="m.menuId"
          :ref="'menu-' + i" :index="m.menuId + ''" @click="handleMenuClick(m.menuId, m.name, m.url)">{{
            m[labelKey] }}</el-menu-item>
      </template>
    </el-menu>
    <div :default-active="activeMenu" :class="{ hidden: hiddenIndex >= topMenuList.length }"
      class="navbar-center-dropdown-icon">
      <el-dropdown trigger="hover" :hide-on-click="false">
        <div class="el-dropdown-link" style="cursor: pointer; padding: 0 15px">
          <!-- <img :src="menuDropdownSrc" alt="" /> -->
          <svg-icon icon-class="menu" class-name="top-menu-svg-icon"></svg-icon>
        </div>
        <el-dropdown-menu slot="dropdown">
          <template v-for="(m, i) in topMenuList">
            <el-dropdown-item :key="m.menuId" v-if="i >= hiddenIndex" :ref="'menu-' + i"
              @click.native="handleMenuClick(m.menuId, m.name, m.url)">{{ m[labelKey]
              }}</el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import menuDropdownSrc from "static/image/layout/menu-dropdown.png";
import debounce from "lodash/debounce";
export default {
  name: "Topbar",
  props: {
    topMenuList: {
      type: Array,
      required: true,
      default: []
    }
  },
  data() {
    return {
      hiddenIndex: 1000,
      // menuDropdownSrc: menuDropdownSrc,
    };
  },
  computed: {
    ...mapGetters(["menuList", "settings"]),
    labelKey() {
      return "name";
    },
    pathKey() {
      return "url";
    },
    activeMenu() {
      let matched = this.$route.matched.filter(item => !!(item.meta && item.meta.title));
      if (matched && matched[1]) {
        return matched[1].path;
      } else {
        return "";
      }
    },
  },
  watch: {
    topMenuList: {
      handler() {
        this.$nextTick(() => {
          this.resizeTopMenuBar();
        });
      },
      deep: true,
    },
  },
  mounted() {
    window.addEventListener("resize", this.resizeTopMenuBar);
    this.$nextTick(() => {
      this.resizeTopMenuBar();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.resizeTopMenuBar);
  },
  methods: {
    getActivedTopMenuId: function () {
      let currentPath = this.$route.path;
      let selectTopMenu = null;
      let item;
      for (let i = 0, len = this.topMenuList.length; i < len; i++) {
        item = this.topMenuList[i];
        if (currentPath.indexOf("/" + item.url) == 0) {
          selectTopMenu = item;
          break;
        }
      }
      if (selectTopMenu) {
        return selectTopMenu.menuId + "";
      } else {
        return null;
      }
    },
    handleMenuClick(id, tabName, tabUrl) {
      let pathTemp = tabUrl.indexOf("/") == 0 ? tabUrl : "/" + tabUrl;
      this.$router.push({ path: pathTemp });
      this.showMobileMenu = false;
    },
    // 重新设置顶部导航栏
    resizeTopMenuBar: debounce(
      function () {
        let len = this.topMenuList.length;
        let hiddenIndex = (this.hiddenIndex = 1000);
        this.$nextTick(() => {
          let firstMenuOffsetLeft;
          if (!len) return;
          for (let i = 0; i < len; i++) {
            let refsItem = this.$refs["menu-" + i];
            if (refsItem && refsItem.length) {
              refsItem = refsItem[0];
            } else {
              continue;
            }
            let menuOffsetLeft = refsItem.$el.offsetLeft;
            if (i === 0) {
              firstMenuOffsetLeft = menuOffsetLeft;
            } else {
              if (menuOffsetLeft <= firstMenuOffsetLeft) {
                hiddenIndex = i;
                break;
              }
            }
          }
          // console.log(hiddenIndex);
          this.hiddenIndex = hiddenIndex;
        });
        // let topbarContainerWidth = this.getStyle(this.$refs.topbarContainer, "width");
        // let topMenuContainerWidth = this.getStyle(this.$refs.topMenuContainer, "width");
        // let viewTopMenuLength = len; // 可视菜单长度
        // for (let i = 0; i < len; i++) {
        //   let topMenuEl = this.$refs["topMenuList-" + i][0].$el;
        //   let topMenuWidth = this.getStyle(topMenuEl, "width");
        //   let offSetLeft = topMenuEl.offsetLeft;

        //   if (i < len - 1 && offSetLeft + topMenuWidth - 15 > topMenuContainerWidth) {
        //     viewTopMenuLength = i;
        //     break;
        //   } else if (i === len - 1 && offSetLeft + topMenuWidth - 15 < topbarContainerWidth) {
        //     viewTopMenuLength = len;
        //     break;
        //   } else if (i === len - 1 && offSetLeft + topMenuWidth - 15 > topbarContainerWidth) {
        //     viewTopMenuLength = len - 1;
        //   }
        // }
        // this.viewTopMenuLength = viewTopMenuLength;
      },
      500,
      { leading: false, trailing: true }
    ),

    getStyle(elm, name) {
      if (window.getComputedStyle) {
        return parseFloat(window.getComputedStyle(elm, null)[name]) || 0;
      } else {
        return parseFloat(elm.currentStyle[name]) || 0;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$topbarHeight: 50px;

.topbar-container {
  position: relative;
  text-align: center;
  flex: 1 auto;
  height: $topbarHeight;
  overflow: hidden;
  padding-right: 20px;

  .topbar-menu {
    display: inline-block;
    border-bottom: none;
  }

  .navbar-center-dropdown-icon {
    position: absolute;
    right: -5px;
    top: 50%;
    margin-top: -21.5px;
    height: 43px;
    line-height: 43px;
    cursor: pointer;

    .el-dropdown-link {
      font-size: 24px;

      img {
        width: 20px;
      }
    }
  }

  .top-menu-svg-icon {
    // color: #9CB4D3;
    color: #fff;
    margin-top: 10px;
  }
}
</style>
