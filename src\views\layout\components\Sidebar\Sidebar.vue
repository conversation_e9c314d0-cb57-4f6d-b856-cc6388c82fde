<template>
  <div class="left-side">
    <div class="collapse-btn el-menu-item" @click="toggleSideBar">
      <div v-show="isCollapse">
        <svg-icon icon-class="arrow-right" class-name="menu-svg-icon"></svg-icon>
        <!-- <span>展开</span> -->
      </div>
      <div v-show="!isCollapse">
        <svg-icon icon-class="arrow-left" class-name="menu-svg-icon"></svg-icon>
        <span data-v-60c5c4c4="">折叠菜单</span>
      </div>
    </div>
    <el-menu :default-active="ActivedLeftMenuIdHandel()" :collapse="isCollapse" class="sidebar-menu" @open="handleOpen"
      @close="handleClose">
      <template v-for="(item, index) in navMenus">
        <template v-if="item.list">
          <el-submenu :index="getMenuItemIndex(item)" :key="item.menuId" class="sidebar-submenu">
            <template slot="title">
              <svg-icon :icon-class="item.icon" class-name="menu-svg-icon"></svg-icon>
              <span slot="title">{{ item.name }}</span>
            </template>
            <el-menu-item v-for="subItem in item.list" :key="subItem.menuId" :index="getMenuItemIndex(subItem)"
              @click="handleLeftMenuClick(subItem)">
              {{ subItem.name }}
            </el-menu-item>
          </el-submenu>
        </template>
        <template v-else>
          <el-menu-item :index="getMenuItemIndex(item)" :key="'' + (index + 2)" @click="handleLeftMenuClick(item)">
            <svg-icon :icon-class="item.icon" class-name="menu-svg-icon"></svg-icon>
            <span slot="title"> {{ item.name }}</span>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script>
import { getMenu } from "@/api/menu";
import { isURL } from "@/utils/validate";

export default {
  name: "Sidebar",
  data() {
    return {
      navMenus: [],
      topMenuList: [],
      activeTopMenu: null,
      topMenuUrl: "" // topMenu的url,默认为'',表示不存在
    };
  },
  created() {
    let _this = this;

    this.topMenuList = JSON.parse(
      sessionStorage.getItem("topMenuList") || "[]"
    );
    let menuList = JSON.parse(sessionStorage.getItem("menuList") || "[]");

    this.activeTopMenu = this.getActivedTopMenu();
    if (this.topMenuList.length > 0 && this.activeTopMenu) {
      this.navMenus = menuList.filter(item => {
        return item.parentId === this.activeTopMenu.menuId;
      });
      let url = this.activeTopMenu.url;
      this.topMenuUrl = url.indexOf("/") >= 0 ? url : "/" + url;
    } else {
      this.topMenuUrl = "";
      this.navMenus = menuList;
    }
  },
  computed: {
    isCollapse() {
      return !this.$store.state.app.sidebar.opened;
    }
  },
  methods: {
    getActivedTopMenu: function () {
      if (this.topMenuList.length == 0) return null; // 表示顶部菜单不存在

      let currentPath = this.$route.path;
      let selectTopMenu = null;
      let item;
      for (let i = 0, len = this.topMenuList.length; i < len; i++) {
        item = this.topMenuList[i];
        if (currentPath.indexOf("/" + item.url + "/") == 0) {
          selectTopMenu = item;
          break;
        }
      }
      return selectTopMenu;
    },
    ActivedLeftMenuIdHandel: function () {
      let currentRoute = null;
      let infoPageIndex = this.$route.path.indexOf("/info/"),
        addPageIndex = this.$route.path.indexOf("/add"),
        formPageIndex = this.$route.path.indexOf("/form/");
      if (infoPageIndex >= 0 || addPageIndex >= 0 || formPageIndex >= 0) {
        if (infoPageIndex >= 0)
          currentRoute = this.$route.path.substr(0, infoPageIndex) + "/list";
        else if (addPageIndex >= 0)
          currentRoute = this.$route.path.substr(0, addPageIndex) + "/list";
        else currentRoute = this.$route.path.substr(0, formPageIndex) + "/list";
      } else {
        currentRoute = this.$route.path;
      }

      return currentRoute;
    },

    handleOpen: function (key, keyPath) {
      console.log(key, keyPath);
    },

    handleClose: function (key, keyPath) {
      console.log(key, keyPath);
    },

    getMenuItemIndex(item) {
      return this.topMenuUrl + "/" + item.url + "";
    },

    handleLeftMenuClick: function (menuItem) {
      this.$router.push({ path: this.topMenuUrl + "/" + menuItem.url });
    },
    toggleSideBar: function () {
      // this.$store.dispatch('ToggleSideBar');
      this.$store.commit("TOGGLE_SIDEBAR");
    }
  }
};
</script>

<style scoped>
.left-side {
  height: 100%;
  padding-bottom: 50px;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  /* background-color: #063f86;
  background: -webkit-linear-gradient(0deg, #0a284a, #063f86);
  background: -o-linear-gradient(0deg, #0a284a, #063f86);
  background: -moz-linear-gradient(0deg, #0a284a, #063f86);
  background: linear-gradient(0deg, #0a284a, #063f86); */
  background: #063f86 url("~static/img/left-menu-bg.png") no-repeat center center;
  background-size: 100% 100%;
}

.left-side .collapse-btn {
  /* background: #172c53; */
}

.left-side .collapse-btn.el-menu-item:focus,
.left-side .collapse-btn.el-menu-item:hover {
  color: #42affa;
}

.left-side .collapse-btn>span>i {
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
}

.left-side::-webkit-scrollbar-track-piece {
  background: #ecf0f5;
}

.left-side::-webkit-scrollbar {
  width: 6px;
}

.left-side::-webkit-scrollbar-thumb {
  background: #dedee0;
  border-radius: 20px;
}

.sidebar-menu {
  border-right: 0;
  background: transparent;
}

.sidebar-submenu {
  background: transparent;
}

.menu-svg-icon {
  margin-right: 8px;
  font-size: 20px;
}
</style>