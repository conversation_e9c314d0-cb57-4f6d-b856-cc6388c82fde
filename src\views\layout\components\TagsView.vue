<template>
	<div class="tags-view-container">
		<scroll-pane class='tags-view-wrapper' ref='scrollPane'>
		  <router-link ref='tag' class="tags-view-item" :class="isActive(tag)?'active':''" v-for="tag in Array.from(visitedViews)"
		    :to="tag.path" :key="tag.path" @contextmenu.prevent.native="openMenu(tag,$event)">
		    {{tag.title}}
		    <span class='el-icon-close' @click.prevent.stop='closeSelectedTag(tag)'></span>
		  </router-link>
		</scroll-pane>
		<ul class='contextmenu' v-show="visible" :style="{left:left+'px',top:top+'px'}">
		  <li @click="closeSelectedTag(selectedTag)">关闭</li>
		  <li @click="closeOthersTags">关闭其他</li>
		  <li @click="closeAllTags">关闭所有</li>
		</ul>
	</div>
</template>

<script>

export default {
  name: 'TagsView',
  data (){
	return	{
		visitedViews:[],

	}
  },
  computed: {
  	
  },
  methods:{
  	isActive (tag){

  	},
  	openMenu (){

  	},
  	closeSelectedTag (){

  	},
  	closeOthersTags (){

  	},
  	closeAllTags (){
  		
  	}

  }
}
</script>

<style scoped>

</style>
