<template>
  <div class="app-wapper clearfix" :class="classObj">
    <header class="app-header">
      <navbar></navbar>
    </header>
    <div class="app-content">
      <div class="sidebar-container" :style="{'width':(sidebar.opened?200:64)+'px'}" v-show="device!='mobile'">
        <sidebar :key="$route.path"></sidebar>
      </div>
      <div class="app-main-container" :style="{'padding-left':device==='mobile'?0:(sidebar.opened?200:64)+'px'}">
        <!-- <tags-view></tags-view> -->
        <div v-if="$route.meta.isIframe" class="app-main-component">
          <iframe :src="$route.meta.iframeUrl" width="100%" height="100%" frameborder="0" scrolling="yes">
          </iframe>
        </div>
        <app-main v-else class="app-main-component"></app-main>
      </div>
    </div>
    <back-to-top></back-to-top>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain, TagsView } from "@/views/layout/components";
import BackToTop from "@/components/BackToTop";
import { mapGetters } from "vuex";
import ResizeMixin from "./mixin/ResizeHandler";

export default {
  name: "Layout",
  components: {
    Sidebar,
    Navbar,
    TagsView,
    AppMain,
    BackToTop
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
			return this.$store.state.app.sidebar
		},
    device() {
			return this.$store.state.app.device
		},
    classObj() {
			return {
				'hide-sidebar': !this.sidebar.opened,
				'show-sidebar': this.sidebar.opened,
				'without-animation': this.sidebar.withoutAnimation,
				'mobile': this.device === 'mobile'
			}
		}
  }
};
</script>

<style scoped>
.sidebar-container {
  width: 200px;
  position: fixed;
  left: 0;
  top: 50px;
  bottom: 0;
  min-height: 100%;
  box-sizing: border-box;
  z-index: 1000;
}
.sidebar-container,
.app-main-container {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
</style>

