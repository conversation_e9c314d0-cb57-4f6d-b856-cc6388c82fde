import store from "@/store";

const { body } = document;
const WIDTH = 600;
const RATIO = 3;

export default {
  watch: {
    $route(route) {
      if (this.device === "mobile" && this.sidebar.opened) {
        store.dispatch("CloseSideBar", { withoutAnimation: false });
      }
    }
  },
  beforeMount() {
    this.resizeHandler();
    window.addEventListener("resize", this.resizeHandler);
  },
  mounted() {
    // const isMobile = this.isMobile()
    // if (isMobile) {
    //   store.dispatch('ToggleDevice', 'mobile')
    //   store.dispatch('CloseSideBar', { withoutAnimation: true })
    // }else{
    //   store.dispatch('ToggleDevice', 'desktop')
    //   store.dispatch('OpenSideBar', { withoutAnimation: true })
    // }
  },
  methods: {
    isMobile() {
      const rect = body.getBoundingClientRect();
      return rect.width - RATIO < WIDTH;
    },
    resizeHandler() {
      if (!document.hidden) {
        const isMobile = this.isMobile();
        let type = isMobile ? "mobile" : "desktop";
        store.dispatch("ToggleDevice", type);

        if (isMobile) {
          store.dispatch("CloseSideBar", { withoutAnimation: true });
        } else {
          store.dispatch("OpenSideBar", { withoutAnimation: true });
        }
      }
    }
  }
};
