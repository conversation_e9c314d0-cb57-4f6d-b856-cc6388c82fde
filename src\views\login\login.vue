<template>
  <div class="login-wrap">
    <!-- 系统公告 -->
    <notice></notice>
    <div class="ms-title">镇海区危险化学品道路运输监管系统</div>
    <div v-show="!stepStatus" class="ms-login" :class="[loginModeFlag ? loginModeFlag : '']">
      <!-- 二维码登录图标 -->
      <div v-show="loginModeFlag != 'zzd'" class="wx-login" title="浙政钉扫码登录" @click="loginMode('zzd')">
        <el-tag class="el-tag-tip" type="warning" size="mini">浙政钉扫码登录</el-tag>
        <svg-icon v-show="loginModeFlag != 'zzd'" icon-class="qrcode-login" class-name="qrcode-login-svg"></svg-icon>
      </div>
      <div v-show="loginModeFlag == 'zzd'" class="wx-login" title="短信验证码登录" @click="loginMode('message')">
        <svg-icon v-show="loginModeFlag == 'zzd'" icon-class="computer" class-name="computer-svg"></svg-icon>
      </div>
      <!-- 浙政钉扫码登录 -->
      <div class="wechat-form" v-if="loginModeFlag === 'zzd'">
        <h3 style="text-align: center;color: rgb(64, 158, 255);margin-bottom: 20px;font-weight: normal;">
          浙政钉扫码登录
        </h3>
        <zzdLogin></zzdLogin>
      </div>
      <!-- 短信登录 -->
      <div class="message-form" v-else>
        <h3 style="text-align: center;color: #409eff;margin-bottom: 20px;font-weight: normal;">
          <!-- <i class="el-icon-message" style="font-size: 23px; vertical-align: middle"></i> -->
          短信验证码登录
        </h3>
        <el-form :model="smsForm" :rules="loginRules" ref="smsForm" label-width="0px">
          <el-form-item prop="mobile">
            <el-input v-model="smsForm.mobile" placeholder="请输入手机号码" icon="user" @keyup.enter.native="handleSmsLogin">
              <template slot="prepend">
                <i class="el-icon-mobile-phone" style="font-size: 22px"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <el-input type="text" placeholder="请输入验证码" v-model="smsForm.captcha" @keyup.enter.native="handleSmsLogin">
              <template slot="append">
                <span class="verif-code" @click="getVerifCode">{{
                  verifCodeCnt
                  }}</span>
              </template>
            </el-input>
          </el-form-item>
          <div class="login-btn">
            <el-button type="primary" @click="handleSmsLogin" :loading="loading">登录</el-button>
          </div>
        </el-form>
      </div>

      <div class="ms-login-tips">本平台为互联网非涉密平台，严禁处理、传输国家秘密</div>
    </div>
    <div class="panel" v-show="stepStatus">
      <div class="panel-header">
        <span class="panel-heading-inner">登录激活码</span>
      </div>
      <div class="panel-body">
        <el-form :model="loginStep1" ref="loginStep1" label-width="130px" class="clearfix" style="padding: 0 20px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="激活码">
                <el-input v-model="loginStep1.codeVal" placeholder="请输入发送到您手机上的激活码" size="small"
                  @keyup.enter.native="nextHandle"></el-input>
                <el-button type="primary" @click="nextHandle" :disabled="stepStatus == 3" v-if="stepStatus < 2"><i
                    class="el-icon-d-arrow-right"></i>&nbsp;验证</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="ms-tips" :style="{ top: msTipsTopValue + 'px' }">
      温馨提示：系统支持Google Chrome版本和360浏览器（采用极速模式），建议推荐<a style="color: #c2c3c3" href="https://www.google.cn/chrome/"
        target="_blank">谷歌浏览器</a>。
    </div>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import { encrypt } from "@/utils/crypto";
import Notice from "./notice";
import { isvalidatemobile, isMobile } from "@/utils/validate";
import { getSmsCodeOnlyMob } from "@/api/login";
import zzdLogin from "./zzdLogin";
import { getKey } from "@/api/common";

const validatePhone = (rule, value, callback) => {
  if (isvalidatemobile(value)[0]) {
    callback(new Error(isvalidatemobile(value)[1]));
  } else {
    callback();
  }
};
const validateCode = (rule, value, callback) => {
  if (value.length != 6 && value.length != 4) {
    callback(new Error("请输入验证码"));
  } else {
    callback();
  }
};
export default {
  name: "login",
  data() {
    return {
      verifCodeCnt: "获取验证码",
      errCount: 0,
      stepStatus: false,
      loading: false,
      isRememberMe: true,
      captchaPath: "",
      smsForm: {
        type: "mob",
        mobile: "",
        captcha: "",
      },
      loginModeFlag: "message",
      loginForm: {
        username: "",
        password: "",
        uuid: "",
        captcha: "",
      },
      loginStep1: {
        codeVal: "", // 激活码
        username: "", // 激活码
      },
      baseAPI: process.env.BASE_API,
      weixinLoginUrl: process.env.WEIXIN_LOGIN_URL,
      weixinAppId: process.env.WEIXIN_APPID,
      msTipsTopValue: Tool.getClientHeight() * 0.25 + 380,
      loginRules: {
        mobile: [{ required: true, trigger: "blur", validator: validatePhone }],
        captcha: [{ required: true, trigger: "blur", validator: validateCode }],
      },
    };
  },
  computed: {
    baseURL() {
      return this.baseAPI.replace("/whjk-gov", "");
    },
  },
  components: {
    Notice,
    zzdLogin
  },
  created() {
    this.getCaptcha();
    let errCountCatch = localStorage.getItem("errCount");
    errCountCatch && (this.errCount = errCountCatch);
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", function () {
      _this.msTipsTopValue = Tool.getClientHeight() * 0.25 + 380;
    });
    this.checkBrowser(); // 检测浏览器
  },
  methods: {
    checkBrowser() {
      var ua = navigator.userAgent.toLocaleLowerCase();
      if (ua.match(/chrome/) == null && ua.match(/firefox/) == null) {
        // 既不是chrome内核，也不是火狐浏览器
        this.$alert(
          `系统检测到您的浏览器可能不兼容本系统，建议您下载以下浏览器：<br /><br /><a href="${this.baseURL}/welcome/tools/49.0.2623.112_chrome_installer.exe" target="_blank">Chrome下载</a>&nbsp;或者&nbsp;<a href="${this.baseURL}/welcome/tools/360cse_9.5.0.138.exe" target="_blank">360极速浏览器下载</a>`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
          }
        );
      }
    },
    getCaptcha() {
      let _this = this;
      this.loginForm.uuid = Tool.getUUID();
    },
    nextHandle() {
      var _this = this;
      _this.loading = true;

      let postData = {};
      postData.username = _this.encode(_this.loginForm.username, 3);
      postData.password = _this.encode(_this.loginForm.password, 3);
      postData.uuid = _this.loginForm.uuid;
      postData.captcha = _this.loginForm.captcha;
      postData.codeVal = _this.loginStep1.codeVal;

      this.$store
        .dispatch("LoginByUsername", postData)
        .then((response) => {
          this.loading = false;
          if (response.code == 0) {
            let redirectTo = _this.$route.query.redirect || "/";
            _this.$router.push({ path: redirectTo });
          } else {
            _this.getCaptcha();
            _this.loginForm.captcha = "";
            this.$message({
              showClose: true,
              message: "登录失败：" + response.msg,
              type: "error",
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 登录方式切换
    loginMode(type) {
      this.loginModeFlag = type;
    },
    // 短信验证登录
    handleSmsLogin() {
      let _this = this;
      this.$refs.smsForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          let res = await getKey().catch(e => { console.log(e); _this.loading = false; })
          _this.loading = false;
          if (res && res.code === 0) {
            let postData = {};
            let sign = res.sign
            let mobile = _this.smsForm.mobile;
            let captcha = _this.smsForm.captcha;
            postData.mobile = encrypt(sign, mobile);
            postData.captcha = encrypt(sign, captcha);
            postData.type = _this.smsForm.type;
            postData.sign = sign;
            _this.loginMethod(postData);
          } else {
            _this.$message({
              type: "error",
              message: res.msg,
            });
          }
        } else {
          console.log("error submit!!");
        }
      });
    },
    loginMethod(postData) {
      let _this = this;
      this.$store
        .dispatch("LoginByUsername", postData)
        .then((response) => {
          this.loading = false;
          if (response.code == 0) {
            localStorage.removeItem("errCount");
            if (response.roleType == 1) {
              //政府端
              if (!response.token) {
                _this.stepStatus = true;
              } else {
                let redirectTo = _this.$route.query.redirect || "/";
                _this.$router.push({ path: redirectTo });
              }
            } else if (response.roleType == 2) {
              //企业端
              window.location.href = _this.baseURL + "/entp";
            } else if (response.roleType == 3) {
              //装卸端
              window.location.href = _this.baseURL + "/cp";
            }
          } else {
            let errCount = response.errCount;

            if (errCount) {
              localStorage.setItem("errCount", errCount);
              _this.errCount = errCount;
            }
            _this.getCaptcha();
            _this.loginForm.captcha = "";
            this.$message({
              showClose: true,
              message: "登录失败：" + response.msg,
              type: "error",
            });
          }
        })
        .catch((err) => {
          let errCount = err.errCount;

          if (errCount) {
            localStorage.setItem("errCount", errCount);
            _this.errCount = errCount;
          }
          this.loading = false;
        });
    },
    //获取短信验证码
    getVerifCode() {
      // 手机号码验证
      let mobile = this.smsForm.mobile;
      if (!isMobile(mobile)) return this.$message("请输入正确的手机号码");

      let cnt = 30,
        dur = 1000,
        initDur = 50,
        timer = null,
        msg = this.verifCodeCnt;

      let cntDown = () => {
        clearTimeout(timer);
        cnt--;
        if (cnt == 0) {
          this.verifCodeCnt = msg;
          clearTimeout(timer);
          this.isVerifing = false;
          return false;
        }
        this.verifCodeCnt = cnt + " 秒";
        setTimeout(cntDown, dur);
      };
      if (!this.isVerifing) {
        getSmsCodeOnlyMob({ mob: mobile }).then((res) => {
          if (res.code == 0) {
            this.$message.success("短信发送成功，请注意查收");
          }
        });
        setTimeout(cntDown, initDur);
      }
      this.isVerifing = true;
    },
  },
};
</script>

<style scoped>
.wx-login {
  cursor: pointer;
  position: absolute;
  top: 1px;
  right: 1px;
}

.el-tag-tip {
  position: absolute;
  top: 1px;
  right: 50px;
}

.el-tag-tip::after {
  content: '';
  position: absolute;
  right: -9px;
  top: 5px;
  width: 0px;
  height: 0px;
  border: 4px solid transparent;
  border-left-color: #f0c583;
}

.svg-icon.qrcode-login-svg,
.svg-icon.computer-svg {
  font-size: 48px;
}

.svg-icon.qrcode-login-svg:hover {
  fill: aqua;
}

.ms-login .wechat-form,
.ms-login .message-form {
  /* display: none; */
}

.ms-login.wechat .standard-form,
.ms-login.wechat .message-form {
  display: none;
}

.ms-login.message .standard-form,
.ms-login.message .wechat-form {
  /* display: none; */
}

.ms-login.wechat .wechat-form {
  display: block;
}

.ms-login.message .message-form {
  display: block;
}

.code-image {
  height: 38px;
  width: 110px;
  line-height: 40px;
  display: inline-block;
  vertical-align: middle;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

#wx-login-qrcode {
  width: 200px;
  height: 200px;
  margin: 0px auto;
  overflow: hidden;
}

.verif-code {
  cursor: pointer;
}

.panel {
  padding-bottom: 20px;
  margin-top: 200px;
}

.login-wrap {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 300px;
  overflow-y: auto;
  background-color: #f5f7f9;
  background-image: url('~@/assets/login-imgs/loginbg.jpg');
  -moz-background-size: 100% 100%;
  background-size: 100% 100%;
}

.ms-title {
  position: absolute;
  top: 10vh;
  width: 100vw;
  text-align: center;
  font-size: 4vh;
  line-height: 6vh;
  color: #fff;
  font-weight: 500;
}

.ms-login {
  position: absolute;
  left: 50vw;
  top: 50vh;
  width: 400px;
  height: 360px;
  margin: 0;
  margin-left: -195px;
  margin-top: -25vh;
  padding: 40px;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.8);
}

.code-image {
  height: 38px;
  width: 110px;
  line-height: 40px;
  display: inline-block;
  vertical-align: middle;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.login-btn {
  text-align: center;
}

.login-btn button {
  width: 100%;
  height: 36px;
}

.singup {
  background-color: #d9534f;
  display: inline;
  float: right;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1.5;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  text-decoration: none;
  margin-top: 9px;
}

.ms-tips {
  position: absolute;
  width: 100%;
  text-align: center;
  color: #c2c3c3;
  font-size: 120%;
}

.ms-login-tips {
  margin-top: 30px;
  text-align: center;
  color: #d30000;
  font-size: 13px;
  font-weight: bold;
}
</style>
