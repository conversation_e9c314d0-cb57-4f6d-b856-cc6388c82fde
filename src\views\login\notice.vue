<template>
  <!-- 系统公告 -->
  <transition name="slide-fade">
    <div id="noticeInfo"
         v-if="notice">
      <div class="clearfix"
           style="width:824px;margin:0 auto;padding-left:90px;">
        <div class="ft-lf notice-content clearfix"
             v-scroll-notice>{{notice}}</div>
        <span class="ft-lf notice-title"><svg-icon icon-class="notice" />系统公告：</span>
      </div>
    </div>
  </transition>
</template>
<script>
import { noticeInfo } from '@/api/common'
export default {
  data () {
    return {
      notice: ''
    }
  },
  mounted () {
    let _this = this;
    //系统公告
    setTimeout(() => {
      this.getNoticeInfo((res) => {
        if (res.code === 0) {
          if (res.data && res.data.details) {
            let details = this.getSimpleText(res.data.details)
            if (res.data.isAlert === 1) {
              this.$nextTick(() => {
                this.$alert(res.data.details, res.data.headline || '提示', {
                  confirmButtonText: '确定',
                  type: 'warning',
                  dangerouslyUseHTMLString: true,
                  callback: action => {
                    _this.notice = details;
                  }
                });
              });
            } else {
              _this.notice = details;
            }
          }
        }
      })
    }, 1000);

  },
  methods: {
    getSimpleText (html) {
      let re1 = new RegExp("<.+?>", "g");//匹配html标签的正则表达式，"g"是搜索匹配多个符合的内容
      let msg = html.replace(re1, '');//执行替换成空字符
      return msg;
    },
    getNoticeInfo (callback) {
      noticeInfo().then(res => {
        if (res.code == 0) {
          callback && callback(res);
        }
      })
    }
  },
  directives: {
    scrollNotice: {
      bind: function (el, binding) {
        let childs = el.childNodes;
        let maxWidth = 739;
        let text;

        for (var i = 0, len = childs.length; i < len; i++) {
          if (childs[i].nodeType == 3) {
            text = (childs[i].nodeValue || childs[i].wholeText);
            break;
          }
        };

        if (text.length * 14 < maxWidth) {
          return false;
        }

        let scrollWrap = document.createElement('div');
        let timer = null;
        let left = 0;

        // c*t/d + b
        let time = 0;
        let beginVal = 0;
        let changeVal = 10;
        let duration = 8.5;
        let scrollWrapWidth = 0;

        scrollWrap.innerHTML = text + text;
        scrollWrap.style.float = 'left';

        el.innerHTML = '';
        el.appendChild(scrollWrap);

        clearTimeout(timer);

        timer = setTimeout(function scrollFn () {
          if (!scrollWrapWidth) {
            scrollWrapWidth = (getComputedStyle(scrollWrap, null)['width'].replace(/px$/g, ''));
          }
          clearTimeout(timer);
          if (Math.abs(parseFloat(getComputedStyle(scrollWrap, null)['marginLeft'])) >= scrollWrapWidth / 2) {
            time = 0;
            scrollWrap.style.marginLeft = '0px';
          }
          time++;
          scrollWrap.style.marginLeft = -(changeVal * time / duration + beginVal) + 'px';
          timer = setTimeout(scrollFn, 56)
        }, 56);

      }
    }
  }
}
</script>
<style scoped>
#noticeInfo {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 30px;
  background: rgba(0, 0, 0, 0.2);
  font-size: 14px;
  color: #fff;
  text-align: left;
  line-height: 30px;
}
#noticeInfo svg {
  font-size: 16px;
  font-weight: 700;
  vertical-align: middle;
}
#noticeInfo .notice-content {
  position: relative;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}
#noticeInfo .notice-title {
  position: relative;
  left: -85px;
  margin-left: -100%;
}
</style>
