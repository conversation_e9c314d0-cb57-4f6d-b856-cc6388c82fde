<template>
  <el-form class="login-form">
    <!-- 浙政钉2.0登录 -->
    <div class="zzd-form">
      <!-- <h3 style="text-align:center;color:#fff;margin-bottom:20px;font-weight:normal;">
        浙政钉扫码登录
      </h3> -->
      <div style="width: 190px;height: 190px;overflow: hidden;margin: 0 auto;">
        <div style="width: 190px;height: 190px;margin: -92px 0px 0px 0px">
          <iframe style="width: 100%;height: 283px"
                  :src="zzdUrl"
                  frameborder="0"
                  scrolling="no"
                  ref="iframe"></iframe>
        </div>
      </div>
      <!-- <div style="font-size:14px;text-align:center;color:#fff;margin-top:14px;">打开浙政钉扫描二维码登录</div> -->
    </div>
    <el-dialog title="绑定手机号"
               :visible.sync="visible"
               width="20%"
               append-to-body
               style="position: absolute;"
               :close-on-click-modal="false">
      <el-form class="login-form"
               status-icon
               :rules="loginRules"
               ref="loginForm"
               :model="loginForm"
               label-width="0">
        <el-form-item prop="mobile">
          <el-input @keyup.enter.native="handleDinding"
                    v-model="loginForm.mobile"
                    auto-complete="off"
                    placeholder="请输入手机号">
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <el-input @keyup.enter.native="handleDinding"
                    v-model="loginForm.captcha"
                    auto-complete="off"
                    placeholder="请输入验证码">
            <template slot="append">
              <span @click="handleSend"
                    class="msg-text"
                    :class="[{ display: msgKey }]">{{ msgText }}</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     style="background: #409eff; border: 1px solid #4fb6ff;width:100%;"
                     size="large"
                     @click.native.prevent="handleDinding"
                     class="login-submit">绑定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-form>
</template>

<script>
import { getSmsCodeForDingCheck } from "@/api/login";
import { isMobile, isvalidatemobile } from "@/utils/validate";
import { encrypt } from "@/utils/crypto";
import { getKey } from "@/api/common";
export default {
  name: "zzdLogin",
  data () {
    const validatePhone = (rule, value, callback) => {
      if (isvalidatemobile(value)[0]) {
        callback(new Error(isvalidatemobile(value)[1]));
      } else {
        callback();
      }
    };

    const validateCode = (rule, value, callback) => {
      if (value.length != 6 && value.length != 4) {
        callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      msgText: '',
      ZZD_API: process.env.VUE_APP_zzdApi,
      ZZD_APPID: process.env.VUE_APP_zzdAppid,
      ZZD_LOGIN_URL: process.env.VUE_APP_zzdLoginUrl,
      MSGTIME: 30,
      visible: false,
      msgKey: false,
      loginForm: {
        mobile: "",
        captcha: "",
        code: '',
        type: "mob",
      },
      loginRules: {
        mobile: [{ required: true, trigger: "blur", validator: validatePhone }],
        captcha: [{ required: true, trigger: "blur", validator: validateCode }],
      },
      ding: {}
    };
  },
  created () { },
  computed: {
    zzdUrl () {
      return (
        "https://" +
        this.ZZD_API +
        "/oauth2/auth.htm?response_type=code&client_id=" +
        this.ZZD_APPID +
        "&redirect_uri=" +
        this.ZZD_LOGIN_URL +
        "&scope=get_user_info&authType=QRCODE&embedMode=true"
      );
    },
    config () {
      return {
        // MSGINIT: this.$t("login.msgText"),
        // MSGSCUCCESS: this.$t("login.msgSuccess"),
        MSGINIT: "发送验证码",
        MSGSCUCCESS: "秒后重发",
        MSGTIME: 30,
      };
    },
  },
  created () {
    this.msgText = this.config.MSGINIT;
    this.msgTime = this.config.MSGTIME;
  },
  mounted () {
    this.bindEventListener();
  },
  destroyed () {
    this.destroyedListener();
  },
  methods: {
    destroyedListener () {
      if (typeof window.addEventListener != "undefined") {
        window.removeEventListener("message", this.zzdHandleMessage);
      } else if (typeof window.attachEvent != "undefined") {
        window.detachEvent("onmessage", this.zzdHandleMessage);
      }
    },
    //获取短信验证码
    handleSend () {
      if (this.msgKey) return;
      let mobile = this.loginForm.mobile;
      if (!isMobile(mobile)) return this.$message("请输入正确的手机号码");
      getSmsCodeForDingCheck({ mob: mobile }).then(res => {
        if (res.code == 0) {
          this.$message.success("短信发送成功，请注意查收");
          this.msgText = this.msgTime + this.config.MSGSCUCCESS;

          this.msgKey = true;
          const time = setInterval(() => {
            this.msgTime--;
            this.msgText = this.msgTime + this.config.MSGSCUCCESS;
            if (this.msgTime == 0) {
              this.msgTime = this.config.MSGTIME;
              this.msgText = this.config.MSGINIT;
              this.msgKey = false;
              clearInterval(time);
            }
          }, 1000);
        }
      });
    },
    handleDinding () {
      let _this = this;
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          let res = await getKey().catch(e => { console.log(e); _this.loading = false; });
          _this.loading = false;
          if (res && res.code === 0) {
            let sign = res.sign;
            let par = {
              mobile: encrypt(sign, _this.loginForm.mobile),
              captcha: encrypt(sign, _this.loginForm.captcha),
              sign: sign,
              code: _this.loginForm.code
            }
            _this.$store.dispatch("checkDingInfo", par).then(res => {
              if (res.code === 0) {
                let redirectTo = _this.$route.query.redirect || "/";
                _this.$router.push({ path: redirectTo });
              }
            });

          } else {
            _this.$message({
              type: "error",
              message: res.msg,
            });
          }
          // 18267401672
        }
      });
    },
    bindEventListener () {
      let _this = this;
      this.destroyedListener();
      if (typeof window.addEventListener != "undefined") {
        window.addEventListener("message", this.zzdHandleMessage, false);
      } else if (typeof window.attachEvent != "undefined") {
        window.attachEvent("onmessage", this.zzdHandleMessage);
      }
    },
    zzdHandleMessage (event) {
      // 这里的event.data 就是登录成功的信息
      // 数据格式：{ "code": "aaaa", "state": "bbbb" }
      let res = event.data || null;
      if (res && res.code) {
        this.getZZDInfo(res.code);
      }
    },
    // 获取浙政钉info
    getZZDInfo (code) {
      let _this = this;
      // if (state === "STATE") {
      // 判断是钉钉扫码进来的
      // 去后端请求 数据  再调用户信息的首页
      this.$store
        .dispatch("getUserInfoByZZDCode", code)
        .then((res) => {
          if (res.code === 0) {
            _this.$message.success({
              message: "登录成功",
              duration: 1000,
              onClose: () => {
                let redirectTo = _this.$route.query.redirect || "/";
                _this.$router.push({ path: redirectTo });
              },
            });
          } else if (res.code === 508) {
            this.loginForm.code = res.errCode
            this.visible = true;
            this.ding = res.ding;
            _this.$message.error("登录失败：" + res.msg);
          } else {
            _this.$message.error("登录失败：" + res.msg);
          }
        })
      // }
    },
  },
};
</script>

<style>
#zzd-login-qrcode {
  width: 200px;
  height: 200px;
  margin: 0px auto;
  overflow: hidden;
}

.msg-text {
  display: block;
  width: 60px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  background: none;
  color: #383434;
}

.msg-text.display {
  color: #383434;
}
</style>
