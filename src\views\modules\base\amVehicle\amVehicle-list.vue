<template>
  <div class="app-main-content">
    <!-- 表单 -->
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    >
      <template slot="button">
        <el-button @click="AddEdit((lkAddEdit = 0))" size="small" type="primary"
          >新增</el-button
        >
      </template>
    </searchbar>
    <!-- 表格 -->
    <el-table
      :data="list"
      style="width: 100%"
      :max-height="tableHeight"
      border
      v-loading="listLoading"
    >
      <el-table-column prop="vecNo" label="牵引车"> </el-table-column>
      <el-table-column prop="entpNm" label="企业名称"> </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-tag
            type="success"
            v-if="scope.row.alarmQty == 0 || scope.row.alarmQty == null"
            >正常</el-tag
          >
          <el-tag type="danger" v-else @click="stateDetails(scope.row.id)">
            <el-link class="alink">异常{{ scope.row.alarmQty }}次</el-link>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="notesDetails(scope.row)"
            v-if="scope.row.remark == null"
            >无备忘</el-button
          >
          <el-button
            type="text"
            @click="notesDetails(scope.row)"
            class="btn"
            v-else
            >有备忘</el-button
          >
          <el-button type="text" @click="AddEdit((lkAddEdit = 1), scope.row)"
            >编辑</el-button
          >
          <el-button type="text" @click="notesDelete(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="toolbar clearfix">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <!-- 状态弹出框 -->
    <el-dialog title="异常状态详情" :visible.sync="dialogVisible1" width="80%">
      <el-table :data="tableData2" border :max-height="tableHeight - 60">
        <el-table-column prop="loadPlace" label="起运地"></el-table-column>
        <el-table-column prop="unloadPlace" label="目的地"></el-table-column>
        <el-table-column prop="vecNo" label="牵引车"></el-table-column>
        <el-table-column prop="rcdDt" label="起运日期">
          <template slot-scope="scope">
            {{ scope.row.rcdDt && scope.row.rcdDt.substring(0, 10) }}
          </template>
        </el-table-column>
        <el-table-column label="轨迹">
          <template slot-scope="scope">
            <el-button type="text" @click="dalLocus(scope.row)">
              查看轨迹
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="运单号">
          <template slot-scope="scope">
            <el-button type="text" @click="dlWaybill(scope.row)">{{
              scope.row.rtePlanCd
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="toolbar clearfix">
        <el-pagination
          background
          layout="sizes, prev, pager, next, total"
          :page-sizes="[10, 20, 30, 40]"
          style="float: right"
          :page-size="tablePagination.limit"
          :current-page.sync="tablePagination.page"
          :total="tablePagination.total"
          @current-change="handleCurrentChangeOfTable"
          @size-change="handleSizeChangeOfTable"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <!-- 备忘弹出框 -->
    <el-dialog
      title="备忘"
      :visible.sync="dialogVisible2"
      width="30%"
      @closed="closed"
    >
      <el-form :model="form2">
        <el-form-item>
          <el-input
            type="textarea"
            v-model="form2.desc"
            rows="3"
            placeholder="请输入备忘内容"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button class="btnDesc" type="success" size="small" @click="submit"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
      <el-collapse>
        <el-collapse-item title="备忘历史记录">
          <div v-for="(item, index) in form2.remark" :key="index">
            <h3 style="margin: 0">{{ item.key }}</h3>
            <span>{{ item.value }}</span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>
    <!-- 编辑/新增弹出框 -->
    <el-dialog
      :title="lkAddEdit == 0 ? '新增' : '编辑'"
      :visible.sync="dialogVisible4"
      width="30%"
      :close-on-click-modal="false"
      @closed="closed2"
    >
      <el-form
        :model="form4"
        label-position="right"
        label-width="auto"
        ref="editForm"
      >
        <el-form-item
          label="牵引车"
          prop="vecNo"
          :rules="$rulesFilter({ required: true, type: 'LPN' })"
        >
          <!-- <el-input v-model="form4.vecNo" @focus="focus"></el-input> -->
          <!-- <el-select v-model="form4.vecNo" placeholder="请输入或选择" filterable :filter-method="dataFilter" @change="Cchange" @clear="Cclear" clearable>
            <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
          </el-select> -->
          <el-autocomplete
            v-model="form4.vecNo"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入内容"
            @select="handleSelect"
            @clear="handleClear"
            clearable
          ></el-autocomplete>
        </el-form-item>
        <el-form-item
          label="企业名称"
          prop="entpNm"
          :rules="$rulesFilter({ required: true })"
        >
          <el-input
            v-model="form4.entpNm"
            placeholder="根据车牌号自动匹配"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible4 = false">取 消</el-button>
        <el-button type="primary" @click="DVedit(lkAddEdit)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
import {
  // 车辆锁定列表接口
  getVehicleLock,
  // 异常详情列表接口
  getVehicleAbnormalDetails,
  // 备忘提交接口
  setMemoSubmit,
  // 删除
  lkDelete,
  // 新增
  lkAdd,
  // 编辑
  lkEdit,
  // 车牌
  lkVehicle
} from "@/api/amVehicle";
export default {
  name: "amVehicleList",
  mounted() {
    this.getList();
    this.setTableHeight();
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  components: {
    Searchbar
  },
  data() {
    return {
      options: [],
      lkAddEdit: null,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "企业名称",
            field: "entpNm",
            type: "text",
            dbfield: "entp_nm",
            dboper: "cn"
          },
          {
            name: "牵引车",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn"
          },
          {
            name: "状态",
            field: "alarmQty",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "正常", value: "0" },
              { label: "异常", value: "1" }
            ],
            dbfield: "alarm_qty",
            dboper: "eq"
          }
        ]
      },
      id: "",
      tableHeight: 500,
      // 加载状态
      loading: true,
      // 状态弹出框
      dialogVisible1: false,
      // 备忘弹出框
      dialogVisible2: false,
      // 新增/编辑弹出框
      dialogVisible4: false,
      // 表单搜索数据
      form1: {
        enterpriseName: "",
        vehicleBrand: "",
        state: ""
      },
      // 备忘弹出框表单数据
      form2: {
        desc: "",
        remark: []
      },
      // 新增弹出框表单数据
      form3: {
        entpNm: "",
        vecNo: ""
      },
      // 编辑弹出框表单数据
      form4: {
        entpNm: "",
        vecNo: ""
      },
      // 表格数据
      tableData1: [],

      // 异常状态弹出框表格数据
      tableData2: [],
      selectedRowId: null,
      tablePagination: { total: 0, page: 1, limit: 10 }
    };
  },
  methods: {
    // 获取数据
    getList: function(data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      filters.rules.forEach(function(val, index) {
        console.log(val, index);
        if (val.data == 1) {
          val.data = 0;
          val.op = "ne";
        }
      });
      let param = Object.assign(
        {
          sidx: "crtTm",
          order: "DESC"
        },
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;
      this.listLoading = true;
      getVehicleLock(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.page.list;
            let entpPks;
            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 状态异常
    async stateDetails(id) {
      this.dialogVisible1 = true;

      if (id) {
        this.selectedRowId = id;
      }
      let param = Object.assign(
        {
          filters: {
            groupOp: "AND",
            rules: [
              { field: "vec_lock_id", op: "eq", data: this.selectedRowId }
            ]
          }
        },
        this.tablePagination
      );
      delete param.total;
      let res = await getVehicleAbnormalDetails(param);
      if (res.code !== 0) {
        this.$message.error(res.msg);
        return;
      }
      this.tableData2 = res.page.list;
      this.tablePagination.total = res.page.totalCount;
    },
    // 分页跳转
    handleCurrentChangeOfTable: function(val) {
      this.tablePagination.page = val;
      this.stateDetails();
    },

    // 分页条数修改
    handleSizeChangeOfTable: function(val) {
      this.tablePagination.limit = val;
      // this.getList();
      this.stateDetails();
    },

    // 备忘
    notesDetails(row) {
      this.dialogVisible2 = true;
      this.id = row.id;
      this.form2.remark = JSON.parse(row.remark);
    },
    // 状态》轨迹
    dalLocus(row) {
      let { href } = this.$router.resolve({
        path: "/monit/hisTrack",
        query: { v: row.vecNo, t: row.rcdDt.substring(0, 10) }
      });
      window.open(href, "_blank");
    },
    // 状态》电子运单
    dlWaybill(row) {
      // this.$router.push({
      //   path: `/base/rteplan/bills/${row.rtePlanId}`
      // });
      window.open(
        window.location.origin +
          window.location.pathname +
          "#/base/rteplan/bills/" +
          row.rtePlanId,
        "_blank"
      );
    },
    // 编辑/新增
    AddEdit(is, row) {
      if (is == 1) {
        this.id = row.id;
        this.form4.entpNm = row.entpNm;
        this.form4.vecNo = row.vecNo;
        this.dialogVisible4 = true;
      } else if (is == 0) {
        (this.form4.entpNm = ""), (this.form4.vecNo = "");
        this.dialogVisible4 = true;
      }
    },
    // 备忘提交
    async submit() {
      console.log(this.id);
      if (this.form2.desc.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入内容"
        });
        return;
      }
      let res = await setMemoSubmit({ id: this.id, remark: this.form2.desc });
      if (res.code !== 0) {
        return;
      }
      this.$message({
        type: "success",
        message: "提交备忘成功"
      });
      this.dialogVisible2 = false;
      this.getList();
    },
    // 备忘关闭
    closed() {
      this.form2.desc = "";
    },
    DVedit(is) {
      if (is == 1) {
        let params = Object.assign({}, this.form4, { id: this.id });
        this.$refs.editForm.validate(valid => {
          if (valid) {
            lkEdit(params).then(res => {
              console.log(res);
              if (res && res.code == 0) {
                this.$message({
                  message: "编辑成功",
                  type: "success"
                });
                this.getList();
                this.dialogVisible4 = false;
              }
            });
          } else {
            this.$message({
              message: "对不起，您的信息填写不正确",
              type: "error"
            });
          }
        });
      } else if (is == 0) {
        let params = Object.assign({}, this.form4);
        this.$refs.editForm.validate(valid => {
          if (valid) {
            lkAdd(params).then(res => {
              console.log(res);
              if (res && res.code == 0) {
                this.$message({
                  message: "新增成功",
                  type: "success"
                });
                this.getList();
                this.dialogVisible4 = false;
              }
            });
          } else {
            this.$message({
              message: "对不起，您的信息填写不正确",
              type: "error"
            });
          }
        });
      }
    },
    // 删除
    notesDelete(id) {
      // let params = Object.assign({}, [{id}]);
      lkDelete([id]).then(res => {
        console.log(res);
        this.$confirm("确认删除？")
          .then(_ => {
            this.getList();
            this.$message({
              message: "删除成功",
              type: "success"
            });
          })
          .catch(_ => {});
      });
    },
    // 输入开启搜索功能
    querySearchAsync(query, cb) {
      if (query !== "") {
        setTimeout(() => {
          // 接口请求的操作内容
          lkVehicle({
            filters: {
              groupOp: "AND",
              rules: [{ field: "vec_no", op: "cn", data: query }]
            }
          }).then(res => {
            res.page.list.forEach((item, index) => {
              this.options.push({
                value: item.vecNo,
                value2: item.ownedCompany,
                label: item.vecNo
              });
            });
            var vecNos = res.page.list;
            var results = vecNos.map(item => {
              return { value: item.vecNo };
            });
            cb(results);
          });
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 选择时触发
    handleSelect(val) {
      if (val.value) {
        let nm = this.options.filter(item => {
          return item.value == val.value;
        });
        this.form4.entpNm = nm[0].value2;
      }
    },
    // 清空时触发
    handleClear() {
      this.form4.entpNm = "";
    },
    // 下拉框开启搜索功能
    dataFilter(query) {
      if (query !== "") {
        this.options = [];
        setTimeout(() => {
          // 接口请求的操作内容
          lkVehicle({
            filters: {
              groupOp: "AND",
              rules: [{ field: "vec_no", op: "cn", data: query }]
            }
          }).then(res => {
            res.page.list.forEach((item, index) => {
              this.options.push({
                value: item.vecNo,
                value2: item.ownedCompany,
                label: item.vecNo
              });
            });
          });
        }, 500);
      }
    },
    // 选择时触发
    Cchange(val) {
      if (val) {
        let nm = this.options.filter(obj => {
          return obj.value == val;
        });
        this.form4.entpNm = nm[0].value2;
      }
    },
    // 清空时触发
    Cclear() {
      this.form4.entpNm = "";
    },
    // 新增、编辑关闭触发
    closed2() {
      this.options = [];
    },
    // 表格自适应浏览器高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    }
  }
};
</script>

<style scoped>
.amVehicleList {
  padding: 10px;
}
.el-pagination {
  text-align: right;
}
.el-timeline {
  padding: 0px;
}
.btn {
  color: red;
}
.form1 {
  display: flex;
  justify-content: space-between;
}
.btnDesc {
  width: 100%;
}
.alink {
  color: #ffffff;
}
</style>
