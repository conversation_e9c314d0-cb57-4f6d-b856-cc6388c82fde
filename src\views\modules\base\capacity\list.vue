<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
    </searchbar>
    <el-table
      v-loading="listLoading"
      :max-height="tableHeight"
      :data="list"
      class="el-table"
      highlight-current-row
      border
      style="width: 100%"
      @sort-change="handleSort"
    >
      <el-table-column type="index" label="序号" width="66px"></el-table-column>
      <el-table-column prop="ownedCompany" label="企业名称" >
        <template slot-scope="scope">
          <el-button type="text" @click="showDetail(scope.row)">{{ scope.row.ownedCompany }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="capacityRptUrl" label="运力申请报告">
        <template slot-scope="scope">
          <template v-for="(item,index) in scope.row.capacityRptUrl.split(',')" >
            <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
              :key="index"
              :src="item"
              :preview-src-list="[item]">
            </el-image>
            <span v-else-if="isPdf(item)" @click="showPdf(item)">
              <svg-icon  icon-class="pdf" class-name="svg-icon" ></svg-icon>
            </span>
          </template>
          
        </template>
      </el-table-column>
      <el-table-column prop="transRptUrl" label="运输合同报告">
        <template slot-scope="scope">
          <template v-for="(item,index) in scope.row.transRptUrl.split(',')" >
            <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
              :key="index"
              :src="item"
              :preview-src-list="[item]">
            </el-image>
            <span v-else-if="isPdf(item)" @click="showPdf(item)">
              <svg-icon  icon-class="pdf" class-name="svg-icon" ></svg-icon>
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="planVecCount" label="计划新增车辆（辆）"></el-table-column>
      <el-table-column prop="planPersCount" label="计划新增人员（个）"></el-table-column>
      <el-table-column prop="auditVecCount" label="审核通过车辆（辆）"></el-table-column>
      <el-table-column prop="auditPersCount" label="审核通过人员（个）"></el-table-column>
      <el-table-column prop="auditStatus" label="审核状态">
        <template slot-scope="scope">
          <el-popover
            v-if="scope.row.auditRmks"
            placement="top-start"
            width="200"
            trigger="hover"
            :content="scope.row.auditRmks">
            <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 1" slot="reference" type="success">通过</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 2" slot="reference" type="danger">未通过</el-tag>
          </el-popover>
          <template v-else>
            <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 1" type="success">通过</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 2" type="danger">未通过</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="updTm" label="申请时间"></el-table-column>
      <el-table-column prop="" label="操作" width="80px">
        <template slot-scope="scope">
          <el-button type="text" size="small" icon="el-icon-s-check" @click="approveHandle(scope.row)">审核</el-button>
          <!-- <el-button type="danger" size="small" icon="el-icon-close" @click="rejectHandle(scope.row)">驳回</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        background
        layout="sizes, prev, pager, next, total"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog :visible.sync="visible" :title="dialogTitle" :close-on-click-modal="false">
      <el-form label-width="160px" inline>
        <el-form-item label="该企业当前车辆数：">
          <span style="font-size: 18px;font-weight:bold;color:red;">{{ capacityApplicationInfo.auditVecTotal }}</span> / 
          <span style="font-size: 15px;font-weight:bold;color: #0abe0a;">{{ capacityApplicationInfo.vecTotal }}</span>
        </el-form-item>
        <el-form-item label="该企业当前人员数：">
          <span style="font-size: 18px;font-weight:bold;color:red;">{{ capacityApplicationInfo.auditPersTotal }}</span> / 
          <span style="font-size: 15px;font-weight:bold;color: #0abe0a;">{{ capacityApplicationInfo.persTotal }}</span>
        </el-form-item>
      </el-form>
      <el-form :model="auditForm" label-width="180px" size="small" ref="auditForm">
          <el-form-item label="是否通过" prop="auditStatus" :rules="$rulesFilter({ required: true})">
            <el-radio-group v-model="auditForm.auditStatus" @change="radioGroupChange">
              <el-radio-button :label="1">通过</el-radio-button>
              <el-radio-button :label="2">驳回</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <template v-if="auditForm.auditStatus == 1">
            <el-form-item label="同意新增车辆数（辆）" prop="auditVecCount" :rules="$rulesFilter({ required: true})">
              <el-input-number v-model="auditForm.auditVecCount" :min="0"  label=""></el-input-number>
            </el-form-item>
            <el-form-item label="同意新增人员数（个）" prop="auditPersCount" :rules="$rulesFilter({ required: true})">
              <el-input-number v-model="auditForm.auditPersCount" :min="0"  label=""></el-input-number>
            </el-form-item>
            <el-form-item label="备注" prop="auditRmks" >
              <el-input v-model="auditForm.auditRmks" type="textarea"></el-input>
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="驳回原因" prop="auditRmks" :rules="$rulesFilter({ required: true})">
              <el-input v-model="auditForm.auditRmks" type="textarea"></el-input>
            </el-form-item>
          </template>
          <el-form-item align="center">
            <el-button type="primary" icon="el-icon-check" @click="saveHandle">提交</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>

    <!-- <el-dialog :visible.sync="visible2" title="驳回" :close-on-click-modal="false">
      <el-form :model="auditForm" label-width="160px" size="small" ref="rejectForm">
          <el-form-item label="驳回原因" prop="auditRmks" :rules="$rulesFilter({ required: true})">
            <el-input v-model="auditForm.auditRmks" type="textarea"></el-input>
          </el-form-item>
          <el-form-item align="center">
            <el-button type="primary" icon="el-icon-check" @click="rejectSaveHandle">驳回</el-button>
          </el-form-item>
        </el-form>
    </el-dialog> -->

    <!--  pdf预览  -->
    <pdf-view v-show="pdfViewVisible" :src="pdfSrc" @filePreviewCancel="filePreviewCancel"></pdf-view>
  </div>
</template>

<script>
import * as $http from "@/api/vec"
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import pdfView from "@/components/pdf-view"

export default {
  name: "",
  components: {
    Searchbar,
    pdfView
  },
  data() {
    return {
      auditForm:{
        "id":null,
        "auditVecCount":null,
        "auditPersCount":null,
        "auditStatus":2, // 审核状态 1：通过， 2：驳回
        "auditRmks":""
      },
      pdfViewVisible:false,
      pdfSrc:"",
      visible:false,
      visible2:false,
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      dialogTitle:"",
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      searchItems: {
      normal: [
        { name: "企业名称", field: "ownedCompany", type: "text", dbfield: "owned_company", dboper: "cn" },
        {
          name: "审核状态",
          field: "auditStatus",
          type: "radio",
          options: [
            { label: "全部", value: "" },
            { label: "待审核", value: "0" },
            { label: "审核通过", value: "1" },
            { label: "审核未通过", value: "2" }
          ],
          dbfield: "audit_status",
          dboper: "eq",
          default:""
        }
      ],
      more: [],
    },
    capacityApplicationInfo:{}
    };
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });
  },
  methods: {
    radioGroupChange(){
      this.auditForm.auditRmks = "";
    },
    showPdf(url){
      console.log('url',url)
      this.pdfSrc = url;
      this.pdfViewVisible = true;
    },
    filePreviewCancel(){
      this.pdfViewVisible = false;
      this.pdfSrc = "";
    },
    isImg(src){
     return /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src)
    },
    isPdf(src){
     return /.pdf?$/.test(src)
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 205 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    // 获取数据
    getList: function (data, sortParam) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;
      this.listparam = param;
      this.listLoading = true;
      $http
        .capacityPage(param)
        .then(response => {
          if (response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: "/base/entp/info/" + row.entpPk,
        params: row
      });
    },
    approveHandle(row){
      const auditForm = this.auditForm;
      auditForm.id = row.id;
      auditForm.auditVecCount = row.planVecCount;
      auditForm.auditPersCount = row.planPersCount;
      auditForm.auditRmks = "";
      this.visible = true;
      this.dialogTitle = "申请审核"+(row.ownedCompany ? '（'+row.ownedCompany+'）' : '');
      this.getCapacityApplicationInfo(row.id)
    },
    getCapacityApplicationInfo(id){
       $http.capacityApplicationInfo(id).then( res => {
         if(res && res.data && res.entpInfo){
          this.capacityApplicationInfo = res.entpInfo;
         }
       })
    },
    // rejectHandle(row){
    //   const auditForm = this.auditForm;
    //   auditForm.id = row.id;
    //   this.visible2 = true;
    // },
    rejectSaveHandle(){
      this.$refs.auditForm.validate( valid => {
        if(valid){
          let param = Object.assign({}, this.auditForm)
          param.auditStatus = 2;
          param.auditVecCount = 0;
          param.auditPersCount = 0;

          $http.capacityAudit(param).then( res => {
            if(res && res.code == 0){
              this.$message.success('审核成功')
              this.getList()
              this.visible = false;
            }
          })
        }
      })
    },
    allowSaveHandle(){
      this.$refs.auditForm.validate( valid => {
        if(valid){
          let param = Object.assign({}, this.auditForm)
          param.auditStatus = 1;

          $http.capacityAudit(param).then( res => {
            if(res && res.code == 0){
              this.$message.success('审核成功')
              this.getList()
              this.visible = false;
            }
          })
        }
      })
    },
    saveHandle(){
      if(this.auditForm.auditStatus == 2){
        this.$confirm('是否驳回企业申请?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.rejectSaveHandle()
        })
      }else{
        this.allowSaveHandle()
      }
    },

    delHandle(row){
      this.$confirm('此操作将删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $http.capacityDel({id:row.id}).then( res => {
           if(res && res.code == 0){
            this.$message.success("删除成功")
            this.getList()
           }
        })
      }).catch(() => {
               
      });
       
    }
  },
};
</script>

<style lang="scss" scoped>
.svg-icon {
  font-size: 70px;
  cursor: pointer;
  vertical-align: middle;
}
</style>