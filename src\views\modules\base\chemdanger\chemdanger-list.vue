<template>
  <div class="enchDoc-app-main-content">
    <transition name="el-fade-in-linear">
      <div class="chart-main">
        <div>
          <div class="grid-btn">
            <el-col :span="24" class="toolbar" style="padding-bottom: 0px;" ref="searchbar">
              <el-form :inline="true" :model="params" @submit.native.prevent>
                <el-form-item>
                  <el-input v-model="params.param" style="width:400px;" placeholder="名称,别名,CAS号" @keyup.enter.native="refreshGrid" @change="pagination.page=1"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="success" icon="el-icon-search" size="small" v-on:click="query">搜索
                  </el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </div>
          <!--列表-->
          <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border style="width: 100%;" :max-height="tableHeight">
            <el-table-column prop="nm" label="名称" width="150px">
              <!-- <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.nm}}</div>
                  <el-button slot="reference" @click.native.prevent="showDetail(scope.row)" type="text">
                    {{scope.row.nm}}
                  </el-button>
                </el-popover>
              </template> -->
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.nm}}</div>
                  <span slot="reference">{{scope.row.nm}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="aliasNm" label="别名">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.aliasNm}}</div>
                  <span slot="reference">{{scope.row.aliasNm}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="englishNm" label="英文名称">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.englishNm}}</div>
                  <span slot="reference">{{scope.row.englishNm}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="casNumber" label="CAS号">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.casNumber}}</div>
                  <span slot="reference">{{scope.row.casNumber}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="hazardClass" label="危险性类型">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.hazardClass}}</div>
                  <span slot="reference">{{scope.row.hazardClass}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="memo" label="备注">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.memo}}</div>
                  <span slot="reference">{{scope.row.memo}}</span>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
          <!--工具条-->
          <div class="toolbar clearfix" ref="paginationbar">
            <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]" style="float:right;" :page-size="pagination.limit"
              :current-page.sync="pagination.page" :total="pagination.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
            </el-pagination>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import * as $http from "@/api/danger";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";

export default {
  name: "chemdanger",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 150,
      title: null,
      delItems: [],
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      params: {
        param: ""
      },
      listLoading: false,
      chemDocRules: {},
      chemDetail: {},
      //创建实体类
      searchForm: {
        shortNmCn: "",
        fullNmCn: ""
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"])
  },
  mounted: function() {
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
    this.$nextTick(function() {
      this.getList();
    });
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        let searchbarHeight = this.$refs.searchbar
          ? this.$refs.searchbar.$el.offsetHeight
          : 0;
        let paginationbarHeight = this.$refs.paginationbar
          ? this.$refs.paginationbar.offsetHeight
          : 0;
        this.tableHeight =
          Tool.getClientHeight() - 125 - searchbarHeight - paginationbarHeight;
      });
    },
    query: function() {
      this.pagination.page = 1;
      this.getList();
    },
    showDetail: function(row) {
      this.$router.push({
        path: "/base/chemica/info/" + row.prodPk
        // params: row,
      });
    },
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      this.getList();
    },
    getList: function() {
      let _this = this;
      let param = Object.assign({}, this.params, this.pagination);
      delete param.total;

      this.listLoading = true;

      $http
        .getChemDanger(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.pageNumber;
            _this.pagination.limit = response.page.pageSize;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    refreshGrid() {
      this.getList();
    },
    resetGrid() {
      if (this.params.param == "") {
        this.getList();
      }
    }
  }
};
</script>

<style scoped>
.enchDoc-app-main-content {
  margin: 10px;
  padding: 20px;
  background: #fff;
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  overflow: hidden;
}

.el-table .cell,
.el-table th > div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
