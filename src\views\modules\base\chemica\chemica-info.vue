<template>
  <div class="mod-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper && !isCompn">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <el-form label-width="145px" class="clearfix">
          <el-row :gutter="20">

            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item label="货物名称">
                <span>{{ ench.fullNmCn }}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item label="CAS号">
                <span>{{ ench.cas || '' }}</span>
                <!-- <span>{{ showInfo('cas') }}</span> -->
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item label="UN号">
                <span>{{ ench.un || '' }}</span>
                <!-- <span>{{ showInfo('un') }}</span> -->
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="12" :lg="12">
              <el-form-item label="类别">
                <span>{{ ench.category || '' }}</span>
                <!-- <span>{{ showInfo('un') }}</span> -->
              </el-form-item>
            </el-col>

            <template v-if="ench && ench.chemMsdsEntity">
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="特别警示">
                  <span>{{ ench.chemMsdsEntity.specialWarning || ""}}</span>
                  <!-- <span>{{ showInfo('specialWarning') }}</span> -->
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="理化特性">
                  <span>{{ ench.chemMsdsEntity.properties || ""}}</span>
                  <!-- <span>{{ showInfo('properties') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24" style="">
                <el-alert title="危害信息 ▼" type="warning" :closable="false"></el-alert>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="燃烧和爆炸危险性">
                  <span>{{ ench.chemMsdsEntity.harmfulBurning || ""}}</span>
                  <!-- <span>{{ showInfo('harmfulBurning') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="活性反应">
                  <span>{{ ench.chemMsdsEntity.harmfulActive || ""}}</span>
                  <!-- <span>{{ showInfo('harmfulActive') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="健康危害">
                  <span>{{ ench.chemMsdsEntity.harmfulHealth || ""}}</span>
                  <!-- <span>{{ showInfo('harmfulHealth') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-alert title="安全措施 ▼" type="warning" :closable="false"></el-alert>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="一般要求">
                  <span>{{ ench.chemMsdsEntity.securityGeneral || ""}}</span>
                  <!-- <span>{{ showInfo('securityGeneral') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="操作安全">
                  <span>{{ ench.chemMsdsEntity.securityOpr || ""}}</span>
                  <!-- <span>{{ showInfo('securityOpr') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="存储安全">
                  <span>{{ ench.chemMsdsEntity.securityStorage || ""}}</span>
                  <!-- <span>{{ showInfo('securityStorage') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="运输安全">
                  <span>{{ ench.chemMsdsEntity.securityTransport || ""}}</span>
                  <!-- <span>{{ showInfo('securityTransport') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-alert title="应急处置原则 ▼" type="warning" :closable="false"></el-alert>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="急救措施">
                  <span>{{ ench.chemMsdsEntity.emergencyTreatment || ""}}</span>
                  <!-- <span>{{ showInfo('emergencyTreatment') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="灭火方法">
                  <span>{{ ench.chemMsdsEntity.emergencyOutfire || ""}}</span>
                  <!-- <span>{{ showInfo('emergencyOutfire') }}</span> -->
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="泄露应急处置">
                  <span>{{ ench.chemMsdsEntity.emergencyReveal || ""}}</span>
                  <!-- <span>{{ showInfo('emergencyReveal') }}</span> -->
                </el-form-item>
              </el-col>
            </template>
            <template v-else>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="救援措施">
                  <span v-html="ench.jycs || ''"></span>
                  <!-- <span>{{ showInfo('emergencyReveal') }}</span> -->
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
        <!-- 顶部信息 -->
        <!-- <ul class="mod-detail-ul">
          <li>
            <div class="mod-desc">中文名称：</div>
            <div class="mod-area">
              <span>{{ench.fullNmCn}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">英文名称：</div>
            <div class="mod-area">
              <span>{{ench.fullNmEn}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">别名：</div>
            <div class="mod-area">
              <span>{{ench.shortNmCn}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">GB编码：</div>
            <div class="mod-area">
              <span>{{ench.gb}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">UN编号：</div>
            <div class="mod-area">
              <span>{{ench.un}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">CAS号：</div>
            <div class="mod-area">
              <span>{{ench.cas}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">分子式：</div>
            <div class="mod-area">
              <span>{{ench.molFormula}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">分子量：</div>
            <div class="mod-area">
              <span>{{ench.molWeight}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">外观与性状：</div>
            <div class="mod-area">
              <span>{{ench.appAndProp}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">熔点：</div>
            <div class="mod-area">
              <span>{{ench.meltPot}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">沸点：</div>
            <div class="mod-area">
              <span>{{ench.boilPot}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">蒸汽压：</div>
            <div class="mod-area">
              <span>{{ench.vapourPres}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">闪点：</div>
            <div class="mod-area">
              <span>{{ench.flashPot}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">密度：</div>
            <div class="mod-area">
              <span>{{ench.dens}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">溶解性：</div>
            <div class="mod-area">
              <span>{{ench.solub}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">稳定性：</div>
            <div class="mod-area">
              <span>{{ench.stab}}</span>
            </div>
          </li>
          <li>
            <div class="mod-desc">危险标记：</div>
            <div class="mod-area">
              <span>{{ench.riskMark}}</span>
            </div>
          </li>
          <li class="info-all" style="width:100%;">
            <div class="mod-desc">主要用途：</div>
            <div class="mod-area">
              <span>{{ench.mainApp}}</span>
            </div>
          </li>
          <li class="info-all" style="width:100%;">
            <div class="dialog-info-all">
              <div class="mod-desc">应急方案：</div>
              <div class="mod-area" style="height:auto;" v-html="ench.contPlan">
              </div>
            </div>
          </li>
        </ul> -->
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>
<script>
import * as $http from "@/api/ench";
export default {
  name: "chemicaInfo",
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    },
    messageIpPk: {
      type: [Number, Boolean],
      default: false
    }
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      currentDate: new Date().getTime(),
      detailLoading: false,
      ench: {},
      licData: []
    };
  },
  created() {
    // if (!this.isCompn) {
    //当是页面时
    let prodPk = this.$route.params.id;
    if (prodPk) {
      this.getInfoByProdPk(prodPk);
    } /* else{
                this.$message.error('对不起，页面数据无法查询');
            } */
    /* let un = this.$route.params.id;
      if (un) {
        this.initByUn(un);
      } */
    // } else {
    //   this.isShowOper = false;
    //   if (this.messageIpPk) {
    //     this.initByPk(this.messageIpPk);
    //   }
    /* else {
				this.$message.error("对不起，数据无法查询");
			} */
    // }
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    }
  },
  watch: {
    messageIpPk(newValue, oldValue) {
      if (newValue === oldValue) {
      } else {
        this.initByPk(newValue);
      }
    }
  },
  methods: {
    showInfo(key) {
      if (this.ench) {
        return this.ench[key] || "";
      } else {
        return "";
      }
    },
    // 初始化
    initByUn(un) {
      let _this = this;
      this.detailLoading = true;
      $http
        .getMSDSByUn(un)
        .then(response => {
          _this.detailLoading = false;
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            _this.ench = response.data;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    initByPk(ipPk) {
      let _this = this;
      this.detailLoading = true;
      $http
        .getchemByPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            _this.ench = response.chem;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },

    initByEnchPk(ipPk) {
      let _this = this;
      this.detailLoading = true;
      $http
        .getChemByEnchPk(ipPk)
        .then(response => {
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            _this.ench = response.data;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },

    getInfoByProdPk(prodPk) {
      let _this = this;
      $http
        .getChemByProdPk(prodPk)
        .then(response => {
          if (response && response.code === 0) {
            // _this.licData = response.data.items;
            _this.$set(_this.$data, "ench", response.chem);
            // _this.ench = response.chem;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form-item /deep/ {
  line-height: 28px;
  margin-bottom: 0;
  .el-form-item__label {
    color: #333;
    font-weight: normal;

    &:after {
      content: ":";
    }
  }
  .el-form-item__content {
    color: #9a9a9a;
  }
}
.el-alert /deep/ {
  line-height: 20px;
  margin: 0 10px;
}
.panel-body {
  padding-right: 20px;
}
</style>
