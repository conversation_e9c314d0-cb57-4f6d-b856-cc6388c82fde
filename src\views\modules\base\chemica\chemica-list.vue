<template>
  <div class="enchDoc-app-main-content">
    <transition name="el-fade-in-linear">
      <div class="chart-main">
        <div>
          <div class="grid-btn">
            <el-col :span="24" class="toolbar" style="padding-bottom: 0px;" ref="searchbar">
              <el-form :inline="true" :model="params" @submit.native.prevent>
                <el-form-item>
                  <el-input v-model="params.param" style="width:400px;" placeholder="中文名称,UN号"
                    @keyup.enter.native="refreshGrid" @change="pagination.page = 1"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="success" icon="el-icon-search" size="small" v-on:click="query">搜索
                  </el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </div>
          <!--列表-->
          <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border
            style="width: 100%;" :max-height="tableHeight">
            <el-table-column prop="fullNmCn" label="中文名称" width="150px" show-overflow-tooltip>
              <template slot-scope="scope">
                <a class="btn" href="javascript:void(0)" title="移动" @click="showDetail(scope.row)">
                  {{ scope.row.fullNmCn }}
                </a>
                <!-- <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{ scope.row.fullNmCn }}</div>
                  <el-button slot="reference" @click.native.prevent="showDetail(scope.row)" type="text">
                    {{ scope.row.fullNmCn }}
                  </el-button>
                </el-popover> -->
              </template>
            </el-table-column>
            <el-table-column prop="fullNmEn" label="英文名称">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{ scope.row.fullNmEn }}</div>
                  <span slot="reference">{{ scope.row.fullNmEn }}</span>
                </el-popover>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="shortNmCn" label="货物简称">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.shortNmCn}}</div>
                  <span slot="reference">{{scope.row.shortNmCn}}</span>
                </el-popover>
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="enchAlias" label="企业别名">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.enchAlias}}</div>
                  <span slot="reference">{{scope.row.enchAlias}}</span>
                </el-popover>
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="gb" label="GB编码">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{scope.row.gb}}</div>
                  <span slot="reference">{{scope.row.gb}}</span>
                </el-popover>
              </template>
            </el-table-column> -->
            <el-table-column prop="un" label="UN号">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{ scope.row.un }}</div>
                  <span slot="reference">{{ scope.row.un }}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="类别">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{ scope.row.category }}</div>
                  <span slot="reference">{{ scope.row.category }}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="packKind" label="包装类别">
              <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{ scope.row.packKind }}</div>
                  <span slot="reference">{{ scope.row.packKind }}</span>
                </el-popover>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="cas" label="CAS号">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{scope.row.cas}}</div>
                  <span slot="reference">{{scope.row.cas}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="molFormula" label="分子式">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{scope.row.molFormula}}</div>
                  <span slot="reference">{{scope.row.molFormula}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="appAndProp" label="外观与性状">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{scope.row.appAndProp}}</div>
                  <span slot="reference">{{scope.row.appAndProp}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="stab" label="稳定性">
              <template slot-scope="scope">
                <el-popover placement="right-start" trigger="hover">
                  <div>{{scope.row.stab}}</div>
                  <span slot="reference">{{scope.row.stab}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="riskMark" label="危险标记">
              <template slot-scope="scope">
                <el-popover placement="left-start" trigger="hover">
                  <div>{{scope.row.riskMark}}</div>
                  <span slot="reference">{{scope.row.riskMark}}</span>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="mainApp" label="主要用途">
              <template slot-scope="scope">
                <el-popover placement="left-start" width="300" trigger="hover">
                  <div>{{scope.row.mainApp}}</div>
                  <span slot="reference">{{scope.row.mainApp}}</span>
                </el-popover>
              </template>
            </el-table-column> -->
          </el-table>
          <!--工具条-->
          <div class="toolbar clearfix" ref="paginationbar">
            <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
              :page-size="pagination.limit" :total="pagination.total" @size-change="handleSizeChange"
              @current-change="handleCurrentChange" :current-page.sync="pagination.page" style="float:right;">
            </el-pagination>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import * as $http from "@/api/ench";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";

export default {
  name: "enchDoc",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 150,
      title: null,
      delItems: [],
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      params: {
        param: ""
      },
      listLoading: false,
      chemDocRules: {},
      chemDetail: {},
      //创建实体类
      searchForm: {
        shortNmCn: "",
        fullNmCn: ""
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function (picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["appRegionNm"])
  },
  mounted: function () {
    const that = this;
    // let wh = Tool.getTableHeight();

    // this.tableHeight = wh - 70;
    // window.addEventListener("resize", function() {
    //   that.tableHeight = wh - 70;
    // });

    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
    this.$nextTick(function () {
      this.getList();
    });
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        let searchbarHeight = this.$refs.searchbar
          ? this.$refs.searchbar.$el.offsetHeight
          : 0;
        let paginationbarHeight = this.$refs.paginationbar
          ? this.$refs.paginationbar.offsetHeight
          : 0;
        this.tableHeight =
          Tool.getClientHeight() - 125 - searchbarHeight - paginationbarHeight;
      });
    },
    query: function () {
      this.pagination.page = 1;
      this.getList();
    },
    showDetail: function (row) {
      var _this = this;
      this.$router.push({
        path: "/base/chemica/info/" + row.prodPk
        // params: row,
      });
    },
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    getList: function () {
      let _this = this;
      let param = Object.assign({}, this.params, this.pagination);
      delete param.total;

      this.listLoading = true;

      $http
        .getChemList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.pageNumber;
            _this.pagination.limit = response.page.pageSize;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    refreshGrid() {
      this.getList();
    },
    resetGrid() {
      if (this.params.param == "") {
        this.getList();
      }
    }
  }
};
</script>

<style scoped>
.enchDoc-app-main-content {
  margin: 10px;
  padding: 20px;
  background: #fff;
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  overflow: hidden;
}

.el-table .cell,
.el-table th>div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
