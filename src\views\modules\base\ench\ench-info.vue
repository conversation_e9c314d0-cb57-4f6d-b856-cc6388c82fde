<template>
	<div class="detail-container" v-loading="detailLoading">
		<div class="mod-container-oper" v-if="isShowOper">
			<el-button-group>
				<el-button type="warning" @click="goBack">
					<i class="el-icon-back"></i>&nbsp;返回</el-button>
			</el-button-group>
		</div>
		<div class="panel">
			<div class="panel-header">
				<span class="panel-heading-inner">基本信息</span>
			</div>
			<div class="panel-body">
				<!-- 顶部信息 -->
				<ul class="detail-ul">
					<li>
						<div class="detail-desc">货物名称：</div>
						<div class="detail-area" :title="ench.nm">{{ench.nm}}</div>
					</li>
					<li>
						<div class="detail-desc">危化品名：</div>
						<div class="detail-area" :title="ench.chemNm">{{ench.chemNm}}</div>
					</li>
					<li>
						<div class="detail-desc">GB编码：</div>
						<div class="detail-area" :title="ench.chemGb">{{ench.chemGb}}</div>
					</li>
					<li>
						<div class="detail-desc">类别：</div>
						<div class="detail-area" :title="ench.chemGbLv">{{ench.chemGbLv}}</div>
					</li>
				</ul>
			</div>
			<!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
		</div>
		<!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
		<div class="panel" ref="licwape">
			<div class="panel-header">
				<span class="panel-heading-inner">证照信息</span>
			</div>
			<div class="panel-body lic-wape" style="background-color:#edf0f5">
				<certificates :data-source="licData" :cert-tepl-data="certTeplData"></certificates>
			</div>
			<!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
		</div>
		<!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
        <div class="mod-container-oper" v-if="isShowOper">
			<el-button-group>
				<el-button type="warning" @click="goBack">
					<i class="el-icon-back"></i>&nbsp;返回</el-button>
			</el-button-group>
		</div>
	</div>
</template>
<script>
import certificates from '@/components/Certificates';
import licConfig from '@/utils/licConfig';
import { getenchByPk } from '@/api/ench';
export default {
    name: 'EnchInfo',
    components: {
        certificates
    },
    props: {
        // 是否是组件，默认为false(页面)
        isCompn: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
			isShowOper:true,       // 默认为页面，显示操作栏
            currentDate: new Date().getTime(),
            detailLoading: false,
            certTeplData: null,
            ench: {},
            licData: []
        };
    },
    created() {
		if(!this.isCompn){   //当是页面时
            let ipPk = this.$route.params.id;
            if(ipPk){
                this.initByPk(ipPk);
            }else{
                this.$message.error('对不起，页面数据无法查询');
            }
		}else{
            this.isShowOper = false;
        }
		this.certTeplData = licConfig['ench'] || {};
	},
    computed: {
        key() {
            return this.$route.id !== undefined ? this.$route.id + +new Date() : this.$route + +new Date();
        }
    },
    methods: {
        // 初始化
        initByPk(ipPk) {
			let _this = this;
			
            this.detailLoading = true;
            getenchByPk(ipPk)
                .then(response => {
                    if (response && response.code === 0) {
                        // let {items} = response.data.items;
                        // _this.licData = response.data.items;
                        _this.ench = response.data.chem;
                    } else {
                        _this.$message({
                            message: response.msg,
                            type: 'error'
                        });
                    }
                    _this.detailLoading = false;
                })
                .catch(error => {
                    console.log(error);
                    _this.detailLoading = false;
                });
		},
		
		// 返回上一页
        goBack() {
            this.$router.go(-1);
        }
    }
};
</script>
