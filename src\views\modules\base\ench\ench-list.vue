<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
        <el-form :inline="true" :model="searchForm">
          <el-form-item>
            <el-input v-model="searchForm.nm" placeholder="货物名称" @keyup.enter.native="refreshGrid" size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="searchForm.chemNm" placeholder="危化品名" @keyup.enter.native="refreshGrid" size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="success" icon="el-icon-search" size="small"
                       v-on:click="refreshGrid">搜索
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </div>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border style="width: 100%;" :max-height="tableHeight">
      <el-table-column prop="nm" label="货物名称" width="150" sortable>
        <template slot-scope="scope">
          <el-button
            @click.native.prevent="showDetail(scope.row)"
            type="text">
            {{scope.row.nm}}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="chemNm" label="危险品学名"></el-table-column>
      <el-table-column prop="chemGb" label="GB编码"></el-table-column>
      <el-table-column prop="chemGbLv" label="类别"></el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit"  @click="update(scope.row)" title="编辑"></el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="del(scope.row.enchPk)" title="删除"></el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
                     @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]"
                     :total="pagination.total" style="float:right;">
      </el-pagination>
    </el-col>
  </div>
</template>
<script>
import { getenchList, delench} from '@/api/ench'
import * as Tool from '@/utils/tool'

export default{
	name:'EnchList',
	data (){
		return {
			tableHeight: 500,
            list: [],

			listLoading: false,
            addLoading: false,
            passValidDate:"",
            searchForm: {
                nm: "",
                chemNm: ""
            },
			ench:{

			},
			pagination:{
                total: 0,
                page: 1,
                limit: 20
            },
            pickerOptions2: {
                shortcuts: [{
                    text: '今天',
                    onClick :function(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime());
                        picker.$emit('pick', [start, end]);
                    }
                },{
                    text: '最近一周',
                    onClick :function(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick:function(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            }
        }
    },
	created (){
        let _this = this;
        let query = this.$route.query;
        Object.keys(query).forEach(key => {
            if(_this.searchForm[key] !== undefined){
                _this.searchForm[key] = decodeURIComponent(query[key])
            }
        });

		this.getList();
	},
	mounted: function () {
        const _this = this;
        this.tableHeight = Tool.getTableHeight();
        window.addEventListener('resize', function () {
            _this.tableHeight = Tool.getTableHeight();
        });
    },
	methods: {
        // 分页跳转
        handleCurrentChange: function (val) {
            this.pagination.page = val;
            this.getList();
        },

        // 分页条数修改
        handleSizeChange: function (val) {
            this.pagination.limit = val;
            this.getList();
        },

        // 获取数据
        getList: function () {
          let _this = this;
          this.listLoading = true;

          var rules=[];

	        if(this.searchForm.nm!= ""){
			   rules.push({"field": "nm", "op": "cn", "data": this.searchForm.nm.trim()});
			}

			if(this.searchForm.chemNm!= ""){
			   rules.push({"field": "chem_nm", "op": "cn", "data": this.searchForm.chemNm.trim()});
			}

             let filters= {
                    "groupOp": "AND",
                    "rules": rules
            }
                let param = Object.assign({},	{filters:filters},this.pagination);
          delete param.total;

          this.listLoading = true;
            getenchList(param).then(response => {
              if (response.code==0) {
                _this.pagination.total = response.page.totalCount;
                _this.list = response.page.list;
              } else {
                _this.list = [];
                _this.pagination.total = 0;
              }
                _this.listLoading = false;
            }).catch(error => {
                console.log(error);
                _this.listLoading = false;
            });
        },

		 // 修改审核状态
        refreshGrid: function () {
            this.getList();
        },

        // 详情
        showDetail: function(row){
            console.log(row);
            this.$router.push({path:'/base/ench/info/'+row.enchPk,params:row});
        }
    }
}
</script>
