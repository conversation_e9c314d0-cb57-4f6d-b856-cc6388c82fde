<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack"
          ><i class="el-icon-back"></i>&nbsp;返回</el-button
        >
      </el-button-group>
    </div>
    <div class="panel" v-loading="basicLoading">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
        <div style="position: absolute; right: 10px; top: 10px">
          <el-button-group size="small">
            <el-button size="small">
              <router-link
                tag="a"
                :to="{
                  path: '/base/vec/list',
                  query: { ownedCompany: entp.entpName },
                }"
                target="_blank"
              >
                车辆
              </router-link>
            </el-button>
            <el-button size="small">
              <router-link
                tag="a"
                :to="{
                  path: '/base/pers/list',
                  query: { ownedCompany: entp.entpName },
                }"
                target="_blank"
              >
                员工
              </router-link>
            </el-button>

            <el-button size="small">
              <router-link
                tag="a"
                :to="{
                  path: '/base/tank/list',
                  query: { ownedCompany: entp.entpName },
                }"
                target="_blank"
              >
                罐体
              </router-link>
            </el-button>

            <el-button size="small">
              <router-link
                tag="a"
                :to="{
                  path: '/base/rteplan/list',
                  query: { carrierNm: entp.entpName },
                }"
                target="_blank"
              >
                电子运单
              </router-link>
            </el-button>
          </el-button-group>
        </div>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">统一社会信用代码：</div>
            <div class="detail-area" :title="entp.uscCd">{{ entp.uscCd }}</div>
          </li>
          <li>
            <div class="detail-desc">企业名称：</div>
            <div class="detail-area" :title="entp.entpName">
              {{ entp.entpName }}
            </div>
          </li>
          <li>
            <div class="detail-desc">公司类型：</div>
            <div class="detail-area" :title="entp.legalRepIdType">
              {{ entp.legalRepIdType }}
            </div>
          </li>
          <li>
            <div class="detail-desc">企业业务分类：</div>
            <div class="detail-area" :title="entp.catNmCn">
              {{ entp.catNmCn }}
            </div>
          </li>
          <li>
            <div class="detail-desc">成立日期：</div>
            <div class="detail-area" :title="entp.establishDate">
              {{ entp.establishDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">营业期限：</div>
            <div class="detail-area" :title="entp.busiEndDate">
              {{ entp.busiEndDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">企业登记注册地：</div>
            <div class="detail-area" :title="entp.entpDist">
              {{ entp.entpDist }}
            </div>
          </li>
          <li>
            <div class="detail-desc">经营状态：</div>
            <div class="detail-area" :title="entp.regStat">
              {{ entp.regStat }}
            </div>
          </li>
          <li>
            <div class="detail-desc">法定代表人：</div>
            <div class="detail-area" :title="entp.legalRepNm">
              {{ entp.legalRepNm }}
            </div>
          </li>
          <li>
            <div class="detail-desc">发照日期：</div>
            <div class="detail-area" :title="entp.aprvDate">
              {{ entp.aprvDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">注册资本：</div>
            <div class="detail-area" :title="entp.regCaptital">
              {{ entp.regCaptital }}{{ entp.regCaptitalUnit }}
            </div>
          </li>
          <li>
            <div class="detail-desc">登记机关：</div>
            <div class="detail-area" :title="entp.regDept">
              {{ entp.regDept }}
            </div>
          </li>
          <li>
            <div class="detail-desc">紧急联系人：</div>
            <div class="detail-area" :title="entp.erNm">{{ entp.erNm }}</div>
          </li>
          <li>
            <div class="detail-desc">紧急联系人电话：</div>
            <div class="detail-area" :title="entp.erMob">{{ entp.erMob }}</div>
          </li>
          <li class="col-all">
            <div class="detail-desc">企业地址：</div>
            <div class="detail-area" :title="entp.location">
              {{ entp.location }}
            </div>
          </li>
          <li class="col-all">
            <div class="detail-desc">营业执照经营范围：</div>
            <div class="detail-area wrap-yes" :title="entp.businessScope">
              {{ entp.businessScope }}
            </div>
          </li>
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer">
        <el-col :sm="12" style="color: red">
          最近一次更新时间：<span v-if="entp.updTm">{{ entp.updTm }}</span>
        </el-col>
        <el-col :sm="12">
          <div class="text-right">
            审核状态：<span class="lic-status">
              <template v-if="entp.basicHandleFlag == ''">未提交</template>
              <template v-else-if="entp.basicHandleFlag === '1'"
                >审核通过</template
              >
              <template v-else-if="entp.basicHandleFlag === '2'">
                审核未通过，原因：
                <template v-if="entp.basicHandleRemark">{{
                  entp.basicHandleRemark
                }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="entp.basicHandleFlag === '0'">
                待受理
              </template>
            </span>
          </div>
          <!-- 审核操作按钮 -->
          <approve-bar
            v-permission="'appr:update'"
            :approve-info="basicApproveInfo"
            :reject-reasons="basicRejectReasons"
            @getPassStatus="getPassStatus"
            @getRjectStatus="getRjectStatus"
            @getHandleStat="getHandleStat"
          ></approve-bar>
        </el-col>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray"></span> 待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green"></span> 审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow"></span> 将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red"></span> 未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred"></span> 已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates
          :data-source="licData"
          :cert-tepl-data="certTeplData"
          :reject-reasons="licRejectReasons"
        ></certificates>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import approveBar from "@/components/approveBar";
import licConfig from "@/utils/licConfig";
import * as $http from "@/api/entp";

export default {
  name: "EntpInfo",
  components: {
    approveBar,
    certificates,
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      detailLoading: false,
      basicLoading: false,
      certTeplData: null,
      entp: {},
      licData: [],
      passSta: {},
      rejectSta: {},
      basicRejectReasons: [
        { reason: "未到区交通执法四中队进行企业资质登记", index: 1 },
        { reason: "未填写紧急联系人及联系方式", index: 2 },
        { reason: "基本信息空缺部分未填写完整", index: 3 },
        { reason: "4、有效期选择错误", index: 4 },
        { reason: "相关资料缺失，详情请拨打咨询服务电话 0574-86280661", index: 5 },
      ],
      licRejectReasons: [
        { reason: "统一社会信用代码填写错误", index: 1 },
        { reason: "道路运输经营许可证号填写错误", index: 2 },
        { reason: "有效期选择错误", index: 3 },
        { reason: "证照上传不清晰，信息无法审核", index: 4 },
        { reason: "证照已过期", index: 5 },
        { reason: "证照上传不符合要求", index: 6 },
        { reason: "证照未上传", index: 7 },
        { reason: "未上传彩色扫描或者彩色拍照证照", index: 8 },
      ],
      basicApproveInfo: {
        entityPk: "",
        catCd: "8010.207",
        licPk: "",
        statCd: "",
        desc: "",
      },
    };
  },
  created() {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }

    // 根据胡总要求，只有证照审核人员账号才显示企业补充材料
    if(!this.isGovCheckLic){
      delete licConfig['entp']['8010.204'];
    }
   
    this.certTeplData = licConfig["entp"] || {};
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    },
    // 判断是否为证照审核人员
    // 2025.05.21,沈莹提出稽查大队也需要查看企业补充材料是否审核通过
    isGovCheckLic(){
      let roleNameList = localStorage.getItem("roleName");
      if(roleNameList){
        roleNameList = roleNameList.split(',');
        return roleNameList.includes('gov_check_lic') || roleNameList.includes('gov_dljc');
      }
      return false
    }
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;
      _this.basicApproveInfo.entityPk = ipPk;
      this.detailLoading = true;
      if (ipPk) {
        $http
          .getEntpByEntpPk(ipPk)
          .then((response) => {
            if (response.code == 0) {
              _this.licData = response.data.items;
              _this.entp = response.data.entp;
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch((error) => {
            throw new Error(error);
            _this.detailLoading = false;
          });
      }
    },
    //获取通过审核操作的返回结果
    getPassStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.entp.basicHandleFlag = payload.handleFlag;
      }
    },
    //获取驳回审核操作的返回结果
    getRjectStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.entp.basicHandleFlag = payload.handleFlag;
        this.entp.basicHandleRemark = payload.remark;
      }
    },
    //点击通过或驳回审核操作的监听
    getHandleStat(type) {
      this.basicLoading = true;
    },
    editFormHandle: function () {
      this.$router.push({ path: "/base/entp/form/" });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style scoped>
a {
  text-decoration: none;
  color: inherit;
}
.panel-footer {
  overflow: hidden;
}
.panel-footer .fl-l {
  float: left;
}
.panel-footer .fl-r {
  float: right;
}
</style>
