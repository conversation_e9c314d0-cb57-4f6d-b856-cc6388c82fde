<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
      :max-height="tableHeight" border @sort-change="handleSort" :row-class-name="tableRowClassName"
      :cell-class-name="cellClassName">
      <el-table-column prop="entpName" label="企业名称" width="200" fixed="left">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.entpName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="uscCd" label="统一社会信用代码" width="180"></el-table-column>
      <el-table-column prop="licApproveResult" width="180" label="审核状态">
        <template slot-scope="scope" v-if="scope.row.licApproveResult">
          <auditStatus licType="entp" :result="scope.row.licApproveResult" :resultCd="scope.row.licApproveResultCd"
            :aiRemark="scope.row.licApproveRemark">
          </auditStatus>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in scope.row.licApproveResult
              .replace(/[\&quot;\{\}\s]/g,       .split(',')">
              <p v-if="item.includes('待审核')" style="color: #e6a23c" :key="index">
                {{ item.replace(/(?:待审核)/i, "待受理") }}
              </p>
              <p v-if="item.includes('审核通过')" style="color: green" :key="index">
                {{ item }}
              </p>
              <p v-if="item.includes('未通过')" style="color: red" :key="index">
                {{ item }}
              </p>
            </template>
            <div slot="reference" class="name-wrapper">
              <template v-for="(licApprove, index) in scope.row.licApproveResultCd
                .replace(/[\&quot;\{\}\s]/g,       .split(',')">
                <el-tag v-if="licApprove.includes('8010.207:0')" close-transition :type="'warning'" :key="index">基
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.207:1')" close-transition :type="'success'" :key="index">基
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.207:2') ||
                  licApprove.includes('基本信息审核未通过')
                  " close-transition :type="'danger'" :key="index">基
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.200:0')" close-transition :type="'warning'" :key="index">营
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.200:1')" close-transition :type="'success'" :key="index">营
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.200:2') ||
                  licApprove.includes('企业营业执照审核未通过')
                  " close-transition :type="'danger'" :key="index">营
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.201:0')" close-transition :type="'warning'" :key="index">组
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.201:1')" close-transition :type="'success'" :key="index">组
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.201:2') ||
                  licApprove.includes('企业组织机构代码证审核未通过')
                  " close-transition :type="'danger'" :key="index">组
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.202:0')" close-transition :type="'warning'" :key="index">税
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.202:1')" close-transition :type="'success'" :key="index">税
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.202:2') ||
                  licApprove.includes('企业税务登记证审核未通过')
                  " close-transition :type="'danger'" :key="index">税
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.203:0')" close-transition :type="'warning'" :key="index">道
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.203:1')" close-transition :type="'success'" :key="index">道
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.203:2') ||
                  licApprove.includes('企业道路运输经营许可证审核未通过')
                  " close-transition :type="'danger'" :key="index">道
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.204:0')" close-transition :type="'warning'" :key="index">安
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.204:1')" close-transition :type="'success'" :key="index">安
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.204:2') ||
                  licApprove.includes('企业安全责任承诺书审核未通过')
                  " close-transition :type="'danger'" :key="index">安
                </el-tag>
              </template>
            </div>
          </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column prop="completeVecRate" label="车辆待审比例">
        <template slot-scope="scope">
          <span v-if="scope.row.completeVecRate">
            {{ scope.row.completeVecRate }}
          </span>
          <span v-else>0/0</span>
        </template>
      </el-table-column>
      <el-table-column prop="completePersRate" label="人员待审比例">
        <template slot-scope="scope">
          <span v-if="scope.row.completePersRate">
            {{ scope.row.completePersRate }}
          </span>
          <span v-else>0/0</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="mobile" sortable="custom" label="注册手机"></el-table-column> -->
      <el-table-column label="紧急联系人/联系方式" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.erNm }}/</span>
          <span>{{ scope.row.erMob }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="completeDocRate" label="完成度">
        <template slot-scope="scope">
          <span v-if="scope.row.completeDocRate == undefined" style="color: red">0/3</span>
          <span v-else-if="scope.row.completeDocRate < 3" style="color: red">{{ scope.row.completeDocRate }}/3</span>
          <span v-else>{{ scope.row.completeDocRate }}/3</span>
        </template>
      </el-table-column>
      <el-table-column prop="isLicExpire" label="证件状态">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.isLicExpire == 1" :type="'info'">已到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 2" :type="'warning'">将到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 0" :type="'success'">正常</el-tag>
          <el-tag size="mini" v-else>未填报</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="notes(scope.row)" :style="{ color: scope.row.memo ? 'red' : '' }">备忘</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <!-- 备忘弹窗 -->
    <el-dialog :visible.sync="notesVisible" title="添加备忘" width="590px">
      <div style="width: 95%; margin: 0 auto" v-loading="notesDialogLoading">
        <el-form size="small" :model="memoData" label-width="55px" ref="memoForm">
          <el-form-item label="备忘:" :rules="$rulesFilter({ required: true })" prop="memo">
            <el-input v-model="memoData.memo" type="textarea" placeholder="请输入备忘内容" rows="3"></el-input>
          </el-form-item>
          <el-form-item align="right">
            <el-button type="success" @click="addMemo">提交</el-button>
          </el-form-item>
        </el-form>
        <!-- <hr size="1" style="background-color:#ccc;height:1px;width:100%;border:none;text-align:center;"> -->
        <el-collapse v-model="activeNames">
          <el-collapse-item title="备忘历史记录" name="1">
            <div style="max-height: 290px; overflow: auto">
              <el-steps direction="vertical" :active="0" :space="80" v-if="hisData">
                <el-step v-for="(item, index) in hisData.memo" :key="index" status="process" icon="el-icon-date">
                  <template>
                    <div slot="title">{{ item.key }}</div>
                    <div slot="description" style="width: 500px; word-wrap: break-word">
                      {{ item.value }}
                    </div>
                  </template>
                </el-step>
              </el-steps>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import auditStatus from "@/components/auditStatus";
import * as $http from "@/api/entp";
import { isExistBlackList } from "@/api/common";
import * as Tool from "@/utils/tool";
import Cookies from "js-cookie";

export default {
  name: "EntpList",
  data() {
    return {
      notesDialogLoading: false,
      memoData: {
        memo: ""
      },
      hisData: null,
      activeNames: ["1"],
      notesVisible: false,
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "统一社会信用代码",
            field: "uscCd",
            type: "text",
            dbfield: "usc_cd",
            dboper: "cn"
          },
          {
            name: "企业名称",
            field: "entpName",
            type: "text",
            dbfield: "entp_name",
            dboper: "cn"
          },
          {
            name: "区域",
            field: "entpDist",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "区内", value: "区内" },
              { label: "市内", value: "市内" },
              { label: "市外", value: "市外" }
            ],
            dbfield: "entp_dist",
            dboper: "nao",
            default: ""
          },
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" }
            ],
            dbfield: "is_lic_expire",
            dboper: "eq"
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              // { label: '未提交', value: '3'},
              { label: "待受理", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" }
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao"
            // default: '1'
          }
          //  { name:'业务类别', field:'businessType', type:'select',
          //     options: [
          //        { label: "所有业务类别", value: "" },
          //         { label: "生产企业", value: "2100.202.205" },
          //         { label: "运输企业", value: "2100.202.210" },
          //         { label: "仓储企业", value: "2100.202.215" },
          //         { label: "生产运输企业", value: "2100.202.220" },
          //         { label: "生产仓储企业", value: "2100.202.225" },
          //         { label: "运输仓储企业", value: "2100.202.230" },
          //        { label: "生产运输仓储企业", value: "2100.202.235" }
          //     ],
          //     dbfield:'cat_cd',dboper:'cn',default:'2100.202.210'
          //  }
        ],
        more: []
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function (picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  components: {
    Searchbar,
    auditStatus
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    this.approvalDefault(); //根据账号类型切换审核状态顺序
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === "licApproveResult") {
        return "overflow-visble"
      } else {
        return ""
      }
    },
    // 根据账号类型切换审核状态顺序
    approvalDefault() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.normal[4].default = "1"; //切换审核状态默认顺序
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $http
        .getEntpList(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.page.list;
            let entpPks;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;

            entpPks = list.map((item, index) => {
              var entpPk = item.ipPk;
              return entpPk;
            });

            if (entpPks.length > 0) {
              entpPks = entpPks.join(",");
              _this.getEntpDocComplate(entpPks); //获取完成度
              _this.getVecApprvCnt(entpPks); //获取车辆待审核比例
              _this.getPersApprvCnt(entpPks); //获取人员待审核比例
              _this.isExistBlack(entpPks);
            }
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    //获取企业完成度
    getEntpDocComplate(entpPks) {
      let _this = this;
      let list = _this.list;
      let entpPk, total, ipPk;
      $http
        .countEntpDocComplete(entpPks)
        .then(response => {
          response.filter((item, index) => {
            entpPk = item.entpPk;
            total = item.total;

            total *= 1; //强制转换为数值类型

            list.filter((item, index) => {
              ipPk = item.ipPk;
              if (entpPk == ipPk) {
                _this.$set(item, "completeDocRate", total);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取车辆待审核比例
    getVecApprvCnt(entpPks) {
      let _this = this;
      let list = this.list;
      let entpPk,
        total,
        nopass,
        nopassRate = "",
        ipPk;
      $http
        .vecApprvCnt(entpPks)
        .then(response => {
          if (!!response.length) {
            response.filter((item, index) => {
              entpPk = item.entpPk;
              total = item.total;
              nopass = item.noPass;

              list.filter((item, index) => {
                ipPk = item.ipPk;
                if (entpPk == ipPk) {
                  nopassRate = nopass + "/" + total;
                  _this.$set(item, "completeVecRate", nopassRate);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取人员待审核比例
    getPersApprvCnt(entpPks) {
      let _this = this;
      let list = this.list;
      let entpPk, total, nopass, nopassRate, ipPk;
      $http
        .persApprvCnt(entpPks)
        .then(response => {
          response.filter((item, index) => {
            entpPk = item.entpPk;
            total = item.total;
            nopass = item.noPass;

            list.filter((item, index) => {
              ipPk = item.ipPk;
              if (entpPk == ipPk) {
                nopassRate = nopass + "/" + total;
                _this.$set(item, "completePersRate", nopassRate);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
    // 判断企业是否在黑名单
    isExistBlack(entpPks) {
      let _this = this;
      let list = this.list;
      let entpPk, result, ipPk;

      isExistBlackList({ ids: entpPks, type: "企业" })
        .then((response) => {
          if (response.code == 0) {
            response.data.filter((it, index) => {
              entpPk = it.pk;
              result = it.type;
              list.filter((item, index) => {
                ipPk = item.ipPk;
                if (entpPk == ipPk) {
                  _this.$set(item, "isExistBlackList", result);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tableRowClassName(scope, rowIndex) {
      if (scope.row.isExistBlackList && scope.row.isExistBlackList == "1") {
        return "warning-row";
      }
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: "/base/entp/info/" + row.ipPk,
        params: row
      });
    },
    //备忘
    notes(row) {
      this.notesVisible = true;
      this.memoData.memo = "";
      if (row.memo) {
        row.memo = JSON.parse(row.memo).reverse();
      }
      this.$set(this.$data, "hisData", row);
    },
    //添加备忘
    addMemo() {
      this.$refs.memoForm.validate(valid => {
        if (valid) {
          let param = {
            ipPk: this.hisData.ipPk,
            memo: this.memoData.memo
          };
          this.notesDialogLoading = true;
          $http
            .addmemo(param)
            .then(res => {
              this.notesDialogLoading = false;
              if (res.code == 0) {
                this.notesVisible = false;
                this.$message.success("添加成功!");
                if (res.data)
                  this.$set(
                    this.hisData,
                    "memo",
                    JSON.parse(res.data).reverse()
                  );
              } else {
                this.$message.error(res.msg || "服务器错误，请联系管理员");
              }
            })
            .catch(err => {
              this.notesDialogLoading = false;
              this.$message.error(err.msg || "服务器错误，请联系管理员");
            });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main-content {
  & /deep/ .el-table {
    & .warning-row {
      background-color: #ff4d4f;
      color: #fff;

      &>td {
        background-color: #ff4d4f !important;

        >div.cell {
          >span {
            color: #fff !important;
          }
        }
      }

      .el-button {
        color: #0050b3 !important;
      }
    }

    & .warning-row.hover-row {
      background-color: #eb5c5e !important;
      color: #fff;

      &>td {
        background-color: #eb5c5e !important;
        color: #fff;
      }
    }

    & tr.warning-row.current-row {
      background-color: #f25557 !important;
      color: #fff;

      &>td {
        background-color: #f25557 !important;
        color: #fff;
      }
    }

  }
}
</style>
