<template>
  <div class="detail-container" v-loading="loading">
    <div id="locMap" v-bind:style="{'height':maxHeight+'px'}"></div>
    <div class="search-wape">
      <el-autocomplete
        v-model="searchItem"
        :fetch-suggestions="querySearch"
        value-key="label"
        placeholder="请输入内容"
        @select="showBySearch('select')"
        clearable
        >
        <el-button type="success" slot="append" icon="el-icon-search" @click="showBySearch('fuzzy')"></el-button>
      </el-autocomplete>
    </div>
    <div class="vec-total-wape">
      <el-button class="vec-total-btn" :class="[typeOfTheShow==='all'?'active':'']" @click="showByType('all')">全部 {{allFencesCount}}</el-button>
      <template v-for="typeItem in fenceTypeList">
        <el-button class="vec-total-btn" :class="[typeOfTheShow===typeItem.cd?'active':'']" @click="showByType(typeItem.cd)" :key="typeItem.cd">
          {{typeItem.nmCn}}{{allFenceObj[typeItem.cd]?allFenceObj[typeItem.cd].length:0}}</el-button>
      </template>
    </div>
  </div>
</template>


<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/fenceall";
import imgsConfig from 'static/jsonConfig/parking.icon.json'

export default {
  data() {
    return {
        map:null,
        infoWin:null,
        maxHeight: 500,
        loading:false,
        typeOfTheShow: "all", // 显示的当前车辆类型，默认显示重车
        fenceTypeList: [],
        searchItem:'',
        searchOptions:[],//搜索建议列表
        typeOfTheShow:'all',                  // 显示的当前车辆类型，默认显示重车
        allFenceObj: {},

    };
  },
  computed: {
    allFencesCount() {
      let count = 0;
      let _this = this;
      Object.keys(this.allFenceObj).forEach((key) => {
        count += _this.allFenceObj[key].length;
      });
      return count;
    },
  },
  created() {
    this.getFenceTypeList();
    this.getAreaList();
  },
  mounted() {
    let maxHeight = Tool.getClientHeight();
    let _this = this;
    this.maxHeight = maxHeight - 80;
    window.addEventListener("resize", function() {
      _this.maxHeight = Tool.getClientHeight() - 80;
    });
    this.$nextTick(() => {
      this.loadMap();
    });
  },
  methods: {
    loadMap() {
      let _this = this;
      let map = new BMap.Map("locMap");
      map.centerAndZoom(new BMap.Point(121.66386, 30.001186), 13); // 初始化地图,设置中心点坐标和地图级别
      map.enableScrollWheelZoom(); //开启鼠标滚轮缩放

      this.map = map;
      //添加控件
      this.map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP]
        })
      );


    },
    // 获取围栏类型
    getFenceTypeList() {
      let _this = this;
      $http.getFenceTypeList().then((res) => {
        if (res.code == 0) {
          _this.fenceTypeList = res.data;

        } else {
          _this.fenceTypeList = [];
          _this.$message({
            type: "error",
            message: res.msg || "围栏获取失败",
          });
        }
      });
    },
    //搜索建议
    querySearch(queryString, cb){
      if (queryString.length > 1){
        var results = queryString ? this.searchOptions.filter(this.createFilter(queryString)) : this.searchOptions;
        cb(results);
      }else {
        cb([])
      }
    },
    //模糊查询数据筛选
    createFilter(queryString) {
      return (list) => {
        return (list.value.indexOf(queryString) > -1);
      };
    },
    //获取列表
    getAreaList() {
      let _this = this;
      this.loading = true;
      $http
        .getOneMapList()
        .then((res) => {
          if (res.code == 0) {
            res.list.forEach((item, i) => {
              let arr = _this.allFenceObj[item.type];
              if (arr === undefined) {
                _this.$set(_this.allFenceObj, item.type, []);
              }
              _this.allFenceObj[item.type].push(item);
              _this.searchOptions.push({
                label:item.name,
                value:item.name
              })
            });
            // 绘制围栏
            _this.showByType("all");
          }
          _this.loading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.loading = false;
        });
    },

    //画围栏
    drawPolygon(item){
      let _this = this
      let itemLngLat = item
      let overlay = null
      let bdPoint = null
      let point,pointArr;
      if(item.lnglat){
          point = JSON.parse(item.lnglat)
          pointArr = point.map(iitem => {
              return new BMap.Point(iitem.lng, iitem.lat);
          })
          overlay = new BMap.Polygon(pointArr, {
              strokeWeight: 2,
              strokeColor: "#e12828"
          });
          bdPoint = this.getCenterPoint(pointArr)

        if(bdPoint){
          _this.map.addOverlay(overlay); //添加覆盖物
          overlay.addEventListener("click", function() {
            _this.createInfoWin(bdPoint,_this.createInfo(itemLngLat))
            // _this.map.setViewport(pointArr); //调整地图视口
          });
          let iconUrl =
            imgsConfig.parkinglot[itemLngLat.type] ||
            "static/img/monitor-img/7_other.png";
          this.drawMarker(
            bdPoint,
            new BMap.Icon(iconUrl, new BMap.Size(32, 32)),
            itemLngLat
          );
        }
      }
    },
    drawMarker(Tlnglat,icon,itemLngLat){
      let _this = this
      icon.setImageSize(new BMap.Size(32,32));//设置图标大小
      //创建标注对象
      let marker = new BMap.Marker(Tlnglat);
      marker.setIcon(icon)
      //向地图上添加标注
      this.map.addOverlay(marker);
      marker.addEventListener("click", function() {
        _this.createInfoWin(Tlnglat,_this.createInfo(itemLngLat))
      });
    },
    //根据类型过滤
    showByType(type) {
      let _this = this;
      this.typeOfTheShow = type;
      this.map.clearOverlays();
      if (type === "all") {
        Object.keys(this.allFenceObj).forEach((key) => {
          _this.allFenceObj[key] &&
          _this.allFenceObj[key].forEach((item) => {
            _this.drawPolygon(item);
          });
        });
      } else {
        this.allFenceObj[type] &&
        this.allFenceObj[type].forEach((item) => {
          _this.drawPolygon(item);
        });
      }
    },

    //根据搜索过滤(select,fuzzy)
    showBySearch(type){
      let _this = this;
      this.map.clearOverlays();
      let centerPoint = []
      Object.keys(this.allFenceObj).forEach(key =>{
        _this.allFenceObj[key].forEach((item) => {
          if (type === 'select'){
            //单个地点选择
            if(item.name === _this.searchItem){
              _this.drawPolygon(item);
              centerPoint = JSON.parse(item.lnglat)
            }
          }else{
            //多个地点模糊搜索
            if(item.name.indexOf(_this.searchItem) > -1){
              _this.drawPolygon(item);
              let lnglatAverage = _this.getCenterLngLat(item)
              centerPoint.push(lnglatAverage)
            }
          }
        });
      })
      if(centerPoint.length){
        _this.location(centerPoint)//调整地图视口
      }
    },
    //地图定位搜索结果
    location(point){
      let bdPoint = null
      let pointArr = point.map(item => {
        return new BMap.Point(item.lng, item.lat);
      })
      bdPoint = this.getCenterPoint(pointArr)
      if(bdPoint){
        this.map.setViewport(pointArr); //调整地图视口
      }
    },
    //设置围栏中心点(地图)
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    //设置围栏中心点（坐标）
    getCenterLngLat(item){
      let bdPoint = null
      let point,pointArr;
      point = JSON.parse(item.lnglat)
      let lngAverage = point.reduce((acc, val) => {
        return acc + parseFloat(val.lng);
      }, 0)/point.length;
      let latAverage = point.reduce((acc, val) => {
        return acc + parseFloat(val.lat);
      }, 0)/point.length;
      return {lng:lngAverage,lat:latAverage}
    },
    //围栏详情
    createInfo(item){
      let _dom = document.createElement("div");
      // _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;
      let _html = `
        <div>${item.name || ''}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    //创建详情框
    createInfoWin(points,_dom){
      this.closeInfoWindow()
      this.infoWin = new BMap.InfoWindow(_dom);
      this.map.openInfoWindow(this.infoWin, points);
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if(this.infoWin){
        this.infoWin.close()
        this.infoWin = null
      }
    },
    // 只显示可是区域内的标注
    pointViews(point){
        var bs = this.map.getBounds();   //获取可视区域
        var bssw = bs.getSouthWest();   //可视区域左下角
        var bsne = bs.getNorthEast();   //可视区域右上角
        if(point.lng > bssw.lng && point.lng <  bsne.lng && point.lat > bssw.lat && point.lat < bsne.lat){
          return true
        }else{
          return false
        }
    },
  }
};
</script>


<style scoped>
#locMap {
  width: 100%;
  height: 600px;
}
.tdt-touch .tdt-control-copyright{
  display: none !important;
}

.vec-total-wape{
    position: absolute;
    bottom: 20px;
    z-index: 1000;
    margin: 0 auto;
    padding: 0px;
    text-align: center;
    width: 100%;
    overflow: hidden;
    padding-bottom: 5px;
}
.vec-total-btn{
    margin: 0;
    box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
    line-height: 30px;
    padding: 2px 10px;
    font-size: 14px;
    border: 1px solid #449d44;
    cursor: pointer;
    font-weight: bold;
    color: #449d44;
    border-radius: 3px;
    background-color: #fff
}
.vec-total-btn.active{
    background-color: #449d44;
    border: 1px solid #449d44;
    color: #fff;
}

.search-wape{
  position: absolute;
  top: 35px;
  z-index: 1000;
  margin-left: 20px;
  padding: 0px;
  width: 100%;
  overflow: hidden;
  padding-bottom: 5px;
}

</style>
