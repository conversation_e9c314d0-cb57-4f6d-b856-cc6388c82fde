<template>
  <div class="app-main-content" v-show="!listLoading">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      :isexport="true"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    >
      <!-- <el-button
        slot="button"
        type="primary"
        icon="el-icon-document"
        size="small"
        @click="exportTable"
        >导出</el-button
      > -->
      <el-form-item prop="year" :label="'年份'">
        <el-date-picker
        v-model="selectedYear.year"
        type="year"
        @change="yearSelectChange"
        value-format="yyyy"
        format="yyyy"
        placeholder="请选择年份">
      </el-date-picker>
    </el-form-item>
      <!-- <el-form-item slot="year">
          <el-select size="small" v-model="selectedYear.year" :placeholder="'请选择年份'" @change="getDataList">
            <el-option v-for="(item, index) in yearOptions" :key="index" :value="item.value"
              :label="item.label"></el-option>
          </el-select>
        </el-form-item> -->
    </searchbar>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      size="mini"
      :max-height="tableHeight"
      border
      @sort-change="handleSort"
    >
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column
        prop="wtTm"
        width="150"
        label="装卸时间"
      ></el-table-column>
      <el-table-column
        prop="argmtWtCd"
        label="装卸记录编号"
        width="190"
      ></el-table-column>
      <el-table-column
        prop="regPot"
        width="200"
        label="装卸企业"
      ></el-table-column>
      <el-table-column prop="icCd" width="80" label="作业类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.icCd == '1'" type="success">装货</el-tag>
          <el-tag v-if="scope.row.icCd == '-1'" type="warning">卸货</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="goodsNm" label="货物名称"></el-table-column>
      <el-table-column prop="goodsCat" label="货物类别"></el-table-column>
      <el-table-column
        prop="loadQty"
        width="120"
        label="装卸数量(KG)"
      ></el-table-column>
      <el-table-column prop="argmtUrl" label="磅单图片">
        <template slot-scope="scope">
          <filePreview
            v-if="scope.row.argmtUrl"
            :files="scope.row.argmtUrl"
            :imageShow="true"
          ></filePreview>
        </template>
      </el-table-column>

      <el-table-column
        prop="consignorAddr"
        width="220"
        label="托运企业"
      ></el-table-column>
      <el-table-column
        prop="entpNmCn"
        width="220"
        label="运输公司"
      ></el-table-column>
<!--      <el-table-column label="区域" prop="isInChemArea">-->
<!--        <template slot-scope="scope">-->
<!--          <span v-if="scope.row.isInChemArea === 1">化工区</span>-->
<!--          <span v-if="scope.row.isInChemArea === 2">港区</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="cd" width="90" label="牵引车号"></el-table-column>
      <el-table-column
        prop="traiCd"
        width="90"
        label="挂车号"
      ></el-table-column>
      <el-table-column prop="dvNm" width="90" label="驾驶员"></el-table-column>
      <el-table-column prop="scNm" width="90" label="押运员"></el-table-column>
      <el-table-column prop="argmtCd" width="190" label="运单编号">
        <template slot-scope="scope">
          <el-button
            @click.native.prevent="showBills(scope.row.argmtPk)"
            type="text"
            >{{ scope.row.argmtCd }}
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="chkBefCds" label="装卸前检查记录号" width="160">
        <template
          slot-scope="scope"
          v-if="scope.row.chkBefCds && scope.row.chkBefCds.length"
        >
          <el-button
            v-for="(item, index) in scope.row.chkBefCds.split(',')"
            :key="index"
            type="text"
            :title="item"
            @click.native.prevent="
              recordHandle(item, scope.row.icCd == '1' ? 0 : 1)
            "
            >{{ item }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="chkAftCds" label="装卸后检查记录号" width="160">
        <template
          slot-scope="scope"
          v-if="scope.row.chkAftCds && scope.row.chkAftCds.length"
        >
          <el-button
            v-for="(item, index) in scope.row.chkAftCds.split(',')"
            :key="index"
            type="text"
            :title="item"
            @click.native.prevent="recordHandle(item, 2)"
            >{{ item }}</el-button
          >
        </template>
      </el-table-column> -->
      <!--<el-table-column prop="argmtRegiNm" label="鹤位编号"></el-table-column>-->
      <!--<el-table-column prop="measType" label="计量方式">
        <template slot-scope="scope">
          <span v-if="scope.row.measType === 0">智能地磅</span>
          <span v-else-if="scope.row.measType === 1">流量计</span>
          <span v-else-if="scope.row.measType === 2">瓶装</span>
          <span v-else-if="scope.row.measType === 3">手动过磅</span>
        </template>
      </el-table-column>-->

      <!--       <el-table-column prop="csnorNmCn" width="220" label="装货人"></el-table-column>-->

      <!-- <el-table-column prop="csneeNmCn" width="220" label="收货人"></el-table-column> -->
      <el-table-column
        fixed="right"
        align="center"
        prop="action"
        label="装卸货记录"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="success"
            size="mini"
            @click="
              handleCall(
                `showloadunloadRecordDetail`,
                scope.row.argmtPk,
                scope.row.argmtWtPk,
                scope.row.icCd
              )
            "
            ><template v-if="scope.row.icCd == '1'">装货</template
            ><template v-if="scope.row.icCd == '-1'">卸货</template></el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <!-- 运单详情 -->
    <el-dialog
      title="运单详情"
      :visible.sync="visibleOfRteplan"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <rteplan-info ref="rteplanInfo" :isCompn="true"></rteplan-info>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="recordDialogVisible"
      :title="`查看产品${recordTitle}查验表单`"
      class="mod-loadRecord-bet-check"
      :close-on-click-modal="true"
      width="80%"
    >
      <record-info ref="recordInfo"></record-info>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import recordInfo from "@/views/modules/base/wb/record-info";
import * as $http from "@/api/shipment/loadAndUnload";
import { getFuzzyTracCd } from "@/api/vec";
import * as Tool from "@/utils/tool";
import filePreview from "@/components/FilesPreview";
import RteplanInfo from "@/views/modules/base/rtePlan/rtePlan-bills";
export default {
  name: "LoadAndUnloadList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 230,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      listLoading: false,
      addLoading: false,
      defaultSearchItems: [{ field: "ic_cd", op: "ne", data: "0" }],
      selectedYear: { year: "2022" }, //默认年份
      yearOptions:[],
      searchItems: {
        normal: [
          // {
          //   name: "装卸时间",
          //   field: "wtTm",
          //   type: "year",
          //   dbfield: "wt_tm",
          //   dboper: "bt",
          //   valueFormat: "yyyy",
          //   default: this.get7Date()
          // },
          {
            name: "作业类型",
            field: "icCd",
            type: "select",
            dbfield: "ic_cd",
            dboper: "eq",
            options: [
              {
                label: "装货",
                value: "1"
              },
              {
                label: "卸货",
                value: -1
              }
            ]
          },
          {
            name: "运单编号",
            field: "argmtCd",
            type: "text",
            dbfield: "argmt_cd",
            dboper: "eq"
          },
          {
            name: "装卸企业",
            field: "regPot",
            type: "filterselect",
            dbfield: "reg_pot",
            dboper: "eq",
            options: []
          },
          {
            name: "牵引车号",
            field: "cd",
            type: "fuzzy",
            dbfield: "cd",
            dboper: "eq",
            api: this.getTracCd
          },
          /*{
            name: "挂车号",
            field: "traiCd",
            type: "fuzzy",
            dbfield: "trai_cd",
            dboper: "eq",
            api: this.getTraiCd,
          },*/
          {
            name: "运输公司",
            field: "entpNmCn",
            type: "text",
            dbfield: "entp_nm_cn",
            dboper: "cn"
          },
          {
            name: "区域",
            field: "entpArea",
            type: "select",
            options: [
              {label: "所有", value: "all"},
              {label: "化工区", value: "chemical"},
              {label: "港区", value: "harbour"},
            ],
            default: "all",
            dbfield: "entp_area",
            dboper: "nao"
          },
        ],
        more: []
      },
      pagination: {
        page: 1,
        limit: 20
      },
      visibleOfRteplan: false,
      customDrawerStyle: {
        position: "absolute",
        top: 0,
        right: 0,
        bottom: 10,
        left: 0,
        overflow: "hidden",
        margin: 0
      },
      recordDialogVisible: false,
      recordTitle: ""
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    }
  },
  components: {
    Searchbar,
    filePreview,
    RteplanInfo,
    recordInfo
  },
  created() {
    //获取企业列表
    let filters = {
      groupOp: "AND",
      rules: []
    };
    filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
    $http
      .getEntpList(params)
      .then(response => {
        if (response.code == 0) {
          this.allSearchItems.forEach(item => {
            if (item.field === "regPot") {
              item.options = response.page.list.map((item, index) => {
                return { label: item.entpName, value: item.entpName };
              });
            }
          });
        }
      })
      .catch(error => {
        console.log(error);
      });
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);
    setTimeout(function() {
      _this.setTableHeight();
    }, 1000);
    this.getList();
    // this.getYearOptions(); //获取年份列表
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    yearSelectChange(val) {
      this.$refs.searchbar.searchHandle(true, { name: "year", value: val });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 190 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList(data, sortParam) {
      let _this = this;
      this.listLoading = true;
      const loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "transparent"
      });
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        // sortParam,
        { filters: filters },
        this.pagination,
        this.selectedYear
      );
      // if (this.defaultSearchItems.length) {
      //   param.filters.rules = param.filters.rules.concat(
      //     this.defaultSearchItems
      //   );
      // }
      $http
        .LoadAndUnloadListHis(param)
        .then(response => {
          if (response.code == 0) {
            let list = response.list;
            _this.list = list;
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
          loading.close();
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
          loading.close();
        });
    },
    //获取近7天日期
    get7Date() {
      var TimeNow = new Date();
      var startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 7);
      var endDay = TimeNow;
      return [
        Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00",
        Tool.formatDate(endDay, "yyyy-MM-dd") + " 23:59:59"
      ];
    },
    handleCall(fn, ...params) {
      this[fn](params);
    },
    showloadunloadRecordDetail(data) {
      const [argmtPk, argmtWtPk, icCd] = data;
      this.$router.push({
        path: "/loadMonit/loadAndUnload/info/" + argmtWtPk,
        query: {
          icCd: icCd,
          rpk: argmtPk,
          apk: argmtWtPk,
          year:this.selectedYear.year
        }
      });
      // this.$router.push({
      //   name: "装卸记录详情",
      //   params:{
      //       argmtPk: argmtPk,
      //       argmtWtPk: argmtWtPk,
      //       icCd:icCd
      //   }
      // query:{
      //   argmtPk: argmtPk,
      //   argmtWtPk: argmtWtPk,
      // }
      // })
    },
    // 单据
    showBills: function(argmtPk) {
      // this.$router.push({
      //   path: "/transport/rteplan/bills/" + argmtPk,
      // });
      if (!argmtPk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfRteplan = true;
      this.$nextTick(() => {
        this.$refs.rteplanInfo.rtePlanNewByPk(argmtPk, this.selectedYear.year);
      });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    exportTable() {
      if (this.list.length > 0) {
        let arr = this.list.map(item => {
          return item.argmtWtPk;
        });
        let str = arr.join(",");
        $http
          .exportList(str)
          .then(res => {
            let a = document.createElement("a");
            let blob = new Blob([res]);
            let url = window.URL.createObjectURL(blob);
            var _date = new Date();

            a.href = url;
            a.download =
              arr.length > 1
                ? "装卸记录监测_" +
                  (_date.getFullYear() +
                    "-" +
                    (_date.getMonth() + 1) +
                    "-" +
                    _date.getDate()) +
                  ".zip"
                : "装卸记录监测_" +
                  (_date.getFullYear() +
                    "-" +
                    (_date.getMonth() + 1) +
                    "-" +
                    _date.getDate()) +
                  ".docx";
            a.click();
            window.URL.revokeObjectURL(url);
          })
          .catch(err => {});
      } else {
        this.$message.error("请选择数据");
      }
    },
    recordHandle(recordId, type) {
      // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      if (!recordId) {
        this.$message.warning("暂无记录");
        return;
      }
      this.recordDialogVisible = true;
      switch (type) {
        case 0:
          this.recordTitle = "装货前";
          break;
        case 1:
          this.recordTitle = "卸货前";
          break;
        case 2:
          this.recordTitle = "卸货后";
          break;
        default:
          this.recordTitle = "";
          break;
      }
      this.$nextTick(() => {
        // this.$refs.recordInfo && this.$refs.recordInfo.init(recordId, type);
        this.$refs.recordInfo && this.$refs.recordInfo.initByCd(recordId, type, this.selectedYear.year);
      });
    }
  }
};
</script>
