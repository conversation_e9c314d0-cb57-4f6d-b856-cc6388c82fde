<template>
  <div>
    <div class="clearfix" style="background-color:#fff;">
      <el-button type="warning" @click="goBack" style="float: right; margin-top: 30px; margin-right: 10px;">返回</el-button>
    </div>
    <h4 class="align-center" v-if="rteplanPk===null || argmtWtPk===null">很抱歉，您当前无法查看记录！</h4>
    <!-- 装货单 -->
    <loading-order ref="loadingOrder" v-else-if="icCd === '1'" title="危险货物充装记录表"></loading-order>
    <!-- 卸货单 -->
    <unloading-order ref="unloadingOrder" v-else-if="icCd === '-1'" title="危险货物卸货记录表"></unloading-order>
  </div>
</template>

<script>
// import loadDetail from "./load-detail";
// import unloadDetail from "./unload-detail";

import loadingOrder from "@/views/modules/base/rtePlan/components/loading-order";
import unloadingOrder from "@/views/modules/base/rtePlan/components/unloading-order";

export default {
  components: {
    loadingOrder,
    unloadingOrder
  },
  data() {
    return {
      icCd: null,
      rteplanPk: null,  // 运单pk
      argmtWtPk: null,  // 装卸单pk
      year:null
    };
  },
  created() {
    let query = this.$route.query;
    if(query){
      this.rteplanPk = query.rpk || null;
      this.argmtWtPk = query.apk || null;
      this.icCd = query.icCd || null;
      this.year = query.year || null;
    }
  },
  mounted(){
    this.$nextTick(()=>{
      this.init(this.rteplanPk, this.argmtWtPk, this.year);
    })
  },
  watch:{
    '$route.query':{
      handler(val){
        this.rteplanPk = val.rpk || null;
        this.argmtWtPk = val.apk || null;
        this.icCd = val.icCd || null;
        this.year = val.year || null;
      
        this.$nextTick(()=>{
          this.init(this.rteplanPk, this.argmtWtPk, this.year);
        })
      },
      deep:true
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    init(rteplanPk, argmtWtPk, year) {
      console.log(`%c${rteplanPk}---${argmtWtPk}`,'color:red')
      if (!(rteplanPk && argmtWtPk)) {
        return;
      }
      let icCd = this.icCd;
      if (icCd === "1") {
        // 装货单
        this.$refs.loadingOrder.init(rteplanPk, argmtWtPk, year);
      } else if (icCd === "-1") {
        // 卸货单
        this.$refs.unloadingOrder.init(rteplanPk, argmtWtPk, year);
      }
    }
  }
};
</script>
