<template>
  <div class="detail-container">
    <!-- <el-button type="primary" @click="printTable()" style="float: right;margin-top:20px;">打印</el-button> -->
    <el-button
      type="primary"
      @click="goBack"
      style="float: right; margin-top: 30px; margin-right: 10px"
      >返回</el-button
    >
    <!-- <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"/>&nbsp;返回
        </el-button>
      </el-button-group>
    </div> -->
    <div class="panel" id="printJS-form">
      <div style="margin: 20px 20px; padding: 0px 100px 40px 100px">
        <div
          style="
            color: #333333;
            font-size: 20px;
            text-align: center;
            padding: 30px 0;
            font-weight: 700;
          "
        >
          {{
            params.icCd === "1" ? "危险货物充装记录表" : "危险货物卸货记录表"
          }}
        </div>
        <div class="content">
          <table class="table" style="table-layout: fixed; width: 100%">
            <colgroup>
              <col style="width: 10%" />
              <col style="width: 15%" />
              <col style="width: 15%" />
              <col style="width: 15%" />
              <col style="width: 15%" />
              <col style="width: 15%" />
              <col style="width: 15%" />
            </colgroup>
            <thead>
              <tr>
                <th class="th-label">编号信息</th>
                <th class="th-label">
                  {{ params.icCd === "1" ? "充装记录编号" : "卸货记录编号" }}
                </th>
                <th class="th-value" colspan="2">{{ argmwtDt.argmtWtCd }}</th>
                <th class="th-label">危险货物运单编号</th>
                <th class="th-value" colspan="2">
                  <div>
                    <router-link
                      :to="'/base/rteplan/bills/' + rtePlanDt.argmtPk"
                    >
                      <span>{{ rtePlanDt.cd }}</span>
                    </router-link>
                    <!-- @click="showRteplanDialog(rtePlanDt.argmtPk)" -->
                    <!-- <a
                      href="javascript:void(0)"

                      :title="handleResult(rtePlanDt.cd)"
                      >{{ handleResult(rtePlanDt.cd) }}</a
                    > -->
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="th-label">托运信息</td>
                <td class="th-label">托运人名称</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.consignorAddr }}
                </td>
                <td class="th-label">联系电话</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.consignorTel }}
                </td>
              </tr>
              <tr v-if="params.icCd === '1'">
                <td class="th-label">收货信息</td>
                <td class="th-label">收货人名称</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csneeWhseAddr }}
                </td>
                <td class="th-label">联系电话</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csneeWhseTel }}
                </td>
              </tr>
              <tr v-if="params.icCd === '-1'">
                <td class="th-label" rowspan="3">装货信息</td>
                <td class="th-label">装货人名称</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csnorWhseAddr }}
                </td>
                <td class="th-label">联系电话</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csnorWhseTel }}
                </td>
              </tr>
              <tr v-if="params.icCd === '-1'">
                <td class="th-label">货物名称</td>
                <td class="th-value" colspan="2">{{ argmwtDt.goodsNm }}</td>
                <td class="th-label">货物UN号</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.un }}</td>
              </tr>
              <tr v-if="params.icCd === '-1'">
                <td class="th-label">货物类别</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.goodsCat }}</td>
                <td class="th-label">货物重量（KG）</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.loadQty * 1000 }}
                </td>
              </tr>
              <tr>
                <td class="th-label" rowspan="8">承运信息</td>
                <td class="th-label">运输公司名称</td>
                <td class="th-value" colspan="2">
                  <router-link :to="'/base/entp/info/' + rtePlanDt.carrierPk">
                    <span>{{ rtePlanDt.carrierNm }}</span>
                  </router-link>
                  <!-- @click="showEntpDialog(rtePlanDt.carrierPk)" -->
                  <!-- <a
                    href="javascript:void(0)"

                    :title="handleResult(rtePlanDt.carrierNm)"
                    >{{ handleResult(rtePlanDt.carrierNm) }}</a
                  > -->
                </td>
                <td class="th-label">联系电话</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.erMob }}</td>
              </tr>
              <tr>
                <td class="th-label">道路运输许可证号</td>
                <td class="th-value" colspan="5">
                  {{ rtePlanDt.tracOpraLicNo }}
                </td>
              </tr>
              <tr>
                <td class="th-label" rowspan="2">车辆信息</td>
                <td class="th-label">车牌号（颜色）</td>
                <td class="th-value">
                  <template v-if="rtePlanDt.tracPk">
                    <router-link :to="'/base/vec/info/' + rtePlanDt.tracPk">
                      <span>{{ rtePlanDt.tracCd }}</span>
                    </router-link>
                    <!-- @click="showVehicleDialog(rtePlanDt.tracPk)" -->
                    <!-- <a
                      href="javascript:void(0)"

                      :title="handleResult(rtePlanDt.tracCd)"
                      >{{ handleResult(rtePlanDt.tracCd) }}</a
                    > -->
                  </template>
                  <template v-else>
                    <span>{{ rtePlanDt.tracCd }}</span>
                  </template>
                  {{
                    rtePlanDt.plateType ? "(" + rtePlanDt.plateType + ")" : ""
                  }}
                </td>
                <td class="th-label" rowspan="2">挂车信息</td>
                <td class="th-label">车辆号码</td>
                <td class="th-value">
                  <template v-if="rtePlanDt.traiPk">
                    <router-link :to="'/base/vec/info/' + rtePlanDt.traiPk">
                      <span>{{ rtePlanDt.traiCd }}</span>
                    </router-link>
                    <!-- @click="showVehicleDialog(rtePlanDt.traiPk)" -->
                    <!-- <a
                      href="javascript:void(0)"

                      :title="handleResult(rtePlanDt.traiCd)"
                      >{{ handleResult(rtePlanDt.traiCd) }}</a
                    > -->
                  </template>
                  <template v-else>
                    <span>{{ rtePlanDt.traiCd }}</span>
                  </template>
                </td>
              </tr>
              <tr>
                <td class="th-label">道路运输证号</td>
                <td class="th-value">{{ rtePlanDt.tracOpraLicNo }}</td>
                <td class="th-label">道路运输证号</td>
                <td class="th-value">{{ rtePlanDt.traiOpraLicNo }}</td>
              </tr>
              <tr>
                <td class="th-label">罐体信息</td>
                <td class="th-label">罐体编号</td>

                <td class="th-value">
                  <div
                    v-if="rtePlanDt.cntrPk"
                    :title="rtePlanDt.tankNum"
                    class="detail-area"
                  >
                    <router-link :to="'/base/tank/info/' + rtePlanDt.cntrPk">
                      <span>{{ rtePlanDt.tankNum }}</span>
                    </router-link>
                    <!-- <a
                      href="javascript:void(0)"
                      @click="showTankDialog(rtePlanDt.cntrPk)"
                      :title="handleResult(rtePlanDt.tankNum)"
                      >{{ handleResult(rtePlanDt.tankNum) }}</a
                    > -->
                  </div>
                  <div v-else :title="rtePlanDt.tankNum" class="detail-area">
                    <span>{{ rtePlanDt.tankNum }}</span>
                  </div>
                </td>
                <td class="th-label">罐体容积（m<sup>3</sup>)</td>
                <td class="th-value">{{ rtePlanDt.tankVolume }}</td>
                <td class="th-value"></td>
              </tr>
              <tr>
                <td class="th-label" rowspan="3">驾驶员</td>
                <td class="th-label">姓名</td>
                <td class="th-value">
                  <div
                    v-if="rtePlanDt.dvPk"
                    :title="rtePlanDt.dvNm"
                    class="detail-area"
                  >
                    <router-link :to="'/base/pers/info/' + rtePlanDt.dvPk">
                      <span>{{ rtePlanDt.dvNm }}</span>
                    </router-link>
                    <!-- @click="showPersDialog(rtePlanDt.dvPk)" -->
                    <!-- <a
                      href="javascript:void(0)"

                      >{{ handleResult(rtePlanDt.dvNm) }}</a
                    > -->
                  </div>
                  <div v-else :title="rtePlanDt.dvNm" class="detail-area">
                    <span>{{ rtePlanDt.dvNm }}</span>
                  </div>
                </td>
                <td class="th-label" rowspan="3">押运员</td>
                <td class="th-label">姓名</td>
                <td class="th-value">
                  <div
                    v-if="rtePlanDt.scPk"
                    :title="rtePlanDt.scNm"
                    class="detail-area"
                  >
                    <router-link :to="'/base/pers/info/' + rtePlanDt.scPk">
                      <span>{{ rtePlanDt.scNm }}</span>
                    </router-link>
                    <!-- @click="showPersDialog(rtePlanDt.scPk)" -->
                    <!-- <a
                      href="javascript:void(0)"

                      >{{ handleResult(rtePlanDt.scNm) }}</a
                    > -->
                  </div>
                  <div v-else :title="rtePlanDt.scNm" class="detail-area">
                    <span>{{ rtePlanDt.scNm }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="th-label">从业资格证</td>
                <td class="th-value">{{ rtePlanDt.dvJobCd }}</td>
                <td class="th-label">从业资格证</td>
                <td class="th-value">{{ rtePlanDt.scJobCd }}</td>
              </tr>
              <tr>
                <td class="th-label">联系电话</td>
                <td class="th-value">{{ rtePlanDt.dvMob }}</td>
                <td class="th-label">联系电话</td>
                <td class="th-value">{{ rtePlanDt.scMob }}</td>
              </tr>
              <tr>
                <td class="th-label">查验信息</td>
                <td class="th-label">查验记录编号</td>
                <td class="th-value" colspan="2" v-if="params.icCd === '1'">
                  {{
                    `${argmwtDt.loadBefCheckCd || ""} / ${
                      argmwtDt.loadAftCheckCd || ""
                    } `
                  }}
                </td>
                <td class="th-value" colspan="2" v-else>
                  {{ argmwtDt.loadBefCheckCd || "" }}
                </td>
                <td class="th-label">查验结论</td>
                <td class="th-value" colspan="2" v-if="params.icCd === '1'">
                  {{
                    formatStatus(argmwtDt.befCheckStatus) +
                    "/" +
                    formatStatus(argmwtDt.aftCheckStatus)
                  }}
                </td>
                <td class="th-value" colspan="2" v-else>
                  {{ formatStatus(argmwtDt.befCheckStatus) }}
                </td>
              </tr>
              <tr>
                <td class="th-label" rowspan="9">
                  {{ params.icCd === "1" ? "装货信息" : "卸货信息" }}
                </td>
                <td class="th-label">装货企业</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csneeWhseAddr }}
                </td>
                <td class="th-label">作业类型</td>
                <td class="th-value" colspan="2">
                  {{ params.icCd === "1" ? "装货" : "卸货" }}
                </td>
              </tr>
              <tr>
                <td class="th-label">装货联系人</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csnorWhseCt }}
                </td>
                <td class="th-label">联系电话</td>
                <td class="th-value" colspan="2">
                  {{ rtePlanDt.csnorWhseTel }}
                </td>
              </tr>
              <tr>
                <td class="th-label">装货操作员</td>
                <td class="th-value" colspan="2">{{ argmwtDt.oprNm }}</td>
                <td class="th-label">作业时间</td>
                <td class="th-value" colspan="2">{{ argmwtDt.wtTm }}</td>
              </tr>
              <tr>
                <td class="th-label">货物名称</td>
                <td class="th-value" colspan="2">
                  <router-link
                    v-if="rtePlanDt.prodPk"
                    :to="'/base/chemica/info/' + rtePlanDt.prodPk"
                  >
                    <span>{{ rtePlanDt.goodsNm }}</span>
                  </router-link>
                  <!-- @click="showGoodsDialog(rtePlanDt.chemNewEntity||rtePlanDt.prodPk)" -->
                  <!-- <a
                    :key="rtePlanDt.prodPk"
                    href="javascript:void(0)"

                    :title="handleResult(rtePlanDt.goodsNm)"
                    >{{ handleResult(rtePlanDt.goodsNm) }}</a
                  > -->
                </td>
                <td class="th-label">UN编号</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.un }}</td>
              </tr>
              <tr>
                <td class="th-label">货物类别</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.goodsCat }}</td>
                <td class="th-label">包装类别</td>
                <td class="th-value" colspan="2">{{ rtePlanDt.packType }}</td>
              </tr>
              <tr>
                <td class="th-label">进厂称重（Kg)</td>
                <td class="th-value" colspan="2">{{ argmwtDt.weighIn }}</td>
                <td class="th-label">出厂称重（Kg)</td>
                <td class="th-value" colspan="2">{{ argmwtDt.weighOut }}</td>
              </tr>
              <tr>
                <td class="th-label">货物净重（Kg)</td>
                <td class="th-value" colspan="2">{{ argmwtDt.loadQty }}</td>
                <td class="th-label">
                  {{
                    params.icCd === "1"
                      ? "罐体(车辆）核定载质量（Kg)"
                      : "货物损耗率%"
                  }}
                </td>
                <td v-if="params.icCd === '1'" class="th-value" colspan="2">
                  {{ rtePlanDt.traiWeight || rtePlanDt.tracApprvWeight }}
                </td>
                <td v-else class="th-value" colspan="2">
                  {{
                    (
                      ((rtePlanDt.loadQty * 1000 - argmwtDt.loadQty) /
                        (rtePlanDt.loadQty * 1000)) *
                      100
                    ).toFixed(2)
                  }}
                </td>
              </tr>
              <tr>
                <td class="th-label">磅单照片</td>
                <td class="th-value" colspan="5">
                  <filePreview
                    v-if="argmwtDt.argmtUrl"
                    :files="argmwtDt.argmtUrl"
                    :imageShow="true"
                  ></filePreview>
                </td>
              </tr>
              <tr>
                <td class="th-label">卸货前检查</td>
                <td class="th-value" colspan="5">
                  <span
                    style="cursor: pointer"
                    v-if="argmwtDt.loadBefCheckId"
                    class="view-dt-btn el-icon-search"
                    @click="recordHandle(argmwtDt.loadBefCheckId, 1)"
                    >查看详情</span
                  >
                  <span v-else>无记录</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="recordDialogVisible"
      :title="`查看产品${recordTitle}查验表单`"
      class="mod-loadRecord-bet-check"
      :close-on-click-modal="true"
      width="80%"
    >
      <record-info ref="recordInfo"></record-info>
    </el-dialog>
    <!-- 企业详情 -->
    <el-dialog
      title="企业详情"
      :visible.sync="visibleOfEntp"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <entp-info ref="entpInfo" :isCompn="true"></entp-info>
    </el-dialog>
    <!-- 人员详情 -->
    <el-dialog
      title="人员详情"
      :visible.sync="visibleOfPers"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <pers-info ref="persInfo" :isCompn="true"></pers-info>
    </el-dialog>
    <!-- 车辆详情 -->
    <el-dialog
      title="车辆详情"
      :visible.sync="visibleOfVec"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <vec-info ref="vecInfo" :isCompn="true"></vec-info>
    </el-dialog>
    <!-- 罐体详情 -->
    <el-dialog
      title="罐体详情"
      :visible.sync="visibleOfTank"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <tank-info ref="tankInfo" :isCompn="true"></tank-info>
    </el-dialog>
    <!-- 运单详情 -->
    <el-dialog
      title="运单详情"
      :visible.sync="visibleOfRteplan"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <rteplan-info ref="rteplanInfo" :isCompn="true"></rteplan-info>
    </el-dialog>
    <!-- 危化品信息详情 -->
    <el-dialog
      title="危化品信息详情"
      :visible.sync="visibleOfChemica"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <chemica-info
        ref="chemicaInfo"
        :isCompn="true"
        :ipPk="prodPk"
      ></chemica-info>
    </el-dialog>
  </div>
</template>


<script>
import recordInfo from "@/views/modules/base/wb/record-info";
import * as API from "@/api/loadandunload";
import filePreview from "@/components/FilesPreview";
// import EntpInfo from "@/views/transport/entp/entp-info";
// import PersInfo from "@/views/transport/pers/pers-info";
// import VecInfo from "@/views/transport/vec/vec-info";
// import TankInfo from "@/views/transport/tank/tank-info";
// import RteplanInfo from "@/views/transport/rteplan/rteplan-bills";
// import ChemicaInfo from "@/views/shipment/goods/goods-info";
export default {
  props: {
    params: {
      type: [Object],
      default: () => {},
    },
  },
  components: {
    recordInfo,
    filePreview,
    //  EntpInfo,
    // PersInfo,
    // VecInfo,
    // ChemicaInfo,
    // TankInfo,
    // RteplanInfo,
  },
  data() {
    return {
      rtePlanDt: {},
      argmwtDt: {},

      recordDialogVisible: false,
      recordId: null,
      recordType: null,
      visibleOfEntp: false,
      visibleOfPers: false,
      visibleOfVec: false,
      visibleOfChemica: false,
      visibleOfRteplan: false,
      visibleOfTank: false,
      prodPk: null,
      recordTitle: "",
    };
  },
  watch: {
    "params.argmtWtPk": {
      immediate: true,
      handler(value) {
        if (!value) return;
        const argmtPk = this.params.argmtPk;
        const argmtWtPk = this.params.argmtWtPk;

        this.getRtePlanDt(argmtPk);
        this.getArgmtDt(argmtWtPk);
      },
    },
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    recordHandle(recordId, type) {
      // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      if (!recordId) {
        this.$message.warning("暂无记录");
        return;
      }
      this.recordDialogVisible = true;
      switch (type) {
        case 0:
          this.recordTitle = "装货前";
          break;
        case 1:
          this.recordTitle = "卸货前";
          break;
        case 2:
          this.recordTitle = "装货后";
          break;
        default:
          this.recordTitle = "";
          break;
      }
      this.$nextTick(() => {
        this.$refs.recordInfo && this.$refs.recordInfo.init(recordId, type);
      });
    },
    aftCheckHandle(argmtCd) {
      if (argmtCd) {
        this.aftCheckVisible = true;
        this.$nextTick(() => {
          // this.$refs.aftCheck.init(argmtCd, 0);
          this.$refs.aftCheck.init(argmtCd, 2);
        });
      } else {
        this.$message.warning("暂无记录");
      }
    },
    getRtePlanDt(argmtCd) {
      API.getRtePlanDtlById(argmtCd).then((res) => {
        if (res.code == 0) {
          this.rtePlanDt = res.data;
        } else {
          this.rtePlanDt = {};
        }
      });
    },
    getArgmtDt(argmtWtPk) {
      API.argmwtInfo(argmtWtPk).then((res) => {
        if (res.code == 0) {
          this.argmwtDt = res.argmwt;
        } else {
          this.argmwtDt = {};
        }
      });
    },
    handleResult(rqs) {
      if (rqs === undefined || rqs == null) {
        return "无";
      } else {
        return rqs;
      }
    },
    //查验状态判断
    formatStatus(Status) {
      if (Status === null) {
        return "";
      } else if (Status === 0) {
        return "通过";
      } else if (Status === 1) {
        return "不通过";
      } else {
        return "";
      }
    },
    // 企业信息弹窗
    showEntpDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/entp/info/'+pk,'_blank');
      this.visibleOfEntp = true;
      this.$nextTick(() => {
        this.$refs.entpInfo.initByPk(pk);
      });
    },
    // 车辆信息弹窗
    showVehicleDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfVec = true;
      this.$nextTick(() => {
        this.$refs.vecInfo.initByPk(pk);
      });
    },
    // 罐体信息弹窗
    showTankDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfTank = true;
      this.$nextTick(() => {
        this.$refs.tankInfo.initByPk(pk);
      });
    },
    // 运单信息弹窗
    showRteplanDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfRteplan = true;
      this.$nextTick(() => {
        this.$refs.rteplanInfo.rtePlanNewByPk(pk);
      });
    },

    // 人员信息弹窗
    showPersDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/pers/info/'+pk,'_blank');
      this.visibleOfPers = true;
      this.$nextTick(() => {
        this.$refs.persInfo.initByPk(pk);
      });
    },

    // 货物信息弹窗
    showGoodsDialog(prodPk) {
      if (!prodPk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.prodPk = prodPk;
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/chemica/info/'+pk,'_blank');
      this.visibleOfChemica = true;
      // this.$nextTick(() => {
      //   this.$refs.chemicaInfo.initByPk(prodPk);
      // });
    },
    //打印
    printTable() {
      printJS({
        printable: "printJS-form",
        type: "html",
        targetStyles: ["*"],
      });
    },
  },
};
</script>

<style scoped lang="scss">
.table {
  border-collapse: collapse;
  border-spacing: 0;
}

.table td {
  border-color: black;
  border-style: solid;
  border-width: 1px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  overflow: hidden;
  padding: 10px 5px;
  word-break: normal;
}

.table th {
  border-color: black;
  border-style: solid;
  border-width: 1px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  font-weight: normal;
  overflow: hidden;
  padding: 10px 5px;
  word-break: normal;
}

.table .th-value {
  background-color: #ffffff;
  border-color: #dee2e6;
  text-align: left;
  vertical-align: middle;
}

.table .th-label {
  background-color: #f5f5f5;
  border-color: #dee2e6;
  text-align: left;
  vertical-align: middle;
}
</style>
