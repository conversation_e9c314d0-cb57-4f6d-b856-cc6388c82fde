<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    ></searchbar>

    <div>

    </div>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      :max-height="tableHeight"
      border
    >
      <el-table-column type="index" label="序号" width="90"></el-table-column>
      <el-table-column prop="crtTm" label="上报时间"></el-table-column>
      <el-table-column prop="carrierNm" label="上报企业"></el-table-column>
      <el-table-column prop="tracCd" label="车牌号"></el-table-column>
      <el-table-column prop="dvNm" label="上报人"></el-table-column>
      <el-table-column prop="dvMob" label="联系电话"></el-table-column>
      <el-table-column prop="eventTypeName" label="上报事件类型"></el-table-column>
      <el-table-column prop="eventInfo" label="上报情况说明"></el-table-column>
      <el-table-column prop="eventEvidence" label="上传附件证明">
        <template slot-scope="scope">
          <template v-for="(item, index) in scope.row.eventEvidence.split(',')">
            <el-image
              v-if="isImg(item)"
              style="width: 60px; height: 60px; vertical-align: middle"
              :key="index"
              :src="item"
              :title="item"
              :preview-src-list="[item]"
            >
            </el-image>
            <span
              v-else-if="isVideo(item)"
              :key="index"
              :title="item"
              @click="showVideo(item)"
            >
              <svg-icon icon-class="play" class-name="svg-icon"></svg-icon>
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="govNotify" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.govNotify" type="info">待确认</el-tag>
          <el-tag v-else type="success">已确认</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="240px">
        <template slot-scope="scope">
          <el-button type="text" @click="showDtl(scope.row)" icon="el-icon-view">详情</el-button>
          <el-button v-if="scope.row.govNotify" type="text" @click="isReadHandle(scope.row)" icon="el-icon-check">确认查看</el-button>
          <el-button type="text" @click="feedbackHandle(scope.row)" icon="el-icon-edit">补充</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right"
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :total="pagination.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>

    <el-dialog :visible.sync="visible1" title="详情">
      <el-row>
        <el-col :span="activities && activities.length ? 14 : 24">
          <el-form label-width="140px">
            <el-form-item label="上报时间：">
              {{ dtl.crtTm }}
            </el-form-item>
            <el-form-item label="上报企业：">
              {{ dtl.carrierNm }}
            </el-form-item>
            <el-form-item label="车牌号：">
              {{ dtl.tracCd }}
            </el-form-item>
            <el-form-item label="上报人：">
              {{ dtl.dvNm }}
            </el-form-item>
            <el-form-item label="联系电话：">
              {{ dtl.dvMob }}
            </el-form-item>
            <el-form-item label="上报事件类型：">
              {{ dtl.eventTypeName }}
            </el-form-item>
            <el-form-item label="上报情况说明：">
              {{ dtl.eventInfo }}
            </el-form-item>
            <el-form-item label="状态：">
              <el-tag v-if="dtl.govNotify" type="info">待确认</el-tag>
              <el-tag v-else type="success">已确认</el-tag>
            </el-form-item>
            <el-form-item label="上传附件证明：">
              <template v-if="dtl.eventEvidence">
                <template v-for="(item, index) in dtl.eventEvidence.split(',')">
                  <el-image
                    v-if="isImg(item)"
                    style="width: 60px; height: 60px; vertical-align: middle"
                    :key="index"
                    :src="item"
                    :title="item"
                    :preview-src-list="[item]"
                  >
                  </el-image>
                  <span
                    v-else-if="isVideo(item)"
                    :key="index"
                    :title="item"
                    @click="showVideo(item)"
                  >
                    <svg-icon
                      icon-class="play"
                      class-name="svg-icon"
                    ></svg-icon>
                  </span>
                </template>
              </template>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="10" v-if="activities && activities.length">
          <div style="margin-bottom: 10px;font-size:18px;font-weight: bold;">事件上报历史记录</div>
          <div style="padding-left: 20px;max-height: 360px;overflow-y: auto;overflow-x: hidden;">
              <el-steps direction="vertical" :active="0" :space="80" >
                  <el-step v-for="(item, index) in activities" :key="index" status="process" icon="el-icon-date">
                    <template v-if="!item.govNotify && !item.replyNotify">
                      <div slot="title">{{ item.updTm}}</div>
                      <div slot="description" style="width: 500px; word-wrap: break-word">
                        <span style="font-size: 18px; color: rgb(0, 195, 253)">
                          已确认查看
                        </span>
                      </div>
                    </template>
                    <template v-else>
                      <!-- 
                        replyNotify为true,说明政府端给企业端回复了，需要展示政府端恢复的内容及updTm
                        govNotify为true,说明企业端给政府端回复了，需要展示企业回复的内容及replyTime
                        如果replyNotify和govNotify都为false时说明政府端已查看，流程结束
                       -->
                      <div slot="title">{{ item.govNotify ? item.updTm : item.replyNotify ? item.replyTime : item.updTm}}</div>
                      <div slot="description" style="width: 500px; word-wrap: break-word">
                        <div style="line-height: 32px;">
                          {{ item.govNotify ? item.carrierNm : '复核人员' }}：
                        </div>
                        <div style="font-size: 18px; color: rgb(0, 195, 253);line-height: 32px;">
                          {{ item.govNotify ? item.eventInfo : item.replyNotify ? item.replyInfo : item.replyInfo }}
                        </div>
                        <div v-if="item.govNotify">
                          <div style="line-height: 32px;">附件</div>
                          <template v-for="(item,index) in item.eventEvidence.split(',')" >
                            <el-image v-if="isImg(item)" style="width: 60px;height: 60px;vertical-align: middle;"
                              :key="index"
                              :src="item"
                              :title="item"
                              :preview-src-list="[item]">
                            </el-image>
                            <span v-else-if="isVideo(item)" :key="index" :title="item" @click="showVideo(item)">
                              <svg-icon icon-class="play" class-name="svg-icon" ></svg-icon>
                            </span>
                          </template>
                        </div>
                      </div>
                    </template>
                  </el-step>
              </el-steps>
            </div>
        </el-col>
      </el-row>
      <div slot="footer" style="text-align: center">
        <el-button v-if="dtl.govNotify" type="primary" size="small" icon="el-icon-check" @click="isReadHandle">确认查看</el-button>
        <el-button size="small" icon="el-icon-close" @click="visible1 = false">关闭</el-button>
       </div>
    </el-dialog>

    <!-- 填写补充信息 -->
    <el-dialog
      :visible.sync="visible3"
      title="反馈信息"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="feedbackForm" size="small" ref="replyForm">
        <el-form-item>
          <el-input
            type="textarea"
            rows="5"
            :rules="$rulesFilter({ required: true})"
            v-model="feedbackForm.replyInfo"
            placeholder="请输入反馈信息"
          ></el-input>
        </el-form-item>
        <el-form-item align="center">
          <el-button type="primary" size="small" @click="replyHandle">下发</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog :visible.sync="visible4" title="附件" width="660px" @closed="closedHandle">
      <video controls width="640" height="320" v-if="videoUlr">
        <source :src="videoUlr" type="video/mp4">
        <source :src="videoUlr" type="video/ogg">
      </video>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/matter.js";
import {cloneDeep} from "lodash";

export default {
  name: "",
  components: {
    Searchbar,
  },
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      visible1: false,
      visible2: false,
      visible3: false,
      visible4: false,
      videoUlr: "",
      listLoading: false,
      list: [],
      feedbackForm: {
        replyInfo: "",
      },
      activities: [],
      dtl: {},
      searchItems: {
        normal: [
          {
            name: "上报企业",
            field: "carrierNm",
            type: "text",
            dbfield: "carrier_nm",
            dboper: "cn",
          },
          {
            name: "上报人",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn",
          },
          {
            name: "车牌号",
            field: "tracCd",
            type: "text",
            dbfield: "trac_cd",
            dboper: "cn",
          },
          {
          	name: "状态", field: "govNotify", type: "radio",
          	options: [
          		{ label: "全部", value: "" },
          		{ label: "待确认", value: true },
          		{ label: "已确认", value: false }
          	],
          	dbfield: "gov_notify", 
            dboper: "eq",
            default:true
          },
          {
          	name: "上报类型", field: "eventType", type: "select",
          	options: [
          		{ label: "全部", value: "" }
          	],
          	dbfield: "event_type", 
            dboper: "eq",
            default:""
          }
        ],
        more: [{
          name: "上报时间",
          field: "crtTm",
          type: "daterange",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          dbfield: "crt_tm",
          dboper: "bt",
        }]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      }
    };
  },
  created(){
    this.getEventTypeDic()
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    const query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    //获取车辆类型分类
    this.$nextTick(() => {
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    });

    // this.getNotifyCount()
  },
  methods: {
    getNotifyCount(){
      $http.notifyCount().then( res => {

      })
    },
    // 获取上报事件类型字典
    getEventTypeDic(){
      $http.listNoPageForAll().then( res => {
        if(res && res.code == 0 && res.data)
        this.searchItems.normal[4].options = this.searchItems.normal[4].options.concat(res.data.map( item => {
          return {label:item.nmCn, value:item.cd}
        }));
      })
    },
    replyHandle(){
      this.$refs.replyForm.validate( valid => {
        if(valid){
          $http.replyReport(this.feedbackForm).then( res => {
            if(res && res.code == 0){
              this.visible3 = false;
              this.$message.success("下发成功")
              this.feedbackForm = {
                replyInfo: ""
              }
              this.getList()
            }
          })
        }
      })
    },
    feedbackHandle(row){
       this.visible3 = true;
       this.feedbackForm = cloneDeep(row);
       this.feedbackForm.replyInfo = "";
    },
    getHistroy(id){
      $http.reportHistory(id).then( res => {
         if(res && res.data){
            this.activities = res.data.sort((a,b) => {
              return new Date(b.crtTm).getTime() - new Date(a.crtTm).getTime();
            });;
         }
      })
    },
    isReadHandle(row){
      this.$confirm('是否将该数据标记为已读?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = row.id ? row : this.dtl;
        $http.readReport(params).then( res => {
          if(res && res.code == 0){
            this.$message.success("已标记为已读")
            this.getList()
          }
        })
      }).catch(() => {
                  
      });
    },
    showDtl(row) {
      this.dtl = row;
      this.visible1 = true;
      this.getHistroy(row.id)
    },
    closedHandle() {
      this.videoUlr = "";
    },
    showVideo(url) {
      this.videoUlr = "";
      this.videoUlr = url;
      this.visible4 = true;
    },
    closeDialog() {
      this.visible = false;
    },
    isImg(src) {
      return /.(jpg|jpeg|png|gif)(@0e_0o_0l_360h_360w_90q.src)?$/.test(src);
    },
    isVideo(src) {
      return /.(mp4|mpeg|wmv)?$/.test(src);
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      delete param.total;

      this.listLoading = true;
      $http
        .reportPage(param)
        .then((response) => {
          if (response.code == 0) {
            let list = response.page.list;

            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.listLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.svg-icon {
  font-size: 55px;
  cursor: pointer;
  vertical-align: middle;
}
</style>