<template>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="70%"
        top="10vh"
        @closed="closedHandle">
        <component :is="map" :compname="'addRoads'" :param="{navigationControl:true,scrollWheelZoom:true,mapHeight:'650px'}" >
            <component :is="childCompName" v-on:handledisp="handleDisp" :data-source="dataSource"></component>
            <!-- <add-roads v-on:handledisp="handleDisp"></add-roads> -->
        </component>

        <span slot="footer" class="dialog-footer" v-if="childCompName=='addRoads'">
            <el-button @click="dialogVisible = false" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmHandle"  size="small" >确 定</el-button>
        </span>
    </el-dialog>
</template>
<script>
    import BMapComp from "@/components/BMapComp"
    import addRoads from "./addRoads"
    import showRoads from "./showRoads"
    import HashMap from "@/utils/hashmap"
    export default{
        name:'addRouteDialog',
        data(){
            return {
                dialogVisible:false,
                HashMap:new HashMap(),
                roadsName:[],
                routeList:[],
                childCompName:'',
                map:'',
                dialogTitle:''
            }
        },
        props:{
            "routes":{
                type:Array,
                default:function(){
                    return  [];
                }
            },
            "childComp":{
                type:String,
                default:''
            },
            "title":{
                type:String,
                default:'选择线路'
            },
            "dataSource":{
                type:Object,
                defualt:function(){
                    return {};
                }
            }
        },
        components:{
            BMapComp,
            addRoads,//添加线路组件
            showRoads//查看线路组件
        },
        created() {
            this.showMap();
            this.childCompName = this.$props.childComp;
            this.dialogTitle = this.$props.title;
        },
        methods:{
            
            //确定按钮事件
            confirmHandle(){
                this.dialogVisible = false; 
                this.$emit('getRouteList',this.routeList,this.roadsName);
            },  
            //显示地图
            showMap(){
                this.map = 'BMapComp';
                this.dialogVisible = true;
                //清空原始线路数据
                if(this.routeList.length)
                    this.routeList = [];
                if(this.roadsName.length)
                    this.roadsName = [];
            },

            //隐藏弹窗回调
            closedHandle(){
                this.map = '';
            },

            //处理地图分发的数据
            handleDisp(payload){
                //保存添加的数据
                this.routeList = payload;
                this.roadsName = this.routeList.map( (item,index) => {
                    return item.label;
                })
            },
        }
    }
</script>
<style>

</style>
