<template>
  <div class="route-add">
    <!-- <div class="hide-tag">
          隐藏地图
        </div> -->
    <div class="card-list">
      <div class="search-bar">
        <el-input type="text" size="small" v-model="queryObj.label" @keyup.native.enter="search" placeholder="请输入关键词搜索线路"></el-input>
      </div>
      <div class="poly-list" v-loading="loading">
        <ul>
          <li v-for="item in list " :key="item.crtTm">
            <span class="road-name" :title="item.label">{{item.label}} </span>
            <div class="fl-r">
              <el-button type="primary" size="mini" @click="viewRoad(item)">查看</el-button>
              <el-button type="success" size="mini" @click="addRoad(item)">添加</el-button>
            </div>
          </li>
        </ul>
      </div>
      <div class="pagingation text-center">
        <el-button size="small" @click="lastPage" :disabled="lastPageDis">上一页</el-button>
        <el-button size="small" @click="nextPage" :disabled="nextPageDis">下一页</el-button>
      </div>
    </div>
    <div class="tag-list">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" class="roads-select">
        <el-form>
          <el-form-item>
            <el-select size="small" class="tag-select" v-model="roadsName" @remove-tag="removeTagHandle" multiple remote default-first-option placeholder="添加的通行证线路">
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
    </div>
  </div>
</template>
<script>
import HashMap from "@/utils/hashmap";
import { mapGetters } from "vuex";
import * as $http from "@/api/passport";
import * as Tool from "@/utils/tool";
export default {
  name: "addRoads",
  data() {
    return {
      loading: false,
      map: null,
      HashMap: new HashMap(),
      roadsName: [],
      list: [],
      page: 1,
      routeList: [],
      queryObj: {
        label: "",
        limit: 5,
        page: 1,
        totalPage: 2
      }
    };
  },
  destroyed() {
    this.bdary = null;
    this.map = null;
    this.HashMap = null;
  },
  created() {
    let _this = this;
    //获取地图组件
    this.map = this.$store.state.maps.map;
    //获取路线列表
    // this.list = this.getRoute();
    //如果存在已添加的路线，则使用已存在的路线
    if (this.roads.length) {
      this.roads.filter(item => {
        this.addRoad(item);
      });
    }

    //获取通行证线路
    this.getList();
  },
  computed: {
    ...mapGetters(["roads"]),
    nextPageDis() {
      return !(
        this.queryObj.totalPage > 1 &&
        this.queryObj.page < this.queryObj.totalPage
      );
    },
    lastPageDis() {
      return !(this.queryObj.page > 1 && this.queryObj.totalPage > 1);
    }
  },

  methods: {
    search() {
      if (!this.loading) {
        this.queryObj.page = 1;
        this.getList();
      }
    },
    //下一页
    nextPage() {
      if (this.queryObj.page < this.queryObj.totalPage && !this.loading) {
        this.queryObj.page += 1;
        this.getList();
      }
    },
    //上一页
    lastPage() {
      if (
        this.queryObj.page > 1 &&
        this.queryObj.totalPage > 1 &&
        !this.loading
      ) {
        this.queryObj.page -= 1;
        this.getList();
      }
    },
    //查看路线
    viewRoad(item) {
      var _this = this;
      var res = Tool.createPolylineByJSON({
        map: this.map,
        lines: item.line
      });
      this.getBoundary(function() {
        if (res && res.lines.length) {
          res.lines.forEach(it => {
            _this.map.addOverlay(it);
          });
          _this.map.setViewport(res.pointes);
        }
      });
    },
    //添加路线
    addRoad(item) {
      //添加线路
      let rteLinePk = item.rteLinePk;
      let roadName = item.label;

      if (this.HashMap.get(rteLinePk)) {
        return this.$message({
          type: "info",
          message: "该条路线已经添加，请勿重复添加"
        });
      } else {
        this.HashMap.put(rteLinePk, roadName);
        this.roadsName.push(roadName);
        this.routeList.push(item);

        this.$emit("handledisp", this.routeList);
      }
    },
    removeTagHandle(tag) {
      let key = this.HashMap.getKeyByValue(tag);
      let routeList = this.routeList;
      this.HashMap.remove(key);

      for (var i = 0, len = routeList.length; i < len; i++) {
        if (routeList[i].rteLinePk == key) {
          routeList.splice(i, 1);
          break;
        }
      }
      this.$emit("handledisp", this.routeList);
    },
    //处理地图的子组件分发的数据
    /* handleDisp(payload){
                let data = payload.data;
                let handleFn = payload.handleFn;
                let lastHandleFn = payload.lastHandleFn;
                let getBoundary = this.getBoundary;

                (Object.prototype.toString.call(handleFn) == '[object Function]') 
                && handleFn.call(this,data);
                if(lastHandleFn){
                    this.$emit('handledisp',data)
                }
            }, */
    //获取行政区域
    getBoundary(callback) {
      var bdary = new BMap.Boundary();
      var map = this.map;

      this.bdary = bdary;
      bdary.get("宁波市镇海区", function(rs) {
        //获取行政区域
        map.clearOverlays(); //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域"
          });
          return;
        }

        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 2,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
    //获取通行证列表
    getList() {
      let _this = this;
      this.getRoute(function(data) {
        _this.list = data.list;
        _this.queryObj.page = data.currPage;
        _this.queryObj.totalPage = data.totalPage;
      });
    },
    // 从数据库获取路线下拉选项
    getRoute(callback) {
      let _this = this;
      let searchLabel = this.queryObj.label;
      let page = this.queryObj.page;
      let limit = this.queryObj.limit;
      let param = {
        limit: limit,
        page: page,
        filters: {
          groupOp: "AND",
          rules: [{ field: "cat_cd", op: "cn", data: "1107.150" }]
        }
      };

      if (this.queryObj.label) {
        param.filters.rules.push({
          field: "label",
          op: "cn",
          data: searchLabel
        });
      }

      this.loading = true;
      $http
        .getPassportRteLine(param)
        .then(response => {
          if (response && response.code === 0) {
            callback(response.page);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          this.loading = false;
        })
        .catch(error => {
          console.log(error);
          this.loading = false;
        });
    }
  }
};
</script>
<style >
.route-add .card-list {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
  width: 368px;
  height: 320px;
  padding: 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}

.route-add .card-list ul {
  padding: 4px;
  margin: 10px 0px;
  height: 209px;
  overflow-y: auto;
  border-radius: 5px;
  box-shadow: 0 0 5px #dfdfdf inset;
  -webkit-box-shadow: 0 0 5px #dfdfdf inset;
}
.route-add .card-list ul li {
  padding: 6px 4px;
  overflow: hidden;
}

.route-add .tag-list {
  position: absolute;
  left: 20px;
  top: 360px;
  z-index: 1;
  width: 368px;
  height: 120px;
  padding: 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
  overflow-x: hidden;
  overflow-y: auto;
}

.route-add .fl-r {
  float: right;
}

.route-add .road-name {
  display: inline-block;
  width: 60%;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #666;
}
.route-add .pagingation {
  padding-top: 6px;
}
.route-add .hide-tag {
  position: absolute;
  left: 0;
  top: 0px;
  padding: 10px;
  background: #fff;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  font-size: 13px;
  cursor: pointer;
}
.route-add .tag-select .el-input__inner {
  border: none;
}
</style>
