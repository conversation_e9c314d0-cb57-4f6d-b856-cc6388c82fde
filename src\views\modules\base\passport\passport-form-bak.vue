<template>
<div class="mod-container" v-loading="detailLoading">
    <div class="mod-container-oper"  v-fixed>
        <el-button-group>
            <el-button type="primary" @click="submitForm" v-if="notEdit"><i class="el-icon-upload"></i>&nbsp;&nbsp;保存数据</el-button>
            <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
        </el-button-group>
    </div>
    <div class="panel">
        <div class="panel-header">
            <span class="panel-heading-inner">基本信息</span>
        </div>
        <div class="panel-body">
            <el-form :model="datas" ref="passport" label-width="120px" class="clearfix" style="padding:0 20px;">
				<el-row :gutter="20">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                        <el-form-item prop="catCd" label="通行证照类型"
                            :rules="$rulesFilter({required:true})">
							<el-select v-model="datas.catCd" placeholder="请选择通行证照类型" size="small">
                                <el-option
                                v-for="(item,index) in catCdOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                                @change="formChangeHandle">
                                </el-option>
                            </el-select>
						</el-form-item>
                        <!-- 通行证照类型中文名 -->
                         <input type="text" v-model="datas.pptTypeNmCn" hidden />
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                        <el-form-item prop="validDate" label="通行证有效期"
                            :rules="$rulesFilter({required:true})">
							<el-select v-model="datas.validDate" placeholder="请选择通行证有效期" size="small" @change="formChangeHandle">
                                <el-option v-for="(item,index) in validDateOptions"
                                    :key="index"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
						</el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12">
                        <el-form-item prop="vecNo" label="牵引车号(多个)"
                            :rules="$rulesFilter({required:true})">
                            <el-input v-if="pageType=='edit'" size="small" :disabled="true" v-model="tracCd"></el-input>
							<el-select v-else v-model="datas.vecNo" multiple filterable remote placeholder="请输入牵引车号"
                                :remote-method="querySearchTracCdAsync"
                                :loading="tracCdLoading" size="small" clearable required @change="formChangeHandle">
                                <el-option v-for="item in tracCdOptions"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.name">
                                </el-option>
                            </el-select>
						</el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" class="roads-select">
                        <el-form-item label="全路段">
                             <el-checkbox v-model="passport.allLine" @change="allLineCheck">全路段</el-checkbox>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="roads-select">
                        <el-form-item label="通行证线路">
                             <el-select
                                :disabled="passport.allLine"
                                size="small"
                                v-model="passport.roadsName"
                                @click.native="showMap"
                                @remove-tag="removeTagHandle"
                                multiple
                                remote
                                default-first-option
                                placeholder="请选择通行线路">
                            </el-select>
                        </el-form-item>
                    </el-col>
				</el-row>
            </el-form>
        </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <!-- 选择线路弹窗 -->
    <add-route-dialog v-if="dialogVisible" ref="mapDialog" @getRouteList="getRouteList" :routes="passport.routeList"
    :childComp="'addRoads'">
    </add-route-dialog>

    <div class="panel" ref="licwape">
         <div class="panel-header">
            <span class="panel-heading-inner">证照信息</span>
            <div class="panel-heading-right" v-show="$route.params.id">
                <div class="lic-status-info"><span class="circle-point gray"></span> 待审核</div>
                <div class="lic-status-info"><span class="circle-point green"></span> 审核通过</div>
                <div class="lic-status-info"><span class="circle-point yellow"></span> 将过期</div>
                <div class="lic-status-info"><span class="circle-point red"></span> 未通过</div>
                <div class="lic-status-info"><span class="circle-point deepred"></span> 已过期</div>
            </div>
        </div>

        <div class="panel-body lic-wape" style="background-color:#edf0f5">
            <certificates ref="certificates" oper-type="edit" :data-source="licData" :cert-tepl-data="certTeplData" @updateCertHandle="updateCertHandle"></certificates>
        </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

</div>
</template>
<script>
import certificates from '@/components/Certificates'
import BMapComp from "@/components/BMapComp"
import AddRouteDialog from './add-route'
import HashMap from "@/utils/hashmap"
import licConfig from '@/utils/licConfig'
import * as $http from '@/api/passport'
import { getFuzzyTracCd } from '@/api/vec'
import * as Tool from "@/utils/tool";


export default {
    name:'PassportForm',
    components:{
        certificates,
        BMapComp,
        AddRouteDialog
    },
    data (){
        return {
            HashMap:new HashMap(),
            dialogVisible:false,
            tracCd:"",
            pageType:null,     // 页面类型:add(新增)，edit(编辑)
            detailLoading:false,
            certTeplData:null,
            tracCdLoading:false,                // 牵引车列表加载
            tracCdOptions:[],                   // 牵引车列表
            catCdOptions:[
                {'label':'专线','value':'2105.180.185'},
                {'label':'临时','value':'2105.180.190'}
            ],
            passport:{
                allLine:false,
                roadsName:[],
                routeList:[],
            },
            validDateOptions:Tool.getQuartDate(),
            // allLineCheck:"非全路段",
            // routeLoading:false,
            // routeOptions:[],
            datas:{
                vecNo:[],
                validDate:'',
                catNmCn:'',
                catCd:'',
                route:'',
                rteLineIds:''
            },
            licData:[]
        }
    },
    destroyed() {
        this.HashMap = null;
    },
	created (){
        let _this = this;
        let ipPk = this.$route.params.id;

        this.certTeplData = licConfig['passport'] || {};

        if(ipPk){
            this.pageType = 'edit';
            this.detailLoading = true;
            $http.getpassportByPk(ipPk).then(response => {
              if (response.code == 0) {
                _this.licData = response.data.items;

                let vldFrom = response.data.licPpt.vldFrom,
                    vldTo = response.data.licPpt.vldTo;
                let routeLngLat,route,routeList=[];//通行证路线和坐标

                _this.datas = response.data.licPpt;
                if(vldFrom && vldTo){
                    //  _this.datas.validDate = vldFrom.slice(0,10) + ',' + vldTo.slice(0,10)
                    _this.$set(_this.datas,'validDate',vldFrom.slice(0,10) + ',' + vldTo.slice(0,10));
                }

                // _this.datas.validDate = ''
                // _this.tracCdOptions = [];
                if(_this.datas.vecNo){
                    _this.tracCd = _this.datas.vecNo;
                    /* _this.tracCdOptions = [{
                        'name':_this.datas.vecNo,
                        'value':_this.datas.vecPk
                    }]; */
                }

                //保存路线名称
                route = this.datas.route ? this.datas.route.split(',') :[];
                //保存路线对应的主键
                routeLngLat = this.datas.routeIds ? this.datas.routeIds.split(',') : [];
                if(route.length){
                    _this.$set(_this.passport,'roadsName',route);
                    if(route.length==1 && route[0] == "全路段"){
                        _this.$set(_this.passport,'allLine',true);
                    }
                }
                routeLngLat.filter( (item,index) => {
                    let label = route[index];
                    let rteLinePk = item;
                    let rtes = {"label":label,"rteLinePk":rteLinePk}
                    routeList.push(
                        rtes
                    );
                    //存入Hashmap key:value
                    if(!this.HashMap.get(rteLinePk)){
                        this.HashMap.put(rteLinePk,label);
                    }
                });

                //设置通行证名称和路线
                if(routeList.length)
                    _this.$set(_this.passport,'routeList',routeList);

                // _this.datas.vecNo = _this.datas.vecNo?_this.datas.vecNo.split(','):[]
                _this.$set(_this.datas,'vecNo',_this.datas.vecNo?_this.datas.vecNo.split(','):[]);
              } else {
                _this.$message({
                  message: response.msg,
                  type: 'error'
                });
              }
                _this.detailLoading = false;
            }).catch(error => {
                console.log(error);
                _this.detailLoading = false;
            });
        }else{
            this.pageType = 'add';
        }
    },
    computed:{
        //如果证照通过审核则不允许修改
        notEdit(){
            return !(this.datas.statCd && this.datas.statCd == '6020.160');
        }
    },
    watch:{
        'datas.catCd'(val){
            let res = [];
            res = this.catCdOptions.filter((item) => {
                if(item.value==val){
                    return true;
                }else{
                    return false;
                }
            });
            if(res.length>0){
                this.datas.catNmCn = res[0].label;
            }else{
                this.datas.catNmCn = ''
            }
        }
    },
    methods:{
        //全路段
        allLineCheck(check){
            if(check){
                if(this.passport.routeList.length){
                    this.passport.routeList = [];
                }
                this.passport.roadsName = ['全路段'];
                this.datas.rteLineIds = '';
                this.datas.route = '全路段';
            }else{
                this.passport.roadsName = [];
            }
        },
        //获取线路列表
        getRouteList(routeList,roadsName){
            let rteLineIds = [],route=[];
            this.passport.routeList = routeList;
            this.passport.roadsName = roadsName;

            this.passport.routeList.filter( item => {
                //添加线路
                let rteLinePk = item.rteLinePk;
                let roadName = item.label;

                //保存线路ID
                rteLineIds.push(item.rteLinePk);
                //保存线路名称
                route.push(item.label);

                if(!this.HashMap.get(rteLinePk)){
                    this.HashMap.put(rteLinePk,roadName);
                }
            });

            this.datas.rteLineIds = rteLineIds.join(',');
            this.datas.route = route.join(',');

        },
        //显示地图弹窗
        showMap(){
            if(this.passport.allLine){
                return false;
            }
            //向添加路线的组件（addRoads.vue）传递已添加的路线
            this.$store.commit('UPDATE_ROADS',this.passport.routeList);

            if(!this.dialogVisible)
                this.dialogVisible = true;
            if(this.$refs['mapDialog'] && this.$refs['mapDialog'].showMap)
                this.$refs['mapDialog'].showMap();
        },
        //删除标签事件
        removeTagHandle(tag){
            let key = this.HashMap.getKeyByValue(tag);
            let routeList = this.passport.routeList;
            let rteLineIds = [],route=[];
            this.HashMap.remove(key);
            for( var i=0,len=routeList.length;i<len;i++){
                if(routeList[i].rteLinePk == key){
                    routeList.splice(i,1)
                    break;
                }
            }

            //更新线路名称和ID
            this.passport.routeList.filter( item => {
                //保存线路ID
                rteLineIds.push(item.rteLinePk);
                //保存线路名称
                route.push(item.label);
            });

            this.datas.rteLineIds = rteLineIds.join(',');
            this.datas.route = route.join(',');

        },

        // 从数据库获取车号下拉选项
        getVecTracCd(vecType,queryString,callback){
            getFuzzyTracCd(vecType,queryString).then(response => {
                if (response && response.code === 0) {
                    callback(response.data);
                }else {
                    _this.$message({
                        message: response.msg,
                        type: 'error'
                    });
                }
            }).catch(error => {
                console.log(error);
            });
        },

        // 牵引车号
        querySearchTracCdAsync(queryString) {
            let _this = this;
            if(queryString){
                this.tracCdLoading = true;
                this.getVecTracCd('1180.154',queryString,function(data){
                    _this.tracCdOptions = data;
                    _this.tracCdLoading = false;
                });
            }else{
                this.tracCdOptions = [];
            }
        },

        // 从数据库获取路线下拉选项
        getRoute(queryObj,callback){
            let param= {filters: {"groupOp":"AND","rules":[{"field":"cat_cd","op":"cn","data":"1107.150"}]}}

            $http.getPassportRteLine(param).then(response => {
                if (response && response.code === 0) {
                    callback(response.page);
                }else {
                    _this.$message({
                        message: response.msg,
                        type: 'error'
                    });
                }
            }).catch(error => {
                console.log(error);
            });
        },

        // 通行证线路
        // querySearchRoute(queryString) {
        //     let _this = this;
        //     if(queryString){
        //         this.routeLoading = true;
        //         this.getRoute('1107.150',{label:encodeURIComponent(queryString)},function(data){
        //             _this.routeOptions = data;
        //             _this.routeLoading = false;
        //         });
        //     }else{
        //         this.routeOptions = [];
        //     }
        // },

        updateCertHandle(data){
            this.licData = data;
        },

        // 返回上一页
        goBack(){
            let _this = this;
            this.$confirm('您未保存信息，是否确定返回上一页?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                 this.$router.go(-1)
            }).catch(() => {

            });

        },

        // 设置修改标志
        formChangeHandle(){
            this.datas.isModify = 1;
        },

        // 提交结果
        submitForm(){
            let _this = this,
                data = Object.assign({},this.datas,true);
            // if(allLineCheck=="全路段"){
            //     data.route = "全路段"
            // }else{
            //     data.route = data.route&&data.route.length>0?data.route.join(','):data.route;
            // }
			data.vecNo = this.datas.vecNo && this.datas.vecNo.length>0?this.datas.vecNo.join(','):'';
            data.licItems = this.licData;
            data.routeIds ? delete data.routeIds : null;
            delete data.summary;
            delete data.items;

            this.detailLoading = true;
            this.$refs.passport.validate((valid) => {
                if(valid){
                    _this.$refs.certificates.validateForm().then(isValid=>{
                        if(isValid){
                            $http[this.pageType=='add'?'addPassport':'updPassport'](data).then(response => {
                                _this.detailLoading = false;
                                if (response.code==0) {
                                    _this.$message({
                                        message: (_this.pageType=='add'?'新增':'编辑')+'通行证成功',
                                        type: 'success'
                                    });
                                    _this.$router.go(-1);
                                } else {
                                    _this.$message({
                                        message: response.msg,
                                        type: 'error'
                                    });
                                }
                            }).catch(error => {
                                _this.detailLoading = false;
                                console.log(error);
                            });
                        }else{
                            _this.detailLoading = false;
                        }
                    });
                }else{
                    this.detailLoading = false;
                    this.$message({
                        message: '对不起，您的信息填写不正确',
                        type: "error"
                    });
                }

            })
        }
    }
}

</script>
<style>
 .roads-select .el-icon-arrow-up{
     display: none;
 }
</style>
