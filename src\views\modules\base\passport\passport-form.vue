<template>
  <div class="mod-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-fixed>
      <el-button-group>
        <el-button type="primary" @click="submitForm"><i
            class="el-icon-upload"></i>&nbsp;&nbsp;保存数据</el-button>
        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <el-form :model="datas" ref="passport" label-width="120px" class="clearfix" style="padding:0 20px;">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item label="通行证编号">
                <el-input v-if="pageType == 'edit'" size="small" :disabled="true" v-model="datas.cd"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item label="开始有效期">
                <el-input size="small" v-model="datas.vldFrom"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12">
              <el-form-item label="结束有效期">
                <el-input size="small" v-model="datas.vldTo"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

  </div>
</template>
<script>
import * as $http from '@/api/passport'


export default {
  name: 'PassportForm',
  components: {
  },
  data () {
    return {
      dialogVisible: false,
      datas: {}
    }
  },
  created () {
    let _this = this;
    let ipPk = this.$route.params.id;

    if (ipPk) {
      this.pageType = 'edit';
      this.detailLoading = true;
      $http.getpassportByPk(ipPk).then(response => {
        if (response.code == 0) {
          _this.datas = response.data.licPpt;
        } else {
          _this.$message({
            message: response.msg,
            type: 'error'
          });
        }
        _this.detailLoading = false;
      }).catch(error => {
        console.log(error);
        _this.detailLoading = false;
      });
    } else {
      this.pageType = 'add';
      this.$router.go(-1)
    }
  },
  methods: {

    // 返回上一页
    goBack () {
      this.$confirm('您未保存信息，是否确定返回上一页?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.go(-1)
      }).catch(() => {

      });

    },

    // 设置修改标志
    formChangeHandle () {
      this.datas.isModify = 1;
    },

    // 提交结果
    submitForm () {
      let _this = this;
      let data = Object.assign({}, this.datas, true);

      this.detailLoading = true;
      $http[this.pageType == 'add' ? 'addPassport' : 'updPassport'](data).then(response => {
        _this.detailLoading = false;
        if (response.code == 0) {
          _this.$message({
            message: (_this.pageType == 'add' ? '新增' : '编辑') + '通行证成功',
            type: 'success'
          });
          _this.$router.go(-1);
        } else {
          _this.$message({
            message: response.msg,
            type: 'error'
          });
        }
        _this.detailLoading = false;
      }).catch(error => {
        _this.detailLoading = false;
        console.log(error);
      });
    }
  }
}

</script>
<style>
.roads-select .el-icon-arrow-up {
  display: none;
}
</style>
