<template>
  <el-dialog
    title="导入通行证"
    :visible.sync="dialogVisible"
    width="35%"
    @closed="closedHandle">
    <div class="form">
      <el-input type="text" v-model="version" placeholder="请输入版本日期yyyy-MM-dd（更新时需要同一个日期）"></el-input>
      <br>
      <el-upload
        class="upload-demo"
        drag
        :action="uploadFile()"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :file-list="fileList">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传xls文件</div>
      </el-upload>
      <br>
      <el-button
        icon="el-icon-search"
        type="success"
        size="small"
        @click="inqueryStatus">查询导入状态</el-button>
      <el-button
        icon="el-icon-search"
        type="success"
        size="small"
        @click="inqueryErr">下载错误信息（按车）</el-button>
      <el-button
        icon="el-icon-search"
        type="success"
        size="small"
        @click="inqueryErr(1)">下载错误信息（按路）</el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" size="small">取 消</el-button>
      <el-button type="primary" @click="confirmHandle" size="small">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import * as $http from "@/api/passport";

export default {
  name: "",
  data () {
    return {
      dialogVisible: false,
      fileList: [],
      version: ''
    };
  },
  props: {},
  components: {},
  created () { },
  methods: {
    init () {
      this.$nextTick(() => {
        this.fileList = [];
        this.dialogVisible = true;
      });
    },
    //确定按钮事件
    confirmHandle () {
      this.dialogVisible = false;
      this.fileList = [];
    },
    //隐藏弹窗回调
    closedHandle () { },
    uploadFile () {
      return `${process.env.BASE_API}/sys/oss/uploadFile`;
    },
    // 判断是否是xls文件
    beforeUpload (file) {
      let arr = file.name.split(".");
      let nameXls = arr.find(item => item == "xls");

      const extension = nameXls === "xls";
      if (!extension) {
        this.$message({
          message: "只能上传是 xls格式文件!",
          type: "error"
        });
        return false;
      }
      return extension; // 必须要有返回值
    },
    handleSuccess (file, fileList) {
      let url = file.data[0].fileUrl;
      this.importPassport(url);
    },

    // 导入通行证
    importPassport (data) {
      $http
        .uploadFile({ url: data, version: this.version })
        .then(res => {
          if (res.code == 0) {
            this.$message({
              message: res.msg,
              type: "success",
              duration: 3000,
              onClose: () => {
                this.getList();
              }
            });
          }
        })
        .catch(err => console.log(err));
    },
    // 查询导入状态
    async inqueryStatus () {
      await $http
        .imptStatus()
        .then(res => {
          if (res.code == 0) {
            this.$message({
              message: res.msg,
              type: "info",
              showClose: true,
              duration: 0,
              top: "5vh",
              onClose: () => {
                this.getList();
              }
            });
          }
        })
        .catch(error => console.log(error));
    },
    inqueryErr (type) {
      if(1==type){
        //按路
        $http
        .imptErr()
        .then(res => {
          if (res.code == 0) {
            this.exptCsvByRoute(res.data)
          }
        })
        .catch(error => console.log(error));
      }else{
        //按车
        $http
        .imptErr()
        .then(res => {
          if (res.code == 0) {
            this.exptCsvByVec(res.data)
          }
        })
        .catch(error => console.log(error));
      }
    },

    exptCsvByVec (jsonarr) {
      let subStr = ["车辆,错误路线"];
      for (let i = 0; i < jsonarr.length; i++) {
        let temp = [];
        temp.push(jsonarr[i].vec)
        temp.push(jsonarr[i].route)
        subStr.push(temp.join(","));
      }
      //两个表数组转成字符串合并
      let merged = subStr.join("\n")

      //## 导出操作
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(merged)
      // 通过创建a标签实现
      let link = document.createElement('a')
      link.href = uri
      // 对下载的文件命名
      link.download = 'lic_vehicle_err.csv'
      document.body.appendChild(link)
      link.click()
    },

    exptCsvByRoute (jsonarr) {
      let title = "错误路线\n";
      let routeArr = [];
      for (let i = 0; i < jsonarr.length; i++) {
        routeArr.push(jsonarr[i].route)
      }
      routeArr = Array.from(new Set(routeArr)).sort((a, b) => a - b)
      //两个表数组转成字符串合并
      let merged = routeArr.join("\n")

      //## 导出操作
      // encodeURIComponent解决中文乱码
      const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent((title+merged))
      // 通过创建a标签实现
      let link = document.createElement('a')
      link.href = uri
      // 对下载的文件命名
      link.download = 'lic_route_err.csv'
      document.body.appendChild(link)
      link.click()
    }
  }
};
</script>
<style scoped>
.form {
  display: flex;
  width: 100%;
  height: 500px;
  align-items: start;
  justify-content: space-around;
  flex-direction: column;
}
</style>
