<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">通行证类型：</div>
            <div class="detail-area" :title="datas.catNmCn">{{datas.catNmCn}}</div>
          </li>
          <li style="width:22.22%;">
            <div class="detail-desc">牵引车号：</div>
            <div class="detail-area" :title="datas.vecNo">{{datas.vecNo}}</div>
          </li>
          <li>
            <div class="detail-desc">通行证有效期：</div>
            <div class="detail-area" :title="datas.vldTo">{{datas.vldTo}}</div>
          </li>
          <template v-for="(item,index) in datas.roadJson" >
            <li :key="index">
              <div class="detail-desc">通行证路线{{index+1}}：</div>
              <div class="detail-area" :title="item.route">
                <el-popover placement="top-start" title="通行证线路" width="600" trigger="hover" :content="item.route">
                  <span slot="reference">{{item.route}}</span>
                </el-popover>
              </div>
            </li>
            <li :key="index+(Math.random()*3+1)" style="width:22.22%;" >
              <div class="detail-desc">审核状态：</div>
              <div class="detail-area">
                <span v-if="item.statCd  == '6020.150'" style="color:#e6a23c;">待受理</span>
                <span v-else-if="item.statCd  == '6020.160'" style="color:#67c23a;">审核通过</span>
                <span v-else-if="item.statCd  == '6020.155'" style="color:#f56c6c;">审核未通过</span>
              </div>
            </li>
            <li :key="index+(Math.random()*3+1)" style="width:22.22%;">
              <div class="detail-desc">高峰期禁行：</div>
              <div class="detail-area">
                <span v-if="item.noThrough === 1" style="color:#67c23a;">不禁行</span>
                <span v-else style="color:#f56c6c;">禁行</span>
              </div>
            </li>
            <!-- <li :key="index+(Math.random() * 9+1)" style="width:22.22%;">
              <div class="detail-desc">是否通过：</div>
              <div class="detail-area">
                <el-checkbox v-model="isApproveList[item.name]">通过审核</el-checkbox>
              </div>
            </li> -->
            <!-- <li :key="index+(Math.random()*6+1)" v-if="item.route !== '全路段'" style="width:22.22%;">
              <div class="detail-desc">查看线路：</div>
              <div class="detail-area">
                <el-button type="text" icon="el-icon-search" size="mini" @click="showMap(item.name)">查看线路</el-button>
              </div>
            </li> -->
            <li :key="index+(Math.random()*6+1)" v-if="item.route !== '全路段'" style="width:22.22%;">
              <div class="detail-desc">全部线路：</div>
              <div class="detail-area" >
                <el-button type="text" icon="el-icon-search" size="mini" @click="showMap">查看全部线路</el-button>
              </div>
            </li>
          </template>
          <!-- <li style="width:22.22%;margin-left:55.55%">
            <div class="detail-desc">全部选择：</div>
            <div class="detail-area" >
              <el-checkbox v-model="selectAllRte" @change="allOfRteApprHandle">全选</el-checkbox>
            </div>
          </li> -->
          <!-- <li style="margin-left:77.77%;width:22.22%;" v-if="datas.route !== '全路段'">
            <div class="detail-desc">全部线路：</div>
            <div class="detail-area" >
              <el-button type="text" icon="el-icon-search" size="mini" @click="showMap">查看全部线路</el-button>
            </div>
          </li> -->
        </ul>
      </div>
       <!-- 审核操作按钮 -->
      <!-- <div class="panel-footer">
        <div class="text-right">审核状态：<span class="lic-status">
            <template v-if="datas.statCd ==='6020.160'">审核通过</template>
            <template v-else-if="datas.statCd ==='6020.155'">
              审核未通过，原因：
              <template v-if="datas.desc">{{datas.desc}}</template>
              <template v-else>无</template>
            </template>
            <template v-else>
              待受理
              <template v-if="datas.desc"><span>原因：{{datas.desc}}</span></template>
            </template>
          </span>
        </div>
        <div class="approvalOper text-right">
          <el-button-group>
            <el-button type="success" size="mini" @click="showApprRteLDialog('通过')" title="通过">
              通过
            </el-button>
            <el-button type="danger" size="mini" @click="showApprRteLDialog('驳回')" title="驳回">
              驳回
            </el-button>
            <el-button type="primary" size="mini" @click="basicApproveLog" :loading="btnLoading">
              审核日志
            </el-button>
          </el-button-group>
        </div>
      </div> -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <!-- 选择线路弹窗 -->
    <add-route-dialog v-if="dialogVisible" ref="mapDialog" :childComp="'showRoads'" :data-source="datas" :title="'查看线路'"></add-route-dialog>
    <!-- <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info"><span class="circle-point gray"></span> 待审核</div>
          <div class="lic-status-info"><span class="circle-point green"></span> 审核通过</div>
          <div class="lic-status-info"><span class="circle-point yellow"></span> 将过期</div>
          <div class="lic-status-info"><span class="circle-point red"></span> 未通过</div>
          <div class="lic-status-info"><span class="circle-point deepred"></span> 已过期</div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color:#edf0f5">
        <certificates :data-source="licData" :cert-tepl-data="certTeplData"></certificates>
      </div>
    </div> -->

    <el-dialog :visible.sync="chooseRteDialog" :title="'通行证'+apprType+'线路选择'" >
      <ul class="detail-ul">
        <template v-for="(item,index) in datas.roadJson" >
          <li :key="index" style="width:66.66%;font-size:16px;margin-bottom:10px;">
            <div class="detail-desc">通行证路线{{index+1}}：</div>
            <div class="detail-area" :title="item.route">
              <el-popover placement="top-start" title="通行证线路" width="600" trigger="hover" :content="item.route">
                <span slot="reference" :style="{'color': item.statCd == '6020.160' ? '#67c23a' : item.statCd == '6020.155' ? '#f56c6c' : '#e6a23c'}">{{item.route}}</span>
              </el-popover>
            </div>
          </li>
          <li v-if="apprType == '通过'" :key="index+(Math.random()*3+1)" style="font-size:16px;margin-bottom:10px;">
            <div class="detail-area" >
              <el-checkbox v-model="isApproveList[item.name]">{{apprType}}</el-checkbox>
            </div>
          </li>
          <li v-else-if="apprType == '驳回'" :key="index+(Math.random()*3+1)" style="font-size:16px;margin-bottom:10px;">
            <div class="detail-area" >
              <el-checkbox v-model="isRejectList[item.name]">{{apprType}}</el-checkbox>
            </div>
          </li>
        </template>
      </ul>
      <div slot="footer">
        <el-button type="primary" size="small" @click="submitApprRteHandle">提交</el-button>
        <el-button type="default" size="small" @click="closeDialogHandle">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import certificates from "@/components/Certificates";
import licConfig from "@/utils/licConfig";
import AddRouteDialog from "./add-route";
import { approveLog } from "@/api/approve";
import { getpassportByPk, licpptAudit } from "@/api/passport";
export default {
  name: "passportInfo",
  components: {
    certificates,
    AddRouteDialog,
    tagsSelect: {
      template: `<el-row >
                <el-col :sm="24" >
                    <el-input type="text" ref="input" placeholder="驳回原因"  v-model="tagVal" ></el-input>
                </el-col>
                <el-col :sm="24" style="border:1px solid #d6d6d6;border-radius:5px;padding:6px;margin-top:10px;">
                    <el-checkbox-group v-model="checkList">
                        <el-col v-for="(item,index) in rejectReasons" :key="item.index" class="checkboxe-label">
                            <el-checkbox   @change="checkedTag($event, item.reason)" :key="item.index"  :label="item.reason"></el-checkbox>
                        </el-col>
                    </el-checkbox-group>
                </el-col>
            </el-row>
            `,
      data() {
        return {
          tagVal: "",
          selected: [],
          checkList: [],
          rejectReasons: [{ reason: "请认真填写空缺部分的基本信息", index: 1 }]
        };
      },
      methods: {
        //选中驳回原因列表选项
        checkedTag(checked, tag) {
          if (checked) {
            this.selected.push(tag);
          } else {
            let index = this.selected.indexOf(tag);
            this.selected.splice(index, 1);
          }
          this.$emit("getreason", this.selected.join("；"));
        }
      }
    }
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      btnLoading: false,
      dialogVisible: false,
      detailLoading: false,
      certTeplData: null,
      chooseRteDialog:false,
      apprType:'通过',
      reason: "",
      datas: {},
      isApproveList:{},
      isRejectList:{},
      selectAllRte:false,
      licData: [],
      approveInfo: {
        entityPk: "",
        statCd: "",
        desc: "",
        entityDesc:""
      }
    };
  },
  created() {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }
    this.certTeplData = licConfig["passport"] || {};
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    }
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;

      this.detailLoading = true;
      getpassportByPk(ipPk)
        .then(response => {
          if (response.code == 0) {
            let { items } = response.data.items;
            let roadJsonParse = JSON.parse(response.data.licPpt.roadJson);

            _this.licData = response.data.items;
            _this.datas = response.data.licPpt;
            _this.$set(_this.datas,'roadJson', roadJsonParse)

            // this.selectAllRte = true;

            for(var i=0,len=roadJsonParse.length;i<len;i++){
              let isPass = roadJsonParse[i].statCd === '6020.160' ? true : false;
              _this.$set(_this.isApproveList, roadJsonParse[i].name, isPass)
              _this.$set(_this.isRejectList, roadJsonParse[i].name, !isPass)
              // if(!isPass){
              //   this.selectAllRte = false;
              // }
            }

            _this.approveInfo.entityPk = _this.datas.licPptPk;
          } else {
            _this.$set(_this.datas,'roadJson', [])
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },

    // allOfRteApprHandle(val){
    //   if(val){
    //     for(var i in this.isApproveList){
    //       this.isApproveList[i] = true;
    //     }
    //   }else{
    //     for(var i in this.isApproveList){
    //       this.isApproveList[i] = false;
    //     }
    //   }
    // },

    //获取驳回原因
    getReason(payload) {
      this.reason = payload;
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    //显示地图弹窗
    showMap(roadNm) {
      if(roadNm && typeof roadNm == 'string')
        this.$set(this.datas,'roadNm',roadNm);
      else
        this.$set(this.datas,'roadNm','');

      if (!this.dialogVisible) this.dialogVisible = true;
      if (this.$refs["mapDialog"] && this.$refs["mapDialog"].showMap)
        this.$refs["mapDialog"].showMap();
    },
    // 显示需要审核的线路
    showApprRteLDialog(apprType){
      this.apprType = apprType;
      this.chooseRteDialog = true;
    },
    closeDialogHandle(){
      this.chooseRteDialog = false;
    },
    submitApprRteHandle(){
      if(this.apprType === '通过'){
        this.approved()
      }else if(this.apprType === '驳回'){
        this.rejective()
      }

      this.chooseRteDialog = false;
    },
    approved() {
      let approveInfo = this.approveInfo;
      let _this = this;

      approveInfo.statCd = "6020.160";
      approveInfo.desc = "";
      approveInfo.entityDesc = "";

      for(var f in this.isApproveList){
        if(this.isApproveList[f]){
          approveInfo.entityDesc += f+','
        }
      }

      // 需要通过的路线
      approveInfo.entityDesc = approveInfo.entityDesc.slice(0,-1);

      this.$confirm("你确定要通过审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.basicLoading = true;
          licpptAudit([approveInfo])
            .then(response => {
              if (response.code == 0) {
                this.datas.statCd = "6020.160";
                this.$message({
                  type: "success",
                  message: "审核操作成功"
                });
              } else {
                this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
              }
              _this.initByPk(_this.$route.params.id);
              _this.basicLoading = false;
            })
            .catch(error => {
              throw new Error(error);
              _this.basicLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消审核操作"
          });
          _this.basicLoading = false;
        });
    },
    rejective() {
      let approveInfo = this.approveInfo;
      let _this = this;
      let timeStamp = new Date().getTime();
      const vElm = this.$createElement;

      this.$msgbox({
        title: "请选择驳回原因",
        message: vElm("tagsSelect", {
          on: { getreason: this.getReason },
          ref: "selectMsgbox" + timeStamp
        }),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose(action, instance, done) {
          if (action === "confirm") {
            //驳回审核操作
            let custReason =
              _this.$refs["selectMsgbox" + timeStamp] &&
              _this.$refs["selectMsgbox" + timeStamp].tagVal;

            if (!_this.reason && !custReason) {
              return _this.$message({
                type: "info",
                message: "驳回原因不能为空"
              });
            }

            if (_this.reason) {
              _this.reason += "；" + custReason;
            } else {
              _this.reason = custReason;
            }

            //清空选中的驳回理由
            _this.$refs["selectMsgbox" + timeStamp] &&
              ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
              (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
              (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
            done();
          } else if (action === "cancel") {
            done();
          }
        }
      })
        .then(action => {
          _this.basicLoading = true;
          approveInfo.desc = _this.reason;
          approveInfo.statCd = "6020.155";

          approveInfo.entityDesc = "";

          for(var f in this.isRejectList){
            if(this.isRejectList[f]){
              approveInfo.entityDesc += f+','
            }
          }

          // 需要驳回的路线
          approveInfo.entityDesc = approveInfo.entityDesc.slice(0,-1);

          licpptAudit([approveInfo])
            .then(response => {
              if (response.code == 0) {
                _this.$message({
                  type: "success",
                  message: "操作成功"
                });
                _this.datas.statCd = "6020.155";
                _this.datas.desc = _this.reason;
                _this.initByPk(_this.$route.params.id);
              } else {
                _this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
              }
              _this.basicLoading = false;
              _this.reason = "";
            })
            .catch(error => {
              _this.$message({
                type: "error",
                message: "操作失败"
              });
              _this.basicLoading = false;
              _this.reason = "";
            });
          //清空选中的驳回理由
          _this.$refs["selectMsgbox" + timeStamp] &&
            ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
            (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
            (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
        })
        .catch(error => {
          this.$message({
            type: "info",
            message: "已取消驳回操作"
          });

          //清空选中的驳回理由
          _this.$refs["selectMsgbox" + timeStamp] &&
            ((_this.$refs["selectMsgbox" + timeStamp].selected = []),
            (_this.$refs["selectMsgbox" + timeStamp].tagVal = ""),
            (_this.$refs["selectMsgbox" + timeStamp].checkList = []));
          _this.basicLoading = false;
          throw new Error(error);
        });
    },
    //审核日志
    basicApproveLog() {
      let _this = this;
      let param = { entityPk: this.datas.licPptPk, catCd: "8010.900" };
      _this.btnLoading = true;
      new Promise(function(resolve, reject) {
        approveLog(param)
          .then(response => {
            if (response.code == 0 && response.data.length) {
              resolve(response.data);
            } else {
              _this.$message("该企业信息暂无审核日志");
            }
            _this.btnLoading = false;
          })
          .catch(error => {
            _this.btnLoading = false;
            reject(error);
          });
      })
        .then(result => {
          _this.dialogVisible = true;
          _this.basicApproveLogList = result;
        })
        .catch(error => {
          throw new Error(error);
        });
    }
  }
};
</script>
