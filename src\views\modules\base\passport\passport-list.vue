<template>
  <div class="app-main-content passport">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>

    <div class="btn-group" v-permission="'licppt:update'">
      <el-button
        icon="el-icon-plus"
        slot="trigger"
        size="small"
        type="danger"
        @click="flieImport">导入通行证</el-button>
      <!-- <el-button type="success" size="small" icon="el-icon-plus" @click="add">新增</el-button>
			<el-button type="primary" size="small" :disabled="canOprate" @click="update" icon="el-icon-edit">修改</el-button>
			<el-button type="danger" size="small" icon="el-icon-delete" @click="del">删除</el-button>
			<el-button type="success" size="small" @click="approved">审核</el-button>
			<el-button type="warning" size="small" @click="rejective">驳回</el-button>
			<el-button type="primary" size="small" @click="continueOpr">续办</el-button> -->
      <router-link tag="a" :to="{ path: '/fences/polyline' }" target="_blank">
        <el-button type="primary" size="small">查看线路</el-button>
      </router-link>
      <!-- <router-link tag="a" :to="{ path: '/fences/polygon' }" target="_blank">
        <el-button type="primary" size="small">维护区域</el-button>
      </router-link> -->
    </div>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%;"
      :max-height="tableHeight"
      border
      ref="multipleTable"
      @selection-change="handleSelectionChange">
      <el-table-column prop="cd" label="编号" fixed="left"></el-table-column>
      <el-table-column prop="vecNo" label="牵引车" width="100" fixed="left">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">
            {{ scope.row.vecNo }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="entpNmCn" label="企业名称"></el-table-column>
      <el-table-column prop="route" label="路线">
        <template slot-scope="scope">
          <el-popover
            placement="top-start"
            title="通行证线路"
            width="400"
            trigger="hover">
            <div
              v-for="(item, index) in JSON.parse(scope.row.roadJson)"
              :key="index"
              style="margin-bottom:10px">
              通行证{{ item.noThrough === 1 ? "高峰" : "" }}线路{{
                index + 1
              }}：<span
                :style="{
                  'margin-bottom': '10px',
                  color:
                    item.statCd == '6020.150'
                      ? '#e6a23c'
                      : item.statCd == '6020.155'
                        ? '#f56c6c'
                        : '#67c23a'
                }">
                {{ item.route }}
              </span>
            </div>
            <span slot="reference" class="pass-route">
              <el-button type="text" @click="showRouteInfo(scope.row.licPptPk)">
                {{ scope.row.route }}
              </el-button>
            </span>
          </el-popover>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="catNmCn" label="通行证类型"></el-table-column> -->
      <el-table-column label="通行证有效期">
        <template slot-scope="scope">
          <span>{{ scope.row.vldFrom | formatDate("yyyy-MM-dd") }}至{{
            scope.row.vldTo | formatDate("yyyy-MM-dd")
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="update(scope.row)">
            修改
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="statNmCn" width="200" label="审核状态">
				<template slot-scope="scope">
					<el-tag v-if="scope.row.statCd == '6020.150'" effect="light" type="warning">待受理</el-tag>
					<el-tag v-else-if="scope.row.statCd == '6020.155'" effect="light" type="danger">审核未通过</el-tag>
					<el-tag v-else-if="scope.row.statCd == '6020.160'" effect="light" type="success">审核通过</el-tag>
				</template>
			</el-table-column> -->
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float:right;" :page-size="pagination.limit" :current-page.sync="pagination.page"
        :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <!-- 选择线路弹窗 -->
    <add-route-dialog v-if="dialogVisible" ref="mapDialog" :childComp="'showRoads'" :data-source="datas"
      :title="'查看线路'"></add-route-dialog>
    <!-- 导入通行证 弹窗 -->
    <passport-import v-if="importDialog" ref="importDialog"></passport-import>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/passport";
import * as Tool from "@/utils/tool";
import { approveLog } from "@/api/approve";
import AddRouteDialog from "./add-route";
import { getpassportByPk } from "@/api/passport";
import passportImport from "./passport-import";
export default {
  name: "PassportList",
  data () {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      oprateRow: [],
      editRow: [],
      list: [],
      datas: {},
      loading: false,
      listLoading: false,
      addLoading: false,
      dialogVisible: false,
      reason: "",
      continueDate: "",
      searchItems: {
        normal: [
          {
            name: "企业名称",
            field: "entpNmCn",
            type: "text",
            dbfield: "entp_nm_cn",
            dboper: "cn"
          },
          {
            name: "牵引车",
            field: "vecNo",
            type: "text",
            dbfield: "VEC_NO",
            dboper: "cn"
          },
          // { name: "通行证生效期", field: "vldTo", type: "selectarr",
          //     options: [
          //       { label: "全部", value: "" },
          //       ...Tool.getQuartDate()
          //     ],
          //     dbfield: "VLD_TO", dboper: "bt"
          // },
          {
            name: "通行证有效期",
            field: "vldTo",
            type: "daterange",
            dbfield: "VLD_TO",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd"
          }
          // {
          // 	name: "审核状态", field: "statCd", type: "radio",
          // 	options: [
          // 		{ label: "全部", value: "" },
          // 		{ label: "待审核", value: "6020.150" },
          // 		{ label: "审核通过", value: "6020.160" },
          // 		{ label: "审核未通过", value: "6020.155" }
          // 	],
          // 	dbfield: "STAT_CD", dboper: "eq"
          // }
        ],
        more: []
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      approveInfo: {
        entityPk: "",
        statCd: "",
        desc: ""
      },
      urlFile: "",
      importDialog: false
    };
  },
  components: {
    Searchbar,
    AddRouteDialog,
    passportImport,
    "select-comp": {
      template: `<el-select v-model="selected"  placeholder="请选择续办日期" style="width:100%;" @change="changeHandle">
                    <el-option
                        v-for="item in validDateOptions"
                        :key="item.label"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>`,
      created () { },
      data () {
        return {
          selected: "",
          validDateOptions: Tool.getQuartDate()
        };
      },
      methods: {
        //计算指定月份的天数
        getDateLen (y, m) {
          var allDay = new Date(y, m, 0).getDate();
          return allDay;
        },
        changeHandle (val) {
          //传递选择的值给父组件
          this.$emit("getcontdate", val);
        }
      }
    },
    "select-msg-box": {
      template: `<el-select v-model="selected" filterable default-first-option allow-create placeholder="请选择驳回原因" style="width:100%;" @change="changeHandle">
                    <el-option
                        v-for="item in selectOpts"
                        :key="item.index"
                        :label="item.reason"
                        :value="item.reason">
                    </el-option>
                </el-select>`,
      data () {
        return {
          selected: "",
          selectOpts: [{ reason: "请认真填写空缺部分的基本信息", index: 1 }]
        };
      },
      methods: {
        changeHandle (val) {
          //传递选择的值给父组件
          this.$emit("getreason", val);
        }
      }
    }
  },
  filters: {
    formatDate: Tool.formatDate
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  computed: {
    canOprate () {
      return this.editRow.length > 0 && this.editRow.length < 2 ? false : true;
    }
  },
  destroyed () {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //获取驳回原因
    getReason (payload) {
      this.reason = payload;
    },
    //获取续办日期
    getContinueDate (payload) {
      this.continueDate = payload;
    },
    //续办操作
    continueOpr () {
      let _this = this;
      const vElm = this.$createElement;

      this.$msgbox({
        title: "请选择续办日期",
        message: vElm("select-comp", {
          on: {
            getcontdate: _this.getContinueDate
          },
          ref: "selectMsgbox1"
        }),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            _this.loading = this.$loading({
              lock: true,
              text: "请稍等...",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)"
            });
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "执行中...";
            if (!_this.continueDate) {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = "确定";
              return _this.$message({
                type: "err",
                message: "请选择续办日期"
              });
            }
            setTimeout(() => {
              if (_this.continueDate) {
                let param = [];
                let licPptPks = _this.oprateRow.map(item => {
                  var continueOprinfo = {
                    validDate: _this.continueDate,
                    licPptPk: ""
                  };

                  continueOprinfo.licPptPk = item.licPptPk;
                  param.push(continueOprinfo);

                  return item.licPptPk;
                });

                _this.continueOprPpt(param, instance, () => {
                  instance.confirmButtonLoading = false;
                  done();
                  _this.loading.close();
                  //清空下拉框选择
                  _this.$refs.selectMsgbox1.selected = "";
                  _this.getList();
                });
              } else {
                _this.$message({
                  type: "error",
                  message: "通行证续办日期不能为空"
                });
                instance.confirmButtonText = "确定";
              }
            }, 500);
          } else {
            done();
          }
        }
      }).catch(err => {
        console.log(err);
      });
    },
    //审核操作
    approved () {
      // let approveInfo = this.approveInfo;
      let _this = this;
      let approveInfos = [];

      this.oprateRow.filter(item => {
        let approveInfo = {
          entityPk: "",
          statCd: "",
          desc: ""
        };
        approveInfo.statCd = "6020.160";
        approveInfo.desc = "";
        approveInfo.entityPk = item.licPptPk;
        approveInfos.push(approveInfo);
      });

      this.$confirm("你确定要通过审核吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;
          $http
            .licpptAudit(approveInfos)
            .then(response => {
              if (response.code == 0) {
                this.$message({
                  type: "success",
                  message: "审核操作成功"
                });
                _this.oprateRow = [];
                _this.editRow = [];
              } else {
                this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
              }
              _this.listLoading = false;
              //刷新列表
              _this.getList();
            })
            .catch(error => {
              throw new Error(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消审核操作"
          });
          _this.listLoading = false;
        });
    },
    //驳回操作
    rejective () {
      let approveInfo = this.approveInfo;
      let _this = this;
      const vElm = this.$createElement;

      this.$msgbox({
        title: "请选择驳回原因",
        message: vElm("select-msg-box", {
          on: { getreason: this.getReason },
          ref: "selectMsgbox2"
        }),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose (action, instance, done) {
          if (action === "confirm") {
            if (!_this.reason) {
              return _this.$message({
                type: "info",
                message: "驳回原因不能为空"
              });
            } else {
              done();
            }
          } else if (action === "cancel") {
            done();
          }
        }
      })
        .then(action => {
          //驳回审核操作
          let approveInfos = [];
          _this.listLoading = true;

          this.oprateRow.filter(item => {
            let approveInfo = {
              entityPk: "",
              statCd: "",
              desc: ""
            };

            approveInfo.desc = _this.reason;
            approveInfo.statCd = "6020.155";
            approveInfo.entityPk = item.licPptPk;
            approveInfos.push(approveInfo);
          });

          $http
            .licpptAudit(approveInfos)
            .then(response => {
              if (response.code == 0) {
                _this.$message({
                  type: "success",
                  message: "操作成功"
                });
              } else {
                _this.$message({
                  type: "error",
                  message: response.msg || "审核操作失败"
                });
              }
              _this.listLoading = false;
              //刷新列表
              _this.getList();
            })
            .catch(error => {
              _this.$message({
                type: "error",
                message: "操作失败"
              });
              _this.listLoading = false;
            });
          //清空选中的驳回理由
          this.$refs.selectMsgbox2.selected = "";
        })
        .catch(error => {
          this.$message({
            type: "info",
            message: "已取消驳回操作"
          });
          //清空选中的驳回理由
          this.$refs.selectMsgbox2.selected = "";
          _this.listLoading = false;
          throw new Error(error);
        });
    },
    //emit事件
    nativeChangeHandler ($evt) {
      this.reasonInp = $evt.target;
    },

    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() -
          160 -
          43 -
          this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //多选事件
    handleSelectionChange (selection) {
      if (!!selection.length) {
        if (selection.length > 1) {
          this.editRow = [];
        } else {
          this.editRow = selection;
        }
        this.oprateRow = selection;
      } else {
        this.oprateRow = [];
        this.editRow = [];
      }
    },
    // 获取数据
    getList: function (data) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign({}, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getpassportList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
      //初始化选中的数据
      if (this.oprateRow.length) {
        this.oprateRow = [];
      }
    },
    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
    //通行证续办
    continueOprPpt (param, instance, callback) {
      $http
        .licpptGoOn(param)
        .then(res => {
          if (res.code == 0) {
            callback && callback();
            this.$message({
              type: "success",
              message: res.msg || "续办成功"
            });
          } else {
            this.loading.close();
            this.$message({
              type: "error",
              message: res.msg || "续办失败"
            });
          }
        })
        .catch(err => {
          this.loading.close();
          instance.confirmButtonLoading = false;
          instance.confirmButtonText = "确定";
        });
    },
    // 删除
    del: function (row) {
      var _this = this;
      var licPptPks = _this.oprateRow.map(item => {
        return item.licPptPk;
      });

      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;

          $http
            .delpassport(licPptPks)
            .then(response => {
              _this.listLoading = false;
              if (response.code == 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success"
                });
                _this.refreshGrid();
                _this.oprateRow = [];
                _this.editRow = [];
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    // 新增
    add: function (row) {
      this.$router.push({ path: "/base/passport/add" });
    },
    // 编辑
    update: function (row) {
      // if (!this.editRow.length) return false;
      // let row = this.editRow[0];
      this.$router.push({
        path: "/base/passport/form/" + row.licPptPk,
        params: row
      });
    },
    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: "/base/passport/info/" + row.licPptPk,
        params: row
      });
    },
    flieImport () {
      this.importDialog = true;
      this.$nextTick(() => {
        this.$refs.importDialog.init();
      });
    },
    //查看通行证路线
    showRouteInfo (ipPk) {
      let _this = this;

      getpassportByPk(ipPk)
        .then(response => {
          if (response.code == 0) {
            _this.datas = response.data.licPpt;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          if (!this.dialogVisible) this.dialogVisible = true;
          if (this.$refs["mapDialog"] && this.$refs["mapDialog"].showMap)
            this.$refs["mapDialog"].showMap();
        })
        .catch(error => {
          console.log(error);
        });
    }
  }
};
</script>
<style>
.passport .el-button+.el-button {
  margin-left: 2px;
}

.passport .btn-group {
  margin-bottom: 10px;
}

.passport .pass-route {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
