<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape" v-if="pers.catCd === '2100.205.150' || pers.catCd === '2100.205.191'"
      v-loading="qrcodeLoading">
      <div class="panel-header">
        <span class="panel-heading-inner">信用信息</span>
      </div>
      <div class="panel-body" style="height: 225px">
        <el-row>
          <el-col :span="10">
            <div class="qrcode_wrap">
              <strong>浙运安全码</strong>
              <div class="qrcode">
                <div id="securityQrcode" align="center" title="浙运安全二维码" />
              </div>
            </div>
          </el-col>
          <el-col :span="14">
            <div v-if="scoreList.length > 0">
              <el-table :data="scoreList" style="width:90%" border size="mini" height="200">
                <el-table-column type="index" label="序号" width="50" align="center" header-align="center">
                </el-table-column>
                <el-table-column prop="gradeItem" label="事件描述" align="center" header-align="center"></el-table-column>
                <el-table-column prop="grade" label="分数" align="center" header-align="center"></el-table-column>
                <el-table-column prop="gradeTime" label="日期" align="center" header-align="center"></el-table-column>
                <div slot="append" class="align-right" style="padding:5px 20px;">
                  个人危运信用得分：{{ healthScore }}分
                </div>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" v-loading="basicLoading" :class="{ 'person-is-quit': pers.isDel === true }">
      <div class="panel-header text-right">
        <span class="panel-heading-inner">基本信息</span>
        <el-button class="btnValidPersInfo" type="primary" size="mini" @click="validatePersInfo">省运管信息校验</el-button>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <el-col :sm="flex">
          <ul class="detail-ul">
            <li class="col-all">
              <div class="detail-desc">企业名称：</div>
              <div class="detail-area" :title="pers.ownedCompany">
                {{ pers.ownedCompany }}
                <span style="color:#d00;" v-if="pers.ownedCompanyisApproved === false">【审核未通过】</span>
              </div>
            </li>
            <li>
              <div class="detail-desc">姓名：</div>
              <div class="detail-area" :title="pers.name">{{ pers.name }}</div>
            </li>
            <li>
              <div class="detail-desc">性别：</div>
              <div class="detail-area" :title="pers.sex">
                {{ pers.sex == "F" ? "女" : "男" }}
              </div>
            </li>
            <li>
              <div class="detail-desc">身份证号码：</div>
              <div class="detail-area" :title="pers.idCard">
                {{ pers.idCard }}
              </div>
            </li>
            <li>
              <div class="detail-desc">入职日期：</div>
              <div class="detail-area" :title="pers.hireDate">
                {{ pers.hireDate }}
              </div>
            </li>
            <li>
              <div class="detail-desc">主要岗位：</div>
              <div class="detail-area" :title="pers.catNmCn">
                {{ pers.catNmCn }}
              </div>
            </li>
            <li>
              <div class="detail-desc">联系电话：</div>
              <div class="detail-area" :title="pers.mobile">
                {{ pers.mobile }}
              </div>
            </li>
          </ul>
        </el-col>
        <el-col :sm="12" v-show="flex == 12">
          <el-card style="width: 90%">
            <div slot="header">
              <span style="font-size: 16px">校验信息</span>
            </div>
            <ul class="detail-ul">
              <li>
                <div class="detail-desc">姓名：</div>
                <div class="detail-area" :title="persValidateInfo.name">
                  {{ persValidateInfo.name }}
                </div>
              </li>
              <li>
                <div class="detail-desc">性别：</div>
                <div class="detail-area" :title="persValidateInfo.sex">
                  {{ persValidateInfo.sex == "M" ? "男" : "女" }}
                </div>
              </li>
              <li>
                <div class="detail-desc">身份证号码：</div>
                <div class="detail-area" :title="persValidateInfo.idCard">
                  {{ persValidateInfo.idCard }}
                </div>
              </li>
              <li>
                <div class="detail-desc">主要岗位：</div>
                <div class="detail-area" :title="persValidateInfo.catNmCn">
                  {{ persValidateInfo.catNmCn }}
                </div>
              </li>
              <li>
                <div class="detail-desc">联系电话：</div>
                <div class="detail-area" :title="persValidateInfo.mobile">
                  {{ persValidateInfo.mobile }}
                </div>
              </li>
            </ul>
          </el-card>
        </el-col>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer">
        <el-col :sm="12" style="color: red">
          最近一次更新时间：<span v-if="pers.updTm">{{ pers.updTm }}</span>
        </el-col>
        <el-col :sm="12">
          <div class="text-right">
            审核状态：
            <span class="lic-status">
              <template v-if="pers.basicHandleFlag == ''">未提交</template>
              <template v-else-if="pers.basicHandleFlag === '1'">审核通过</template>
              <template v-else-if="pers.basicHandleFlag === '2'">
                审核未通过，原因：
                <template v-if="pers.basicHandleRemark">{{
                  pers.basicHandleRemark
                }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="pers.basicHandleFlag === '0'">
                待受理
              </template>
            </span>
          </div>
          <!-- 审核操作按钮 -->
          <approve-bar v-permission="'appr:update'" :approve-info="basicApproveInfo" :rejectReasons="basicRejectReasons"
            @getPassStatus="getPassStatus" @getRjectStatus="getRjectStatus"
            @getHandleStat="getHandleStat"></approve-bar>
        </el-col>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray"></span> 待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green"></span> 审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow"></span> 将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red"></span> 未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred"></span> 已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates :data-source="licData" :cert-tepl-data="certTeplData" :reject-reasons="licRejectReasons">
        </certificates>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- <div class="mod-container-oper" v-if="isShowOper">
            <el-button-group>
                <el-button type="warning" @click="goBack">
                    <i class="el-icon-back"></i>&nbsp;返回</el-button>
            </el-button-group>
        </div> -->
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import approveBar from "@/components/approveBar";
import licConfig from "@/utils/licConfig";
import { formatDate } from "@/utils/tool";
import {
  getPersByPk,
  persValidInfo,
  getGradePoint,
  securityCode
} from "@/api/pers";
import QRCode from "qrcodejs2";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "PersInfo",
  components: {
    certificates,
    approveBar
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      flex: 24,
      currentDate: new Date().getTime(),
      detailLoading: false,
      basicLoading: false,
      qrcodeLoading: false,
      certTeplData: null,
      pers: {
        isDel: false // 是否已离职（默认未离职）
      },
      persValidateInfo: {},
      licData: [],
      //证照自定义驳回原因
      basicRejectReasons: [
        { reason: "人员姓名填写错误", index: 1 },
        { reason: "人员主要岗位选择错误", index: 2 },
        { reason: "人员身份证号码填写错误", index: 3 },
        {
          reason: "人员超过备案人数标准，请删除部分人员重新提交审核",
          index: 4
        }
      ],
      //证照自定义驳回原因
      licRejectReasons: [
        { reason: "身份证有效期选择错误", index: 1 },
        { reason: "劳动合同有效期选择错误", index: 2 },
        { reason: "驾驶证审验有效期选择错误", index: 3 },
        { reason: "从业资格证有效期选择错误", index: 4 },
        { reason: "安全责任状有效期选择错误", index: 5 },
        { reason: "从业资格证号填写错误", index: 6 },
        { reason: "劳动合同与安全责任状人员签字不一致", index: 7 },
        { reason: "证照上传不清晰，信息无法审核", index: 8 },
        { reason: "证照已过期", index: 9 },
        { reason: "证照上传不符合要求", index: 10 },
        { reason: "证照未上传", index: 11 },
        { reason: "未上传彩色扫描或者彩色拍照证照", index: 12 },
        { reason: "未加盖服务单位公章", index: 13 }
      ],
      basicApproveInfo: {
        entityPk: "",
        catCd: "8010.406",
        licPk: "",
        statCd: "",
        desc: ""
      },
      entityPk: "",
      creditInfoData: [],
      healthScore: "",
      scoreList: []
    };
  },
  watch: {
    "pers.entpPk"(newValue) {
      this.getEntpApproveInfoOfVec(newValue);
    }
  },
  created() {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
        this.entityPk = ipPk;
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }
    this.certTeplData = licConfig["pers"] || {};
  },
  mounted() { },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    }
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;
      this.basicApproveInfo.entityPk = ipPk;
      this.detailLoading = true;
      // 没有数据则不显示的证照
      let noDataNoLic = ["8010.403", "8010.407", "8010.408", "8010.404", "8010.409", "8010.410"];
      getPersByPk(ipPk)
        .then(response => {
          if (response && response.code == 0) {
            let licData = response.data.items;
            let arr = response.data.items.map(item => {
              return item.licCatCd;
            });
            _this.pers = response.data.pers;
            if (
              _this.pers.catCd === "2100.205.150" ||
              _this.pers.catCd === "2100.205.191"
            ) {
              _this.popoverQrcode(_this.pers.ipPk); //只有 驾驶员 和 驾押员 有安全码
            }
            /** 设置证件信息中需要显示基础信息的>>>>>>>>>>> */
            let licPropertyInVec = {
              sex: _this.pers.sex,
              idCard: _this.pers.idCard,
              mobile: _this.pers.mobile
            };
            licData = licData.map(item => {
              return Object.assign({}, item, licPropertyInVec);
            });
            /**设置证件信息中需要显示基础信息的<<<<<<<<<<<< */
            _this.licData = licData;

            _this.pers.hireDate = formatDate(_this.pers.hireDate, "yyyy-MM-dd");

            let catCd = _this.pers.catCd,
              certTeplData = Object.assign({}, licConfig["pers"]);
            if (catCd === "2100.205.150") {
              //'驾驶员';
              delete certTeplData["8010.404"];
              // 删除押运员的爆炸和剧毒从业证
              delete certTeplData["8010.409"];
              delete certTeplData["8010.410"];
              noDataNoLic.forEach(item => {
                if (!arr.includes(item)) {
                  delete certTeplData[item];
                }
              });
            } else if (catCd === "2100.205.190") {
              // '押运员';
              delete certTeplData["8010.402"];
              delete certTeplData["8010.403"];
              // 删除驾驶员的爆炸和剧毒从业证
              delete certTeplData["8010.407"];
              delete certTeplData["8010.408"];
              noDataNoLic.forEach(item => {
                if (!arr.includes(item)) {
                  delete certTeplData[item];
                }
              });
            } else if (catCd === "2100.205.191") {
              // '驾驶员/押运员';
              noDataNoLic.forEach(item => {
                if (!arr.includes(item)) {
                  delete certTeplData[item];
                }
              });
            }
            _this.certTeplData = certTeplData;
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    goBack() {
      this.$router.go(-1);
    },
    //获取通过审核操作的返回结果
    getPassStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.pers.basicHandleFlag = payload.handleFlag;
      }
    },
    //获取驳回审核操作的返回结果
    getRjectStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.pers.basicHandleFlag = payload.handleFlag;
        this.pers.basicHandleRemark = payload.remark;
      }
    },
    //点击通过或驳回审核操作的监听
    getHandleStat(type) {
      this.basicLoading = true;
    },
    //验证人员基本信息
    validatePersInfo() {
      let idCard = this.pers.idCard;
      this.basicLoading = true;
      persValidInfo(idCard)
        .then(response => {
          if (response.code == 0) {
            if (response.data.isValid == true) {
              if (this.flex != 12) {
                this.flex = 12;
              }
              this.persValidateInfo = response.data;
              this.$message({
                type: "success",
                message: "获取验证信息成功!"
              });
            } else {
              this.$message({
                type: "error",
                message: "省运管中未查询到该人员的信息!"
              });
            }
          }
          this.basicLoading = false;
        })
        .catch(error => {
          throw new Error(error);
          this.basicLoading = false;
        });
    },
    //显示安全码
    popoverQrcode(ipPk) {
      securityCode(ipPk)
        .then(res => {
          let codeColor;
          if (res.code === 0) {
            let codeState = res.data.codeState;
            this.healthScore = res.data.healthScore;
            switch (codeState) {
              case 0: //蓝码
                codeColor = "#0089e8";
                break;
              case 1: //黄码
                codeColor = "#ffc600";
                break;
              case 2: //红码
                codeColor = "#ff0000";
                break;
              case 99: //无码
                codeColor = "#cccccc";
                break;
            }
            let _this = this;
            document.getElementById("securityQrcode").innerHTML = "";
            new QRCode(document.getElementById("securityQrcode"), {
              text: _this.pers.idCard,
              width: 140,
              height: 140,
              colorDark: codeColor,
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.L
            });
          } else {
            this.healthScore = "";
            document.getElementById("securityQrcode").innerHTML = "";
            // return this.$message.error("获取安全码失败！");
          }
          //非押运员
          if (this.pers.catCd !== "2100.205.190") {
            this.GradePoint(ipPk); //安全码扣分明细
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    //安全码扣分明细
    GradePoint(ipPk) {
      this.qrcodeLoading = true;
      getGradePoint(ipPk)
        .then(res => {
          this.qrcodeLoading = false;
          if (res.code === 0) {
            this.scoreList = res.data.items;
          } else {
            this.scoreList = [];
          }
        })
        .catch(err => {
          this.qrcodeLoading = false;
          this.scoreList = [];
          console.log(err);
        });
    },
    // 获取车辆所属企业的审核状态
    async getEntpApproveInfoOfVec(entpPk) {
      if (entpPk) {
        let res = await getEntpIsApprovedByIds([entpPk]);
        if (res && res.code == 0) {
          let val = true;
          res.data.forEach(item => {
            if (item.ipPk == entpPk) {
              val = item.isApproved;
            }
          });
          this.$set(this.pers, 'ownedCompanyisApproved', val);
        }
      } else {
        this.$set(this.pers, 'ownedCompanyisApproved', false);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.qrcode_wrap {
  text-align: center;

  .qrcode {
    margin-top: 10px;
  }
}

.panel-footer {
  overflow: hidden;

  .fl-l {
    float: left;
  }

  .fl-r {
    float: right;
  }
}

/* 省运管校验信息 */
.btnValidPersInfo {
  margin-top: 10px;
}

// 人员已离职标识
.person-is-quit {
  position: relative;

  &:after {
    content: "";
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    // width:auto;
    // height:90%;
    // aspect-ratio:1/1;
    width: 200px;
    height: 200px;
    margin: 0 auto;
    background: url('~static/img/personIsQuit.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
</style>
