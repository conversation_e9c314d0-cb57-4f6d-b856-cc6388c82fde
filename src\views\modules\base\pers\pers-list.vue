<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>

    <!--列表-->
    <el-table ref="persTable" class="el-table" highlight-current-row border style="width: 100%" v-loading="listLoading"
      :max-height="tableHeight" :data="list" @sort-change="handleSort" :row-class-name="tableRowClassName"
      :cell-class-name="cellClassName">
      <el-table-column prop="name" label="姓名" fixed="left" min-width="100">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="ownedCompany" label="所属企业" min-width="280" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="{ color: scope.row.ownedCompanyisApproved === false ? '#d00' : '' }">{{
            scope.row.ownedCompany
            }}<span v-show="scope.row.ownedCompanyisApproved === false">【审核未通过】</span></span>
        </template>
      </el-table-column>
      <el-table-column prop="licApproveResult" min-width="300" label="审核状态">
        <template slot-scope="scope" v-if="scope.row.licApproveResult">
          <auditStatus licType="entp" :result="scope.row.licApproveResult" :resultCd="scope.row.licApproveResultCd"
            :aiRemark="scope.row.licApproveRemark">
          </auditStatus>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in Object.keys(scope.row.licApproveResult)">
              <p v-if="scope.row.licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">
                {{ item + "：待受理" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('审核通过')
                " :key="index" style="color: green">
                {{ item + "：审核通过" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('未通过')" :key="index" style="color: red">
                {{ item + "：未通过" }}
              </p>
            </template>
  <div slot="reference" class="name-wrapper">
    <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.401')
                " :type="getTagType(scope.row.licApproveResultCd['8010.401'])" close-transition>劳</el-tag>
    <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.405')
                " :type="getTagType(scope.row.licApproveResultCd['8010.405'])" close-transition>安</el-tag>
    <el-tag v-if="Object.keys(scope.row.licApproveResultCd).includes('8010.406')
                " :type="getTagType(scope.row.licApproveResultCd['8010.406'])" close-transition>基</el-tag>
    <template v-if="isInArr(Object.keys(scope.row.licApproveResultCd), [
                '8010.403',
                '8010.407',
                '8010.408',
              ])
                ">
                <span class="father-tag" style="white-space: nowrap">
                  <el-tag type="info" close-transition>驾</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.403'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.403'])" close-transition>危</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.407'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.407'])" close-transition>毒</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.408'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.408'])" close-transition>爆</el-tag>
                </span>
              </template>
    <template v-if="isInArr(Object.keys(scope.row.licApproveResultCd), [
                '8010.404',
                '8010.409',
                '8010.410',
              ])
                ">
                <span class="father-tag">
                  <el-tag type="info" close-transition>押</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.404'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.404'])" close-transition>危</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.409'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.409'])" close-transition>毒</el-tag>
                  <el-tag class="children-tag" v-if="Object.keys(scope.row.licApproveResultCd).includes(
                    '8010.410'
                  )
                    " :type="getTagType(scope.row.licApproveResultCd['8010.410'])" close-transition>爆</el-tag>
                </span>
              </template>
  </div>
  </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column prop="idCard" label="身份证号" min-width="180"></el-table-column>
      <el-table-column prop="catNmCn" label="主要岗位" min-width="120"></el-table-column>
      <el-table-column prop="mobile" label="手机号" min-width="120"></el-table-column>
      <el-table-column prop="crtTm" label="登记日期" min-width="150"> </el-table-column>
      <el-table-column prop="completePersRate" label="完成度" min-width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.completePersRate == undefined" style="color: red">0/
            <!-- 驾驶员 -->
            <template v-if="scope.row.catCd === '2100.205.150'">14</template>
            <!-- 押运员 -->
            <template v-else-if="scope.row.catCd === '2100.205.190'">10</template>
            <!-- 驾驶员/押运员 -->
            <template v-else>17</template>
          </span>
          <span v-else-if="scope.row.catCd === '2100.205.150' &&
            scope.row.completePersRate < 14
          " style="color: red">{{ scope.row.completePersRate }}/14</span>
          <span v-else-if="scope.row.catCd === '2100.205.190' &&
            scope.row.completePersRate < 10
          " style="color: red">{{ scope.row.completePersRate }}/10</span>
          <span v-else-if="scope.row.completePersRate < 17 &&
            scope.row.catCd !== '2100.205.150' &&
            scope.row.catCd !== '2100.205.190'
          " style="color: red">{{ scope.row.completePersRate }}/17</span>
          <span v-else>{{ scope.row.completePersRate }}/
            <!-- 驾驶员 -->
            <template v-if="scope.row.catCd === '2100.205.150'">14</template>
            <!-- 押运员 -->
            <template v-else-if="scope.row.catCd === '2100.205.190'">10</template>
            <!-- 驾驶员/押运员 -->
            <template v-else>17</template>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="isLicExpire" label="证件状态" min-width="90">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.isLicExpire == 1" :type="'info'">已到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 2" :type="'warning'">将到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 0" :type="'success'">正常</el-tag>
          <el-tag size="mini" v-else>未填报</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="安全码" min-width="70">
        <template slot-scope="scope">
          <div style="cursor: pointer; height: 25px; width: 25px" :id="'securityCode' + scope.row.ipPk"
            title="点击显示安全分值详情" @click="popoverQrcode(scope.row)" type="text"></div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="安全码">
        <template slot-scope="scope">
          <el-button @click="popoverQrcode(scope.row)" type="text" slot="reference" v-if="scope.row.catNmCn.indexOf('驾驶员')>-1">安全码</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200]"
        :total="pagination.total" style="float: right">
      </el-pagination>
    </div>
    <!-- 安全码弹窗 -->
    <el-dialog :visible.sync="securityCodeVisible" :title="'安全码 (' + currentDvname + ')'" width="600px">
      <el-card v-loading="qrcodeLoading">
        <el-row style="margin-bottom: 30px">
          <el-col :span="12">
            <div class="qrcode">
              <div id="securityQrcode" align="center" title="xxx" />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="securityScore">{{ healthScore }}分</div>
          </el-col>
        </el-row>
        <el-table border style="width: 100%" :data="scoreList" :max-height="tableHeight * 0.5">
          <el-table-column prop="gradeItem" label="事件描述"></el-table-column>
          <el-table-column prop="grade" label="分数"></el-table-column>
          <el-table-column prop="gradeTime" label="日期"></el-table-column>
        </el-table>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import Searchbar from "@/components/Searchbar";
import auditStatus from "@/components/auditStatus";
import { getPersList, firePers, getCountPersComplete } from "@/api/pers";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/pers";
import QRCode from "qrcodejs2";
import { isExistBlackList } from "@/api/common";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "PersList",
  data() {
    return {
      qrcodeLoading: false,
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "所属企业",
            field: "ownedCompany",
            type: "text",
            dbfield: "owned_company",
            dboper: "cn"
          },
          {
            name: "姓名",
            field: "name",
            type: "text",
            dbfield: "name",
            dboper: "cn"
          },
          {
            name: "身份证号",
            field: "idCard",
            type: "text",
            dbfield: "id_card",
            dboper: "cn"
          },
          {
            name: "手机号",
            field: "mobile",
            type: "text",
            dbfield: "mobile",
            dboper: "cn"
          },
          {
            name: "岗位",
            field: "catCd",
            type: "select",
            options: [
              { label: "所有岗位", value: "" },
              { label: "驾驶员", value: "2100.205.150" },
              { label: "押运员", value: "2100.205.190" },
              { label: "驾驶员/押运员", value: "2100.205.191" }
            ],
            dbfield: "cat_cd",
            dboper: "cn"
          }
        ],
        more: [
          {
            name: "区域",
            field: "entpDist",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "区内", value: "区内" },
              { label: "市内", value: "市内" },
              { label: "市外", value: "市外" }
            ],
            dbfield: "entp_dist",
            dboper: "nao"
          },
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" }
            ],
            dbfield: "is_lic_expire",
            dboper: "eq"
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              // { label: "未提交", value: "3" },
              { label: "待受理", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              { label: "审核未通过企业", value: "4" }
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao"
          }
        ]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      currentDvname: "",
      securityCodeVisible: false,
      healthScore: "",
      scoreList: []
    };
  },
  components: {
    Searchbar,
    auditStatus
  },
  computed: {},
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.approvalStatus(); //根据账号类型显示审核状态
    this.approvalDefault(); //根据账号类型切换审核状态顺序
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === "licApproveResult") {
        return "overflow-visble"
      } else {
        return ""
      }
    },
    // 根据账号类型切换审核状态顺序
    approvalDefault() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.more[2].default = "1"; //切换审核状态默认顺序
        this.searchItems.normal[4].default = "2100.205.150"; //切换岗位，默认显示驾驶员岗位
      }
    },
    //根据账号类型显示审核状态
    approvalStatus() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-ROLENAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.more[2].options.splice(4, 1); //隐藏'审核未通过企业'选项
      } else {
        //政府其他账号
        // this.searchItems.more[2].options.splice(1, 1); //隐藏'未提交'选项
      }
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      let ipPks, ipPk;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      getPersList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list.map(item => {
              const rowData = Object.assign({}, item);
              rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
              rowData.licApproveResultCd = JSON.parse(
                rowData.licApproveResultCd
              );
              return rowData;
            });
            ipPks = _this.list.map((item, index) => {
              ipPk = item.ipPk;
              return ipPk;
            });

            if (ipPks.length > 0) {
              ipPks = ipPks.join(",");
              _this.getCountPersComplete(ipPks);
              _this.getSecurityCodeByIds(ipPks);
              _this.isExistBlackList(ipPks);
            }

            _this.changeEntpInfo(); //企业是否审核通过
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    changeEntpInfo() {
      this.$nextTick(async () => {
        let data = this.list;
        let ids = data.map(item => item.entpPk);
        let uniqueIds = [...new Set(ids)];
		if (uniqueIds.length == 0) {
          return;
        }
        let res = await getEntpIsApprovedByIds(uniqueIds);
        let entpObj = {};
        if (res && res.code == 0) {
          res.data.forEach(item => {
            entpObj[item.ipPk] = item.isApproved;
          })

          data.forEach((item, index) => {
            let id = item.entpPk;
            if (id) {
              this.$set(this.list[index], 'ownedCompanyisApproved', entpObj[id])
            } else {
              this.$set(this.list[index], 'ownedCompanyisApproved', false)
            }
          })
        }
      })
    },
    // 判断人员是否在黑名单
    isExistBlackList(ipPks) {
      let _this = this;
      let list = this.list;

      isExistBlackList({ ids: ipPks, type: "人员" })
        .then((response) => {
          if (response.code == 0) {
            response.data.filter((it, index) => {
              list.filter((item, index) => {
                if (it.pk == item.ipPk) {
                  _this.$set(item, "isExistBlackList", it.type);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tableRowClassName(scope, rowIndex) {
      if (scope.row.isExistBlackList && scope.row.isExistBlackList == "1") {
        console.log("sss");
        return "warning-row";
      }
    },
    //人员证照完成度
    getCountPersComplete(ipPks) {
      let _this = this;
      let list = this.list;
      let ipPk, total, pk;
      $http
        .getCountPersComplete(ipPks)
        .then(response => {
          response.filter((item, index) => {
            ipPk = item.ipPk;
            total = item.total;

            total *= 1; //强制转换为数值类型

            list.filter((item, index) => {
              pk = item.ipPk;
              if (ipPk == pk) {
                _this.$set(item, "completePersRate", total);
              }
            });
          });
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取安全分
    getSecurityCodeByIds(ipPks) {
      let _this = this;
      let list = this.list;
      let allIpPks = ipPks.split(",");
      let ipPk, codeColor, codeState, healthScore;
      $http
        .getSecurityCodeByIds(ipPks)
        .then(res => {
          if (res.code === 0) {
            res.data.forEach((it, index) => {
              ipPk = it.ipPk;
              let selectedIndex = allIpPks.findIndex(temp => temp === ipPk);
              if (selectedIndex >= 0) {
                allIpPks.splice(selectedIndex, 1);
              } else {
                return;
              }
              let data = it.data;
              data = data ? JSON.parse(data) : "";
              codeState = data.codeState;
              healthScore = data.healthScore || "";
              switch (codeState) {
                case 0: //蓝码
                  codeColor = "#0089e8";
                  break;
                case 1: //黄码
                  codeColor = "#ffc600";
                  break;
                case 2: //红码
                  codeColor = "#ff0000";
                  break;
                case 99: //无码
                  codeColor = "#cccccc";
                  break;
              }
              let nodeId = "securityCode" + ipPk;
              document.getElementById(nodeId).innerHTML = "";
              new QRCode(document.getElementById(nodeId), {
                text: healthScore + "分",
                width: 25,
                height: 25,
                colorDark: codeColor,
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L
              });
            });

            allIpPks.forEach(item => {
              let nodeId = "securityCode" + item;
              document.getElementById(nodeId).innerHTML = "";
              document.getElementById(nodeId).securityCode = "none";
              new QRCode(document.getElementById(nodeId), {
                text: "无分值",
                width: 25,
                height: 25,
                colorDark: "#cccccc",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({ path: "/base/pers/info/" + row.ipPk, params: row });
    },
    //显示安全码
    async popoverQrcode(row) {
      let nodeId = "securityCode" + row.ipPk;
      let msg = document.getElementById(nodeId).securityCode;
      if (msg === "none") {
        return;
      }
      this.currentDvname = row.name;
      this.securityCodeVisible = true;
      this.qrcodeLoading = true;
      const res = await $http.securityCode(row.ipPk);
      let codeColor;
      if (res.code === 0) {
        let codeState = res.data.codeState;
        this.healthScore = res.data.healthScore;
        switch (codeState) {
          case 0: //蓝码
            codeColor = "#0089e8";
            break;
          case 1: //黄码
            codeColor = "#ffc600";
            break;
          case 2: //红码
            codeColor = "#ff0000";
            break;
          case 99: //无码
            codeColor = "#cccccc";
            break;
        }
        document.getElementById("securityQrcode").innerHTML = "";
        new QRCode(document.getElementById("securityQrcode"), {
          text: row.idCard,
          width: 120,
          height: 120,
          colorDark: codeColor,
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L
        });
      } else {
        this.healthScore = "";
        document.getElementById("securityQrcode").innerHTML = "";
        return this.$message.error("获取安全码失败！");
      }
      this.GradePoint(row); //安全码扣分明细
      this.qrcodeLoading = false;
    },
    //安全码扣分明细
    GradePoint(row) {
      $http
        .getGradePoint(row.ipPk)
        .then(res => {
          if (res.code === 0) {
            this.scoreList = res.data.items;
          } else {
            this.scoreList = [];
          }
        })
        .catch(err => {
          this.scoreList = [];
          console.log(err);
        });
    },
    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },
    // 判断数组中是否包含某个元素
    isInArr(fatherArr, sonArr) {
      let flag = false;
      for (let i = 0; i < sonArr.length; i++) {
        if (fatherArr.includes(sonArr[i])) {
          flag = true;
          break;
        }
      }
      return flag;
    }
  }
};
</script>

<style scoped lang="scss">
.app-main-content {
  & /deep/ .el-table {
    & tr.el-table__row.warning-row {
      background-color: #ff4d4f;
      color: #fff;

      &>td {
        background-color: #ff4d4f !important;
        color: #fff !important;

        >div.cell {
          >span {
            color: #fff !important;
          }
        }
      }

      .el-button {
        color: #0050b3 !important;
      }
    }

    & .warning-row.hover-row {
      background-color: #eb5c5e !important;
      color: #fff;

      &>td {
        background-color: #eb5c5e !important;
        color: #fff;
      }
    }

    & tr.warning-row.current-row {
      background-color: #f25557 !important;
      color: #fff;

      &>td {
        background-color: #f25557 !important;
        color: #fff;
      }
    }
  }
}

.cell .el-tag {
  margin-right: 6px;
}

.securityScore {
  font-size: 70px;
  line-height: 110px;
  text-align: center;
}

.cell .el-tag.children-tag {
  margin-right: 0px;
  margin-left: -5px;
  height: 20px;
  line-height: 10px;
  border-radius: 0;
}

.father-tag {
  white-space: nowrap;

  .el-tag:first-child {
    margin-right: 1px;
  }

  .children-tag:first-child {
    margin-right: 1px;
  }

  .children-tag:last-child {
    border-radius: 0 2px 2px 0;
    margin-right: 6px;
  }
}

.father-tag span:nth-child(2) {
  border-left: 0px;
}
</style>
