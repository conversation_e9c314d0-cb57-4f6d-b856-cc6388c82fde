

<template>
  <div class="app-main-content">
    <searchbar ref="searchbar"
               :searchItems="searchItems"
               :pagination="pagination"
               @resizeSearchbar="resizeSearchbar"
               @search="getList">
      <el-button slot="button"
                 icon="el-icon-plus"
                 type="primary"
                 size="small"
                 @click="addPersrmk">新增</el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table"
              :data="list"
              highlight-current-row
              v-loading="listLoading"
              style="width: 100%;"
              :max-height="tableHeight"
              :row-class-name="tableRowClassName">
      <el-table-column prop="nm"
                       label="姓名">
        <template slot-scope="scope">
          <div>{{scope.row.nm}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="idCd"
                       label="身份证">
        <template slot-scope="scope">
          <div>{{scope.row.idCd}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="mob"
                       label="手机号">
        <template slot-scope="scope">
          <div>{{scope.row.mob}}</div>
        </template>
      </el-table-column>
      <el-table-column label="岗位">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.catCd==='2100.205.150'">驾驶员</span>
            <span v-else-if="scope.row.catCd==='2100.205.190'">押运员</span>
            <span v-else-if="scope.row.catCd==='2100.205.191'">驾驶员/押运员</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="类型">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type==='100'">吸毒人员</span>
            <span v-else-if="scope.row.type==='200'">涉危化品前科劣迹</span>
            <span v-else-if="scope.row.type==='300'">前科劣迹</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="入镇提示">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.intoZhFlag===0">未入镇</span>
            <span v-else-if="scope.row.intoZhFlag===1">入镇</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop=""
                       label="状态">
        <template slot-scope="scope">
          <div v-if="scope.row.status===0">异常</div>
          <div v-else-if="scope.row.status===1">正常</div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text"
                     v-if="scope.row.type==='100'"
                     @click="consult(scope.row)">记录</el-button>
          <el-button type="text"
                     @click="upd(scope.row)">编辑</el-button>
          <el-button type="text"
                     @click="delet(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background
                     layout="sizes, prev, pager, next, total"
                     :page-sizes="[20, 30, 50, 100, 200]"
                     style="float:right;"
                     :page-size="pagination.limit"
                     :current-page.sync="pagination.page"
                     :total="pagination.total"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <el-dialog :visible.sync="visible">
      <el-form ref="form"
               label-width="120px"
               :model="savePersInfo"
               size="small">
        <el-form-item label="类型:"
                      prop="type"
                      :disabled="modify==-1"
                      :rules="$rulesFilter({required:true})">
          <el-select v-model="savePersInfo.type"
                     @change="typeChange"
                     filterable
                     allow-create
                     default-first-option
                     :disabled="modify==1"
                     placeholder="请选择人员类型">
            <el-option v-for="(item,index) in type"
                       :key="index"
                       :value="item.cd"
                       :label="item.nmCn" />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位:"
                      prop="catCd"
                      :rules="$rulesFilter({required:true})">
          <el-select v-model="savePersInfo.catCd"
                     filterable
                     allow-create
                     default-first-option
                     :disabled="modify==-1"
                     placeholder="请选择岗位类型">
            <el-option v-for="(item,index) in catList"
                       :key="index"
                       :value="item.value"
                       :label="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名:"
                      prop="nm"
                      :rules="$rulesFilter({required:true})">
          <el-input v-model="savePersInfo.nm"
                    type="text"></el-input>
        </el-form-item>
        <el-form-item label="身份证号:"
                      prop="idCd"
                      :rules="$rulesFilter({required:true,type:'ID'})">
          <el-input v-model="savePersInfo.idCd"
                    type="text"></el-input>
        </el-form-item>
        <el-form-item label="手机号:"
                      prop="mob"
                      :rules="$rulesFilter({required:true,type:'mobile'})">
          <el-input v-model="savePersInfo.mob"
                    :disabled="modify==-1"
                    type="text"></el-input>
        </el-form-item>
        <el-form-item label="监测时间:"
                      prop="time"
                      v-if="poison"
                      :rules="$rulesFilter({required:true})">
          <el-date-picker v-model="savePersInfo.time"
                          :disabled="modify==1"
                          type="daterange"
                          range-separator="至"
                          value-format="yyyy-MM-dd"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="频率:"
                      prop="frequency"
                      v-if="poison"
                      :rules="$rulesFilter({required:true})">
          <el-select v-model="savePersInfo.frequency"
                     filterable
                     allow-create
                     default-first-option
                     :disabled="modify==1">
            <el-option v-for="(item,index) in rate"
                       :key="index"
                       :value="item.value"
                       :label="item.label" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注:"
                      prop="rmks">
          <el-input v-model="savePersInfo.rmks"
                    type="text"
                    placeholder="请选择备注信息"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small"
                   @click="visible=false">取消</el-button>
        <el-button v-if="modify>-1"
                   type="primary"
                   size="small"
                   @click="savePersrmks">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="logVisible"
               width="45%"
               :title="recordInfo.nm+'-'+recordInfo.idCd">
      <div class="log_text">检测周期：{{recordInfo.beginTime}} 至 {{recordInfo.endTime}}</div>
      <div class="log_text">频率：按<span v-if="recordInfo.frequency==='500'">季度</span>
        <span v-else-if="recordInfo.frequency==='300'">月份</span>
        <span v-else-if="recordInfo.frequency==='100'">周</span>
      </div>
      <div class="inspection_box">
        <div class="inspection_info"
             v-for="(item ,index) in recordList"
             :key="index">
          <div class="inspection_title">{{item.title}}</div>
          <div class="inspection_time">
            <el-date-picker v-model="item.rcdTm"
                            type="date"
                            size="small"
                            placeholder="选择检查日期">
            </el-date-picker>
          </div>
          <div class="inspection_rmks">
            <el-input v-model="item.rmks"
                      :disabled="modify==-1"
                      size="small"
                      type="text"></el-input>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button size="small"
                   @click="logVisible=false">取消</el-button>
        <el-button type="primary"
                   size="small"
                   @click="saveRecord">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import * as $http from '@/api/persrmks.js';
import * as Tool from '@/utils/tool';
import Searchbar from '@/components/Searchbar';

export default {
  name: 'PersrmksList',
  data () {
    return {
      list: [],
      recordList: [],
      id: "",
      modify: 0,
      visible: false,
      poison: true,
      tableHeight: 520,
      listLoading: false,
      logVisible: false,
      rateTime: null,
      recordInfo: {},
      savePersInfo: {
        nm: "",
        idCd: "",
        beginTime: "",
        endTime: "",
        frequency: "",
        mob: '',
        record: '',
        type: '100',
        catCd: '',
        status: 1,
        rmks: '',
        time: [],
      },
      searchItems: {
        normal: [{ name: '人员姓名', field: 'nm', type: 'text', dbfield: 'nm', dboper: 'cn' },
        {
          name: "人员岗位",
          field: "catCd",
          type: "select",
          options: [
            { label: "所有岗位", value: "" },
            { label: "驾驶员", value: "2100.205.150" },
            { label: "押运员", value: "2100.205.190" },
            { label: "驾驶员/押运员", value: "2100.205.191" }
          ],
          dbfield: "cat_cd",
          dboper: "cn"
        },
        {
          name: "人员类型",
          field: "type",
          type: "select",
          options: [
            { label: "吸毒人员", value: "100" },
            { label: "涉危化品前科劣迹", value: "200" },
            { label: "前科劣迹", value: "300" },
          ],
          dbfield: "type",
          dboper: "cn"
        }
        ],

        more: []
      },
      rmks: [{
        label: "已尿检，呈阴性",
        value: "已尿检，呈阴性"
      }],
      catList: [
        { label: "驾驶员", value: "2100.205.150" },
        { label: "押运员", value: "2100.205.190" },
        { label: "驾驶员/押运员", value: "2100.205.191" }
      ],
      type: [],
      rate: [{
        label: "季度",
        value: "500"
      }, {
        label: "月",
        value: "300"
      }, {
        label: "周",
        value: "100"
      }],
      pagination: {
        total: 0,
        limit: 15,
        page: 1
      }
    };
  },
  components: {
    Searchbar
  },
  mounted () {
    const _this = this;
    window.addEventListener('resize', this.setTableHeight);
    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
    this.getFuzzyPersonalType()
  },
  destroyed () {
    window.removeEventListener('resize', this.setTableHeight);
  },
  methods: {
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      })
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.status === 1) {
        return 'warning-row';
      } else if (rowIndex === 0) {
        return 'success-row';
      }
      return '';
    },
    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },
    getFuzzyPersonalType () {
      $http.getFuzzyPersonalType().then(res => {
        if (res.code === 0) {
          this.type = res.data
        }
      })
    },
    getList (data) {
      this.listLoading = true;
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let params = Object.assign({}, { filters: filters }, this.pagination);
      delete params.total;
      $http.getPersrmksList(params)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
          }
          this.listLoading = false;
        })
        .catch(error => {
          this.listLoading = false;
        });
    },
    addPersrmk () {
      this.savePersInfo = {
        nm: "",
        idCd: "",
        beginTime: "",
        endTime: "",
        frequency: "",
        catCd: '',
        mob: '',
        record: '',
        type: '100',
        status: 1,
        time: [],
        rmks: '',
      },
        this.typeChange(this.savePersInfo.type)
      this.visible = true;
      this.modify = 0;
      this.$nextTick(() => {
        this.closeCb()
      })
    },
    savePersrmks () {
      if (this.savePersInfo.frequency === '500') {
        this.rateTime = (3 * 30 * 24 * 60 * 60 * 1000)
      } else if (this.savePersInfo.frequency === '300') {
        this.rateTime = (30 * 24 * 60 * 60 * 1000)
      } else if (this.savePersInfo.frequency === '100') {
        this.rateTime = (7 * 24 * 60 * 60 * 1000)
      }


      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.savePersInfo.id) {
            let params = Object.assign({}, this.savePersInfo, { id: this.savePersInfo.id })
            this.$confirm('确认提交修改吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              $http.updPersrmks(params).then(res => {
                if (res.code == 0) {
                  this.$message.success("修改成功!")
                  this.visible = false;
                  this.id = "";
                  this.getList();
                } else {
                  this.$message.error(res.msg || "修改失败！")
                }
              })
            }).catch(err => {

            })
          } else {
            let rateNum = Math.ceil((Date.parse(new Date(this.savePersInfo.time[1])) - Date.parse(new Date(this.savePersInfo.time[0]))) / this.rateTime),
              rateList = [],
              year = new Date(this.savePersInfo.time[0]).getFullYear() * 1,
              currMonth = new Date(new Date(this.savePersInfo.time[0])).getMonth() + 1,
              currQuarter = Math.floor((currMonth % 3 == 0 ? (currMonth / 3) : (currMonth / 3 + 1))),
              date1 = Date.parse(new Date(this.savePersInfo.time[0])),
              date2 = new Date(year, 0, 1),
              d = Math.round((date1.valueOf() - date2.valueOf()) / 86400000),
              week = Math.ceil((d + (date2.getDay() + 1 - 1)) / 7);
            for (let i = 1; i <= rateNum; i++) {
              let obj = {
                title: '',
                rcdTm: '',
                rmks: '',
              }
              if (this.savePersInfo.frequency === '500') {
                if (currQuarter > 4) {
                  currQuarter = 1
                  year = year + 1
                  obj.title = year + '-' + currQuarter + '季度'
                  currQuarter++
                } else {
                  obj.title = year + '-' + currQuarter + '季度'
                  currQuarter++
                }
              } else if (this.savePersInfo.frequency === '300') {
                if (currMonth > 12) {
                  currMonth = 1
                  year = year + 1
                  obj.title = year + '-' + currMonth + '月'
                  currMonth++
                } else {
                  obj.title = year + '-' + currMonth + '月'
                  currMonth++
                }
              } else if (this.savePersInfo.frequency === '100') {
                if (week > 52) {
                  week = 1
                  year = year + 1
                  obj.title = year + '-' + week + '周'
                  week++
                } else {
                  obj.title = year + '-' + week + '周'
                  week++
                }
              }
              rateList.push(obj)
            }

            this.savePersInfo.record = JSON.stringify(rateList)
            this.savePersInfo.beginTime = this.savePersInfo.time[0]
            this.savePersInfo.endTime = this.savePersInfo.time[1]
            $http.savePersrmks(this.savePersInfo).then(res => {
              if (res.code == 0) {
                this.$message.success("新增成功!")
              }
              this.visible = false;
              this.id = "";
              this.getList();
            })
          }
        }
      })
    },
    delet (row) {
      let id = row.id;
      this.$confirm('确认删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $http.deletePersrmks(id).then(res => {
          if (res.code == 0) {
            this.$message.success("删除成功!");
            this.getList();
          }
        })
      }).catch(err => {

      })
    },
    upd (row) {
      this.modify = 1;
      $http.getPersrmksInfo(row.id).then(res => {
        if (res.code === 0) {
          this.savePersInfo = res.data;
          this.savePersInfo.time = [res.data.beginTime, res.data.endTime]
          this.typeChange(this.savePersInfo.type)
        }
      })
      this.visible = true;
    },
    saveRecord () {
      this.$confirm('确认提交修改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.recordInfo.record = JSON.stringify(this.recordList)
        $http.updPersrmks(this.recordInfo).then(res => {
          if (res.code == 0) {
            this.$message.success("修改成功!")
            this.logVisible = false;
            this.id = "";
            this.getList();
          } else {
            this.$message.error(res.msg || "修改失败！")
          }
        })
      }).catch(err => {

      })
    },
    consult (row) {
      $http.getPersrmksInfo(row.id).then(res => {
        if (res.code === 0) {
          this.recordInfo = {}
          this.recordInfo = res.data;
          this.recordList = JSON.parse(res.data.record)
        }
      })
      this.logVisible = true
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;

      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;

      this.$refs.searchbar.searchHandle(true);
    },
    closeCb () {
      if (this.$refs["form"] !== undefined) {
        this.$refs["form"].resetFields();
      }
    },
    typeChange (item) {
      if (item === '100') {
        this.poison = true
      } else {
        this.poison = false
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.log_text {
  font-size: 16px;
  margin: 10px 0 0 10px;
}
.inspection_box {
  margin: 10px;
  height: 300px;
  overflow: auto;
  border: 1px rgb(124, 120, 120) solid;
}
.inspection_info {
  margin-top: 10px;
  margin-left: 15px;
  display: flex;
  height: 12%;
}
.inspection_title {
  width: 12%;
  line-height: 32px;
}
.inspection_time {
  width: 32%;
  margin-right: 2%;
}
.inspection_rmks {
  width: 55%;
}
</style>