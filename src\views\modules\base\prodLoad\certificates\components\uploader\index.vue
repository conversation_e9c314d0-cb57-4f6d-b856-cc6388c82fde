<template>
  <div>
    <el-upload ref="upload" list-type="picture-card" :disabled="filesArray.length>=limit" :file-list="filesArray" :multiple="multiple" :limit="limit" :http-request="upload" :on-preview="handlePictureCardPreview" :on-exceed="handleExceed" :on-remove="handleRemove">
      <i class="el-icon-plus"></i>
      <div class="el-upload__tip" slot="tip">
        <span v-if="filesArray.length>=limit">最多上传{{limit}}个</span>
      </div>
    </el-upload>
  </div>
</template>
<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
import * as $http from "@/api/lic";
export default {
  name: "imgUpload",
  model: {
    prop: "modelVal",
    event: "modelChangeEvent"
  },
  props: {
    modelVal: {
      type: Array,
      default: null
    },
    acceptFileType: {
      type: Array,
      default() {
        return [
          "image/jpeg",
          "image/png",
          "image/jpg",
          "image/webp",
          "image/gif"
        ];
      }
    },
    multiple: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    postData: {
      type: Object,
      default: null
    },
    max: {
      type: Number,
      default: 1024 * 1024 * 10
    },
    limit: {
      type: Number,
      default: 999
    }
  },
  data() {
    return {};
  },
  computed: {
    filesArray() {
      let urls = [];
      let files = this.modelVal;
      return files.map(function(it, index, array) {
        let url = it.fileUrl;
        let name = url.slice(url.indexOf("_") + 1);
        return {
          name: name,
          url: it.thumbUrl,
          fileUrl: it.fileUrl,
          thumbUrl: it.thumbUrl,
          waterMarkUrl: it.waterMarkUrl
        };
      });
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.showImage(file.fileUrl);
    },
    // 图片预览
    showImage(url) {
      let divNode = document.createElement("div");
      divNode.style.display = "none";
      let imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        zIndex: 99999,
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    // 文件格式和大小验证
    validateFileHandle(file) {
      let isJPG = [
        "image/jpeg",
        "image/png",
        "image/jpg",
        "image/webp",
        "image/gif"
      ].includes(file.type);

      if (this.acceptFileType.length > 0) {
        isJPG = this.acceptFileType.includes(file.type);
      }

      if (!isJPG) {
        this.$message.error(
          `上传的文件格式有误，只允许 ${this.acceptFileType.join(",")} 格式!`
        );
        return false;
      }

      let isOverMax = file.size < this.max;
      if (!isOverMax) {
        this.$message.error(
          `上传文件大小不能超过 ${this.max / (1024 * 1024)} M!`
        );
        return false;
      }
      return true;
    },
    // 自定义上传
    upload(item) {
      let _this = this;
      let file = item.file;
      let formData = new FormData();

      // 判断文件格式
      let isValidate = this.validateFileHandle(file);
      if (!isValidate) {
        item.onError();
        return false;
      }

      formData.append("file", file, file.name);
      if (this.postData) {
        Object.keys(this.postData).forEach(key => {
          formData.append("key", _this.postData.key);
        });
      }

      $http
        .uploadLicImage(formData)
        .then(({ data: res }) => {
          if (res && res.code === 0) {
            _this.addFileUrl(res.data);
            item.onSuccess("上传成功");
          } else {
            _this.$message({
              message: res.msg,
              type: "error"
            });
            item.onError("上传失败");
          }
        })
        .catch(error => {
          item.onError("上传失败");
          console.log(error);
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.log(
              "配时文件上传失败(" +
                error.response.status +
                ")，" +
                error.response.data
            );
          } else if (error.request) {
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
            // http.ClientRequest in node.js
            console.log("配时文件上传失败，服务器端无响应");
          } else {
            // Something happened in setting up the request that triggered an Error
            console.log("配时文件上传失败，请求封装失败");
          }
        });
    },
    // 图片url
    addFileUrl(files) {
      if (!files.length) return;
      let newImageArr = [].concat(this.modelVal);
      files.forEach(it => {
        newImageArr.push({
          fileUrl: it.fileUrl,
          thumbUrl: it.thumbUrl,
          waterMarkUrl: it.waterMarkUrl
        });
      });
      this.submitModifyHandle(newImageArr);
    },
    handleExceed(file, fileList) {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    handleRemove(file, fileList) {
      let urlMap = fileList.map(it => {
        return {
          fileUrl: it.fileUrl,
          thumbUrl: it.thumbUrl,
          waterMarkUrl: it.waterMarkUrl
        };
      });
      this.submitModifyHandle(urlMap);
    },
    // 向父组件提交证件修改信息，触发父组件方法
    submitModifyHandle(dataArr) {
      this.$nextTick(() => {
        this.$emit("modelChangeEvent", dataArr);
        this.$emit("modify", dataArr);
      });
    }
  }
};
</script>
