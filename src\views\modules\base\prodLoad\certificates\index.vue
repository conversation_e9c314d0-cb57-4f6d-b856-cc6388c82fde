<template>
  <div>
    <div class="lic-panel-wrapper" v-for="key in Object.keys(templateConfig)" :key="key">
      <div v-show="isSingleSave && isModifyConfig[key]" class="padding-md align-right"
        style="border-radius: 5px 5px 0 0; background-color: #3e4686">
        <el-button size="mini" @click="saveHandle">证件信息保存</el-button>
      </div>
      <div class="lic-panel flex" data-grap
        :class="[dataSourceFormat[key] && isExpired(dataSourceFormat[key].licVldTo) ? 'is-expired' : dataSourceFormat[key] && isGoingExpired(dataSourceFormat[key].licVldTo) ? 'is-going-expired' : '']">
        <div class="panel-header no-flex flex y-center x-center">
          {{ templateConfig[key].licNm }}
        </div>
        <div class="panel-body" ref="licWrapper">
          <el-form v-if="edit" ref="licForm" :licKey="key" class="lic-edit-form" :size="size"
            :model="dataSourceFormat[key]">
            <el-row :gutter="20">
              <template v-for="header in templateConfig[key].header">
                <el-col :span="header.span" :key="header.licCatCd">
                  <el-form-item :prop="header.field" :label="header.name" :rules="$rulesFilter({
                    required: header.required,
                    type: header.validateType || '',
                    validator: header.validator || null,
                  })
                    ">
                    <template v-if="header.isAttachment">
                      <!-- 登记品种，附件表 -->
                      <el-button type="primary" round @click="showAttachment(key, header.field)">
                        <strong>附件录入</strong>
                        <span v-if="dataSourceFormat[key] && dataSourceFormat[key][header.field]">（已登记{{
                          dataSourceFormat[key][header.field].length }}种品种）</span>
                      </el-button>
                    </template>
                    <template v-else-if="header.type == 'input' && header.allowCreate">
                      <form-tag :size="size" v-model="dataSourceFormat[key][header.field]" :placeholder="header.name"
                        @change="modelChangeHandler(key)"></form-tag>
                      <!-- 自由输入创建条目 -->
                      <!-- <el-select v-model="dataSourceFormat[key][header.field]" :placeholder="'请选择' + header.name" clearable multiple filterable allow-create default-first-option
                        @change="modelChangeHandler(key)" style="width:100%;">
                        <template v-if="header.options">
                          <el-option v-for="(ist, istindex) in header.options" :key="ist.value + istindex" :label="ist.label" :value="ist.value" />
                        </template>
                      </el-select> -->
                    </template>
                    <template v-else-if="header.type == 'input'">
                      <el-input v-model="dataSourceFormat[key][header.field]" :placeholder="'请输入' + header.name" clearable
                        @change="modelChangeHandler(key)" />
                    </template>
                    <template v-else-if="header.type == 'textarea'">
                      <el-input v-model="dataSourceFormat[key][header.field]" :placeholder="'请输入' + header.name"
                        type="textarea" :rows="header.rows || 3" clearable @change="modelChangeHandler(key)" />
                    </template>
                    <template v-else-if="header.type == 'number'">
                      <el-input v-model="dataSourceFormat[key][header.field]" :placeholder="'请输入' + header.name"
                        type="number" clearable @change="modelChangeHandler(key)" />
                    </template>
                    <template v-else-if="header.type == 'date'">
                      <el-date-picker :placeholder="'请选择' + header.name" v-model="dataSourceFormat[key][header.field]"
                        :picker-options="datePickerOptions" type="date" align="right" value-format="yyyy-MM-dd"
                        @input="modelChangeHandler(key)" style="width: 100%" />
                    </template>
                    <template v-else-if="header.type == 'daterange'">
                      <el-date-picker :placeholder="'请选择' + header.name" v-model="dataSourceFormat[key][header.field]"
                        type="daterange" align="right" value-format="yyyy-MM-dd" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" @input="modelChangeHandler(key)"
                        style="width: 100%" />
                      <div v-if="header.field === 'licVldTo'">
                        <span style="color: #d00; font-weight: bold"
                          v-if="getValidateDateClassName(dataSourceFormat[key], 'licVldTo') === 'is-expired'">提示：您的证件已到期，请尽快更换！</span>
                        <span style="color: #d00; font-weight: bold"
                          v-else-if="getValidateDateClassName(dataSourceFormat[key], 'licVldTo') === 'is-going-expired'">
                          提示：您的证件即将到期，请尽快更换。
                        </span>
                      </div>
                    </template>
                    <template v-else-if="header.type == 'select'">
                      <el-select v-model="dataSourceFormat[key][header.field]" :placeholder="'请选择' + header.name"
                        clearable @change="modelChangeHandler(key)">
                        <el-option v-for="(ist, istindex) in header.options" :key="ist.value + istindex"
                          :label="ist.label" :value="ist.value" />
                      </el-select>
                    </template>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <el-row :gutter="20">
              <div class="padding-tp-md"
                :class="{ 'upload-wrapper': templateConfig[key].header.length > 0 ? true : false }">
                <el-col :span="24">
                  <el-form-item prop="attachments" label="证件照片"
                    :rules="$rulesFilter({ required: templateConfig[key].attachments.required })">
                    <uploader v-model="dataSourceFormat[key].attachments" @modify="modelChangeHandler(key)" :limit="5"
                      :multiple="true"></uploader>
                  </el-form-item>
                </el-col>
              </div>
            </el-row>
          </el-form>
          <template v-else>
            <el-form class="lic-read-form" :size="size">
              <el-row :gutter="20">
                <template v-for="header in templateConfig[key].header">
                  <el-col :span="header.span" :key="header.licCatCd">
                    <el-form-item :label="header.name + ':'">
                      <template v-if="header.isAttachment">
                        <!-- 登记品种，附件表 -->
                        <el-button type="primary" round @click="showAttachment(key, header.field)">
                          <strong>附件录入</strong>
                          <span v-if="dataSourceFormat[key] && dataSourceFormat[key][header.field]">（已登记{{
                            dataSourceFormat[key][header.field].length }}种品种）</span>
                        </el-button>
                      </template>
                      <template v-else-if="header.type == 'input' && header.allowCreate">
                        <!-- 自由输入创建条目 -->
                        <form-tag :edit="false" :size="size" v-model="dataSourceFormat[key][header.field]"
                          :placeholder="header.name" @change="modelChangeHandler(key)"></form-tag>
                      </template>
                      <template v-else-if="header.type == 'select'">
                        {{ header.options.find(item => item.value == dataSourceFormat[key][iitem.field]) ?
                          header.options.find(item => item.value == dataSourceFormat[key][header.field]).label : "" }}
                      </template>
                      <template v-else-if="header.type == 'date'">
                        {{ dataSourceFormat[key][header.field] }}
                        <span v-if="isExpired(dataSourceFormat[key].licVldTo)" style="color: #e6a23c">(该证件已过期)</span>
                        <span v-else-if="isGoingExpired(dataSourceFormat[key].licVldTo)"
                          style="color: #e6a23c">(该证件将过期)</span>
                      </template>
                      <template v-else-if="header.type == 'daterange'">
                        <template v-if="dataSourceFormat[key] && dataSourceFormat[key][header.field]">
                          {{ dataSourceFormat[key][header.field] | formatDataRange }}
                        </template>
                        <div v-if="header.field === 'licVldTo'">
                          <span style="color: #d00; font-weight: bold"
                            v-if="getValidateDateClassName(dataSourceFormat[key], 'licVldTo') === 'is-expired'">提示：您的证件已到期，请尽快更换！</span>
                          <span style="color: #d00; font-weight: bold"
                            v-else-if="getValidateDateClassName(dataSourceFormat[key], 'licVldTo') === 'is-going-expired'">
                            提示：您的证件即将到期，请尽快更换。
                          </span>
                        </div>
                      </template>
                      <template v-else>
                        {{ dataSourceFormat[key][header.field] }}
                      </template>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
              <el-row :gutter="20">
                <div class="upload-wrapper padding-tp-md">
                  <el-col :span="24">
                    <el-form-item prop="attachments" label="证件照片">
                      <template v-for="(imageSrc, index) in dataSourceFormat[key].attachments">
                        <!-- <img :src="imageSrc" :is-viewer-show="true" style="width:178px;cursor:pointer;" @click="previewHandle" :key="index" /> -->
                        <el-image style="width: 100px; height: 100px; margin-right: 10px" :src="imageSrc.fileUrl"
                          :preview-src-list="getPreviewList(dataSourceFormat[key].attachments)" :key="index"></el-image>
                      </template>
                    </el-form-item>
                  </el-col>
                </div>
              </el-row>
            </el-form>
          </template>
        </div>
      </div>
    </div>
    <el-drawer ref="drawer" direction="rtl" :visible.sync="attachmentDgVisible" :wrapperClosable="false" title="登记品种附件录入"
      size="80%" width="80%" custom-class="custom-drawer">
      <div style="width: 100%; height: 100%; padding: 10px">
        <el-table :data="drawerData" style="width: 100%">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="nm" label="化学品名称"></el-table-column>
          <el-table-column prop="aliasNm" label="别名">
            <template slot-scope="scope">
              {{ scope.row.aliasNm && scope.row.aliasNm.join("，") }}
            </template>
          </el-table-column>
          <el-table-column prop="nature" label="化学品性质"></el-table-column>
        </el-table>
        <div style="margin-top: 20px; text-align: right">
          <el-button :size="size" round @click="$refs.drawer.closeDrawer()">关 闭</el-button>
          <el-button v-if="edit" :size="size" type="primary" round @click="drawerSubmit" :loading="drawerLoading">{{
            drawerLoading ? "提交中 ..." : "批量保存" }}</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import "viewerjs/dist/viewer.min.css";
import uploader from "./components/uploader";
import formTag from "@/components/formTag";
import * as Tool from "@/utils/tool";

export default {
  name: "certificates",
  components: { uploader, formTag },
  model: {
    prop: "dataSource",
    event: "modelChangeEvent",
  },
  props: {
    // 是否只读
    edit: {
      type: Boolean,
      default: false,
    },
    // 证件是否单独保存
    isSingleSave: {
      type: Boolean,
      required: false,
    },
    // 证件模板数据
    templateConfig: {
      type: Object,
      required: true,
    },
    // 证件数据结果，默认是空
    dataSource: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      croperContentHeight: Tool.getClientHeight() - 90,
      imgCropperData: {
        file: null, // 上传的文件
        // previewSrc:'',              // 剪裁前预览图地址,
        fileType: null,
        fileName: null,
      },
      postCropperData: null, // 裁剪时子组件传递过来的数据
      cropper: null,
      startImageCrop: false,
      cropperLoading: false,
      scaleXFlag: 1,
      scaleYFlag: 1,
      cropperTarget: null, // 裁剪对象节点

      size: "small",
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
      dataSourceFormat: null,
      isModifyConfig: {},

      attachmentDgVisible: false,

      drawerLoading: false,
      option: {
        // viewBtn: false,
        // labelWidth: 120,
        cellBtn: false, // 表格行编辑操作false
        editBtn: false,
        delBtn: false, // 行内删除
        cancelBtn: false,
        dialogClickModal: false,
        index: true,
        indexWidth: 50,
        indexLabel: "序号",
        rowKey: "id",
        dialogType: "drawer",
        searchShow: false,
        searchShowBtn: false,
        align: "center",
        column: [
          {
            label: "化学品名称",
            prop: "nm",
            cell: true,
            align: "center",
            rules: [
              {
                required: true,
                message: "请输入化学品名称",
                trigger: "blur",
              },
            ],
          },
          {
            label: "别名",
            prop: "aliasNm",
            cell: true,
            slot: true,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入别名",
            //     trigger: "blur",
            //   },
            // ],
          },
          {
            label: "化学品性质",
            prop: "nature",
            type: "select",
            cell: true,
            rules: [
              {
                required: true,
                message: "请输入化学品性质",
                trigger: "blur",
              },
            ],
            dicData: [
              {
                label: "原料",
                value: "原料",
              },
              {
                label: "产品",
                value: "产品",
              },
              {
                label: "中间产品",
                value: "中间产品",
              },
            ],
          },
        ],
      },
      drawerData: [],
    };
  },
  watch: {
    // 根据模板数据顺序排序证照数据dataSource，若证照数据不存在则生成对应证件数据格式
    dataSource: {
      handler() {
        const _this = this;
        let res = {}; // 证照数据结果集
        let isModifyConfig = {};

        Object.keys(this.templateConfig).forEach(key => {
          const teplItem = _this.templateConfig[key];
          const dataSource = _this.dataSource[key] || {};
          const headers = teplItem.header;

          const itemDate = {};
          headers.forEach(function (it) {
            // daterange 日期间隔 ，isAttachment 附件表格，allowCreate：允许自建条目
            if (it.type === "daterange") {
              itemDate[it.field] = dataSource ? dataSource[it.field] || [] : [];
            } else if (it.isAttachment === true) {
              itemDate[it.field] = dataSource ? dataSource[it.field] || [] : [];
            } else if (it.allowCreate) {
              let val = dataSource && dataSource[it.field] ? dataSource[it.field] : [];
              if (Object.prototype.toString.apply(val) === "[object String]") {
                val = val.replace(/，/g, ",");
                itemDate[it.field] = val.split(",");
              } else {
                itemDate[it.field] = val;
              }
            } else {
              itemDate[it.field] = dataSource ? dataSource[it.field] || null : null;
            }
          });
          // 上传的附件
          itemDate.attachments = (dataSource && dataSource.attachments) || [];

          res[key] = itemDate;
          isModifyConfig[key] = false;
        });
        this.$set(this, "dataSourceFormat", res);
        this.$set(this, "isModifyConfig", isModifyConfig);
      },
      immediate: true,
      deep: true,
    },
  },
  filters: {
    formatDataRange: function (value) {
      if (!value || !value.length) return "";
      if (Array.isArray(value)) {
        return value.join("~");
      } else {
        return value;
      }
    },
  },
  methods: {
    createNodeLoading(targetNode, loadingText) {
      const loading = this.$loading({
        lock: true,
        text: loadingText || "加载中...",
        target: targetNode.$el,
      });
      return loading;
    },
    getValidateDateClassName(data, key) {
      if (!data) return "";
      if (!data[key]) return "";
      if (this.isExpired(data[key])) {
        return "is-expired";
      }
      if (this.isGoingExpired(data[key])) {
        return "is-going-expired";
      }
      return "";
    },
    // 判断证件是否过期
    isExpired(to) {
      let licVldTo = to;
      if (Object.prototype.toString.apply(licVldTo) === "[object Array]") {
        licVldTo = licVldTo[1] ? licVldTo[1] : "";
      }
      if (!licVldTo) {
        return false;
      }
      const res = licVldTo.match(/^\d{4}-\d{2}-\d{2}/);

      if (res.length > 0) {
        licVldTo = res[0];
        licVldTo = new Date(licVldTo + " 23:59:59").getTime();
        if (licVldTo && new Date().getTime() > licVldTo) {
          // 有效期小于当前时间，则说明过期了
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    // 判断证件是否将过期
    isGoingExpired(to) {
      let licVldTo = to;
      if (Object.prototype.toString.apply(licVldTo) === "[object Array]") {
        licVldTo = licVldTo[1] ? licVldTo[1] : "";
      }
      if (!licVldTo) {
        return false;
      }
      let bssLicenceValidity = new Date(licVldTo.replace(/-/g, "/")).getTime();
      let bssLicenceValidityLast30 = bssLicenceValidity - 60 * 60 * 24 * 30 * 1000; //30天将到期提醒
      if (new Date(bssLicenceValidityLast30).getTime() < new Date().getTime()) {
        return true;
      } else {
        return false;
      }
    },
    // 输入信息修改触发事件
    modelChangeHandler(key) {
      // 如果是具有单独保存功能的，则当证件修改时出现单独保存按钮
      if (this.isSingleSave) {
        this.isModifyConfig[key] = true;
      } else {
        this.$emit("change", this.dataSourceFormat);
        this.$emit("modelChangeEvent", this.dataSourceFormat);
      }
    },
    // 验证表单信息
    validate() {
      const _this = this;
      const promises = [];
      if (this.$refs.licForm && this.$refs.licForm.length > 0) {
        this.$refs.licForm.forEach(licForm => {
          let temp = new Promise((resolve, reject) => {
            licForm.validate(valid => {
              if (valid) {
                resolve({
                  code: 1,
                  msg: _this.templateConfig[licForm.$attrs.licKey].licNm + "验证通过",
                });
              } else {
                resolve({
                  code: 0,
                  msg: _this.templateConfig[licForm.$attrs.licKey].licNm + "：信息填写不正确",
                });
              }
            });
          });
          promises.push(temp);
        });
        let isValid = true;
        let msg = "";
        return Promise.all(promises)
          .then(results => {
            for (let i = 0, len = results.length; i < len; i++) {
              if (results[i].code === 0) {
                isValid = false;
                msg += results[i].msg + "<br />";
              }
            }
            if (isValid) {
              return true;
            } else {
              _this.$message({
                type: "error",
                dangerouslyUseHTMLString: true,
                message: msg,
              });
              return false;
            }
          })
          .catch(err => {
            console.log(err);
            return false;
          });
      } else {
        return new Promise((resolve, reject) => {
          resolve(1);
        });
      }
    },
    // submit() {
    //   this.validate().then(isValid => {
    //     if (isValid) {
    //       this.$emit("save", this.dataSourceFormat);
    //     }
    //   });
    // },
    // 单独保存
    singleSaveHandler(key) {
      this.saveHandle();
    },
    // 保存
    saveHandle() {
      this.$emit("modelChangeEvent", this.dataSourceFormat);
      this.$emit("save", this.dataSourceFormat);
    },
    // cancle() {
    //   this.$confirm("您确认取消吗?", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   })
    //     .then(() => {
    //       this.$router.go(-1);
    //     })
    //     .catch(() => {});
    // },
    // 图片预览
    // previewHandle() {
    //   var viewer = new Viewer(this.$refs.licWrapper, {
    //     zIndex: 2099,
    //     url(image) {
    //       return image.src.replace(/\@\w+\.src$/, "");
    //     },
    //     ready() {
    //       viewer.viewer.className += " custom-lic-viewer-container";
    //     },
    //     viewed() {
    //       const viewCanvas = viewer.viewer.getElementsByClassName(
    //         "viewer-canvas"
    //       );
    //       if (viewCanvas.length > 0) {
    //         const imgTags = viewCanvas[0].getElementsByTagName("img");
    //         if (imgTags.length > 0) {
    //           imgTags[0].style.marginLeft =
    //             parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
    //         }
    //       }
    //     },
    //     hidden() {
    //       viewer.destroy();
    //     }
    //   });
    // }
    showAttachment(key, field) {
      this.attachmentDgVisible = true;
      this.attachmentDgKey = key;
      this.attachmentDgField = field;
      let values = this.dataSourceFormat[key][field];
      if (Object.prototype.toString.apply(values) === "[object Array]") {
      } else if (Object.prototype.toString.apply(values) === "[object String]" && values.length > 0) {
        // values = values.replace(/，/g,',');
        // values = values.split(",");
        values = [];
      }
      this.drawerData = values.map(item => {
        item.$cellEdit = true;
        return item;
      });
    },
    addNextRow(index) {
      this.drawerData.splice(index + 1, 0, {
        $cellEdit: true,
      });
    },
    addBreakRow(index) {
      this.drawerData.splice(index == 0 ? 0 : index - 1, 0, {
        $cellEdit: true,
      });
    },
    delRow(index) {
      this.drawerData.splice(index, 1);
    },
    // drawer 提交保存附件
    drawerSubmit() {
      let _this = this;
      this.$refs.crud.validateCellForm().then(noValidObj => {
        if (Object.keys(noValidObj).length) {
          this.$message.error("请填写正确的信息！");
          return false;
        } else {
          _this.dataSourceFormat[_this.attachmentDgKey][_this.attachmentDgField] = _this.drawerData.map(res => {
            delete res.$cellEdit;
            delete res.$index;
            return res;
          });
          _this.attachmentDgVisible = false;
          _this.modelChangeHandler(_this.attachmentDgField);
        }
      });
    },
    rowUpdate(row, index, done, loading) {
      console.log(row, index, done, loading);
    },

    getPreviewList(list) {
      return list.map(it => {
        return it.fileUrl;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.cropper-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  background-color: #797979;
  font-size: 16px;
  color: #fff;

  .btn-group {
    display: table;
    margin-bottom: 8px;
    width: 100%;
    text-align: center;
  }

  .btn {
    display: inline-block;
    background: #409eff;
    padding: 5px 10px;
    margin: 0;
    color: #fff;
    font-size: 15px;
    border-right: 1px solid rgba(255, 255, 255, 0.5);

    &:first-child {
      border-radius: 5px 0 0 5px;
    }

    :last-child {
      border-radius: 0 5px 5px 0;
      border-right: none;
    }
  }

  .svg-icon {
    font-size: 20px;
  }
}

.custom-drawer {
  padding-bottom: 60px;

  .custom-drawer__content {
    height: calc(100% - 70px);
    padding: 0px 10px 20px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .custom-drawer__footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: right;
    padding: 10px;
    z-index: 10;
    background: #ededed;
  }
}

/*.el-form /deep/ {
  .el-form-item.is-expired {
    .el-form-item__label {
      color: #d00;
      font-weight: bold;
    }
  }
  .el-form-item.is-going-expired {
    .el-form-item__label {
      color: #d00;
      font-weight: bold;
    }
  }
}*/
.lic-panel-wrapper {
  margin-top: 15px;

  &:first-child {
    margin-top: 0;
  }
}

.lic-panel {
  background: #ffffff;
  border: 1px solid #e5eaf5;
  border-radius: 8px;

  .panel-header {
    box-sizing: border-box;
    width: 212px;
    line-height: 35px;
    padding: 10px;
    background-color: #f2f5ff;
    border-right: 1px solid #e5eaf5;
    border-radius: 8px 0 0 8px;
    font-size: 20px;
    color: #2f3566;
    text-align: center;
    margin: 0;
  }

  .panel-body {
    flex: 1 1 auto;
    padding: 10px;
  }

  // 已到期
  &.is-expired {
    .panel-header {
      background-color: #ffb0b0 !important;
      border: 1px solid #c95050 !important;
    }

    .panel-body {
      border: 1px solid #c95050;
      background-color: #fee2e2;
      border-left: none;
    }
  }

  // 将到期
  &.is-going-expired {
    .panel-header {
      background-color: #ffd2b2 !important;
      border: 1px solid #ecb084 !important;
    }

    .panel-body {
      border: 1px solid #ecb084;
      border-left: none;
      background-color: #fff4ec;
    }
  }

  /*  .lic-edit-form /deep/ {
    .el-form-item {
      margin-bottom: 15px;

      .el-form-item__label {
        display: block;
        font-size: 13px;
        line-height: 24px;
        color: #8184a1;
        width: 100%;
        text-align: left;
      }
      .el-form-item__content {
        width: 100%;
        // color: #2F3566;

        .el-date-editor {
          width: 100%;
        }
      }
    }
    .upload-wrapper {
      border-top: 1px solid #e5eaf5;
    }
  }
  .lic-read-form /deep/ {
    .el-form-item {
      margin-bottom: 0;
    }

    .upload-wrapper {
      border-top: 1px solid #e5eaf5;
    }
  }*/
}
</style>
