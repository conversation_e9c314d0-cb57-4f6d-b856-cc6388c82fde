<template>
    <div class="detail-container">
    <!-- <transition name="el-fade-in-linear"> -->
            <!-- <div class="chart-main" > -->
                <div class="mod-container-oper">
                    <span  class="icon" v-if="entpName" style="float:left;font-size:18px;line-height:2;color:#333;"><svg-icon :icon-class="'entp'" class-name="menu-svg-icon "></svg-icon>{{entpName}}</span>
                    <el-button-group>
                        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
                    </el-button-group>
                </div>
                <div class="panel">
                <el-row :gutter="20">
                    <el-col :sm="24" v-loading="loading">
                        <div  id="entpLocMap" v-bind:style="{'height':maxHeight+'px'}">
                
                        </div>
                    </el-col>
                </el-row>
                <el-card class="box-card draw-panel loc-list">
                        <el-collapse  >
                            <el-collapse-item title="围栏列表" name="1">
                              <el-table class="el-table" :data="list" :max-height="tableHeight" v-loading="tableLoading" highlight-current-row>
                                 <el-table-column
                                    type="index"
                                    width="30">
                                </el-table-column>
                                <el-table-column prop="name" label="围栏名称">
                                    <template slot-scope="scope">
                                        <el-popover
                                        placement="top-start"
                                        trigger="hover" >
                                            <div>{{scope.row.name}}</div>
                                            <el-button
                                                slot="reference"
                                                type="text">
                                                    <div class="loc-name" style="width:160px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
                                                        {{scope.row.name}}
                                                    </div>
                                            </el-button>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                 <el-table-column prop="lnglat"  label="操作" align="center" >
                                    <template slot-scope="scope">
                                        <div>
                                            <el-button type="primary" size="mini" plain @click="showOverlay(scope.row.lnglat)">查看</el-button>
                                        </div>
                                    </template>
                                 </el-table-column>
                            </el-table>
                            </el-collapse-item>
                        </el-collapse>
                </el-card>
                </div>
            <!-- </div> -->
    <!-- </transition> -->
    </div>
</template>


<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/prodLoad"
export default {
  data() {
    return {
      map: null,
      path: [],
      list: [],
      entpName:"",
      overlay: null,
      loading: true,
      tableLoading:false,
      tableHeight: 170,
      maxHeight: 500,
      pk:"",
      addForm: {
        entpPk:"",
        catCd: "1107.160",
        lnglat: "",
        name:"",
        centerPoint: "",
        zoom: "18",
        sysId: "330604"
      }
    };
  },
  created() {
    let entpPk = this.$route.params.id;
    this.entpName = this.$route.query.name;
    this.pk = entpPk
    this.addForm.entpPk = entpPk;
    this.getList();
  },
  mounted() {
    let maxHeight = Tool.getTableHeight() + 30;
    let _this = this;

    this.maxHeight = maxHeight;
    window.addEventListener("resize", function() {
      _this.maxHeight = Tool.getTableHeight() + 30;
    });
    this.$nextTick(() => {
      this.loadMap();
      this.loading = false;
    });
  },
  methods: {
    getList() {
      let _this = this;
      let pk = _this.pk;
      this.tableLoading = true;
      $http.getListEntp(pk)
      .then(response => {
        if (response.code == 0) {
          _this.list = response.data;
          if(!_this.entpName && _this.list[0].entpName){
            _this.entpName =  _this.list[0].entpName;
          }
        }
        this.tableLoading = false;
      })
      .catch(error => {
        throw new Error(error);
        this.tableLoading = false;
      });
      
    },
    loadMap() {
      let _this = this;
      let map = new BMap.Map("entpLocMap");
      let polyOption = {
        strokeWeight: 2,
        strokeColor: "#078aff"
      };
      this.map = map;

      map.centerAndZoom(new BMap.Point(121.682848,29.9664), 13); // 初始化地图,设置中心点坐标和地图级别
      //添加地图类型控件

      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
        })
      );
      map.setCurrentCity("宁波市"); // 设置地图显示的城市 此项是必须设置的
      map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放


      this.getBoundary();
    },
    showOverlay(lnglat) {
      if (lnglat) {
        let lnglatArr = lnglat
          .split(",")
          .join(";")
          .split(";");
        let polygon;
        let pointArr = [];

        this.path.length = 0;

        for (var i = 0, len = lnglatArr.length; i < len; i += 2) {
          pointArr.push(new BMap.Point(lnglatArr[i], lnglatArr[i + 1]));
        }

        polygon = new BMap.Polygon(pointArr, {
          strokeWeight: 2,
          strokeColor: "#e12828"
        });

        if(this.overlay){
          this.map.removeOverlay(this.overlay);
        }
        
        this.map.addOverlay(polygon); //添加覆盖物
        this.map.setViewport(pointArr); //调整地图视口
        this.overlay = polygon;
      } else {
        this.$message({
          message: "无围栏坐标数据",
          type: "error"
        });
      }
    },

    clear() {
      this.map.removeOverlay(this.overlay);
      this.path.length = 0;
      this.addForm.lnglat = "";
    },
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    refreshPage() {
      this.clear();
      this.getList();
    },
    getCurrentMapZoom() {
      return this.map.getZoom();
    },
    //获取行政区域
    getBoundary(callback){
      var bdary = new BMap.Boundary();
      var map = this.map;
      
      this.bdary = bdary;
      
      bdary.get("宁波市镇海区", function(rs){       //获取行政区域
        map.clearOverlays();        //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type:'error',
            message:'未能获取当前输入行政区域'
          });
          return ;
        }

        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 2,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#ff0000",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); //建立多边形覆盖物
          map.addOverlay(ply);  //添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        }
        if(callback){
          callback.call();
        }else{
          map.setViewport(pointArray);
        }
      });
    },
    goBack() {
        this.$router.go(-1);
    }
  }
};
</script>


<style scoped>
#entpLocMap {
  width: 100%;
  height: 600px;
}
.panel {
  position: relative;
  padding: 20px;
  background: #fff;
  webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  margin: 10px 0;
  overflow: hidden;
}
.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none !important;
}
.draw-panel {
  position: absolute;
  left: 30px;
  top: 30px;
  width: 372px;
}
.loc-list {
  top: 30px;
}
.icon svg.menu-svg-icon{
  font-size:30px;
  margin-bottom: -6px;
}
</style>