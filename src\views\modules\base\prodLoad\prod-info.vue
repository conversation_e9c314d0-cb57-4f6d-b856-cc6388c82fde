<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel" v-loading="basicLoading">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li>
            <div class="detail-desc">统一社会信用代码：</div>
            <div class="detail-area" :title="entp.uscCd">{{ entp.uscCd }}</div>
          </li>
          <li>
            <div class="detail-desc">企业名称：</div>
            <div class="detail-area" :title="entp.entpName">{{ entp.entpName }}</div>
          </li>
          <li>
            <div class="detail-desc">公司类型：</div>
            <div class="detail-area" :title="entp.legalRepIdType">{{ entp.legalRepIdType }}</div>
          </li>
          <li>
            <div class="detail-desc">企业业务分类：</div>
            <div class="detail-area" :title="entp.catNmCn">{{ entp.catNmCn }}</div>
          </li>
          <li>
            <div class="detail-desc">成立日期：</div>
            <div class="detail-area" :title="entp.establishDate">{{ entp.establishDate | FormatDate('yyyy-MM-dd') }}</div>
          </li>
          <li>
            <div class="detail-desc">营业期限：</div>
            <div class="detail-area" :title="entp.busiEndDate">{{ entp.busiEndDate | FormatDate('yyyy-MM-dd') }}</div>
          </li>
          <li>
            <div class="detail-desc">企业登记注册地：</div>
            <div class="detail-area" :title="entp.entpDist">{{ entp.entpDist }}</div>
          </li>
          <li>
            <div class="detail-desc">经营状态：</div>
            <div class="detail-area" :title="entp.regStat">{{ entp.regStat }}</div>
          </li>
          <li>
            <div class="detail-desc">法定代表人：</div>
            <div class="detail-area" :title="entp.legalRepNm">{{ entp.legalRepNm }}</div>
          </li>
          <li>
            <div class="detail-desc">发照日期：</div>
            <div class="detail-area" :title="entp.aprvDate">{{ entp.aprvDate | FormatDate('yyyy-MM-dd') }}</div>
          </li>
          <li>
            <div class="detail-desc">注册资本：</div>
            <div class="detail-area" :title="entp.regCaptital">{{ entp.regCaptital }}{{ entp.regCaptitalUnit }}</div>
          </li>
          <li>
            <div class="detail-desc">登记机关：</div>
            <div class="detail-area" :title="entp.regDept">{{ entp.regDept }}</div>
          </li>
          <li>
            <div class="detail-desc">紧急联系人：</div>
            <div class="detail-area" :title="entp.erNm">{{ entp.erNm }}</div>
          </li>
          <li>
            <div class="detail-desc">紧急联系人电话：</div>
            <div class="detail-area" :title="entp.erMob">{{ entp.erMob }}</div>
          </li>
          <li class="col-all">
            <div class="detail-desc">企业地址：</div>
            <div class="detail-area" :title="entp.location">{{ entp.location }}</div>
          </li>
          <li class="col-all">
            <div class="detail-desc">营业执照经营范围：</div>
            <div class="detail-area wrap-yes" :title="entp.businessScope">{{ entp.businessScope }}</div>
          </li>
        </ul>
      </div><!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div><!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
      </div>
      <div class="panel-body" style="background-color: #edf0f5">
        <certificates ref="certificates" :edit="false" v-model="licData" :templateConfig="certTeplData" />
      </div>
    </div>
  </div>
</template>

<script>
import * as $http from "@/api/entp";
import initLicImg from 'static/img/initLicImg.jpg'
import Viewer from 'viewerjs'
import 'viewerjs/dist/viewer.min.css'
import licConfig from "@/utils/licConfig";
import certificates from "./certificates";


export default {
  name: "EntpInfo",
  props: {         // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    }
  },
  components: {
    certificates,
  },
  data() {
    return {
      isShowOper: true,       // 默认为页面，显示操作栏
      detailLoading: false,
      basicLoading: false,
      entp: {},
      initLicImgBg: initLicImg,
      licData: {},
      prodEntpTypeList: [],
      custArr: [],
      custArr2: [],
      custArr3: [],
      certTeplData: null,

    };
  },
  created() {
    if (!this.isCompn) {   //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error('对不起，页面数据无法查询');
      }
    } else {
      this.isShowOper = false;
    }
    this.certTeplData = licConfig["chemicalentp"] || {};
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    }
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;
      this.detailLoading = true;
      if (ipPk) {
        $http
          .getEntpByEntpPk(ipPk)
          .then(response => {
            if (response.code == 0) {
              _this.entp = response.data ? response.data.entp : {};
              if (_this.entp.licJson) {
                let licJson = JSON.parse(_this.entp.licJson);
                _this.$set(_this, "licData", licJson);
              } else {
                _this.$set(_this, "licData", '');
              }
              // 托运联系人
              if (_this.entp.custDetail) {
                let custDetail = JSON.parse(_this.entp.custDetail) || [];
                _this.$set(_this, "custArr", custDetail);
              }

              // 紧急联系人
              if (_this.entp.erDetail) {
                let erDetail = JSON.parse(_this.entp.erDetail) || [];
                _this.$set(_this, "custArr2", erDetail);
              }

              // 装卸联系人
              if (_this.entp.loadDetail) {
                let loadDetail = JSON.parse(_this.entp.loadDetail) || [];
                _this.$set(_this, "custArr3", loadDetail);
              }
            } else {
              _this.$message({
                message: response.msg,
                type: "error",
              });
            }
            _this.detailLoading = false;
          })
          .catch(error => {
            throw new Error(error);
            _this.detailLoading = false;
          });
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    // 图片预览
    previewHandle(e) {
      e.target.click();
      var viewer = new Viewer(this.$refs.licwape, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, '');
        },
        ready() {
          viewer.viewer.className += ' custom-lic-viewer-container'
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName('viewer-canvas');
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName('img');
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = (parseFloat(imgTags[0].style.marginLeft) - 200) + 'px'
            }
          }
        },
        hidden() {
          viewer.destroy();
        }
      })
    }
  }
};
</script>
<style scoped>
a {
  text-decoration: none;
  color: inherit;
}

.panel-footer {
  overflow: hidden;
}

.panel-footer .fl-l {
  float: left;
}

.panel-footer .fl-r {
  float: right;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.upload-img-cropper-wape {
  float: left;
  padding-right: 10px;
  margin-bottom: 15px;
  box-sizing: border-box;
}

.upload-img-cropper-wape .upload-img-cropper-msg {
  font-size: 12px;
  color: #9a9a9a;
  text-align: left;
}

.upload-img-cropper-wape .upload-img-cropper-main {
  position: relative;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  background-color: #fff;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  width: 180px;
  height: 130px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show {
  width: 100%;
  height: 100%;
  display: table;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show>span {
  vertical-align: middle;
  text-align: center;
  display: block;
  display: table-cell;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show .upload-img-cropper-main-show-addbtn .desc {
  font-size: 12px;
  color: #9c9c9c;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-show .upload-img-cropper-main-show-addbtn button {
  border-radius: 50%;
  padding: 12px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  cursor: default;
  border-radius: 0 8px 8px 0;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-around;
  font-size: 22px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper.show-oper {
  width: 50px;
}

.upload-img-cropper-wape .upload-img-cropper-main .upload-img-cropper-main-oper>i {
  flex: 1 1 1;
  cursor: pointer;
}
</style>
