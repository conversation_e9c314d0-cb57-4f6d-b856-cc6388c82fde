

<template>
<div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination"
        @resizeSearchbar="resizeSearchbar" @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" 
        style="width: 100%;" :max-height="tableHeight">
        <el-table-column prop="entpName" label="企业名称">
            <template slot-scope="scope">
                <el-popover placement="right-start" width="200" trigger="hover">
                  <div>{{scope.row.entpName}}</div>
                  <el-button slot="reference" @click.native.prevent="showDetail(scope.row)" type="text">
                    {{scope.row.entpName}}
                  </el-button>
                </el-popover>
              </template>
        </el-table-column>
        <el-table-column prop="erNm" label="联系人"></el-table-column>
        <el-table-column prop="erMob" label="手机号码"></el-table-column>
        <el-table-column prop="mobile" label="固定电话"></el-table-column>
        <el-table-column prop="location" label="联系地址"></el-table-column>
        <el-table-column prop="" label="操作">
            <template slot-scope="scope">
                <div>
                    <el-button type="primary" size="mini" plain @click="showOverlay(scope.row)">查看围栏</el-button>
                </div>
            </template>
        </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
        <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]" style="float:right;"
            :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
            @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
    </div>
</div>
</template>


<script>
import Searchbar from '@/components/Searchbar';
import * as Tool from '@/utils/tool';
import { getEntpLocList, getListEntp, getEntpLocAdd, getEntpLocUpd } from '@/api/prodLoad';
export default {
    name: 'ProdList',
    data() {
        return {
            map: null,
            path: [],
            list: [],
            tableHeight: 520,
            listLoading: false,
            catCd: '2100.202.210',
            searchItems: {
                normal: [{ name: '企业名称', field: 'entpName', type: 'text', dbfield: 'entp_name', dboper: 'cn' }],
                more: []
            },
            pagination: {
                total: 0,
                limit: 15,
                page: 1
            }
        };
    },
    components: {
        Searchbar
    },
    mounted() {
        const _this = this;
        window.addEventListener('resize', this.setTableHeight);
        
        let query = this.$route.query;
        this.pagination.page = query.currPage?parseInt(query.currPage):1;
        this.pagination.limit = query.pageSize?parseInt(query.pageSize):20;
        this.pagination.total = this.pagination.page*this.pagination.limit;
        this.$refs.searchbar.init(query);
        
        this.setTableHeight();
        this.getList();
    },
    destroyed(){
        window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
        //显示企业详情
        showDetail: function(row) {
            // console.log(row);
            this.$router.push({
                path: "/base/prodLoad/info/" + row.ipPk,
                query:row
            });
        },

        setTableHeight(){
            this.$nextTick(() => {
                this.tableHeight = Tool.getClientHeight()-160-this.$refs.searchbar.$el.offsetHeight;
            })
        },

        // 改变搜索框的高度
        resizeSearchbar(){
            this.setTableHeight();
        },

        // 分页跳转
        handleCurrentChange: function(val) {
            this.pagination.page = val;
            // this.getList();
            this.$refs.searchbar.searchHandle(true);
        },

        // 分页条数修改
        handleSizeChange: function(val) {
            this.pagination.limit = val;
            // this.getList();
            this.$refs.searchbar.searchHandle(true);
        },

        getList(data) {
            let _this = this;
            let filters;
            if (data) {
                if(data.resetCurrentPage){
                    this.pagination.page = 1;
                }
                if(data.searchData){
                    filters = data.searchData;
                }
            } else {
                filters = this.$refs.searchbar.get();
            }

            filters.rules.push({ field: 'cat_cd', op: 'eq', data: '2100.185.150.150' });

            let params = Object.assign({}, { filters: filters }, this.pagination);

            delete params.total;
            getEntpLocList(params)
                .then(response => {
                    if (response.code == 0) {
                        _this.list = response.page.list;
                        _this.pagination.total = response.page.totalCount;
                        _this.pagination.page = response.page.currPage;
                    }
                    this.listLoading = false;
                })
                .catch(error => {
                    this.listLoading = false;
                });
        },

        showOverlay(row) {
            this.$router.push({ path: '/base/prodLoad/add/' + row.ipPk, query: { name: row.entpName } });
        }
    }
};
</script>