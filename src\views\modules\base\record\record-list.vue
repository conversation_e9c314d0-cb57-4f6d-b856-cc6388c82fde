<!--
 * @Author: your name
 * @Date: 2020-09-23 15:47:13
 * @LastEditTime: 2020-10-28 13:24:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \gov\src\views\modules\base\record\record-list.vue
-->
<template>
  <div class="recordList">
    <el-card style="width: 100%">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="date1" label="登记类别"> </el-table-column>
        <el-table-column prop="date2" label="登记数量"> </el-table-column>
        <el-table-column prop="date3" label="否决数量"> </el-table-column>
        <el-table-column prop="date4" label="通过数量"> </el-table-column>
        <el-table-column prop="date5" label="合格比例"> </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import * as $http from "@/api/alarmFeedback";
export default {
  name: "recordList",
  mounted() {
    this.getgetBasicData();
  },
  activated() {
    this.getgetBasicData();
  },
  methods: {
    getgetBasicData() {
      $http.getBasic().then((res) => {
        console.log(res);
        this.tableData[0].date1 = "运输公司";
        this.tableData[1].date1 = "车辆信息";
        this.tableData[2].date1 = "从业人员";
        // 登记数量
        this.tableData[0].date2 = res.entpCnt;
        this.tableData[1].date2 = res.vecCnt;
        this.tableData[2].date2 = res.persCnt;
        // 否决数量
        this.tableData[0].date3 = res.entpCnt - res.entpApprCnt;
        this.tableData[1].date3 = res.vecCnt - res.vecApprCnt;
        this.tableData[2].date3 = res.persCnt - res.persApprCnt;
        // 通过数量
        this.tableData[0].date4 = res.entpApprCnt;
        this.tableData[1].date4 = res.vecApprCnt;
        this.tableData[2].date4 = res.persApprCnt;
        // 合格比例
        this.tableData[0].date5 =
          ((res.entpApprCnt / res.entpCnt) * 100).toFixed(2) + "%";
        this.tableData[1].date5 =
          ((res.vecApprCnt / res.vecCnt) * 100).toFixed(2) + "%";
        this.tableData[2].date5 =
          ((res.persApprCnt / res.persCnt) * 100).toFixed(2) + "%";
        this.loading = false;
      });
    },
  },
  data() {
    return {
      loading: true,
      tableData: [
        {
          date1: "",
          date2: "",
          date3: "",
          date4: "",
          date5: "",
        },
        {
          date1: "",
          date2: "",
          date3: "",
          date4: "",
          date5: "",
        },
        {
          date1: "",
          date2: "",
          date3: "",
          date4: "",
          date5: "",
        },
      ],
    };
  },
};
</script>
<style scoped>
.recordList {
  padding: 20px;
}
</style>
