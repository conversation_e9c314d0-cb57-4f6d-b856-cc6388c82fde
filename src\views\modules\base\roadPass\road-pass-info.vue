<template>
  <el-dialog title="通行证详情" :close-on-click-modal="false" :visible.sync="visible" width="63%" top="5vh">
    <div style="padding: 5px 20px;" v-loading="formLoading">
      <TemplateFrom ref="templateFrom" @templateData="setTemplateId"></TemplateFrom>
      <ContractFrom :isView="isView" v-if="templateId" ref="contractFrom" :templateId="templateId" @update="updateContractList"></ContractFrom>
      <RouteMapFrom :isView="isView" v-if="templateId && visible" ref="routeMapFrom" :contractList="newContractList" :templateId="templateId"></RouteMapFrom>
      <VehicleFrom :isView="isView" v-if="templateId" ref="vehicleFrom" :templateId="templateId"></VehicleFrom>
      <submitFrom :isView="isView" v-if="templateId" ref="submitFrom" :templateId="templateId" @changeLoading="changeLoading"></submitFrom>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/roadPass";
import TemplateFrom from './roadPassForm/template-from.vue';
import ContractFrom from './roadPassForm/contract-from.vue';
import RouteMapFrom from './roadPassForm/route-map-from.vue';
import VehicleFrom from './roadPassForm/vehicle-from';
import submitFrom from './roadPassForm/submit-from.vue';
import { cloneDeep } from "lodash";
export default {
  name: "",
  components: {
    TemplateFrom,
    ContractFrom,
    VehicleFrom,
    RouteMapFrom,
    submitFrom,
  },
  data() {
    return {
      visible: false,
      formLoading: false,
      isView:true,
      templateId: "",
      templateData: {},
      newContractList: [],
      
    };
  },
  methods: {
    init(id) {
      this.visible = true;
      if(id){
        this.templateId = id;
        $http.getTemplateDetail(id).then((res) => {
          if (res.code == 0 && res.data) {
            this.setTemplateData(res.data);
          } else {
            this.$message.error(res.msg || "获取模板详情失败");
          }
        });
      }
    },
    setTemplateId(data) {
      this.templateId = data.id;
    },
    setTemplateData(data){
      this.$set(this, "templateData", data);
      let contracts = cloneDeep(data.contracts);
      this.newContractList = contracts || [];
      // this.$set(this,"newContractList", data.contracts || []);
      this.$nextTick(() => {
        this.$refs.templateFrom.setData(data);
        this.$refs.contractFrom.setData(data);
        this.$refs.vehicleFrom.setData(data);
        this.$refs.routeMapFrom.setData(data);
      });
    },
    updateContractList(contractList) {
      let data = cloneDeep(contractList);
      this.newContractList = data;
      // this.$set(this,"newContractList", data);
    },
    changeLoading(loading, visible = false) {
      this.formLoading = loading;
      if (visible) {
        this.visible = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>