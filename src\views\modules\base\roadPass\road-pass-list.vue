<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar">
      <template slot="button">
        <el-button :size="size" type="primary" icon="el-icon-plus" title="新增" @click="editHandler()">新增</el-button>
      </template>
    </searchbar>

    <!-- 卡片列表 -->
    <div class="card-container" v-loading="listLoading">
      <el-row :gutter="16">
        <el-col
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="6"
          v-for="item in dataList"
          :key="item.id"
          class="card-col"
        >
          <el-card class="road-pass-card" shadow="hover" @click="infoHandler(item.id)">
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="card-title">{{ item.carrierName }}</div>
              <el-tag
                :type="getStatusTagType(item.auditStatus)"
                size="small"
                class="status-tag"
              >
                {{ item.auditStatusStr }}
              </el-tag>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="info-row">
                <span class="info-label">模板名称:</span>
                <span class="info-value">{{ item.templateName }}</span>
              </div>
              <!-- <div class="info-row">
                <span class="info-label">申请时间:</span>
                <span class="info-value">{{ formatDate(item.startDate) }}</span>
              </div> -->

              <!-- <div class="info-row">
                <span class="info-label">车辆数:</span>
                <span class="info-value">{{ item.vehicleCount || 0 }}辆</span>
              </div> -->

              <!-- <div class="info-row">
                <span class="info-label">路线:</span>
                <span class="info-value" :title="item.routeInfo">{{ item.routeInfo || '未设置路线' }}</span>
              </div> -->

              <div class="info-row">
                <span class="info-label">有效期:</span>
                <span class="info-value">{{ item.startDate }} 至 {{ item.endDate }}</span>
              </div>
            </div>

            <!-- 剩余天数 -->
            <!-- <div class="remaining-days" v-if="item.auditStatus === 'APPROVE'">
              <i class="el-icon-time"></i>
              <span>剩余{{ getRemainingDays(item.endDate) }}天</span>
            </div> -->

            <!-- 操作按钮 -->
            <div class="card-actions">
              <!-- <el-button type="primary" size="small" @click="infoHandler(item.id)">详情</el-button> -->
              <el-button
                type="warning"
                size="small"
                :disabled="item.auditStatus == 'APPROVE'"
                @click="editHandler(item.id)"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                size="small"
                :disabled="item.auditStatus == 'APPROVE'"
                @click="acceptHandler(item)"
              >
                受理
              </el-button>
              <el-button
                type="danger"
                size="small"
                :disabled="item.auditStatus == 'APPROVE'"
                @click="rejectHandler(item)"
              >
                驳回
              </el-button>
              <el-button
                type="success"
                size="small"
                :disabled="item.auditStatus == 'APPROVE'"
                @click="approveHandler(item)"
              >
                通过
              </el-button>
              <el-button
                type="warning"
                size="small"
                :disabled="item.auditStatus == 'APPROVE'"
                @click="printHandler(item)"
              >
                汇总表
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <div v-if="!listLoading && dataList.length === 0" class="empty-state">
        <i class="el-icon-document"></i>
        <p>暂无通行证数据</p>
      </div>
    </div>
    <!-- 分页条 -->
    <div ref="paginationbar" class="pagination-wrapper">
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="pageNo"
        :total="total"
        style="float: right"
        layout="sizes, prev, pager, next, total"
        @current-change="pageNoChangeHandler"
        @size-change="pageSizeChangeHandler"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <RoadPassForm v-if="editVisible" ref="edit"></RoadPassForm>
    <!-- 弹窗, 详情-->
    <RoadPassInfo v-if="infoVisible" ref="info"></RoadPassInfo>
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/roadPass";
import { cloneDeep } from "lodash";
import RoadPassForm from "./road-pass-form.vue";
import RoadPassInfo from "./road-pass-info.vue";
import dayjs from "dayjs";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    RoadPassForm,
    RoadPassInfo,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getTemplatePage,
        delAPI: $http.deleteTemplate,
        isPackWithFilters: false,
      },
      searchItems: {
        normal: [
          {
            name: "模板名称",
            field: "templateName",
            type: "text",
            dbfield: "templateName",
            dboper: "cn",
          },
          {
            name: "有效期",
            field: "daterange",
            type: "daterange",
            dbfield: "daterange",
            dboper: "le",
            default: this.get30Date(),
          },
        ],
        more: [],
      },
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    setSubmitParams(postData) {
      let params = cloneDeep(postData);
      delete params.daterange;
      // 设置有效期时间范围
      if (postData.daterange) {
        let dateRange = postData.daterange.split(",");
        params.begin = dateRange[0];
        params.end = dateRange[1];
      }
      return params;
    },
    initListAfter(res) {
      if (res.code == 0) {
        this.dataList = res.data.list || [];
        this.total = res.data.totalCount || 0;
      }
    },
    // 获取状态标签类型
    getStatusTagType(auditStatus) {
      const statusMap = {
        'APPROVE': 'success',    // 已生效
        'PENDING': 'warning',    // 待审核
        'REJECT': 'danger',      // 已拒绝
        'DRAFT': 'info'          // 草稿
      };
      return statusMap[auditStatus] || 'info';
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      return dayjs(dateStr).format('YYYY-MM-DD');
    },
    // 计算剩余天数
    getRemainingDays(endDate) {
      if (!endDate) return 0;
      const today = dayjs();
      const end = dayjs(endDate);
      const diff = end.diff(today, 'day');
      return diff > 0 ? diff : 0;
    },
    
    acceptHandler(row){
      let params = {
        id: row.id,
        receiveRemark:''
      };
      this.$confirm("确定受理此通行证审批吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.receive(params).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: "受理成功!"
          });
          this.getDataList();
         }else {
          this.$message.error(msg);
        }
        this.listLoading = false;
      }).catch(error => {
        console.log(error);
        this.listLoading = false;
      })
      }).catch(err => {})
      
    },
    // 审批通过
    approveHandler(row) {
      let params = {
        id: row.id,
        finishRemark:''
      };
      this.$confirm("确定审批通过吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        $http.approve(params)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
              });
              this.getDataList();
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      });
    },
    // 驳回
    rejectHandler(row) {
      this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{2,200}$/,
          inputErrorMessage: '驳回原因长度应在2-200个字符之间'
        }).then(({ value }) => {
          let params= {
            id:row.id,
            finishRemark:value,
          }
          $http.approve(params)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
              });
              this.getDataList();
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
          
         
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消驳回'
          });       
        });
    },

    printHandler(row) {
      $http
        .print({id:row.id})
        .then(response => {
          if (response) {
            const blob = new Blob([response], { type: "application/zip" }); // 根据文件类型调整 MIME
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `通行证汇总表_${row.carrierName}_${row.templateName}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } else {
            this.$message.error("下载失败: " + res.msg);
          }
        })
        .catch(error => {
          console.error("下载失败:", error);
        });
    },
    //获取近30天日期
    get30Date() {
      return [dayjs().subtract(1, "day").format("YYYY-MM-DD"), dayjs().add(30, "day").format("YYYY-MM-DD")];
    },
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  margin-top: 16px;
  min-height: 400px;
}

.card-col {
  margin-bottom: 16px;
}

.road-pass-card {
  // height: 280px;
  display: flex;
  flex-direction: column;

  .el-card__body {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .status-tag {
    flex-shrink: 0;
  }
}

.card-content {
  flex: 1;

  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    .info-label {
      color: #909399;
      width: 70px;
      flex-shrink: 0;
    }

    .info-value {
      color: #606266;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.remaining-days {
  display: flex;
  align-items: center;
  margin: 12px 0;
  padding: 6px 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  font-size: 12px;
  color: #1890ff;

  i {
    margin-right: 4px;
  }
}

.card-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;

  // .el-button--text {
  //   padding: 4px 8px;
  //   font-size: 13px;
  // }
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .card-col {
    margin-bottom: 12px;
  }

  .road-pass-card {
    height: auto;
    min-height: 240px;
  }

  .card-header .card-title {
    font-size: 15px;
  }

  .card-content .info-row {
    font-size: 13px;

    .info-label {
      width: 60px;
    }
  }
}

@media (max-width: 480px) {
  .card-actions {
    gap: 12px;
  }

  .card-header .card-title {
    font-size: 14px;
  }
}
</style>
