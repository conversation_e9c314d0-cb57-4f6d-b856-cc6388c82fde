<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :search-items="searchItems" :pagination="pagination" @search="getDataList" @resize="setTableHeight" :size="size" class="grid-search-bar">
      <template slot="button">
        <el-button :size="size" type="primary" icon="el-icon-plus" title="新增" @click="editHandler()">新增</el-button>
      </template>
    </searchbar>
    <el-table class="el-table" :data="dataList" highlight-current-row border style="width: 100%" v-loading="listLoading" :height="tableHeight" :size="size">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column label="模板名称" prop="templateName" min-width="140"></el-table-column>
      <el-table-column label="有效期" prop="startDate" min-width="300" align="center">
        <template slot-scope="scope">{{ scope.row.startDate }} 至 {{ scope.row.endDate }}</template>
      </el-table-column>
      <el-table-column label="备注" prop="submitRemark" min-width="150" align="center"></el-table-column>
      <el-table-column label="审核状态" prop="auditStatusStr" min-width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" title="详情" @click="infoHandler(scope.row.id)">详情</el-button>
          <el-button type="text" title="修改" :disabled="scope.row.auditStatus == 'APPROVE'" @click="editHandler(scope.row.id)">编辑</el-button>
          <el-button v-if="scope.row.auditStatus == 'APPROVE'" type="text" title="删除" @click="printHandler(scope.row)">下载</el-button>
        </template>
      </el-table-column>   
    </el-table>
    <!-- 分页条 -->
    <div ref="paginationbar" class="pagination-wrapper">
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="pageNo"
        :total="total"
        style="float: right"
        layout="sizes, prev, pager, next, total"
        @current-change="pageNoChangeHandler"
        @size-change="pageSizeChangeHandler"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <RoadPassForm v-if="editVisible" ref="edit"></RoadPassForm>
    <!-- 弹窗, 详情-->
    <RoadPassInfo v-if="infoVisible" ref="info"></RoadPassInfo>
  </div>
</template>

<script>
import mixinGrid from "@/mixins/grid";
import Searchbar from "@/components/searchbar2";
import * as $http from "@/api/roadPass";
import { cloneDeep } from "lodash";
import RoadPassForm from "./road-pass-form.vue";
import RoadPassInfo from "./road-pass-info.vue";
import dayjs from "dayjs";
export default {
  mixins: [mixinGrid],
  name: "trainingList",
  components: {
    Searchbar,
    RoadPassForm,
    RoadPassInfo,
  },
  data() {
    return {
      // 搜索栏功能
      gridOptions: {
        listAPI: $http.getTemplatePage,
        delAPI: $http.deleteTemplate,
        isPackWithFilters: false,
      },
      searchItems: {
        normal: [
          {
            name: "模板名称",
            field: "templateName",
            type: "text",
            dbfield: "templateName",
            dboper: "cn",
          },
          {
            name: "有效期",
            field: "daterange",
            type: "daterange",
            dbfield: "daterange",
            dboper: "le",
            default: this.get30Date(),
          },
        ],
        more: [],
      },
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  created() {},
  mounted() {},
  destroyed() {},
  methods: {
    setSubmitParams(postData) {
      let params = cloneDeep(postData);
      delete params.daterange;
      // 设置有效期时间范围
      if (postData.daterange) {
        let dateRange = postData.daterange.split(",");
        params.begin = dateRange[0];
        params.end = dateRange[1];
      }
      return params;
    },
    initListAfter(res, postData) {
      if (res.code == 0) {
        this.dataList = res.data.list || [];
        this.total = res.data.totalCount || 0;
      }
    },
    // 删除
    deleteHandler(id) {
      let params = {
        id: id,
      };
      this.$confirm("确定进行删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        this.gridOptions
          .delAPI(params)
          .then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.getDataList();
                },
              });
            } else {
              this.$message.error(msg);
            }
            this.listLoading = false;
          })
          .catch(err => {
            console.log(err);
            this.listLoading = false;
          });
      });
    },
    printHandler(row) {
      $http
        .print(row.id)
        .then(response => {
          if (response) {
            const blob = new Blob([response.data], { type: "application/json" }); // 根据文件类型调整 MIME
            const url = URL.createObjectURL(blob);
            const newWindow = window.open(url);
            newWindow.onload = () => {
              newWindow.print();
            };

            // const blob = new Blob([res.data], { type: "application/pdf" });
            // const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `通行证_${row.templateName}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } else {
            this.$message.error("打印失败: " + res.msg);
          }
        })
        .catch(error => {
          console.error("打印失败:", error);
        });
    },
    //获取近30天日期
    get30Date() {
      return [dayjs().subtract(1, "day").format("YYYY-MM-DD"), dayjs().add(30, "day").format("YYYY-MM-DD")];
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
