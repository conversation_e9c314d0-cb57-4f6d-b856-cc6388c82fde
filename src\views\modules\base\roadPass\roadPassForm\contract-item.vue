<template>
  <el-card class="box-card" v-loading="formLoading">
    <div slot="header" class="header-box">
      <div>合同-{{ contractIndex }}</div>
      <div v-if="!isView">
        <el-button v-if="dataForm.isAdd || isEdit" type="text" @click="submit()">保存</el-button>
        <el-button v-if="!dataForm.isAdd && !isEdit" type="text" @click="update()">修改</el-button>
        <el-button v-if="!dataForm.isAdd && !isEdit" type="text" @click="deleteHandle()">删除</el-button>
        <el-button v-if="!dataForm.isAdd && isEdit" type="text" @click="cancel()">取消</el-button>
      </div>
    </div>
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="dataForm"
      :size="size"
      @keyup.enter.native="submit()"
      label-width="130px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="装货企业:" prop="loadAddress">
            <el-input
              style="width: 240px"
              :disabled="!isEdit"
              v-model="dataForm.loadAddress"
              placeholder="请填入装货企业名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卸货企业:" prop="unloadAddress">
            <el-input
              style="width: 240px"
              :disabled="!isEdit"
              v-model="dataForm.unloadAddress"
              placeholder="请填入卸货企业名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同开始日期:" prop="startDate">
            <el-date-picker
              style="width: 240px"
              :disabled="!isEdit"
              v-model="dataForm.startDate"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同结束日期:" prop="endDate">
            <el-date-picker
              style="width: 240px"
              :disabled="!isEdit"
              v-model="dataForm.endDate"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="货物重量（吨）:" prop="qty">
            <el-input-number :disabled="!isEdit" v-model="dataForm.qty" :step="1"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同文件上传:" prop="file">
            <FilesPreview :disabled="false" :files="dataForm.file">
              <template slot="showName" slot-scope="{ index }">
                <span>附件-{{ index + 1 }}</span>
              </template>
            </FilesPreview>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
import FileUpload from '@/components/FileUpload'
// import FilesPreview from "@/components/FilesPreview";
import FilesPreview from './files-preview'
import * as $http from '@/api/roadPass'
export default {
  name: '',
  components: {
    FileUpload,
    FilesPreview,
  },
  props: {
    contractIndex: {
      type: Number,
      required: true, // 必传
    },
    contractData: {
      type: Object,
      required: true, // 必传
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      size: 'small',
      formLoading: false,
      dataForm: {
        templateId: '',
        loadAddress: '',
        unloadAddress: '',
        startDate: '',
        endDate: '',
        qty: 1,
        file: '',
      },
      initData: {
        templateId: '',
        loadAddress: '',
        unloadAddress: '',
        startDate: '',
        endDate: '',
        qty: 1,
        file: '',
      },
      fileList: [],
      rules: {
        templateId: [{ required: true, message: '请选择合同模板', trigger: 'blur' }],
        loadAddress: [{ required: true, message: '请输入装货企业', trigger: 'blur' }],
        unloadAddress: [{ required: true, message: '请输入卸货企业', trigger: 'blur' }],
        startDate: [{ required: true, message: '请选择合同开始日期', trigger: 'change' }],
        endDate: [{ required: true, message: '请选择合同结束日期', trigger: 'change' }],
        qty: [
          { required: true, message: '请输入货物重量', trigger: 'blur' },
          { type: 'number', min: 1, message: '货物重量必须大于等于1', trigger: 'blur' },
        ],
        file: [{ required: true, message: '请上传合同文件', trigger: 'blur' }],
      },
      isEdit: false, // 是否编辑状态
      oldData: {},
    }
  },
  watch: {
    contractData: {
      handler(newValue, oldValue) {
        // newValue=新值 oldValue=旧值
        if (newValue && newValue.id) {
          let data = Object.assign({}, this.initData, newValue)
          if (data.isAdd) {
            this.isEdit = true
          }
          this.$set(this, 'dataForm', data)
          this.resetFileData()
        }
      },
      deep: true, // 开启深度监听
      immediate: true, // 开启立即执行
    },
  },
  methods: {
    // 上传文件
    onUpload(e) {
      if (e.length) {
        let fileStr = [...this.fileList, ...e.map((item) => ({ url: item.fileUrl }))]
        this.dataForm.file = fileStr.map((item) => item.url).join(',')
        this.resetFileData()
      }
    },
    // 上传文件变化
    onFileChange(e) {
      this.dataForm.file = e.map((item) => item.url).join(',')
      this.resetFileData()
    },
    // 更新文件列表
    resetFileData() {
      this.$nextTick(() => {
        const fileStr = this.dataForm.file
        let fileList = fileStr
          ? fileStr.split(',').map((item, index) => ({
              url: item,
              name: `附件${index + 1}`,
            }))
          : []
        this.$set(this, 'fileList', fileList)
      })
    },
    getIsEdit() {
      return this.isEdit
    },
    setLoading(loading) {
      this.formLoading = loading
    },
    update() {
      this.isEdit = true
      this.$set(this, 'oldData', Object.assign({}, this.dataForm))
    },
    // 删除
    deleteHandle() {
      this.$confirm('确定删除操作', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let params = {
            id: this.dataForm.id,
            templateId: this.dataForm.templateId,
          }
          $http.deleteContract(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '删除成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.$emit('contractData', 'delete', this.dataForm.id)
                },
              })
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    cancel() {
      if (this.dataForm.isAdd) {
        this.$emit('contractData', 'delete', this.dataForm.id)
      } else {
        this.isEdit = false
        this.$set(this, 'dataForm', this.oldData)
        this.resetFileData()
        this.$set(this, 'oldData', {})
      }
    },
    submit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let params = Object.assign({}, this.dataForm)
          let dateId = params.id
          let API = params.isAdd ? $http.addContract : $http.updateContract
          if (params.isAdd) {
            delete params.id
          }
          delete params.isAdd
          this.formLoading = true
          API(params)
            .then((res) => {
              this.formLoading = false
              this.isEdit = false
              if (res.code == 0) {
                this.$message.success('保存合同成功')
                if (res.data) {
                  this.$emit('contractData', 'update', dateId, res.data)
                }
              }
            })
            .catch((error) => {
              this.formLoading = false
              this.isEdit = false
              console.error('操作失败:', error)
            })
        } else {
          this.$message.error('请填写完整信息')
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  &::v-deep .el-card__header {
    padding: 5px 20px;
  }
  .header-box {
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 3px;
      height: 60%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  &::v-deep .file-upload-tip {
    margin-left: 5px;
    color: #ff6b6b;
    white-space: nowrap;
  }
}
</style>