<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="header-box">
        <div>备注信息</div>
      </div>
      <el-input type="textarea" :disabled="isView" placeholder="请输入备注信息" v-model="textarea" maxlength="200"></el-input>
    </el-card>
    <div style="height: 60px;">
      <div style="float: right; ">
        <el-button type="primary" @click="close">关闭</el-button>
        <el-button v-if="!isView" type="warning" @click="submit">提交通行证</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as $http from "@/api/roadPass";
import { mapGetters } from "vuex";
import day from "dayjs";
export default {
  name: "",
  components: {},
  props: {
    templateId: {
      type: String,
      default: false,  // 默认
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textarea: "",
    };
  },
  computed: {
    ...mapGetters(["username","logonMobile"]),
  },
  methods: {
    submit() {
      // 提交逻辑
      this.$emit("changeLoading", true);
      let params = {
        id: this.templateId,
        submitInfo: this.username + this.logonMobile,
        submitTime: day().format("YYYY-MM-DD HH:mm:ss"),
        submitRemark: this.textarea,
      }
      // 模拟提交
      // setTimeout(() => {
      //   console.log("提交参数:", params);
      //   this.$message.success("提交成功");
      //   this.$emit("changeLoading", false, true);
      // }, 1000);
      // 调用API提交
      $http.submit(params).then((res) => {
        this.$emit("changeLoading", false);
        if (res.code === 0) {
          this.$message.success("通行证提交成功");
          this.$emit("changeLoading", false, true);
        } else {
          this.$emit("changeLoading", false);
          console.error("提交失败:", res.msg);
        }
      }).catch((error) => {
        this.$emit("changeLoading", false);
        console.error("提交失败:", error);
      });
    },
    close(){
      this.$emit("changeLoading", false, true);
    }
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  &::v-deep .el-card__header {
    padding: 15px 20px;
  }
  .header-box {
    height: 25px;
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 100%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>