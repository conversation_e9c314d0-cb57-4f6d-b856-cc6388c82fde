<template>
  <el-form class="form-container" v-loading="formLoading" ref="dataForm" :rules="rules" :model="dataForm" :size="size" :inline="true" :disabled="isEdit" @keyup.enter.native="dataFormSubmit()" label-width="90px">
    <el-form-item label="模板名称:" prop="templateName">
      <el-input style="width: 240px;" v-model="dataForm.templateName" placeholder="请填入便于分辨的模板名称"></el-input>
    </el-form-item>
    <el-form-item label="有效日期:" prop="validityDate">
      <el-date-picker v-model="dataForm.validityDate" type="daterange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至"></el-date-picker>
    </el-form-item>
    <el-form-item v-if="!isEdit">
      <el-button type="primary" @click="submit()">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {createOrUpdateTemplate} from "@/api/roadPass";
export default {
  name: "",
  components: {},
  data() {
    return {
      size: "small",
      formLoading: false,
      dataForm: {
        templateName: "",
        validityDate: [],
      },
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
          { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
        ],
        validityDate: [
          { type: "array", required: true, message: "请选择有效日期", trigger: "change" },
        ],
      },

      // 
      templateId: "",
      templateData: {},
    };
  },
  computed: {
    isEdit() {
      return !!this.templateId;
    },
  },
  created() { 
    this.init();
  },
  methods: {
    init() { 
      this.templateId = '';
      this.templateData = {};
      this.resetForm();
    },
    setData(data){
      this.templateId = data.id;
      this.dataForm.templateName = data.templateName || "";
      let date = data.endDate ? [data.startDate, data.endDate] : [];
      this.$set(this.dataForm, "validityDate", date);
    },

    submit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let params = Object.assign({}, this.dataForm);
          if (this.dataForm.validityDate && this.dataForm.validityDate.length === 2) {
            params.startDate = this.dataForm.validityDate[0];
            params.endDate = this.dataForm.validityDate[1];
          }
          delete params.validityDate;
          createOrUpdateTemplate(params).then((res) => {
            if (res.code == 0 && res.data) {
              this.$message.success("模板创建成功");
              this.templateId = res.data.id;
              this.$set(this,"templateData",res.data);
              this.$emit("templateData",res.data);
            } else {
              this.$message.error(res.message || "保存失败");
              
            }
            
          }).catch((error) => {
            console.error("保存模板失败:", error);
          });
        } else {
          return false;
        }
      });
    },
    resetForm() { 
      if(this.$refs.dataForm){
        this.$refs.dataForm.resetFields();
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.form-container {
  padding-top: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  margin-bottom: 20px;
}
</style>