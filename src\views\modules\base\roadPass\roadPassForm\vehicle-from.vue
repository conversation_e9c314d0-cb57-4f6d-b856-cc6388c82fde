<template>
  <el-card class="box-card" v-loading="formLoading">
    <div slot="header" class="header-box">
      <div>车辆列表</div>
      <div v-if="!isView">
        <el-select style="width: 220px" v-model="vecNo" :size="size" filterable remote reserve-keyword placeholder="请输入车牌号进行查询或添加" :remote-method="remoteMethod" :loading="vecNoLoading">
          <el-option v-for="item in vecNoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button style="margin-left: 10px" :size="size" :disabled="!vecNo" type="primary" icon="el-icon-plus" @click="addHandle()">添加</el-button>
      </div>
    </div>
    <el-table class="el-table" :data="dataList" border style="width: 100%" v-loading="listLoading" height="260" :size="size">
      <el-table-column type="index" label="序号" align="center" width="100"></el-table-column>
      <el-table-column label="车牌号" prop="plateNo" min-width="140"></el-table-column>
      <el-table-column label="操作" width="150" align="center" v-if="!isView">
        <template slot-scope="scope">
          <el-button type="text" title="删除" @click="deleteHandle(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script>
import { getFuzzyTracCd, getVecList } from "@/api/vec";
import * as $http from "@/api/roadPass";
export default {
  name: "VehicleFrom",
  components: {},
  props: {
    templateId: {
      type: String,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      size: "small",
      formLoading: false,
      listLoading: false,
      dataList: [],

      vecNo: "",
      vecNoLoading: false,
      vecNoOptions: [],
    };
  },
  methods: {
    remoteMethod(vecNo) {
      if (vecNo !== "" && vecNo.length > 2) {
        this.vecNoLoading = true;
        let params = this.getParams(vecNo);
        getVecList(params)
          .then(res => {
            let list = res.page.list;
            if (res.code == 0 && list) {
              this.vecNoOptions = list.map(item => ({ label: item.vecNo, value: item.vecNo }));
            } else {
              this.$message.error(res.msg || "获取车辆列表失败");
            }
            this.vecNoLoading = false;
          })
          .catch(error => {
            this.vecNoLoading = false;
            console.error("获取车辆列表失败>>", error);
          });
      }
    },
    getParams(vecNo) {
      let cat_cd_new = { field: "cat_cd_new", op: "eq", data: "牵引车" };
      let lic_approve_result_cd = { field: "lic_approve_result_cd", op: "nao", data: "1" };
      let vec_no = { field: "vec_no", op: "cn", data: vecNo };
      let filters = {
        groupOp: "AND",
        rules: [cat_cd_new, lic_approve_result_cd, vec_no],
      };
      return {
        filters: filters,
        page: 1,
        limit: 1000,
      };
    },
    // remoteMethod(vecNo){
    //   if(vecNo !== "" && vecNo.length > 2){
    //     this.vecNoLoading = true;
    //     getFuzzyTracCd("1180", vecNo).then((res) => {
    //       if (res.code == 0 && res.data) {
    //         this.vecNoOptions = res.data.map(item => ({ label: item.name, value: item.name }));
    //       } else {
    //         this.$message.error(res.msg || "获取车辆列表失败");
    //       }
    //       this.vecNoLoading = false;
    //     }).catch((error) => {
    //       this.vecNoLoading = false;
    //       console.error("获取车辆列表失败>>",error);
    //     });
    //   } else {
    //     this.vecNoOptions = [];
    //   }
    // },
    addHandle() {
      if (!this.vecNo) {
        this.$message.error("请输入车牌号");
        return;
      }
      let confirmText = `确定添加车辆【${this.vecNo}】吗？`;
      this.$confirm(confirmText, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            templateId: this.templateId,
            plateNo: this.vecNo,
          };
          $http.addVehicle(params).then(res => {
            if (res.code === 0) {
              this.$message({
                message: "添加车辆成功",
                type: "success",
                duration: 1500,
              });
              this.$set(this, "dataList", [...this.dataList, res.data]);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    // 删除
    deleteHandle(row) {
      let confirmText = `确定删除车辆【${row.plateNo}】吗？`;
      this.$confirm(confirmText, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: row.id,
            templateId: row.templateId,
          };
          $http.deleteVehicle(params).then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功",
                type: "success",
                duration: 1500,
              });
              let index = this.dataList.findIndex(item => item.id == row.id);
              this.dataList.splice(index, 1);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    setData(data) {
      let dataList = data.vehicles || [];
      this.$set(this, "dataList", dataList);
    },
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  &::v-deep .el-card__header {
    padding: 15px 20px;
  }
  .header-box {
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 60%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>