<template>
    <div>
        <div style="display:flex;">
            <steps style="flex:1;" :list="getstepList(rtePlan)" :active="getActive(rtePlan)"  :invalid="getInvalid(rtePlan)"></steps>
            <el-button size="small" style="flex:0 0 32px;height:32px;position: relative;top: 8px;" type="primary" icon="el-icon-refresh" circle @click="refreshRtePlanStatus(rtePlan)"></el-button>
        </div>
        <div class="statusInfo" style="padding: 10px;font-size: 16px;">
            <el-row :gutter="10">
                <el-col :span="6">
                    <el-card>
                        <div style="font-weight: bold;" :class="rtePlan.transportationStatusCode >= 1 ?'statusActive':''">发车</div>
                        <div>实时位置：{{rtePlan.goAddr}}</div>
                        <div>{{rtePlan.goTm?'备注：发车提货':'备注：'}}</div>
                        <div>操作人：<span v-if="rtePlan.transportationStatusCode>= 1">{{rtePlan.dvNm}}</span></div>
                        <div>操作时间：{{rtePlan.goTm}}</div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card>
                        <div style="font-weight: bold;" :class="rtePlan.transportationStatusCode >= 2 ?'statusActive':''">装货</div>
                        <div>实时位置：{{rtePlan.loadAddr}}</div>
                        <div>{{rtePlan.loadTm?'备注：装货启运':'备注：'}}</div>
                        <div>操作人：<span v-if="rtePlan.transportationStatusCode>= 2">{{rtePlan.dvNm}}</span></div>
                        <div>操作时间：{{rtePlan.loadTm}}</div>
<!--                        <div>实名认证：</div>-->
                        <div>装货重量：{{rtePlan.loadActQty}}<span v-if="rtePlan.loadActQty">吨</span></div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card>
                        <div style="font-weight: bold;" :class="rtePlan.transportationStatusCode >= 3 ?'statusActive':''">卸货</div>
                        <div>实时位置：{{rtePlan.unloadAddr}}</div>
                        <div>{{rtePlan.unloadTm?'备注：卸货':'备注：'}}</div>
                        <div>操作人：<span v-if="rtePlan.transportationStatusCode>= 3">{{rtePlan.dvNm}}</span></div>
                        <div>操作时间：{{rtePlan.unloadTm}}</div>
<!--                        <div>实名认证：</div>-->
                        <div>卸货重量：{{rtePlan.unloadActQty}}<span v-if="rtePlan.unloadActQty">吨</span></div>
                    </el-card>
                </el-col>
                <el-col v-if="rtePlan.errBackStatus == 211" :span="6">
                    <el-card>
                        <div style="font-weight: bold;color:#ff0000">异常结束</div>
                        <div>实时位置：{{rtePlan.errBackAddr}}</div>
                        <div>备注：异常结束</div>
                        <div>操作人：<span v-if="rtePlan.transportationStatusCode == -1">{{rtePlan.dvNm}}</span></div>
                        <div>操作时间：{{rtePlan.errBackTm}}</div>
                    </el-card>
                </el-col>
                <el-col v-else :span="6">
                    <el-card>
                        <div style="font-weight: bold;" :class="rtePlan.transportationStatusCode >= 4 ?'statusActive':''">结束</div>
                        <div>实时位置：{{rtePlan.backAddr}}</div>
                        <div>{{rtePlan.backTm?'备注：结束':'备注：'}}</div>
                        <div>操作人：<span v-if="rtePlan.transportationStatusCode>= 4">{{rtePlan.dvNm}}</span></div>
                        <div>操作时间：{{rtePlan.backTm}}</div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
    import Steps from "@/components/Steps";
    import * as $http from "@/api/rtePlan";

    export default {
        name: "four-states",
        components: {
            Steps,
        },
        data() {
            return {
                rtePlan:{}
            };
        },
        props:{
            originalRtePlan:{
                type:Object,
                default: {}
            },
        },
        created() {
            this.getTransportationStatus()
        },
        watch: {
            originalRtePlan: function () {
                this.getTransportationStatus()
            },
        },
        methods: {
            //获取电子运单4状态
            getTransportationStatus(){
                let rtePlan = Object.assign({}, this.originalRtePlan);
                if(rtePlan.backTm){
                    rtePlan.transportationStatus = '结束'
                    rtePlan.transportationStatusCode = 4
                    rtePlan.operateTm = rtePlan.backTm
                }else if(rtePlan.unloadTm){
                    rtePlan.transportationStatus='卸货'
                    rtePlan.transportationStatusCode = 3
                    rtePlan.operateTm = rtePlan.unloadTm
                }else if(rtePlan.loadTm){
                    rtePlan.transportationStatus='装货'
                    rtePlan.transportationStatusCode = 2
                    rtePlan.operateTm = rtePlan.loadTm
                }else if(rtePlan.goTm){
                    rtePlan.transportationStatus='发车'
                    rtePlan.transportationStatusCode = 1
                    rtePlan.operateTm = rtePlan.goTm
                }else{
                    rtePlan.transportationStatus='无'
                    rtePlan.transportationStatusCode = 0
                    rtePlan.operateTm = ''
                }
                if (rtePlan.errBackStatus == 211){
                    rtePlan.transportationStatus='异常结束'
                    rtePlan.transportationStatusCode = -1
                    rtePlan.operateTm = rtePlan.errBackTm
                }
                this.rtePlan = rtePlan
            },
            //刷新运单四状态
            refreshRtePlanStatus(rtePlan){
                $http.getRtePlanStatus(rtePlan.cd).then(({data:res}) => {
                    res.code = 0
                    if (res.code === 0){
                        this.$emit("refreshRtePlan",rtePlan.argmtPk)
                    }
                }).catch(error => {
                    console.log(error);
                });
            },
            //获取步骤条时间信息
            getstepList(item){
                let stepList = [{
                    title:"1",
                    icon:"fa"
                },{
                    title:"2",
                    icon:"zhuang"
                },{
                    title:"3",
                    icon:"xie"
                },{
                    title:"4",
                    icon:"hui"
                }]

                // 发
                stepList[0].title = item.goTm || '';
                // 装
                stepList[1].title = item.loadTm || '';
                // 卸
                stepList[2].title = item.unloadTm || '';
                //回
                stepList[3].title = item.backTm || '';
                //异
                if(item.errBackStatus == 211){
                    stepList[3].icon = "yi"
                    stepList[3].title = item.errBackTm || '';
                }
                return stepList;
            },
            //获取步骤条进度
            getActive(item){
                if(item.backTm){
                    return 4;
                }else if( item.unloadTm ){
                    return 3
                }else if( item.loadTm ){
                    return 2
                }else if(item.goTm){
                    return 1;
                }
            },
            //获取步骤条运单异常状态
            getInvalid(item){
                if(item.errBackStatus == 211){
                    return true
                }
            },
        }
    }
</script>

<style scoped>
    .statusActive{
        color: rgb(37, 37, 238);
    }
    .statusInfo div{
        margin-bottom: 5px;
    }
</style>
