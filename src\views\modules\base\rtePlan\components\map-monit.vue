<template>
  <div v-loading="loading" class="app-main-content" style="position:relative;margin:0;padding:0;">
    <div ref="mapNode" :style="{ height: modelHeight+'px'}" class="map"/>
  </div>
</template>

<script>
import * as $http from '@/api/mapMonit'
export default {
  name: 'MapMonit',
  props: {
    modelHeight: {
      type: Number,
      default: 200
    },
    historyTraceColor: {
      type: String,
      default: '#d00'
    },
    routePlanColor: {
      type: String,
      default: '#3c64ff'
    }
  },
  data() {
    return {
      loading: false,
      map: null,
      mapNodeLoading: null,
      gpsRunTrace: { // 有gsp的时间轨道长度
        left: 0,
        width: 0
      },
      vecIcon: new BMap.Icon('static/img/monitor-img/_icon.png', new BMap.Size(73, 73))
    }
  },
  methods: {
    init() {

    },

    // 显示位置定位
    showLocation(item, isClearOverlay) {
      this._initMap(isClearOverlay)
      this._showLocation(item.lng, item.lat)
    },

    // 显示历史轨迹
    showHistroyTrace(traceData, date, isClearOverlay) {
      this._initMap(isClearOverlay)
      this._createVecGpsTrace(traceData, date)
    },

    // 根据车牌号和时间显示车辆历史轨迹
    showHistoryTraceByVecNo(vecNo, date, isClearOverlay) {
      this._initMap(isClearOverlay)
      const _this = this
      this.loading = true
      $http.getVecGpsTraceByDate({
        t: date,
        v: vecNo
      }).then((res) => {
        _this.loading = false
        if (res.code === 0) {
          if (res.data.length === 0) {
            _this.$message.warning(`${vecNo}，${date}日无轨迹信息！`)
          } else {
            _this._createVecGpsTrace(res.data, date) // 绘制车辆历史轨迹
          }
        } else {
          _this.$message.error(res.msg)
        }
      }).catch(error => {
        _this.loading = false
        console.log(error)
      })
    },

    // 规划线路
    showRoutePlan(startItem, endItem, isClearOverlay) {
      const startPoint = new BMap.Point(startItem.lng, startItem.lat) // 起点
      const endPoint = new BMap.Point(endItem.lng, endItem.lat) // 终点
      this._initMap(isClearOverlay)
      this._createRoutePlanning(startPoint, endPoint)
    },

    // 线路规划
    /* global BMAP_STATUS_SUCCESS */
    showRoutePlanByPointNm(startNm, endNm) {
      // const localSearch = new BMap.LocalSearch()
      // const startPoint = localSearch.search(startNm) // 起点
      // const endPoint = localSearch.search(endNm) // 起点
      // localSearch.setSearchCompleteCallback(function(rs) {
      //   if (localSearch.getStatus() === BMAP_STATUS_SUCCESS) {
      //     const poi = rs.getPoi(0)
      //     // for (var i = 0; i < rs.getCurrentNumPois(); i++) {
      //     //   var poi = rs.getPoi(i)
      //     // }
      //   }
      // })
    },

    /** ************************* 以下默认为内部函数 *************************************** */

    // loading effect
    _getMapNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: 'el-icon-loading'
      })
      return loading
    },

    _getTransLoading() { // 无背景色
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: 'el-icon-loading',
        background: 'transparent'
      })
      return loading
    },

    // map init
    /* global BMAP_NORMAL_MAP,BMAP_SATELLITE_MAP*/
    _initMap(isClearOverlay) {
      if (!this.map) {
        this.map = new BMap.Map(this.$refs.mapNode, { enableMapClick: false })
        const point = new BMap.Point(121.66386, 30.001186)
        this.map.centerAndZoom(point, 13)
        this.map.enableScrollWheelZoom()
        this.map.addControl(new BMap.MapTypeControl({ mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP] }))
        this.map.addEventListener('click', function(e) {
          console.log(e.point.lng + ', ' + e.point.lat)
        })
      }
      if (isClearOverlay) {
        this.map.removeOverlay() // 清空覆盖物
      }
    },

    // load zhenhai fence
    loadFence() {
      // const boundaries = ['121.62985, 30.074127;121.614342,30.049695;121.603742, 30.04447;121.582121, 30.045448;121.554009, 30.062082;121.538023, 30.060881;121.52901, 30.066244;121.504026, 30.058271;121.493747, 30.060368;121.490375, 30.047058;121.486419, 30.045994;121.483403, 30.038025;121.473141, 30.036884;121.473363, 30.034586;121.49254, 30.005923;121.489436, 29.993577;121.517545, 29.986929;121.533786, 29.990671;121.549748, 29.984102;121.557824, 29.976318;121.572282, 29.965128;121.574176, 29.96115;121.588676, 29.948288;121.608201, 29.945618;121.608245, 29.936831;121.688195, 29.926089;121.697782, 29.934764;121.707198, 29.951321;121.720699, 29.950687;121.732525, 29.954658;121.73868, 29.969907;121.755889, 29.977719;121.764236, 29.980001;121.772088, 29.979603;121.799044, 30.000394;121.794039, 30.003855;121.788597, 30.034502;121.754832, 30.068651;121.731593, 30.113779;121.68639, 30.098785;121.62985, 30.074127']

      // for (var i = 0; i < boundaries.length; i++) {
      //   var poly = new BMap.Polyline(boundaries[i], {
      //     strokeWeight: 3,
      //     strokeColor: '#FF6919',
      //     strokeOpacity: 0.8,
      //     strokeStyle: 'dashed'
      //   })
      //   this.map.addOverlay(poly)
      //   poly.disableMassClear()
      // }
    },

    // 显示定位信息
    _showLocation(lng, lat) {
      const point = new BMap.Point(lng, lat)
      // const icon = new BMap.Icon('imageSrc', new BMap.Size(24, 24))
      const marker = new BMap.Marker(point)
      // marker.setRotation(item.direction)
      this.map.centerAndZoom(point, 15)
      this.map.addOverlay(marker)
      // marker.setAnimation(BMAP_ANIMATION_BOUNCE) // 跳动的动画
    },

    // 绘制历史轨迹
    _createVecGpsTrace(data, searchDate) {
      // 清理老的轨迹数据
      this.progressLeft = 0

      this.runpartList = [] // 清空车辆行驶时间轴
      this.gpsRunTrace.left = 0 // 清空有gps时间轨道
      this.gpsRunTrace.width = 0

      clearInterval(this.timelineTask) // 清除播放历史轨迹的定时器
      this.timelineStatus = 'start'

      // 当前日期时间戳
      const todayTime = Date.parse(new Date(searchDate + ' 00:00:00')) / 1000

      // 默认gsp时间间隔内数据都为0
      let startTime = data[0].updateTimeStamp / 1000
      let endTime = data[data.length - 1].updateTimeStamp / 1000
      // 把时间转换为位置
      let startPosition = Math.round((startTime - todayTime) / 120)
      const endPosition = Math.round((endTime - todayTime) / 120)
      let widthPosition = endPosition - startPosition
      this.$set(this.gpsRunTrace, 'left', startPosition)
      this.$set(this.gpsRunTrace, 'width', widthPosition)

      // 对每段进行渲染
      for (var i = 0; i < data.length - 1; i++) {
        if (data[i].speed === 0) {
          continue
        }

        let j = i
        let minSpeed
        let maxSpeed
        let title
        minSpeed = maxSpeed = data[i].speed
        while (data[j + 1] && data[j + 1].speed > 0) { // 多个具有速度的时间段进行叠加绘制
          if (minSpeed > data[j + 1].speed) {
            minSpeed = data[j + 1].speed
          }
          if (maxSpeed < data[j + 1].speed) {
            maxSpeed = data[j + 1].speed
          }
          j++
        }
        if (i === j) {
          startTime = data[i].updateTimeStamp / 1000
          endTime = data[i + 1].updateTimeStamp / 1000
          title = `${data[i].updateTime}，速度为${data[i].speed}km/h`
        } else {
          startTime = data[i].updateTimeStamp / 1000
          endTime = data[j].updateTimeStamp / 1000
          title = `"${data[i].updateTime}至${data[j].updateTime}"时间内，速度为${minSpeed}km/h ~ ${maxSpeed}km/h`
        }
        // 把时间转换为位置
        startPosition = Math.round((startTime - todayTime) / 120)
        widthPosition = ((endTime - startTime) / 120).toFixed(2)

        // 只绘制具有速度的片段
        this.runpartList.push({ left: startPosition, width: widthPosition, title: title })

        i = j
      }
      // 绘制车辆在地图上的行驶路线
      this._drawDrivingTrace(data)
    },

    // 绘制车辆在地图上的行驶路线
    _drawDrivingTrace(data) {
      const points = []
      data.forEach(item => {
        points.push(item.lonBd + ',' + item.latBd)
      })

      const track = points.join(';')
      let pointArray = []
      const boundaries = [track]
      for (let i = 0; i < boundaries.length; i++) {
        const ply = new BMap.Polyline(boundaries[i], {
          strokeColor: this.historyTraceColor,
          strokeOpacity: 0.7,
          strokeWeight: 5
        }) // 建立多边形覆盖物

        this.map.addOverlay(ply) // 添加覆盖物
        pointArray = pointArray.concat(ply.getPath())
      }
      this.map.setViewport(pointArray)
      // 添加起始点
      if (data.length > 0) {
        // this.vecMarker = new BMap.Marker(new BMap.Point(data[0].lonBd, data[0].latBd), { icon: this.vecIcon })
        // this.map.addOverlay(this.vecMarker)
        // this.map.setCenter(new BMap.Point(data[0].lonBd, data[0].latBd)) // 设置起始点为中心

        this.map.addOverlay(new BMap.Label('起点', {
          position: new BMap.Point(data[0].lonBd, data[0].latBd), // 指定文本标注所在的地理位置
          offset: new BMap.Size(0, 0) // 设置文本偏移量
        }))
        const len = data.length - 1
        this.map.addOverlay(new BMap.Label('终点', {
          position: new BMap.Point(data[len].lonBd, data[len].latBd), // 指定文本标注所在的地理位置
          offset: new BMap.Size(0, 0) // 设置文本偏移量
        }))
      }
    },

    // 规划线路
    _createRoutePlanning(startPoint, endPoint) {
      const _this = this
      const driving = new BMap.DrivingRoute(this.map) // 创建驾车实例
      driving.search(startPoint, endPoint) // 第一个驾车搜索
      driving.setSearchCompleteCallback(function() {
        var pts = driving.getResults().getPlan(0).getRoute(0).getPath() // 通过驾车实例，获得一系列点的数组
        var polyline = new BMap.Polyline(pts, {
          strokeColor: _this.routePlanColor,
          strokeOpacity: 0.7,
          strokeWeight: 5
        })
        const pointArray = [].concat(polyline.getPath())
        _this.map.setViewport(pointArray)
        _this.map.addOverlay(polyline)
      })
    }

  }
}
</script>

<style scoped>

</style>
