<template>
  <div class="print-panel">
    <div class="print-panel-header">
      <div class="panel-heading-content">
        <h3>危险货物道路运输电子运单(回单)</h3>
        <div v-show="rtePlan" style="font-weight: initial;font-size:15px;line-height: 30px;">
          <div :title="'回执单号'">{{ rtePlan.orderReceiptCd }}</div>
          <div :title="'完结时间'">完结时间：{{ rtePlan.receiptTm | FormatDate('yyyy年MM月dd日') }}</div>
          <!-- <div>
            <div class="badge badge-green">毒</div>
            <div class="badge badge-red">爆</div>
            <div class="badge badge-info">腐</div>
            <div class="badge badge-dark">制毒</div>
            <div class="badge badge-dark">制爆</div>
          </div> -->
        </div>
      </div>
      <div class="panel-heading-right no-print">
        <slot name="header-buttons"/>
      </div>
    </div>
    <div v-if="!rtePlan.orderReceiptCd"  class="print-panel-body">
      暂无数据
    </div>
    <div v-else class="print-panel-body">
      <!-- <div>运单编号：{{ rtePlan.cd }}， 提货单号：{{ rtePlan.shipOrdCustCd }}</div> -->
      <table class="custom-table">
        <tbody>
          <!-- <tr>
            <th rowspan="3" class="title">承运方</th>
            <th colspan="2">单位名称</th>
            <td colspan="1">{{ rtePlan.carrierNm }}</td>
            <th colspan="2">物流交换代码</th>
            <td colspan="1">{{ rtePlan.loginkUid }}</td>
          </tr>
          <tr>
            <th colspan="2">统一社会信用代码</th>
            <td colspan="1">{{ rtePlan.carrierUscCd }}</td>
            <th colspan="2">经营许可证号</th>
            <td colspan="1">{{ rtePlan.carrierUscCd }}</td>
          </tr>
          <tr>
            <th colspan="2">负责人</th>
            <td colspan="1">{{ rtePlan.erNm }}</td>
            <th colspan="2">负责人电话</th>
            <td colspan="1">{{ rtePlan.erMob }}</td>
          </tr>
          <tr>
            <th rowspan="4" class="title">车辆信息</th>
            <th rowspan="3" class="subtitle">牵引车</th>
            <th colspan="1">车牌号(头)</th>
            <td colspan="1">
              <template v-if="rtePlan.tracPk">
                <router-link :to="'/base/vec/info/'+rtePlan.tracPk">
                  <span>{{ rtePlan.tracCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.tracCd }}<span class="error-tips">（牵引车未备案或备案失败）</span></span>
              </template>
            </td>
            <th rowspan="3" class="subtitle">挂车</th>
            <th colspan="1">车牌号(挂)</th>
            <td colspan="1">
              <template v-if="rtePlan.traiPk">
                <router-link :to="'/base/vec/info/'+rtePlan.traiPk">
                  <span>{{ rtePlan.traiCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span>{{ rtePlan.traiCd }}<span class="error-tips">（挂车未备案或备案失败）</span></span>
              </template>
            </td>
          </tr>
          <tr>
            <th colspan="1" title="牵引车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlan.tracOpraLicNo }}</td>
            <th colspan="1" title="挂车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlan.traiOpraLicNo }}</td>
          </tr>
          <tr>
            <th colspan="1" title="牵引车质量">牵引车质量（KG）</th>
            <td colspan="1">{{ rtePlan.tracWeight }}</td>
            <th colspan="1" title="挂车核准质量">挂车核准质量（KG）</th>
            <td colspan="1">{{ rtePlan.traiWeight }}</td>
          </tr>
          <tr>
            <th colspan="2">罐体编号</th>
            <td colspan="1">{{ rtePlan.tankNum }}</td>
            <th colspan="2">罐体容积（m<sup>3</sup>）</th>
            <td colspan="1">{{ rtePlan.tankVolume }}</td>
          </tr>
          <tr>
            <th rowspan="3" class="title">人员信息</th>
            <th rowspan="3" class="subtitle">驾驶员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div v-if="rtePlan.dvPk" :title="rtePlan.dvNm" class="detail-area">
                <router-link :to="'/base/pers/info/'+rtePlan.dvPk">
                  <span>{{ rtePlan.dvNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.dvNm" class="detail-area">
                <span>{{ rtePlan.dvNm }}<span class="error-tips">（驾驶员未备案或备案失败）</span></span>
              </div>
            </td>
            <th rowspan="3" class="subtitle">押运员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div v-if="rtePlan.scPk" :title="rtePlan.scNm" class="detail-area">
                <router-link :to="'/base/pers/info/'+rtePlan.scPk">
                  <span>{{ rtePlan.scNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlan.scNm" class="detail-area">
                <span>{{ rtePlan.scNm }}<span class="error-tips">（押运员未备案或备案失败）</span></span>
              </div>
            </td>
          </tr>
          <tr>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlan.dvCd }}</td>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlan.scCd }}</td>
          </tr>
          <tr>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlan.dvMob }}</td>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlan.scMob }}</td>
          </tr>
          <tr>
            <th rowspan="9" class="title">货物信息</th>
            <th rowspan="5" class="subtitle">装货方</th>
            <th colspan="1">装货企业</th>
            <td colspan="1">{{ rtePlan.csnorWhseAddr }}</td>
            <th rowspan="5" class="subtitle">卸货方</th>
            <th colspan="1">卸货企业</th>
            <td colspan="1">{{ rtePlan.csneeWhseAddr }}</td>
          </tr>
          <tr>
            <th colspan="1">装货地区</th>
            <td colspan="1">{{ rtePlan.csnorWhseDist }}</td>
            <th colspan="1">卸货地区</th>
            <td colspan="1">{{ rtePlan.csneeWhseDist }}</td>
          </tr>
          <tr>
            <th colspan="1">装货地编码</th>
            <td colspan="1">{{ rtePlan.csnorWhseDistCd }}</td>
            <th colspan="1">卸货地编码</th>
            <td colspan="1">{{ rtePlan.csneeWhseDistCd }}</td>
          </tr>
          <tr>
            <th colspan="1">装货地联系人</th>
            <td colspan="1">{{ rtePlan.csnorWhseCt }}</td>
            <th colspan="1">卸货地联系人</th>
            <td colspan="1">{{ rtePlan.csneeWhseCt }}</td>
          </tr>
          <tr>
            <th colspan="1">联系方式</th>
            <td colspan="1">{{ rtePlan.csnorWhseTel }}</td>
            <th colspan="1">联系方式</th>
            <td colspan="1">{{ rtePlan.csneeWhseTel }}</td>
          </tr> -->

          <tr>
            <th rowspan="2" class="title">收货方</th>
            <th colspan="2">收货人</th>
            <td colspan="1">{{ rtePlan.receiptEntpNm }}</td>
            <th colspan="2">收货地点</th>
            <td colspan="1">{{ rtePlan.receiptAddress }}</td>
          </tr>
          <tr>
            <th colspan="2">联系人名称</th>
            <td colspan="1">{{ rtePlan.receiptContactNm }}</td>
            <th colspan="2">联系电话</th>
            <td colspan="1">{{ rtePlan.receiptContactTel }}</td>
          </tr>

          <tr style="border-top: 2px solid #ebeef5;">
            <th rowspan="2" class="title">货物信息</th>
            <th colspan="2">货品名称</th>
            <td colspan="1">
              <router-link v-if="rtePlan.enchPk" :to="'/base/ench/info/'+rtePlan.enchPk">
                <span>{{ rtePlan.goodsNm }}</span>
              </router-link>
              <span v-else>{{ rtePlan.goodsNm }}<span class="error-tips">（货品未备案或备案失败）</span></span>
            </td>
            <th colspan="2">装货数量（吨）</th>
            <td colspan="1">
              {{ rtePlan.loadQty }}
            </td>
          </tr>
          <tr>
            <th colspan="2">危险货物类别</th>
            <td colspan="1">{{ rtePlan.gb }}</td>
            <th colspan="2">UN号</th>
            <td colspan="1">{{ rtePlan.un }}</td>
          </tr>
          <!-- <tr>
            <th colspan="6">应急救援资料</th>
          </tr> 
          <tr>
            <td colspan="6" v-html="rtePlan.contPlan"/>
          </tr>-->
          <tr>
            <th rowspan="6" class="title">轨迹分析</th>
            <th colspan="6">装卸货轨迹 (实际轨迹(红色) / 计划轨迹(蓝色))</th>
          </tr>
          <tr>
            <td ref="mapMonitWape" colspan="6">
              <map-monit ref="mapMonit" :model-height="mapMonitWapeHeight"/>
            </td>
          </tr>
          <tr>
            <th colspan="2">行驶里程</th>
            <td colspan="1">暂无数据</td>
            <th colspan="2">总耗时</th>
            <td colspan="1">暂无数据</td>
          </tr>
          <tr>
            <th colspan="2">行驶时间</th>
            <td colspan="1">暂无数据</td>
            <th colspan="2">停车时间</th>
            <td colspan="1">暂无数据</td>
          </tr>
          <tr>
            <th colspan="2">最高速度</th>
            <td colspan="1">暂无数据</td>
            <th colspan="2">平均速度</th>
            <td colspan="1">暂无数据</td>
          </tr>
          <tr>
            <th colspan="1" class="subtitle">违章信息</th>
            <td colspan="5">暂无数据</td>
          </tr>
        </tbody>
      </table>
      <!-- <div class="text-center">
        <h3>使用官方APP扫描二维码核验详情</h3>
        <div ref="qrcode" align="center"/>
      </div> -->
    </div>
</div></template>

<script>
import mapMonit from './map-monit'
import QRCode from 'qrcodejs2'
import * as $http from '@/api/rtePlan'
export default {
  name: 'ReceiptOrder',
  components: {
    mapMonit
  },
  props: {
    rtePlan: {
      type: Object,
      default() {
        return {}
      }
    },
    goodsList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      mapMonitWapeHeight: 400,
      hasRender: false
    }
  },
  watch: {
    rtePlan: {
      deep: true,
      handler(val, oldval) {
        this.render()
      }
    }
  },
  methods: {
    render(isRendering) {
      this.$nextTick(() => {
        if (isRendering) { // 重新渲染
          this.hasRender = false
        }
        if (!this.hasRender) {
          if (this.rtePlan) {
            if (this.rtePlan.argmtPk) {
              this.initQRCode(this.rtePlan.argmtPk)
            }
          }
          this.hasRender = true
        }
        if(this.rtePlan.orderReceiptCd)
          this.initMapMonit(this.rtePlan.tracCd, this.rtePlan.vecDespTm);
        // this.mapMonitWapeHeight = this.$refs.mapMonitWape.offsetHeight
      })
    },
    initById(id) {
      const _this = this
      if (id) {
        $http.getRtePlanByPk(id).then(response => {
          if (response && response.code === 0) {
            _this.rtePlan = response.data
            _this.goodsList = response.items
            //_this.initQRCode(response.data.argmtPk) // 初始化二维码
          } else {
            _this.$message({
              message: response.msg,
              type: 'error'
            })
          }
        })
          .catch(error => {
            console.log(error)
          })
      }
    },
    initQRCode(argmtPk) {
      this.createdQRCode(argmtPk)
    },
    createdQRCode(argmtPk) {
      if (argmtPk) {
        $http
          .getQRCode(argmtPk)
          .then(res => {
            if (res) {
              new QRCode(this.$refs.qrcode, {
                text: res,
                width: 200,
                height: 200,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.L
              })
              this.$refs.qrcode.title = ''
            }
          })
          .catch(error => {
            console.log(error)
          })
      } else {
        this.$message.error('argmtPk为空，二维码生成失败')
      }
    },
    // 显示历史轨迹
    initMapMonit(vecNo, date) {
      this.$refs.mapMonit.showHistoryTraceByVecNo(vecNo, date, true)
    }
  }
}
</script>

<style scoped>

</style>
