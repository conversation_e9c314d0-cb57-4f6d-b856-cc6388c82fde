<template>
  <div class="print-panel">
    <div class="print-panel-header">
      <div class="panel-heading-content">
        <h3>{{ title }}</h3>
      </div>
    </div>
    <div v-if="!(orderObj && orderObj.argmtWtCd)" class="print-panel-body">
      暂无数据
    </div>
    <div v-else class="print-panel-body">
      <table class="custom-table">
        <tbody>
          <tr style="border-top: 1px solid #dee2e6">
            <th colspan="2">编号信息</th>
            <th colspan="1">卸货单号</th>
            <td colspan="1">{{ orderObj.argmtWtCd }}</td>
            <th colspan="2">运单编号</th>
            <td colspan="1">{{ rtePlanObj.cd }}</td>
          </tr>
          <tr>
            <th colspan="2">托运信息</th>
            <th colspan="1">托运人名称</th>
            <td colspan="1">{{ rtePlanObj.consignorAddr }}</td>
            <th colspan="2">托运人联系电话</th>
            <td colspan="1">{{ rtePlanObj.consignorTel }}</td>
          </tr>
          <tr>
            <th rowspan="3" class="title">装货信息</th>
            <th colspan="2">装货人名称</th>
            <td colspan="1">{{ orderObj.csnorNmCn }}</td>
            <th colspan="2">联系人</th>
            <td colspan="1">
              {{ rtePlanObj.csnorWhseCt }}
              <span v-show="rtePlanObj.csnorWhseTel"
                >（{{ rtePlanObj.csnorWhseTel }}）</span
              >
            </td>
          </tr>
          <tr>
            <th colspan="2">货物名称</th>
            <td colspan="1">
              <router-link
                v-if="orderObj.prodPk"
                :to="'/base/chemica/info/' + orderObj.prodPk"
              >
                <span>{{ orderObj.goodsNm }}</span>
              </router-link>
              <span v-else
                >{{ orderObj.goodsNm
                }}<span class="error-tips">（货物未备案或备案失败）</span></span
              >
            </td>
            <th colspan="2">UN编码</th>
            <td colspan="1">{{ orderObj.un }}</td>
          </tr>
          <tr>
            <th colspan="2">货物类别</th>
            <td colspan="1">{{ orderObj.goodsCat }}</td>
            <th colspan="2">货物净重（Kg)</th>
            <td colspan="1">{{ orderObj.loadQty }}</td>
            <!-- <td colspan="1">{{ orderObj.weighIn-orderObj.weighOut }}</td> -->
          </tr>
          <tr>
            <th rowspan="8" class="title">承运信息</th>
            <th colspan="2">单位名称</th>
            <td colspan="1">{{ rtePlanObj.carrierNm }}</td>
            <th colspan="2">负责人</th>
            <td colspan="1">
              {{ rtePlanObj.erNm
              }}<span v-show="rtePlanObj.erMob"
                >（{{ rtePlanObj.erMob }}）</span
              >
            </td>
          </tr>
          <tr>
            <th colspan="2">统一社会信用代码</th>
            <td colspan="1">{{ rtePlanObj.carrierUscCd }}</td>
            <th colspan="2">许可证号</th>
            <td colspan="1">{{ rtePlanObj.carrierBssCd }}</td>
          </tr>
          <tr>
            <th rowspan="2" class="subtitle">牵引车</th>
            <th colspan="1">车牌号(头)</th>
            <td colspan="1">
              <template v-if="rtePlanObj.tracPk">
                <router-link :to="'/base/vec/info/' + rtePlanObj.tracPk">
                  <span>{{ rtePlanObj.tracCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span
                  >{{ rtePlanObj.tracCd
                  }}<span class="error-tips"
                    >（牵引车未备案或备案失败）</span
                  ></span
                >
              </template>
            </td>
            <th rowspan="2" class="subtitle">挂车</th>
            <th colspan="1">车牌号(挂)</th>
            <td colspan="1">
              <template v-if="rtePlanObj.traiPk">
                <router-link :to="'/base/vec/info/' + rtePlanObj.traiPk">
                  <span>{{ rtePlanObj.traiCd }}</span>
                </router-link>
              </template>
              <template v-else>
                <span
                  >{{ rtePlanObj.traiCd
                  }}<span class="error-tips"
                    >（挂车未备案或备案失败）</span
                  ></span
                >
              </template>
            </td>
          </tr>
          <tr>
            <th colspan="1" title="牵引车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlanObj.tracOpraLicNo }}</td>
            <th colspan="1" title="挂车道路运输证号">道路运输证号</th>
            <td colspan="1">{{ rtePlanObj.traiOpraLicNo }}</td>
          </tr>
          <!-- <tr>
            <th colspan="1" title="牵引车质量">牵引车质量（KG）</th>
            <td colspan="1">{{ rtePlanObj.tracWeight }}</td>
            <th colspan="1" title="挂车核准质量">挂车核准质量（KG）</th>
            <td colspan="1">{{ rtePlanObj.traiWeight }}</td>
          </tr> -->
          <tr>
            <th colspan="2">罐体编号</th>
            <td colspan="1">{{ rtePlanObj.tankNum }}</td>
            <th colspan="2">罐体容积（m<sup>3</sup>）</th>
            <td colspan="1">{{ rtePlanObj.tankVolume }}</td>
          </tr>
          <tr>
            <!-- <th rowspan="3" class="title">人员信息</th> -->
            <th rowspan="3" class="subtitle">驾驶员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div
                v-if="rtePlanObj.dvPk"
                :title="rtePlanObj.dvNm"
                class="detail-area"
              >
                <router-link :to="'/base/pers/info/' + rtePlanObj.dvPk">
                  <span>{{ rtePlanObj.dvNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlanObj.dvNm" class="detail-area">
                <span
                  >{{ rtePlanObj.dvNm
                  }}<span class="error-tips"
                    >（驾驶员未备案或备案失败）</span
                  ></span
                >
              </div>
            </td>
            <th rowspan="3" class="subtitle">押运员</th>
            <th colspan="1">姓名</th>
            <td colspan="1">
              <div
                v-if="rtePlanObj.scPk"
                :title="rtePlanObj.scNm"
                class="detail-area"
              >
                <router-link :to="'/base/pers/info/' + rtePlanObj.scPk">
                  <span>{{ rtePlanObj.scNm }}</span>
                </router-link>
              </div>
              <div v-else :title="rtePlanObj.scNm" class="detail-area">
                <span
                  >{{ rtePlanObj.scNm
                  }}<span class="error-tips"
                    >（押运员未备案或备案失败）</span
                  ></span
                >
              </div>
            </td>
          </tr>
          <tr>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlanObj.dvCd }}</td>
            <th colspan="1">从业资格证</th>
            <td colspan="1">{{ rtePlanObj.scCd }}</td>
          </tr>
          <tr>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlanObj.dvMob }}</td>
            <th colspan="1">联系电话</th>
            <td colspan="1">{{ rtePlanObj.scMob }}</td>
          </tr>
          <tr>
            <th rowspan="9" class="title">收货信息</th>
            <th colspan="2">收货企业</th>
            <td colspan="1">{{ orderObj.csneeNmCn }}</td>
            <th colspan="2">收货地联系人</th>
            <td colspan="1">
              {{ rtePlanObj.csneeWhseCt }}
              <span v-show="rtePlanObj.csneeWhseTel"
                >（{{ rtePlanObj.csneeWhseTel }}）</span
              >
            </td>
          </tr>
          <tr>
            <th colspan="2">卸货操作员</th>
            <td colspan="1">{{ orderObj.oprNm }}</td>
            <th colspan="2">卸货时间</th>
            <td colspan="1">{{ orderObj.wtTm }}</td>
          </tr>
          <tr>
            <th colspan="2">货物名称</th>
            <td colspan="1">
              <router-link
                v-if="orderObj.prodPk"
                :to="'/base/chemica/info/' + orderObj.prodPk"
              >
                <span>{{ orderObj.goodsNm }}</span>
              </router-link>
              <span v-else
                >{{ orderObj.goodsNm
                }}<span class="error-tips">（货物未备案或备案失败）</span></span
              >
            </td>
            <th colspan="2">UN编码</th>
            <td colspan="1">{{ orderObj.un }}</td>
          </tr>
          <tr>
            <th colspan="2">货物类别</th>
            <td colspan="1">{{ orderObj.goodsCat }}</td>
            <th colspan="2">包装类别</th>
            <td colspan="1">{{ orderObj.pkgType }}</td>
          </tr>
          <tr>
            <th colspan="2">进厂称重（Kg)</th>
            <td colspan="1">{{ orderObj.weighIn }}</td>
            <th colspan="2">出厂称重（Kg)</th>
            <td colspan="1">{{ orderObj.weighOut }}</td>
          </tr>
          <tr>
            <th colspan="2">货物净重（Kg)</th>
            <td colspan="1">{{ orderObj.loadQty }}</td>
            <th colspan="2">货物损耗率%</th>
            <td colspan="1">
              {{
                (
                  ((rtePlanObj.loadQty * 1000 - orderObj.loadQty) /
                    (rtePlanObj.loadQty * 1000)) *
                  100
                ).toFixed(2)
              }}%
            </td>
          </tr>
          <tr>
            <th colspan="2">磅单照片</th>
            <td colspan="4">
              <filePreview
                v-if="orderObj.argmtUrl"
                :files="orderObj.argmtUrl"
                :imageShow="true"
              ></filePreview>
            </td>
          </tr>
          <tr>
            <th colspan="2">卸货前检查</th>
            <td colspan="4">
              <!-- <span
                style="cursor: pointer"
                v-if="orderObj.loadBefCheckId"
                class="view-dt-btn el-icon-search"
                @click="recordHandle(orderObj.loadBefCheckId, 1)"
                >查看详情</span
              > -->
              <span v-if="orderObj.chkBefCds">
                <el-button
                  v-for="(item, index) in orderObj.chkBefCds.split(',')"
                  :key="index"
                  type="text"
                  :title="item"
                  @click.native.prevent="recordHandle(item, 1)"
                  >{{ item }}</el-button
                >
              </span>
              <span v-else>无记录</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <el-dialog
      append-to-body
      :visible.sync="recordDialogVisible"
      :title="`查看产品${recordTitle}查验表单`"
      class="mod-loadRecord-bet-check"
      :close-on-click-modal="true"
      width="80%"
    >
      <record-info ref="recordInfo"></record-info>
    </el-dialog>
  </div>
</template>

<script>
import mapMonit from "./map-monit";
import filePreview from "@/components/FilesPreview";
import recordInfo from "@/views/modules/base/wb/record-info";
import * as $http from "@/api/rtePlan";
import * as $httpWb from "@/api/loadandunload";
export default {
  name: "LoadingOrder",
  components: {
    mapMonit,
    filePreview,
    recordInfo
  },
  props: {
    rtePlan: {
      type: Object,
      default() {
        return {};
      }
    },
    order: {
      type: Object,
      default() {
        return {};
      }
    },
    title: {
      type: String,
      default: "危险货物道路运输电子运单(卸货单)"
    }
  },
  data() {
    return {
      recordDialogVisible: false,
      recordTitle: "",
      rtePlanObj: this.rtePlan,
      orderObj: this.order
    };
  },
  watch: {
    rtePlan: {
      handler(val) {
        val && (this.rtePlanObj = val);
      },
      deep: true
    },
    order: {
      handler(val) {
        val && (this.orderObj = val);
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化
     * rteplanPk:运单pk
     * argmtWtPk:装货单pk
     */
    init(rteplanPk, argmtWtPk, year) {
      if (rteplanPk || argmtWtPk) {
        this.getRteplanData(rteplanPk);
        this.getOrderData(argmtWtPk, year);
      } else {
        this.$message({
          message: "很抱歉，装货单信息无法获取！",
          type: "error"
        });
      }
    },
    // 获取运单信息
    getRteplanData(rteplanPk) {
      const _this = this;
      $http
        .getRtePlanNewByPk(rteplanPk)
        .then(response => {
          if (response && response.code === 0) {
            _this.rtePlanObj = response.data || {};
          } else {
            _this.rtePlanObj = {};
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取卸货单信息
    getOrderData(argmtWtPk, year) {
      const _this = this;
      let api,param;
      
      if(year){
        api = $httpWb.getArgmwtHisInfo;
        param = {
          id:argmtWtPk,
          year:year
        }
      }else{
        api = $httpWb.argmwtInfo;
        param = argmtWtPk;
      }

      api(param)
        .then(response => {
          if (response && response.code === 0) {
            _this.orderObj = response.argmwt || response.data || {};
          } else {
            _this.orderObj = {};
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    recordHandle(recordId, type) {
      // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      if (!recordId) {
        this.$message.warning("暂无记录");
        return;
      }
      this.recordDialogVisible = true;
      switch (type) {
        case 0:
          this.recordTitle = "装货前";
          break;
        case 1:
          this.recordTitle = "卸货前";
          break;
        case 2:
          this.recordTitle = "卸货后";
          break;
        default:
          this.recordTitle = "";
          break;
      }
      this.$nextTick(() => {
        this.$refs.recordInfo && this.$refs.recordInfo.initByCd(recordId, type);
      });
    }
  }
};
</script>

<style scoped>
.only-print-show-row {
  display: none;
}
</style>
