<template>
  <div class="detail-container">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>&nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-body">
        <div class="steps-wape">
          <fourStates
            :originalRtePlan="rtePlan"
            @refreshRtePlan="rtePlanNewByPk"
          ></fourStates>
          <!--          <steps :list="stepsList" :active="stepsNum" />-->
        </div>
      </div>
    </div>
    <el-tabs v-model="tabName" @tab-click="tabClick">
      <el-tab-pane label="派车单" name="planOrder">
        <!-- 派车单 -->
        <div style="text-align: right;padding:0px 20px;margin-bottom: 10px;" v-if="isDLJC">
          <el-button v-if="!isPurePlanOrder" type="primary" size="mini" @click="switchPurePlanOrder">切换简洁版</el-button>
          <el-button v-else type="primary" size="mini" @click="switchPurePlanOrder">切换标准版</el-button>
        </div>
        <plan-order-pure v-if="isPurePlanOrder && isDLJC" ref="planOrderPure" :rte-plan="rtePlan"></plan-order-pure>
        <plan-order v-else ref="planOrder" :rte-plan="rtePlan">
          <!-- <template slot="header-buttons">
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('planOrder')">点击打印</el-button>
          </template> -->
        </plan-order>
      </el-tab-pane>
      <el-tab-pane
        :label="stepsNum >= 2 ? '装货单' : '装货单（尚未提交）'"
        name="loadingOrder"
      >
        <!-- 装货单 -->
        <!-- <loading-order v-if="stepsNum>=2" ref="loadingOrder" :rte-plan="rtePlan"> -->
        <loading-order
          ref="loadingOrder"
          :rte-plan="rtePlan"
          :order="loadOrder"
        >
          <!-- <template slot="header-buttons">
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('loadingOrder')">点击打印</el-button>
          </template> -->
        </loading-order>
      </el-tab-pane>
      <el-tab-pane
        :label="stepsNum >= 3 ? '卸货单' : '卸货单（尚未提交）'"
        name="unloadingOrder"
      >
        <!-- 卸货单 -->
        <!-- <unloading-order v-if="stepsNum>=3" ref="unloadingOrder" :rte-plan="rtePlan"> -->
        <unloading-order
          ref="unloadingOrder"
          :rte-plan="rtePlan"
          :order="unloadOrder"
        >
          <!-- <template slot="header-buttons">
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('unloadingOrder')">点击打印</el-button>
          </template> -->
        </unloading-order>
      </el-tab-pane>
      <el-tab-pane
        :label="stepsNum >= 4 ? '回执单' : '回执单（尚未提交）'"
        name="receiptOrder"
        v-if="false"
      >
        <!-- 回执单 -->
        <!-- <receipt-order v-if="stepsNum>=4" ref="receiptOrder" :rte-plan="rtePlan"> -->
        <receipt-order
          ref="receiptOrder"
          :rte-plan="rtePlan"
          :order="receiptOrder"
        >
          <!-- <template slot="header-buttons">
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('receiptOrder')">点击打印</el-button>
          </template> -->
        </receipt-order>
      </el-tab-pane>
    </el-tabs>

    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <div id="print_content" />
  </div>
</template>

<script>
import Steps from "@/components/Steps";
import * as $API from "@/api/rtePlan";
import PlanOrder from "./components/plan-order";
import PlanOrderPure from "./components/plan-order-pure";
import loadingOrder from "./components/loading-order";
import unloadingOrder from "./components/unloading-order";
import receiptOrder from "./components/receipt-order";
import fourStates from "./components/four-states";
import { resolve } from "q";

export default {
  name: "rtePlanBills",
  components: {
    Steps,
    PlanOrder,
    loadingOrder,
    unloadingOrder,
    receiptOrder,
    fourStates,
    PlanOrderPure
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      detailLoading: false,
      isShowOper: true, // 默认为页面，显示操作栏
      rtePlan: {},
      unloadOrder: {},
      receiptOrder: {},
      loadOrder: {},
      stepsNum: 0,
      stepsList: [
        { title: "派车单", subTitle: "2019-03-04" },
        { title: "装货单", subTitle: " " },
        { title: "卸货单", subTitle: " " },
        { title: "回执单", subTitle: " " }
      ],
      tabName: "planOrder",
      isPurePlanOrder:false
    };
  },
  computed:{
    isDLJC(){
      let roleNameList = localStorage.getItem('roleName');
      
      if(roleNameList){
        try {
          roleNameList = roleNameList.split(',');
          if(roleNameList && Object.prototype.toString.call(roleNameList) == '[object Array]'){
            // 判断是否为稽查大队角色
            let isGOVDLJC = roleNameList.includes('gov_dljc');
            if(isGOVDLJC){
              this.isPurePlanOrder = true;
            }
            
            return isGOVDLJC;
          }else{
            return false;
          }
        } catch (error) {
          return false;
        }
      }
      return false;
    }
  },
  created() {
    this.detailLoading = true;
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      let query = this.$route.query;
      if (ipPk) {
        this.API = 'getRtePlanNewByPk';
        if (query.year && query.year.length === 4) {
          this.API = 'getHistoryRtePlanNewByPk';
          this.historyYear = query.year;
        }
        this.rtePlanNewByPk(ipPk, query.year);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
      // console.log(123)
      // this.rtePlanNewByPk(ipPk);
    }
    // const pk = this.$route.params.id;
    // this.rtePlanNewByPk(pk);
  },
  mounted() {},
  methods: {
    switchPurePlanOrder(){
      this.isPurePlanOrder = !this.isPurePlanOrder;
      if(!this.isPurePlanOrder){
        // 重新渲染运单二维码
        this.$nextTick(() => {
          this.$refs.planOrder.render(true);
        })
      }
    },
    rtePlanNewByPk(pk, year) {
      const _this = this;
      let params = pk;
      if (this.API == 'getHistoryRtePlanNewByPk' || year) {
        params = {
          id: pk
        }
        if(year){
          params.year = year;
          this.API = 'getHistoryRtePlanNewByPk';
        }
      }else{
        this.API = "getRtePlanNewByPk";
      }
      $API[this.API](params)
        .then(response => {
          if (response && response.code === 0) {
            _this.rtePlan = response.data;
            _this.stepsList[0].subTitle = response.data.crtTm;
            if (response.data.goodsGw <= 0 && response.data.unloadQty <= 0) {
              // 未装卸
              _this.stepsNum = 1;
            } else if (
              response.data.goodsGw > 0 &&
              response.data.unloadQty <= 0
            ) {
              // 已装未卸载
              _this.stepsNum = 2;
              // } else if (response.data.goodsGw <= 0 && response.data.unloadQty > 0) { // 未装已卸载 error
              //   _this.stepsNum = 3;
            } else if (
              response.data.goodsGw > 0 &&
              response.data.unloadQty > 0
            ) {
              // 已装已卸载
              _this.stepsNum = 3;
            } else {
              _this.stepsNum = 1;
            }
            this.getInit(this.rtePlan);
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
          _this.detailLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.detailLoading = false;
        });
    },
    getInit(rtePlan) {
      this.getLoadOrder(rtePlan).then(() => {
        this.getUnloadOrder(rtePlan).then(() => {
          this.getReceiptOrder(rtePlan);
        });
      });
    },
    // this,
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    printHandle(moduleNm) {
      // 获取当前页的html代码
      var printhtml = this.$refs[moduleNm].$el.outerHTML;
      var f = document.getElementById("printf");
      if (f) {
        document.getElementById("print_content").removeChild(f);
      }
      var iframe = document.createElement("iframe");
      iframe.id = "printf";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";
      document.getElementById("print_content").appendChild(iframe);

      iframe.contentDocument.write(
        '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">'
      );
      iframe.contentDocument.write(
        '<html xmlns="http://www.w3.org/1999/xhtml">'
      );
      iframe.contentDocument.write("<head>");
      iframe.contentDocument.write(
        "<link rel='stylesheet' type='text/css' href='static/styles/rteplan_print.css'>"
      );
      iframe.contentDocument.write("</head>");
      iframe.contentDocument.write("<body>");
      iframe.contentDocument.write(printhtml);
      iframe.contentDocument.write("</body>");
      iframe.contentDocument.write("</html>");

      var script = iframe.contentDocument.createElement("script");
      script.setAttribute("type", "text/javascript");
      script.src =
        "https://api.map.baidu.com/api?v=2.0&ak=P26bkIlWewExmWDr0kaOjZyaoAqOUPBT";
      iframe.contentDocument.body.appendChild(script);

      iframe.contentDocument.close();
      iframe.contentWindow.focus();

      setTimeout(() => {
        iframe.contentWindow.print();
      }, 1000);
    },

    tabClick(tab, event) {
      if (this.$refs[tab.name] && this.$refs[tab.name].render) {
        this.$refs[tab.name].render(); // 四联单渲染
      }
    },
    getUnloadOrder(rteplan) {
      return new Promise((resolve, reject) => {
        let cd = rteplan.cd;
        if (cd) {
          // 根据 cd 值获取卸货单据信息
          // let params = {
          //   filters: {
          //     groupOp: "AND",
          //     rules: [{ field: "rteplan_cd", op: "cn", data: cd }],
          //   },
          // };

          $API
            .getOrderunloadByCd(cd)
            .then(res => {
              let data = res.list[0] || null;
              if (res.code == 0 && data) {
                this.unloadOrder = data;
                //如果argmwt/allListNoCount接口没有拿到单号，就从rtePlan/getDtlById接口给他数据
                if (!this.unloadOrder.chkBefCds)
                  this.unloadOrder.chkBefCds = this.rtePlan.chkUnloadBefCds;
                this.stepsNum = 3;
                this.stepsList[2].subTitle = data.crtTm;
              } else {
                this.unloadOrder = {};
              }
              resolve();
            })
            .catch(err => {
              resolve();
            });
        } else {
          this.unloadOrder = {};
          resolve();
        }
      });
    },
    getLoadOrder(rteplan) {
      return new Promise((resolve, reject) => {
        let cd = rteplan.cd;
        if (cd) {
          // 根据 cd 值获取回执单据信息
          // let params = {
          //   filters: {
          //     groupOp: "AND",
          //     rules: [{ field: "rteplan_cd", op: "cn", data: cd }],
          //   },
          // };
          $API
            .getOrderloadByCd(cd)
            .then(res => {
              let data = res.list[0] || null;
              if (res.code == 0 && data) {
                this.loadOrder = data;
                //如果argmwt/allListNoCount接口没有拿到单号，就从rtePlan/getDtlById接口给他数据
                if (!this.loadOrder.chkAftCds)
                  this.loadOrder.chkAftCds = this.rtePlan.chkLoadAftCds;
                if (!this.loadOrder.chkBefCds)
                  this.loadOrder.chkBefCds = this.rtePlan.chkLoadBefCds;
                this.stepsNum = 2;
                this.stepsList[1].subTitle = data.crtTm;
              } else {
                this.loadOrder = {};
              }
              resolve();
            })
            .catch(err => {
              resolve();
            });
        } else {
          this.loadOrder = {};
          resolve();
        }
      });
    },
    getReceiptOrder(rteplan) {
      return new Promise((resolve, reject) => {
        let cd = rteplan.cd;
        if (cd) {
          // 根据 cd 值获取回执单据信息
          let params = {
            filters: {
              groupOp: "AND",
              rules: [{ field: "rteplan_cd", op: "cn", data: cd }]
            }
          };

          $API
            .getOrderreceiptByCd(params)
            .then(res => {
              let data = res.page.list[0];
              if (res.code == 0 && data) {
                this.receiptOrder = data;
                this.stepsNum = 4;
                this.stepsList[3].subTitle = data.receiptTm;
              } else {
                this.receiptOrder = {};
              }
              resolve();
            })
            .catch(err => {
              resolve();
            });
        } else {
          this.receiptOrder = {};
          resolve();
        }
      });
    }
  }
};
</script>
<style scoped>
.error-tips {
  color: #d00;
}
</style>
