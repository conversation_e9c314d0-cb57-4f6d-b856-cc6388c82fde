<template>
  <div class="detail-container">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>&nbsp;返回</el-button
        >
      </el-button-group>
    </div>
    <div class="app-main-content">
      <searchbar
        ref="searchbar"
        :search-items="searchItems"
        :pagination="pagination"
        @resizeSearchbar="resizeSearchbar"
        @search="getList"
      >
        <el-form-item prop="year" :label="'年份'">
          <el-select
            size="small"
            v-model="selectedYear.year"
            :placeholder="'请选择年份'"
            @change="yearSelectChange"
          >
            <el-option
              v-for="(item, index) in yearOptions"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </searchbar>
      <el-table
        v-loading="listLoading"
        :max-height="tableHeight"
        :data="list"
        class="el-table"
        cell-class-name="custom-el-table_column"
        highlight-current-row
        border
        style="width: 100%"
        @sort-change="handleSort"
      >
        <el-table-column prop="cd" label="运单号" width="210" fixed="left">
          <template slot-scope="scope">
            <el-button @click.native.prevent="showBills(scope.row.argmtPk)" type="text">{{ scope.row.cd }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="tracCd" label="牵引车" />
        <el-table-column prop="traiCd" label="挂车号" />
        <el-table-column prop="dvNm" label="驾驶员" />
        <el-table-column prop="scNm" label="押运员" />
        <el-table-column prop="goodsNm" label="货物" />
        <el-table-column prop="loadQty" label="重量" />
        <el-table-column prop="csnorWhseDist" label="起运地" />
        <el-table-column prop="csneeWhseDist" label="目的地" />
        <el-table-column prop="vecDespTm" label="起运日期">
          <template slot-scope="scope">{{
            scope.row.vecDespTm && scope.row.vecDespTm.substring(0, 10)
          }}</template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-size="pagination.limit"
        :current-page.sync="pagination.page"
        :page-sizes="[20, 30, 50, 100, 200]"
        background
        layout="sizes, prev, next"
        style="float: right"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/rtePlan";
import { getFuzzyTracCd } from "@/api/vec";
export default {
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      tableHeight: Tool.getClientHeight() - 210,
      listLoading: false,
      list: [],
      selectedYear: { year: "2019" }, //默认年份
      yearOptions: [], //年份列表
      tracList: [], //牵引车模糊搜索列表
      searchItems: {
        normal: [
          {
            name: "运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq"
          },
          {
            name: "牵引车",
            field: "tracCd",
            type: "fuzzy",
            dbfield: "trac_cd",
            dboper: "eq",
            api: this.getTracCd
          },
          {
            name: "起运日期",
            field: "vecDespTm",
            type: "daterange",
            dbfield: "vec_desp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20
      }
    };
  },
  components: {
    Searchbar
  },
  mounted: function() {
    this.getYearOptions(); //获取年份列表
    // this.getList()
    this.setTableHeight();
  },
  methods: {
    //获取年份列表
    getYearOptions() {
      $http.getHisYearList().then(res => {
        if (res.code === 0) {
          this.yearOptions = res.list.map(item => {
            return { label: item, value: item };
          });
          this.$set(this.selectedYear, "year", res.list[0]);
          this.yearSelectChange(res.list[0]);
          this.getList();
        }
      });
    },

    showBills: function (argmtPk) {
      this.$router.push({
        path: "/base/rteplan/bills/" + argmtPk,
        query: {
          year:this.selectedYear.year,
        }
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 改变搜索框的高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 获取数据
    getList: function(data, sortParam, callback) {
      const _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      const param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination,
        this.selectedYear
      );
      this.listLoading = true;
      $http
        .getHistoryList(param)
        .then(response => {
          if (response.code === 0) {
            _this.list = response.list;
            // _this.pagination.total = response.page.totalCount
            if (callback) {
              callback.call(_this, response);
            }
          } else {
            _this.list = [];
            // _this.pagination.total = 0
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 返回上一页
    goBack() {
      // this.$router.go(-1);
      this.$router.push({ path: "/base/rteplan/list/" });
    },
    yearSelectChange(val) {
      this.$refs.searchbar.searchHandle(true, { name: "year", value: val });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    }
  }
};
</script>
<style scoped>
.app-main-content {
  margin: 0;
}
</style>
