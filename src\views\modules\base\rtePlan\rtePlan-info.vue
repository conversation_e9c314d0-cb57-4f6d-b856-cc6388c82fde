<template>
  <div v-loading="detailLoading" class="detail-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
        <i class="el-icon-back"/>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <div v-if="rtePlan.invalid===1" style="margin:15px;">
          <el-alert :closable="false" type="error">
            <span style="font-size:14px;">电子运单无效原因：</span>{{ rtePlan.invalidReason }}
          </el-alert>
        </div>

        <!-- 派车单 -->
        <plan-order ref="planOrder" :rte-plan="rtePlan">
          <template v-slot:header-buttons>
            <el-button type="primary" round size="small" icon="el-icon-printer" @click="printHandle('planOrder')">点击打印</el-button>
          </template>
        </plan-order>

      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->

    <div id="print_content"/>
  </div>
</template>

<script>
import { getRtePlanNewByPk } from '@/api/rtePlan'
import PlanOrder from './components/plan-order'

export default {
  components: {
    PlanOrder
  },
  data() {
    return {
      detailLoading: false,
      rtePlan: {}
    }
  },
  created() {
    const _this = this
    const pk = this.$route.params.id

    this.detailLoading = true
    getRtePlanNewByPk(pk).then((res) => {
      if (res && res.code === 0) {
        _this.rtePlan = res.data
      } else {
        this.$message.error(res.msg)
      }
      _this.detailLoading = false
    }).catch(error => {
      this.detailLoading = false
      console.log(error)
    })
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    printHandle(moduleNm) {
      // 获取当前页的html代码
      var printhtml = this.$refs[moduleNm].$el.outerHTML
      var f = document.getElementById('printf')
      if (f) {
        document.getElementById('print_content').removeChild(f)
      }
      var iframe = document.createElement('iframe')
      iframe.id = 'printf'
      iframe.style.width = '0'
      iframe.style.height = '0'
      iframe.style.border = 'none'
      document.getElementById('print_content').appendChild(iframe)

      iframe.contentDocument.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">')
      iframe.contentDocument.write('<html xmlns="http://www.w3.org/1999/xhtml">')
      iframe.contentDocument.write('<head>')
      iframe.contentDocument.write("<link rel='stylesheet' type='text/css' href='static/styles/rteplan_print.css'>")
      iframe.contentDocument.write('</head>')
      iframe.contentDocument.write('<body>')
      iframe.contentDocument.write(printhtml)
      iframe.contentDocument.write('</body>')
      iframe.contentDocument.write('</html>')

      var script = iframe.contentDocument.createElement('script')
      script.setAttribute('type', 'text/javascript')
      script.src = 'https://api.map.baidu.com/api?v=2.0&ak=P26bkIlWewExmWDr0kaOjZyaoAqOUPBT'
      iframe.contentDocument.body.appendChild(script)

      iframe.contentDocument.close()
      iframe.contentWindow.focus()

      setTimeout(() => {
        iframe.contentWindow.print()
      }, 1000)
    }
  }
}
</script>
<style scoped>
.error-tips{
  color:#d00;
}
</style>
