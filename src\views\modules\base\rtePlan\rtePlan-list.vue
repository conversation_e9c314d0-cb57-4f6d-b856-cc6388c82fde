<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <!-- <template slot="button">
          <el-button size="small" icon="el-icon-download" @click="downloadRteplanExcel">导出</el-button>
      </template> -->
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;"
      :max-height="tableHeight" border @sort-change="handleSort" @expand-change="expandSelect">
      <!-- 倒三角详情 -->
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="form-expand-in-table">
            <el-row :gutter="10">
              <!-- 人车罐信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="牵引车">
                  <span>{{ props.row.tracCd }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="挂车号">
                  <span>{{ props.row.traiCd }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="罐体编号">
                  <span>{{ props.row.tankNum }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="驾驶员">
                  <span>{{ props.row.dvNm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="18">
                <el-form-item label="联系方式">
                  <span>{{ props.row.dvMob }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="押运员">
                  <span>{{ props.row.scNm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="18">
                <el-form-item label="联系方式">
                  <span>{{ props.row.scMob }}</span>
                </el-form-item>
              </el-col>
              <!-- 货物信息 -->

              <el-col :xs="24" :sm="12" :md="8" :lg="6" v-if="props.row.dangGoodsNm">
                <el-form-item label="危险货物名称">
                  <span>{{ props.row.dangGoodsNm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="货物类型">
                  <span>{{ props.row.goodsCat }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="装运数量(吨)">
                  <span>{{ props.row.loadQty }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="包装规格">
                  <span>{{ props.row.packType }}</span>
                </el-form-item>
              </el-col>
              <!-- 相关方信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="托运人">
                  <span>{{ props.row.consignorAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="18">
                <el-form-item label="联系方式">
                  <span>{{ props.row.consignorTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="装货人">
                  <span>{{ props.row.csnorWhseAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="联系人">
                  <span>{{ props.row.csnorWhseCt }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="联系方式">
                  <span>{{ props.row.csnorWhseTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="起运地">
                  <span>{{ props.row.csnorWhseDist }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="详细地址">
                  <span>{{ props.row.csnorWhseLoc }}</span>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="所属园区">
                  <span>{{ props.row.csnorPark }}</span>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="收货人">
                  <span>{{ props.row.csneeWhseAddr }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="联系人">
                  <span>{{ props.row.csneeWhseCt }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="联系方式">
                  <span>{{ props.row.csneeWhseTel }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="目的地">
                  <span>{{ props.row.csneeWhseDist }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="详细地址">
                  <span>{{ props.row.csneeWhseLoc }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="所属园区">
                  <span>{{ props.row.csneePark }}</span>
                </el-form-item>
              </el-col>

              <!-- 调度信息 -->
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="调度员">
                  <span>{{ props.row.dispatcher }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="调度日期">
                  <span>{{ props.row.reqtTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="城市配送">
                  <span v-if="props.row.cityDelivery === 1">是</span>
                  <span v-if="props.row.cityDelivery === 0">否</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="起运日期">
                  <span>{{ props.row.vecDespTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="计划开始日期">
                  <span>{{ props.row.planStartTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="计划结束日期">
                  <span>{{ props.row.planEndTm }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="备注">
                  <span>{{ props.row.freeText }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="充装企业提货单号">
                  <span>{{ props.row.shipOrdCustCd }}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="12">
                <el-form-item label="事件ID">
                  <span>{{ eventIdList[props.row.cd] }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="cd" label="运单号" width="230">
        <template slot-scope="scope">
          <el-badge :value="scope.row.loadType != null && scope.row.loadType != '一装一卸' ? '多' : ''" type="primary"
            :hidden="false">
            <el-button @click.native.prevent="showBills(scope.row.argmtPk)" type="text">
              {{ scope.row.cd }}&nbsp;&nbsp;
            </el-button>
          </el-badge>
        </template>
      </el-table-column>
      <el-table-column prop="clientType" label="运输状态">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="hover" width="1200">
            <fourStates :ref="scope.row.cd + 'fourStates'" :originalRtePlan="scope.row" @refreshRtePlan="getList()">
            </fourStates>
            <el-button type="text" slot="reference">{{
              scope.row.transportationStatus
              }}</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="tracCd" label="牵引车" width="85"></el-table-column>
      <el-table-column prop="traiCd" label="挂车号" width="90"></el-table-column>
      <el-table-column prop="dvNm" label="驾驶员" width="95">
        <template slot-scope="scope">
          <span :style="{ color: securityColors[scope.row.codeState] }" class="dvNm" @click="showDvInfo(scope.row)">
            {{ scope.row.dvNm }}{{ dvNmFormat(scope.row.codeState) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="scNm" label="押运员" width="65"></el-table-column>
      <el-table-column prop="goodsNm" label="货物" width="120">
        <template slot-scope="scope">
          <el-popover placement="right-start" width="200" trigger="hover">
            <div>{{ scope.row.goodsNm }}</div>
            <div slot="reference" class="ellipsis">{{ scope.row.goodsNm }}</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="csnorWhseDist" label="起运地" width="130"></el-table-column>
      <el-table-column prop="csneeWhseDist" label="目的地" width="130"></el-table-column>

      <el-table-column prop="csnorWhseAddr" label="装货企业" width="150"></el-table-column>
      <el-table-column prop="csneeWhseAddr" label="卸货企业" width="150"></el-table-column>

      <!-- <el-table-column prop="consignorAddr" label="委托单位"></el-table-column> -->
      <el-table-column prop="vecDespTm" label="起运日期" width="105">
        <template slot-scope="scope">
          {{ scope.row.vecDespTm && scope.row.vecDespTm.substring(0, 10) }}
        </template>
      </el-table-column>
      <el-table-column prop="loginkStatCd" label="同步状态" width="145">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.loginkStatCd == '1105'">上传状态</el-tag>
          <el-tag size="mini" v-else-if="scope.row.loginkStatCd == '1105.150'" type="inf'" title="未同步至省运管">未同步</el-tag>
          <el-tag size="mini" v-else-if="scope.row.loginkStatCd == '1105.160'" type="warning" title="正在同步">正在同步</el-tag>
          <el-popover v-else-if="scope.row.loginkStatCd == '1105.170'" trigger="hover" placement="top">
            失败原因：<span v-if="scope.row.loginkStatRemark">{{
              scope.row.loginkStatRemark
              }}</span><span v-else>无</span>
            <div slot="reference" class="name-wrapper">
              <el-tag size="mini" type="danger" title="同步至省运管失败">同步失败</el-tag>
            </div>
          </el-popover>
          <el-tag size="mini" v-else-if="scope.row.loginkStatCd == '1105.180'" type="success" title="同步至省运管成功">同步成功
          </el-tag>
          <el-tag size="mini" v-else-if="scope.row.loginkStatCd == '1105.190'">企业自行同步</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="refFlag" label="装卸状态">
        <template slot-scope="scope">
          <!-- <el-tag size="mini" type="success" v-if="scope.row.goodsGw>0 && scope.row.unloadQty>0">已装卸</el-tag> -->
          <el-tag size="mini" type="danger" v-if="scope.row.goodsGw > 0" title="已装">已装</el-tag>
          <el-tag size="mini" type="success" v-else-if="scope.row.unloadQty > 0" title="已卸">已卸</el-tag>
          <el-tag size="mini" type="info" v-else title="未装卸">未装卸</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="smsTm" label="是否发短信">
          <template slot-scope="scope">
              <span v-if="scope.row.smsTm">发送时间：<br />{{scope.row.smsTm | FormatDate('yyyy-MM-dd HH:mm:ss')}}</span>
                <el-tag size="mini" v-else :type="'warning'" title="未发送">未发送</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="生成二维码" width="140" align="center">
            <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="openQRCodeDialog(scope.row.argmtPk)">生成二维码</el-button>
            </template>
        </el-table-column> -->
      <el-table-column prop="crtTm" label="创建时间" width="105"></el-table-column>
      <el-table-column prop="clientType" label="上报来源">
        <template slot-scope="scope">
          <span v-if="scope.row.clientType == 1">网页</span>
          <span v-else-if="scope.row.clientType == 2">app</span>
          <span v-else-if="scope.row.clientType == 3">微信</span>
          <span v-else-if="scope.row.clientType == 4">小程序</span>
          <span v-else-if="scope.row.clientType == 5">接口</span>
          <span v-else-if="scope.row.clientType == 6">LOGINK</span>
        </template>
      </el-table-column>
      <el-table-column prop="invalid" label="运单状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.invalid == 0" type="success">有效</el-tag>
          <el-popover v-else-if="scope.row.invalid == 1" trigger="hover" placement="top">
            无效原因：<span v-if="scope.row.invalidReason">{{
              scope.row.invalidReason
              }}</span><span v-else>无</span>
            <div slot="reference" class="name-wrapper">
              <el-tag type="danger">无效</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>

      <!-- <el-table-column prop="dispatcher" label="操作人"  align="center"></el-table-column> -->
      <!-- <el-table-column prop="crtTm" label="操作时间" width="180" align="center"></el-table-column> -->
      <!-- <el-table-column prop="goAddr" label="实时位置"  align="center"></el-table-column> -->
      <el-table-column prop="freeText" label="备注" align="center"></el-table-column>
      <!-- <el-table-column label="单据" width="80" align="center">
          <template slot-scope="scope">
              <el-button type="text" title="单据" @click="showBills(scope.row.argmtPk)">单据</el-button>
          </template>
      </el-table-column> -->
    </el-table>

    <el-dialog title="二维码" :visible.sync="openQRCode" :append-to-body="true" :before-close="cancelQR">
      <div id="qrcode" ref="qrcode" style="margin:0px 0px 0px 35%;"></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelQR()">取 消</el-button>
        <el-button type="primary" @click="download()">下 载</el-button>
      </span>
    </el-dialog>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="text" size="small" class="link_btn" title="查看历史运单" @click="historyRteplan">
          <svg-icon icon-class="link" class-name="svg-icon" />
          历史运单
        </el-button>
      </div>
      <el-pagination background layout="sizes, prev, next" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :current-page.sync="pagination.page"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import Steps from "@/components/Steps";
import { getRtePlanList, delRtePlane, getQRCode } from "@/api/rtePlan";
import * as $http from "@/api/rtePlan";
import { getFuzzyTracCd } from "@/api/vec";
import { getChemDanger } from "@/api/danger";
import * as Tool from "@/utils/tool";
import QRCode from "qrcodejs2";
import fourStates from "./components/four-states";

export default {
  name: "RtePlanList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      eventIdList: {},
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      dangGoodsList: [], //危化品模糊查询列表
      qRName: "",
      openQRCode: false,
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "起运日期",
            field: "vecDespTm",
            type: "datetimerange",
            dbfield: "vec_desp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            default: [
              Tool.formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00",
              Tool.formatDate(new Date(), "yyyy-MM-dd") + " 23:59:59"
            ],
            width: "140"
          },
          {
            name: "牵引车",
            field: "tracCd",
            type: "fuzzy",
            dbfield: "trac_cd",
            dboper: "eq",
            api: this.getTracCd
          },
          {
            name: "运单号",
            field: "cd",
            type: "text",
            dbfield: "cd",
            dboper: "eq"
          }
        ],
        more: [
          {
            name: "挂车号",
            field: "traiCd",
            type: "fuzzy",
            dbfield: "trai_cd",
            dboper: "eq",
            api: this.getTraiCd
          },
          {
            name: "承运人",
            field: "carrierNm",
            type: "text",
            dbfield: "carrier_nm",
            dboper: "eq"
          },
          {
            name: "驾驶员",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn"
          },
          {
            name: "押运员",
            field: "scNm",
            type: "text",
            dbfield: "sc_nm",
            dboper: "cn"
          },
          {
            name: "货物",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn"
          },
          {
            name: "危化品名",
            field: "dangGoodsNm",
            type: "fuzzy",
            dbfield: "dang_goods_nm",
            dboper: "eq",
            api: this.getDangGoodsNm
          },
          {
            name: "罐体",
            field: "tankNum",
            type: "text",
            dbfield: "tank_num",
            dboper: "cn"
          },
          {
            name: "起运地",
            field: "csnorWhseDistCd",
            type: "region",
            dbfield: "csnor_whse_dist_cd",
            dboper: "eq"
          }, // 省市区
          {
            name: "目的地",
            field: "csneeWhseDistCd",
            type: "region",
            dbfield: "csnee_whse_dist_cd",
            dboper: "eq"
          },
          {
            name: "装货企业",
            field: "csnorWhseAddr",
            type: "text",
            dbfield: "csnor_whse_addr",
            dboper: "cn"
          },
          {
            name: "卸货企业",
            field: "csneeWhseAddr",
            type: "text",
            dbfield: "csnee_whse_addr",
            dboper: "cn"
          }, // 省市区
          {
            name: "同步状态",
            field: "loginkStatCd",
            type: "select",
            options: [
              { label: "所有状态", value: "" },
              { label: "上传状态", value: "1105" },
              { label: "未同步", value: "1105.150" },
              { label: "正在同步", value: "1105.160" },
              { label: "同步失败", value: "1105.170" },
              { label: "同步成功", value: "1105.180" },
              { label: "企业自行同步", value: "1105.190" }
            ],
            dbfield: "logink_stat_cd",
            dboper: "eq"
          },
          {
            name: "上报来源",
            field: "clientType",
            type: "select",
            options: [
              { label: "所有来源", value: "" },
              { label: "网页", value: 1 },
              // { label: 'app', value: 2 },
              { label: "微信公众号", value: 3 },
              { label: "小程序", value: 4 },
              { label: "接口", value: 5 },
              { label: "省内同步", value: 6 }
            ],
            dbfield: "client_type",
            dboper: "eq"
          },
          {
            name: "运单类型",
            field: "isEmpty",
            type: "select",
            options: [
              { label: "全部", value: "" },
              { label: "空车单", value: "1" },
              { label: "非空车单", value: "0" },
            ],
            dbfield: "is_empty",
            dboper: "nao"
          },
          {
            name: "装卸状态",
            field: "refFlag",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              // { label: "已装卸", value: "2" },
              { label: "已装", value: "1" },
              { label: "已卸", value: "-1" },
              { label: "未装卸", value: "0" }
            ],
            dbfield: "ref_flag",
            dboper: "eq"
          },
          // { name: '完结时间', field:'endTm', type:'daterange',dbfield:'end_tm',dboper:'bt',valueFormat:'yyyy-MM-dd HH:mm:ss'},
          // { name: '危化品名', field: 'dangGoodsNm', type: 'text', dbfield: 'dang_goods_nm', dboper: 'cn' },

          // { name: '是否发短信', field:'smsTm', type:'radio',
          //     options:[
          //         { label: '全部', value: ''},
          //         { label: '已发短信', value:""},
          //         { label: '未发短信', value:""}
          //     ],
          //     dbfield:'sms_tm',dboper:'cn'
          // },
          {
            name: "上报系统",
            field: "sysId",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "镇海", value: "330211" },
              { label: "上虞", value: "330604" },
              { label: "青峙", value: "330206007" }
            ],
            dbfield: "sys_id",
            dboper: "eq"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 15
      },
      securityColors: {
        0: "#0089e8",
        1: "#ffc600",
        2: "#ff0000",
        99: "#cccccc"
      }
    };
  },
  components: {
    Searchbar,
    Steps,
    fourStates,
    ElDatepicker: {
      template: `
        <el-date-picker
          v-model="dateVal"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="daterange"
          @change="changeHandle"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>`,
      data() {
        return {
          dateVal: ""
        };
      },
      methods: {
        changeHandle(param) {
          this.$emit("changeHandle", param);
        }
      }
    }
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    // //起运日期默认显示当天
    // let today = Tool.formatDate(new Date(), "yyyy-MM-dd");
    // this.searchItems.normal[0].default = today;
    // this.searchItems.normal[1].default = today;

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //刷新运单四状态
    refreshRtePlanStatus(row) {
      $http
        .getRtePlanStatus(row.cd)
        .then(res => {
          if (res.code == 0) {
            this.getList();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //展开行获取事件ID
    expandSelect(row) {
      let _this = this;
      $http
        .getEventId(row.cd)
        .then(res => {
          if (res && res.code == 0) {
            _this.$set(_this.eventIdList, row.cd, res.eventId || "无");
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //历史运单
    historyRteplan() {
      this.$router.push({
        path: "/base/rtePlan/history"
      });
    },
    downloadRteplanExcel() {
      //导出电子运单excel
      const h = this.$createElement;
      let exportTime = null;
      let attributes = {
        on: {
          changeHandle: param => {
            exportTime = param;
          }
        }
      };

      this.$msgbox({
        title: "消息",
        message: h("ElDatepicker", attributes),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "执行中...";
            setTimeout(() => {
              done();
              setTimeout(() => {
                instance.confirmButtonLoading = false;
              }, 300);
            }, 3000);
          } else {
            done();
          }
        }
      }).then(action => { });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      let argmtPks, argmtPk;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;
      // 对起运日期 始&终 处理
      // param = this.vecDespTmDeal(param)
      // if (!param) return //只有 一个日期的情况

      getRtePlanList(param)
        .then(response => {
          if (response.code == 0) {
            response.list.forEach(item => {
              item.codeState = "";
            });
            _this.list = response.list;
            argmtPks = _this.list.map((item, index) => {
              argmtPk = item.argmtPk;
              return argmtPk;
            });
            if (argmtPks.length > 0) {
              argmtPks = argmtPks.join(",");
              _this.getSecurityCode(argmtPks);
            } else {
              _this.listLoading = false;
            }
            _this.list.forEach(item => {
              if (item.backTm) {
                item.transportationStatus = "结束";
                item.transportationStatusCode = 4;
                item.operateTm = item.backTm;
              } else if (item.unloadTm) {
                item.transportationStatus = "卸货";
                item.transportationStatusCode = 3;
                item.operateTm = item.unloadTm;
              } else if (item.loadTm) {
                item.transportationStatus = "装货";
                item.transportationStatusCode = 2;
                item.operateTm = item.loadTm;
              } else if (item.goTm) {
                item.transportationStatus = "发车";
                item.transportationStatusCode = 1;
                item.operateTm = item.goTm;
              } else {
                item.transportationStatus = "无";
                item.transportationStatusCode = 0;
                item.operateTm = "";
              }
              if (item.errBackStatus == 211) {
                item.transportationStatus = "异常结束";
                item.transportationStatusCode = -1;
                item.operateTm = item.errBackTm;
              }
            });
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },

    // 删除
    del: function (id) {
      var _this = this;

      this.$confirm("确认删除该记录吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;

          delRtePlane({ argmtPk: id })
            .then(response => {
              _this.listLoading = false;
              if (response.code == 0) {
                _this.$message({
                  message: "删除成功",
                  type: "success"
                });
                _this.refreshGrid();
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({
        path: "/base/rteplan/info/" + row.argmtPk,
        params: row
      });
    },
    //生成二维码
    openQRCodeDialog: function (argmtPk) {
      const _this = this;
      _this.openQRCode = true;
      _this.qRName = argmtPk;
      getQRCode(argmtPk)
        .then(response => {
          if (response) {
            _this.createdQRCode(response);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    createdQRCode: function (qrStr) {
      this.$nextTick(function () {
        this.qrcode(qrStr);
      });
    },

    qrcode(qrStr) {
      new QRCode(this.$refs.qrcode, {
        text: qrStr,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L
      });
    },

    cancelQR: function () {
      const _this = this;
      _this.openQRCode = false;
      _this.$refs.qrcode.innerHTML = "";
    },
    download: function () {
      const _this = this;
      let myCanvas = document
        .getElementById("qrcode")
        .getElementsByTagName("canvas");
      let img = document.getElementById("qrcode").getElementsByTagName("img");
      let a = document.createElement("a");
      let imgURL = myCanvas[0].toDataURL("image/jpg");
      img.src = myCanvas[0].toDataURL("image/jpg");
      a.href = img.src;
      a.download = _this.qRName;
      a.click();
    },

    // 单据
    showBills: function (argmtPk) {
      this.$router.push({
        path: "/base/rteplan/bills/" + argmtPk
      });
    },
    //获取步骤条时间信息
    getstepList(item) {
      let stepList = [
        {
          title: "1",
          icon: "fa"
        },
        {
          title: "2",
          icon: "zhuang"
        },
        {
          title: "3",
          icon: "xie"
        },
        {
          title: "4",
          icon: "hui"
        }
      ];

      // 发
      stepList[0].title = item.goTm || "";
      // 装
      stepList[1].title = item.loadTm || "";
      // 卸
      stepList[2].title = item.unloadTm || "";
      //回
      stepList[3].title = item.backTm || "";
      //异
      if (item.errBackStatus == 211) {
        stepList[3].icon = "yi";
        stepList[3].title = item.errBackTm || "";
      }
      return stepList;
    },
    //获取步骤条进度
    getActive(item) {
      if (item.backTm) {
        return 4;
      } else if (item.unloadTm) {
        return 3;
      } else if (item.loadTm) {
        return 2;
      } else if (item.goTm) {
        return 1;
      }
    },
    //获取步骤条运单异常状态
    getInvalid(item) {
      if (item.errBackStatus == 211) {
        return true;
      }
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    //危化品模糊搜索
    async getDangGoodsNm(queryString, cb) {
      const res = await getChemDanger(queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.dangGoodsList = [];
          return;
        }
        this.dangGoodsList = res.page.list.map(item => {
          return { value: item.nm };
        });
        cb(this.dangGoodsList);
      }
    },
    // 获取安全分
    getSecurityCode(argmtPks) {
      this.listLoading = true;
      $http
        .getSecurityCodeByArgmtPks(argmtPks)
        .then(res => {
          this.listLoading = false;
          if (res.code === 0) {
            res.data.forEach((item, index) => {
              if (JSON.parse(item.data)) {
                let codeState = JSON.parse(item.data).codeState;
                this.list[index].codeState = codeState;
              }
            });
          }
        })
        .catch(error => {
          console.log(error);
          this.listLoading = false;
        });
    },
    //人员名字后添加（蓝码；黄码……）
    dvNmFormat(codeState) {
      let securityNm = {
        0: "(蓝码)",
        1: "(黄码)",
        2: "(红码)",
        99: "(无码)"
      };
      return securityNm[codeState];
    },
    //跳转到人员详情
    showDvInfo(row) {
      this.$router.push({ path: "/base/pers/info/" + row.dvPk, params: row });
    }
  }
};
</script>
<style scoped>
.dvNm {
  cursor: pointer;
}

.statusInfo div {
  margin-bottom: 5px;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

<style>
.el-table .cell,
.el-table th div {
  overflow: visible;
}
</style>
