<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <div class="panel">
      <div class="panel-header">
        <span class="panel-heading-inner">基本信息</span>
      </div>
      <div class="panel-body">
        <!-- 顶部信息 -->
        <ul class="detail-ul">
          <li class="col-all">
            <div class="detail-desc">企业名称：</div>
            <div class="detail-area" :title="tank.ownedCompany">
              {{ tank.ownedCompany }}
              <span style="color:#d00;" v-if="tank.ownedCompanyisApproved === false">【审核未通过】</span>
            </div>
          </li>
          <li>
            <div class="detail-desc">罐体编号：</div>
            <div class="detail-area" :title="tank.tankNum">
              {{ tank.tankNum }}
            </div>
          </li>
          <li>
            <div class="detail-desc">罐体类型：</div>
            <div class="detail-area" :title="tank.tankType">
              {{ tank.tankType }}
            </div>
          </li>
          <li>
            <div class="detail-desc">罐体容积：</div>
            <div class="detail-area" :title="tank.volume + 'm<sup>3</sup>'">
              {{ tank.volume }}m
              <sup>3</sup>
            </div>
          </li>
          <li>
            <div class="detail-desc">产品名称：</div>
            <div class="detail-area" :title="tank.prdtNm">
              {{ tank.prdtNm }}
            </div>
          </li>
          <li v-if="tank.tankType == '常压罐'">
            <div class="detail-desc">额定载质量：</div>
            <div class="detail-area" :title="tank.filWeight + 'Kg'">
              {{ tank.filWeight }}Kg
            </div>
          </li>
          <li v-else>
            <div class="detail-desc">最大允许充装量：</div>
            <div class="detail-area" :title="tank.filWeight + 'Kg'">
              {{ tank.filWeight }}Kg
            </div>
          </li>
          <li>
            <div class="detail-desc">关联车牌号：</div>
            <!-- <div class="detail-area" :title="tank.traiNo">{{tank.traiNo}}</div> -->
            <div class="detail-area" :title="tank.traiNo" @click="showDetail(tank.traiPk)" v-if="tank && tank.traiPk">
              <el-button type="text" size="mini" style="font-size: 13px; vertical-align: middle">{{ tank.traiNo
              }}</el-button>
            </div>
            <div class="detail-area" :title="tank.traiNo" v-else>
              <div class="detail-area" :title="tank.traiNo">
                {{ tank.traiNo }}
              </div>
            </div>
          </li>
          <li>
            <div class="detail-desc">投运/制造日期：</div>
            <div class="detail-area" :title="tank.commDate">
              {{ tank.commDate | FormatDate("yyyy-MM-dd") }}
            </div>
          </li>
          <li>
            <div class="detail-desc">制造单位：</div>
            <div :title="tank.manuFact" class="detail-area">
              {{ tank.manuFact }}
            </div>
          </li>
          <li>
            <div class="detail-desc">检验机构：</div>
            <div :title="tank.ispctOrg" class="detail-area">
              {{ tank.ispctOrg }}
            </div>
          </li>
          <li v-if="tank.tankType == '常压罐'">
            <div class="detail-desc">罐体设计代码：</div>
            <div :title="tank.designCode" class="detail-area">
              {{ tank.designCode }}
            </div>
          </li>
          <li>
            <div class="detail-desc">装运介质：</div>
            <div class="detail-area" :title="tank.medProp">
              {{ tank.medProp }}
            </div>
          </li>
        </ul>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer">
        <el-col :sm="12" style="color: red">
          最近一次更新时间：<span v-if="tank.updTm">{{ tank.updTm }}</span>
        </el-col>
        <el-col :sm="12">
          <div class="text-right">
            审核状态：
            <span class="lic-status">
              <template v-if="tank.basicHandleFlag == ''">未提交</template>
              <template v-else-if="tank.basicHandleFlag === '1'">审核通过</template>
              <template v-else-if="tank.basicHandleFlag === '2'">
                审核未通过，原因：
                <template v-if="tank.basicHandleRemark">{{
                  tank.basicHandleRemark
                }}</template>
                <template v-else>无</template>
              </template>
              <template v-else>
                待受理
                <template v-if="tank.basicHandleRemark"></template>
              </template>
            </span>
          </div>
          <approve-bar v-permission="'appr:update'" :approve-info="basicApproveInfo" :rejectReasons="basicRejectReasons"
            @getPassStatus="getPassStatus" @getRjectStatus="getRjectStatus"
            @getHandleStat="getHandleStat"></approve-bar>
        </el-col>
      </div>
      <!-- 审核操作按钮 -->
      <!--         <div class="approvalOper text-right">
            <el-button-group>
            <el-button type="success" size="mini"  title="通过">
            通过
            </el-button>
            <el-button type="danger" size="mini"  title="驳回">
                驳回
            </el-button>
            </el-button-group>
        </div>
      </div> -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray"></span> 待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green"></span> 审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow"></span> 将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red"></span> 未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred"></span> 已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape">
        <certificates :data-source="licData" :cert-tepl-data="certTeplData" :reject-reasons="licRejectReasons">
        </certificates>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <!-- <div class="mod-container-oper" v-if="isShowOper">
			<el-button-group>
				<el-button type="warning" @click="goBack">
					<i class="el-icon-back"></i>&nbsp;返回</el-button>
			</el-button-group>
		</div> -->
  </div>
</template>

<script>
import certificates from "@/components/Certificates";
import approveBar from "@/components/approveBar";
import licConfig from "@/utils/licConfig";
import { getTankByPk } from "@/api/tank";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "TankInfo",
  components: {
    certificates,
    approveBar,
  },
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      currentDate: new Date().getTime(),
      detailLoading: false,
      certTeplData: null,
      tank: {},
      licData: [],
      //基本信息自定义驳回原因
      basicRejectReasons: [
        { reason: "罐体编号填写有误", index: 1 },
        { reason: "罐体容积填写有误", index: 2 },
      ],
      //证照自定义驳回原因
      licRejectReasons: [
        { reason: "证照模糊信息无法确认，请重新上传", index: 1 },
        { reason: "证照已过期，请重新上传", index: 2 },
        { reason: "资料上传不符，请重新上传", index: 3 },
      ],
      basicApproveInfo: {
        entityPk: "",
        catCd: "8010.504",
        licPk: "",
        statCd: "",
        desc: "",
      },
    };
  },
  watch: {
    "tank.entpPk"(newValue) {
      this.getEntpApproveInfoOfVec(newValue);
    }
  },
  created() {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }
    this.certTeplData = licConfig["tank"] || {};
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    },
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;
      this.basicApproveInfo.entityPk = ipPk;
      this.detailLoading = true;
      getTankByPk(ipPk)
        .then((response) => {
          if (response && response.code === 0) {
            // let { items } = response.data.items;
            _this.licData = response.data.items;
            _this.tank = response.data.tank;
            let certTeplData = {};
            if (_this.tank.tankType == "压力罐") {
              certTeplData = Object.assign({}, licConfig["yaLiDict"]);
            } else if (_this.tank.tankType == "常压罐") {
              certTeplData = Object.assign({}, licConfig["changYaDict"]);
            }
            // let tankType = _this.tank.tankType;
            // let certTeplData = Object.assign({}, licConfig["tank"]);
            // if (tankType === "常压罐") {
            //   delete certTeplData["8010.501"];
            //   delete certTeplData["8010.503"];
            //   delete certTeplData["8010.505"];
            // } else if (tankType === "压力罐") {
            //   delete certTeplData["8010.506"];
            //   delete certTeplData["8010.502"];
            // }
            // for (var key in certTeplData) {
            //   if (key === "8010.506") {
            //     certTeplData["8010.505"] = certTeplData[key];
            //     delete certTeplData[key];
            //   }
            // }
            _this.certTeplData = certTeplData;

            // const permissionList = _this.$store.state.user.permissions;
            // permissionList.forEach(item => {
            //   if (item == "tank:update") {
            //     _this.certTeplData["8010.500"].aprvAprvOfGongguan = true;
            //     _this.certTeplData["8010.502"].aprvAprvOfGongguan = true;
            //     if (tankType === "压力罐") {
            //       _this.certTeplData["8010.501"].aprvAprvOfGongguan = true;
            //       _this.certTeplData["8010.503"].aprvAprvOfGongguan = true;
            //     }
            //   }
            // });
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          _this.detailLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.detailLoading = false;
        });
    },

    goBack() {
      this.$router.go(-1);
    },

    //获取通过审核操作的返回结果
    getPassStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.tank.basicHandleFlag = payload.handleFlag;
      }
    },
    //获取驳回审核操作的返回结果
    getRjectStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.tank.basicHandleFlag = payload.handleFlag;
        this.tank.basicHandleRemark = payload.remark;
      }
    },
    //点击通过或驳回审核操作的监听
    getHandleStat(type) {
      this.basicLoading = true;
    },

    // 挂车详情
    showDetail: function (vecPk) {
      if (vecPk) {
        this.$router.push({ path: "/base/vec/info/" + vecPk });
      } else {
        this.$message({
          message: "对不起，车牌pk为空，无法查看详情，请联系管理员！",
          type: "error",
        });
      }
    },
    // 获取车辆所属企业的审核状态
    async getEntpApproveInfoOfVec(entpPk) {
      if (entpPk) {
        let res = await getEntpIsApprovedByIds([entpPk]);
        if (res && res.code == 0) {
          let val = true;
          res.data.forEach(item => {
            if (item.ipPk == entpPk) {
              val = item.isApproved;
            }
          });
          this.$set(this.tank, 'ownedCompanyisApproved', val);
        }
      } else {
        this.$set(this.tank, 'ownedCompanyisApproved', false);
      }
    }
  },
};
</script>
<style scoped>
.panel-footer {
  overflow: hidden;
}

.panel-footer .fl-l {
  float: left;
}

.panel-footer .fl-r {
  float: right;
}
</style>
