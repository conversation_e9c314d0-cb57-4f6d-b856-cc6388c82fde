<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" highlight-current-row border style="width: 100%" v-loading="listLoading"
      :max-height="tableHeight" :data="list" @sort-change="handleSort" :cell-class-name="cellClassName">
      <el-table-column prop="tankNum" label="罐体编号" min-width="200" fixed="left">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.tankNum }}
          </el-button>
        </template>
      </el-table-column>
     <el-table-column prop="ownedCompany" label="所属企业" min-width="280" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="{ color: scope.row.ownedCompanyisApproved === false ? '#d00' : '' }">{{
            scope.row.ownedCompany
            }}<span v-show="scope.row.ownedCompanyisApproved === false">【审核未通过】</span></span>
        </template>
      </el-table-column>
      <el-table-column prop="licApproveResult" min-width="140" label="审核状态">
        <template v-if="scope.row.licApproveResult" slot-scope="scope">
          <auditStatus licType="entp" :result="scope.row.licApproveResult" :resultCd="scope.row.licApproveResultCd"
            :aiRemark="scope.row.licApproveRemark">
          </auditStatus>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in Object.keys(scope.row.licApproveResult)">
              <p v-if="scope.row.licApproveResult[item].includes('待审核')" :key="index" style="color: #e6a23c">
                {{ item + "：待受理" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('审核通过')
                " :key="index" style="color: green">
                {{ item + "：审核通过" }}
              </p>
              <p v-else-if="scope.row.licApproveResult[item].includes('未通过')" :key="index" style="color: red">
                {{ item + "：未通过" }}
              </p>
            </template>
  <div slot="reference" class="name-wrapper">
    <template v-for="(key, index) in Object.keys(
                scope.row.licApproveResultCd
              )">
                <el-tag :key="index" :type="getTagType(scope.row.licApproveResultCd[key])" close-transition>
                  <template v-if="key === '8010.504'">基</template>
    <template v-else-if="key === '8010.508'">质</template>
    <template v-else-if="key === '8010.506'">合</template>
    <template v-else-if="key === '8010.500'">检</template>
    <template v-else-if="key === '8010.507'">铭</template>
    <template v-else-if="key === '8010.509'">容</template>
    <template v-else-if="key === '8010.510'">移</template>
    <template v-else-if="key === '8010.501'">特</template>
    </el-tag>
    </template>
  </div>
  </el-popover> -->
        </template>
      </el-table-column>
      <!-- <el-table-column prop="tankType" label="状态"></el-table-column> -->
      <el-table-column prop="tankType" label="罐体类型" min-width="100"></el-table-column>
      <el-table-column prop="volume" label="罐体容积(m³)" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.volume }}m<sup>3</sup>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="tankType" label="投运日期"></el-table-column> -->
      <el-table-column prop="traiNo" label="关联车牌号" min-width="100"></el-table-column>
      <el-table-column prop="completeDocRate" label="完成度" min-width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.completeDocRate == undefined" style="color: red">0/
            <!-- 常压罐 -->
            <template v-if="scope.row.tankType === '常压罐'">8</template>
            <!-- 压力罐 -->
            <template v-else-if="scope.row.tankType === '压力罐'">9</template>
          </span>
          <span v-else-if="scope.row.tankType === '常压罐' && scope.row.completeDocRate < 9
          " style="color: red">{{
            scope.row.completeDocRate > 9 ? 9 : scope.row.completeDocRate
            }}/{{ 8 }}</span>
          <span v-else-if="scope.row.tankType === '压力罐' && scope.row.completeDocRate < 8
          " style="color: red">{{
            scope.row.completeDocRate > 8 ? 8 : scope.row.completeDocRate
            }}/{{ 9 }}</span>
          <span v-else>{{ scope.row.completeDocRate }}/
            <!-- 常压罐 -->
            <template v-if="scope.row.tankType === '常压罐'">8</template>
            <!-- 压力罐 -->
            <template v-else-if="scope.row.tankType === '压力罐'">9</template>
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="sysId" label="证件状态" min-width="80">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.isLicExpire == 1" :type="'info'">已到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 2" :type="'warning'">将到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 0" :type="'success'">正常</el-tag>
          <el-tag size="mini" v-else>未填报</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200]"
        :total="pagination.total" style="float: right">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import Searchbar from "@/components/Searchbar";
import auditStatus from "@/components/auditStatus";
import { getTankList, delTank, getTankDocComplete } from "@/api/tank";
import * as Tool from "@/utils/tool";
import HashMap from "@/utils/hashmap";
import Cookies from "js-cookie";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "TankList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      searchItems: {
        normal: [
          {
            name: "所属企业",
            field: "ownedCompany",
            type: "text",
            dbfield: "owned_company",
            dboper: "cn"
          },
          {
            name: "车牌号",
            field: "traiNo",
            type: "text",
            dbfield: "trai_no",
            dboper: "cn"
          },

          {
            name: "罐体类型",
            field: "tankType",
            type: "select",
            options: [
              { label: "所有罐体类型", value: "" },
              { label: "常压罐", value: "常压罐" },
              { label: "压力罐", value: "压力罐" }
            ],
            dbfield: "tank_type",
            dboper: "cn"
          },
          {
            name: "区域",
            field: "entpDist",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "区内", value: "区内" },
              { label: "市内", value: "市内" },
              { label: "市外", value: "市外" }
            ],
            dbfield: "entp_dist",
            dboper: "nao"
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              // { label: "未提交", value: "3" },
              { label: "待受理", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              { label: "审核未通过企业", value: "4" }
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao"
          }
        ],
        more: [
          {
            name: "罐体编号",
            field: "tankNum",
            type: "text",
            dbfield: "tank_num",
            dboper: "cn"
          },
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" }
            ],
            dbfield: "is_lic_expire",
            dboper: "eq"
          }
        ]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  components: {
    Searchbar,
    auditStatus
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.approvalDefault(); //根据账号类型切换审核状态顺序
    this.approvalStatus(); //根据账号类型显示审核状态
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === "licApproveResult") {
        return "overflow-visble"
      } else {
        return ""
      }
    },
    // 根据账号类型切换审核状态顺序
    approvalDefault() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.normal[4].default = "1"; //切换审核状态默认顺序
      }
    },
    //根据账号类型显示审核状态
    approvalStatus() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.normal[4].options.splice(4, 1); //隐藏'审核未通过企业'选项
      } else {
        //政府其他账号
        // this.searchItems.normal[4].options.splice(1, 1); //隐藏'未提交'选项
      }
    },

    getTagType(state) {
      if (state === "0") {
        return "warning";
      } else if (state === "1") {
        return "success";
      } else if (state === "2") {
        return "danger";
      } else {
        return "";
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      getTankList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list.map(item => {
              let rowData = Object.assign({}, item);
              rowData.licApproveResult = JSON.parse(rowData.licApproveResult);
              rowData.licApproveResultCd = JSON.parse(
                rowData.licApproveResultCd
              );
              return rowData;
            });

            let tankPks = "";
            for (let i = 0, len = _this.list.length; i < len; i++) {
              tankPks += _this.list[i].cntrPk + ",";
            }

            if (tankPks != "") {
              _this.countTankDocComplete(tankPks.replace(/,$/, ""));
            }

            _this.changeEntpInfo(_this.list); //企业是否审核通过
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    changeEntpInfo() {
      this.$nextTick(async () => {
        let data = this.list;
        let ids = data.map(item => item.entpPk);
        let uniqueIds = [...new Set(ids)];
		if (uniqueIds.length == 0) {
          return;
        }
        let res = await getEntpIsApprovedByIds(uniqueIds);
        let entpObj = {};
        if (res && res.code == 0) {
          res.data.forEach(item => {
            entpObj[item.ipPk] = item.isApproved;
          })

          data.forEach((item, index) => {
            let id = item.entpPk;
            if (id) {
              this.$set(this.list[index], 'ownedCompanyisApproved', entpObj[id])
            } else {
              this.$set(this.list[index], 'ownedCompanyisApproved', false)
            }
          })
        }
      })
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },

    // 显示罐体信息完成度
    countTankDocComplete: function (tankPks) {
      let _this = this;
      this.listLoading = true;

      getTankDocComplete(tankPks)
        .then(response => {
          if (!response || response.length == 0) return;
          let list = this.list;
          var map = new HashMap();
          for (let i = 0, len = response.length; i < len; i++) {
            map.put(response[i].cntrPk, response[i].total);
          }
          _this.list.forEach((item, index) => {
            const tankPkTemp = map.get(item.cntrPk);
            if (tankPkTemp == null) {
              _this.$set(_this.list[index], "completeDocRate", 0);
            } else {
              _this.$set(_this.list[index], "completeDocRate", tankPkTemp);
            }
          });
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({ path: "/base/tank/info/" + row.cntrPk, params: row });
    }
  }
};
</script>
