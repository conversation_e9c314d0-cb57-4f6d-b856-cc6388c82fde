<template>
  <div>
    <template v-if="edit">
      <el-table :data="currentVal" style="width: 100%" :size="size" border empty-text="无车辆安全设备信息">
        <el-table-column prop="name" label="安全设备名称">
          <template slot-scope="{row,$index}">
            <el-select v-model="row.name" placeholder="请选择安全设备名称" :size="size" @change="handleCellEdit($index,row)">
              <el-option v-for="item in equipmentList" :key="item.id" :label="item.nmCn" :value="item.cd"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="数量" width="130">
          <template slot-scope="{row,$index}">
            <el-input-number v-model="row.value" placeholder="请输入数量" :min="0" :step="1" :size="size" :precision="0" style="width:120px" @change="handleCellEdit($index,row)"></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="50" align="center">
          <template slot="header">
            <el-button size="mini" type="primary" icon="el-icon-plus" circle title="添加" @click="add"></el-button>
          </template>
          <template slot-scope="scope">
            <i class="el-icon-delete" @click="del(scope.$index)" style="cursor:pointer;" title="删除"></i>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-else>
      <el-table :data="currentVal" style="width: 100%" :size="size" border empty-text="无车辆安全设备信息">
        <el-table-column prop="name" label="安全设备名称">
          <template slot-scope="scope">
            {{getNameByCd(scope.row.name)}}
          </template>
        </el-table-column>
        <el-table-column prop="value" label="数量" width="100"></el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
import { getEquipDict } from "@/api/vec";
export default {
  name: "customeItem",
  model: {
    prop: "modelVal",
    event: "modelChangeEvent",
  },
  props: {
    modelVal: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "mini",
    },
    edit: {
      type: Boolean,
      default: false,
    },
    vecPk:{
      type:[Number,String],
    }
  },
  computed: {
    currentVal() {
      let res = [];
      let modelVal = this.modelVal;
      if (
        Object.prototype.toString.apply(modelVal) === "[object String]" &&
        modelVal.length > 0
      ) {
        res = JSON.parse(modelVal);
      }
      return res;
    },
  },
  data() {
    return {
      loading: false,
      inputVisible: false,
      inputValue: "",

      equipmentList: [],
    };
  },
  watch:{
    vecPk:{
      handler(val,oldVal){
        if(val){
          this.getEquipDictData(val);
        }else{
          this.$set(this, "equipmentList", []);
        }
      },
      immediate:true
    }
  },
  created() {
    
  },
  methods: {
    getNameByCd(cd){
      let res = this.equipmentList.filter(it=>it.cd===cd)
      if(res.length){
        return res[0].nmCn
      }else{
        return ""
      }
    },
    getEquipDictData(vecPk) {
      let self = this;
      getEquipDict(vecPk).then((res) => {
        if (res && res.code === 0) {
          self.$set(self, "equipmentList", res.data);
        }
      });
    },
    handleCellEdit(index, data) {
      this.$set(this.currentVal, index, data);
      this.modifyHandle(this.currentVal);
    },
    add() {
      this.currentVal.push({ name: "", value: "" });
      this.modifyHandle(this.currentVal);
    },
    del(index) {
      this.currentVal.splice(index, 1);
      this.modifyHandle(this.currentVal);
    },
    // 向父组件提交证件修改信息，触发父组件方法
    modifyHandle(dataArr) {
      let val = dataArr.length > 0 ? JSON.stringify(dataArr) : "";
      this.$emit("change", val);
      this.$emit("modelChangeEvent", val);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>