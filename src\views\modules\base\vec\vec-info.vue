<template>
  <div class="detail-container" v-loading="detailLoading">
    <div class="mod-container-oper" v-if="isShowOper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back"></i>&nbsp;返回
        </el-button>
      </el-button-group>
    </div>
    <div class="panel" v-loading="basicLoading">
      <div class="panel-header text-right">
        <span class="panel-heading-inner">基本信息</span>
        <el-button class="btnValidVecInfo" type="primary" size="mini" @click="validateVecInfo">省运管信息校验</el-button>
      </div>
      <div class="panel-body">
        <el-col :sm="flex">
          <!-- 顶部信息 -->
          <ul class="detail-ul">
            <li class="col-all">
              <div class="detail-desc">企业名称：</div>
              <div class="detail-area" :title="vec.ownedCompany">
                {{ vec.ownedCompany }}
                <span style="color:#d00;" v-if="vec.ownedCompanyisApproved === false">【审核未通过】</span>
              </div>
            </li>
            <li>
              <div class="detail-desc">车牌号：</div>
              <div class="detail-area" :title="vec.vecNo">{{ vec.vecNo }}</div>
            </li>
            <li>
              <div class="detail-desc">车辆类型：</div>
              <div class="detail-area" :title="vec.catNmCn">
                {{ vec.catNmCn }}
              </div>
            </li>
            <li>
              <div class="detail-desc">车架号：</div>
              <div class="detail-area" :title="vec.chassisNo">
                {{ vec.chassisNo }}
              </div>
            </li>
            <li>
              <div class="detail-desc">道路运输证号：</div>
              <div class="detail-area" :title="vec.opraLicNo">
                {{ vec.opraLicNo }}
              </div>
            </li>
            <li>
              <div class="detail-desc">整备质量（KG）：</div>
              <div class="detail-area" :title="vec.selfWeight">
                {{ vec.selfWeight }}
              </div>
            </li>
            <li>
              <div class="detail-desc">核载/准牵引质量(KG)：</div>
              <div class="detail-area" :title="vec.apprvWeight">
                {{ vec.apprvWeight }}
              </div>
            </li>
            <li>
              <div class="detail-desc">车牌类型：</div>
              <div class="detail-area" :title="vec.plateType">
                {{ vec.plateType }}
              </div>
            </li>
            <li class="col-all wape-no">
              <div class="detail-desc">经营类型：</div>
              <div class="detail-area" :title="vec.bizScope">
                <el-tree node-key="id" :data="bizScopeDataDisabled" show-checkbox ref="bizScopeTree"
                  :default-expanded-keys="expandedNode">
                  <span class="custom-tree-node" slot-scope="{ node }">
                    <span>{{ node.label
                    }}<span v-if="node.data.extra != ''">({{ node.data.extra }})</span></span>
                  </span>
                </el-tree>
              </div>
            </li>
          </ul>
        </el-col>
        <el-col :sm="12" v-show="flex == 12">
          <!-- 顶部信息 -->
          <el-card style="width: 90%">
            <div slot="header">
              <span style="font-size: 16px">校验信息</span>
            </div>
            <div>
              <ul class="detail-ul valid-vec-info">
                <li>
                  <div class="detail-desc">车辆类型：</div>
                  <div class="detail-area" :title="validatedInfo.validCatNmCn">
                    {{ validatedInfo.validCatNmCn }}
                  </div>
                </li>
                <li>
                  <div class="detail-desc">道路运输证号：</div>
                  <div class="detail-area" :title="validatedInfo.validRoadTransNum">
                    {{ validatedInfo.validRoadTransNum }}
                  </div>
                </li>
                <li class="col-all wape-no">
                  <div class="detail-desc">经营类型：</div>
                  <div class="detail-area" :title="validatedInfo.validBusinessScope">
                    {{ validatedInfo.validBusinessScope }}
                  </div>
                </li>
              </ul>
              <ul class="detail-ul valid-vec-info">
                <li class="col-all wape-no">
                  <div class="detail-desc">信息验证时间：</div>
                  <div class="detail-area" :title="validatedInfo.validTm">
                    {{ validatedInfo.validTm }}
                  </div>
                </li>
              </ul>
            </div>
          </el-card>
        </el-col>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="panel-footer">
        <el-col :sm="18" style="color: red">
          最近一次更新时间：
          <span v-if="vec.updTm">{{ vec.updTm }}</span>&nbsp;&nbsp;
          卫星定位更新时间：
          <span v-if="latestGpsTime">{{ latestGpsTime }}</span>&nbsp;&nbsp;
          上传频率：
          <span v-if="intervalUpdate">{{ intervalUpdate / 1000 }}秒</span>
        </el-col>
        <el-col :sm="6">
          <div class="text-right">
            审核状态：
            <span class="lic-status">
              <template v-if="vec.basicHandleFlag == ''">未提交</template>
              <template v-else-if="vec.basicHandleFlag === '1'">审核通过</template>
              <template v-else-if="vec.basicHandleFlag === '2'">
                审核未通过，原因：
                <template v-if="vec.basicHandleRemark">{{
                  vec.basicHandleRemark
                  }}</template>
                <template v-else>无</template>
              </template>
              <template v-else-if="vec.basicHandleFlag === '0'">
                待受理
              </template>
            </span>
          </div>
          <!-- 审核操作按钮 -->
          <approve-bar v-permission="'appr:update'" v-if="basicRejectReasons.length" :approve-info="basicApproveInfo"
            :reject-reasons="basicRejectReasons" @getPassStatus="getPassStatus" @getRjectStatus="getRjectStatus"
            @getHandleStat="getHandleStat"></approve-bar>
        </el-col>
      </div>
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="panel" ref="licwape">
      <div class="panel-header">
        <span class="panel-heading-inner">证照信息</span>
        <div class="panel-heading-right">
          <div class="lic-status-info">
            <span class="circle-point gray"></span> 待审核
          </div>
          <div class="lic-status-info">
            <span class="circle-point green"></span> 审核通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point yellow"></span> 将过期
          </div>
          <div class="lic-status-info">
            <span class="circle-point red"></span> 未通过
          </div>
          <div class="lic-status-info">
            <span class="circle-point deepred"></span> 已过期
          </div>
        </div>
      </div>
      <div class="panel-body lic-wape" style="background-color: #edf0f5">
        <certificates v-if="licRejectReasons.length" :data-source="licData" :cert-tepl-data="certTeplData"
          :reject-reasons="licRejectReasons">
          <template slot="8010.305" slot-scope="{ data }">
            <custome-item v-model="data.equip" :vecPk="vec.vecPk"></custome-item>
          </template>
        </certificates>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
  </div>
</template>

<style scoped>
.btnValidVecInfo {
  margin-top: 10px;
}
</style>


<script>
import certificates from "@/components/Certificates";
import approveBar from "@/components/approveBar";
import customeItem from "./components/customeItem";
import licConfig from "@/utils/licConfig";
import * as $http from "@/api/vec";
import { vecBizScopeData } from "@/utils/globalData";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "VecInfo",
  props: {
    // 是否是组件，默认为false(页面)
    isCompn: {
      type: Boolean,
      default: false
    }
  },
  components: {
    certificates,
    approveBar,
    customeItem
  },
  data() {
    return {
      isShowOper: true, // 默认为页面，显示操作栏
      flex: 24,
      vecNo: "",
      detailLoading: false,
      basicLoading: false,
      certTeplData: null,
      latestGpsTime: "", //最近一次GPS更新时间
      intervalUpdate: "", //GPS上传频率
      validatedInfo: {},
      basicRejectReasons: [],
      licRejectReasons: [],
      //牵引车基本信息自定义驳回原因
      tracBasicRejectReasons: [
        { reason: "车牌号信息填写错误", index: 1 },
        { reason: "车辆类型选择错误", index: 2 },
        { reason: "车架号填写错误", index: 3 },
        { reason: "道路运输证号填写错误", index: 4 },
        { reason: "整备质量填写错误", index: 5 },
        { reason: "核载/准牵引质量填写错误", index: 6 },
        { reason: "车牌类型选择错误", index: 7 },
        { reason: "经营类型选择错误", index: 8 },
        { reason: "卫星定位未接入", index: 9 },
        { reason: "车辆超过备案车辆数标准", index: 10 },
        { reason: "未上传等级评定报告", index: 11 },
        { reason: "车辆未备案，详情请拨打执法队电话：0574-86280661", index: 12 },
      ],
      //牵引车证照自定义驳回原因
      tracLicRejectReasons: [
        { reason: "年审有效期选择错误", index: 1 },
        { reason: "等级评定有效期选择错误", index: 2 },
        { reason: "检验有效期选择错误", index: 3 },
        { reason: "保险有效期选择错误", index: 4 },
        { reason: "证照上传不清晰，信息无法审核", index: 5 },
        { reason: "证照已过期", index: 6 },
        { reason: "证照上传不符合要求", index: 7 },
        { reason: "证照未上传", index: 8 },
        { reason: "未上传彩色扫描或者彩色拍照证照", index: 9 },
        { reason: "牵引车车牌号未在保单上体现", index: 10 },
        { reason: "未将备案车辆整车与安全设备统一拍照上传", index: 11 },
        { reason: "未上传等级评定报告", index: 12 }
      ],
      //挂车基本信息自定义驳回原因
      traiBasicRejectReasons: [
        { reason: "车牌号信息填写错误", index: 1 },
        { reason: "车辆类型选择错误", index: 2 },
        { reason: "车架号填写错误", index: 3 },
        { reason: "道路运输证号填写错误", index: 4 },
        { reason: "整备质量填写错误", index: 5 },
        { reason: "核载/准牵引质量填写错误", index: 6 },
        { reason: "车牌类型选择错误", index: 7 },
        { reason: "经营类型选择错误", index: 8 },
        { reason: "卫星定位未接入", index: 9 },
        { reason: "车辆超过备案车辆数标准", index: 10 },
        { reason: "未上传等级评定报告", index: 11 },
        { reason: "车辆未备案，详情请拨打执法队电话：0574-86280661", index: 12 },
      ],
      //挂车证照自定义驳回原因
      traiLicRejectReasons: [
        { reason: "年审有效期选择错误", index: 1 },
        { reason: "等级评定有效期选择错误", index: 2 },
        { reason: "检验有效期选择错误", index: 3 },
        { reason: "保险有效期选择错误", index: 4 },
        { reason: "证照上传不清晰，信息无法审核", index: 5 },
        { reason: "证照已过期", index: 6 },
        { reason: "证照上传不符合要求", index: 7 },
        { reason: "证照未上传", index: 8 },
        { reason: "未上传彩色扫描或者彩色拍照证照", index: 9 },
        { reason: "牵引车车牌号未在保单上体现", index: 10 },
        { reason: "未将备案车辆整车与安全设备统一拍照上传", index: 11 },
        { reason: "未上传等级评定报告", index: 12 }
      ],
      // 车辆类型
      catNmCnOptions: [],
      // 经营类型
      bizScopeData: vecBizScopeData,
      bsCatCnDetail: [],
      expandedNode: [],
      vec: { vecPk: null },
      licData: [],
      basicApproveInfo: {
        entityPk: "",
        catCd: "8010.307",
        licPk: "",
        statCd: "",
        desc: ""
      }
    };
  },
  created() {
    if (!this.isCompn) {
      //当是页面时
      let ipPk = this.$route.params.id;
      if (ipPk) {
        this.initByPk(ipPk);
      } else {
        this.$message.error("对不起，页面数据无法查询");
      }
    } else {
      this.isShowOper = false;
    }
    this.certTeplData = licConfig["vec"] || {};
  },
  computed: {
    key() {
      return this.$route.id !== undefined
        ? this.$route.id + +new Date()
        : this.$route + +new Date();
    },
    bizScopeDataDisabled() {
      let arr = [].concat(JSON.parse(JSON.stringify(this.bizScopeData)));
      arr.forEach(item => {
        item.disabled = true;
        if (item.children) {
          item.children.forEach(it => {
            it.disabled = true;
          });
        }
      });
      return arr;
    }
  },
  watch: {
    "vec.entpPk"(newValue) {
      this.getEntpApproveInfoOfVec(newValue);
    }
  },
  methods: {
    // 初始化
    initByPk(ipPk) {
      let _this = this;
      this.basicApproveInfo.entityPk = ipPk;
      this.detailLoading = true;
      $http
        .getVecType()
        .then(res => {
          if (res && res.code == 0) {
            res.data.forEach(item => {
              // 1180.154:牵引车; 1180.155:挂车; 1180.155.150:半挂车;1180.155.155:全挂车;1180.157:其他
              if (
                item.cd === "1180.154" ||
                item.cd === "1180.155" ||
                item.cd === "1180.155.150" ||
                item.cd === "1180.155.155"
              ) {
                _this.catNmCnOptions.push({
                  label: `----${item.nmCn}----`,
                  value: item.nmCn,
                  disabled: true
                });
              } else {
                if (item.cd.indexOf("1180.155") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "挂车"
                  });
                } else if (item.cd.indexOf("1180.154") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "牵引车"
                  });
                } else if (item.cd.indexOf("1180.157") > -1) {
                  _this.catNmCnOptions.push({
                    label: item.nmCn,
                    value: item.nmCn,
                    type: "其他"
                  });
                }
              }
            });
            $http
              .getVecByPk(ipPk)
              .then(response => {
                if (response && response.code === 0) {
                  let licData = response.data.items;
                  _this.bsCatCnDetail = JSON.parse(
                    response.data.vec.bsCatCnDetail
                  );
                  // _this.vec = response.data.vec;
                  _this.$set(_this, "vec", response.data.vec);
                  /** 设置证件信息中需要显示基础信息的>>>>>>>>>>> */
                  let licPropertyInVec = {
                    opraLicNo: _this.vec.opraLicNo,
                    chassisNo: _this.vec.chassisNo,
                    catNmCn: _this.vec.catNmCn,
                    plateType: _this.vec.plateType,
                    selfWeight: _this.vec.selfWeight,
                    apprvWeight: _this.vec.apprvWeight
                  };
                  licData = licData.map(item => {
                    return Object.assign({}, item, licPropertyInVec);
                  });
                  /**设置证件信息中需要显示基础信息的<<<<<<<<<<<< */
                  // _this.licData = licData;
                  _this.$set(_this, "licData", licData);
                  _this.initBizScopeTree(_this.vec.bsCatCd);
                  _this.setRejectReason(_this.vec.vecNo);

                  let catNmCn = _this.vec.catNmCn,
                    certTeplData = JSON.parse(JSON.stringify(licConfig["vec"]));

                  let selectedVecArr = _this.catNmCnOptions.filter(item => {
                    return item.value === catNmCn;
                  });
                  if (
                    selectedVecArr.length > 0 &&
                    selectedVecArr[0].type == "挂车"
                  ) {
                    delete certTeplData["8010.303"];
                    delete certTeplData["8010.304"];
                    delete certTeplData["8010.305"];
                    delete certTeplData["8010.300"].list["8010.300.153"];
                    certTeplData["8010.300"].header.splice(2, 1);
                  }
                  _this.certTeplData = certTeplData;

                  //查询最近一次GPS更新时间
                  _this.getLatestGpsTime([_this.vec.vecNo]);
                } else {
                  _this.$message({
                    message: response.msg,
                    type: "error"
                  });
                }
                _this.detailLoading = false;
              })
              .catch(error => {
                throw new Error(error);
                _this.detailLoading = false;
              });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //GPS更新时间
    getLatestGpsTime(vecNos) {
      let _this = this;

      $http
        .getLatestGpsTime(vecNos)
        .then(response => {
          if (response && response.length) {
            _this.latestGpsTime = response[0].updateTime;
            _this.intervalUpdate = response[0].intervalUpdate;
          }
        })
        .catch(error => { });
    },
    //根据车辆类型(牵引车/挂车)配置不同的驳回原因
    setRejectReason(vecNo) {
      //判断审核车辆的类型为牵引车或挂车
      //配置不同的驳回原因
      if (vecNo && vecNo.indexOf("挂") > -1) {
        this.basicRejectReasons = this.traiBasicRejectReasons;
        this.licRejectReasons = this.traiLicRejectReasons;
      } else {
        this.basicRejectReasons = this.tracBasicRejectReasons;
        this.licRejectReasons = this.tracLicRejectReasons;
      }
    },
    initBizScopeTree(treedata) {
      if (treedata && treedata != null && treedata != "" && treedata != "[]") {
        let parseTreedata = treedata;
        if (treedata.indexOf("[") >= 0) {
          parseTreedata = eval(treedata);
        } else {
          if (treedata.length > 0) {
            parseTreedata = treedata.split(",");
          } else {
            parseTreedata = [];
          }
        }
        this.$set(this, "expandedNode", parseTreedata);
        this.$refs.bizScopeTree.setCheckedKeys(parseTreedata);
        this.$nextTick(() => {
          let treeSelect = this.$refs.bizScopeTree.getCheckedNodes();
          // console.log(treeSelect);
          if (this.bsCatCnDetail && this.bsCatCnDetail.length > 0) {
            for (let i = 0; i < this.bsCatCnDetail.length; i++) {
              for (let j = 0; j < treeSelect.length; j++) {
                if (this.bsCatCnDetail[i].id == treeSelect[j].id) {
                  treeSelect[j].extra = this.bsCatCnDetail[i].extra;
                  break;
                }
              }
            }
          }
        });
      } else {
        this.$set(this, "expandedNode", []);
        this.$refs.bizScopeTree.setCheckedKeys([]);
      }
    },
    getLicmodelData(rsrcCd) {
      let res = [];

      let originalData = this.licListOriginal[rsrcCd];
      let headers = originalData.header;
      res = {};
      headers.forEach(function (item) {
        res[item.field] = null;
      });

      if (!this.licData) return res;
      let arr = this.licData.filter(it => {
        return it.licCatCd === rsrcCd;
      });
      if (arr.length > 0) {
        return arr[0];
      } else {
        return res;
      }
    },
    goBack() {
      this.$router.go(-1);
    },
    //验证车辆基本信息
    validateVecInfo() {
      let vecNo = this.vec.vecNo;
      this.basicLoading = true;
      $http
        .validateVceInfo(vecNo)
        .then(response => {
          if (response.code == 0) {
            if (response.data.isValid == "true") {
              if (this.flex != 12) {
                this.flex = 12;
              }
              this.validatedInfo = response.data;
              this.$message({
                type: "success",
                message:
                  "获取验证信息成功，有效期至:" + response.data.expirationDate
              });
            } else {
              this.$message({
                type: "error",
                message: "省运管中未查询到该车辆的信息"
              });
            }
          }
          this.basicLoading = false;
        })
        .catch(error => {
          throw new Error(error);
          this.basicLoading = false;
        });
    },
    //获取通过审核操作的返回结果
    getPassStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.vec.basicHandleFlag = payload.handleFlag;
      }
    },
    //获取驳回审核操作的返回结果
    getRjectStatus(payload) {
      var handleFlag = payload.handleFlag;
      this.basicLoading = false;
      if (handleFlag !== "fail") {
        this.vec.basicHandleFlag = payload.handleFlag;
        this.vec.basicHandleRemark = payload.remark;
      }
    },
    //点击通过或驳回审核操作的监听
    getHandleStat(type) {
      this.basicLoading = true;
    },
    // 获取车辆所属企业的审核状态
    async getEntpApproveInfoOfVec(entpPk) {
      if (entpPk) {
        let res = await getEntpIsApprovedByIds([entpPk]);
        if (res && res.code == 0) {
          let val = true;
          res.data.forEach(item => {
            if (item.ipPk == entpPk) {
              val = item.isApproved;
            }
          });
          this.$set(this.vec, 'ownedCompanyisApproved', val);
        }
      } else {
        this.$set(this.vec, 'ownedCompanyisApproved', false);
      }
    }
  }
};
</script>
<style scoped>
.panel-footer {
  overflow: hidden;
}

.panel-footer .fl-l {
  float: left;
}

.panel-footer .fl-r {
  float: right;
}
</style>
