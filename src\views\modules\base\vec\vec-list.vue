<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>
    <!--列表-->
    <el-table class="el-table" highlight-current-row border style="width: 100%" ref="singleTable"
      v-loading="listLoading" :max-height="tableHeight" :data="list" @current-change="handleSelectionChange"
      @sort-change="handleSort" :row-class-name="tableRowClassName" :cell-class-name="cellClassName">
      <el-table-column fixed="left" width="50" label="#">
        <template slot-scope="scope">
          <el-radio v-model="radio" :label="scope.row.vecPk">&nbsp;</el-radio>
        </template>
      </el-table-column>
      <el-table-column prop="vecNo" label="车牌号" min-width="100" fixed="left">
        <template slot-scope="scope">
          <el-button @click.native.prevent="showDetail(scope.row)" type="text">{{ scope.row.vecNo }}
          </el-button>
        </template>
      </el-table-column>
	  <el-table-column prop="ownedCompany" label="所属企业" min-width="280" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="{ color: scope.row.ownedCompanyisApproved === false ? '#d00' : '' }">{{
            scope.row.ownedCompany
            }}<span v-show="scope.row.ownedCompanyisApproved === false">【审核未通过】</span></span>
        </template>
      </el-table-column>
      <el-table-column prop="licApproveResult" min-width="160" label="审核状态">
        <template slot-scope="scope" v-if="scope.row.licApproveResult">
          <auditStatus licType="entp" :result="scope.row.licApproveResult" :resultCd="scope.row.licApproveResultCd"
            :aiRemark="scope.row.licApproveRemark">
          </auditStatus>
          <!-- <el-popover trigger="hover" placement="top">
            <template v-for="(item, index) in scope.row.licApproveResult
              .replace(/[\&quot;\{\}\s]/g,       .split(',')">
              <p v-if="item.includes('待审核')" style="color: #e6a23c" :key="index">
                {{ item.replace(/(?:待审核)/i, "待受理") }}
              </p>
              <p v-if="item.includes('审核通过')" style="color: green" :key="index">
                {{ item }}
              </p>
              <p v-if="item.includes('未通过')" style="color: red" :key="index">
                {{ item }}
              </p>
            </template>
  <div slot="reference" class="name-wrapper">
    <template v-for="(licApprove, index) in scope.row.licApproveResultCd
                .replace(/[\&quot;\{\}\s]/g,       .split(',')">
                <el-tag v-if="licApprove.includes('8010.307:0')" close-transition :type="'warning'" :key="index">基
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.307:1')" close-transition :type="'success'" :key="index">基
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.307:2')" close-transition :type="'danger'" :key="index">基
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.300:0')" close-transition :type="'warning'" :key="index">运
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.300:1')" close-transition :type="'success'" :key="index">运
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.300:2')" close-transition :type="'danger'" :key="index">运
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.303:0')" close-transition :type="'warning'" :key="index">卫
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.303:1')" close-transition :type="'success'" :key="index">卫
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.303:2')" close-transition :type="'danger'" :key="index">卫
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.304:0')" close-transition :type="'warning'" :key="index">险
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.304:1')" close-transition :type="'success'" :key="index">险
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.304:2')" close-transition :type="'danger'" :key="index">险
                </el-tag>

                <el-tag v-if="licApprove.includes('8010.305:0')" close-transition :type="'warning'" :key="index">安
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.305:1')" close-transition :type="'success'" :key="index">安
                </el-tag>
                <el-tag v-if="licApprove.includes('8010.305:2')" close-transition :type="'danger'" :key="index">安
                </el-tag>
              </template>
  </div>
  </el-popover> -->
        </template>
      </el-table-column>
      <el-table-column min-width="150" label="车辆类型" prop="catNmCn"></el-table-column>
      <el-table-column width="154" prop="latestGpsTime" label="卫星定位时间">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.latestGpsTime" size="mini" :type="gpsStatus(scope.row.latestGpsTime)">{{
            scope.row.latestGpsTime }}</el-tag>
          <el-tag v-else size="mini" type="info">无</el-tag>
        </template>
      </el-table-column>
      <el-table-column min-width="150" prop="comPany" label="卫星定位运营商">
        <template slot-scope="scope">
          {{ scope.row.comPany }}
        </template>
      </el-table-column>
      <!-- <el-table-column :width="154" prop="contactNm" label="运营商联系人">
        <template slot-scope="scope">
          {{scope.row.contactNm}}<span v-if="scope.row.contactMob">_{{scope.row.contactMob}}</span>
        </template>
      </el-table-column> -->
      <!--<el-table-column prop="opraLicNo" label="道路运输号" sortable="custom"></el-table-column>-->
      <!--<el-table-column prop="selfWeight" :width="80" label="自重(KG)" ></el-table-column>-->
      <!--<el-table-column prop="apprvWeight" :width="80" label="核载(KG)"></el-table-column>-->
      <el-table-column width="150" prop="crtTm" label="登记日期"> </el-table-column>
      <el-table-column min-width="80" label="完成度" prop="completeVecRate">
        <template slot-scope="scope">
          <span v-if="scope.row.completeVecRate == undefined" style="color: red">0/{{ scope.row.vecNo.indexOf("挂") > 0 ?
            9
            : 17 }}</span>
          <!-- 挂车 -->
          <span v-else-if="scope.row.completeVecRate < 9 && scope.row.vecNo.indexOf('挂') > 0
          " style="color: red">{{ scope.row.completeVecRate }}/9</span>
          <!-- 非挂车 -->
          <span v-else-if="scope.row.completeVecRate < 17 &&
            scope.row.vecNo.indexOf('挂') < 0
          " style="color: red">{{ scope.row.completeVecRate }}/17</span>
          <span v-else>{{ scope.row.completeVecRate }}/{{
            scope.row.vecNo.indexOf("挂") > 0 ? 9 : 17
            }}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="80" prop="isLicExpire" label="证件状态">
        <template slot-scope="scope">
          <el-tag size="mini" v-if="scope.row.isLicExpire == 1" :type="'info'">已到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 2" :type="'warning'">将到期</el-tag>
          <el-tag size="mini" v-else-if="scope.row.isLicExpire == 0" :type="'success'">正常</el-tag>
          <el-tag size="mini" v-else>未填报</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <!-- <el-badge :value="needauditcnt" class="item">
          <el-button type="success" icon="el-icon-plus" size="small" @click="newCapacity">新增申请</el-button>
        </el-badge> -->
        <el-button type="primary" size="small" :disabled="radio == null" @click="histroyTra">历史轨迹</el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page"
        :total="pagination.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import Searchbar from "@/components/Searchbar";
import auditStatus from "@/components/auditStatus";
import * as $http from "@/api/vec";
import * as Tool from "@/utils/tool";
import { FormatDate } from "@/filters/index";
import { latestTime, isExistBlackList } from "@/api/common";
import { getEntpIsApprovedByIds } from "@/api/entp";

export default {
  name: "VecList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      latest: "",
      listLoading: false,
      addLoading: false,
      fileslist: [],
      subSystemCheck: [],
      radio: null,
      currentRow: null,
      searchItems: {
        normal: [
          {
            name: "所属企业",
            field: "ownedCompany",
            type: "text",
            dbfield: "owned_company",
            dboper: "cn"
          },
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn"
          },
          {
            name: "车架号",
            field: "chassisNo",
            type: "text",
            dbfield: "chassis_no",
            dboper: "cn"
          },
          {
            name: "车辆类型",
            field: "catCdNew",
            type: "select",
            options: [{ label: "所有车辆类型", value: "" }],
            dbfield: "cat_cd_new",
            dboper: "eq"
          },
          {
            name: "区域",
            field: "entpDist",
            type: "select",
            options: [
              { label: "所有", value: "" },
              { label: "区内", value: "区内" },
              { label: "市内", value: "市内" },
              { label: "市外", value: "市外" }
            ],
            dbfield: "entp_dist",
            dboper: "nao"
          },
          {
            name: "证件状态",
            field: "isLicExpire",
            type: "select",
            options: [
              { label: "所有证件状态", value: "" },
              { label: "正常", value: "0" },
              { label: "已到期", value: "1" },
              { label: "将到期", value: "2" }
            ],
            dbfield: "is_lic_expire",
            dboper: "eq"
          },
          {
            name: "审核状态",
            field: "licApproveStatus",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              // { label: "未提交", value: "3" },
              { label: "待受理", value: "0" },
              { label: "审核通过", value: "1" },
              { label: "审核未通过", value: "2" },
              { label: "审核未通过企业", value: "4" }
            ],
            dbfield: "lic_approve_result_cd",
            dboper: "nao"
          }
        ],
        more: []
      },
      pagination: {
        total: 0,
        page: 3,
        limit: 20
      },
      needauditcnt: null
    };
  },
  components: {
    Searchbar,
    auditStatus
  },
  created() {
    latestTime().then(res => {
      if (res.code == 0) {
        this.latest = res.nowStamp;
      } else {
        this.latest = new Date().getTime();
      }
    });
    // 获取需要审核的数量
    this.needAuditCnt()
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.approvalDefault(); //根据账号类型切换审核状态顺序
    this.getVecCategory(); //获取车辆类型分类
    this.approvalStatus(); //根据账号类型显示审核状态
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    needAuditCnt() {
      $http.needAuditCnt().then(res => {
        if (res && res.data) {
          this.needauditcnt = res.data;
        }
      })
    },
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === "licApproveResult") {
        return "overflow-visble"
      } else {
        return ""
      }
    },
    //获取车辆类型分类,添加到搜索选项
    getVecCategory() {
      $http.getVecCategory().then(res => {
        if (res && res.code == 0) {
          let options = res.data.map(item => {
            return { label: item.nmCn, value: item.nmCn };
          });
          this.searchItems.normal[3].options.push(...options);
        }
      });
    },
    // 根据账号类型切换审核状态顺序
    approvalDefault() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.normal[6].default = "1"; //切换审核状态默认顺序
      }
    },
    //根据账号类型显示审核状态
    approvalStatus() {
      let username =
        sessionStorage.getItem("WHJK-USERNAME") || Cookies.get("WHJK-USERNAME");
      if (username === "admin") {
        //admin账号
        this.searchItems.normal[6].options.splice(4, 1); //隐藏'审核未通过企业'选项
      } else {
        //政府其他账号
        // this.searchItems.normal[6].options.splice(1, 1); //隐藏'未提交'选项
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      let vecPks = [],
        vecPk,
        vecNos = [],
        vecNo;
      sortParam = sortParam || {};
      this.listLoading = true;

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $http
        .getVecList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;

            for (var i = 0, len = _this.list.length; i < len; i++) {
              vecPk = _this.list[i].vecPk;
              vecNo = _this.list[i].vecNo;

              vecPks.push(vecPk);
              vecNos.push(vecNo);
            }

            // vecPks = vecPks.join(',');
            // vecNos = vecNos.join(',');
            if (vecPks.length > 0) {
              _this.getCountVecComplete(vecPks.join(",")); //车辆证照完成度
              _this.isExistBlackList(vecPks.join(","));
            }
            if (vecNos.length > 0) {
              _this.getLatestGpsTime(vecNos.join(",")); //GPS更新时间
            }

            _this.changeEntpInfo(); //企业是否审核通过
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    changeEntpInfo() {
      this.$nextTick(async () => {
        let data = this.list;
        let ids = data.map(item => item.entpPk);
        let uniqueIds = [...new Set(ids)];
		if (uniqueIds.length == 0) {
          return;
        }
        let res = await getEntpIsApprovedByIds(uniqueIds);
        let entpObj = {};
        if (res && res.code == 0) {
          res.data.forEach(item => {
            entpObj[item.ipPk] = item.isApproved;
          })

          data.forEach((item, index) => {
            let id = item.entpPk;
            if (id) {
              this.$set(this.list[index], 'ownedCompanyisApproved', entpObj[id])
            } else {
              this.$set(this.list[index], 'ownedCompanyisApproved', false)
            }
          })
        }
      })
    },
    //历史轨迹
    histroyTra() {
      let location = window.location;
      let vehicleNo = this.currentRow.vecNo || this.currentRow.trailerNo;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack?v=" +
        encodeURIComponent(vehicleNo) +
        "&t=" +
        Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },

    //单选事件
    handleSelectionChange(currentRow, oldCurrentRow) {
      this.currentRow = currentRow;
      if (currentRow && currentRow.vecPk) this.radio = currentRow.vecPk;
      else this.radio = null;
    },

    //GPS更新时间
    getLatestGpsTime(vecNos) {
      let _this = this;
      let list = this.list;
      let entpPk, pk, updateTimeStamp;

      $http
        .getLatestGpsTime(vecNos)
        .then(response => {
          if (response && response.length) {
            response.forEach((item, index) => {
              // entpPk = item.entpPk;
              // updateTimeStamp = item.updateTimeStamp;
              list.forEach((it, iindex) => {
                // pk = it.entpPk;
                // updateTimeStamp = FormatDate(updateTimeStamp,'yyyy-MM-dd HH:mm:ss');
                if (item._id === it.vecNo) {
                  // updateTimeStamp = FormatDate(updateTimeStamp, 'yyyy-MM-dd HH:mm:ss');
                  _this.$set(it, "latestGpsTime", item.updateTime); // gps最新更新时间

                  _this.$set(it, "comPany", item.downLinkNm); // gps运营商
                  _this.$set(it, "contactNm", item.contactNm); // gps运营商
                  _this.$set(it, "contactMob", item.contactMob); // gps运营商联系方式
                }
              });
            });
          }
        })
        .catch(error => { });
    },

    //获取车辆完成度
    getCountVecComplete(vecPks) {
      let _this = this;
      let list = this.list;
      let vecPk, pk, total;
      $http
        .countVecComplete(vecPks)
        .then(response => {
          if (response && response.length) {
            response.forEach((item, index) => {
              vecPk = item.vecPk;
              total = item.total;

              total *= 1; //强制转换为数值类型

              list.forEach((it, iindex) => {
                pk = it.vecPk;
                if (vecPk == pk) {
                  _this.$set(it, "completeVecRate", total);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },

    // 解聘
    fire: function (row) {
      var _this = this;
      this.$confirm("确认解聘该车辆吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          _this.listLoading = true;

          $http
            .fireVec({ ipPks: row.ipPk })
            .then(response => {
              _this.listLoading = false;
              if (response.code == 0) {
                _this.$message({
                  message: "解聘车辆成功",
                  type: "success"
                });
                _this.refreshGrid();
              } else {
                _this.$message({
                  message: response.msg,
                  type: "error"
                });
              }
            })
            .catch(error => {
              console.log(error);
              _this.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消解聘"
          });
        });
    },

    // 详情
    showDetail: function (row) {
      this.$router.push({ path: "/base/vec/info/" + row.vecPk });
    },
    // 判断车辆是否在黑名单
    isExistBlackList(vecPks) {
      let _this = this;
      let list = this.list;

      isExistBlackList({ ids: vecPks, type: "车辆" })
        .then((response) => {
          if (response.code == 0) {
            response.data.filter((it, index) => {
              list.filter((item, index) => {
                if (it.pk == item.vecPk) {
                  _this.$set(item, "isExistBlackList", it.type);
                }
              });
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    tableRowClassName(scope, rowIndex) {
      if (scope.row.isExistBlackList && scope.row.isExistBlackList == "1") {
        return "warning-row";
      }
    },
    gpsStatus(gpsTime) {
      let now = this.latest;
      let diff = (now - new Date(gpsTime).getTime()) / 1000 / 60;

      if (diff >= 60 && diff < 120) {
        return "warning";
      } else if (diff >= 120) {
        return "danger";
      } else {
        return "success";
      }
    },
    // newCapacity(){
    //   this.$router.push({path:"/base/vec/capacity"})
    // }
  }
};
</script>

<style lang="scss" scoped>
.app-main-content {
  & /deep/ .el-table {
    & tr.el-table__row.warning-row {
      background-color: #ff4d4f;
      color: #fff;

      &>td {
        background-color: #ff4d4f !important;

        >div.cell {
          >span {
            color: #fff !important;
          }
        }
      }

      .el-button {
        color: #0050b3 !important;
      }
    }

    & .warning-row.hover-row {
      background-color: #eb5c5e !important;
      color: #fff;

      &>td {
        background-color: #eb5c5e !important;
        color: #fff;
      }
    }

    & tr.warning-row.current-row {
      background-color: #f25557 !important;
      color: #fff;

      &>td {
        background-color: #f25557 !important;
        color: #fff;
      }
    }

  }
}
</style>
