<template>
  <el-dialog
    title="数字化登记记录"
    :visible.sync="dialogVisible"
    width="80%"
    top="5vh"
    :before-close="handleClose"
  >
    <div class="modal-out">
      <div class="modal-top">
        <div
          class="config-item"
          v-for="item in config"
          :key="item.cd"
          :style="{ width: `${100 / config.length}%` }"
        >
          <div class="title">{{ item.nm }}</div>
          <div class="value">
            <el-image
              v-if="item.type === 'Image'"
              style="width: 100%; height: 100%"
              :src="picMap[item.cd]"
              fit="cover"
            >
              <div slot="error" class="ck-image-slot">
                <span>无</span>
              </div>
            </el-image>
            <div v-if="item.type === 'Number' && detail" class="wt">
              {{ detail.weigh }}<span>kg</span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-bottom">
        <div class="modal-left">
          <RtePlan :rtePlan="rteplan" />
        </div>
        <div class="modal-right">
          <CheckItem :data="finalCheckItem" :column="2" :len="checkLen" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/wb";
import * as $httpRtePlan from "@/api/rtePlan";
import RtePlan from "./rte-plan.vue";
import CheckItem from "./check-item.vue";

const config = [
  { nm: "车前脸", cd: "100.001", type: "Image", sort: 0 },
  { nm: "车后脸", cd: "100.002", type: "Image", sort: 1 },
  { nm: "道闸", cd: "100.003", type: "Image", sort: 2 },
  { nm: "地磅", cd: "100.006", type: "Number", sort: 3 },
  { nm: "驾驶员人脸", cd: "100.004", type: "Image", sort: 4 },
  { nm: "押运员人脸", cd: "100.005", type: "Image", sort: 5 }
];

export default {
  components: {
    RtePlan,
    CheckItem
  },
  data() {
    return {
      dialogVisible: false,
      detail: null,
      config: config,
      checkItems: {},
      checkItem: [],
      rteplan: null,
      checkLen: 12,
      finalCheckItem: [],
      checkSuccessMap: [],
      EnumCheckResType: {
        Success: 0,
        Error: 1,
        None: 2,
        QrCode: 3
      },
      EnumCheckType: {
        Overload: "106.001",
        QRCode: "105.004"
      }
    };
  },
  created() {
    this.getCheckItems();
    this.getCheckDescr();
  },
  computed: {
    picMap() {
      const temp = {};
      try {
        const d = JSON.parse(this.detail.wtPic);
        Object.keys(d).forEach(key => {
          temp[key] = d[key].pic;
        });
      } catch (e) {}
      return temp;
    }
  },
  methods: {
    open(d) {
      this.dialogVisible = true;
      this.detail = d;
      this.getRtePlan();
      this.checkItem = this.checkItems[d.regNumber].config;
      this.setCheckItem();
    },
    handleClose() {
      this.dialogVisible = false;
      this.detail = null;
    },
    async getCheckItems() {
      const res = await $http.checkConfig();
      if (res) {
        this.checkItems = res;
      }
    },
    async getCheckDescr() {
      const res = await $http.checkDescr();
      if (res) {
        const temp = {};
        res.forEach(item => {
          Object.keys(item).forEach(key => {
            if (key !== "nm") {
              temp[key] = item[key];
            }
          });
        });
        this.checkSuccessMap = temp;
        // console.log("[ res ]", res);
      }
    },
    async getRtePlan() {
      const res = await $httpRtePlan.getRtePlanByCd(this.detail.argmtCd);
      if (res.code === 0) {
        this.rteplan = res.data[0];
      }
    },
    setCheckItem() {
      const temp = [];
      this.checkItem.forEach(item => {
        if (!item.itemList) return;
        item.itemList.forEach((ite, index) => {
          if (item.enabled && ite.enabled && item.nm && ite.nm) {
            temp.push({
              p: item.nm,
              len: index === 0 ? item.itemList.length || 0 : 0,
              res: this.EnumCheckResType.None,
              ...ite,
              nm: ite.nm,
              resValue: ""
            });
          }
        });
      });

      const tempd = JSON.parse(this.detail.wtStatus);
      const d = tempd.length ? tempd[0] : {};
      // console.log("[ d ]", this.checkItem);
      Object.keys(d).forEach(key => {
        const val = d[key];
        if (!val) return;
        temp.forEach(item => {
          if (item.cd === key) {
            (item.res =
              (key === this.EnumCheckType.QRCode
                ? this.EnumCheckResType.QrCode
                : null) ||
              (val.status === 1
                ? this.EnumCheckResType.Success
                : this.EnumCheckResType.Error)),
              (item.resValue =
                val.content ||
                (val.status === 1
                  ? this.checkSuccessMap[key].success
                  : this.checkSuccessMap[key].error));
          }
        });
      });

      console.log("[ this.finalCheckItem ]", temp);
      this.finalCheckItem = temp;
    }
  }
};
</script>

<style lang="scss" scoped>
.modal-out {
  .modal-top {
    height: 200px;
    border: 1px solid #ddd;
    border-right: 0;
    .config-item {
      height: 100%;
      border-right: 1px solid #ddd;
      float: left;
      .title {
        height: 30px;
        line-height: 30px;
        text-align: center;
      }
      .value {
        background: #000;
        border-radius: 4px;
        width: calc(100% - 20px);
        margin: 0 auto;
        height: 150px;
        position: relative;
      }
      .wt {
        line-height: 80px;
        height: 80px;
        position: absolute;
        text-align: center;
        top: 0;
        bottom: 0;
        margin: auto;
        width: 100%;
        color: #fff;
        font-size: 40px;
        span {
          font-size: 20px;
        }
      }
    }
  }
  .modal-bottom {
    overflow: hidden;
    height: 580px;
    border: 1px solid #ddd;
    border-top: 0;
    > * {
      height: 100%;
      width: 50%;
      padding: 10px;
      overflow: auto;
    }
    .modal-left {
      float: left;
      border-right: 1px solid #ddd;
    }
    .modal-right {
      float: right;
    }
  }
}
</style>

<style lang="scss">
.ck-image-slot {
  width: 100%;
  height: 100%;
  background: #eee;
  color: #999;
  position: relative;
  span {
    display: block;
    line-height: 30px;
    height: 30px;
    position: absolute;
    text-align: center;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
  }
}
</style>
