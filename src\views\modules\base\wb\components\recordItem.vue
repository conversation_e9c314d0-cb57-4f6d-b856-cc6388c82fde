<template>
  <tr>
    <td class="align-center">{{ index }}</td>
    <td><span>{{ label }}</span></td>
    <td v-if="isShowValue">
      <span v-if="type === 'label'">{{ value }}</span>
      <el-button type="text" v-else-if="type === 'img' || type === 'wash'" @click="showModal">{{ value ||
        '' }}</el-button>
      <!-- type == 3  不显示子项-->
      <template v-else-if="type === 'qr'">
        <div ref="qrcode" v-if="value" id="qrcode"></div>
      </template>
      <!-- #ZHYS-2074 若查验选择正确，则不提示数据库查验结果（即若数据库查验不通过且有查验不通过理由，但查验企业选择查验通过，则仍然不显示查验失败理由） -->
      <div v-if="error && status == 1" style="color: red; font-weight: 800;">{{ error }}</div>
    </td>
    <td align="center">
      <span v-if="status == 0" style="color: rgb(25, 183, 6); font-weight: bold">√</span>
    </td>
    <td align="center">
      <span v-if="status == 1" style="color: #d00">X</span>
    </td>
  </tr>
</template>

<script>
import QRCode from "qrcodejs2";
export default {
  props: {
    index: String,
    label: String,
    value: [String, Number],
    isShowValue: {
      type: Boolean,
      default: true,
    },
    error: String,
    type: String,
    status: {
      type: Number,
    },
    data: Object,
  },
  data() {
    return {
      qrcode: null
    };
  },
  watch: {
    value: {
      handler(val) {
        if (this.type === "qr" && val) {
          try {
            const d = JSON.parse(val);
            let codeState = d.codeState;
            let bg;
            switch (codeState) {
              case 0: //蓝码
                bg = "#0089e8";
                break;
              case 1: //黄码
                bg = "#ffc600";
                break;
              case 2: //红码
                bg = "#ff0000";
                break;
              case 99: //无码
                bg = "#cccccc";
                break;
            }
            this.$nextTick(() => {
              //创建二维码
              if (!this.$refs.qrcode) return false;
              this.$refs.qrcode.innerHTML = "";
              this.qrcode = new QRCode(this.$refs.qrcode, {
                text: d.healthScore,
                width: 50,
                height: 50,
                colorDark: bg,
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.L,
              });
            });
          } catch (e) { }
        }
      },
      immediate: true,
    },
  },
  destroyed() {
    if (this.qrcode) {
      this.qrcode.clear();
      this.qrcode = null;
    }
    this.$refs.qrcode && (this.$refs.qrcode.innerHTML = '');
  },
  methods: {
    showModal() {
      this.$emit("show", this.data);
    },
  },
};
</script>

<style lang="scss" scoped>
td {
  border: 1px solid #e2e2e2;
  padding: 10px 6px;
}
</style>
