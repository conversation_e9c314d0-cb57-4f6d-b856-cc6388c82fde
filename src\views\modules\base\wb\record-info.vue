<template>
  <div class="app-main-content" v-loading="loading">
    <div class="mod-container-oper" v-fixed>
      <el-button-group>
        <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
      </el-button-group>
    </div>
    <template v-if="error">
      <div style="color: #d00; text-align: center">{{ error }}</div>
    </template>
    <template v-else>

      <!-- <el-button type="primary" @click="printTable()" style="float: right"
        >打印</el-button
      > -->
      <!-- 新版本查验内容展示 -->
      <div class="record-detail" id="printJS-form" v-if="dataVersion === '2.0'">
        <div class="table-head">
          <h3>{{ assignmentName }}</h3>
          <table style="width: 100%">
            <tbody>
              <tr>
                <th style="float: left; font-weight: normal">
                  查验日期：<span>{{ argmtData.updTm }}</span>
                </th>
                <th style="font-weight: normal">
                  运单编号：<span>{{ argmtData.rtePlanCd }}</span>
                </th>
                <th style="float: right; font-weight: normal">
                  安全检查人员：<span>{{ argmtData.checkOperator }}</span>
                </th>
              </tr>
            </tbody>
          </table>
        </div>
        <table class="table">
          <colgroup v-if="type === 2">
            <col width="60" />
            <col />
            <col width="80" />
            <col width="80" />
          </colgroup>
          <colgroup v-else>
            <col width="60" />
            <col width="320" />
            <col />
            <col width="50" />
            <col width="50" />
          </colgroup>
          <thead>
            <template v-if="type === 2">
              <tr>
                <th rowspan="2" colspan="1">序号</th>
                <th rowspan="2" colspan="1">查验项目</th>
                <th colspan="2" class="align-center">现场查验结论</th>
              </tr>
              <tr>
                <th>正确</th>
                <th>错误</th>
              </tr>
            </template>
            <template v-else>
              <tr>
                <th>序号</th>
                <th colspan="2" align="left">查验项目</th>
                <th colspan="2">现场查验结论</th>
              </tr>
            </template>
          </thead>
          <tbody>
            <template v-for="(item, index) in templateConfig">
              <tr :key="index" class="row-title" v-if="type !== 2">
                <th colspan="1">{{ index + 1 }}</th>
                <th align="left" colspan="2">{{ item.nm }}</th>
                <th>正确</th>
                <th>错误</th>
              </tr>
                <record-item 
                 v-for="(jitem, jindex) in item.itemList"
                :key="jitem.cd" 
                :index="type === 2 ? `${jindex + 1}` : `${index + 1}.${jindex + 1}`" 
                :label="`${jitem.nm}`" 
                :type="jitem.dataType" 
                :value="compResult[jitem.cd] ? compResult[jitem.cd].data : ''"
                :isShowValue="type === 2 ? false : true" 
                :error="compResult[jitem.cd]? compResult[jitem.cd].content : ''" 
                @show="showLicModal"
                :data="jitem" :status="compResult[jitem.cd] ? compResult[jitem.cd].status : ''">
              </record-item>
            </template>
            <!-- 拍照取证 -->
            <template v-if="argmtData && (type === 0 || type === 1)">
              <tr>
                <th>{{ templateConfig.length + 1 }}</th>
                <th colspan="6" align="left">拍照取证</th>
              </tr>
              <tr v-for="(item, index) in photosEvidence" :key="index + 'img'">
                <td class="align-center">
                  {{ templateConfig.length + 1 }}.{{ index + 1 }}
                </td>
                <td>{{ item.name }}</td>
                <td colspan="5">
                  <div v-if="argmtData[item.value + 'Url']">
                    <el-image :ref="'elImg' + index" v-for="(imgitem, imgindex) in srcList[item.value + 'Url']"
                      :key="imgindex" style="width: 100px; height: 100px; margin: 10px" :src="imgitem"
                      :preview-src-list="srcList[item.value + 'Url']"></el-image>
                  </div>
                </td>
              </tr>
            </template>
            <template v-if="argmtData">
              <tr class="row-summary">
                <td class="align-center">
                  {{
                    type === 2
                    ? newAftCheckCount + 1
                    : templateConfig.length + 2
                  }}
                </td>
                <td>检查人员是否同意车辆放行</td>
                <td colspan="5">
                  检查意见：<span style="color: #d00">
                    <template v-if="argmtData.checkStatus == '0'">同意</template>
                    <template v-else-if="argmtData.checkStatus == '1'">不同意</template>
                    <template v-else></template> </span>&nbsp;&nbsp;&nbsp;&nbsp;
                  <span v-if="argmtData.checkStatus == '1'">原因：{{ argmtData.checkResult }}</span>
                </td>
              </tr>
              <tr class="row-summary">
                <td class="align-center">
                  {{
                    type === 2
                    ? newAftCheckCount + 2
                    : templateConfig.length + 3
                  }}
                </td>
                <td>检查人员</td>
                <td colspan="5">
                  <img :src="argmtData.checkSignUrl" width="150" />
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      <!-- 旧版本查验内容展示 -->
      <div class="record-detail" v-else>
        <div class="table-head">
          <h3>{{ assignmentName }}</h3>
          <ul>
            <li>查验日期：{{ argmtData.updTm }}</li>
            <!-- <li>装卸栈台/鹤位:{{ argmtData.argmtAreaNm }}</li> -->
            <li>运单编号：{{ argmtData.rtePlanCd }}</li>
            <li>安全检查人员：{{ argmtData.checkOperator }}</li>
          </ul>
        </div>
        <template v-if="type === 0 || type === 1">
          <!-- 装货前检查 -->
          <table class="table">
            <colgroup>
              <col width="60" />
              <col width="320" />
              <col />
              <col width="50" />
              <col width="50" />
            </colgroup>
            <thead>
              <tr>
                <th>序号</th>
                <th colspan="2" align="left">查验项目</th>
                <th colspan="2">安全检查结果</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in settingConfig">
                <tr :key="index" class="row-title">
                  <th colspan="1">{{ index + 1 }}</th>
                  <th align="left" colspan="2">{{ item.name }}</th>
                  <th>正确</th>
                  <th>错误</th>
                </tr>
                <template v-for="(jitem, jindex) in item.message">
                  <template v-if="jitem.children && jitem.children.length">
                    <tr :key="index + '-' + jindex">
                      <td :rowspan="jitem.children.length" class="align-center">
                        {{ index + 1 }}.{{ jindex + 1 }}
                      </td>
                      <td :rowspan="jitem.children.length">{{ jitem.name }}</td>
                      <td>
                        {{ jitem.children[0].name }}:
                        <span v-if="jitem.children[0].type == 0">
                          {{ argmtData[jitem.children[0].text] }}
                        </span>
                        <el-button v-if="jitem.children[0].type == 2" type="text"
                          @click="showLicModal(jitem.children[0])">{{ argmtData[jitem.children[0].text] }}
                        </el-button>
                      </td>
                      <td :rowspan="jitem.children.length" align="center">
                        <span v-if="compResult[jitem.text] == 1"
                          style="color: rgb(25, 183, 6); font-weight: bold">√</span>
                      </td>
                      <td :rowspan="jitem.children.length" align="center">
                        <span v-if="compResult[jitem.text] == 2" style="color: #d00">X</span>
                      </td>
                    </tr>
                    <!-- 合并单元格 -->
                    <template v-for="(kitem, kindex) in jitem.children">
                      <tr v-if="kindex > 0" :key="kitem.text + '_' + kindex">
                        <td>
                          {{ kitem.name }}:
                          <span v-if="kitem.type == 0">{{
                            argmtData[kitem.text]
                          }}</span>
                          <el-button v-if="kitem.type == 2" type="text" @click="showLicModal(kitem)">{{
                            argmtData[kitem.text] }}</el-button>
                        </td>
                      </tr>
                    </template>
                  </template>
                  <template v-else>
                    <tr :key="index + '-' + jindex">
                      <td class="align-center">
                        {{ index + 1 }}.{{ jindex + 1 }}
                      </td>
                      <td>{{ jitem.name }}</td>
                      <td v-if="jitem.type == 0">
                        <span v-if="jitem.text == 'enchQty'">{{
                          argmtData[jitem.text] * 1000
                        }}</span>
                        <span v-else>{{ argmtData[jitem.text] }}</span>
                      </td>
                      <td v-if="jitem.type == 1">
                        <el-button type="text" @click="showLicModal(jitem)">{{
                          jitem.desc
                        }}</el-button>
                      </td>
                      <td v-if="jitem.type == 2">
                        <el-button type="text" @click="showLicModal(jitem)">{{
                          argmtData[jitem.text]
                        }}</el-button>
                      </td>
                      <td v-if="jitem.type == 4">
                        <div ref="dvQrcode" align="center"></div>
                      </td>
                      <td class="align-center">
                        <span v-if="compResult[jitem.text] == 1"
                          style="color: rgb(25, 183, 6); font-weight: bold">√</span>
                      </td>
                      <td class="align-center">
                        <span v-if="compResult[jitem.text] == 2" style="color: #d00">X</span>
                      </td>
                    </tr>
                  </template>
                </template>
              </template>
              <!-- 拍照取证 -->
              <tr>
                <th>{{ settingConfig.length + 1 }}</th>
                <th colspan="6" align="left">拍照取证</th>
              </tr>
              <tr v-for="(item, index) in photosEvidence" :key="index + 'img'">
                <td class="align-center">
                  {{ settingConfig.length + 1 }}.{{ index + 1 }}
                </td>
                <td>{{ item.name }}</td>
                <td colspan="5">
                  <div v-if="argmtData[item.value + 'Url']" v-viewer>
                    <el-image :ref="'elImg' + index" v-for="(imgitem, imgindex) in argmtData[
                      item.value + 'Url'
                    ].split(',')" :key="imgindex" style="width: 100px; height: 100px; margin: 10px" :src="imgitem"
                      :preview-src-list="srcList"></el-image>
                    <!-- <img
                      v-for="(imgitem, imgindex) in argmtData[
                        item.value + 'Url'
                      ].split(',')"
                      :key="imgindex"
                      style="width: 100px; height: 100px; margin: 10px"
                      :src="imgitem"
                    /> -->
                  </div>
                </td>
              </tr>
              <tr class="row-summary">
                <td class="align-center">{{ settingConfig.length + 2 }}</td>
                <td>检查人员是否同意车辆放行</td>
                <td colspan="5">
                  检查意见：<span style="color: #d00">
                    <template v-if="argmtData.checkStatus == '0'">同意</template>
                    <template v-else-if="argmtData.checkStatus == '1'">不同意</template>
                    <template v-else></template> </span>&nbsp;&nbsp;&nbsp;&nbsp;
                  <span v-if="argmtData.checkStatus == '1'">原因：{{ argmtData.checkResult }}</span>
                </td>
              </tr>
              <tr class="row-summary">
                <td class="align-center">{{ settingConfig.length + 3 }}</td>
                <td>检查人员</td>
                <td colspan="5">
                  <img :src="argmtData.checkSignUrl" width="150" />
                </td>
              </tr>
            </tbody>
          </table>
        </template>

        <template v-else-if="type === 2">
          <!-- 装货后检查 -->
          <table class="table">
            <thead>
              <tr>
                <th rowspan="2" width="40">序号</th>
                <th rowspan="2">交付查验项目</th>
                <th colspan="2" width="160" class="align-center">
                  现场查验结论
                </th>
              </tr>
              <tr>
                <th width="80">正确</th>
                <th width="80">错误</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in settingConfig">
                <tr :key="index">
                  <td class="align-center">{{ index + 1 }}</td>
                  <td>{{ item.name }}</td>
                  <td class="align-center">
                    <span v-if="argmtData[item.text] == 1" style="color: rgb(25, 183, 6); font-weight: bold">√</span>
                  </td>
                  <td class="align-center">
                    <span v-if="argmtData[item.text] == 2" style="color: #d00">X</span>
                  </td>
                </tr>
              </template>
              <tr class="row-summary">
                <td class="align-center">{{ settingConfig.length + 1 }}</td>
                <td>检查人员是否同意车辆放行</td>
                <td colspan="5">
                  <div>
                    检查意见：<span style="color: #d00; font-weight: bold">
                      <template v-if="argmtData.checkStatus == '0'">同意</template>
                      <template v-else-if="argmtData.checkStatus == '1'">不同意</template>
                      <template v-else></template></span>
                  </div>
                  <div v-if="argmtData.checkStatus == '1'">
                    原因：<span style="color: #d00; font-weight: bold">{{
                      argmtData.checkResult
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr class="row-summary">
                <td class="align-center">{{ settingConfig.length + 2 }}</td>
                <td>检查人员</td>
                <td colspan="5">
                  <img :src="argmtData.checkSignUrl" width="150" />
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
    </template>
    <el-dialog title="图片预览" append-to-body :visible.sync="dialogVisible">
      <div v-if="licData.length > 0">
      <!-- <div v-if="licData.length > 0" class="images" v-viewer v-loading="licLoading" element-loading-text="图片加载中"
        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)"> -->
        <template v-for="src in licData">
          <span class="pdf-wrap" :style="{ width: 100 / licData.length + '%', 'height':'400px'  }" v-if="isPdf(src)" @click="showPdf(src)">
            <svg-icon icon-class="pdf" class-name="svg-icon" ></svg-icon>
          </span>
          <img v-else :style="{ width: 100 / licData.length + '%', 'height':'400px' }" :key="src" :src="src" />
        </template>
      <!-- </div> -->
      </div>
      <div v-else>
        <div v-if="licLoading" class="images" v-viewer v-loading="licLoading" element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)"></div>
        <p v-else>暂无图片</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!--  pdf预览  -->
    <pdf-view :zIndex="10001" v-show="pdfViewVisible" :src="pdfSrc" @filePreviewCancel="filePreviewCancel"></pdf-view>
  </div>
</template>

<script>
import * as API from "@/api/record";
import QRCode from "qrcodejs2";
import { before, after, unload } from "@/utils/checkItemsConfig.js";
import { formatDate } from "@/utils/tool.js";
import * as $httpVec from "@/api/shipment/record/vec.js";
import * as $httpEntp from "@/api/shipment/record/entp.js";
import * as $httpPers from "@/api/shipment/record/person.js";
import * as $httpTank from "@/api/shipment/record/tank.js";
import recordItem from "./components/recordItem";
import pdfView from "@/components/pdf-view"

export default {
  components: {
    recordItem,
    pdfView
  },
  data() {
    return {
      loading: false,
      error: null,

      licLoading: false,
      dialogVisible: false,
      settingConfig: before,
      templateConfig: [],
      argmtData: {},
      type: 0, // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      year:"",
      assignmentName: "装货前检查",

      compResult: {}, // 存储所有比对结果

      dataVersion: "2.0",
      photosEvidence: [
        {
          name: "车辆正面照片（牵引车牌照及顶灯可见）",
          value: "vecPosi",
          cd: "300.001"
        },
        {
          name: "车辆后面照片（挂车牌照及菱形牌可见）",
          value: "vecNega",
          cd: "300.002"
        },
        {
          name: "车辆侧面照片（介质名称及菱形牌可见）",
          value: "vecSide",
          cd: "300.003"
        },
        {
          name: "驾驶员正面照片和从业资格证照片",
          value: "dvCert",
          cd: "300.004"
        },
        {
          name: "押运员正面照片和从业资格证照片",
          value: "scCert",
          cd: "300.005"
        }
      ],
      srcList: [],
      licData: [],
      pdfViewVisible: false,
      pdfSrc: ""
    };
  },
  computed: {
    newAftCheckCount() {
      if (this.templateConfig.length === 0) return 0;
      return this.templateConfig[0].itemList.length;
    }
  },
  created() {
    const recordId = this.$route.params.id,
      type = this.$route.query.type,
      year = this.$route.query.year;
    
    this.init(recordId, type, year);
  },
  mounted() { },
  destroyed() {
    this.$refs.dvQrcode && (this.$refs.dvQrcode.innerHTML = "");
  },
  methods: {
    filePreviewCancel(){
      this.pdfViewVisible = false;
      this.pdfSrc = "";
    },
    showPdf(url){
      this.pdfSrc = url;
      this.pdfViewVisible = true;
    },
    isPdf(src){
     return /.pdf?$/.test(src)
    },
    initStart() {
      // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      if (this.type === 2) {
        this.assignmentName = "装货后检查";
        this.settingConfig = after;
      } else {
        if (this.type === 0) {
          this.assignmentName = "装货前检查";
          this.settingConfig = before;
        } else if (this.type === 1) {
          this.assignmentName = "卸货前检查";
          this.settingConfig = unload;
        }
      }
      this.recordId && this.type != undefined && this.getArgmtCheckData();
    },
    _initByCd() {
      // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
      if (this.type === 2) {
        this.assignmentName = "装货后检查";
        this.settingConfig = after;
      } else {
        if (this.type === 0) {
          this.assignmentName = "装货前检查";
          this.settingConfig = before;
        } else if (this.type === 1) {
          this.assignmentName = "卸货前检查";
          this.settingConfig = unload;
        }
      }
      this.recordId && this.type != undefined && this.getArgmtCheckDataByCd();
    },
    init(recordId, type, year) {
      if (
        !recordId ||
        !(
          type === 0 ||
          type === 1 ||
          type === 2 ||
          type === "0" ||
          type === "1" ||
          type === "2"
        )
      ) {
        return;
      }
      this.recordId = recordId;
      this.year = year;
      this.type = parseInt(type);
      this.initStart();
    },
    initByCd(recordId, type, year) {
      if (
        !recordId ||
        !(
          type === 0 ||
          type === 1 ||
          type === 2 ||
          type === "0" ||
          type === "1" ||
          type === "2"
        )
      ) {
        return;
      }
      this.recordId = recordId;
      this.type = parseInt(type);
      if(year){
        this.year = year;
      }
      this._initByCd();
    },

    //渲染二维码
    showLicModal(data) {
      this.licData = [];
      if (this.dataVersion === "2.0") {
        this.checkLic(data);
      } else {
        this.getType(data.licName);
      }
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 新版本查询证照
    async checkLic(d) {
      let result = this.compResult[d.cd];
      if (!result) {
        return;
      }
      let _this = this;
      let lic = Object.assign({}, result.licParamVO);
      const record = result.record;
      if (lic) {
        lic.type = lic.entityType;
        delete lic.entityType;
        this.dialogVisible = true;
        this.licLoading = true;
        const res = await API.checkLic(lic);
        this.licLoading = false;
        if (res.code === 0 && res.data && res.data.rsrc) {
          let licData = res.data.rsrc.map(
            item => item.waterMarkUrl || item.url || ""
          );
          licData = licData.filter(it => (it ? true : false));
          if (licData && licData.length) {
            
            var img = new Image();
            img.src = licData[0];

            img.onload = function () {
              _this.licLoading = false;
              img = null;
            };
            this.$set(this, "licData", licData);
            
          }
        }
      }
      if (record) {
        this.record = record;
      }
    },
    //查找对应的证照信息
    getType(catCd) {
      if (catCd) {
        let http = null,
          pk = null;
        if (catCd.indexOf("-1") > -1 || catCd.indexOf("-2") > -1) {
          //-1牵引车/-2挂车
          http = $httpVec;
          pk =
            catCd.indexOf("-1") > -1
              ? this.argmtData.tracId
              : this.argmtData.traiId;
        }
        if (catCd.indexOf("-3") > -1 || catCd.indexOf("-4") > -1) {
          //-3驾驶员/-4押运员
          http = $httpPers;
          pk =
            catCd.indexOf("-3") > -1
              ? this.argmtData.dvId
              : this.argmtData.scId;
        }
        if (catCd.indexOf("8010.50") > -1) {
          //罐体
          http = $httpTank;
          pk = this.argmtData.cntrId;
        }
        if (catCd.indexOf("8010.20") > -1) {
          //企业
          http = $httpEntp;
          pk = this.argmtData.carrId;
        }

        this.getLicInfo(http, pk, catCd);
      } else {
        this.$message({
          type: "error",
          message: "对不起，暂时无法获取该证件信息"
        });
      }
    },
    // 获取证件详情
    getLicInfo(http, id, catCd) {
      let _this = this;
      if (id) {
        http
          .getInfo(id)
          .then(res => {
            if (res.code == 0) {
              let licdata = [],
                items = res.data.items,
                subItems = null;
              for (var i = 0, len = items.length; i < len; i++) {
                if (catCd.indexOf(items[i].licCatCd) > -1) {
                  subItems = items[i].subItems;
                  break;
                }
                continue;
              }

              subItems.forEach(item => {
                if (item.url) {
                  licdata.push(item.url);
                }
              });

              if (licdata && licdata[0]) {
                this.licLoading = true;
                var img = new Image();
                img.src = licdata[0];

                img.onload = function () {
                  _this.licLoading = false;
                  img = null;
                };
              }

              this.licdata = licdata;
              this.$set(this, "licData", licdata);
              this.dialogVisible = true;
              // this.$refs.elImg.clickHandler();
            } else {
            }
          })
          .catch(err => {
            this.licLoading = false;
          });
      } else {
        this.$message({
          type: "error",
          message: "对不起，无法获取该证件信息"
        });
      }
    },
    // 获取查验结果数据
    getArgmtCheckData() {
      this.loading = true;
      // 获取查验结果数据
      let api;
      let type = this.type;
      let year = this.year;
      let param = null;
      const _this = this;

      if (type === 2) {
        // 装货后检查
        if(this.year){
          api = API.getAftHisDtlById;
          param = {
            id:this.recordId,
            year:year
          }
        }else{
          api = API.getArgmtaftcheckDtl;
          param = this.recordId;
        }
      } else {
        if(this.year){
          api = API.getBefHisDtlById;
          param = {
            id:this.recordId,
            year:year
          }
        }else{
          api = API.getArgmtbefcheckDtl;
          param = this.recordId;
        }
      }

      api &&
        api(param)
          .then(res => {
            _this.loading = false;
            _this.error = null;
            if (res.code == 0 && res.data) {
              // 判断是新版本数据还是老版本数据
              // dataVersion等于2.0为新
              _this.dataVersion = res.data.dataVersion;
              _this.$set(_this, "argmtData", {
                ..._this.argmtData,
                ...res.data
              });

              let compResult = JSON.parse(res.data.compResult || "{}");
              _this.$set(_this, "compResult", compResult);
              [
                "tracOpraLicExpiDt",
                "tracOpraLicLvlExpiDt",
                "tracDrivLicExpiDt",
                "traiOpraLicExpiDt",
                "traiDrivLicExpiDt"
              ].forEach(function (item) {
                if (_this.argmtData[item]) {
                  _this.argmtData[item] = formatDate(
                    _this.argmtData[item],
                    "yyyy-MM-dd"
                  );
                }
              });
              // 如果为新版本采用后台咧列表模板
              if (res.data.dataVersion === "2.0") {

                // 新版本拍照
                // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查
                if (type === 0 || type === '0' || type === 1 || type === '1') {
                  _this.photosEvidence.forEach((item, index) => {
                    let compResultVal = compResult[item.cd];
                    _this.$set(
                      _this.argmtData,
                      item.value + "Url",
                      compResultVal ? compResultVal.data : ""
                    );
                    if (_this.argmtData[item.value + "Url"]) {
                      _this.srcList[item.value + "Url"] = _this.argmtData[
                        item.value + "Url"
                      ].split(",");
                    }
                  });
                }
                // 生成查验结果列表模板
                let configMap = {
                  0: "Check0001", // 装货前
                  1: "Check0003", // 卸货前
                  2: "Check0002" // 装货后
                };

                let checkConfApi;
                let param = {
                  orderNo: res.data.rtePlanCd,
                  checkType: configMap[type + ""]
                };
                
                // 获取历史查验配置模板
                if(this.year){
                  checkConfApi = API.hisCheckConfig;
                  param.year = this.year;
                }else{
                  checkConfApi = API.checkConfig
                }
                
                checkConfApi(param)
                  .then(res2 => {
                    // 根据 type 字段给定模板类型
                    let config = res2.data.list;
                    _this.$set(_this, "templateConfig", config);
                  })
                  .catch(err => { });
              } else {
                // 旧版本拍照
                _this.photosEvidence.forEach((item, index) => {
                  if (_this.argmtData[item.value + "Url"]) {
                    _this.srcList = _this.srcList.concat(
                      (_this.srcList[item.value + "Url"] = _this.argmtData[
                        item.value + "Url"
                      ].split(","))
                    );
                  }
                });
                _this.drawQRcode(_this.argmtData); //显示人员安全码
              }
            } else {
              _this.error = res.data.msg;
              _this.$set(_this, "argmtData", {});
            }
          })
          .catch((e) => {
            this.loading = false;
            this.error = "查验记录数据获取失败，请联系管理员！";
          });
    },
    getArgmtCheckDataByCd() {
      this.loading = true;
      // 获取查验结果数据
      let api;
      let type = this.type;
      let year = this.year;
      let param = null;
      if(year){
        param = {
          id: this.recordId,
          year: year
        }
        if (type === 2) {
          // 装货后检查
          api = API.getAftHisDtlById;
        } else {
          api = API.getBefHisDtlById;
        }

      }else{
        if (type === 2) {
          // 装货后检查
          api = API.getArgmtaftcheckDtlByCd;
        } else {
          api = API.getArgmtbefcheckDtlByCd;
        }
        param = this.recordId;
      }
      
      api &&
        api(param)
          .then(res => {
            this.loading = false;
            this.error = null;
            if (res.code == 0 && res.data) {
              // 判断是新版本数据还是老版本数据
              // dataVersion等于2.0为新
              this.dataVersion = res.data.dataVersion;
              const _this = this;

              this.$set(this, "argmtData", {
                ...this.argmtData,
                ...res.data
              });

              let compResult = JSON.parse(res.data.compResult || "{}");
              this.$set(this, "compResult", compResult);
              [
                "tracOpraLicExpiDt",
                "tracOpraLicLvlExpiDt",
                "tracDrivLicExpiDt",
                "traiOpraLicExpiDt",
                "traiDrivLicExpiDt"
              ].forEach(function (item) {
                if (_this.argmtData[item]) {
                  _this.argmtData[item] = formatDate(
                    _this.argmtData[item],
                    "yyyy-MM-dd"
                  );
                }
              });

              // 如果为新版本采用后台咧列表模板
              if (res.data.dataVersion === "2.0") {
                // 新版本拍照
                // type=0:装货前检查,type=1:卸货前检查,type=2:装货后检查

                (type === 0 || type === 1) &&
                  this.photosEvidence.forEach((item, index) => {
                    let compResultVal = compResult[item.cd];
                    _this.$set(
                      this.argmtData,
                      item.value + "Url",
                      compResultVal?compResultVal.data:""
                    );
                    if (_this.argmtData[item.value + "Url"]) {
                      this.srcList[item.value + "Url"] = _this.argmtData[
                        item.value + "Url"
                      ].split(",");
                    }
                  });
                // 生成查验结果列表模板
                let configMap = {
                  0: "Check0001", // 装货前
                  1: "Check0003", // 卸货前
                  2: "Check0002" // 装货后
                };
                let param = {
                  orderNo: res.data.rtePlanCd,
                  checkType: configMap[type + ""]
                };
                API.checkConfig(param)
                  .then(res2 => {
                    // 根据 type 字段给定模板类型
                    let config = res2.data.list;
                    this.$set(this, "templateConfig", config);
                  })
                  .catch(err => { });
              } else {
                // 旧版本拍照
                this.photosEvidence.forEach((item, index) => {
                  if (_this.argmtData[item.value + "Url"]) {
                    this.srcList = this.srcList.concat(
                      (this.srcList[item.value + "Url"] = _this.argmtData[
                        item.value + "Url"
                      ].split(","))
                    );
                  }
                });
                this.drawQRcode(this.argmtData); //显示人员安全码
              }
            } else {
              this.error = res.data.msg;
              this.$set(this, "argmtData", {});
            }
          })
          .catch(() => {
            this.loading = false;
            this.error = "查验记录数据获取失败，请联系管理员！";
          });
    },
    //渲染二维码
    drawQRcode(data) {
      if (!data.securityCode) return false;
      nm = data.dvNm;
      let CodeColor = "";

      let cd = data.dvCd.substring(0, data.dvCd.length - 4) + "****";

      switch (data.securityCode) {
        case 0: //蓝码
          CodeColor = "#0089e8";
          break;
        case 1: //黄码
          CodeColor = "#ffc600";
          break;
        case 2: //红码
          CodeColor = "#ff0000";
          break;
        case 99: //无码
          CodeColor = "#cccccc";
          break;
      }
      this.$nextTick(() => {
        //创建二维码
        if (!this.$refs.dvQrcode) return false;
        this.$refs.dvQrcode.innerHTML = "";
        new QRCode(this.$refs.dvQrcode, {
          text: nm + " " + cd,
          width: 50,
          height: 50,
          colorDark: CodeColor,
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L
        });
      });
    },
    //打印
    printTable() {
      printJS({
        printable: "printJS-form",
        type: "html",
        targetStyles: ["*"]
      });
    }
  }
};
</script>

<style scoped lang="scss">
table.table {
  width: 100%;
  font-size: 14px;

  th,
  td {
    border: 1px solid #e2e2e2;
    padding: 10px 6px;
  }

  thead {
    th {
      background: #cdcfe0;
      border-color: #cdcfe0;
      color: #2f3566;
    }
  }

  th {
    background: #cdcfe0;
    border-color: #cdcfe0;
    color: #2f3566;
  }
}

.table-head {
  margin-bottom: 5px;

  h3 {
    text-align: center;
    font-size: 24px;
    // font-weight: normal;
  }

  ul {
    overflow: hidden;
    font-size: 14px;
    width: 100%;
    padding: 0;

    li {
      float: left;
      // width: 25%;
      width: 33.333%;
      text-align: center;

      >span {
        font-weight: bold;
      }

      &:first-child {
        text-align: left;
      }

      &:last-child {
        text-align: right;
      }
    }
  }
}

.isCorrect {
  color: green;
  font-size: 18px;
  font-family: Arial, Helvetica, sans-serif;
}

.isWrong {
  color: red;
  font-size: 24px;
  font-family: Arial, Helvetica, sans-serif;
}

#dvQrcode {
  width: 70px;
  height: 70px;
}

/deep/ {
  .el-button--text {
    white-space: normal;
    text-align: left;
  }
}

.pdf-wrap{
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  border: 1px solid #ccc;
  line-height: 400px;

  .svg-icon{
    font-size: 120px;
  }
}
</style>
