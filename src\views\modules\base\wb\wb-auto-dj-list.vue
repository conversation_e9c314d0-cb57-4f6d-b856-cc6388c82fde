<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable" style="width: 100%" :max-height="tableHeight"
      @sort-change="handleSort">
      <!-- <el-table-column fixed="left" width="50">
        <template slot-scope="scope">
          <el-radio v-model="radio" :label="scope.row.argmtWtPk">&nbsp;</el-radio>
        </template>
      </el-table-column> -->
      <el-table-column prop="regPot" label="登记点" min-width="180"></el-table-column>
      <el-table-column prop="wtTm" label="时间" min-width="180"></el-table-column>
      <el-table-column prop="cd" label="牵引车" min-width="90"></el-table-column>
      <el-table-column prop="traiCd" label="挂车号" min-width="90"></el-table-column>
      <el-table-column prop="dvNm" label="驾驶员" min-width="90"></el-table-column>
      <el-table-column prop="scNm" label="押运员" min-width="90"></el-table-column>
      <el-table-column prop="goodsNm" label="货物" min-width="220"></el-table-column>
      <el-table-column prop="weigh" label="过磅重量(KG)" min-width="120"></el-table-column>
      <el-table-column label="操作" min-width="120" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button class="cell-btn el-icon-document" type="text" size="mini" plain title="查看登记详情" @click="showRecord(scope.row)">详情</el-button>
          <el-button class="cell-btn el-icon-location" type="text" size="mini" plain title="查看历史轨迹" @click="histroyTrace(scope.row)">轨迹</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <!-- <div class="grid-operbar ft-lf">
        <el-button type="primary" size="small" :disabled="radio == null" v-on:click="histroyTrace">历史轨迹</el-button>
      </div> -->
      <el-pagination background layout="sizes, prev, next" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right">
      </el-pagination>
    </div>

    <CkModal ref="ckModalRef"/>

  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { getCKList } from "@/api/wb";
import { getFuzzyTracCd } from "@/api/vec";
import { getVideoList } from "@/api/video";
import CkModal from "./components/ck-modal";

export default {
  components: {
    Searchbar,
    CkModal
  },
  data: function() {
    return {
      placeType: "",
      showList: true,
      tableHeight: 500,
      title: null,
      showQuery: false,
      currentRow: null,
      radio: null,
      listLoading: false,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      searchItems: {
        normal: [
          {
            name: "登记点",
            field: "regPot",
            type: "filterselect",
            dbfield: "reg_pot",
            dboper: "cn",
            options: []
          },
          {
            name: "运单号",
            field: "argmtCd",
            type: "text",
            dbfield: "argmt_cd",
            dboper: "eq"
          },
          {
            name: "牵引车",
            field: "cd",
            type: "fuzzy",
            dbfield: "cd",
            dboper: "eq",
            api: this.getTracCd
          },
          {
            name: "挂车号",
            field: "traiCd",
            type: "fuzzy",
            dbfield: "trai_cd",
            dboper: "eq",
            api: this.getTraiCd
          }, // 省市区
          {
            name: "登记时间",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            field: "wtTm",
            type: "daterange",
            dbfield: "wt_tm",
            dboper: "bt"
          }
        ],
        more: [
          {
            name: "驾驶员",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn"
          },
          {
            name: "押运员",
            field: "scNm",
            type: "text",
            dbfield: "sc_nm",
            dboper: "cn"
          },
          // { name: '运输企业名称', field: 'entpNmCn', type: 'text', dbfield: 'entp_nm_cn', dboper: 'cn' },
          {
            name: "货物",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn"
          },
          // {
          //   name: "充装类型",
          //   field: "icCd",
          //   type: "select",
          //   options: [
          //     { label: "所有充装类型", value: "" },
          //     { label: "充装", value: "1" },
          //     { label: "卸货", value: "-1" },
          //     { label: "途经登记", value: "0" }
          //   ],
          //   dbfield: "ic_cd",
          //   dboper: "eq"
          // },
          {
            name: "起运地",
            field: "csnorWhseDistCd",
            type: "region",
            dbfield: "csnor_whse_dist_cd",
            dboper: "cn"
          }, // 省市区
          {
            name: "目的地",
            field: "csneeWhseDistCd",
            type: "region",
            dbfield: "csnee_whse_dist_cd",
            dboper: "cn"
          }
        ]
      },
      defaultSearchItems: [],
      pagination: {
        page: 1,
        limit: 15
      },
      weightDate: "",
    };
  },
  created() {
    let filters = {
      groupOp: "AND",
      rules: []
    };
    // filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
    // console.log(params);
    // 登记点列表
    let _this = this;
    getVideoList({ page: 1, limit: 999 })
      .then(res => {
        if (res && res.code === 0) {
          _this.videoList = res.page.list;
          this.searchItems.normal[0].options = res.page.list.map(
            (item, index) => {
              return {
                label: item.routeName,
                value: item.routeName
              };
            }
          );
        }
      })
      .catch(error => {
        console.log(error);
      });
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    showQueryPanel: function() {
      this.showQuery = !this.showQuery;
    },
    //历史轨迹
    histroyTrace(row) {
      let location = window.location;
      // let vehicleNo = this.currentRow.cd || this.currentRow.traiCd;
      let vehicleNo = row.cd || row.traiCd;
      window.open(
        location.origin +
          location.pathname +
          "#/monit/hisTrack?v=" +
          encodeURIComponent(vehicleNo) +
          "&t=" +
          Tool.formatDate(new Date(row.wtTm), "yyyy-MM-dd"),
        "_blank"
      );
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    getList: function(data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      this.radio = null; //清空单选
      //关闭loading
      this.listLoading = true;
      //查询列表
      getCKList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list || [];
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    showRecord(d) {
      this.$refs.ckModalRef.open(d)
    }
  }
};
</script>

<style scoped>
.el-table .cell,
.el-table th > div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
