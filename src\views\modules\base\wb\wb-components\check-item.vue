<template>
  <div class="check-table-box">
    <div class="check-title" style="line-height: 28px">自动查验信息</div>
    <el-table
      border
      :span-method="arraySpanMethod"
      size="small"
      v-for="(item, index) in myData"
      :key="index"
      :data="item"
      class="check-table"
      :style="{ width: ` calc(${100 / column}% - ${(column - 1) * 10}px)` }"
    >
      <el-tableColumn prop="p" label="名称"></el-tableColumn>
      <el-tableColumn prop="nm" label="查验项">
        <template slot-scope="{ row }">
          <span v-if="row.must">*</span>
          <span>{{ row.nm }}</span>
        </template>
      </el-tableColumn>
      <el-tableColumn prop="res" label="结果">
        <template slot-scope="{ row }">
          <span class="ok-text" v-if="row.res === EnumCheckResType.Success">
            <div v-if="row.nm === '安全码'" ref="qrRef"></div>
            <div v-else>{{ row.resValue }}</div>
          </span>
          <span
            class="error-text"
            v-else-if="row.res === EnumCheckResType.Error"
          >
            {{ row.resValue }}
          </span>
          <span v-else>无</span>
          <!-- <div
            v-show="row.res == EnumCheckResType.Success && row.nm === '安全码'"
            ref="qrRef"
            style="color: #67c23a"
          >
            通过
          </div> -->
          <!-- <div v-show="row.res == EnumCheckResType.Success && row.nm === '安全码'" ref="qrRef"></div> -->
        </template>
      </el-tableColumn>
    </el-table>
  </div>
</template>

<script>
import lodash from "lodash";
import QrCodeStyling from "qr-code-styling";

export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    column: {
      type: Number,
      default: 1,
    },
    len: {
      type: Number,
      default: 16,
    },
  },
  data() {
    return {
      EnumCheckResType: {
        Success: 0,
        Error: 1,
        None: 2,
        QrCode: 3,
      },
    };
  },
  computed: {
    myData() {
      const d = lodash.cloneDeep(this.data);
      // const singleLen = this.len;
      const singleLen = this.data.length > 24 ? this.data.length / 2 : this.len;

      const tempData = [];
      let tempLen = 0;
      let fixIndex = 0;
      let tempArr = [];
      d.forEach((item, index) => {
        if (d[fixIndex].p === item.p) {
          tempLen += 1;
        } else {
          d[fixIndex].len = tempLen;
          tempLen = 1;
          fixIndex = index;
        }
        tempArr.push(item);
        if (tempArr.length >= singleLen || index >= d.length - 1) {
          tempData.push(tempArr);
          tempArr = [];
          d[fixIndex].len = tempLen;
          tempLen = 0;
          fixIndex = index + 1;
        }
      });
      return tempData;
    },
  },

  watch: {
    myData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        const colorMap = {
          0: "#0089e8",
          1: "#ffc600",
          2: "#ff0000",
          99: "#cccccc",
        };
        const qrDataTemp = this.data.filter((item) => item.nm === "安全码")[0]
          .resValue;

        try {
          const qrData = JSON.parse(qrDataTemp);
          if (!qrData) return;

          this.$nextTick(function () {
            let dom = this.$refs.qrRef;

            if (Array.isArray(this.$refs.qrRef)) dom = this.$refs.qrRef[0];
            if (dom) {
              dom.innerHTML = "";
              const qr = new QrCodeStyling({
                width: 50,
                height: 50,
                type: "svg",
                data: qrData.idNo,
                dotsOptions: {
                  color: colorMap[qrData.codeState],
                  type: "square",
                },
                backgroundOptions: {
                  color: "#fff",
                },
              });
              qr.append(dom);
            }
          });
        } catch (e) {}
      },
    },
  },
  methods: {
    arraySpanMethod({ row, columnIndex }) {
      if (columnIndex === 0) {
        if (row.len) {
          return {
            rowspan: row.len,
            colspan: 1,
          };
        }
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.check-table-box {
  overflow: hidden;

  .check-table {
    float: left;

    + .check-table {
      margin-left: 10px;
    }
  }
}

.error-text {
  color: #f56c6c;
  font-weight: 800;
}

.ok-text {
  color: #67c23a;
  font-weight: 800;
}
</style>
