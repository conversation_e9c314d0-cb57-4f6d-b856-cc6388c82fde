<!--  -->
<template>
  <div class="app-main-content" style="position: relative">
    <div class="print-panel-body">
      <div class="print-panel-body_flexwrap">
        <div class="left-table">
          <table class="custom-table" cellspacing="0" cellpadding="0">
            <tbody v-if="passPort">
              <tr>
                <th>牵引车</th>
                <td>{{ passPort.vecNo }}</td>
              </tr>
              <tr>
                <th>企业</th>
                <td>{{ passPort.entpNmCn }}</td>
              </tr>
              <tr>
                <th>路线</th>
                <td>{{ passPort.route }}</td>
              </tr>
              <tr>
                <th>通行证类型</th>
                <td>{{ passPort.catNmCn }}</td>
              </tr>
              <tr>
                <th>通行证有效期</th>
                <td>{{ passPort.vldTo }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    passPort: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
::v-deep {
  .app-main-content {
    height: calc(100% - 30px);
  }
}
.custom-table {
  th {
    width: 15%;
    text-align: center;
  }
}
</style>
