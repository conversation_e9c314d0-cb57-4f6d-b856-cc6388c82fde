<template>
  <div class="rte-plan-box" v-if="rtePlan">
    <el-steps align-center :active="activeIndex" process-status="wait" finish-status="finish">
      <el-step>
        <span slot="icon">
          <el-popover trigger="hover" slot="icon">
            <span slot="reference">发</span>
            <div shadow="never" class="card-item">
              <p>发车</p>
              <p>时间：{{ rtePlan.goTm || "" }}</p>
              <p>操作人：{{ rtePlan.dvNm || "" }}</p>
              <p :title="rtePlan.goAddr || ''">位置：{{ rtePlan.goAddr || "" }}</p>
              <p>备注：{{ "发车提货" }}</p>
            </div>
          </el-popover>
        </span>
      </el-step>
      <el-step>
        <span slot="icon">
          <el-popover trigger="hover" slot="icon">
            <span slot="reference">装</span>
            <div shadow="never" class="card-item">
              <p>装货</p>
              <p>时间：{{ rtePlan.loadTm || "" }}</p>
              <p>操作人：{{ rtePlan.dvNm || "" }}</p>
              <p :title="rtePlan.loadAddr || ''">位置：{{ rtePlan.loadAddr || "" }}</p>
              <p>装货吨数：{{ rtePlan.loadActQty || "" }}</p>
              <p>备注：{{ "装货启运" }}</p>
            </div>
          </el-popover>
        </span>
      </el-step>
      <el-step>
        <span slot="icon">
          <el-popover trigger="hover" slot="icon">
            <span slot="reference">卸</span>
            <div shadow="never" class="card-item">
              <p>卸货</p>
              <p>时间：{{ rtePlan.unloadTm || "" }}</p>
              <p>操作人：{{ rtePlan.dvNm || "" }}</p>
              <p :title="rtePlan.unloadAddr || ''">位置：{{ rtePlan.unloadAddr || "" }}</p>
              <p>卸货吨数：{{ rtePlan.unloadActQty || "" }}</p>
              <p>备注：{{ "卸货" }}</p>
            </div>
          </el-popover>
        </span>
      </el-step>
      <el-step v-if="rtePlan.errBackStatus !== '211'">
        <span slot="icon">
          <el-popover trigger="hover" slot="icon">
            <span slot="reference">回</span>
            <div shadow="never" class="card-item">
              <p>结束</p>
              <p>时间：{{ rtePlan.backTm || "" }}</p>
              <p>操作人：{{ rtePlan.dvNm || "" }}</p>
              <p :title="rtePlan.backAddr || ''">位置：{{ rtePlan.backAddr || "" }}</p>
              <p>备注：{{ "结束" }}</p>
            </div>
          </el-popover>
        </span>
      </el-step>
      <el-step v-if="rtePlan.errBackStatus === '211'">
        <span slot="icon">
          <el-popover trigger="hover" slot="icon">
            <span slot="reference">异</span>
            <div shadow="never" class="card-item">
              <p>异常结束</p>
              <p>时间：{{ rtePlan.errBackTm || "" }}</p>
              <p>操作人：{{ rtePlan.dvNm || "" }}</p>
              <p :title="rtePlan.errBackAddr || ''">位置：{{ rtePlan.errBackAddr || "" }}</p>
              <p>备注：{{ "异常结束" }}</p>
            </div>
          </el-popover>
        </span>
      </el-step>
    </el-steps>
    <table class="custom-table" cellspacing="0" cellpadding="0">
      <tbody>
        <tr>
          <th style="width: 70px">运单编号</th>
          <td colspan="6">{{ rtePlan.cd }}</td>
        </tr>
        <tr>
          <th rowspan="2">托运人</th>
          <th style="width: 70px">名称</th>
          <td colspan="2">{{ rtePlan.consignorAddr }}</td>
          <th rowspan="2" style="width: 70px">收货人</th>
          <th style="width: 100px">名称</th>
          <td colspan="2">{{ rtePlan.csneeWhseAddr }}</td>
        </tr>

        <tr>
          <th>联系电话</th>
          <td colspan="2">{{ rtePlan.consignorTel }}</td>
          <th>联系电话</th>
          <td colspan="2">{{ rtePlan.csneeWhseTel }}</td>
        </tr>

        <tr>
          <th rowspan="2">装货人</th>
          <th>名称</th>
          <td colspan="2">{{ rtePlan.csnorWhseAddr }}</td>
          <th>起运日期</th>
          <td colspan="2">
            {{ rtePlan.vecDespTm ? dayjs(rtePlan.vecDespTm).format("YYYY-MM-DD") : "" }}
          </td>
        </tr>

        <tr>
          <th>联系电话</th>
          <td colspan="2">{{ rtePlan.csnorWhseTel }}</td>
          <th>起运地</th>
          <td colspan="2">{{ rtePlan.csnorWhseDist }}{{ rtePlan.csnorWhseLoc }}</td>
        </tr>

        <tr>
          <th colspan="2">目的地</th>
          <td colspan="3">{{ rtePlan.csneeWhseDist }}{{ rtePlan.csneeWhseLoc }}</td>
          <td colspan="2" style="vertical-align: middle">
            <!-- <input :checked="rtePlan.check" disabled="disabled" type="checkbox" /> 城市配送 -->
            <input :checked="rtePlan.cityDelivery == 1" disabled type="checkbox" />
            城市配送
          </td>
        </tr>

        <tr>
          <th rowspan="8">承运人</th>
          <th>单位名称</th>
          <td colspan="2">{{ rtePlan.carrierNm }}</td>
          <th>联系电话</th>
          <td colspan="2">{{ rtePlan.erMob }}</td>
        </tr>

        <tr>
          <th>许可证号</th>
          <td colspan="5">{{ rtePlan.carrierBssCd }}</td>
        </tr>

        <tr>
          <th rowspan="2">车辆信息</th>
          <th style="width: 90px">车牌号(颜色)</th>
          <td>
            <template v-if="rtePlan.tracPk">
              <span>{{ rtePlan.tracCd }}</span>
            </template>
            <template v-else>
              <span>{{ rtePlan.tracCd }}</span>
            </template>
            <!-- {{ rtePlan.plateType ? '(' + rtePlan.plateType + ')' : '' }} -->
          </td>
          <th rowspan="2">挂车信息</th>
          <th>车辆号牌</th>
          <td>
            <template v-if="rtePlan.traiPk">
              <span>{{ rtePlan.traiCd }}</span>
            </template>
            <template v-else>
              <span>{{ rtePlan.traiCd }}</span>
            </template>
          </td>
        </tr>

        <tr>
          <th>道路运输证号</th>
          <td>{{ rtePlan.tracOpraLicNo }}</td>
          <th>道路运输证号</th>
          <td>{{ rtePlan.traiOpraLicNo }}</td>
        </tr>

        <tr>
          <th>罐体信息</th>
          <th>罐体编号</th>
          <td colspan="2">
            <template v-if="rtePlan.cntrPk">
              <span>{{ rtePlan.tankNum }}</span>
            </template>
            <template v-else>
              <span>{{ rtePlan.tankNum }}</span>
            </template>
          </td>
          <th>罐体容积(m³)</th>
          <td>{{ rtePlan.tankVolume }}</td>
        </tr>

        <tr>
          <th rowspan="3">驾驶员</th>
          <th>姓名</th>
          <td>
            <div v-if="rtePlan.dvPk" :title="rtePlan.dvNm" class="detail-area">
              <span>{{ rtePlan.dvNm }}</span>
            </div>
            <div v-else :title="rtePlan.dvNm" class="detail-area">
              <span>{{ rtePlan.dvNm }}</span>
            </div>
          </td>
          <th rowspan="3">押运员</th>
          <th>姓名</th>
          <td>
            <div v-if="rtePlan.scPk" :title="rtePlan.scNm" class="detail-area">
              <span>{{ rtePlan.scNm }}</span>
            </div>
            <div v-else :title="rtePlan.scNm" class="detail-area">
              <span>{{ rtePlan.scNm }}</span>
            </div>
          </td>
        </tr>

        <tr>
          <th>从业资格证</th>
          <td>{{ rtePlan.dvCd }}</td>
          <th>从业资格证</th>
          <td>{{ rtePlan.scCd }}</td>
        </tr>

        <tr>
          <th>联系电话</th>
          <td>{{ rtePlan.dvMob }}</td>
          <th>联系电话</th>
          <td>{{ rtePlan.scMob }}</td>
        </tr>

        <tr>
          <th>货物信息</th>
          <!--多装卸-->
          <td colspan="6" v-if="rtePlan.loadType != null && rtePlan.loadType != '一装一卸'">
            <div v-if="JSON.parse(rtePlan.goodsInfoJson).length">
              <div v-for="(item,index) in JSON.parse(rtePlan.goodsInfoJson)" :key="index">
                {{ item.un ? `${index+1}，UN${item.un}，` : `${index+1}，` }}
                <span v-if="item.prodPk">{{ item.goodsNm }}</span>
                <span v-else>
                  {{ item.goodsNm }}
                  <span class="error-tips">（货品未登记或登记失败）</span></span>
                {{ `${item.dangGoodsNm ? '（' + item.dangGoodsNm + '），' : '（空），'}` }}
                {{ `${item.prodCategory ? +item.prodCategory + '类，' : '未分类，'}` }}
                {{ `${item.prodPackKind ? ('PG ' + item.prodPackKind + '，') : ''}` }}
                {{ `${item.packType ? (item.packType + '，') : ''}` }}
                {{ `${item.loadQty}吨` }}
              </div>
            </div>
            <div v-else><span>空车</span></div>
          </td>
          <!--非多装卸-->
          <td colspan="6" v-else>
            <template v-if="rtePlan.un">
              {{ `1，UN${rtePlan.un}，` }}
              <span v-if="rtePlan.enchPk">{{ rtePlan.goodsNm }}</span>
              <span v-else>
                {{ rtePlan.goodsNm }}
                <span class="error-tips">（货品未登记或登记失败）</span>
              </span>
              {{ `${rtePlan.dangGoodsNm ? "（" + rtePlan.dangGoodsNm + "），" : "（空），"}` }}
              {{ `${rtePlan.prodCategory ? +rtePlan.prodCategory + "类，" : "未分类，"}` }}
              {{ `${rtePlan.prodPackKind ? "PG " + rtePlan.prodPackKind + "，" : ""}` }}
              {{ `${rtePlan.packType ? rtePlan.packType + "，" : ""}` }}
              {{ `${rtePlan.loadQty}吨` }}
            </template>
          </td>
          
        </tr>

        <tr>
          <th>备注</th>
          <td colspan="4">
            <span>{{ rtePlan.freeText }}</span>
          </td>
          <td colspan="2">
            <span v-if="rtePlan.qr">
              <img :src="rtePlan.qr" alt="" style="height: 140px; width: 140px" />
            </span>
          </td>
        </tr>

        <tr>
          <th>调度人</th>
          <td colspan="3">{{ rtePlan.dispatcher }}</td>
          <th>调度日期</th>
          <td colspan="2">{{ dayjs(rtePlan.reqtTm).format("YYYY-MM-DD") }}</td>
        </tr>
      </tbody>
    </table>
    <!--   多装卸附表   -->
    <div v-if="rtePlan.loadType != null && rtePlan.loadType != '一装一卸'">
      <div v-for="(item,index) in rtePlan.ways" :key="index">
        <div style="margin-top: 20px"></div>
        <table class="custom-table" cellspacing="0" cellpadding="0">
          <colgroup>
            <col style="width: 9.54%">
            <col style="width: 9.54%">
            <col style="width: 27.88%">
            <col style="width: 10.13%">
            <col style="width: 9.54%">
            <col style="width: 13.28%">
            <col style="width: 12.07%">
          </colgroup>
          <thead>
          <tr>
            <th colspan="8" style="font-size: 14px;">危险货物道路运输运单附页{{ index + 1 }}</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td colspan="3">运单编号:{{ rtePlan.cd }}</td>
            <th>装卸类型</th>
            <td colspan="2">{{ rtePlan.loadType }}</td>
            <th>顺序号</th>
            <td>{{ index + 1 }}</td>
          </tr>
          <tr>
            <th rowspan="2">托运人</th>
            <th>名称</th>
            <td colspan="2">{{ item.consignorAddr }}</td>
            <th rowspan="2">收货人</th>
            <th>名称</th>
            <td colspan="2">{{ item.csneeWhseAddr }}</td>
          </tr>
          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ item.consignorTel }}</td>
            <th>联系电话</th>
            <td colspan="2">{{ item.csneeWhseTel }}</td>
          </tr>
          <tr>
            <th rowspan="2">装货人</th>
            <th>名称</th>
            <td colspan="2">{{ item.csnorWhseAddr }}</td>
            <th>起运地</th>
            <td colspan="3">{{ item.csnorWhseDist }}{{ item.csnorWhseLoc }}</td>
          </tr>
          <tr>
            <th>联系电话</th>
            <td colspan="2">{{ item.csnorWhseTel }}</td>
            <th>目的地</th>
            <td colspan="3">{{ item.csneeWhseDist }}{{ item.csneeWhseLoc }}</td>
          </tr>
          <tr>
            <th>货物信息</th>
            <td colspan="7" v-html="item.goodsInfo?item.goodsInfo.replace(/;/g,'<br>'):''"></td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";

export default {
  props: {
    rtePlan: {
      type: Object,
    },
  },
  data() {
    return {
      dayjs: dayjs
    };
  },
  computed: {
    activeIndex() {
      let index = 0;
      const activeKeyMap = {
        goTm: 1,
        loadTm: 2,
        unloadTm: 3,
        backTm: 4,
        errBackTm: 4,
      };
      Object.keys(activeKeyMap).forEach(key => {
        if (this.rtePlan && this.rtePlan[key]) {
          index = activeKeyMap[key];
        }
      });
      // console.log("[ index ]", index);
      return index;
    },
  },
};
</script>

<style scoped lang="scss">
.card-item {
  p {
    &:nth-child(1) {
      text-align: center;
      font-size: 12px;
      font-weight: 800;
    }
  }
}
.rte-plan-info {
  margin-top: 20px;
}
.custom-table {
  width: 100%;
  border: 1px solid #dee2e6;
  border-collapse: collapse;
  line-height: 20px;
  font-size: 12px;
  margin-top: 20px;
  color: var(--el-table-font-color);
}

.custom-table tbody th {
  text-align: left;
  font-weight: bold;
  padding: 0 5px;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
}
.custom-table tbody th.title {
  background-color: #e1e1e1;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  border-bottom: 1px solid #ccc;
  text-align: center;
}
.custom-table tbody th.subtitle {
  background-color: #eceaea;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  border-bottom: 1px solid #ccc;
  text-align: center;
}

.custom-table tbody td {
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  padding: 5px;
}

.custom-table a {
  color: #000;
  text-decoration: none;
}
.custom-table a:hover {
  color: #d00;
}
.custom-table thead {
  border-bottom: 1px solid #dee2e6;
  td {
    padding: 5px;
    font-weight: 800;
  }
}
</style>

<style lang="scss">
.rte-plan-box {
  .el-step__description {
    width: 100%;
    padding: 0 !important;
  }
}
</style>
