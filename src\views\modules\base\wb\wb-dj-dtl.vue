<template>
  <div class="unattended">
    <div class="img-list-bar">
      <ElRow class="img-bar img-box" justify="space-between">
        <div
          class="col-item"
          v-for="(item, index) in listData"
          :key="index"
          :style="{
            width: `calc(100% / ${listData.length})`,
            display: 'inline-block',
          }"
        >
          <ElCard class="card-item">
            <p class="card-title">
              {{ item.label }}
              <span v-if="item.sublabel"> [ {{ item.sublabel }} ] </span>
              <template v-if="item.type === imgBarType.Value && item.value">
                <i v-if="item.warning"></i>
                <i class="warning-success" v-if="!item.warning"></i>
              </template>
            </p>
            <ElImage
              class="card-img"
              v-if="item.type === imgBarType.Img"
              :src="item.img"
              :preview-src-list="[item.img || '']"
              fit="contain"
            >
              <template #error>
                <div class="image-slot">
                  <span class="el-icon-picture-outline"></span>
                </div>
              </template>
            </ElImage>
            <p class="card-value" v-if="item.type === imgBarType.Value">
              <span class="value-v">{{ item.value || 0 }}</span>
              <span class="value-u">{{ item.unit }}</span>
            </p>
            <!-- <p class="card-error" v-if="item.error">{{ item.error }}</p> -->
            <p v-else>
              <span class="card-error" v-if="item.error">
                {{ item.error }}
              </span>
              <span class="card-info" v-if="item.info">
                {{ item.info }}
              </span>
            </p>
          </ElCard>
        </div>
      </ElRow>
    </div>
    <ElRow class="info-box">
      <ElCol :span="12" class="col-item">
        <ElCard class="card-item">
          <template #header>
            <div class="info-header-box">
              <ElTabs
                v-model="tabActive"
                @tab-click="tabClick"
                class="tab-bar-box"
              >
                <ElTabPane label="电子运单信息" :name="tabType.RtePlan"></ElTabPane>
                <ElTabPane label="通行证信息" :name="tabType.Lic"></ElTabPane>
              </ElTabs>
            </div>
          </template>
          <ElScrollbar style="width: 100%; height: 100%;">
            <!-- 电子运单 -->
            <div class="rte-plan-box" v-if="tabActive === tabType.RtePlan">
              <ElSteps
                style="margin-top:20px;"
                align-center
                :active="activeIndex"
                process-status="wait"
                finish-status="finish"
              >
                <ElStep title="">
                  <template slot="icon">
                    <ElPopover trigger="hover">
                      <span slot="reference" class="step-text-icon">发</span>
                      <div shadow="never" class="card-item">
                        <p>发车</p>
                        <p>时间：{{ rtePlan.goTm || "" }}</p>
                        <p>操作人：{{ rtePlan.dvNm || "" }}</p>
                        <p :title="rtePlan.goAddr || ''">
                          位置：{{ rtePlan.goAddr || "" }}
                        </p>
                        <p>备注：{{ "发车提货" }}</p>
                      </div>
                    </ElPopover>
                  </template>
                </ElStep>

                <ElStep title="">
                  <template slot="icon">
                    <ElPopover trigger="hover">
                      <span slot="reference" class="step-text-icon">装</span>
                      <div shadow="never" class="card-item">
                        <p>装货</p>
                        <p>时间：{{ rtePlan.loadTm || "" }}</p>
                        <p>操作人：{{ rtePlan.dvNm || "" }}</p>
                        <p :title="rtePlan.loadAddr || ''">
                          位置：{{ rtePlan.loadAddr || "" }}
                        </p>
                        <p>装货吨数：{{ rtePlan.loadActQty || "" }}</p>
                        <p>备注：{{ "装货启运" }}</p>
                      </div>
                    </ElPopover>
                  </template>
                </ElStep>

                <ElStep title="">
                  <template slot="icon">
                    <ElPopover trigger="hover">
                      <span slot="reference" class="step-text-icon">卸</span>
                      <div shadow="never" class="card-item">
                        <p>卸货</p>
                        <p>时间：{{ rtePlan.unloadTm || "" }}</p>
                        <p>操作人：{{ rtePlan.dvNm || "" }}</p>
                        <p :title="rtePlan.unloadAddr || ''">
                          位置：{{ rtePlan.unloadAddr || "" }}
                        </p>
                        <p>卸货吨数：{{ rtePlan.unloadActQty || "" }}</p>
                        <p>备注：{{ "卸货" }}</p>
                      </div>
                    </ElPopover>
                  </template>
                </ElStep>

                <ElStep title="">
                  <template slot="icon">
                    <ElPopover trigger="hover">
                      <span slot="reference" class="step-text-icon">回</span>
                      <div shadow="never" class="card-item">
                        <p>结束</p>
                        <p>时间：{{ rtePlan.backTm || "" }}</p>
                        <p>操作人：{{ rtePlan.dvNm || "" }}</p>
                        <p :title="rtePlan.backAddr || ''">
                          位置：{{ rtePlan.backAddr || "" }}
                        </p>
                        <p>备注：{{ "结束" }}</p>
                      </div>
                    </ElPopover>
                  </template>
                </ElStep>

                <ElStep title="">
                  <template slot="icon">
                    <ElPopover trigger="hover">
                      <span slot="reference" class="step-text-icon">异</span>
                      <div shadow="never" class="card-item">
                        <p>异常结束</p>
                        <p>时间：{{ rtePlan.errBackTm || "" }}</p>
                        <p>操作人：{{ rtePlan.dvNm || "" }}</p>
                        <p :title="rtePlan.errBackAddr || ''">
                          位置：{{ rtePlan.errBackAddr || "" }}
                        </p>
                        <p>备注：{{ "异常结束" }}</p>
                      </div>
                    </ElPopover>
                  </template>
                </ElStep>
              </ElSteps>
              <table class="custom-table" cellspacing="0" cellpadding="0">
                <tbody>
                  <tr>
                    <th style="width: 70px">运单编号</th>
                    <td colspan="6">{{ rtePlan.cd }}</td>
                  </tr>
                  <tr>
                    <th rowspan="2">托运人</th>
                    <th style="width: 70px">名称</th>
                    <td colspan="2">{{ rtePlan.consignorAddr }}</td>
                    <th rowspan="2" style="width: 70px">收货人</th>
                    <th style="width: 100px">名称</th>
                    <td colspan="2">{{ rtePlan.csneeWhseAddr }}</td>
                  </tr>

                  <tr>
                    <th>联系电话</th>
                    <td colspan="2">{{ rtePlan.consignorTel }}</td>
                    <th>联系电话</th>
                    <td colspan="2">{{ rtePlan.csneeWhseTel }}</td>
                  </tr>

                  <tr>
                    <th rowspan="2">装货人</th>
                    <th>名称</th>
                    <td colspan="2">{{ rtePlan.csnorWhseAddr }}</td>
                    <th>起运日期</th>
                    <td colspan="2">{{ dayjsDay(rtePlan.vecDespTm) }}</td>
                  </tr>

                  <tr>
                    <th>联系电话</th>
                    <td colspan="2">{{ rtePlan.csnorWhseTel }}</td>
                    <th>起运地</th>
                    <td colspan="2">
                      {{ rtePlan.csnorWhseDist }}{{ rtePlan.csnorWhseLoc }}
                    </td>
                  </tr>

                  <tr>
                    <th colspan="2">目的地</th>
                    <td colspan="3">
                      {{ rtePlan.csneeWhseDist }}{{ rtePlan.csneeWhseLoc }}
                    </td>
                    <td colspan="2" style="vertical-align: middle">
                      <!-- <input :checked="rtePlan.check" disabled="disabled" type="checkbox" /> 城市配送 -->
                      <input
                        :checked="rtePlan.cityDelivery == 1"
                        disabled
                        type="checkbox"
                      />
                      城市配送
                    </td>
                  </tr>

                  <tr>
                    <th rowspan="8">承运人</th>
                    <th>单位名称</th>
                    <td colspan="2">{{ rtePlan.carrierNm }}</td>
                    <th>联系电话</th>
                    <td colspan="2">{{ rtePlan.erMob }}</td>
                  </tr>

                  <tr>
                    <th>许可证号</th>
                    <td colspan="5">{{ rtePlan.carrierBssCd }}</td>
                  </tr>

                  <tr>
                    <th rowspan="2">车辆信息</th>
                    <th style="width: 90px">车牌号(颜色)</th>
                    <td class="highlight-bg">
                      <template v-if="rtePlan.tracPk">
                        <span>{{ rtePlan.tracCd }}</span>
                      </template>
                      <template v-else>
                        <span>{{ rtePlan.tracCd }}</span>
                      </template>
                      <!-- {{ rtePlan.plateType ? '(' + rtePlan.plateType + ')' : '' }} -->
                    </td>
                    <th rowspan="2">挂车信息</th>
                    <th>车辆号牌</th>
                    <td class="highlight-bg">
                      <template v-if="rtePlan.traiPk">
                        <span>{{ rtePlan.traiCd }}</span>
                      </template>
                      <template v-else>
                        <span>{{ rtePlan.traiCd }}</span>
                      </template>
                    </td>
                  </tr>

                  <tr>
                    <th>道路运输证号</th>
                    <td>{{ rtePlan.tracOpraLicNo }}</td>
                    <th>道路运输证号</th>
                    <td>{{ rtePlan.traiOpraLicNo }}</td>
                  </tr>

                  <tr>
                    <th>罐体信息</th>
                    <th>罐体编号</th>
                    <td colspan="2" class="highlight-bg">
                      <template v-if="rtePlan.cntrPk">
                        <span>{{ rtePlan.tankNum }}</span>
                      </template>
                      <template v-else>
                        <span>{{ rtePlan.tankNum }}</span>
                      </template>
                    </td>
                    <th>罐体容积(m³)</th>
                    <td>{{ rtePlan.tankVolume }}</td>
                  </tr>

                  <tr>
                    <th rowspan="3">驾驶员</th>
                    <th>姓名</th>
                    <td>
                      <div
                        v-if="rtePlan.dvPk"
                        :title="rtePlan.dvNm"
                        class="detail-area"
                      >
                        <span>{{ rtePlan.dvNm }}</span>
                      </div>
                      <div v-else :title="rtePlan.dvNm" class="detail-area">
                        <span>{{ rtePlan.dvNm }}</span>
                      </div>
                    </td>
                    <th rowspan="3">押运员</th>
                    <th>姓名</th>
                    <td>
                      <div
                        v-if="rtePlan.scPk"
                        :title="rtePlan.scNm"
                        class="detail-area"
                      >
                        <span>{{ rtePlan.scNm }}</span>
                      </div>
                      <div v-else :title="rtePlan.scNm" class="detail-area">
                        <span>{{ rtePlan.scNm }}</span>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <th>从业资格证</th>
                    <td class="highlight-bg">{{ rtePlan.dvCd }}</td>
                    <th>从业资格证</th>
                    <td class="highlight-bg">{{ rtePlan.scCd }}</td>
                  </tr>

                  <tr>
                    <th>联系电话</th>
                    <td>{{ rtePlan.dvMob }}</td>
                    <th>联系电话</th>
                    <td>{{ rtePlan.scMob }}</td>
                  </tr>

                  <tr>
                    <th>货物信息</th>
                    <td colspan="6">
                      <template v-if="rtePlan.un">
                        {{ `1，UN${rtePlan.un}，` }}
                        <span v-if="rtePlan.enchPk">{{ rtePlan.goodsNm }}</span>
                        <span v-else
                          >{{ rtePlan.goodsNm
                          }}<span class="error-tips"
                            >（货品未登记或登记失败）</span
                          ></span
                        >
                        {{
                          `${
                            rtePlan.dangGoodsNm
                              ? "（" + rtePlan.dangGoodsNm + "），"
                              : "（空），"
                          }`
                        }}
                        {{
                          `${
                            rtePlan.prodCategory
                              ? +rtePlan.prodCategory + "类，"
                              : "未分类，"
                          }`
                        }}
                        {{
                          `${
                            rtePlan.prodPackKind
                              ? "PG " + rtePlan.prodPackKind + "，"
                              : ""
                          }`
                        }}
                        {{
                          `${rtePlan.packType ? rtePlan.packType + "，" : ""}`
                        }}
                        {{ `${rtePlan.loadQty}吨` }}
                      </template>
                    </td>
                  </tr>

                  <tr>
                    <th>备注</th>
                    <td colspan="4">
                      <span>{{ rtePlan.freeText }}</span>
                    </td>
                    <td colspan="2">
                      <span v-if="rtePlan.qr">
                        <img
                          :src="rtePlan.qr"
                          alt=""
                          style="height: 140px; width: 140px"
                        />
                      </span>
                    </td>
                  </tr>

                  <tr>
                    <th>调度人</th>
                    <td colspan="3">{{ rtePlan.dispatcher }}</td>
                    <th>调度日期</th>
                    <td colspan="2">{{ dayjsDay(rtePlan.reqtTm) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- 通行证 -->
            
            <div class="pass-port-box" v-if="tabActive === tabType.Lic">
                <table  class="custom-table" cellspacing="0" cellpadding="0">
                  <tbody>
                  <tr>
                    <th style="width: 120px">牵引车</th>
                    <td>{{ lic.vecNo }}</td>
                  </tr>
                  <tr>
                    <th style="width: 120px">企业</th>
                    <td>{{ lic.entpNmCn }}</td>
                  </tr>
                  <tr>
                    <th style="width: 120px">路线</th>
                    <td>{{ lic.route }}</td>
                  </tr>
                  <tr>
                    <th style="width: 120px">通行证类型</th>
                    <td>{{ lic.catNmCn }}</td>
                  </tr>
                  <tr>
                    <td style="width: 120px">通行证有效期</td>
                    <td>{{ lic.vldTo }}</td>
                  </tr>
                </tbody>
                </table>
            </div>
            
          </ElScrollbar>
        </ElCard>
      </ElCol>
      <ElCol :span="12" class="col-item">
        <ElCard class="card-item">
          <template #header>
            <span>自动查验信息</span>
          </template>
          <ElScrollbar ref="checkRef" style="width: 100%; height: 100%">
            <div class="check-table-box">
              <ElTable
                border
                :span-method="arraySpanMethod"
                size="small"
                v-for="(item, index) in myData"
                :key="index"
                :data="item"
                class="check-table"
                :style="{
                  width: ` calc(${100 / checkItemColumn}% - ${
                    (checkItemColumn - 1) * 10
                  }px)`,
                }"
              >
                <ElTableColumn prop="p" label="名称"></ElTableColumn>
                <ElTableColumn prop="nm" label="查验项">
                  <template slot-scope="{ row }">
                    <span v-if="row.must">*</span>
                    <span>{{ row.nm }}</span>
                  </template>
                </ElTableColumn>
                <ElTableColumn label="结果">
                  <template slot-scope="{ row }">
                    <span
                      class="ok-text"
                      v-if="row.res === checkResType.Success"
                    >
                      {{ row.resValue }}
                    </span>
                    <span
                      class="error-text"
                      v-if="row.res === checkResType.Error"
                    >
                      {{ row.resValue }}
                    </span>
                    <div
                      v-if="row.res === checkResType.QrCode"
                      ref="qrRef"
                    ></div>
                  </template>
                </ElTableColumn>
              </ElTable>
            </div>
          </ElScrollbar>
        </ElCard>
      </ElCol>
    </ElRow>

    <ElRow class="handel-box">
      <ElCol :span="8" class="handel-item">
        <ElCard class="handel-card handel-card-info">
          <div class="handel-card-inner">
            <p>镇海区危险货物道路运输管理系统（登记点）</p>
            <div>
              <span>[ {{ locationTitle }} ]</span>
              <span>登记时间：{{ wtTm }}</span>
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :span="16" class="handel-item">
        <ElCard class="handel-card">
          <p class="handel-info" style="color: #f56c6c">
            <!-- <span v-if="rmks">{{ rmks }}，</span> -->
            <span v-if="referReason"> 手动开闸原因:{{ referReason }} </span>
            <span v-if="handleMode"> 人工处理方式:{{ handleMode }} </span>
          </p>
          <ElButton
            size="small"
            type="primary"
            icon="el-icon-back"
            class="handel-submit"
            @click="goPrevious"
          >
            返回上一页
          </ElButton>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script>
import lodash from "lodash";
import dayjs from "dayjs";
import * as $http from "@/api/wb.js";
import QRCode from "qrcodejs2";

export default {
  name: "",
  data() {
    return {
      tabActive: "rtePlan",
      rtePlan: {},
      checkItemData: [],
      checkItemColumn: 2,
      checkItemLen: 14,
      qrRef: null,
      projectModule: {
        title: "",
        version: "",
      },
      locationTitle: "",
      wtTm: "",
      referReason: "",
      handleMode:"",
      rmks: "",
      checkResType: {
        Success: 0,
        Error: 1,
        None: 2,
        QrCode: 3,
      },
      collectType: {
        AfterVec: "100.002", //车尾
        BeforeVec: "100.001", //车头
        BrakeVec: "100.003", //道闸
        JSY: "100.004", //驾驶员
        YYY: "100.005", //押运员
        WB: "100.006", //地磅
      },
      tabType: {
        RtePlan: "rtePlan",
        Lic: "lic",
      },
      checkType: {
        Overload: "108.001",
        QRCode: "105.004",
      },
      initListData: [],
      initListDataDefault: [],
      listData: [],
      whites: null,
      lic: {},
      checkTableList: [],
      imgBarType: {
        Img: "Image",
        Value: "Number",
      },
      checkSuccessMap: {},
    };
  },
  computed: {
    activeIndex() {
      let index = 0;
      const activeKeyMap = {
        goTm: 1,
        loadTm: 2,
        unloadTm: 3,
        backTm: 4,
        errBackTm: 4,
      };
      const rtePlan = this.rtePlan;
      Object.keys(activeKeyMap).forEach((key) => {
        if (rtePlan[key]) {
          index = activeKeyMap[key];
        }
      });
      
      return index;
    },
    myData() {
      const d = lodash.cloneDeep(this.checkTableList);
      const col = this.checkItemColumn;
      const singleLen = this.checkItemLen;
      const tempData = [];
      let tempLen = 0;
      let fixIndex = 0;
      let tempArr = [];
      d.forEach((item, index) => {
        if (d[fixIndex].p === item.p) {
          tempLen += 1;
        } else {
          d[fixIndex].len = tempLen;
          tempLen = 1;
          fixIndex = index;
        }
        tempArr.push(item);
        if (tempArr.length >= singleLen || index >= d.length - 1) {
          tempData.push(tempArr);
          tempArr = [];
          d[fixIndex].len = tempLen;
          tempLen = 0;
          fixIndex = index + 1;
        }
      });

      return tempData;
    },
  },
  created() {
    const temp = [];
    const checkTableItem = [
      {
        result: null,
        cd: "100.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "100.001",
            method: "checkOrder",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "状态",
            scene: null,
          },
        ],
        type: "trac",
        enabled: true,
        nm: "电子运单",
        scene: null,
      },
      {
        result: null,
        cd: "101.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "101.001",
            method: "checkVecLicPpt",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "是否持有",
            scene: null,
          },
        ],
        type: "trac",
        enabled: true,
        nm: "通行证",
        scene: null,
      },
      {
        result: null,
        cd: "102.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "102.001",
            method: "checkEntpAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "102.002",
            method: "checkEntpLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
        ],
        type: "trac",
        enabled: true,
        nm: "运输企业",
        scene: null,
      },
      {
        result: null,
        cd: "103.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "103.001",
            method: "checkTracAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "103.002",
            method: "checkTracLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
          {
            result: null,
            cd: "103.003",
            method: "checkTracGps",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "卫星定位",
            scene: null,
          },
          {
            result: null,
            cd: "103.004",
            method: "checkVecBlack",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "黑名单",
            scene: null,
          },
        ],
        type: "trac",
        enabled: true,
        nm: "牵引车",
        scene: null,
      },
      {
        result: null,
        cd: "104.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "104.001",
            method: "checkTraiAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "104.002",
            method: "checkTraiLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
        ],
        type: "trai",
        enabled: true,
        nm: "挂车",
        scene: null,
      },
      {
        result: null,
        cd: "105.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "105.001",
            method: "checkDvAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "105.002",
            method: "checkDvLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
          {
            result: null,
            cd: "105.003",
            method: "checkDvTired",
            must: false,
            itemList: null,
            type: null,
            enabled: false,
            nm: "疲劳驾驶",
            scene: null,
          },
          {
            result: null,
            cd: "105.004",
            method: "checkDvSafeQr",
            must: false,
            itemList: null,
            type: null,
            enabled: true,
            nm: "安全码",
            scene: null,
          },
          {
            result: null,
            cd: "105.005",
            method: "checkDvFace",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "人脸核验",
            scene: null,
          },
          {
            result: null,
            cd: "105.006",
            method: "checkDvBlack",
            must: false,
            itemList: null,
            type: null,
            enabled: false,
            nm: "异常人员",
            scene: null,
          },
        ],
        type: "dv",
        enabled: true,
        nm: "驾驶员",
        scene: null,
      },
      {
        result: null,
        cd: "106.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "106.001",
            method: "checkScAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "106.002",
            method: "checkScLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
          {
            result: null,
            cd: "106.003",
            method: "checkScFace",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "人脸核验",
            scene: null,
          },
          {
            result: null,
            cd: "106.004",
            method: "checkScBlack",
            must: false,
            itemList: null,
            type: null,
            enabled: false,
            nm: "异常人员",
            scene: null,
          },
        ],
        type: "sc",
        enabled: true,
        nm: "押运员",
        scene: null,
      },
      {
        result: null,
        cd: "107.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "107.001",
            method: "checkTankAudit",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "审核状态",
            scene: null,
          },
          {
            result: null,
            cd: "107.002",
            method: "checkTankLic",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "证件状态",
            scene: null,
          },
        ],
        type: "tank",
        enabled: true,
        nm: "罐体",
        scene: null,
      },
      {
        result: null,
        cd: "108.000",
        method: null,
        must: null,
        itemList: [
          {
            result: null,
            cd: "108.001",
            method: "checkWb",
            must: true,
            itemList: null,
            type: null,
            enabled: true,
            nm: "是否超载",
            scene: null,
          },
        ],
        type: "wb",
        enabled: true,
        nm: "车辆过磅",
        scene: { empty: false, heavy: true },
      },
    ];
    const checkResType = this.checkResType;
    checkTableItem.forEach((item) => {
      item.itemList.forEach((ite, index) => {
        if (item.enabled && ite.enabled && item.nm && ite.nm) {
          temp.push({
            p: item.nm,
            len: index === 0 ? item.itemList.length || 0 : 0,
            res: checkResType.None,
            ...ite,
            nm: ite.nm,
            resValue: "",
          });
        }
      });
    });

    this.checkTableList = temp;

    // 获取查验成功配置项
    this.getSuccessCheckRes();

    // 货物白名单
    this.getWhiteGoods();
  },
  mounted() {
    this.$nextTick(() => {
      this.getCollectData();
      this.getCheckLen();

      window.addEventListener("resize", this.getCheckLen);
    });
  },
  methods: {
    // 货物白名单
    async getWhiteGoods() {
      let whites = [];

      const res = await $http.getWhiteGoods();
      if (res) {
        whites = JSON.parse(res.data.paramValue);
      }
      this.whites = whites;
    },
    setCheck(type, res) {
      const checkTableList = this.checkTableList;
      const d = checkTableList.filter((item) => item.cd === type)[0];
      if (d) Object.assign(d, { res });
    },
    getCheckLen() {
      const checkRef = this.$refs.checkRef;
      const height = checkRef.$el.clientHeight || 0;
      const _checkLen = Math.floor(height / 30) - 1;
      this.checkItemLen = Math.min(14, _checkLen);
    },
    // // 获取设备信息
    // async getWbSceneConfig(regNumber) {
    //   const res = await $http.getWbSceneConfig(regNumber);
    //   if (res && this.projectModule.scene) {
    //     return res.data[this.projectModule.scene].enabled || false;
    //   }
    //   return false;
    // },
    async getCollectData() {
      const collectType = this.collectType;
      //   TODO
      //   const res = {"code":0,"data":"[{\"nm\":\"车前脸\",\"cd\":\"100.001\",\"type\":\"Image\",\"sort\":0},{\"nm\":\"车后脸\",\"cd\":\"100.002\",\"type\":\"Image\",\"sort\":1},{\"nm\":\"道闸\",\"cd\":\"100.003\",\"type\":\"Image\",\"sort\":2},{\"nm\":\"地磅\",\"cd\":\"100.006\",\"type\":\"Number\",\"sort\":3},{\"nm\":\"驾驶员人脸\",\"cd\":\"100.004\",\"type\":\"Image\",\"sort\":4},{\"nm\":\"押运员人脸\",\"cd\":\"100.005\",\"type\":\"Image\",\"sort\":5}]"}
      //   const enabled = false;
      const res = await $http.getCollectData();
      // const enabled = await this.getWbSceneConfig('ZHCK0001');
      if (res && res.data) {
        const data = JSON.parse(res.data.paramValue);
        const initListDataItem = {
          sublabel: "",
          value: "",
          img: "",
          error: "",
          info: "",
          isok: true,
          changeData: "",
          changeValidator: undefined,
        };
        const imgBarType = this.imgBarType;
        this.initListData = data
          .filter(
            (item) =>
              item.cd !== collectType.WB ||
              item.cd === collectType.WB /* && enabled */
          )
          .sort((a, b) => a.sort - b.sort)
          .map((item) => ({
            ...initListDataItem,
            label: item.nm,
            type: item.type,
            cd: item.cd,
            unit: item.type === imgBarType.Value ? "kg" : "",
          }));
      }
      this.initListDataDefault = await lodash.cloneDeep(this.initListData);
      this.reSetListData();
    },
    // 重置数据
    reSetListData() {
      const temp = this.initListDataDefault.map((item) => {
        const d = lodash.cloneDeep(item);
        this.listData.forEach((listDataItem) => {
          if (listDataItem && listDataItem.cd === item.cd && d) {
            Lang.assign(d, { isok: listDataItem.isok });
          }
        });
        return d;
      });
      this.listData = lodash.cloneDeep(temp);

      this.getRecordDtl();
    },
    tabClick(tab) {
      this.tabActive = tab.paneName || this.tabType.RtePlan;
    },
    dayjsDay(date) {
      return date ? dayjs(date).format("YYYY-MM-DD") : "";
    },
    arraySpanMethod({ row, columnIndex }) {
      if (columnIndex === 0) {
        if (row.len) {
          return {
            rowspan: row.len,
            colspan: 1,
          };
        }
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },
    goPrevious() {
      this.$router.go(-1);
    },
    async getSuccessCheckRes() {
      const res = await $http.getSuccessCheckRes();
      const checkSuccessMap = this.checkSuccessMap;
      if (res && res.data) {
        const d = JSON.parse(res.data.paramValue);
        d.forEach((item) => {
          Object.keys(item).forEach((key) => {
            checkSuccessMap[key] = item[key];
          });
        });
      }
    },
    async getRecordDtl() {
      const checkSuccessMap = this.checkSuccessMap;
      const checkResType = this.checkResType;
      const checkType = this.checkType;
      const id = this.$route.params.id;
      const res = await $http.recordDtl(id);
      if (res) {
        try {
          const data = res.data;
          const ckRecod = data.ckRecod
          let rtePlan = this.rtePlan;
          let checkTableList = this.checkTableList;

          this.rtePlan = data.rtePlan
          // const lic = JSON.parse(data.licPpt)
          const wtPic = ckRecod.wtPic ? JSON.parse(ckRecod.wtPic) : {};
          const wtStatusData = ckRecod.wtStatus ? JSON.parse(ckRecod.wtStatus)[0] : [];
          const collectType = this.collectType;
          
          if(data.licPpt){
            Object.assign( this.lic, data.licPpt)
          }

          this.wtTm = data.ckRecod.wtTm;
          this.locationTitle = data.ckRecod.regPot;
          this.referReason = data.ckRecod.referReason;
          this.handleMode = data.ckRecod.handleMode;
          this.rmks = data.ckRecod.rmks;

          
          const temp = lodash.cloneDeep(checkTableList);
          if(wtStatusData){
            Object.keys(wtStatusData).forEach((key) => {
              if (/\w+\.\w+/.test(key)) {
                const val = wtStatusData[key];

                temp.forEach((item) => {
                  if (item.cd === key) {
                    Object.assign(item, {
                      res:
                        (key === checkType.QRCode && val.status === 1
                          ? checkResType.QrCode
                          : null) ||
                        (val.status === 1
                          ? checkResType.Success
                          : checkResType.Error),
                      resValue:
                        val.content ||
                        (val.status === 1
                          ? checkSuccessMap[key].success
                          : checkSuccessMap[key].error),
                    });
                  }
                });
                this.checkTableList = JSON.parse(JSON.stringify(temp));
              }
            });
          }
          
          // 驾驶员安全码
          this.qrCode(temp)

          this.listData.forEach((item) => {
            if (item.cd === collectType.WB) {
              item.value = data.ckRecod.weigh;
              if (data.ckRecod.weigh > 50000) {
                this.setCheck(checkType.Overload, checkResType.Error);
              } else {
                this.setCheck(checkType.Overload, checkResType.Success);
              }
            } else {
              if (wtPic[item.cd] && wtPic[item.cd].pic) {
                item.img = wtPic[item.cd].pic;
              }
            }
          });

          // 货物白名单 去除押运员
          const whites = this.whites;
          if (whites.find((item) => item.un === rtePlan.un)) {
            const yyyIndex = this.listData.findIndex(
              (item) => item.cd === collectType.YYY
            );
            // 校验下标值，防止传入-1
            if (lodash.isNumber(yyyIndex) && yyyIndex > -1) {
              this.listData.splice(yyyIndex, 1);
            }
          }

          // 如果为空车道则删除地磅显示
          if (data.ckRecod.referScene === "empty") {
            const wbIndex = this.listData.findIndex(
              (item) => item.cd === collectType.WB
            );
            // 校验下标值，防止传入-1
            if (lodash.isNumber(wbIndex) && wbIndex > -1) {
              this.listData.splice(wbIndex, 1);
            }

            // 如果为空车道删除车辆过磅查验信息
            checkTableList.value.forEach((item, index) => {
              if (item.cd === this.checkType.Overload) {
                checkTableList.value.splice(index, 1);
              }
            });
          }
        } catch (error) {}
      }
    },
    qrCode(data){
      const checkResType = this.checkResType;
      const qrData = JSON.parse(data.filter((item) => item.res === checkResType.QrCode)[0].resValue)
      const colorMap = {
        0: '#0089e8',
        1: '#ffc600',
        2: '#ff0000',
        99: '#cccccc'
      }
      this.$nextTick(() => {
        let qrcode = new QRCode(this.$refs.qrRef[0], {
          text: qrData.idNo,
          width: 40,
          height: 40,
          colorDark: colorMap[qrData.codeState],
          colorLight: "#ffffff",
          correctLevel: QRCode.CorrectLevel.L
        })
      })
      
    }
  },
};
</script>

<style lang="scss" scoped>
p {
  padding: 0;
  margin: 0;
}
.unattended {
  height: 100vh;
  box-sizing: border-box;
  color: #666 !important;
}
.img-list-bar {
  height: 180px;
  padding-bottom: 10px;
}

.info-box {
  height: calc(100% - 260px);
  padding-bottom: 10px;
  padding-top: 0;
  .card-item {
    margin: 0 5px;
    height: 100%;
  }
  .col-item {
    height: 100%;
  }
  .check-info-box {
    font-size: 16px;
    line-height: 28px;
    span {
      margin-right: 10px;
    }
  }
}
.handel-box {
  height: 80px;
  padding-top: 0;
  //   .handel-item {
  //     width: 100%;
  //   }
  .handel-card {
    height: 100%;
    margin: 0 5px;
    position: relative;
  }
  .handel-card-inner {
    text-align: center;
    font-weight: 800;
    line-height: 30px;
    font-size: 16px;
    div {
      font-size: 14px;
      span {
        &:nth-child(1) {
          float: right;
          font-weight: 400;
        }
        &:nth-child(2) {
          float: left;
          font-weight: 800;
          font-family: numberFont;
          font-size: 16px;
        }
      }
    }
  }
  .handel-info {
    line-height: 34px;
    font-size: 28px;
    font-weight: 800;
    text-align: center;
    width: 70%;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
  }

  .handel-submit {
    float: right;
    margin-top: 4px;
  }
}

/deep/ {
  .el-card__header {
    padding: 10px 10px;
    font-size: 14px;
    line-height: 14px;
  }

  .el-card__body {
    height: calc(100% - 34px);
  }

  .card-item {
    .el-card__body {
      height: calc(100% - 34px);
    }
    .el-card__header {
      padding: 10px 10px;
      font-size: 14px;
      line-height: 14px;
    }
  }
  .el-table .el-table__cell {
    padding: 3px 0;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background: transparent;
  }
  .el-tabs__item {
    height: 14px;
    line-height: 14px;
  }
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__nav-scroll {
    overflow: initial;
  }
  .el-tabs__nav-wrap {
    overflow: initial;
    height: 15px;
  }
  .el-tabs__active-bar {
    bottom: -10px;
  }

  .handel-card-info {
    .el-card__body {
      padding: 5px 20px;
    }
  }
  .el-table .el-table__inner-wrapper .success-row {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }

  .img-bar {
    .card-item {
      .el-card__body {
        height: 100% !important;
        position: relative;
        text-align: center;
        padding: 10px;
      }
    }
  }

  .rte-plan-box {
    .el-step__description {
      width: 100%;
      padding: 0 !important;
    }
  }
  .highlight-bg {
    background-color: #daf6fa;
  }

  .el-row {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    box-sizing: border-box;
  }

  .el-col-12 {
    max-width: 50%;
    flex: 0 0 50%;
  }
}

.check-table-box {
  overflow: hidden;
  .check-table {
    float: left;
    + .check-table {
      margin-left: 10px;
    }
  }
}
.error-text {
  color: #f56c6c;
  font-weight: 800;
}
.ok-text {
  color: #67c23a;
  font-weight: 800;
}

.img-box {
  height: 100%;
  width: 100%;
}
.col-item {
  height: 100%;
}
.card-item {
  margin: 0 5px;
  height: 100%;
  .card-title {
    text-align: center;
    margin-bottom: 5px;
    line-height: 20px;
    font-size: 15px;
    position: relative;
    i {
      position: absolute;
      width: 10px;
      height: 10px;
      right: 10px;
      background: #f56c6c;
      border-radius: 50%;
      &.warning-success {
        background: #67c23a;
      }
    }
  }
  .card-img {
    height: calc(100% - 48px);
    width: 100%;
    .image-slot {
      background: #ecf0f5;
      width: 100%;
      height: 100%;
      line-height: 100px;
      .img-empty {
        line-height: 100px;
        font-size: 25px;
      }
    }
  }
  .card-value {
    height: 100px;
    width: calc(100% - 20px);
    text-align: center;
    font-weight: 800;
    position: absolute;
    left: 0;
    right: 0;
    top: 18px;
    bottom: 15px;
    margin: auto;
    background: #000;
    color: #fff;
    border-radius: 3px;
    .value-u {
      font-size: 30px;
      line-height: 120px;
      margin-left: 5px;
      font-family: numberFont;
    }
    .value-v {
      line-height: 100px;
      font-size: 60px;
      font-family: numberFont;
    }
  }
  .card-error {
    font-size: 14px;
    color: red;
    line-height: 15px;
    text-align: center;
    font-weight: 800;
  }
  .card-info {
    font-size: 14px;
    line-height: 15px;
    text-align: center;
    font-weight: 800;
  }
}
.edit-icon {
  margin-left: 2px;
  margin-right: 2px;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
}

.card-item {
  p {
    &:nth-child(1) {
      text-align: center;
      font-size: 12px;
      font-weight: 800;
    }
  }
}
.rte-plan-info {
  margin-top: 20px;
}
.custom-table {
  width: 100%;
  border: 1px solid #808080;
  border-collapse: collapse;
  line-height: 20px;
  font-size: 12px;
  margin-top: 20px;
  color: var(--el-table-font-color);
}

.custom-table tbody th {
  text-align: left;
  font-weight: bold;
  padding: 0 5px;
  border-bottom: 1px solid #808080;
  border-right: 1px solid #808080;
}
.custom-table tbody th.title {
  background-color: #e1e1e1;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  border-bottom: 1px solid #ccc;
  text-align: center;
}
.custom-table tbody th.subtitle {
  background-color: #eceaea;
  vertical-align: middle;
  width: 24px;
  line-height: 16px;
  border-bottom: 1px solid #ccc;
  text-align: center;
}

.custom-table tbody td {
  text-align: left;
  border-bottom: 1px solid #808080;
  border-right: 1px solid #808080;
  padding: 5px;
}

.custom-table a {
  color: #000;
  text-decoration: none;
}
.custom-table a:hover {
  color: #d00;
}
.custom-table thead {
  border-bottom: 1px solid #dee2e6;
  td {
    padding: 5px;
    font-weight: 800;
  }
}

.check-table-box {
  overflow: hidden;
  .check-table {
    float: left;
    + .check-table {
      margin-left: 10px;
    }
  }
}
.error-text {
  color: #f56c6c;
  font-weight: 800;
}
.ok-text {
  color: #67c23a;
  font-weight: 800;
}
</style>