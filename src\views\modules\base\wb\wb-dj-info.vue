<template>
  <el-dialog title="详情记录" :visible.sync="dialogVisible" top="3vh" width="90%" :before-close="handleClose" :append-to-body="true">
    <div class="modal-out">
      <div class="modal-top">
        <div class="config-item" v-for="item in config" :key="item.cd" :style="{ width: `${99 / config.length}%` }">
          <div class="title">{{ item.nm }}</div>
          <div class="value">
            <canvas  v-if="item.type === 'Image'" :id="'myCanvas_' + item.cd" :width="w" :height="h"  style="border: 1px solid #ccc">您的浏览器不支持 HTML5 canvas 标签。</canvas>
            <div v-if="item.type === 'Number' && detail" class="wt">
              {{ detail.weigh }}
              <span>kg</span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-bottom">
        <div class="modal-left">
          <el-tabs ref="headerRef" v-model="activeName">
            <el-tab-pane label="电子运单信息" name="archives">
              <RtePlan :rtePlan="rteplan" />
            </el-tab-pane>
            <el-tab-pane label="通行证信息" name="passport">
              <Passport :passPort="passport" />
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="modal-right">
          <CheckItem :data="finalCheckItem" :column="2" :len="checkLen" />
          <div style="margin-top:18px;color:red;font-size:16px;font-weight:600;">
            <el-col span="12" v-if="referReason">手动开闸原因:{{ referReason }}</el-col> <el-col span="12" v-if="handleMode">处理方式:{{ handleMode }}</el-col>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/wb";
import RtePlan from "./wb-components/retplan-bill.vue";
import CheckItem from "./wb-components/check-item.vue";
import Passport from "./wb-components/pass-port.vue";

const config = [
  { nm: "车前脸", cd: "100.001", type: "Image", sort: 0 },
  { nm: "车后脸", cd: "100.002", type: "Image", sort: 1 },
  { nm: "道闸", cd: "100.003", type: "Image", sort: 2 },
  { nm: "地磅", cd: "100.006", type: "Number", sort: 3 },
  { nm: "驾驶员人脸", cd: "100.004", type: "Image", sort: 4 },
  { nm: "押运员人脸", cd: "100.005", type: "Image", sort: 5 },
];

export default {
  components: {
    RtePlan,
    CheckItem,
    Passport,
  },
  data() {
    return {
      dialogVisible: false,
      detail: null,
      // config: config,
      checkItem: [],
      rteplan: null,
      checkLen: 12,
      finalCheckItem: [],
      checkSuccessMap: [],
      EnumCheckResType: {
        Success: 0,
        Error: 1,
        None: 2,
        QrCode: 3,
      },
      EnumCheckType: {
        Overload: "108.001",
        QRCode: "105.004",
      },
      activeName: "archives",
      passport: null,
      canvas:null,
      w:null,
      h:null,
    };
  },
  created() {
    this.getCheckItems();
    this.getCheckDescr();
  },
  computed: {
    picMap() {
      const temp = {};
      try {
        const d = JSON.parse(this.detail.wtPic);
        Object.keys(d).forEach(key => {
          temp[key] = d[key].pic;
        });
      } catch (e) {}
      return temp;
    },
    referReason(){
      if(this.detail === null){
        return false;
      }
      return this.detail.referReason;
    },
    handleMode(){
      if(this.detail === null){
        return false;
      }
      return this.detail.handleMode;
    },
    config(){
      if(this.detail === null){
        return config;
      }
      // 如果过磅重量为零，则不显示地磅图片,除了蛟川二点，蛟川二点不区分空重车道都有过磅
      if(this.detail.weigh === 0 && this.detail.regNumber != 'ZHCK0004'){
        return config.filter( item => {
          return item.cd !== "100.006";
        });
      }else{
        return config;
      }
    }
  },
  methods: {
    open(row) {
      this.dialogVisible = true;
      this.detail = row;
      this.getRtePlan();
      this.getPassport();
      this.checkItem = this.checkItems[row.regNumber].config;
      this.setCheckItem(row.id);

      this.$nextTick(() => {
        // 绘制矩形
        this.initCanvas();
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.detail = null;
    },
    async getCheckItems() {
      const res = await $http.checkConfig();
      if (res.code == 0) {
        const d = res.page.list.find(item => item.paramKey === "ckControlCheckConfig");
        this.checkItems = JSON.parse(d.paramValue);
      }
    },
    async getCheckDescr() {
      const res = await $http.checkDescr();

      let arr = [];
      if (res.code == 0) {
        const d = res.page.list.find(item => item.paramKey === "ckControlCheckDescr");
        arr = JSON.parse(d.paramValue);
      }

      if (arr) {
        const temp = {};
        arr.forEach(item => {
          Object.keys(item).forEach(key => {
            if (key !== "nm") {
              temp[key] = item[key];
            }
          });
        });
        this.checkSuccessMap = temp;
      }
    },
    // 电子运单
    async getRtePlan() {
      const res = await $http.getDtlById({ id: this.detail.argmtPk });
      console.log(res)
      if (res.code === 0) {
        this.rteplan = res.data;
      }
    },
    async setCheckItem() {
      const temp = [];
      this.checkItem.forEach(item => {
        if (!item.itemList) return;
        item.itemList.forEach((ite, index) => {
          if (item.enabled && ite.enabled && item.nm && ite.nm) {
            temp.push({
              p: item.nm,
              len: index === 0 ? item.itemList.length || 0 : 0,
              res: this.EnumCheckResType.None,
              ...ite,
              nm: ite.nm,
              resValue: "",
            });
          }
        });
      });
      let status;
      if (this.detail.wtStatus) {
        status = this.detail.wtStatus;
      } else {
        status = "[{}]";
      }
      const tempd = JSON.parse(status);
      const d = tempd.length ? tempd[0] : {};
      // 判断是否超载
      const overloadCd = this.EnumCheckType.Overload;
      const weigh = this.detail.weigh;

      Object.keys(d).forEach(key => {
        const val = d[key];
        if (!val) return;
        temp.forEach(item => {
          if (item.cd === key) {
            item.res = (key === this.EnumCheckType.QRCode ? this.EnumCheckResType.QrCode : null) || val.status === 1 ? this.EnumCheckResType.Success : this.EnumCheckResType.Error;
            item.resValue = val.content || (val.status === 1 ? this.checkSuccessMap[key].success : this.checkSuccessMap[key].error);
          }else if(item.cd === overloadCd ){
            // 判断是否超载
            item.res = val.status === 1 ? this.EnumCheckResType.Success : this.EnumCheckResType.Error;
            if (weigh > 50000) {
              item.resValue = "是";
            }else{
              item.resValue = "否";
            }
          }
        });
      });

      this.finalCheckItem = temp;
    },
    // 通行证信息
    async getPassport() {
      const res = await $http.getPassport({
        vecNo: this.detail.cd,
      });
      if (res.code === 0) {
        this.passport = res.data;
      } else {
        this.passport = {};
      }
    },

    // 绘制人脸和车牌号的矩形框
    async initCanvas() {
      let self = this;

      // 获取图片宽高
      let docDiv = document.getElementsByClassName("value");
      this.w = docDiv[0].offsetWidth;
      this.h = docDiv[0].offsetHeight;

      const loadImage = src =>
        new Promise((resolve, reject) => {
          const img = new Image();
          img.src = src;
          img.onload = () => resolve(img);
          img.onerror = reject;
        });

      const imageUrls = [];
      this.config.forEach(item => {
        if (self.picMap[item.cd]) {
          imageUrls.push(self.picMap[item.cd]);
        }
      });

      Promise.all(imageUrls.map(loadImage)).then(img => {
        Object.keys(self.picMap).forEach((key, val) => {
          self.canvas = document.getElementById("myCanvas_" + key);
          self.ctx = self.canvas.getContext("2d");
          // 绘制图片
          self.ctx.drawImage(img[val], 0, 0, self.w, self.h);
        });
      });

      let keys = [];//存储图片的key值
      // 获取标识数据
      const res = await $http.getVecAndPersDetect({id:this.detail.id});
      for (const key in res.data) {
        keys.push(key);
      }

      if (res.code == 0 && res.data) {

        Promise.all(imageUrls.map(loadImage)).then(img => {
          keys.forEach((item,index) => {
            self.canvas = document.getElementById("myCanvas_" + item);
            self.ctx = self.canvas.getContext("2d");

            self.ctx.save();
            // 绘制图片
            // self.ctx.drawImage(img[index], 0, 0, self.w, self.h);
            // 计算缩放比例
            let x_scale= img[index].width/self.w ;
            let y_scale = img[index].height/self.h;

            let location = res.data[item];
            if (location) {
              // 区分是车牌还是人脸 true车牌 flase人脸
              let flag = Array.isArray(location);
              if (flag == true) {
                let data = location[0]["vertexes_location"];

                self.ctx.beginPath();
                self.ctx.moveTo( data[0].x/x_scale,data[0].y/y_scale);
                self.ctx.lineTo( data[1].x/x_scale,data[1].y/y_scale);
                self.ctx.lineTo( data[2].x/x_scale,data[2].y/y_scale);
                self.ctx.lineTo( data[3].x/x_scale,data[3].y/y_scale);
                self.ctx.closePath();

                self.ctx.lineWidth = 2;
                self.ctx.strokeStyle = "red";
                self.ctx.stroke();
              }else if (flag == false) {

                let data = location["face_list"][0]["location"];

                self.ctx.beginPath();
                self.ctx.strokeStyle = "red";
                self.ctx.lineWidth = 2;
                self.ctx.rect(data.left/x_scale-10, data.top/y_scale-10, data.width/x_scale + 15, data.height/y_scale+15);
                self.ctx.stroke();
              }
            }

            self.ctx.restore();
          });
        }).catch(error=>{
          console.error(error);
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-out {
  .modal-top {
    height: 200px;
    border: 1px solid #ddd;
    border-right: 0;
    .config-item {
      height: 100%;
      border-right: 1px solid #ddd;
      float: left;
      .title {
        height: 30px;
        line-height: 30px;
        text-align: center;
      }
      .value {
        background: #000;
        border-radius: 4px;
        width: calc(100% - 20px);
        margin: 0 auto;
        height: 150px;
        position: relative;
      }
      .bottom-title {
        height: 30px;
        line-height: 30px;
        text-align: center;
      }
      .wt {
        line-height: 80px;
        height: 80px;
        position: absolute;
        text-align: center;
        top: 0;
        bottom: 0;
        margin: auto;
        width: 100%;
        color: #fff;
        font-size: 40px;
        span {
          font-size: 20px;
        }
      }
    }
  }
  .modal-bottom {
    display: flex;
    overflow: hidden;
    min-height: 625px;
    border: 1px solid #ddd;
    border-top: 0;
    > * {
      height: 100%;
      width: 50%;
      padding: 10px;
      overflow: auto;
    }
    .modal-left {
      float: left;
      border-right: 1px solid #ddd;
    }
    .modal-right {
      float: right;
    }
  }
}
</style>

<style lang="scss">
.ck-image-slot {
  width: 100%;
  height: 100%;
  background: #eee;
  color: #999;
  position: relative;
  span {
    display: block;
    line-height: 30px;
    height: 30px;
    position: absolute;
    text-align: center;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
  }
}
</style>
