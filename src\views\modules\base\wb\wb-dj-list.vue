<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    ></searchbar>

    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      border
      ref="singleTable"
      style="width: 100%"
      :max-height="tableHeight"
      @current-change="handleSelectionChange"
      @sort-change="handleSort"
    >
      <!-- <el-table :data="dataList" highlight-current-row v-loading="listLoading" :height="tableHeight" :size="size" border> -->
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="regPot"
        label="卡口名称"
        min-width="180"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="wtTm"
        label="登记时间"
        width="160"
        align="center"
      ></el-table-column>
      <el-table-column prop="regNumber" label="方向" width="100" align="center">
        <template slot-scope="scope">
          <!-- <span
            v-if="
              scope.row.regNumber == 'ZHCK0001' ||
                scope.row.regNumber == 'ZHCK0003' ||
                scope.row.regNumber == 'ZHCK0006' ||
                scope.row.regNumber == 'ZHCK0007'
            "
            >进镇海</span
          > -->
          <span v-if="isInOrOut(scope.row.regNumber)">进镇海</span>
          <span v-else>出镇海</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="cd"
        label="车牌号"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="dvNm"
        label="驾驶员"
        width="90"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="entpNmCn"
        label="承运方"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="csnorNmCn"
        label="装货方"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="csneeNmCn"
        label="卸货方"
        min-width="160"
      ></el-table-column>
      <el-table-column
        prop="goodsNm"
        label="货物"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="loadQty"
        label="货重(KG)"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column prop="" label="查验结果" width="100" align="center">
        <template slot-scope="scope">
          <el-button
            @click="showDetail(scope.row)"
            size="small"
            style="background-color: #67c23a; color: #fff"
            v-if="scope.row.checkStatus == '200'"
            >通过</el-button
          >
          <el-button
            @click="showDetail(scope.row)"
            size="small"
            style="background-color: #f78989; color: #fff"
            v-else-if="scope.row.checkStatus == '400'"
            >异常</el-button
          >
          <el-button
            @click="showDetail(scope.row)"
            size="small"
            type="primary"
            v-else
            >人工</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div ref="paginationbar" class="pagination-wrapper">
      <!-- <div class="grid-operbar ft-lf">
        <el-button
          type="primary"
          size="small"
          :disabled="radio == null"
          v-on:click="histroyTra"
          >历史轨迹</el-button
        >
      </div> -->
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :page-sizes="[20, 30, 50, 100, 200]"
        :current-page.sync="pagination.page"
        :total="total"
        style="float: right"
      >
      </el-pagination>
    </div>
    <!-- 详情 -->
    <CkModal ref="ckModalRef" />
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { queryList,getRegPots } from "@/api/wb";
import { getFuzzyTracCd } from "@/api/vec";
import { getVideoList } from "@/api/video";
import CkModal from "./wb-dj-info";

export default {
  components: {
    Searchbar,
    CkModal
  },
  data: function() {
    return {
      placeType: "",
      showList: true,
      tableHeight: 500,
      title: null,
      showQuery: false,
      currentRow: null,
      radio: null,
      listLoading: false,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      searchItems: {
        normal: [
          {
            name: "卡口名称",
            field: "regNumber",
            type: "filterselect",
            dbfield: "reg_number",
            dboper: "eq",
            options: []
          },
          {
            name: "登记日期",
            field: "wtTm",
            type: "daterange",
            dbfield: "wt_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            default: this.get7Date()
          },
          {
            name: "车牌号",
            field: "cd",
            type: "fuzzy",
            dbfield: "cd",
            dboper: "cn",
            api: this.getTracCd
          },
          {
            name: "驾驶员",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn"
          },
          {
            name: "是否过境",
            field: "isTransit",
            type: "radio",
            dbfield: "isTransit",
            dboper: "nao",
            options: [
              { value: "1", label: "是" },
              { value: "", label: "否" },
            ],
            default: ""
          },
          {
            name: "查验结果",
            field: "checkStatus",
            type: "select",
            dbfield: "check_status",
            dboper: "eq",
            options: [
              { value: "", label: "所有" },
              { value: "200", label: "通过" },
              { value: "400", label: "异常" }
            ],
            default: ""
          }
        ],
        more: []
      },
      defaultSearchItems: [],
      pagination: {
        page: 1,
        limit: 15
      },
      total: 0
    };
  },
  created() {
    let filters = {
      groupOp: "AND",
      rules: []
    };
    filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
    // console.log(params);
    // 登记点列表
    let _this = this;
    getRegPots({ page: 1, limit: 999 })
      .then(res => {
        if (res && res.code === 0) {
          this.searchItems.normal[0].options =  res.data.map((item, index) => {
            return {
              label: item.regPot,
              value: item.regNumber,
            };
          });
        }
      })
      .catch(error => {
        console.log(error);
      });
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //获取近7天日期
    get7Date() {
      let TimeNow = new Date();
      let startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 7);
      return [
        Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00",
        Tool.formatDate(TimeNow, "yyyy-MM-dd") + " 23:59:59"
      ];
    },
    showDtl(row) {
      this.$router.push({ path: "/video/wb/dtl/" + row.id });
    },
    //判断充装类型
    icCdType(iccdType) {
      var iccdMap = {
        0: "途径登记",
        1: "充装",
        "-1": "卸货"
      };
      return iccdMap[iccdType];
    },
    showQueryPanel: function() {
      this.showQuery = !this.showQuery;
    },
    delRows: function(index, row) {
      //删除过磅数据
      if (row && row.argmtWtPk) {
        var argmtWtPk = row.argmtWtPk;
        var that = this;
        this.$confirm("您是否确定删除该过磅数据？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消"
        })
          .then(() => {
            $.ajax({
              url: baseURL + "argmtWt/del/",
              type: "GET",
              data: { pk: argmtWtPk },
              success: function(data) {
                if (data.pojo.json.success) {
                  this.$message({
                    type: "success",
                    message: "删除成功!"
                  });
                } else {
                  that.$message({
                    message: "删除数据失败",
                    type: "error"
                  });
                }
              },
              error: function(error) {
                that.$message({
                  message: "删除数据失败",
                  type: "error"
                });
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除"
            });
          });
      }
    },
    loadAddr(row) {
      this.$router.push({
        path: "/base/wb/form/" + row.argmtWtPk,
        query: { ipPk: row.ipPk }
      });
    },

    // 详情
    showDetail(row) {
      if (row.checkStatus === null) {
        // 人工查验展示一单四状态页面
        this.$router.push({ path: "/base/rteplan/bills/" + row.argmtPk });
      } else {
        // 智慧登记
        this.$nextTick(() => {
          this.$refs.ckModalRef.open(row);
        });
      }
    },
    //历史轨迹
    histroyTra() {
      let location = window.location;
      let vehicleNo = this.currentRow.cd || this.currentRow.traiCd;
      window.open(
        location.origin +
          location.pathname +
          "#/monit/hisTrack?v=" +
          encodeURIComponent(vehicleNo) +
          "&t=" +
          Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },
    //单选事件
    handleSelectionChange(currentRow, oldCurrentRow) {
      this.radio = currentRow.argmtWtPk;
      this.currentRow = currentRow;
    },

    loadQtyFormatter: function(row, column) {
      var value = row.loadQty;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      }
      if (value > 1000) {
        return value;
      } else {
        return (value * 1000).toFixed(0);
      }
    },
    weighFormatter: function(row, column) {
      var value = row.weigh;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      } else {
        return value;
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取列表
    getList: function(data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      this.radio = null; //清空单选
      //关闭loading
      this.listLoading = true;
      //查询列表
      queryList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.total = response.page.totalCount;
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    // 判断进出镇海
    isInOrOut(num) {
      switch (num) {
        case "ZHCK0001":
        case "ZHCK0003":
        case "ZHCK0006":
        case "ZHCK0007":
          return true;
        default:
          return false;
      }
    }
  }
};
</script>

<style scoped>
.el-table .cell,
.el-table th > div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
