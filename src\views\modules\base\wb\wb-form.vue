<template>
    <div class="detail-container" >
        <div class="mod-container-oper">
            <span  class="icon" v-if="vecNo" style="float:left;font-size:18px;line-height:2;color:#333;">{{vecNo}}</span>
            <el-button-group>
                <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
            </el-button-group>
        </div>
        <div class="panel">
            <!-- <div class="panel-header">
                <span class="panel-heading-inner">装卸地址</span>
            </div> -->
            <div class="panel-body">
                <div style="padding:15px 15px 0px 15px;">
                    <b-map-comp :param="mapSetting">
                        <set-lng-lat :get-vec-no="getVecNo"></set-lng-lat>
                    </b-map-comp>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    import BMapComp from "@/components/BMapComp"
    import * as $http from "@/api/prodLoad"
    import {getArgmtWtDetail} from '@/api/wb'
    export default{
        data(){
            return {
                map:null,
                vecNo:"",
                mapName:'BMapComp',
                mapSetting:{
                    scrollWheelZoom:true,
                    mapHeight:'538px',
                    mapDrawing:false
                }
            }
        },
        methods:{
            // 返回上一页
            goBack(){
                this.$router.go(-1);
            },
            getVecNo(vecno){
                this.vecNo = vecno;
            }
        },
        components:{
            BMapComp,
            setLngLat:{
                template:'<span></span>',
                props:["getVecNo"],
                data(){
                    return {
                        map:null
                    }
                },
                created() {
                    this.map = this.$store.state.maps.map
                },
                mounted() {
                    let _this = this;
                    this.getBoundary(function(){
                        _this.getList();
                        _this.getLngLat();
                    });
                },
                methods:{
                    //获取行政区域
                    getBoundary(callback){
                        var bdary = new BMap.Boundary();
                        var map = this.map;
                        
                        this.bdary = bdary;
                        
                        bdary.get("宁波市镇海区", function(rs){       //获取行政区域
                            map.clearOverlays();        //清除地图覆盖物
                            var count = rs.boundaries.length; //行政区域的点有多少个
                            if (count === 0) {
                                this.$message({
                                    type:'error',
                                    message:'未能获取当前输入行政区域'
                                });
                                return ;
                            }

                            var pointArray = [];
                            for (var i = 0; i < count; i++) {
                                var ply = new BMap.Polygon(rs.boundaries[i], {
                                    strokeWeight: 2,
                                    fillOpacity: 0.0,
                                    fillColor: "none",
                                    strokeColor: "#ff0000",
                                    strokeOpacity: 0.8,
                                    strokeStyle: "dashed"
                                }); //建立多边形覆盖物
                                map.addOverlay(ply);  //添加覆盖物
                                pointArray = pointArray.concat(ply.getPath());
                            }
                            if(callback){
                                callback.call();
                            }else{
                                map.setViewport(pointArray);
                            }
                        });
                    },
                    getLngLat(){
                        let pk = this.$route.params.id;
                        let _this = this;
                        let map = this.map;
                        getArgmtWtDetail(pk).then( response => {
                            if(response.code == 0){
                                if(response.argmwt.lng && response.argmwt.lat){
                                    var marker = new BMap.Marker(new BMap.Point(response.argmwt.lng, response.argmwt.lat));
                                    var label = new BMap.Label("装货点",{offset:new BMap.Size(20,-10)});
	                                marker.setLabel(label);
                                    map.addOverlay(marker);
                                }
                                response.argmwt.cd && (this.getVecNo(response.argmwt.cd))
                            }
                        })
                        .catch( err => {

                        });
                    },
                    getList(){
                        let pk = this.$route.query.ipPk;
                        let _this = this;
                        $http.getListEntp(pk)
                        .then(response => {
                            if (response.code == 0 && response.data.length > 0) {
                                let data = response.data;
                                let pointArr = [];
                                for(var i=0,len=data.length;i<len;i++){
                                    var lnglat = data[i].lnglat;
                                    var lines = lnglat.match(/(\b(\w+\.?\w{0,})\b(?!['":]))/g); //正则匹配坐标值，返回坐标数组
                                    var points = [],
                                    j = 0,
                                    len2 = lines.length; //坐标点容器，每次清空后存储两个值

                                    for (; j < len2; j += 2) {
                                        var point = new BMap.Point(lines[j], lines[j + 1]);
                                        points.push(point);
                                        pointArr.push(point)
                                    }

                                    var polygon = new BMap.Polygon(points,{
                                            strokeWeight: 2,
                                            fillOpacity: 0.3,
                                            fillColor: "#ff1f3e",
                                            strokeColor: "#ff0000"
                                        });
                                    _this.map.addOverlay(polygon);
                                }
                                _this.map.setViewport(pointArr);
                                
                            }
                        })
                        .catch(error => {
                            throw new Error(error);
                        });
                    }
                }
            }
        }
    }
</script>