<template>
    <div class="detail-container" v-loading="detailLoading">
        <div class="mod-container-oper">
            <el-button-group>
            <el-button type="warning" @click="goBack"><i class="el-icon-back"></i>&nbsp;返回</el-button>
            </el-button-group>
        </div>
        <div class="panel">
            <div class="panel-header">
                <span class="panel-heading-inner">过磅数据详情</span>
            </div>
            <div class="panel-body">
                <ul class="detail-ul">
                <li class="col-half">
                    <div class="detail-desc">运输企业：</div>
                    <div class="detail-area" :title="rteplan.entpNmCn">{{rteplan.entpNmCn}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">牵引车牌：</div>
                    <div class="detail-area" :title="rteplan.cd">{{rteplan.cd}}</div>
                </li>
<!--                 <li class="col-half">
                    <div class="detail-desc">gps最后接入时间:：</div>
                    <div class="detail-area" :title="rteplan.gps">{{rteplan.gps}}</div>
                </li> -->
                <li class="col-half">    
                    <div class="detail-desc">挂车牌：</div>
                    <div class="detail-area" :title="rteplan.traiCd">{{rteplan.traiCd}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">发货日期：</div>
                    <div class="detail-area" :title="rteplan.wtTm">{{rteplan.wtTm | formatDate("yyyy-MM-dd")}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">装货地区：</div>
                    <div class="detail-area" :title="rteplan.csnorWhseDist">{{rteplan.csnorWhseDist}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">卸货地区：</div>
                    <div class="detail-area" :title="rteplan.csneeWhseDist">{{rteplan.csneeWhseDist}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">装货单位：</div>
                    <div class="detail-area" :title="rteplan.csnorWhseAddr">{{rteplan.csnorWhseAddr}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">卸货单位：</div>
                    <div class="detail-area" :title="rteplan.csneeNmCn">{{rteplan.csneeNmCn}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">驾驶员：</div>
                    <div class="detail-area" :title="rteplan.dvNm">{{rteplan.dvNm}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">驾驶员联系电话：</div>
                    <div class="detail-area" :title="rteplan.dvMob">{{rteplan.dvMob}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">驾驶员从业资格证有效期：</div>
                    <div class="detail-area" :title="rteplan.driVldTo">{{rteplan.driVldTo}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">押运员：</div>
                    <div class="detail-area" :title="rteplan.scNm">{{rteplan.scNm}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">押运员联系电话：</div>
                    <div class="detail-area" :title="rteplan.scMob">{{rteplan.scMob}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">押运员从业资格证有效期：</div>
                    <div class="detail-area" :title="rteplan.scVldto">{{rteplan.scVldto}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">提单号：</div>
                    <div class="detail-area" :title="rteplan.shipOrdCustCd">{{rteplan.shipOrdCustCd}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">货物类别：</div>
                    <div class="detail-area" :title="rteplan.chemGbLv">{{rteplan.chemGbLv}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">货物名称：</div>
                    <div class="detail-area" :title="rteplan.goodsNm">{{rteplan.goodsNm}}</div>
                </li>
                <li class="col-half">
                    <div class="detail-desc">货物重量(KG)：</div>
                    <div class="detail-area" :title="rteplan.loadQty">{{rteplan.loadQty}}</div>
                </li>
                <li class="col-half">    
                    <div class="detail-desc">作业类型：</div>
                    <div class="detail-area" :title="rteplan.icCd">
                        <span v-if="rteplan.icCd == '1'">充装</span>
                        <span v-else-if="rteplan.icCd == '-1'">卸货</span>
                    </div>
                </li>
            </ul>
            </div>
        </div>
    </div>
</template>
<script>
import * as Tool from '@/utils/tool';
import {getArgmtWtDetail, getIpLicByPk, getGoodsInfo} from '@/api/wb'
export default {
    name:'WbInfo',
    filters:{
        formatDate:Tool.formatDate
    },
    created(){
        let argmtPk = this.$route.params.id;
        let icCd = this.$route.query.icCd;
        let _this = this;
        let scPk,dvPk,enchPk;

        
        getArgmtWtDetail(argmtPk).then( response => {
            if(response.code == 0){
                this.rteplan = response.argmwt;
                this.$set(this.rteplan,'icCd',icCd);
                scPk = response.data.scPk;
                dvPk = response.data.dvPk;
                enchPk = response.data.enchPk;

                this.rteplan.reqtTm = Tool.formatDate(this.rteplan.reqtTm,'yyyy-MM-dd');

                //押运员从业资格证有效期
                if (scPk) {
                    getIpLicByPk(scPk, "8010.404")
                    .then(response => {
                        if (response.code == 0 && response.data) {
                            var scvldto = response.data.vldTo;
                            _this.rteplan.scVldto = scvldto;
                        }
                    })
                    .catch(error => {
                        console.log(error);
                    });
                }

                //驾驶员从业资格证有效期
                if (dvPk) {
                    getIpLicByPk(dvPk, "8010.403")
                    .then(response => {
                        if (response.code == 0 && response.data) {
                            var vldTo = response.data.vldTo;
                            _this.rteplan.driVldTo = vldTo;
                        }
                    })
                    .catch(error => {
                        throw new Error(error);
                    });
                }

                //货物类别
                if (enchPk) {
                    getGoodsInfo(enchPk)
                    .then(response => {
                        if (response.code == 0 && response.data) {
                            _this.rteplan.chemGbLv = response.data.chemGbLv;
                        }
                    })
                    .catch(error => {
                        throw new Error(error);
                    });
                }
            }
        })
        .catch( error => {

        });
    },
    data(){
        return {
            detailLoading:false,
            rteplan:{

            }
        }
    },
    methods:{
        // 返回上一页
        goBack(){
            this.$router.go(-1);
        }
    }
}
</script>
<style>

</style>
