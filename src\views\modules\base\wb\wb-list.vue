<template>
  <div class="app-main-content" v-show="!listLoading">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" :isexport="true"
      @resizeSearchbar="resizeSearchbar" @search="getList">
      <el-button slot="button" type="primary" icon="el-icon-document" size="small" @click="exportTable">导出</el-button>
    </searchbar>
    <!--列表-->
    <div id="export">
      <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%"
        size="mini" :max-height="tableHeight" border @sort-change="handleSort">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="rcdTm" label="检查时间" width="160"></el-table-column>
        <el-table-column prop="entpNm" label="装卸企业" width="200"></el-table-column>
        <el-table-column prop="rtePlanCd" width="220" label="运单编号">
          <template slot-scope="scope">
            <el-button @click.native.prevent="showBills(scope.row.rtePlanId)" type="text">{{ scope.row.rtePlanCd }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="tracCd" label="牵引车号"></el-table-column>
        <el-table-column prop="traiCd" label="挂车号"> </el-table-column>
        <el-table-column prop="carrNm" label="运输公司"> </el-table-column>
        <el-table-column prop="type" label="装卸货状态" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.type == 0">装货前</span>
            <span v-else-if="scope.row.type == 2">装货后</span>
            <span v-else-if="scope.row.type == 1">卸货</span>
          </template>
        </el-table-column>
        <el-table-column prop="enchNm" label="货物名称"></el-table-column>
        <el-table-column prop="enchQty" align="center" width="110" label="装卸数量(吨)">
          <template slot-scope="scope">
            <span>{{ scope.row.enchQty }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="checkOperator" label="安全检查人员" width="120"></el-table-column>
        <el-table-column align="center" prop="action" width="90" label="检查记录">
          <template slot-scope="scope">
            <el-button @click="viewDetail(scope.row.id, scope.row.type)" type="text" size="mini">
              <template v-if="scope.row.type === 0">{{
                scope.row.checkStatus === 0
                ? "同意装货"
                : "不同意装货,原因：" + scope.row.checkResult
              }}</template>
              <template v-if="scope.row.type === 1">{{
                scope.row.checkStatus === 0
                ? "同意卸货"
                : "不同意卸货,原因：" + scope.row.checkResult
              }}</template>
              <template v-if="scope.row.type === 2">{{
                scope.row.checkStatus === 0
                ? "同意出厂"
                : "不同意出厂,原因：" + scope.row.checkResult
              }}</template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <el-button @click="printExcel">打印</el-button> -->
    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="text" size="small" class="link_btn" title="查看历史装卸记录" @click="historyRecord">
          <svg-icon icon-class="link" class-name="svg-icon" />
          历史装卸记录
        </el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <!-- 运单详情 -->
    <el-dialog title="运单详情" :visible.sync="visibleOfRteplan" append-to-body width="80%" top="5vh" class="detail-dialog">
      <rteplan-info ref="rteplanInfo" :isCompn="true"></rteplan-info>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/record";
import { getFuzzyTracCd } from "@/api/vec";
import * as Tool from "@/utils/tool";
import RteplanInfo from "@/views/modules/base/rtePlan/rtePlan-bills";
export default {
  name: "RecordList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 230,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      listLoading: false,
      addLoading: false,
      defaultSearchItems: [],
      searchItems: {
        normal: [
          {
            name: "检查时间",
            field: "rcdTm",
            type: "daterange",
            dbfield: "rcd_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            default: this.get7Date(),
          },
          {
            name: "装卸企业",
            field: "entpNm",
            type: "filterselect",
            dbfield: "entp_nm",
            dboper: "eq",
            options: [],
          },
          {
            name: "牵引车号",
            field: "tracCd",
            type: "fuzzy",
            dbfield: "trac_cd",
            dboper: "cn",
            api: this.getTracCd,
          },
          {
            name: "挂车号",
            field: "traiCd",
            type: "fuzzy",
            dbfield: "trai_cd",
            dboper: "eq",
            api: this.getTraiCd,
          },
          {
            name: "装卸货状态",
            field: "type",
            type: "select",
            dbfield: "type",
            dboper: "eq",
            options: [
              {
                label: "装货前",
                value: "0",
              },
              {
                label: "装货后",
                value: 2,
              },
              {
                label: "卸货",
                value: 1,
              },
            ],
          },
          /*  {
              name: "运单编号",
              field: "rtePlanCd",
              type: "text",
              dbfield: "rte_plan_cd",
              dboper: "cn",
            },*/
        ],
        more: [
          {
            name: "货物名称",
            field: "enchNm",
            type: "text",
            dbfield: "ench_nm",
            dboper: "cn",
          },
        ],
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      visibleOfRteplan: false,
    };
  },
  computed: {
    allSearchItems() {
      return [...this.searchItems.normal, ...this.searchItems.more];
    },
  },
  components: {
    Searchbar,
    RteplanInfo,
  },
  created() {
    //获取企业列表
    let filters = {
      groupOp: "AND",
      rules: [],
    };
    filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
    // console.log(params);
    $http
      .getEntpList(params)
      .then((response) => {
        if (response.code == 0) {
          this.allSearchItems.forEach((item) => {
            if (item.field === "entpNm") {
              item.options = response.page.list.map((item, index) => {
                return { label: item.entpName, value: item.entpName };
              });
            }
          });
        }
      })
      .catch((error) => {
        console.log(error);
      });
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    setTimeout(function () {
      _this.setTableHeight();
    }, 1000);
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
     historyRecord(){
      this.$router.push({
        path: "/loadMonit/shipment/record/history"
      });
    },
    printExcel() {
      printExcel("export");
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 190 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList(data, sortParam) {
      let _this = this;
      this.listLoading = true;
      const loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "transparent",
      });
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      delete param.total;
      $http
        .getRecordList(param)
        .then((response) => {
          if (response.code == 0) {
            let list = response.page.list;
            console.log(list)
            _this.pagination.total = response.page.totalCount;
            _this.list = list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
          loading.close();
        })
        .catch((error) => {
          console.log(error);
          _this.listLoading = false;
          loading.close();
        });
    },
    //获取近7天日期
    get7Date() {
      var TimeNow = new Date();
      var startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * 7);
      var endDay = TimeNow;
      return [
        Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00",
        Tool.formatDate(endDay, "yyyy-MM-dd") + " 23:59:59",
      ];
    },
    viewDetail(cd, type) {
      this.$router.push({
        path: "/loadMonit/record/info/" + cd,
        query: {
          type: type,
        },
      });
    },
    // 单据
    showBills: function (argmtPk) {
      // this.$router.push({
      //   path: "/transport/rteplan/bills/" + argmtPk,
      // });
      if (!argmtPk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfRteplan = true;
      this.$nextTick(() => {
        this.$refs.rteplanInfo.rtePlanNewByPk(argmtPk);
      });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(
        (error) => {
          cb([]);
          console.log(error);
        }
      );
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map((item) => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(
        (error) => {
          cb([]);
          console.log(error);
        }
      );
      if (res) {
        if (res.data.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.data.map((item) => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    exportTable() {
      if (this.list.length > 0) {
        let arr = this.list.map((item) => {
          return item.cd;
        });
        let str = arr.join(",");
        $http
          .exportList(str)
          .then((res) => {
            let a = document.createElement("a");
            let blob = new Blob([res]);
            let url = window.URL.createObjectURL(blob);
            var _date = new Date();

            a.href = url;
            a.download = arr.length > 1 ?
              "装卸检查监测_" +
              (_date.getFullYear() +
                "-" +
                (_date.getMonth() + 1) +
                "-" +
                _date.getDate()) +
              ".zip" : "装卸检查监测_" +
              (_date.getFullYear() +
                "-" +
                (_date.getMonth() + 1) +
                "-" +
                _date.getDate()) +
              ".docx";
            a.click();
            window.URL.revokeObjectURL(url);
          })
          .catch((err) => { });
      } else {
        this.$message.error("请选择数据");
      }
    },
  },
};
</script>
<style lang="scss">
@media print {
  @page {
    /* 纵向打印 */
    size: portrait;

    /* 横向打印 */
    /*size: landscape;*/

    /* 去掉页眉页脚*/
    margin-top: 0;
    margin-bottom: 0;
  }

  /*打印不显示打印按钮*/
  .print-button-container {
    display: none !important;
  }
}
</style>
