<template>
  <div class="chart-bordered" style="width:100%;height:100%;">
    <div class="container">
      <h3 v-if="title">{{title}}</h3>
      <div class="echarts-container" ref="echartsWape" :style="{'height':echartsHeight}"></div>
    </div>
    <span class="border-top-left"></span>
    <span class="border-top-right"></span>
    <span class="border-bottom-left"></span>
    <span class="border-bottom-right"></span>
  </div>
</template>


<script>
import * as $http from "@/api/dashboard";
export default {
  name: "barEcharts",
  props: {
    title: {
      type: String,
    },
    echartsHeight: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      mainChart: null,
      options: null, // echarts的配置文件

      colorList: [
        "#0487ed",
        "#3eb177",
        "#c1c049",
        "#c59838",
        "#cd6237",
        "#e11000",
        "#aa00a0",
        "#59057b",
        "#ffa96a",
        "#7da87b",
        "#84f2d6",
        "#53c7f0",
        "#005585",
        "#2931b3",
        "#0666e8",
      ],
      seriesData: [],
      windowResizeFun: null,
    };
  },
  destroyed() {
    if (this.mainChart) {
      this.mainChart.dispose();
      this.mainChart = null;
    }
    if (this.windowResizeFun) {
      window.removeEventListener("resize", this.windowResizeFun, false);
    }
  },
  mounted() {
    let _this = this;
    this.windowResizeFun = function () {
      if (_this.mainChart) {
        _this.mainChart.resize();
      }
    };
    window.addEventListener("resize", this.windowResizeFun);
  },
  methods: {
    // 随机生成十六进制颜色
    randomHexColor() {
      var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
      while (hex.length < 6) {
        //while循环判断hex位数，少于6位前面加0凑够6位
        hex = "0" + hex;
      }
      return "#" + hex; //返回‘#'开头16进制颜色
    },

    // 根据传入的配置项设置图表配置内容
    getOptions(config) {
      let options = {
        backgroundColor: "transparent",
        title: {
          text: config.title || "",
          textStyle: {
            // color: data.color,
            fontSize: 12,
            fontWeight: "normal",
          },
          right: "5",
          top: "0",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            var tar = params[0];
            return tar.name + "数量：" + tar.data;
          },
        },
        grid: config.grid || {
          left: "5%",
          right: "5%",
          top: "5%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLabel: {
            textStyle: {
              color: "#fff",
            },
            // interval:0,
            rotate: config.rotate || 0,
            formatter: function (val) {
              if (val.length > 4) {
                var _val = val.substring(0, 5) + "\n" + val.substring(5); // 让series 中的文字超出5个显示...
                return _val;
              }
              return val;
            },
          },
          data: [],
        },
        yAxis: {
          splitLine: {
            show: false,
          },
        },
        series: [],
      };

      if (config && config.interval != undefined) {
        options.xAxis.axisLabel.interval = config.interval;
      }
      return options;
    },

    /**
     * 图表设置数据
     * seriesData：柱状图表数据---[value,value,value]
     * xAxis: X轴的数据---[]
     * config:echart的options配置
     * clickFun:点击的回调函数
     */
    setData(seriesData, xAxis, config, clickFun) {
      let _this = this,
        options = null;
      config = config || {};
      // if (!this.options) {
        this.options = this.getOptions(config);
      // }
      options = Object.assign({}, this.options);

      /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
      options.series = [];
      /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/

      options.xAxis.data = xAxis; // 设置X轴数据
      options.series.push({
        name: "数量",
        type: "bar",
        barWidth: "60%",
        label: {
          normal: {
            show: true,
            position: "top",
            textStyle: {
              color: "#fff",
            },
          },
        },
        itemStyle: {
          normal: {
            color: config.barColor?config.barColor:function (params) {
              if (params.dataIndex >= _this.colorList.length) {
                _this.colorList.push(_this.randomHexColor());
              }
              return _this.colorList[params.dataIndex];
            },
          },
        },
        data: seriesData,
      });

      if (this.mainChart) {
        this.mainChart.setOption(options);
        this.seriesData = seriesData;
        this.options = options;
      } else {
        let _this = this;
        let mainChart = this.$echarts.init(this.$refs.echartsWape, "dark");
        mainChart.setOption(options, true);
        this.mainChart = mainChart;
      }
      if (clickFun && this.mainChart) {
        this.mainChart.on("click", clickFun);
      }
    },
  },
};
</script>
<style scoped>
.echarts-container {
  width: 100%;
}
.chart-bordered {
  background: url("~@/assets/dashboard-img/echart-bg.png");
  border: 1px solid #144277;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 20px;
  height: 100%;
  width: 100%;
}

.chart-bordered h3 {
  color: #fff;
  background-color: #0b1d54;
  border: 1px solid #2075a9;
  line-height: 1.8vw;
  /* height: 35px; */
  font-size: 0.8vw;
  margin: 10px;
  font-weight: 100;
  padding: 5px 10px;
  position: relative;
  text-align: center;
}

.border-top-left,
.border-top-right,
.border-bottom-left,
.border-bottom-right {
  position: absolute;
  width: 10px;
  height: 10px;
  line-height: 10px;
  display: block;
}

.border-top-left {
  left: 0;
  top: 0;
  border-left: solid 2px #0e68b1;
  border-top: solid 2px #0e68b1;
}

.border-top-right {
  right: 0;
  top: 0;
  border-right: solid 2px #0e68b1;
  border-top: solid 2px #0e68b1;
}

.border-bottom-left {
  left: 0;
  bottom: 0;
  border-left: solid 2px #0e68b1;
  border-bottom: solid 2px #0e68b1;
}

.border-bottom-right {
  right: 0;
  bottom: 0;
  border-right: solid 2px #0e68b1;
  border-bottom: solid 2px #0e68b1;
}
</style>
