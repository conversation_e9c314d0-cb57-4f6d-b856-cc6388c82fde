<template>
    <div class="chart-bordered" style="width:100%;height:100%;">
        <div class="container">
            <div class="sub-container">
                <el-row class="el-row-1">
                    <el-col :span="24" class="base-info-title">
                        <span>企业登记统计</span>
                        <span>{{ dataSource.entpCnt }}</span>
                    </el-col>
                    <el-col :span="24" class="base-info-cont">
                        <el-col :span="12">
                            <div class="text-center blue-bg">
                                <span>已通过</span>
                                <span>{{ dataSource.entpApprCnt }}</span>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="text-center red-bg">
                                <span>未通过</span>
                                <span>{{ dataSource.entpCnt - dataSource.entpApprCnt }}</span>
                            </div>
                        </el-col>
                    </el-col>
                </el-row>

                <el-row class="el-row-2">
                    <el-col :span="24" class="base-info-title">
                        <span>车辆登记统计</span>
                        <span>{{ dataSource.vecCnt }}</span>
                    </el-col>
                    <el-col :span="24" class="base-info-cont">
                        <el-col :span="12">
                            <div class="text-center blue-bg">
                                <span>已通过</span>
                                <span>{{ dataSource.vecApprCnt }}</span>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="text-center red-bg">
                                <span>未通过</span>
                                <span>{{ dataSource.vecCnt - dataSource.vecApprCnt }}</span>
                            </div>
                        </el-col>
                    </el-col>
                </el-row>

                <el-row class="el-row-3">
                    <el-col :span="24" class="base-info-title">
                        <span>人员登记统计</span>
                        <span>{{ dataSource.persCnt }}</span>
                    </el-col>
                    <el-col :span="24" class="base-info-cont">
                        <el-col :span="12">
                            <div class="text-center blue-bg">
                                <span>已通过</span>
                                <span>{{ dataSource.persApprCnt }}</span>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="text-center red-bg">
                                <span>未通过</span>
                                <span>{{ dataSource.persCnt - dataSource.persApprCnt }}</span>
                            </div>
                        </el-col>
                    </el-col>
                </el-row>

                <!-- <el-row class="el-row-4">
                    <el-col :span="24" class="base-info-title">
                        <span>罐体备案统计</span>
                        <span>{{dataSource.tankCnt}}</span>
                    </el-col>
                </el-row> -->
            </div>

        </div>
        <span class="border-top-left"></span>
        <span class="border-top-right"></span>
        <span class="border-bottom-left"></span>
        <span class="border-bottom-right"></span>
    </div>
</template>


<script>
import * as $http from '@/api/dashboard';
export default {
    name: 'barEcharts',
    props: {
        title: {
            type: String
        },
        dataSource: {
            type: Object,
            default() {
                return {
                    "entpApprCnt": 0,
                    "entpCnt": 0,
                    "vecApprCnt": 0,
                    "tankCnt": 0,
                    "persApprCnt": 0,
                    "persCnt": 0,
                    "vecCnt": 0
                }
            }
        }
    },
    data() {
        return {

        };
    },
    destroyed() {

    },
    mounted() {

    },
    methods: {
    }
};
</script>
<style scoped>
.echarts-container {
    width: 100%;
}

.chart-bordered {
    /* background: url('~@/assets/dashboard-img/echart-bg.png'); */
    /* border: 1px solid #144277; */
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    height: 100%;
    width: 100%;
}

/*
.border-top-left,
.border-top-right,
.border-bottom-left,
.border-bottom-right {
    position: absolute;
    width: 10px;
    height: 10px;
    line-height: 10px;
    display: block;
}

.border-top-left {
    left: 0;
    top: 0;
    border-left: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-top-right {
    right: 0;
    top: 0;
    border-right: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-bottom-left {
    left: 0;
    bottom: 0;
    border-left: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}

.border-bottom-right {
    right: 0;
    bottom: 0;
    border-right: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
} */

.sub-container {
    color: #fff;
    padding: 10px;
}

.el-row-1,
.el-row-2,
.el-row-3 {
    border-bottom: 1px solid #0e68b1;
}

.base-info-title,
.base-info-cont {
    padding: 10px 0px;
    font-size: .7vw;
}

.blue-bg {
    padding: 6px;
    margin: 0 6px;
    background-color: #2b54a9;
}

.red-bg {
    padding: 6px;
    margin: 0 6px;
    background-color: #b73928;
}
</style>
