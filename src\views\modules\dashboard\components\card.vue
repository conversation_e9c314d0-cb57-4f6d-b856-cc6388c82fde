<template>
  <div class="card" :style="innerStyle">
    <div class="card-header">
      <div class="card-title">{{ title }}</div>
      <div class="card-header-oper">
        <slot name="oper"></slot>
      </div>
    </div>
    <div class="card-content">
      <el-scrollbar style="height: 100%" v-if="scroll">
        <slot></slot>
      </el-scrollbar>
      <div style="height: 100%; position: relative" v-if="!scroll">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    innerStyle: {
      type: Object,
      default: () => { },
    },
    scroll: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes toShow {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.card {
  // flex: 1;
  min-height: 100px;
  width: 100%;
  box-sizing: border-box;
  opacity: 0;
  animation: toShow 1s ease;
  animation-fill-mode: forwards;

  .card-header {
    height: 40px;
    line-height: 40px;
    position: relative;
    font-size: 20px;
    padding: 0;

    &:before {
      content: "";
      display: block;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("~static/img/dashboard/title.png") no-repeat left;
      background-size: 100% 100%;
      height: 30px;
    }

    .card-title {
      height: 100%;
      margin-left: 40px;
      line-height: 40px;
      font-weight: bold;
      color: #fff;
      letter-spacing: 5px;
      position: relative;
      // background: linear-gradient(0deg, #5cc0fa 0.68359375%, #ffffff 100%);
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      // font-style: italic;
    }

    .card-header-oper {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      font-size: 12px;
      // width: 240px;
      // line-height: 30px;
      color: #fff;
      display: flex;
      justify-content: space-evenly;

      span {
        display: inline-block;
        height: 28px;
        line-height: 30px;
        padding: 0 15px;
        font-size: 12px;
        text-align: center;
        border-radius: 6px;
        margin-left: 15px;
        cursor: pointer;
        background: rgba(62, 149, 247, 0);
        border: 1px solid #3B80D3;

        &.active {
          background: rgba(62, 149, 247, 0.3);
          border: 1px solid #3B80D3;
        }
      }
    }
  }

  .card-content {
    height: calc(100% - 40px);

    // >div {
    //   display: flex;
    //   flex-direction: column;
    //   justify-content: center;
    // }
  }
}
</style>

<style lang="scss" scoped>
.card ::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: auto;
    // margin-right: 0 !important;
  }

  .el-scrollbar__view {
    height: 100%;
  }

  .el-scrollbar__view::before {
    content: "";
    display: table;
  }
}
</style>
