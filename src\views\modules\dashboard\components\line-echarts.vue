<template>
    <div class="chart-bordered" style="width:100%;height:100%;">
        <div class="container">
            <h3 v-if="title">{{title}}</h3>
            <div class="echarts-container" ref="echartsWape" :style="{'height':echartsHeight}"></div>
        </div>
        <span class="border-top-left"></span>
        <span class="border-top-right"></span>
        <span class="border-bottom-left"></span>
        <span class="border-bottom-right"></span>
    </div>
</template>


<script>
import * as $http from '@/api/dashboard';
export default {
    name: 'lineEcharts',
    props: {
        title: {
            type: String
        },
        echartsHeight: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            mainChart: null,
            options:null,                // echarts的配置文件

            colorList:['#3398DB', '#f8ec04'],
            areaColorList:['rgba(72, 153, 241, 0.3)','rgba(72, 153, 241, 0.3)'],
            
            seriesData:[],
            windowResizeFun:null
        };
    },
    destroyed() {
        if (this.mainChart) {
			this.mainChart.dispose();
			this.mainChart = null;
		}
		if(this.windowResizeFun){
			window.removeEventListener("resize",this.windowResizeFun,false)
		}
    },
    mounted() {
        let _this = this;
        this.windowResizeFun = function(){
            if (_this.mainChart) {
                _this.mainChart.resize();
            }
        }
        window.addEventListener('resize', this.windowResizeFun);
    },
    methods: {
        // 随机生成十六进制颜色
        randomHexColor() {
            var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
            while (hex.length < 6) {
                //while循环判断hex位数，少于6位前面加0凑够6位
                hex = '0' + hex;
            }
            return '#' + hex; //返回‘#'开头16进制颜色
        },

        // 根据传入的配置项设置图表配置内容
        getOptions(config){
            return {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: config.trigger || 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    show:false,
                    data: []
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    top: '3%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis:{
                    type: 'category',
                    name: config.xAxisName || '',
                    boundaryGap: true,
                    axisLine: {
                        lineStyle: {
                            color: '#6995aa',
                            width:  1
                        }
                    },
                    axisLabel: {
                        fontSize: 11,
                        color:  "#6995aa"
                    },
                    axisTick: {
                        show: true,
                        lineStyle: {
                            color: '#6995aa',
                            width:1
                        }
                    },
                    splitLine: {
                        show: config.splitLine || false,
                        lineStyle: {
                            type: 'dotted',
                            color: '#6995aa'
                        }
                    },
                    data: []
                },
                yAxis:  [
                    {
                        type: 'value',
                        name: config.yAxisName || '',
                        axisLine: {
                            lineStyle: {
                                color: '#6995aa',
                                width:1
                            }
                        },
                        axisLabel: {
                            fontSize: 11,
                            color:  "#6995aa"
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: '#6995aa',
                                width:1
                            }
                        },
                        splitLine: {
                            show: config.splitLine || false,
                            lineStyle: {
                                type: 'dotted',
                                color: '#6995aa'
                            }
                        },
                    }
                ],
                series: []
            }
        },

        // 图表设置数据
        /**
         * seriesData：折现图表数据---[{name:'',data:[]},{name:'',data:[]}]   PS:其中每个对象内formatter属性可加可不加，用于显示提示内容
         * xAxis: X轴的数据---[]
         * config:echart的options配置
         * clickFun:点击的回调函数
         */
        setData(seriesData, xAxis, config, clickFun){
            let _this = this, options = null;
            config = config || {};
            if(!this.options){
                this.options = this.getOptions(config);
            }
            options = Object.assign({},this.options);

            /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
            options.legend.data = [];
            options.series = [];
            /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/

            seriesData.forEach((item,index)=>{          // 设置y轴数据
                options.legend.data.push({name:item.name,textStyle:{color:_this.colorList[index],fontSize:"12"}});
                let ops = {
                    name: item.name,
                    type: 'line',
                    data: item.data,
                    areaStyle: {normal: {color:_this.areaColorList[index]}},
                    lineStyle: {
                        normal: {
                            color: config.lineStyleColor || _this.colorList[index],
                            width: 2
                        }
                    },
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: config.labelColor || _this.colorList[index]
                            },
                            formatter:item.formatter || "{c}"
                        }
                    },
                    itemStyle : {
                        color:config.lineStyleColor || _this.colorList[index]
                    },
                    lineStyle: {
                        width:config.lineWidth || 2
                    }
                }
                if(config.issmooth){
                    ops = Object.assign(ops,{
                        smooth: true,
                        symbolSize: 5,
                        symbol: 'circle'
                    })
                }
                options.xAxis.data = xAxis;              // 设置X轴数据
                if(seriesData.length<=1){
                    options.legend.show = false;
                    ops.areaStyle = null;
                }else{
                    options.legend.show = true;
                }
                options.series.push(ops)
            })

            if(this.mainChart){
                this.mainChart.setOption(options);
                this.seriesData = seriesData;
                this.options = options;
            }else {
                let _this = this;
                let mainChart = this.$echarts.init(this.$refs.echartsWape, 'dark');
                mainChart.setOption(options, true);
                this.mainChart = mainChart;
            }
            if(clickFun && this.mainChart){
                this.mainChart.on("click", clickFun);
            }
        }
    }
};
</script>
<style scoped>
.echarts-container {
    width: 100%;
}
.chart-bordered {
    background: url('~@/assets/dashboard-img/echart-bg.png');
    border: 1px solid #144277;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    height: 100%;
    width: 100%;
}

.chart-bordered h3 {
    color: #fff;
    background-color: #0b1d54;
    border: 1px solid #2075a9;
    line-height: 1.8vw;
    /* height: 35px; */
    font-size: .8vw;
    margin: 10px;
    font-weight: 100;
    padding: 5px 10px;
    position: relative;
    text-align: center;
}

.border-top-left,
.border-top-right,
.border-bottom-left,
.border-bottom-right {
    position: absolute;
    width: 10px;
    height: 10px;
    line-height: 10px;
    display: block;
}

.border-top-left {
    left: 0;
    top: 0;
    border-left: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-top-right {
    right: 0;
    top: 0;
    border-right: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-bottom-left {
    left: 0;
    bottom: 0;
    border-left: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}

.border-bottom-right {
    right: 0;
    bottom: 0;
    border-right: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}
</style>
