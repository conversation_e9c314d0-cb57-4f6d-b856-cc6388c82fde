<template>
    <div style="width:100%;height:100%;" :class="{'chart-bordered':title?true:false}">
        <div class="container">
            <h3 v-if="title">{{title}}</h3>
            <div class="echarts-container" ref="echartsWape" :style="{'height':echartsHeight}"></div>
        </div>
        <template v-if="title">
        <span class="border-top-left"></span>
        <span class="border-top-right"></span>
        <span class="border-bottom-left"></span>
        <span class="border-bottom-right"></span>
        </template>
        
    </div>
</template>


<script>
import * as $http from '@/api/dashboard';
import * as globalData from '@/utils/globalData'
export default {
    name: 'barEcharts',
    props: {
        title: {
            type: String
        },
        echartsHeight: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            mainChart: null,
            colorList: ['#0487ed', '#3eb177', '#c1c049', '#c59838', '#cd6237', '#e11000', '#aa00a0', '#59057b', '#ffa96a', '#7da87b', '#84f2d6', '#53c7f0', '#005585', '#2931b3', '#0666e8'],
            seriesData: [],
            // planePath: 'path://M1705.06,1318.313v-89.254l-319.9-221.799l0.073-208.063c0.521-84.662-26.629-121.796-63.961-121.491c-37.332-0.305-64.482,36.829-63.961,121.491l0.073,208.063l-319.9,221.799v89.254l330.343-157.288l12.238,241.308l-134.449,92.931l0.531,42.034l175.125-42.917l175.125,42.917l0.531-42.034l-134.449-92.931l12.238-241.308L1705.06,1318.313z',
            symbolPath:'path://M52.8,189.5l0-14.5c14.1-1.8,25-13.8,25-28.4c0-14.6-11-26.6-25.1-28.3l0-11c0-5.9,4.8-10.7,10.7-10.7l96.5-0.2c5.9,0,10.7,4.8,10.7,10.7l0.1,82.2c0,5.9-4.8,10.7-10.7,10.7l-96.5,0.2C57.7,200.2,52.8,195.4,52.8,189.5L52.8,189.5z M52.7,78.7l0-7.4c14.1-1.8,25-13.8,25-28.4c0-14.6-11-26.6-25.1-28.3l0-7.4c0-3.9,3.2-7.2,7.1-7.2L99,0.1c14.3,0,50.1,24.4,50.1,35.6l0.1,42.9c0,3.9-3.2,7.2-7.1,7.2l-82.2,0.1C55.9,85.9,52.7,82.7,52.7,78.7L52.7,78.7z M131.2,64.3c0,0,0.5-11.3,0-24.8C131,33.8,109.9,17.9,99,17.9l-3.6,0l0.1,46.4L131.2,64.3L131.2,64.3z M66.9,43c0,9.9-8,17.9-17.8,17.9c-9.9,0-17.9-8-17.9-17.8c0-9.9,8-17.9,17.8-17.9C58.9,25.1,66.9,33.1,66.9,43L66.9,43z M67.1,147.5c0,10.4-8.4,18.8-18.7,18.8c-10.4,0-18.8-8.4-18.8-18.7c0-10.4,8.4-18.8,18.7-18.8C58.6,128.8,67,137.1,67.1,147.5L67.1,147.5z',
			geoCoordMap: globalData.geoCoordMap,
			windowResizeFun:null,
            inOutType:''//in：进 ；out：出
        };
    },
    destroyed() {
        if (this.mainChart) {
			this.mainChart.dispose();
			this.mainChart = null;
		}
		if(this.windowResizeFun){
			window.removeEventListener("resize",this.windowResizeFun,false)
		}
    },
    mounted() {
        let _this = this;
        this.windowResizeFun = function(){
            if (_this.mainChart) {
                _this.mainChart.resize();
            }
        }
        window.addEventListener('resize', this.windowResizeFun);
    },
    methods: {
        // 随机生成十六进制颜色
        randomHexColor() {
            var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
            while (hex.length < 6) {
                //while循环判断hex位数，少于6位前面加0凑够6位
                hex = '0' + hex;
            }
            return '#' + hex; //返回‘#'开头16进制颜色
        },

        // 根据传入的配置项设置图表配置内容
        getOptions(config){
            let options = {
                backgroundColor: 'transparent',
                legend: {
                    show:config.show!=undefined?config.show:false,
                    orient: 'horizontal', // 'vertical'
                    x: 'left', // 'center' | 'left' | {number},
                    y: 'top', // 'center' | 'bottom' | {number}
                    data: [],
                    textStyle: {
                        color: '#fff'
                    },
                    selectedMode: 'single'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params, ticket, callback) {
                        // console.log(params);
                        let loadType = ''
                        if(params.seriesName == '出镇海'){
                            loadType = '卸货量：'
                        } else if(params.seriesName == '进镇海'){
                            loadType = '装货量：'
                        }
                        if (params.seriesType == 'effectScatter') {
                            return params.data.name + loadType + params.data.value[2];
                        } else if (params.seriesType == 'lines') {
                            return params.data.fromName + '到' + params.data.toName + '，运输量：' + params.data.value;
                        } else {
                            return params.name;
                        }
                    }
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    top: '3%',
                    bottom: '3%',
                    containLabel: true
                },
                geo: {
                    map: 'china',
                    label: {
                        emphasis: {
                            show: true,
                            color: '#fff'
                        }
                    },
					roam: true,
                    zoom: 7,
                    center: [121.009792, 30.868388],
                    itemStyle: {
                        normal: {
                            areaColor: 'rgba(8, 25, 66, .5)',
                            borderColor: '#404a59'
                        },
                        emphasis: {
                            areaColor: 'rgba(37, 43, 61, .5)'
                        }
                    }
                },
                series: []
            }
            return options;
        },

        convertData(data) {
			let _this = this,
				res = [];
			data.forEach((item)=>{
                if(_this.geoCoordMap){
                    var fromCoord = _this.geoCoordMap[item[0]];
                    var toCoord = _this.geoCoordMap[item[1]];
                    if (fromCoord && toCoord) {
                        res.push({
                            fromName: item[0],
                            toName: item[1],
                            coords: [fromCoord, toCoord],
                            value: item[2]
                        });
                    }
                }
			})
            return res;
        },

        getMapCitys(datas){
			let _this = this;
            let citysSet = new Set([]);
            datas.forEach(item=>{
                //显示始点或终点
                if (_this.inOutType == 'out'){
                    citysSet.add(item[1]);//出宁波
                }else if (_this.inOutType == 'in') {
                    citysSet.add(item[0]);//进宁波
                }
			})
			
            let citysArray = Array.from(citysSet);
            let citys = [];
            citysArray.forEach(item=>{
                let res = datas.filter((it)=>{
                    if (_this.inOutType == 'out'){
                        return it[1]===item;//出宁波
                    }else if (_this.inOutType == 'in'){
                        return it[0]===item;//进宁波
                    }
                })
                
                if(res && res.length>0){
                    if(_this.geoCoordMap[item]){
                        citys.push({
                            name: item,
                            value: _this.geoCoordMap[item].concat([res[0][2]])
                        });
                    }
                }else{
                    if(_this.geoCoordMap[item]){
                        citys.push({
                            name: item,
                            value: _this.geoCoordMap[item]
                        });
                    }
                }
			})
			// console.log('citys:');
			// console.log(citys);
            return citys;
        },

        /**
         * 图表设置数据
         * seriesData：柱状图表数据---[value,value,value]
         * config:echart的options配置
         */
        setData(seriesData , config){
            let type = seriesData[0].name
            switch (type) {
                case "出镇海":
                    this.inOutType = "out";
                    break;
                case "进镇海":
                    this.inOutType = "in";
                    break;
            }
            let _this = this, options = null;
            config = config || {};
            // if(!this.options){
                this.options = this.getOptions(config);
            // }
            options = Object.assign({},this.options);

            /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
            options.series = [];
            /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/
            if(seriesData.length>=2){
                options.legend.show = true;
                options.legend.data = seriesData.map(it=>{return it.name});
            }else{
                options.legend.show = false;
            }
            seriesData.forEach(item=>{
                options.series.push({
                    name: item.name,
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    zlevel: 2,
                    rippleEffect: {
                        brushType: 'stroke'
                    },
                    label: {
                        normal: {
                            fontSize:16,
                            show: true,
                            position: 'right',
                            formatter: '{b}'
                        }
                    },
                    symbolSize: 10,
                    showEffectOn: 'render',
                    itemStyle: {
                        normal: {
                            color: '#46bee9'
                        }
                    },
                    data:_this.getMapCitys(item.data)
                });
                options.series.push({
                    name: item.name,
                    type: 'lines',
                    coordinateSystem: 'geo',
                    zlevel: 2,
                    large: true,
                    effect: {
                        show: true,
                        period: 6,
                        trailLength: 0,
                        symbol: _this.symbolPath,
                        symbolSize: 15,
                        color:'#F58158'
                    },
                    lineStyle: {
                        normal: {
                            color: "#46bee9",
                            width: 1,
                            opacity: 0.6,
                            curveness: 0.2
                        }
                    },
                    data: _this.convertData(item.data)
                })
            })
            

            if(this.mainChart){
                this.mainChart.setOption(options);
                this.seriesData = seriesData;
                this.options = options;
            }else {
                let _this = this;
                let mainChart = this.$echarts.init(this.$refs.echartsWape, 'dark');
                mainChart.setOption(options, true);
                this.mainChart = mainChart;
            }
        }
    }
};
</script>
<style scoped>
.echarts-container {
    width: 100%;
}
.chart-bordered {
    background: url('~@/assets/dashboard-img/echart-bg.png');
    border: 1px solid #144277;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    height: 100%;
    width: 100%;
}
.chart-bordered h3 {
    color: #fff;
    background-color: #0b1d54;
    border: 1px solid #2075a9;
    line-height: 1.8vw;
    /* height: 35px; */
    font-size: .8vw;
    margin: 10px;
    font-weight: 100;
    padding: 5px 10px;
    position: relative;
    text-align: center;
}

.border-top-left,
.border-top-right,
.border-bottom-left,
.border-bottom-right {
    position: absolute;
    width: 10px;
    height: 10px;
    line-height: 10px;
    display: block;
}

.border-top-left {
    left: 0;
    top: 0;
    border-left: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-top-right {
    right: 0;
    top: 0;
    border-right: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-bottom-left {
    left: 0;
    bottom: 0;
    border-left: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}

.border-bottom-right {
    right: 0;
    bottom: 0;
    border-right: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}
</style>
