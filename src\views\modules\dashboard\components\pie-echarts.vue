<template>
    <div class="chart-bordered" style="width:100%;height:100%;">
        <div class="container">
            <h3 v-if="title">{{title}}</h3>
            <div v-if="isShowLabel==false" class="echarts-container" ref="echartsWape" :style="{'height':echartsHeight}"></div>
            <el-row v-else>
                <el-col :sm="12">
                    <div class="echarts-container" ref="echartsWape" :style="{'height':echartsHeight}"></div>
                </el-col>
                <el-col :sm="12">
                    <el-row style="margin-top:60px;">
                        <template v-for="(item,index) in seriesData">
                            <el-col :span="12" class="list-exception" :key="index">
                                <em :style="{'background-color':colorList[index]}"></em>{{seriesData[index].name}}　　
                                <span id="spnoreg">{{seriesData[index].value}}</span>
                            </el-col>
                        </template>
                    </el-row>
                </el-col>
            </el-row>
        </div>
        <span class="border-top-left"></span>
        <span class="border-top-right"></span>
        <span class="border-bottom-left"></span>
        <span class="border-bottom-right"></span>
    </div>
</template>

<script>
import * as $http from '@/api/dashboard';
export default {
    name: 'pieEcharts',
    props: {
        title: {
            type: String
        },
        echartsHeight: {
            type: String,
            required: true
        },
        isShowLabel:{
            type:Boolean,
            default:false
        }
    },
    data() {
        return {
            mainChart: null,
            options:null,                // echarts的配置文件
            colorList:['#0487ed', '#3eb177', '#c1c049', '#c59838', '#cd6237', '#e11000',
                    '#aa00a0','#59057b','#ffa96a','#7da87b','#84f2d6','#53c7f0','#005585','#2931b3','#0666e8'],

            seriesData:[],
            windowResizeFun:null
        };
    },
    destroyed() {
        if (this.mainChart) {
			this.mainChart.dispose();
			this.mainChart = null;
		}
		if(this.windowResizeFun){
			window.removeEventListener("resize",this.windowResizeFun,false)
		}
    },
    mounted() {
        let _this = this;
        this.windowResizeFun = function(){
            if (_this.mainChart) {
                _this.mainChart.resize();
            }
        }
        window.addEventListener('resize', this.windowResizeFun);
    },
    methods: {
        // 随机生成十六进制颜色
        randomHexColor() {
            var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
            while (hex.length < 6) {
                //while循环判断hex位数，少于6位前面加0凑够6位
                hex = '0' + hex;
            }
            return '#' + hex; //返回‘#'开头16进制颜色
        },

        // 根据传入的配置项设置图表配置内容
        getOptions(config){
            return {
                backgroundColor: 'transparent',
                title: {
                    show: (config && config.titleShow) || false,
                    text: (config && config.title) || '',
                    x: 'center',
                    y: '60%',
                    textStyle: {
                        fontWeight: 'normal',
                        // color: "#0bb6f0",
                        color: "#fff",
                        fontSize:13
                    }
                },
                legend: {
                    show:false,
                    orient: 'horizontal',
                    bottom: '10',
                    itemHeight: '5',
                    itemWidth: 5,
                    textStyle: {
                        color: '#fff'
                    },
                    data:[]
                },
                tooltip: {
                    trigger: 'item',
                    formatter: config.tooltipFormatter || '{b}<br/>数量：{c}（{d}%）'
                },
                series: []
            };
        },

        // 图表设置数据
        setData(seriesData,config,clickFun){
            let _this = this, options = null;
            config = config || {};
            if(!this.options){
                this.options = this.getOptions(config);
            }
            options = Object.assign({},this.options);

            /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
            options.series = [];
            /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/
            if(config.title){
                options.title.text = config.title;
            }
            if(config.legendData && config.legendData.length>0){
                options.legend.show = true;
                options.legend.data = config.legendData;
            }
            options.series.push({
                name: 'pie',
                type: 'pie',
                radius: config.radius || ['35%', '60%'],
                center: config.center||['50%', '50%'],
                label: {
                    normal: {
                        show: true,
                        position: 'outside',
                        textStyle: {
                            color: '#fff'
                        },
                        formatter: config.labelFormatter || "{b}: {c}"
                    },
                    emphasis: {
                        show: true
                    }
                },
                labelLine: {
                    normal: {
                        show: true,
                        lineStyle: {
                            color: '#fff'
                        }
                    }
                },
                itemStyle: {
                    normal: {
                        color: function(params) {
                            if(params.dataIndex>=_this.colorList.length){
                                _this.colorList.push(_this.randomHexColor());
                            }
                            return _this.colorList[params.dataIndex];
                        }
                    }
                },
                data: seriesData
            })

            if(this.mainChart){
                this.mainChart.setOption(options);
                this.seriesData = seriesData;
                this.options = options;
            }else {
                let _this = this;
                let mainChart = this.$echarts.init(this.$refs.echartsWape, 'dark');
                mainChart.setOption(options, true);
                this.mainChart = mainChart;
            }
            if(clickFun && this.mainChart){
                this.mainChart.on("click", clickFun);
            }
        }
    }
};
</script>
<style scoped>
.echarts-container {
    width: 100%;
}
.chart-bordered {
    background: url('~@/assets/dashboard-img/echart-bg.png');
    border: 1px solid #144277;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-bottom: 20px;
    height: 100%;
    width: 100%;
}
.chart-bordered h3 {
    color: #fff;
    background-color: #0b1d54;
    border: 1px solid #2075a9;
    line-height: 1.8vw;
    /* height: 35px; */
    font-size: .8vw;
    margin: 10px;
    font-weight: 100;
    padding: 5px 10px;
    position: relative;
    text-align: center;
}
.border-top-left,
.border-top-right,
.border-bottom-left,
.border-bottom-right {
    position: absolute;
    width: 10px;
    height: 10px;
    line-height: 10px;
    display: block;
}

.border-top-left {
    left: 0;
    top: 0;
    border-left: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-top-right {
    right: 0;
    top: 0;
    border-right: solid 2px #0e68b1;
    border-top: solid 2px #0e68b1;
}

.border-bottom-left {
    left: 0;
    bottom: 0;
    border-left: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}

.border-bottom-right {
    right: 0;
    bottom: 0;
    border-right: solid 2px #0e68b1;
    border-bottom: solid 2px #0e68b1;
}

.list-exception {
    color: #fff;
    padding: 5px 10px 5px 40px;
    font-size: .8em;
}
.list-exception em {
    display: inline-block;
    margin-right: 8px;
    width: 6px;
    height: 6px;
    background-color: #fff;
    vertical-align: middle;
    border-radius: 100%;
}
.list-exception{
    margin-bottom: 14px;
}
.list-exception > span{
    font-size: 18px;
}
</style>
