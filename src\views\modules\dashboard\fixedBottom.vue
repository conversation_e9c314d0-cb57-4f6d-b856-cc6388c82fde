<template>
    <div class="bottom-module">
        <div class="bottom-head" @click="toggleCollapse">
            <span>企业装卸情况&nbsp;</span>
            <i class="el-icon-arrow-down" v-show="!isCollapse"></i>
            <i class="el-icon-arrow-up" v-show="isCollapse"></i>
        </div>
        <collapse-transition>
            <div class="bottom-content" v-show="!isCollapse" v-loading="loading"
                element-loading-background="rgba(8,26,65, 0.2)" element-loading-text="正在加载中，请稍等...">
                <div class="bottom-content-header">
                    <ul class="ul-list">
                        <li class="diseasePoor" :class="{ active: showType == 'diseasePoor' }"
                            @click="selectType($event, 'diseasePoor')">企业装货情况</li>
                        <li class="fivePeopleGroupNum" :class="{ active: showType == 'fivePeopleGroupNum' }"
                            @click="selectType($event, 'fivePeopleGroupNum')">企业卸货情况</li>
                        <li class="passByVecPic" :class="{ active: showType == 'passByVecPic' }"
                            @click="selectType($event, 'passByVecPic')">车辆过登记点</li>
                    </ul>
                </div>
                <div class="bottom-content-body">
                    <div v-show="showType == 'diseasePoor'">
                        <!-- 企业装货情况 -->
                        <bar-echarts :echartsHeight="'12.3vw'" ref="entpLoadGoodsChart"></bar-echarts>
                    </div>
                    <div v-show="showType == 'fivePeopleGroupNum'">
                        <!-- 企业卸货情况 -->
                        <bar-echarts :echartsHeight="'12.3vw'" ref="entpUnloadGoodsChart"></bar-echarts>
                    </div>
                    <div v-show="showType == 'passByVecPic'">
                        <!-- 车辆过登记点图片 -->
                        <!-- <el-carousel :interval="4000" type="card" height="12.3vw">
                        <el-carousel-item v-for="item in passByVecPicList" :key="item">
                            <h3>{{ item }}</h3>
                        </el-carousel-item>
                    </el-carousel> -->
                        <slider :dataSource="passByVecPicList" ref="vecPicSlider">
                            <template slot-scope="{list}">
                                <div v-for="(item, index) in list" :key="index"
                                    style="position:relative;float:left;padding:0 1vw;font-size:12px;text-align:center">
                                    <img style="width:10vw;height:8vw;border:1px solid #465b77;" :src="item[2]">
                                    <div style="margin-top:8px;">{{ item[0] }}({{ item[1] | FormatDate('HH:mm') }})</div>
                                    <div style="margin-top:8px;">{{ item[3] }}</div>
                                </div>
                            </template>
                        </slider>
                    </div>
                </div>
            </div>
        </collapse-transition>
    </div>
</template>

<script>
import * as $http from '@/api/dashboard';
import * as Tool from '@/utils/tool'
import collapseTransition from '@/components/CollapseTransition'
import slider from '@/components/Slider'
import barEcharts from './components/bar-echarts'; // 柱状图
export default {
    name: 'fixedBottom',
    components: {
        collapseTransition,
        slider,
        barEcharts
    },
    data() {
        return {
            intervalTimers: [],
            isCollapse: true,                    // 是否折叠，默认是折叠状态

            showType: 'diseasePoor',             // 默认显示企业装货情况
            loading: false,

            passByVecPicList: []                 // 车辆过登记点图片
        };
    },
    mounted() {
        let _this = this;
    },
    destroyed() {
        this.intervalTimers.forEach(item => {
            clearTimeout(item);
        })
    },
    methods: {
        toggleCollapse() {
            let _this = this;
            this.isCollapse = !this.isCollapse;
            if (!this.isCollapse) {       // 若是展开状态
                this.initChart();

                // 每五分钟刷新一次
                let interval = setInterval(function () {
                    _this.initChart(true);
                }, 300000);
                this.intervalTimers.push(interval);
            } else {
                this.intervalTimers.forEach(item => {
                    clearTimeout(item);
                })
            }
        },

        shortCorp(entpName) {
            entpName = entpName.replace("宁波市", "");
            entpName = entpName.replace("宁波", "");
            entpName = entpName.replace("浙江", "");
            entpName = entpName.replace("浙江省", "");
            entpName = entpName.replace("发展", "");
            entpName = entpName.replace("（）", "");
            entpName = entpName.replace("化工", "");
            entpName = entpName.replace("化学", "");
            entpName = entpName.replace("工业", "");
            entpName = entpName.replace("码头", "");
            entpName = entpName.replace("储运", "");
            entpName = entpName.replace("中国", "");
            entpName = entpName.replace("石油", "");
            entpName = entpName.replace("镇海", "");
            entpName = entpName.replace("中外合资", "");
            entpName = entpName.replace("石化", "");
            entpName = entpName.replace("科技", "");
            entpName = entpName.replace("城市", "");
            entpName = entpName.replace("渣土", "");
            entpName = entpName.replace("渣运", "");
            entpName = entpName.replace("运输", "");
            entpName = entpName.replace("物流", "");
            entpName = entpName.replace("施工", "");
            entpName = entpName.replace("乙烯胺", "");
            entpName = entpName.replace("液化", "");
            entpName = entpName.replace("液体", "");
            entpName = entpName.replace("仓储", "");
            entpName = entpName.replace("制造", "");
            entpName = entpName.replace("诺贝尔", "");
            entpName = entpName.replace("服务有限公司", "");
            entpName = entpName.replace("技术有限公司", "");
            entpName = entpName.replace("股份有限公司", "");
            entpName = entpName.replace("有限责任公司", "");
            entpName = entpName.replace("责任有限公司", "");
            entpName = entpName.replace("有限公司", "");
            entpName = entpName.replace("责任公司", "");
            entpName = entpName.replace("分公司", "");
            entpName = entpName.replace("公司", "");
            return entpName;
        },

        selectType($event, type) {
            console.log($event);
            this.showType = type;
            this.initChart();
        },

        // 初始化图表
        initChart(refresh) {
            let _this = this,
                mainChart = null;
            switch (this.showType) {
                case 'diseasePoor':             // 企业装货情况
                    mainChart = this.$refs.entpLoadGoodsChart.$data.mainChart;
                    this.$nextTick(() => {
                        if (!mainChart || refresh) {
                            this.initEntpLoadGoodsChart();
                        } else {
                            mainChart.resize();
                        }
                    })
                    break;
                case 'fivePeopleGroupNum':      // 企业卸货情况
                    mainChart = this.$refs.entpUnloadGoodsChart.$data.mainChart;
                    this.$nextTick(() => {
                        if (!mainChart || refresh) {
                            this.initEntpUnloadGoodsChart();
                        } else {
                            mainChart.resize();
                        }
                    })
                    break;
                case 'passByVecPic':            // 车辆过登记点
                    _this.initPassByVecPic(refresh);
                    break;
                default:
                    break;
            }
        },

        // 企业装货情况
        initEntpLoadGoodsChart() {
            let _this = this;
            this.loading = true;
            $http
                .getEntpLoadGoodsCnt()
                .then(res => {
                    // TODO 待删
                    // res = [["宁波中金石化有限公司",6820.040000],["镇海石化海达发展有限责任公司",4207.859000],["镇海港埠公司化工队",2239.500000],["宁波镇洋化工发展有限公司",2155.980000],["中国石油化工股份有限公司镇海炼化分公司",512.720000],["宁波宁翔新翔液化储运码头有限公司",448.500000],["宁波市镇海宁远化工仓储有限公司",355.500000],["宁波孚宝仓储有限公司",341.500000],["宁波四明化工有限公司",309.120000],["阿克苏诺贝尔乙烯胺（宁波）有限公司",294.300000],["浙江恒河石油化工股份有限公司",282.520000],["宁波昊德化学工业股份有限公司",282.510000],["宁波浙铁江宁化工有限公司",238.720000],["宁波富德能源有限公司",232.990000],["镇海石化工业贸易有限责任公司",229.200000],["中外合资宁波辰菱液体化工仓储有限公司",210.000000],["宁波九龙气体制造有限公司",123.070000],["宁波市镇海泰达化工有限公司",117.170000],["宁波中燃船舶燃料有限公司",66.000000],["宁波华宁化工储运有限公司",60.000000],["宁波金海晨光化学股份有限公司",29.060000],["宁波越洋化工码头储运有限公司",18.000000]]

                    _this.loading = false;
                    let xAxisArr = [], seriesData = [];
                    res.forEach(item => {
                        if (item[1] > 0) {
                            xAxisArr.push(_this.shortCorp(item[0]));
                            seriesData.push(parseFloat(item[1]).toFixed(2));
                        }
                    });
                    let config = {
                        grid: {
                            left: '2%',
                            right: '2%',
                            top: '15%',
                            bottom: '5%',
                            containLabel: true
                        },
                        // interval:0
                    };
                    this.$refs.entpLoadGoodsChart.setData(seriesData, xAxisArr, config)
                })
                .catch(error => {
                    _this.loading = false;
                    console.log(error);
                });
        },

        // 企业卸货情况
        initEntpUnloadGoodsChart() {
            let _this = this;
            this.loading = true;
            $http
                .getEntpUnloadGoodsCnt()
                .then(res => {
                    // res = [["浙江恒河石油化工股份有限公司",409.540000],["阿克苏诺贝尔乙烯胺（宁波）有限公司",375.080000],["宁波浙铁江宁化工有限公司",214.460000],["宁波大安化学工业有限公司",204.680000],["镇海石化海达发展有限责任公司",152.680000],["宁波甬华树脂有限公司",137.280000],["中外合资宁波辰菱液体化工仓储有限公司",120.000000],["宁波金海晨光化学股份有限公司",85.920000],["宁波LG甬兴化工有限公司",53.000000],["宁波爱思开合成橡胶有限公司",39.305000],["宁波昊德化学工业股份有限公司",29.580000],["宁波争光树脂有限公司",29.560000],["宁波金辉化工实业有限公司",29.540000],["宁波镇洋化工发展有限公司",26.280000],["宁波九龙气体制造有限公司",18.900000]]

                    _this.loading = false;
                    let xAxisArr = [], seriesData = [];
                    res.forEach(item => {
                        if (item[1] > 0) {
                            xAxisArr.push(_this.shortCorp(item[0]));
                            seriesData.push(parseFloat(item[1]).toFixed(2));
                        }
                    });
                    let config = {
                        grid: {
                            left: '2%',
                            right: '2%',
                            top: '15%',
                            bottom: '5%',
                            containLabel: true
                        },
                        // interval:0
                    };
                    this.$refs.entpUnloadGoodsChart.setData(seriesData, xAxisArr, config)
                })
                .catch(error => {
                    _this.loading = false;
                    console.log(error);
                });
        },

        // 车辆过登记点
        initPassByVecPic() {
            let _this = this;
            this.loading = true;
            $http
                .getPassByVecPic()
                .then(res => {
                    _this.loading = false;
                    _this.passByVecPicList = res;
                    _this.$refs.vecPicSlider.init();
                })
                .catch(error => {
                    _this.loading = false;
                    console.log(error);
                });
        }

    }
};
</script>
<style scoped>
.bottom-module {
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 101;
    /*background: #999;*/
}

.bottom-head {
    height: 60px;
    width: 503px;
    background: url('~static/img/dashboard/bottom-header-bg.png') no-repeat center bottom;
    background-size: 100% 100%;
    margin: 0 auto;
    text-align: center;
    vertical-align: middle;
    color: #fff;
    cursor: pointer;
    font-size: 20px;
    line-height: 75px;
    font-weight: bold;
    color: #fff;
    letter-spacing: 5px;
}

.bottom-content {
    display: block;
    /* height: 15.9vw; */
    background: #0e1a33;
    border-top: 1px solid #526b96;
    color: #fff;
}

.bottom-content-header {
    /* height: 3.5vw; */
    background: #20407b;
    display: table;
    width: 100%;
}

.bottom-content-header ul {
    margin: 0 auto;
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}

.bottom-content-header ul li {
    display: inline-block;
    font-size: 1vw;
    line-height: 1.8vw;
    border-radius: 10px;
    box-sizing: border-box;
    cursor: pointer;
    padding: 8px 10px;
    margin: 5px;
}

.bottom-content-header ul li:hover {
    background: #062856;
}

.bottom-content-header .active {
    background: #062856;
    border-bottom: 1px solid #ccc;
}

.bottom-content-body {
    position: relative;
    height: 12.3vw;
    background: transparent;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}
</style>
