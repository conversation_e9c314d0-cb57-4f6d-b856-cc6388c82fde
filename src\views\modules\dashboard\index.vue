<template>
  <div class="dashboard-container">
    <div class="left-module">
      <card title="当前区内">
        <vecStatusChart ref="vecStatusChart"></vecStatusChart>
      </card>
      <card title="今日预警">
        <violationChart style="height:290px;" ref="violationChart"></violationChart>
      </card>
      <card title="车辆进出">
        <vecEntryAndExitChart style="height:290px;" ref="vecEntryAndExitChart"></vecEntryAndExitChart>
      </card>
      <card title="基础信息">
        <!-- 系统登记信息 -->
        <baseInfo :title="'系统登记信息'" :dataSource="baseInfoData"></baseInfo>
      </card>
    </div>
    <div class="right-module">
      <card title="今日货量">
        <div class="qty-chart">
          <div>
            <div class="qty-content" @click="qtyChartClickFun({ name: '装货量' })">
              <div class="value">{{ loadQty }} 吨</div>
              <div class="label">装货量</div>
            </div>
          </div>
          <div>
            <div class="qty-content" @click="qtyChartClickFun({ name: '卸货量' })">
              <div class="value">{{ unloadQty }} 吨</div>
              <div class="label">卸货量</div>
            </div>
          </div>
          <div>
            <div class="qty-content" @click="qtyChartClickFun({ name: '装货车次' })">
              <div class="value">{{ loadCnt }} 车</div>
              <div class="label">装货车次</div>
            </div>
          </div>
          <div>
            <div class="qty-content" @click="qtyChartClickFun({ name: '卸货车次' })">
              <div class="value">{{ unloadCnt }} 车</div>
              <div class="label">卸货车次</div>
            </div>
          </div>
        </div>
      </card>
      <card title="今日卡口">
        <div class="qty-header" @click="qtyChartClickFun({ name: '登记车次' })" style="cursor: pointer;">
          <div class="label">登记车次</div>
          <!-- <div v-for="it in (passDjdCnt + '车').split('')" :key="it">
            <span class="num">{{ it }}</span>
          </div> -->
          <div v-for="(it, index) in passDjdCntComputed" :key="index">
            <span class="num">{{ it }}</span>
          </div>
        </div>
        <regPotStatChart style="height:290px;" ref="regPotStatChart"></regPotStatChart>
      </card>
      <card title="今日装卸">
        <template slot="oper">
          <div class="load-unload-tag" :class="{ active: isActiveOfLoadGoodsTypeChart }"
            @click="selectGoodsType('load')" title="点击查看装货情况">装货</div>
          <!-- 卸货 -->
          <div class="load-unload-tag" :class="{ active: !isActiveOfLoadGoodsTypeChart }"
            @click="selectGoodsType('unload')" title="点击查看卸货情况">卸货</div>
        </template>
        <!-- 装货 -->
        <loadGoodsTypeChart style="height:290px;" ref="loadGoodsTypeChart"
          :class="{ hidden: !isActiveOfLoadGoodsTypeChart }">
        </loadGoodsTypeChart>
        <unloadGoodsTypeChart style="height:290px;" ref="unloadGoodsTypeChart"
          :class="{ hidden: isActiveOfLoadGoodsTypeChart }"></unloadGoodsTypeChart>
      </card>
      <card title="今日货物">
        <goodsTypeChart style="height:290px;" ref="goodsTypeChart"></goodsTypeChart>
      </card>
      <card title="车辆登记">
        <regPotChart style="height:290px;" ref="regPotChart"></regPotChart>
      </card>
    </div>
    <div class="center-top">
      <div>
        零时区内：
        <span class="number">{{ truckCnt.zeroCarCnt }}</span>
      </div>
      <div @click="showComeInOutVecDetail('进镇海')">
        今日驶入：
        <span class="number">{{ truckCnt.comeInZhCnt }}</span>
      </div>
      <div @click="showComeInOutVecDetail('出镇海')">
        今日驶出：
        <span class="number">{{ truckCnt.comeOutZhCnt }}</span>
      </div>
      <div>
        <router-link :to="{ path: '/monit/index.html' }" class="routerLink" tag="a" target="_blank">
          <span>
            当前区内：
            <span class="number">{{ truckCnt.innerZhCnt }}</span>
          </span>
        </router-link>
      </div>
    </div>
    <div class="center-module">
      <template v-if="!isShowGoodsMapFlow">
        <div class="reback-btns">
          <div class="reback out" @click="showGoodsMapFlow('in')">
            货物流入
          </div>
          <div class="reback in" @click="showGoodsMapFlow('out')">
            货物流出
          </div>
        </div>
        <div class="web-map"></div>
        <!-- 各道路卡口进出危险品车辆（6个） -->
        <div class="major-road-wape" :class="{ closed: isCloseMajor }" v-show="majorRoadData.length > 0">
          <div class="major-road-header" @click="isCloseMajor = !isCloseMajor">车辆出入信息<div class="header-arrow"></div>
          </div>
          <div class="major-road-body">
            <div class="major-road-item" v-for="(item, index) in majorRoadData" :key="index">
              {{ item.key }}：
              <span>{{ item.value }}</span> 辆
            </div>
          </div>
        </div>
      </template>
      <!-- 对上面这个模块的数据展示 -->
      <template v-else>
        <!-- 镇海区近当日货物流向统计图 -->
        <map-flow-echarts :echartsHeight="maxHeight - 110 + 'px'" ref="goodsMapFlow"></map-flow-echarts>
        <div class="reback-btns">
          <div class="reback return" @click="isShowGoodsMapFlow = false">返回</div>
        </div>
      </template>
    </div>
    <fixed-bottom></fixed-bottom>
    <el-dialog :title="comDgHasPaginationTitle" :visible.sync="comDgHasPaginationVisible" width="80%" append-to-body
      destroy-on-close>
      <simple-table v-loading="comTbHasPaginationLoading" :tableTitle="''" :tableHeader="comTbHasPaginationHeader"
        :tablePage="comTbHasPaginationPage" @tableRefreshByPagination="tableRefreshByPaginationHandle"></simple-table>
    </el-dialog>
    <el-dialog :title="comDgTitle" :visible.sync="comDgVisible" width="80%" append-to-body destroy-on-close>
      <el-table class="el-table" highlight-current-row style="width: 100%" border element-loading-text="正在加载中，请稍等..."
        :data="comTbList" v-loading="comTbLoading" :max-height="maxHeight - 100">
        <el-table-column type="index" width="50"></el-table-column>
        <template v-for="(item, index) in comTbHeader">
          <el-table-column :label="item.name" :key="index" :width="item.width" :formatter="item.formatter">
            <template slot-scope="scope">
              <template v-if="item.formatter">
                <span v-html="item.formatter(scope.row[item.field], scope.row, index)
                  "></span>
              </template>
              <template v-else>
                <span v-html="scope.row[item.field]"></span>
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div slot="footer" style="overflow: hidden;">
        <el-pagination background layout="sizes, prev, next" :page-sizes="[10, 20, 30, 50]" style="float: right"
          :page-size="pagination.limit" @current-change="handleCurrentChange" @size-change="handleSizeChange">
        </el-pagination>
      </div>
    </el-dialog>
    <el-dialog :title="travelVec + ' 节点详情'" :visible.sync="travelVisible" width="30%" append-to-body destroy-on-close>
      <div class="block">
        <el-timeline v-loading="travelLoading">
          <el-timeline-item v-for="(activity, index) in travels" :key="index" placement="top"
            :timestamp="activity.timestamp">
            <div>{{ activity.content }}</div>
            <img v-if="activity.img" :src="activity.img" style="max-width: 30%" /><img />
          </el-timeline-item>
          <el-timeline-item>
            <el-button round @click="histroyTra">查询轨迹</el-button>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
    <el-dialog title="重要提示" :visible.sync="homeTipsVisible" width="32%" :show-close="false"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <div style="padding: 20px 10px;font-size: 17px;">
        <div>你有{{ unfinishCount }}条待办事项未完成，请通过备忘录查看</div>
        <el-button type="primary" @click="closeHomeTips" style="margin-top: 20px;">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/dashboard";
import * as $httpM from "@/api/menorandum";
import card from "./components/card"; // 卡片
import fixedBottom from "./fixedBottom"; // 饼图
import pieEcharts from "./components/pie-echarts"; // 饼图
import pieEchartsInside from "./components/pie-echarts-inside"; // 饼图
import barEcharts from "./components/bar-echarts"; // 柱状图
import lineEcharts from "./components/line-echarts"; // 折线图
import mutiBarEcharts from "./components/muti-bar-echarts"; // 柱状图
import mapFlowEcharts from "./components/map-flow-echarts"; // 地图流向图
import baseInfo from "./components/base-info"; // 地图流向图
// import zhenhaiSvg from "./zhenhai-svg"; // 地图流向图
import SimpleTable from "@/components/SimpleTable";

import vecStatusChart from "./vecStatusChart"
import violationChart from "./violationChart"
import vecEntryAndExitChart from "./vecEntryAndExitChart"
import regPotStatChart from "./regPotStatChart"
import loadGoodsTypeChart from "./loadGoodsTypeChart"
import unloadGoodsTypeChart from "./unloadGoodsTypeChart"
import goodsTypeChart from "./goodsTypeChart"
import regPotChart from "./regPotChart"
import mapWebpSrc from "static/img/dashboard/map.webp"


export default {
  components: {
    card,
    vecStatusChart,
    violationChart,
    vecEntryAndExitChart,
    regPotStatChart,
    loadGoodsTypeChart,
    unloadGoodsTypeChart,
    goodsTypeChart,
    regPotChart,

    // zhenhaiSvg,
    fixedBottom,
    pieEcharts,
    pieEchartsInside,
    barEcharts,
    lineEcharts,
    mutiBarEcharts,
    mapFlowEcharts,
    SimpleTable,
    baseInfo
  },
  data() {
    return {
      mapWebpSrc: mapWebpSrc,
      todayTime: new Date(), // 当前时间
      maxHeight: Tool.getClientHeight() - 50,
      intervalTimers: [], // 定时器

      isShowGoodsMapFlow: false, // 是否显示货物流向图，默认不显示，显示地图的svg
      isActiveOfLoadGoodsTypeChart: true,

      truckCnt: {
        comeInZhCnt: 0, // 进镇海车辆数
        comeOutZhCnt: 0, // 出镇海车辆数
        innerZhCnt: 0, // 区内车辆数
        zeroCarCnt: 0 // 零时区内车辆数
      },

      goodsTypeOfTop5Loading: false, // 今日区内装卸货Top5
      majorRoadData: [], // 各道路卡口进出危险品车辆（6个）车辆情况
      isCloseMajor: false, // 是否关闭显示车辆情况

      // 公共dialog
      comDgTitle: "",
      comDgVisible: false,
      comTbLoading: false,
      comTbHeader: [],
      comTbList: [],
      travelVisible: false,
      travelLoading: false,
      travelVec: null,
      travelTime: null,
      travels: [],
      comDgHasPaginationTitle: "",
      comDgHasPaginationVisible: false,
      comTbHasPaginationLoading: false,
      comTbHasPaginationType: null,
      comTbHasPaginationHeader: [],
      comTbHasPaginationStaticPostData: {},
      comTbHasPaginationPage: {
        list: [],
        currPage: 0,
        pageSize: 10,
        totalPage: 0
      },
      baseInfoData: {
        entpApprCnt: 0,
        entpCnt: 0,
        vecApprCnt: 0,
        tankCnt: 0,
        persApprCnt: 0,
        persCnt: 0,
        vecCnt: 0
      },
      homeTipsVisible: false, // 首页重要提示弹窗是否可见
      unfinishCount: 0,

      loadQty: 0,  // 装货量
      unloadQty: 0,  // 卸货量

      loadCnt: 0,  //装货车次
      unloadCnt: 0,  //卸货车次
      passDjdCnt: 0, // 登记车次

      pagination: {
        page: 1,
        limit: 10
      }
    };
  },
  computed: {
    passDjdCntComputed() {
      let len = 6;  // 设置默认长度
      let v = (this.passDjdCnt + '车').split('');
      if (v.length < len) {
        let count = len - v.length;
        for (let i = 0; i < count; i++) {
          v.unshift('0');
        }
      }
      return v;
    }
  },
  mounted() {
    let _this = this;
    window.addEventListener("resize", function () {
      _this.maxHeight = Tool.getClientHeight() - 50;
    });
    this.refreshTime(); // 看板时间展示
    this.getMenorandum(); //获取备忘录
    // 进镇海，出镇海，区内车数量
    this.getComeInOutVecCnt();
    // 主要道路车辆数据
    this.initMajorRoadData();

    // 今日车辆情况
    this.initVecStatusChart();
    // 今日违章情况
    this.initViolationChart();
    // 今日车辆进出镇海情况
    this.initVecEntryAndExitChart();
    // 今日货量情况
    this.initQtyChart();
    // 今日登记点登记情况
    this.initRegPotStatChart();
    // 今日区内装卸货Top5
    this.initQtyTop5Chart();
    // 今日货物类别情况
    this.initGoodsTypeChart();
    // 今日车辆登记情况
    this.initRegPotChart();
    //系统登记信息
    this.getPlatformBaseInfo();

    // 每五分钟刷新一次 300000毫秒
    let interval = setInterval(function () {
      // 进镇海，出镇海，区内车数量
      _this.getComeInOutVecCnt();
      // 主要道路车辆数据
      _this.initMajorRoadData(true);
      // 今日车辆情况
      _this.initVecStatusChart();
      // 今日违章情况
      _this.initViolationChart();
      // 今日车辆进出镇海情况
      _this.initVecEntryAndExitChart();
      // 今日货量情况
      _this.initQtyChart();
      // 今日登记点登记情况
      _this.initRegPotStatChart();
      // 今日区内装卸货Top5
      _this.initQtyTop5Chart(true);
      // 今日货物类别情况
      _this.initGoodsTypeChart();
      // 今日车辆登记情况
      _this.initRegPotChart();
    }, 300000);
    this.intervalTimers.push(interval);
  },
  destroyed() {
    this.intervalTimers.forEach(item => {
      clearTimeout(item);
    });
  },
  methods: {
    closeHomeTips() {
      this.homeTipsVisible = false;
    },
    getMenorandum() {
      $httpM
        .judgeIsDljc()
        .then(res => {
          if (res.code == 0 && res.data) {
            $httpM
              .getUnFinishCount()
              .then(res => {
                if (res.code == 0 && res.data > 0) {
                  this.homeTipsVisible = true;
                  this.unfinishCount = res.data;
                }
              })
              .catch(error => {
                console.log(error);
              });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //刷新当前时间
    refreshTime: function () {
      let _this = this;
      setInterval(function () {
        _this.todayTime = new Date();
      }, 1000);
    },
    // 看板中进出镇海车辆
    getComeInOutVecCnt() {
      $http
        .getComeInOutVecCnt()
        .then(res => {
          let innerZhCnt = res.innerZhCnt || 0;
          let leaveCarCnt = res.leaveCarCnt || 0;
          let zeroCarCnt = res.zeroCarCnt || 0;
          let enterCarCnt = res.enterCarCnt || 0;
          // this.$set(this.truckCnt, "comeInZhCnt", res.enterCarCnt || 0);
          this.$set(this.truckCnt, "comeOutZhCnt", leaveCarCnt);
          this.$set(this.truckCnt, "zeroCarCnt", zeroCarCnt);
          this.$set(this.truckCnt, "innerZhCnt", innerZhCnt);
          this.$set(this.truckCnt, "comeInZhCnt", enterCarCnt);
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 进出镇海车次详情
    showComeInOutVecDetail(type) {
      let _this = this;
      let tagNm = null,
        postData = {},
        actionName = null;
      switch (type) {
        case "进镇海":
          tagNm = "进镇海";
          this.comTbHasPaginationType = "进镇海";
          break;
        case "出镇海":
          tagNm = "出镇海";
          this.comTbHasPaginationType = "出镇海";
          break;
        case "区内车":
          tagNm = "区内车";
          this.comTbHasPaginationType = "区内车";
          break;
      }
      this.comDgHasPaginationTitle = tagNm + "车次详情";
      this.comTbHasPaginationHeader = [];
      if (type == "进镇海" || type == "出镇海") {
        this.comDgHasPaginationVisible = true;
        this.comTbHasPaginationHeader = [
          { name: "车牌号", field: "vehicle_no" },
          { name: "所属企业", field: "CARRIER_NM" },
          { name: "挂车", field: "TRAI_CD" },
          { name: "驾驶员", field: "DV_NM" },
          { name: "押运员", field: "SC_NM" },
          { name: "货物", field: "GOODS_NM" },
          { name: "重量(吨)", field: "LOAD_QTY" },
          { name: "发货地", field: "CSNOR_WHSE_DIST" },
          { name: "收货地", field: "CSNEE_WHSE_DIST" }
        ];
        if (type == "进镇海") {
          this.comTbHasPaginationHeader.splice(2, 0, {
            name: "进入时间",
            field: "enter_date"
          });
        } else if (type == "出镇海") {
          this.comTbHasPaginationHeader.splice(2, 0, {
            name: "离开时间",
            field: "leave_date"
          });

          this.comTbHasPaginationHeader.push({
            name: "操作",
            operations: [
              {
                func: function (row) {
                  // console.log(row);
                  // row.leave_date: "2021-03-16 00:01:02"
                  // row.vehicle_no: "浙J83951"
                  // _this.getTravelByLeave(row.vehicle_no, row.leave_date);
                  let location = window.location;
                  window.open(
                    location.origin +
                    location.pathname +
                    "#/monit/hisTrack?v=" +
                    encodeURIComponent(row.vehicle_no) +
                    "&t=" +
                    Tool.formatDate(row.leave_date, "yyyy-MM-dd"),
                    "_blank"
                  );
                },
                label: "查看详情"
              }
            ]
          });
        }
        this.getTbList({
          page: 1,
          limit: 20
        });
      }
    },

    // getTravelByLeave(vecNo, leaveTime) {
    //   this.travelVisible = true;
    //   this.travelLoading = true;
    //   this.travelVec = vecNo;
    //   this.travelTime = leaveTime;
    //   this.travels = [];
    //   $http
    //     .getTravel(vecNo, leaveTime)
    //     .then((res) => {
    //       for (let i = 0; i < res.data.length; i++) {
    //         let description = "";
    //         if (res.data[i].type == "enter") {
    //           description = "进入";
    //         }
    //         if (res.data[i].type == "leave") {
    //           description = "离开";
    //         }
    //         this.travels.push({
    //           img: res.data[i].img,
    //           content: description + res.data[i].name,
    //           timestamp: res.data[i].time,
    //         });
    //       }
    //       this.travelLoading = false;
    //       // console.log(res.data);
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    // },
    histroyTra() {
      let location = window.location;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack?v=" +
        encodeURIComponent(this.travelVec) +
        "&t=" +
        Tool.formatDate(this.travelTime, "yyyy-MM-dd"),
        "_blank"
      );
    },

    getTbList(params) {
      let _this = this;
      let tagNm = null,
        postData = {},
        actionName = null;
      let type = this.comTbHasPaginationType;
      switch (type) {
        case "进镇海":
          tagNm = "进镇海";
          postData.type = 1;
          actionName = "getComeInOutVecDetail";
          this.comTbHasPaginationType = "进镇海";
          break;
        case "出镇海":
          tagNm = "出镇海";
          postData.type = 0;
          actionName = "getComeInOutVecDetail";
          this.comTbHasPaginationType = "出镇海";
          break;
        case "区内车":
          tagNm = "区内车";
          actionName = "getVecDetail";
          this.comTbHasPaginationType = "区内车";
          break;
        case "今日车辆情况":
          tagNm = "今日车辆情况";
          actionName = "getVecDetailByType";
          break;
      }
      if (type === "今日车辆情况") {
        postData = Object.assign(
          {},
          params,
          _this.comTbHasPaginationStaticPostData
        );
      } else {
        postData = Object.assign({}, params, postData);
      }
      this.comTbHasPaginationLoading = true;
      $http[actionName](postData)
        .then(res => {
          _this.comTbHasPaginationLoading = false;
          _this.$set(_this, "comTbHasPaginationPage", res.page);
        })
        .catch(error => {
          _this.comTbHasPaginationLoading = false;
          _this.$set(_this, "comTbHasPaginationPage", {
            list: [],
            currPage: 0,
            pageSize: 10,
            totalPage: 0
          });
          console.log(error);
        });
    },

    // table翻页事件
    tableRefreshByPaginationHandle(paginationData) {
      console.log(this.comTbHasPaginationType);
      let params = {
        page: 1,
        limit: 20,
        type: this.comTbHasPaginationType == "进镇海" ? 1 : 0
      };
      this.getTbList(Object.assign({}, params, paginationData));
    },

    // svg中主要道路数据
    initMajorRoadData() {
      let _this = this;
      $http
        .getMajorRoadCnt()
        .then(res => {
          if (res && res.code === 0) {
            res.data.forEach((item, index) => {
              if (item.key === "金塘大桥(舟山)") {
                res.data.splice(index, 1);
              }
            });
            _this.$set(_this, "majorRoadData", res.data);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 显示货物流向详情
    showGoodsMapFlow(type) {
      let mainChart = null;
      this.isShowGoodsMapFlow = true;

      if (this.$refs.goodsMapFlow) {
        mainChart = this.$refs.goodsMapFlow.$data.chart;
      }
      this.$nextTick(() => {
        if (!mainChart) {
          this.initGoodsMapFlowChart(type);
        } else {
          mainChart.resize();
        }
      });
    },

    // 初始化货物流向图
    initGoodsMapFlowChart(type) {
      let _this = this;
      $http
        .getGoodsMapFlow()
        .then(res => {
          // res = [{"unload":"4270.624000","name":"宁波市","load":"8257.707000"},{"unload":"86.759000","name":"上海市辖区","load":"100"},{"unload":"697.299000","name":"南京市","load":"200"},{"unload":"60.481000","name":"无锡市","load":"0"},{"unload":"784.129000","name":"苏州市","load":"0"},{"unload":"338.880000","name":"南通市","load":"0"},{"unload":"539.468000","name":"扬州市","load":"0"},{"unload":"30.000000","name":"杭州市","load":"0"},{"unload":"59.398000","name":"温州市","load":"0"},{"unload":"216.790000","name":"嘉兴市","load":"0"},{"unload":"202.780000","name":"湖州市","load":"0"},{"unload":"282.708000","name":"绍兴市","load":"0"},{"unload":"49.060000","name":"金华市","load":"0"},{"unload":"57.680000","name":"舟山市","load":"0"},{"unload":"23.220000","name":"马鞍山市","load":"0"},{"unload":"28.931000","name":"铜陵市","load":"0"},{"unload":"505.360000","name":"漳州市","load":"0"},{"unload":"24.140000","name":"滨州市","load":"0"}]

          let seriesData = [];
          let loadRegion = {
            name: type == "out" ? "出镇海" : "进镇海",
            data: []
          };
          if (type == "out") {
            res.forEach(item => {
              if (Number(item.unload)) {
                loadRegion.data.push([
                  "宁波",
                  item.name.replace("市", "").replace("辖区", ""),
                  Number(item.unload).toFixed(2)
                ]);
              }
            });
          } else if (type == "in") {
            res.forEach(item => {
              if (Number(item.load)) {
                loadRegion.data.push([
                  item.name.replace("市", "").replace("辖区", ""),
                  "宁波",
                  Number(item.load).toFixed(2)
                ]);
              }
            });
          }
          seriesData.push(loadRegion);
          _this.$refs.goodsMapFlow.setData(seriesData);
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 今日车辆点击查看详情
    vecStatusChartClickFun(param) {
      let _this = this;
      let tagNm = null,
        postData = {};
      switch (param.name) {
        case "重车":
          tagNm = "重车";
          postData.type = 2;
          break;
        case "空车":
          tagNm = "空车";
          postData.type = 1;
          break;
        case "过境":
          tagNm = "过境车";
          postData.type = 2;
          break;
        case "有毒化学品":
          tagNm = "有毒化学品车";
          postData.type = 4;
          break;
        // case "易制爆":
        //   tagNm = "易制爆品车";
        //   postData.type = 3;
        //   break;
        case "特别管控":
          tagNm = "特别管控车";
          postData.type = 7;
          break;
        case "易燃气体":
          tagNm = "易燃气体";
          postData.type = 5;
          break;
        case "易燃液体":
          tagNm = "易燃液体";
          postData.type = 6;
          break;
        case "重点":
          tagNm = "重点车辆";
          postData.type = 3;
          break;
      }

      // this.comDgTitle = tagNm + "明细";
      // this.comDgVisible = true;
      // this.comTbHeader = [
      //   { name: "所属企业", field: "entpName" },
      //   { name: "车牌号", field: "_id" },
      // ];
      // this.$set(_this, "comTbList", []);
      // this.comTbLoading = true;
      // $http
      //   .getVecDetailByType(postData)
      //   .then((res) => {
      //     _this.comTbLoading = false;
      //     _this.$set(_this, "comTbList", res);
      //   })
      //   .catch((error) => {
      //     _this.comTbLoading = false;
      //     console.log(error);
      //   });

      this.comTbHasPaginationType = "今日车辆情况";
      this.comDgHasPaginationTitle = tagNm + "车次详情";
      this.comDgHasPaginationVisible = true;
      this.comTbHasPaginationHeader = [];
      this.comTbHasPaginationHeader = [
        { name: "货物名称", field: "goodsNm", sortable: true },
        { name: "货物数量", field: "loadQty" },
        { name: "牵引车牌号", field: "_id" },
        { name: "挂车牌号", field: "traiVecNo" },
        { name: "所属企业", field: "entpName" }
      ];
      this.comTbHasPaginationStaticPostData = postData;
      this.getTbList({
        page: 1,
        limit: 20
      });
    },

    // 今日车辆情况
    initVecStatusChart() {
      let _this = this;
      $http
        .getVecStatusData()
        .then(data => {
          // this.$set(this.truckCnt, "innerZhCnt", data.innerZhCnt); // 设置看板区内车数量；//todo
          // let xAxisArr = ["重车", "空车", "重点", "易制毒", "易制爆","过境", "园区内"],
          let xAxisArr = [
            "空车",
            "重车",
            "特别管控",
            "有毒化学品",
            "易燃气体",
            "易燃液体"
          ],
            seriesData = null;
          // seriesData = [data.fullCarsCnt, data.emptyCarsCnt, data.zdclCnt, data.yzdpCnt, data.yzbpCnt, data.transitCnt, data.innerZhCnt];
          seriesData = [
            data.emptyCarsCnt,
            data.fullCarsCnt,
            data.gkCnt,
            data.ydCnt,
            data.yrqtCnt,
            data.yrytCnt
          ];
          let windowClientWidth = Tool.getClientWidth();
          let config = {};
          if (windowClientWidth < 1200) {
            config.rotate = 45;
          } else {
            config.interval = 0;
          }
          this.$refs.vecStatusChart.setData(
            seriesData,
            xAxisArr,
            config,
            _this.vecStatusChartClickFun
          );
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 今日违章情况点击查看详情
    violationChartClickFun(param) {
      let _this = this;
      this.comDgTitle = param.name + "明细";
      if (param.name == "无卫星定位") {
        param.name = "无GPS报警";
      }
      if (param.name == "异常停车") {
        param.name = "停车";
      }
      let postData = { catNmCn: param.name };
      this.comDgVisible = true;
      this.comTbHeader = [
        { name: "所属企业", field: "entp_nm" },
        { name: "车牌号", field: "tractor_no" },
        { name: "挂车", field: "trailer_no" },
        { name: "驾驶员", field: "driver_nm" },
        { name: "押运员", field: "guards_nm" },
        { name: "报警时间", field: "alarm_time" },
        { name: "详情", field: "descr" }
      ];
      this.$set(_this, "comTbList", []);
      this.comTbLoading = true;
      $http
        .getAlarmDetail(postData)
        .then(res => {
          _this.comTbLoading = false;
          _this.$set(_this, "comTbList", res);
        })
        .catch(error => {
          _this.comTbLoading = false;
          console.log(error);
        });
    },

    // 今日违章情况
    initViolationChart() {
      let _this = this;
      $http
        .getViolationData()
        .then(res => {
          // res = [{"y":33,"x":"偏离路线预警"},{"y":195,"x":"停车预警"},{"y":15,"x":"挂车未登记报警"},{"y":3,"x":"无GPS报警"},{"y":117,"x":"超速预警"}]

          let data = {};
          let seriesData = [],
            alarmData = [];
          let disease = [];
          let alarmTotal = 0; // 报警总数

          alarmTotal = res.reduce((total, item) => {
            return total + item.y;
          }, 0);

          res.forEach(item => {
            // Jira任务： ZHYS-1454 (根据任务以下代码进行注释)
            // if (item.x == "停车报警") {
            //   item.x = "异常停车";
            // }
            // if (item.x == "偏离路线报警") {
            //   item.x = "偏离路线";
            // }
            // if (item.x == "挂车未登记报警") {
            //   item.x = "挂车未登记";
            // }
            // if (item.x == "无卫星定位报警") {
            //   item.x = "无卫星定位";
            // }
            let per = ((item.y / alarmTotal) * 100).toFixed(2) + "%";
            seriesData.push({ name: item.x, value: item.y });
            alarmData.push({ name: item.x, percent: per });
          });
          let config = {
            titleShow: true,
            title: alarmTotal,
            radius: ["20%", "40%"],
            center: ["50%", "63%"],
            labelFormatter: "{b}\n{c}个\n({d}%)",
            tooltipFormatter: "{b}<br />{c}个<br />({d}%)"
          };
          this.$refs.violationChart.setData(
            seriesData,
            config,
            _this.violationChartClickFun
          );
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 今日车辆进出镇海情况
    initVecEntryAndExitChart() {
      $http
        .getVecEntryAndExitData()
        .then(res => {
          // res = [{"time":"00:00","count":[45,31]},{"time":"01:00","count":[30,38]},{"time":"02:00","count":[41,23]},{"time":"03:00","count":[32,33]},{"time":"04:00","count":[40,60]},{"time":"05:00","count":[76,133]},{"time":"06:00","count":[161,205]},{"time":"07:00","count":[123,112]},{"time":"08:00","count":[132,105]},{"time":"09:00","count":[163,164]},{"time":"10:00","count":[15,13]}]
          let _this = this;

          let xAxis = [],
            seriesData = [
              { name: "进镇海", data: [] },
              { name: "出镇海", data: [] }
            ];
          var seriesData2 = [];
          res &&
            res.length > 0 &&
            res.forEach(item => {
              xAxis.push(item.time);
              seriesData[0].data.push(item.count[0]); // 进镇海
              seriesData[1].data.push(item.count[1]); // 出镇海
            });
          let config = {
            xAxisName: "时间",
            yAxisName: "辆"
          };
          this.$refs.vecEntryAndExitChart.setData(seriesData, xAxis, config);
        })
        .catch(error => {
          console.log(error);
        });
    },

    handleCurrentChange(page) {
      this.pagination.page = page;
      this.qtyChartClickFun({ name: '登记车次' })
    },

    handleSizeChange(limit) {
      this.pagination.limit = limit;
      this.qtyChartClickFun({ name: '登记车次' })
    },
    // 今日货量情况点击查看详情
    qtyChartClickFun(param) {
      let _this = this;
      let tagNm = null,
        postData = { ...this.pagination };
      switch (param.name) {
        case "装货量":
          tagNm = "装货量";
          postData.icCd = 1;
          break;
        case "卸货量":
          tagNm = "卸货量";
          postData.icCd = -1;
          break;
        case "装货车次":
          tagNm = "装货车次";
          postData.icCd = 1;
          break;
        case "卸货车次":
          tagNm = "卸货车次";
          postData.icCd = -1;
          break;
        case "登记车次":
          tagNm = "登记车次";
          postData.icCd = 0;
          break;
      }
      this.comDgTitle = tagNm + "详情";
      this.comDgVisible = true;
      this.comTbHeader = [];
      this.comTbHeader = [
        { name: "所属企业", field: "entp_nm_cn" },
        { name: "车牌号", field: "cd" },
        { name: "驾驶员", field: "dv_nm" },
        { name: "押运员", field: "sc_nm" },
        { name: "装卸地点", field: "reg_pot" },
        { name: "货物", field: "goods_nm" },
        { name: "重量（千克）", field: "load_qty" }
      ];

      this.$set(_this, "comTbList", []);
      this.comTbLoading = true;
      $http
        .getGoodsTodayDetail(postData)
        .then(res => {
          _this.comTbLoading = false;
          _this.$set(_this, "comTbList", res);
        })
        .catch(error => {
          _this.comTbLoading = false;
          console.log(error);
        });
    },

    /**
      * 今日货量情况
      */
    initQtyChart() {
      let _this = this;
      // 获取装卸货量
      $http
        .getDailyLoadUnloadQty()
        .then(res => {
          // 1表示装，-1表示卸货，0表示空车过磅
          let loadQty = res.find(item => {
            return item.key == "1";
          }),
            unloadQty = res.find(item => {
              return item.key == "-1";
            });
          _this.loadQty = loadQty ? loadQty.value.toFixed(2) : 0
          _this.unloadQty = unloadQty ? unloadQty.value.toFixed(2) : 0
        })
        .catch(error => {
          console.log(error);
        });

      // 获取装货车次，卸货车次，登记车次
      $http
        .getGoodsToday()
        .then(res => {
          // 装车次：loadCnt，卸货车次：unloadCnt，过登记点车次：passDjdCnt
          _this.loadCnt = res.loadCnt || 0;
          _this.unloadCnt = res.unloadCnt || 0;
          _this.passDjdCnt = res.passDjdCnt || 0;
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 今日登记点登记情况
    initRegPotStatChart() {
      let _this = this,
        total = 0;
      $http
        .getRegPotStat()
        .then(res => {
          // res = [{"key":"镇海登记点1","value":298},{"key":"镇海登记点2","value":373},{"key":"镇海登记点3","value":381},{"key":"镇海登记点4","value":398},{"key":"镇海登记点5","value":322},{"key":"镇海登记点6","value":266}]

          let xAxisArr = [
            "海天①",
            "海天②",
            "蛟川①",
            "蛟川②",
            "澥浦①",
            "澥浦②",
            "石化①",
            "石化②"
          ],
            seriesData = [0, 0, 0, 0, 0, 0];

          res.data.forEach(item => {
            if (item.key.indexOf("镇海登记点1") >= 0) {
              seriesData[0] = item.value;
            } else if (item.key.indexOf("镇海登记点2") >= 0) {
              seriesData[1] = item.value;
            } else if (item.key.indexOf("镇海登记点3") >= 0) {
              seriesData[2] = item.value;
            } else if (item.key.indexOf("镇海登记点4") >= 0) {
              seriesData[3] = item.value;
            } else if (item.key.indexOf("镇海登记点5") >= 0) {
              seriesData[4] = item.value;
            } else if (item.key.indexOf("镇海登记点6") >= 0) {
              seriesData[5] = item.value;
            } else if (item.key.indexOf("镇海登记点7") >= 0) {
              seriesData[6] = item.value;
            } else if (item.key.indexOf("镇海登记点8") >= 0) {
              seriesData[7] = item.value;
            }
            total += item.value;
          });

          let windowClientWidth = Tool.getClientWidth();
          let config = {
            interval: 0,
            title: "总量：" + total + " 车次",
            grid: {
              left: "3%",
              right: "3%",
              top: "20%",
              bottom: "3%",
              containLabel: true
            },
            barColor: "#54a992"
          };
          if (windowClientWidth < 1200) {
            config.rotate = 45;
          }
          _this.$refs.regPotStatChart.setData(seriesData, xAxisArr, config);
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 点击装卸货显示内容
    selectGoodsType(type) {
      if (type == "load") {
        this.isActiveOfLoadGoodsTypeChart = true;
        // 刷新显示装货数据
      } else if (type == "unload") {
        this.isActiveOfLoadGoodsTypeChart = false;
        // 刷新显示卸货数据
      }
      this.initQtyTop5Chart();
    },

    // 今日区内装卸货Top5---装货
    initLoadGoodsTypeChart() {
      let _this = this;
      this.goodsTypeOfTop5Loading = true;
      $http
        .getLoadCnt()
        .then(res => {
          // res = [{"key":"二甲苯","value":1813059},{"key":"石油混合二甲苯","value":481629},{"key":"工业己烷","value":414983},{"key":"石油苯","value":279152},{"key":"异丁烷","value":213200},{"key":"液化石油气","value":212593},{"key":"工业用碳十粗芳烃","value":122139},{"key":"戊烷发泡剂","value":115706},{"key":"对二甲苯 ","value":41856},{"key":"液碱","value":40629}];

          _this.goodsTypeOfTop5Loading = false;
          let seriesData = [],
            total = 0;
          res.forEach((item, index) => {
            if (index < 5) {
              // 统计top5
              seriesData.push({ value: item.value, name: item.key });
              total += item.value;
            }
          });
          let config = {
            titleShow: true,
            title: total.toFixed(2) + "吨",
            labelLineIsShow: true,
            labelPosition: "outside",
            tooltipShow: true,
            radius: ["30%", "50%"],
            labelFormatter: function (a, b, c) {
              if (a.name && a.name.length >= 8) {
                let length = a.name.length;
                return `${a.name.slice(0, 7)}\n${a.name.slice(
                  7
                )}\n${a.value.toFixed(2)}吨\n(${a.percent}%)`;
              } else {
                return `${a.name}\n${a.value.toFixed(2)}吨\n(${a.percent}%)`;
              }
            },
            tooltipFormatter: function (a, b, c) {
              if (a.name && a.name.length >= 8) {
                let length = a.name.length;
                return `${a.name.slice(0, 7)}<br />${a.name.slice(
                  7
                )}<br />${a.value.toFixed(2)}吨<br />(${a.percent}%)`;
              } else {
                return `${a.name}<br />${a.value.toFixed(2)}吨<br />(${a.percent
                  }%)`;
              }
            }
          };
          this.$refs.loadGoodsTypeChart.setData(seriesData, config);
        })
        .catch(error => {
          _this.goodsTypeOfTop5Loading = false;
          console.log(error);
        });
    },

    // 今日区内装卸货Top5---卸货
    initUnloadGoodsTypeChart() {
      let _this = this;
      this.goodsTypeOfTop5Loading = true;
      $http
        .getUnloadCnt()
        .then(res => {
          // res = [{"y":276.84,"x":"液碱"},{"y":224.48,"x":"碳五"},{"y":211.45,"x":"碳九"},{"y":175.37,"x":"冰醋酸"},{"y":123.72,"x":"硫酸"}]

          _this.goodsTypeOfTop5Loading = false;
          let seriesData = [],
            total = 0;
          res.forEach((item, index) => {
            if (index < 5) {
              // 统计top5
              seriesData.push({ value: item.value, name: item.key });
              total += item.value;
            }
          });
          let config = {
            titleShow: true,
            title: total.toFixed(2) + "吨",
            labelLineIsShow: true,
            labelPosition: "outside",
            tooltipShow: true,
            radius: ["30%", "50%"],
            labelFormatter: function (a, b, c) {
              if (a.name && a.name.length >= 8) {
                return `${a.name.slice(0, 7)}\n${a.name.slice(
                  7
                )}\n${a.value.toFixed(2)}吨\n(${a.percent}%)`;
              } else {
                return `${a.name}\n${a.value.toFixed(2)}吨\n(${a.percent}%)`;
              }
            },
            tooltipFormatter: function (a, b, c) {
              if (a.name && a.name.length >= 8) {
                return `${a.name.slice(0, 7)}<br />${a.name.slice(
                  7
                )}<br />${a.value.toFixed(2)}吨<br />(${a.percent}%)`;
              } else {
                return `${a.name}<br />${a.value.toFixed(2)}吨<br />(${a.percent
                  }%)`;
              }
            }
          };
          this.$refs.unloadGoodsTypeChart.setData(seriesData, config);
        })
        .catch(error => {
          _this.goodsTypeOfTop5Loading = false;
          console.log(error);
        });
    },

    // 今日区内装卸货Top5
    initQtyTop5Chart(refresh) {
      let mainChart = null;
      // 装货
      if (this.isActiveOfLoadGoodsTypeChart) {
        if (this.$refs.loadGoodsTypeChart && this.$refs.loadGoodsTypeChart.$data) {
          mainChart = this.$refs.loadGoodsTypeChart.$data.chart || null;
        }
        this.$nextTick(() => {
          if (!mainChart || refresh) {
            this.initLoadGoodsTypeChart();
          } else {
            mainChart.resize();
          }
        });
      } else {
        // 卸货
        if (this.$refs.unloadGoodsTypeChart && this.$refs.unloadGoodsTypeChart.$data) {
          mainChart = this.$refs.unloadGoodsTypeChart.$data.chart || null;
        }
        this.$nextTick(() => {
          if (!mainChart || refresh) {
            this.initUnloadGoodsTypeChart();
          } else {
            mainChart.resize();
          }
        });
      }
    },

    // 今日货物类别情况
    initGoodsTypeChart() {
      let _this = this;
      $http
        .getRtePlanGoodsType()
        .then(res => {
          // TODO
          // res = [{"key":"2","value":8520.230000},{"key":"3","value":37356.861000},{"key":"4","value":1013.155000},{"key":"5","value":389.110000},{"key":"6","value":1763.710000},{"key":"8","value":10089.374000}]

          let seriesData = [],
            legendData = [];
          res.forEach(item => {
            let name = "";
            if (item.key == 1) {
              name = "一类";
            }
            if (item.key == 2) {
              name = "二类";
            }
            if (item.key == 3) {
              name = "三类";
            }
            if (item.key == 4) {
              name = "四类";
            }
            if (item.key == 5) {
              name = "五类";
            }
            if (item.key == 6) {
              name = "六类";
            }
            if (item.key == 7) {
              name = "七类";
            }
            if (item.key == 8) {
              name = "八类";
            }
            if (item.key == 9) {
              name = "九类";
            }
            seriesData.push({
              value: item.value.toFixed(2),
              name: name
            });
            legendData.push(name);
          });
          let config = {
            // title:'共'+alarmTotal+'个',
            radius: ["40%", "60%"],
            center: ["50%", "50%"],
            labelFormatter: "{b}{c}吨\n({d}%)",
            tooltipFormatter: "{b}<br />{c}吨<br />({d}%)",
            legendData: legendData
          };
          this.$refs.goodsTypeChart.setData(seriesData, config);
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 今日车辆登记情况
    initRegPotChart() {
      let _this = this;
      $http
        .getInOutAreaDailyVec()
        .then(res => {
          // TODO 待删
          // res = [{"key":"00:00","value":50},{"key":"01:00","value":19},{"key":"02:00","value":10},{"key":"03:00","value":10},{"key":"04:00","value":17},{"key":"05:00","value":52},{"key":"06:00","value":74},{"key":"07:00","value":100},{"key":"08:00","value":94},{"key":"09:00","value":130},{"key":"10:00","value":130},{"key":"11:00","value":144},{"key":"12:00","value":129}];

          let xAxis = [],
            seriesData = [{ name: "24小时车辆登记情况", data: [] }];
          res &&
            res.length > 0 &&
            res.forEach(item => {
              xAxis.push(item.key);
              seriesData[0].data.push(item.value);
            });
          let config = {
            xAxisName: "时间",
            yAxisName: "辆",
            issmooth: true,
            splitLine: false,
            labelColor: "#66ff00",
            lineStyleColor: "#66ff00",
            lineWidth: 0.8
          };
          this.$refs.regPotChart.setData(seriesData, xAxis, config);
        })
        .catch(error => {
          console.log(error);
        });
    },
    //系统登记信息
    getPlatformBaseInfo() {
      $http
        .getBasicData()
        .then(res => {
          if (res) {
            for (var attr in res) {
              res[attr] = parseFloat(res[attr]) || 0;
            }
            this.baseInfoData = res;
          } else {
            this.baseInfoData = {
              entpApprCnt: 0,
              entpCnt: 0,
              vecApprCnt: 0,
              tankCnt: 0,
              persApprCnt: 0,
              persCnt: 0,
              vecCnt: 0
            };
          }
        })
        .catch(err => {
          this.baseInfoData = {
            entpApprCnt: 0,
            entpCnt: 0,
            vecApprCnt: 0,
            tankCnt: 0,
            persApprCnt: 0,
            persCnt: 0,
            vecCnt: 0
          };
        });
    }
  }
};
</script>

<style lang="scss" scoped>
// $margin: 25px;
// $padding: 20px;
// $padding-sm: 10px;
$cardWidth: 470px;
// $topCardHeight: 148px;
// $headerHeight: 50px;

.dashboard-container {
  position: relative;
  height: calc(100vh - 50px);
  width: 100%;
  background: url("~static/img/dashboard/bj.jpg") no-repeat center center;
  background-size: cover;

  .left-module,
  .right-module {
    box-sizing: border-box;
    position: absolute;
    top: 20px;
    height: calc(100% - 50px);
    overflow-y: auto;
    overflow-x: hidden;
    bottom: 0;
    z-index: 1;
    scrollbar-color: transparent transparent;
    /* 设置滚动条上的箭头颜色  */
    // scrollbar-arrow-color: #f0f2f5;
    /* 设置滚动条的底色 */
    // scrollbar-base-color: #1d3147;
    /* 设置滚动条块的背景色 */
    // scrollbar-track-color: #407390;
    /* 设置箭头和滚动条右侧和底边的颜色 */
    // scrollbar-shadow-color: #f0f2f5;

    &::-webkit-scrollbar-track-piece {
      background-color: #1d3147;
    }

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #407390;
      border-radius: 20px;
    }

    // padding: 0;
    .card-slot-title {
      font-size: 12px;
      width: 240px;
      // line-height: 30px;
      color: #fff;
      display: flex;
      justify-content: space-evenly;
    }
  }

  .left-module {
    width: $cardWidth;
    // padding-left: $padding;
    animation-fill-mode: forwards;
    left: 10px;
  }

  .right-module {
    width: $cardWidth;
    // padding-right: $padding;
    animation: RToL 1s ease 1s;
    animation-fill-mode: forwards;
    right: 10px;

    .load-unload-tag {
      margin-top: 5px;
      display: inline-block;
      width: 50px;
      height: 28px;
      margin-left: 5px;
      background: linear-gradient(0deg, rgba(51, 214, 255, 0.3), rgba(51, 229, 255, 0.8));
      box-shadow: 0px 1px 0px 0px rgba(51, 214, 255, 0.3), 0px -1px 0px 0px rgba(51, 229, 255, 0.8);
      border-radius: 6px;
      cursor: pointer;
      font-size: 10px;
      line-height: 28px;
      text-align: center;

      &.active {
        background: linear-gradient(0deg, rgba(255, 226, 101, 0.6), rgba(255, 156, 0, 0.6));
        box-shadow: 0px 1px 0px 0px rgba(255, 226, 101, 0.6), 0px -1px 0px 0px rgba(255, 156, 0, 0.6);
      }

      &:hover {
        background: linear-gradient(0deg, rgba(255, 226, 101, 0.6), rgba(255, 156, 0, 0.6));
        box-shadow: 0px 1px 0px 0px rgba(255, 226, 101, 0.6), 0px -1px 0px 0px rgba(255, 156, 0, 0.6);
      }
    }

    .qty-header {
      width: 100%;
      height: 65px;
      background: url("~static/img/dashboard/number.png") no-repeat center center;
      background-size: 100% 100%;
      margin-bottom: 5px;
      padding: 20px 30px 16px 30px;
      display: flex;
      color: #fff;
      font-weight: bold;

      >div {
        width: 80px;
        flex: 1 auto;
        line-height: 29px;
        height: 29px;
        text-align: center;

        &.label {
          flex: 0 0 80px;
          font-size: 16px;
        }

        .num {
          display: inline-block;
          width: 30px;
          height: 29px;
          line-height: 29px;
          text-align: center;
          background: url("~static/img/dashboard/number-bg.png") no-repeat center center;
          background-size: 100% 100%;
          font-size: 17px;
        }
      }
    }

    .qty-chart {
      width: 100%;
      height: 269px;
      background: url("~static/img/dashboard/chart.png") no-repeat center center;
      background-size: cover;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      >div {
        position: relative;
        width: 50%;
        height: 50%;
        text-align: center;
        color: #fff;
        font-size: 16px;
        align-content: center;
        line-height: 30px;

        .qty-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          cursor: pointer;

          .value {
            // margin-bottom: 5px;
            // padding-bottom: 5px;

            &::after {
              display: block;
              width: 75px;
              height: 5px;
              background: url("~static/img/dashboard/chart-number-bottom.png") no-repeat center center;
              background-size: 100% 100%;
              content: '';
              margin: 5px auto;
            }
          }

          .label {
            background: url("~static/img/dashboard/chart-label-bottom.png") no-repeat center center;
            background-size: contain;
          }
        }

        &:nth-child(2n) {
          .qty-content {
            margin-left: 30px;
          }
        }

        &:nth-child(2n+1) {
          .qty-content {
            margin-left: -30px;
          }
        }

        &:nth-child(1),
        &:nth-child(2) {
          .qty-content {
            // margin-top: -10px;
          }
        }

        &:nth-child(3),
        &:nth-child(4) {
          .qty-content {
            // margin-bottom: -10px;
          }
        }
      }
    }
  }

  .center-top {
    position: absolute;
    top: 20px;
    left: 480px;
    right: 480px;
    display: flex;
    flex-direction: row;


    >div {
      flex: 1 auto;
      margin: 0 15px;
      background: url("~static/img/dashboard/header-btn-bg.png") no-repeat center center;
      background-size: 100% 100%;
      height: 60px;
      line-height: 45px;
      color: #fff;
      text-align: center;
      font-size: 16px;

      .number {
        font-size: 150%;
        text-shadow: 0px 0px 9px #000;
        color: #8fc320;
        cursor: pointer;
      }

      .routerLink {
        text-align: center;
        flex-grow: 1;
        flex-shrink: 0;
        text-decoration: none;
        color: white;
      }
    }
  }

  .center-module {
    position: absolute;
    top: 100px;
    left: 500px;
    right: 500px;
    bottom: 60px;
    z-index: 1;

    .web-map {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      background: url("~static/img/dashboard/map.webp") no-repeat center top;
      background-size: contain;
    }

    .reback-btns {
      position: absolute;
      top: 10px;
      right: 20px;
      color: #fff;
      z-index: 10;
      white-space: nowrap;
      text-align: center;

      .reback {
        padding: 10px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 100;
        background: url("~static/img/dashboard/button.png") no-repeat center top;
        background-size: 100% 100%;
        margin-bottom: 10px;
        font-weight: bold;
        color: #fff;
        letter-spacing: 4px;
      }
    }

    .major-road-wape {
      color: #fff;
      position: absolute;
      bottom: -10px;
      left: 10px;
      background: url("~static/img/dashboard/legend.png") no-repeat center top;
      background-size: 100% 100%;
      width: 326px;
      height: 283px;
      transition: all 0.3s;

      .major-road-header {
        position: relative;
        height: 38px;
        font-weight: bolder;
        line-height: 38px;
        font-size: 16px;
        padding: 0 10px;
        padding-left: 20px;
        letter-spacing: 2px;
        cursor: pointer;

        .header-arrow {
          position: absolute;
          top: 15px;
          right: 10px;
          width: 24px;
          height: 10px;
          background: url("~static/img/dashboard/arrow.png") no-repeat center top;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }

      .major-road-body {
        height: 240px;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        align-items: stretch;
        justify-content: center;
        padding: 10px;
        transition: opacity 0.3s;
        opacity: 1;
        transition-delay: 0.3s;

        .major-road-item {
          background: linear-gradient(90deg, rgba(3, 108, 153, 1), rgba(3, 108, 153, 0));
          margin: 0 0 5px 0;
          flex: 1 auto;
          align-items: stretch;
          align-content: center;
          padding-left: 10px;
          font-size: 16px;
          letter-spacing: 1px;
          line-height: 32.5px;

          span {
            color: #45bc2f;
            font-weight: bolder;
            letter-spacing: 0;
          }

          &:first-of-type {
            margin-top: 0;
          }

          &:last-of-type {
            margin-bottom: 0;
          }
        }
      }

      &.closed {
        height: 38px;
        overflow: hidden;
        background-size: cover;

        .header-arrow {
          transform: rotate(180deg);
          transition: all 0.3s;
        }

        .major-road-body {
          opacity: 0;
          transition: all 0.3s;
          // transition-delay: ;
        }
      }
    }
  }
}
</style>
