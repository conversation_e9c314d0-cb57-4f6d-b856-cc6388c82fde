<template>
  <!-- 今日登记点登记情况 -->
  <div class="box-container">
    <div class="dashboard-chart-container" ref="chart"></div>
  </div>
</template>

<script>
import chart from "@/mixins/chart";
const fontColor = "#30eee9";
export default {
  name: "",
  mixins: [chart],
  data() {
    return {
      chart: null,
      options: null,
      colorList: [
        "#0487ed",
        "#3eb177",
        "#c1c049",
        "#c59838",
        "#cd6237",
        "#e11000",
        "#aa00a0",
        "#59057b",
        "#ffa96a",
        "#7da87b",
        "#84f2d6",
        "#53c7f0",
        "#005585",
        "#2931b3",
        "#0666e8",
      ],
      seriesData: [],
    };
  },
  mounted() {
  },
  methods: {
    // 随机生成十六进制颜色
    randomHexColor() {
      var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
      while (hex.length < 6) {
        //while循环判断hex位数，少于6位前面加0凑够6位
        hex = "0" + hex;
      }
      return "#" + hex; //返回‘#'开头16进制颜色
    },

    // 根据传入的配置项设置图表配置内容
    getOptions(config) {
      let options = {
        backgroundColor: "transparent",
        title: {
          text: config.title || "",
          textStyle: {
            // color: data.color,
            fontSize: 12,
            fontWeight: "normal",
          },
          right: "5",
          top: "0",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "none",
            shadowStyle: {
              color: 'none',
            }
          },
          formatter: function (params) {
            var tar = params[0];
            return tar.name + "数量：" + tar.data;
          },
        },
        grid: config.grid || {
          left: "5%",
          right: "5%",
          top: "5%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          axisTick: {
            show: true,
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          axisLabel: {
            textStyle: {
              // color: "#6995aa",
              color: "#fff",
            },
            // interval:0,
            rotate: config.rotate || 0,
            formatter: function (val) {
              if (val.length > 4) {
                var _val = val.substring(0, 5) + "\n" + val.substring(5); // 让series 中的文字超出5个显示...
                return _val;
              }
              return val;
            },
          },
          data: [],
        },
        yAxis: {
          axisLine: {
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          axisLabel: {
            fontSize: 11,
            // color: "#6995aa"
            color: "#fff"
          },
          axisTick: {
            show: true,
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          splitLine: {
            show: false,
          },
        },
        series: [],
      };

      if (config && config.interval != undefined) {
        options.xAxis.axisLabel.interval = config.interval;
      }
      return options;
    },

    /**
     * 图表设置数据
     * seriesData：柱状图表数据---[value,value,value]
     * xAxis: X轴的数据---[]
     * config:echart的options配置
     * clickFun:点击的回调函数
     */
    setData(seriesData, xAxis, config, clickFun) {
      let _this = this,
        options = null;
      config = config || {};
      // if (!this.options) {
      this.options = this.getOptions(config);
      // }
      options = Object.assign({}, this.options);

      /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
      options.series = [];
      /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/

      options.xAxis.data = xAxis; // 设置X轴数据
      options.series.push({
        name: "数量",
        type: "bar",
        barWidth: "60%",
        label: {
          normal: {
            show: true,
            position: "top",
            textStyle: {
              color: "#fff",
            },
          },
        },
        itemStyle: {
          normal: {
            // color: config.barColor ? config.barColor : function (params) {
            //   if (params.dataIndex >= _this.colorList.length) {
            //     _this.colorList.push(_this.randomHexColor());
            //   }
            //   return _this.colorList[params.dataIndex];
            // },
            color: function () {
              return new _this.$echarts.graphic.LinearGradient(
                0, 0, 1, 0, [{
                  offset: 0,
                  color: '#2d6e56'
                },
                {
                  offset: 0.5,
                  color: '#61fdc4'
                },
                {
                  offset: 1,
                  color: '#2d6e56'
                }
              ]
              )
            }
          },
        },
        data: seriesData,
      });
      this.seriesData = seriesData;
      this.set(options, clickFun)
    },
  },
};
</script>

<style lang="scss" scoped>
.box-container {
  width: 100%;
  height: 100%;
  position: relative;

  .dashboard-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
  }
}
</style>
