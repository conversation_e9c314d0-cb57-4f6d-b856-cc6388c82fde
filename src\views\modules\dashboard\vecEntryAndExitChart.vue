<template>
  <!-- 今日车辆进出镇海情况 -->
  <div class="box-container">
    <div class="dashboard-chart-container" ref="chart"></div>
  </div>
</template>

<script>
import chart from "@/mixins/chart";
const fontColor = "#30eee9";
export default {
  name: "",
  mixins: [chart],
  data() {
    return {
      chart: null,
      options: null,
      // colorList: [
      //   "#0487ed",
      //   "#3eb177",
      //   "#c1c049",
      //   "#c59838",
      //   "#cd6237",
      //   "#e11000",
      //   "#aa00a0",
      //   "#59057b",
      //   "#ffa96a",
      //   "#7da87b",
      //   "#84f2d6",
      //   "#53c7f0",
      //   "#005585",
      //   "#2931b3",
      //   "#0666e8",
      // ],
      // areaColorList: ['rgba(72, 153, 241, 0.3)', 'rgba(72, 153, 241, 0.3)'],
      colorList: ['#3398DB', '#f8ec04'],
      areaColorList: ['rgba(72, 153, 241, 0.3)', 'rgba(248, 236, 4, 0.3)'],
      seriesData: [],
    };
  },
  mounted() {
  },
  methods: {
    // 随机生成十六进制颜色
    randomHexColor() {
      var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
      while (hex.length < 6) {
        //while循环判断hex位数，少于6位前面加0凑够6位
        hex = "0" + hex;
      }
      return "#" + hex; //返回‘#'开头16进制颜色
    },

    // 根据传入的配置项设置图表配置内容
    getOptions(config) {
      return {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: config.trigger || 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          show: false,
          data: []
        },
        grid: {
          left: '3%',
          right: '3%',
          top: '3%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          name: config.xAxisName || '',
          boundaryGap: true,
          axisLine: {
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          axisLabel: {
            fontSize: 11,
            // color: "#6995aa"
            color: "#fff"
          },
          axisTick: {
            show: true,
            lineStyle: {
              // color: '#6995aa',
              color: '#fff',
              width: 1
            }
          },
          splitLine: {
            show: config.splitLine || false,
            lineStyle: {
              type: 'dotted',
              // color: '#6995aa'
              color: '#fff'
            }
          },
          data: []
        },
        yAxis: [
          {
            type: 'value',
            name: config.yAxisName || '',
            axisLine: {
              lineStyle: {
                // color: '#6995aa',
                color: '#fff',
                width: 1
              }
            },
            axisLabel: {
              fontSize: 11,
              // color: "#6995aa"
              color: "#fff"
            },
            axisTick: {
              show: true,
              lineStyle: {
                // color: '#6995aa',
                color: '#fff',
                width: 1
              }
            },
            splitLine: {
              show: config.splitLine || false,
              lineStyle: {
                type: 'dotted',
                // color: '#6995aa'
                color: '#fff'
              }
            },
          }
        ],
        series: []
      }
    },

    /**
     * 图表设置数据
     * seriesData：柱状图表数据---[value,value,value]
     * xAxis: X轴的数据---[]
     * config:echart的options配置
     * clickFun:点击的回调函数
     */
    setData(seriesData, xAxis, config, clickFun) {
      let _this = this, options = null;
      config = config || {};
      if (!this.options) {
        this.options = this.getOptions(config);
      }
      options = Object.assign({}, this.options);

      /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
      options.legend.data = [];
      options.series = [];
      /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/

      seriesData.forEach((item, index) => {          // 设置y轴数据
        options.legend.data.push({ name: item.name, textStyle: { color: _this.colorList[index], fontSize: "12" } });
        let ops = {
          name: item.name,
          type: 'line',
          data: item.data,
          areaStyle: { normal: { color: _this.areaColorList[index] } },
          lineStyle: {
            normal: {
              color: config.lineStyleColor || _this.colorList[index],
              width: 2
            }
          },
          label: {
            normal: {
              show: true,
              position: 'top',
              textStyle: {
                color: config.labelColor || _this.colorList[index]
              },
              formatter: item.formatter || "{c}"
            }
          },
          itemStyle: {
            color: config.lineStyleColor || _this.colorList[index]
          },
          lineStyle: {
            width: config.lineWidth || 2
          }
        }
        if (config.issmooth) {
          ops = Object.assign(ops, {
            smooth: true,
            symbolSize: 5,
            symbol: 'circle'
          })
        }
        options.xAxis.data = xAxis;              // 设置X轴数据
        if (seriesData.length <= 1) {
          options.legend.show = false;
          ops.areaStyle = null;
        } else {
          options.legend.show = true;
        }
        options.series.push(ops)
      })
      this.seriesData = seriesData;
      this.set(options, clickFun)
    },
  },
};
</script>

<style lang="scss" scoped>
.box-container {
  width: 100%;
  height: 100%;
  position: relative;

  .dashboard-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
  }
}
</style>
