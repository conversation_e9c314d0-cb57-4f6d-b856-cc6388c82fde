<template>
  <!-- 今日预警情况 -->
  <div class="box-container">
    <div class="dashboard-chart-container" ref="chart"></div>
  </div>
</template>

<script>
import chart from "@/mixins/chart";
const fontColor = "#30eee9";
export default {
  name: "",
  mixins: [chart],
  data() {
    return {
      chart: null,
      options: null,                // echarts的配置文件
      colorList: ['#0487ed', '#3eb177', '#c1c049', '#c59838', '#cd6237', '#e11000',
        '#aa00a0', '#59057b', '#ffa96a', '#7da87b', '#84f2d6', '#53c7f0', '#005585', '#2931b3', '#0666e8'],

      seriesData: [],
    };
  },
  mounted() {
  },
  methods: {
    // 随机生成十六进制颜色
    randomHexColor() {
      var hex = Math.floor(Math.random() * 16777216).toString(16); //生成ffffff以内16进制数
      while (hex.length < 6) {
        //while循环判断hex位数，少于6位前面加0凑够6位
        hex = "0" + hex;
      }
      return "#" + hex; //返回‘#'开头16进制颜色
    },

    // 根据传入的配置项设置图表配置内容
    getOptions(config) {
      return {
        backgroundColor: 'transparent',
        title: {
          show: (config && config.titleShow) || false,
          text: (config && config.title) || '',
          x: 'center',
          y: '60%',
          textStyle: {
            fontWeight: 'normal',
            // color: "#0bb6f0",
            color: "#fff",
            fontSize: 13
          }
        },
        legend: {
          show: false,
          orient: 'horizontal',
          bottom: '10',
          itemHeight: '5',
          itemWidth: 5,
          textStyle: {
            color: '#fff'
          },
          data: []
        },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: config.tooltipFormatter || '{b}<br/>数量：{c}（{d}%）'
        },
        series: []
      };
    },

    /**
     * 图表设置数据
     * seriesData：柱状图表数据---[value,value,value]
     * config:echart的options配置
     * clickFun:点击的回调函数
     */
    setData(seriesData, config, clickFun) {
      let _this = this, options = null;
      config = config || {};
      if (!this.options) {
        this.options = this.getOptions(config);
      }
      options = Object.assign({}, this.options);

      /*>>>>>>>>>>> 设置初始值 >>>>>>>>>>>*/
      options.series = [];
      /*<<<<<<<<<<< 设置初始值 <<<<<<<<<<*/
      if (config.title) {
        options.title.text = config.title;
      }
      if (config.legendData && config.legendData.length > 0) {
        options.legend.show = true;
        options.legend.data = config.legendData;
      }
      options.series.push({
        name: 'pie',
        type: 'pie',
        radius: config.radius || ['35%', '60%'],
        center: config.center || ['50%', '50%'],
        label: {
          normal: {
            show: true,
            position: 'outside',
            textStyle: {
              color: '#fff'
            },
            formatter: config.labelFormatter || "{b}: {c}"
          },
          emphasis: {
            show: true
          }
        },
        labelLine: {
          normal: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          }
        },
        itemStyle: {
          normal: {
            color: function (params) {
              if (params.dataIndex >= _this.colorList.length) {
                _this.colorList.push(_this.randomHexColor());
              }
              return _this.colorList[params.dataIndex];
            }
          }
        },
        data: seriesData
      })
      this.seriesData = seriesData;
      this.set(options, clickFun)
    },
  },
};
</script>

<style lang="scss" scoped>
.box-container {
  width: 100%;
  height: 100%;
  position: relative;

  .dashboard-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
  }
}
</style>
