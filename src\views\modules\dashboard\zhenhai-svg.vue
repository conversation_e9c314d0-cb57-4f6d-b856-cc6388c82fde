<template>
  <div>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="图层_2"
      class='moqiSvg' viewBox="0 0 521 354" xml:space="preserve">
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M246.302,244.414c-0.573-2.211-3.416-2.31-3.416-2.31l3.416-10.264  c0,0-2.365-4.59-2.416-5.736s4.736-3.925,4.736-3.925s-0.31-3.367-0.698-4.736s-1.622-1-1.622-1v-4.226c0,0,3.698-0.717,4.339-1.957  s0.983-2.885,0.094-3.176c-0.889-0.29-2.986-1.085-2.717-2.113s0.604-7.849,0.604-7.849s0.94-1.06,0.811-2.925  c-0.129-1.864-2.059-3.198-2.131-4.019s3.698-7.117,3.735-8.151s2.837,0.472,3.434,0s2.403-2.091,2-2.717  c-0.403-0.626-8.597-3.061-10.169-4.216s-8.302-5.658-8.056-6.35s4.037-1.811,4.037-1.811s1.529-1.809,1.208-3.321  c-0.322-1.512-5.244,0-5.244,0s-2.371-6.738-2.113-7.849s15.508-5.002,17.169-5.434c1.661-0.432,3.448-2.768,3.17-4.226  c-0.278-1.459-6.347-3.791-6.34-4.528s3.68-2.513,4.177-4.528c0.497-2.015-1-6.725-1-6.725c-2.165-0.061-1.484-1.808-2.058-2.382  c-0.656-0.656-2.406-0.11-4.375,0.875s-2.188-0.875-2.844-1.203c-0.656-0.328-3.063,0.219-4.485,1.094  c-1.422,0.875-3.172,1.094-3.828,0.875s0.328-1.203,0-3.828s-1.312-3.39-3.609-2.953s-3.609,0.328-3.938-0.547  s0.546-1.313-1.642-1.094c-2.187,0.219-2.844-0.109-4.375,0.875c-1.53,0.984-1.969,1.312-2.733,0.656  c-0.765-0.656-1.203-1.203-0.984-2.297c0.22-1.094-0.546-1.204-1.203-1.313c-0.656-0.109-0.438-2.296-1.313-2.625  s-0.984,0.984-1.094,2.734s-1.095,2.297-2.079,1.969c-0.985-0.328-1.313-0.547-3.172,1.203s-3.392,0.984-5.578,0.656  c-2.188-0.328-1.75,0.219-2.406-0.547c-0.258-0.301-0.888-0.789-1.683-1.349c0,0-1.818,2.66-2.476,2.77s-4.594,0.438-5.688-0.711  c-1.095-1.148-1.095-1.983-1.859-2.059c-0.765-0.074-2.297,3.025-3.281,2.897c-0.983-0.128-9.188-2.329-10.063-1.447  s0.875,4.71,1.531,5.913c0.655,1.203,0.655,2.516,1.202,2.625c0.548,0.109,3.281-1.859,3.5-0.547  c0.22,1.313-0.328,4.594,0.109,6.344c0.438,1.75,1.094,9.516-0.328,11.156c-1.422,1.643-9.078,2.297-10.172,3.172  s1.422,4.156,0,5.031s-2.844-0.766-2.844,4.814c0,5.578,2.297,12.029,1.641,12.469c-0.656,0.438-4.922-1.203-5.031-0.547  c-0.108,0.656,0.656,14.656,0,15.531c-0.655,0.875-6.452,5.357-6.344,6.451c0.109,1.096-0.001,2.393-0.001,2.393l-1.213,1.449  l0.003-0.003c2.108,2.065,3.047,3.399,3.249,5.618c0.276,3.033,0,4.136,0,4.136c3.033,0.552,6.341-2.759,6.341-2.759  c0.827,2.205,4.688,4.688,5.515,7.168c0.826,2.481-0.827,5.238,0,7.17c0.826,1.931,4.135,1.378,6.064,1.103  c1.931-0.274,5.79-3.308,7.72-2.481c1.931,0.828,0.827,3.309,2.206,4.412c1.38,1.103,2.205-0.275,4.136,1.103  c1.93,1.378,0,3.585,0,3.585s1.378-0.552,2.48,0.551c1.104,1.104,1.104,4.963,1.104,4.963s6.065-1.654,6.893,1.654s0,8.547,0,8.547  l13.785,14.896c2.758-1.655,6.893,1.654,6.893,1.654l2.206-1.931c0,0,1.93,1.931,3.309,1.931s2.481-2.205,4.136-2.205  c0.827,0,2.55,1.103,4.136,2.619l-0.102-0.091c0,0,0.357-4.155,1.06-3.875c0.702,0.281,2.81-1.264,2.949-2.246  c0.14-0.981,0.701-1.825,1.825-2.526c1.124-0.702,0-3.092,0-4.354C244.502,246.784,245.358,245.533,246.302,244.414" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M246.302,244.414c-0.573-2.211-3.416-2.31-3.416-2.31l3.416-10.264  c0,0-2.365-4.59-2.416-5.736s4.736-3.925,4.736-3.925s-0.31-3.367-0.698-4.736s-1.622-1-1.622-1v-4.226c0,0,3.698-0.717,4.339-1.957  s0.983-2.885,0.094-3.176c-0.889-0.29-2.986-1.085-2.717-2.113s0.604-7.849,0.604-7.849s0.94-1.06,0.811-2.925  c-0.129-1.864-2.059-3.198-2.131-4.019s3.698-7.117,3.735-8.151s2.837,0.472,3.434,0s2.403-2.091,2-2.717  c-0.403-0.626-8.597-3.061-10.169-4.216s-8.302-5.658-8.056-6.35s4.037-1.811,4.037-1.811s1.529-1.809,1.208-3.321  c-0.322-1.512-5.244,0-5.244,0s-2.371-6.738-2.113-7.849s15.508-5.002,17.169-5.434c1.661-0.432,3.448-2.768,3.17-4.226  c-0.278-1.459-6.347-3.791-6.34-4.528s3.68-2.513,4.177-4.528c0.497-2.015-1-6.725-1-6.725c0.309,0.009,0.677-0.017,1.114-0.085  c3.5-0.547,6.234-0.875,8.422-0.656c2.189,0.219,3.188-0.109,2.796-2.844c0,0-1.25-5.797,0-6.891s1.469,1.531,2.672,1.641  s4.156-2.297,4.484,0.547s2.733,9.953,2.078,10.719c-0.656,0.766-7.001,1.969-7.329,2.625s2.734,5.688,2.954,7.656  c0.219,1.968,3.719,10.171,2.953,10.937c-0.767,0.765-0.11,1.313,0.983,2.734s3.391,7.548,3.828,7.548s4.593-3.172,5.031-1.642  c0.436,1.531,3.5,6.781,5.031,8.531c1.53,1.75,4.375,5.906,4.375,5.906l2.516-2.406l1.174,2.232l0.001-0.002l1.93,4.136l3.86-2.614  l9.648,8.681c0,0,0.828-2.206,2.759-1.654c1.93,0.551,3.309,3.86,3.309,3.86s-2.757,1.93-3.585,1.93s-7.445,4.609-6.893,6.165  c0.551,1.556-1.654,4.036-1.654,4.036s1.929,1.653,2.48,2.757c0.551,1.105-2.48,4.118-1.103,5.092s3.309-1.801,4.963,0  c1.655,1.802,0.551,4.007,0.551,4.007c-2.667,0.139-6.095-0.203-6.937,1.061s-1.687,1.264-1.687,1.264s-1.402-2.808-3.369-2.387  c-1.966,0.421-2.528,1.08-3.511,1.172c-0.982,0.092-2.809,1.353-3.791,2.197c-0.981,0.843-2.386,2.246-2.245,3.089  c0.14,0.842,2.246,1.684,1.264,2.526c-0.983,0.843-6.74,4.635-6.74,4.635l1.687,2.668c0,0-6.601,6.74-7.303,7.021  c-0.702,0.28-0.7-1.123-1.685-1.123s-6.88,4.772-8.145,4.772c-1.264,0-4.353-5.056-5.336-4.914  c-0.982,0.142-1.965,2.246-2.526,2.246c-0.562,0-1.123-1.545-1.965-1.404c-0.843,0.142-6.319,9.267-7.302,8.847  c-0.983-0.422-3.651-3.369-4.634-2.668C247.98,242.594,247.108,243.456,246.302,244.414" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M408.469,243.426c0,0-10.339-11.687-11.166-12.238  c-0.827-0.553-2.481-10.754-2.481-12.131c0-1.379,0-2.482-1.379-4.137s-1.93-4.688-1.378-6.617s22.332-22.607,27.785-33.208  l-0.003,0.001c-5.144-2.069-9.654-4.192-12.894-6.217c-13.233-8.271-27.845-20.675-41.354-34.737l-21.784,23.707l-11.028-10.477  l-4.136,1.654c0,0,4.411,5.515,5.238,6.617s-8.547,8.271-9.374,8.271s-3.31-4.687-3.31-4.687l-14.337,7.72l1.104,3.033l-11.58,6.893  l1.93,4.136l3.86-2.614l9.648,8.681c0,0,0.828-2.206,2.759-1.654c1.93,0.552,3.309,3.86,3.309,3.86s-2.758,1.93-3.585,1.93  s-7.444,4.609-6.893,6.165s-1.654,4.036-1.654,4.036s1.93,1.652,2.48,2.757c0.552,1.104-2.479,4.118-1.103,5.092  s3.309-1.801,4.963,0c1.654,1.802,0.551,4.007,0.551,4.007l2.758,1.379c0,0,1.654-3.309,2.758-2.205  c1.103,1.103,8.271,5.515,8.271,5.515l-9.926,8.821c0,0,4.136,5.238,5.238,4.964c1.102-0.275,4.411-3.584,4.411-3.584l1.653,1.378  l-2.48,2.217l1.931,1.368l3.584-3.033l6.893,6.89c0,0-1.93,1.932-0.274,3.586c1.655,1.653-1.379,2.865-1.379,2.865  s-3.033-0.386-1.654,3.475s2.757,5.24,4.963,6.343s-0.826,1.931-1.93,2.481c-1.104,0.549-4.136,1.103-2.481,2.756  c1.655,1.654,4.136,3.86,3.032,5.791c-1.103,1.93-3.584,1.653-3.584,1.653s-0.274,5.238,0,6.341  c0.275,1.104-0.827,4.965-0.827,6.342c0,1.379-1.103,4.137-1.103,5.238c0,1.103-3.584,0.551-2.757,2.205s0.551,6.618,1.378,6.895  c0.827,0.275,6.617,0,6.617,0s0.552,3.584,0.552,4.41c0,0.828-3.196,0.644-2.758,1.654c0.807,1.863,1.521,3.703,1.521,3.703  l-0.002-0.002c1.892-1.137,3.966-2.123,6.201-2.868c8.271-2.756,20.679-3.04,25.365-4.694c4.686-1.653,12.682-7.443,15.99-12.957  c3.31-5.515,3.033-11.305,6.343-19.575c3.309-8.271,7.72-13.235,14.337-17.646c0.953-0.636,2.021-1.146,3.17-1.55" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M198.69,64.831c2.12,1.862,2.998,0.931,5.894,0.931  c3.859,0,11.304-0.275,12.683-1.103c1.379-0.828,1.93-2.206,6.065-1.93c4.135,0.276,3.584,2.481,6.893,0  c3.31-2.481,4.963-2.756,7.168-0.275c2.205,2.481,7.168,3.032,8.547,0.827s0.827-4.688,2.481-6.065s3.547,1.103,9.354-5.238  c5.809-6.341,3.327-8.271,4.154-12.683s5.238-8.823,9.098-13.786s9.653-11.301,9.653-11.301s11.855,20.678,16.818,27.847  c4.963,7.169,20.125,26.192,26.468,36.669c6.342,10.477,28.123,41.356,41.633,55.418l-21.784,23.707l-11.028-10.477l-4.136,1.654  c0,0,4.411,5.515,5.238,6.617s-8.547,8.271-9.374,8.271s-3.31-4.687-3.31-4.687l-14.337,7.72l1.104,3.033l-11.58,6.893l-0.001,0.002  l-1.174-2.232l-2.516,2.406c0,0-2.845-4.156-4.375-5.906c-1.531-1.75-4.595-7-5.031-8.531c-0.438-1.531-4.594,1.641-5.031,1.641  s-2.735-6.127-3.828-7.548s-1.75-1.968-0.983-2.734c0.766-0.766-2.734-8.969-2.953-10.937c-0.22-1.968-3.282-7-2.954-7.656  s6.673-1.859,7.329-2.625c0.655-0.766-1.75-7.875-2.078-10.719s-3.281-0.437-4.484-0.547s-1.422-2.735-2.672-1.641s0,6.891,0,6.891  c0.392,2.735-0.607,3.063-2.796,2.844c-2.188-0.219-4.922,0.109-8.422,0.656c-3.5,0.547-2.516-1.641-3.172-2.297  c-0.656-0.656-2.406-0.11-4.375,0.875s-2.188-0.875-2.844-1.203c-0.656-0.328-3.063,0.219-4.485,1.094  c-1.422,0.875-3.172,1.094-3.828,0.875s0.328-1.203,0-3.828s-1.312-3.39-3.609-2.953s-3.609,0.328-3.938-0.547  s0.546-1.313-1.642-1.094c-2.187,0.219-2.844-0.109-4.375,0.875c-1.53,0.984-1.969,1.312-2.733,0.656  c-0.765-0.656-1.203-1.203-0.984-2.297c0.22-1.094-0.546-1.204-1.203-1.313c-0.656-0.109-0.438-2.296-1.313-2.625  s-0.984,0.984-1.094,2.734s-1.095,2.297-2.079,1.969c-0.985-0.328-1.313-0.547-3.172,1.203s-3.392,0.984-5.578,0.656  c-2.188-0.328-1.75,0.219-2.406-0.547c-0.258-0.301-0.888-0.789-1.684-1.348c-1.229-0.864-2.853-1.902-4.113-2.699  c-2.078-1.313-0.984-1.969-0.984-3.063s-1.423-1.641-1.833-2.516s-0.026-2.077,1.833-4.484c1.86-2.407,1.422-2.188,0.219-4.266  c-1.202-2.078-1.75-0.328-3.391-1.86s0.656-2.735,1.339-3.063c0.684-0.328,0.521-2.078,0-3.172c-0.52-1.094-1.776,0.984-2.433,0.656  c-0.656-0.328,0.109-2.735,0-4.266c-0.11-1.531-2.407-3.827-1.969-4.484s2.734-0.657,3.719-1.313  c0.983-0.656-0.547-2.734-0.547-4.484s0.22-2.516,1.313-2.188s1.75-0.328,1.859-2.406c0.108-2.078-3.172-8.422-2.517-9.297  c0.656-0.875,6.725-3.022,6.725-3.022" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M199.129,65.195c0,0-6.068,2.147-6.725,3.022  c-0.655,0.875,2.625,7.219,2.517,9.297c-0.109,2.078-0.767,2.734-1.859,2.406s-1.313,0.438-1.313,2.188s1.53,3.828,0.547,4.484  c-0.984,0.656-3.281,0.656-3.719,1.313c-0.438,0.657,1.858,2.953,1.969,4.484c0.109,1.531-0.656,3.938,0,4.266  c0.656,0.328,1.913-1.75,2.433-0.656c0.521,1.094,0.684,2.844,0,3.172c-0.683,0.328-2.979,1.531-1.339,3.063  c1.641,1.532,2.188-0.218,3.391,1.86s1.642,1.859-0.219,4.266c-1.859,2.407-2.243,3.609-1.833,4.484s1.833,1.422,1.833,2.516  s-1.094,1.75,0.984,3.063c1.261,0.797,2.885,1.835,4.114,2.698c0,0-1.818,2.66-2.476,2.769s-4.594,0.438-5.688-0.711  c-1.095-1.149-1.095-1.983-1.859-2.058c-0.765-0.075-2.297,3.025-3.281,2.897c-0.983-0.128-9.188-2.329-10.063-1.447  s0.875,4.71,1.531,5.913c0.655,1.203,0.655,2.516,1.202,2.625c0.548,0.109,3.281-1.859,3.5-0.547  c0.22,1.312-0.328,4.594,0.109,6.344c0.438,1.75,1.094,9.516-0.328,11.157s-9.078,2.297-10.172,3.172s1.422,4.156,0,5.031  s-2.844-0.766-2.844,4.813s2.297,12.031,1.641,12.469s-4.922-1.203-5.031-0.547c-0.108,0.656,0.656,14.656,0,15.531  c-0.655,0.875-6.452,5.358-6.344,6.452c0.109,1.095-0.001,2.392-0.001,2.392l-1.213,1.45l0.003-0.003  c-0.774-0.759-1.706-1.616-2.816-2.653c-4.137-3.859-20.402-21.78-22.885-22.884c-2.481-1.104-6.342-1.93-7.443-4.136  c-1.103-2.206-1.931-3.584-6.065-4.411c-4.135-0.827-9.926,2.479-11.58,3.032s-0.827,2.206-3.308,3.309  c-2.481,1.103-9.925,3.308-11.305,1.93c-1.379-1.378,0-3.309-1.93-4.136s-4.411,0.827-7.72,0c-3.31-0.827-4.688-3.584-4.688-3.584  s-1.653,0.828-3.309,0.276c-1.655-0.552-1.103-2.482-2.757-2.758s-3.584,1.379-6.341,1.379c-2.758,0-4.688-1.378-7.168-1.103  c-2.481,0.275-6.065,3.032-7.444,3.032s-1.93-4.963-5.514-3.584c0,0,0-4.963-1.654-4.963s-4.136-4.136-4.136-4.136l5.514-3.584  l2.206,1.654c0,0,4.411-4.096,3.86-7.562c-0.552-3.466-5.515-8.429-5.515-10.083s1.931,0.551,0.827-6.066  c-1.103-6.617-1.93-3.305-4.411-5.787c-2.48-2.482,0.551-2.206-0.552-4.411c-1.103-2.205-4.412-2.206-5.514-4.136  c-1.103-1.93-4.137-9.374-7.721-10.201s-2.206-2.481-1.653-4.136c0.552-1.655,2.961-6.065-0.726-6.893  c-3.686-0.828-7.546-0.276-7.821-2.757c-0.276-2.481-1.379-5.239,0-6.617c1.378-1.378-4.137-2.757-3.584-4.687  c0.552-1.93,1.103-2.757,2.48-3.584s6.341-1.378,7.996,0s5.79-3.584,6.341-1.103s0-2.206,1.654-4.136s3.308-1.378,1.378-6.065  s1.103-6.893,1.103-6.893c1.654,0,6.894-0.828,10.201,0.275c3.309,1.103,6.065,2.206,8.547,1.103  c2.482-1.103,1.932-7.441,4.688-13.783c2.757-6.342,4.687-7.444,6.341-7.444s3.308,2.481,6.617,2.206  c3.309-0.275,5.515-2.205,7.72-4.962c2.205-2.757,4.962-4.963,6.065-6.066c1.103-1.103,1.653,1.103,4.136,0.827  s0.827-3.309,3.86-3.309c3.032,0,7.168,1.654,7.996,2.481c0.827,0.827,0.826-0.827,3.032-2.757s0.827-4.411,4.137-2.481  c3.309,1.93,4.41,3.308,5.514,5.238c1.103,1.93,4.412,1.103,5.79,1.93s0.827,3.309,2.48,3.86c1.654,0.551,2.757,0.276,4.136,0.827  s3.584,1.654,5.238,1.654c1.655,0,1.379-3.584,4.963-3.033c3.585,0.551,9.927-1.93,11.855-1.93s3.584,2.757,3.584,3.86  s5.515,3.584,8.271,3.86s8.271,0.276,7.72,2.206s-0.827,4.411,1.103,5.79c1.931,1.379,2.481,4.136,4.688,5.79  c2.206,1.654,1.93,2.481,9.099,4.963c7.169,2.482,7.444-1.103,11.855,4.411c1.103,1.378,1.947,2.309,2.653,2.929" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M408.469,243.426c0,0-10.339-11.687-11.166-12.238  c-0.827-0.553-2.481-10.754-2.481-12.131c0-1.379,0-2.482-1.379-4.137s-1.93-4.688-1.378-6.617s22.332-22.607,27.785-33.208  l-0.003,0.001c15.872,6.385,37.773,12.251,46.936,13.084c0,0,0-3.31,4.963-5.238c0,0,16.819-4.137,20.127-1.379  c3.309,2.758,3.585,2.758,6.065,3.859c2.481,1.102,5.79,2.758,5.79,2.758s-3.861,1.103-5.239,1.653s-4.411,1.931-7.168,2.206  s-6.343,0-10.478,1.93c-4.136,1.93-14.889,8.819-19.852,13.508c-4.963,4.688-11.027,17.92-13.51,25.09  c-2.481,7.168-1.103,7.443-4.136,9.099s-6.616,2.204-12.958,1.377c-5.428-0.708-15.098-2.021-21.92,0.381" />
      <path fill="#30323C" stroke="#696A6F" stroke-miterlimit="10"
        d="M238.667,260.466c0,0,0.357-4.155,1.06-3.875  c0.702,0.281,2.81-1.264,2.949-2.246c0.14-0.981,0.701-1.825,1.825-2.526c1.124-0.702,0-3.092,0-4.354s2.948-4.491,3.931-5.194  c0.983-0.701,3.651,2.246,4.634,2.668c0.983,0.42,6.459-8.705,7.302-8.847c0.842-0.141,1.403,1.404,1.965,1.404  c0.561,0,1.544-2.104,2.526-2.246c0.983-0.142,4.072,4.914,5.336,4.914c1.265,0,7.16-4.772,8.145-4.772s0.983,1.403,1.685,1.123  c0.702-0.281,7.303-7.021,7.303-7.021l-1.687-2.668c0,0,5.757-3.792,6.74-4.635c0.982-0.842-1.124-1.684-1.264-2.526  c-0.141-0.843,1.264-2.246,2.245-3.089c0.982-0.844,2.809-2.105,3.791-2.197c0.983-0.092,1.545-0.751,3.511-1.172  c1.967-0.421,3.369,2.387,3.369,2.387s0.845,0,1.687-1.264s4.272-0.921,6.939-1.061l2.758,1.379c0,0,1.654-3.309,2.758-2.205  c1.103,1.103,8.271,5.515,8.271,5.515l-9.926,8.821c0,0,4.136,5.238,5.238,4.964c1.102-0.275,4.411-3.584,4.411-3.584l1.653,1.378  l-2.48,2.217l1.931,1.368l3.584-3.033l6.893,6.89c0,0-1.93,1.932-0.274,3.586c1.655,1.653-1.379,2.865-1.379,2.865  s-3.033-0.386-1.654,3.475s2.757,5.24,4.963,6.343s-0.826,1.931-1.93,2.481c-1.104,0.549-4.136,1.103-2.481,2.756  c1.655,1.654,4.136,3.86,3.032,5.791c-1.103,1.93-3.584,1.653-3.584,1.653s-0.274,5.238,0,6.341  c0.275,1.104-0.827,4.965-0.827,6.342c0,1.379-1.103,4.137-1.103,5.238c0,1.103-3.584,0.551-2.757,2.205s0.551,6.618,1.378,6.895  c0.827,0.275,6.617,0,6.617,0s0.552,3.584,0.552,4.41c0,0.828-3.196,0.644-2.758,1.654c0.807,1.863,1.521,3.703,1.521,3.703  l-0.002-0.002c-5.107,3.067-8.881,7.222-10.893,10.643c-2.758,4.687-6.065,19.023-8.548,21.781  c-2.481,2.758-5.238,7.444-7.995,7.444c-2.758,0-6.065-6.065-6.065-6.065s4.964-5.514,7.169-11.304s4.963-6.894,4.963-6.894  l-4.412-7.443c0,0-3.032,1.379-6.064,1.654s-6.341,0-6.341,0s0-3.584-1.379-4.963c0,0-11.576,2.48-16.817,1.103  s-6.62-0.827-6.62-0.827s0.551-1.93-1.653-4.963c-2.205-3.033-3.585-3.309-3.585-3.309l-9.098,7.996l-4.136-1.932l-3.86,4.137  l-3.584-2.758v1.654l-4.136-1.379l-1.654-23.711l3.033-5.514l-5.239-7.444c0,0,2.481-1.654,1.931-4.963  c-0.276-1.654-1.723-3.585-3.309-5.101" />

      <g class="validMap color_a colorL" data-name='329国道(慈溪)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="60.93548" cx="232.5" />
        <text fill="#919294" y="63.93548" x="170" style="font-size:9px;">329国道(慈溪)</text>
      </g>
      <g class="validMap color_a colorL" data-name='绕城高速(江北)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="199" cx="158.5" />
        <text fill="#919294" y="201.93548" x="168" style="font-size: 9px;">绕城高速(江北)</text>
      </g>
      <g class="validMap color_a colorL" data-name='北环东路(江北)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="260.3871" cx="236" />
        <text fill="#919294" y="264.3871" x="246" style="font-size: 9px;">北环东路(江北)</text>
      </g>
      <g class="validMap color_a colorL" data-name='明州大桥(鄞州)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="310.3871" cx="329" />
        <text fill="#919294" y="320.3871" x="335" style="font-size:9px;">明州大桥(鄞州)</text>
      </g>
      <g class="validMap color_a colorL" data-name='绕城高速(北仑)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="290.3871" cx="374" />
        <text fill="#919294" y="293.3871" x="382" style="font-size:9px;">绕城高速(北仑)</text>
      </g>
      <g class="validMap color_a colorL" data-name='招宝山大桥(北仑)'>
        <circle r="3" stroke-width="0" fill="#7AD6CA" cy="229.83871" cx="447.5" />
        <text fill="#919294" y="232.83871" x="455" style="font-size: 9px;">招宝山大桥(北仑)</text>
      </g>
      <!--<g class="validMap color_a colorL" data-name='金塘大桥(舟山)'>
			<circle class="cir cir-in" r="13" cy="134.36" cx="365.76"/>
      <text fill="#919294" y="136.36" x="376" style="font-size: 9px;">金塘大桥(舟山)</text>
		</g>-->
      <g class="validMap color_a colorL" data-name='蛟川登记点'>
        <circle class="cir cir-in" cx="320.94445" cy="161.11649" r="3" stroke-width="0" fill="#7AD6CA" id="svg_16"
          stroke="black" />
        <text style="font-size:12px;" fill="#fff" stroke="#696A6F" stroke-width="0" stroke-opacity="null" x="329.88888"
          y="164.22218" id="svg_28" text-anchor="start" xml:space="preserve">蛟川登记点</text>
      </g>
      <g class="validMap color_a colorL" data-name='石化登记点'>
        <circle class="cir cir-in" cx="265.61111" cy="33.2276" r="3" stroke-width="0" fill="#7AD6CA" id="svg_18"
          stroke="black" />
        <text style="font-size:12px;" fill="#fff" stroke="#696A6F" stroke-width="0" stroke-opacity="null" x="276.33333"
          y="37.11109" id="svg_31" text-anchor="start" xml:space="preserve">石化登记点</text>
      </g>
      <g class="validMap color_a colorL" data-name='澥浦登记点'>
        <circle class="cir cir-in" cx="255.61111" cy="56.2276" r="3" stroke-width="0" fill="#7AD6CA" id="svg_18"
          stroke="black" />
        <text style="font-size:12px;" fill="#fff" stroke="#696A6F" stroke-width="0" stroke-opacity="null" x="264.33333"
          y="60.11109" id="svg_31" text-anchor="start" xml:space="preserve">澥浦登记点</text>
      </g>
      <g class="validMap color_a colorL" data-name='海天登记点'>
        <circle class="cir cir-in" cx="405.77778" cy="186.94982" r="3" stroke-width="0" fill="#7AD6CA" id="svg_17"
          stroke="black" />
        <text style="font-size:12px;" fill="#fff" stroke="#696A6F" stroke-width="0" stroke-opacity="null" x="414.11107"
          y="189.88883" id="svg_32" text-anchor="start" xml:space="preserve">海天登记点</text>
      </g>
    </svg>
  </div>
</template>
<script>
export default {
  name: 'zhenhaiSvg',
  data() {
    return {

    };
  },
  methods: {
  }
}
</script>
<style scoped>
path {
  stroke: #436082;
  stroke-width: 2;
  fill: #1c2d49;
  opacity: 0.7;
  stroke-width: 1px;
}

/* svg .st1 {
	fill: #fff;
	font-size: 110%;
}
.baoshanzhenSvg .st1{
	font-size: 50%;
}
.dengtekebanshichuSvg .st1{
	font-size: 70%;
}
.moqiSvg .st1 {
	font-size: 50px;
	fill: #fff;
} */
/* svg .validMap:hover path {
	fill: #1d4b99;
	cursor: pointer;
}
svg .validMap {
	fill: #1b2769;
} */

/* svg .color_a path {
	fill: #0e1a30;
}

svg .color_b path {
	fill: #182641;
}

svg .validMap path {
	fill: #1b2769;
} */

svg .validMap:hover {
  fill: #1d4b99;
  cursor: pointer;
}

svg .validMap:active path {
  fill: blue;
  cursor: pointer;
}

svg .cir {
  fill: rgba(27, 247, 37, 1);
  stroke: rgba(27, 247, 37, 1);
  stroke-miterlimit: 10;
}

svg .cir-in {
  fill: rgba(27, 247, 37, 1);
  stroke: rgba(27, 247, 37, 1);
  stroke-width: 0;
  r: 0.3%;
}

svg .cir-out {
  fill: rgba(27, 247, 37, 0.5);
  stroke: rgba(27, 247, 37, 1);
  stroke-width: 0.1%;
  r: 1.2%;
  -webkit-animation: WaveOutS 3s infinite;
}

svg .colorM .cir-out {
  fill: rgba(27, 247, 37, 0.5);
  stroke: rgba(27, 247, 37, 1);
  stroke-width: 0.1%;
  r: 1.2%;
  -webkit-animation: WaveOutM 3s infinite;
}

svg .colorL .cir-out {
  fill: rgba(27, 247, 37, 0.5);
  stroke: rgba(27, 247, 37, 1);
  stroke-width: 0.1%;
  r: 1.2%;
  -webkit-animation: WaveOutL 3s infinite;
}

svg .colorL .cir-in {
  fill: rgba(236, 57, 35, 0.87);
  stroke: rgba(236, 57, 35, 0.87);
  stroke-width: 0.1%;
  r: 1.2%;
  -webkit-animation: WaveOutS 3s infinite;
}

svg .colorM .cir-in {
  fill: rgba(247, 255, 1, 0.98);
  stroke: rgba(247, 255, 1, 0.98);
  stroke-width: 0;
  r: 1.3%;
}

svg .colorL .cir-in {
  fill: rgba(236, 57, 35, 0.87);
  stroke: rgba(236, 57, 35, 0.87);
  stroke-width: 0;
  r: 1.3%;
}

.moqiSvg .cir-in {
  fill: rgba(236, 57, 35, 0.87);
  stroke: rgba(236, 57, 35, 0.87);
  stroke-width: 0;
  r: 1.3%;
}

.moqiSvg .cir-out {
  fill: rgba(27, 247, 37, 0.5);
  stroke: rgba(27, 247, 37, 1);
  stroke-width: 0.1%;
  r: 1.2%;
  -webkit-animation: WaveOutL 3s infinite;
}

@keyframes WaveOutS {
  0% {
    r: 0.1%;
    fill: rgba(27, 247, 37, 1);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.3%;
  }

  60% {
    fill: rgba(27, 247, 37, 0.7);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.2%;
  }

  80% {
    fill: rgba(27, 247, 37, 0.5);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.1%;
  }

  99% {
    fill: rgba(27, 247, 37, 0.3);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.1%;
    r: 1.2%;
  }
}

@keyframes WaveOutM {
  from {
    fill: rgba(247, 255, 1, 1);
    stroke-width: 0.3%;
    stroke: rgba(247, 255, 1, 1);
    r: 0.1%;
  }

  60% {
    fill: rgba(247, 255, 1, 0.7);
    stroke: rgba(247, 255, 1, 1);
    stroke-width: 0.2%;
  }

  80% {
    fill: rgba(247, 255, 1, 0.5);
    stroke-width: 0.1%;
    stroke: rgba(247, 255, 1, 1);
  }

  to {
    fill: rgba(247, 255, 1, 0.3);
    stroke: rgba(247, 255, 1, 1);
    stroke-width: 0.1%;
    r: 1.2%;
  }
}

@keyframes WaveOutL {
  from {
    fill: rgba(236, 57, 35, 1);
    stroke-width: 0.3%;
    stroke: rgba(236, 57, 35, 1);
    r: 0.1%;
  }

  60% {
    fill: rgba(236, 57, 35, 0.7);
    stroke: rgba(236, 57, 35, 1);
    stroke-width: 0.2%;
  }

  80% {
    fill: rgba(236, 57, 35, 0.5);
    stroke-width: 0.1%;
    stroke: rgba(236, 57, 35, 1);
  }

  to {
    fill: rgba(236, 57, 35, 0.3);
    stroke: rgba(236, 57, 35, 1);
    stroke-width: 0.1%;
    r: 1.2%;
  }
}

@keyframes WaveOut {
  from {
    fill: rgba(27, 247, 37, 1);
    stroke-width: 0.3%;
    stroke: rgba(27, 247, 37, 1);
    r: 0.1%;
  }

  60% {
    fill: rgba(27, 247, 37, 0.7);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.2%;
  }

  80% {
    fill: rgba(27, 247, 37, 0.5);
    stroke-width: 0.1%;
    stroke: rgba(27, 247, 37, 1);
  }

  to {
    fill: rgba(27, 247, 37, 0.3);
    stroke: rgba(27, 247, 37, 1);
    stroke-width: 0.1%;
    r: 1.2%;
  }
}
</style>
