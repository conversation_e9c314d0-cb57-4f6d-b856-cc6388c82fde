<template>
  <div class="effect-page">
    <el-carousel :autoplay="false" :height="viewPortHeight+'px'" arrow="hover" :indicator-position="'none'">
      <el-carousel-item stylle="position:relative;">
        <div class="fixed-title">违章变化趋势</div>
        <!-- <div style="width:100%;height:100%" id="alarm-chart"></div> -->
        <iframe src="https://p-bp1elwh9e37z1jhhacc8.dacyun.com/html/reports-yearly/zhenhai/2022/#/alarm-slide" frameborder="0" style="height:100%;width:100%;"></iframe>
      </el-carousel-item>
      
      <el-carousel-item stylle="position:relative;">
        <div class="fixed-title">突发事故支持</div>
        <div style="width:100%;text-align:center;height: calc(100vh - 50px);background:#000;">
          <img src="static/img/effect/accident.jpg" style="height:100%;" />
        </div>
        <!-- <iframe :src="iframeUrl[5]" frameborder="0" style="height:100%;width:100%;"></iframe> -->
      </el-carousel-item>
      <el-carousel-item>
        <div class="map-panel" style="width:100%;height:100%;overflow:hidden;position:relative;">
          <div class="fixed-title">车辆超速治理</div>
          <div style="position:relative;float:left;width:50%;height:100%;border:5px solid transparent;border-right-width:0px;">
            <div class="left-map-title">2017超速热力图</div>
            <div class="left-map-desc">
              2017年超速违章高发路段 ：<br />九龙大道 > G329国道 > 长邱线 > 慈海北路 > 临俞南路 > 镇骆东路 > 北环东路
            </div>
            <iframe :src="iframeUrl[0]" frameborder="0" style="height:100%;width:100%;float:left;"></iframe>
          </div>
          <div style="position:relative;float:left;width:50%;height:100%;border:5px solid transparent;">
            <div class="center-map-title">2018超速热力图</div>
            <div class="center-map-desc">
              2018年超速违章高发路段 ：<br />海天中路 > 雄镇路 > 慈海北路 > 招宝山大桥 > 北环东路 > 九龙大道 > G329国道
            </div>
            <iframe :src="iframeUrl[1]" frameborder="0" style="height:100%;width:100%;float:left;"></iframe>
          </div>
          <!-- <div style="position:relative;float:left;width:33.33%;height:100%;border:5px solid transparent;border-left-width:0px;">
                <div class="right-map-title">2019超速热力图</div>
                <div class="right-map-desc">
                    2019年超速违章高发路段 ：<br/>海天中路 > 庄南公路 > 招宝山大桥
                </div>
                <iframe :src="iframeUrl[2]" frameborder="0" style="height:100%;width:100%;float:left;"></iframe>
            </div> -->
        </div>
      </el-carousel-item>
      <el-carousel-item>
        <div style="width:100%;height:100%;background:#222;position:relative;">
          <div class="fixed-title">异常停车治理</div>
          <div style="padding-top:10px;color:#fff;">
            <p class="text-center" style="font-size:16px;">11·28 张家口爆炸事故 警钟长鸣</p>
            <p class="text-center" style="position:relative;font-size:12px;color:#ccc;line-height:1;">系统针对夜间停车展开分析研判</p>
            <p class="text-center" style="position:relative;font-size:12px;color:#ccc;line-height:1;">夜间22:00~次日05:00 路边停车过夜危运车辆，并通报区内执法人员</p>
          </div>
          <div style="padding: 0; margin: 0 auto; margin-top: 20px; border: 1px solid #ccc; position:relative;  width: 1000px; height: 500px">
            <iframe :src="nightPrakAndTrendurl" frameborder="0" style="height:100%;width:100%;"></iframe>
          </div>
          <p class="text-center f-12 f-c-gray m-10" style="position:relative; ">
            <el-button type="warning" size="small" @click="nightPrakAndTrendurl = iframeUrl[6]">夜间停车趋势</el-button>
            <el-button type="danger" size="small" @click="nightPrakAndTrendurl = iframeUrl[7]">夜间停车点</el-button>
          </p>
        </div>
      </el-carousel-item>
      <el-carousel-item stylle="position:relative;">
        <div class="fixed-title">异常人员监测</div>
        <div style="width:100%;height:100%" id="change-chart"></div>
      </el-carousel-item>

      
      <!-- <el-carousel-item>
              <div style="width:100%;height:100%;background:#222;text-align:center;position:relative;" >
                  <div class="fixed-title">危运事故趋势</div>
                  <div style="font-size:18px;color:#fff;text-align:center;line-height:32px;padding-top:20px;font-weight:bold;">危运事故趋势</div>
                  <br/>
                  <img :src="accidentPic" alt="" style="vertical-align:baseline;">
              </div>
          </el-carousel-item> -->
      
    </el-carousel>
  </div>
</template>
<script>
import { getClientHeight } from "@/utils/tool";
import accidentPic from "@/assets/dashboard-img/accident.png";
export default {
  data() {
    return {
      accidentPic: accidentPic,
      viewPortHeight: 500,
      nightPrakAndTrendurl:
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2018report/nightparking-point.html",
      iframeUrl: [
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2019report/overspeed-thd-map-2017.html",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2019report/overspeed-thd-map-2018.html",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2018report/#/nightparking-slide",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2019report/#/change-slide",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2019report/#/alarm-slide",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2019report/achievement.html",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2018report/nightparking-trend.html",
        "//p-bp1elwh9e37z1jhhacc8.dacyun.com/html/2018report/nightparking-point.html"
      ]
    };
  },
  created() {
    this.viewPortHeight = getClientHeight() - 50;
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener("resize", () => {
        this.viewPortHeight = getClientHeight() - 50;
      });
      // this.alarmChart();
      this.changeChart();
      /* let chartIframe = document.getElementById("chartIframe");
            let style = document.createElement('style');
                style.type = 'text/css';
                style.innerHTML='body{ background-color:blue; }';
                window.postMessage('addAndEdit', '*');
                chartIframe.contentWindow.addEventListener('message',function(e){ 
                    console.log(22222,this.style.display = 'none')


                    console.log(this.document)
                    // chartIframe.contentWindow.document.getElementsByTagName('HEAD').item(0).appendChild(style);
                }); */
    });
  },
  methods: {
    alarmChart() {
      var alarmChart = this.$echarts.init(
        document.getElementById("alarm-chart"),
        "dark"
      );

      var option = {
        title: {
          text: "违章/异常 变化趋势",
          subtext: "2016年8月~2018年12月 违章数量变化趋势",
          x: "center",
          y: "5%"
        },
        tooltip: {
          trigger: "axis"
        },
        dataZoom: [
          {
            type: "inside"
          }
        ],
        grid: {
          left: "10%",
          right: "10%",
          bottom: "25%",
          top: "15%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: [
              "2016-08-01",
              "2016-08-02",
              "2016-08-03",
              "2016-08-04",
              "2016-08-05",
              "2016-08-06",
              "2016-08-07",
              "2016-08-08",
              "2016-08-09",
              "2016-08-10",
              "2016-08-11",
              "2016-08-12",
              "2016-08-13",
              "2016-08-14",
              "2016-08-15",
              "2016-08-16",
              "2016-08-17",
              "2016-08-18",
              "2016-08-19",
              "2016-08-20",
              "2016-08-21",
              "2016-08-22",
              "2016-08-23",
              "2016-08-24",
              "2016-08-25",
              "2016-08-26",
              "2016-08-27",
              "2016-08-28",
              "2016-08-29",
              "2016-08-30",
              "2016-08-31",
              "2016-09-01",
              "2016-09-02",
              "2016-09-03",
              "2016-09-04",
              "2016-09-05",
              "2016-09-06",
              "2016-09-07",
              "2016-09-08",
              "2016-09-09",
              "2016-09-10",
              "2016-09-11",
              "2016-09-12",
              "2016-09-13",
              "2016-09-14",
              "2016-09-15",
              "2016-09-16",
              "2016-09-17",
              "2016-09-18",
              "2016-09-19",
              "2016-09-20",
              "2016-09-21",
              "2016-09-22",
              "2016-09-23",
              "2016-09-24",
              "2016-09-25",
              "2016-09-26",
              "2016-09-27",
              "2016-09-28",
              "2016-09-29",
              "2016-09-30",
              "2016-10-01",
              "2016-10-02",
              "2016-10-03",
              "2016-10-04",
              "2016-10-05",
              "2016-10-06",
              "2016-10-07",
              "2016-10-08",
              "2016-10-09",
              "2016-10-10",
              "2016-10-11",
              "2016-10-12",
              "2016-10-13",
              "2016-10-14",
              "2016-10-15",
              "2016-10-16",
              "2016-10-17",
              "2016-10-18",
              "2016-10-19",
              "2016-10-20",
              "2016-10-21",
              "2016-10-22",
              "2016-10-23",
              "2016-10-24",
              "2016-10-25",
              "2016-10-26",
              "2016-10-27",
              "2016-10-28",
              "2016-10-29",
              "2016-10-30",
              "2016-10-31",
              "2016-11-01",
              "2016-11-02",
              "2016-11-03",
              "2016-11-04",
              "2016-11-05",
              "2016-11-06",
              "2016-11-07",
              "2016-11-08",
              "2016-11-09",
              "2016-11-10",
              "2016-11-11",
              "2016-11-12",
              "2016-11-13",
              "2016-11-14",
              "2016-11-15",
              "2016-11-16",
              "2016-11-17",
              "2016-11-18",
              "2016-11-19",
              "2016-11-20",
              "2016-11-21",
              "2016-11-22",
              "2016-11-23",
              "2016-11-24",
              "2016-11-25",
              "2016-11-26",
              "2016-11-27",
              "2016-11-28",
              "2016-11-29",
              "2016-11-30",
              "2016-12-01",
              "2016-12-02",
              "2016-12-03",
              "2016-12-04",
              "2016-12-05",
              "2016-12-06",
              "2016-12-07",
              "2016-12-08",
              "2016-12-09",
              "2016-12-10",
              "2016-12-11",
              "2016-12-12",
              "2016-12-13",
              "2016-12-14",
              "2016-12-15",
              "2016-12-16",
              "2016-12-17",
              "2016-12-18",
              "2016-12-19",
              "2016-12-20",
              "2016-12-21",
              "2016-12-22",
              "2016-12-23",
              "2016-12-24",
              "2016-12-25",
              "2016-12-26",
              "2016-12-27",
              "2016-12-28",
              "2016-12-29",
              "2016-12-30",
              "2016-12-31",
              "2017-01-01",
              "2017-01-02",
              "2017-01-03",
              "2017-01-04",
              "2017-01-05",
              "2017-01-06",
              "2017-01-07",
              "2017-01-08",
              "2017-01-09",
              "2017-01-10",
              "2017-01-11",
              "2017-01-12",
              "2017-01-13",
              "2017-01-14",
              "2017-01-15",
              "2017-01-16",
              "2017-01-17",
              "2017-01-18",
              "2017-01-19",
              "2017-01-20",
              "2017-01-21",
              "2017-01-22",
              "2017-01-23",
              "2017-01-24",
              "2017-01-25",
              "2017-01-26",
              "2017-01-27",
              "2017-01-28",
              "2017-01-29",
              "2017-01-30",
              "2017-01-31",
              "2017-02-01",
              "2017-02-02",
              "2017-02-03",
              "2017-02-04",
              "2017-02-05",
              "2017-02-06",
              "2017-02-07",
              "2017-02-08",
              "2017-02-09",
              "2017-02-10",
              "2017-02-11",
              "2017-02-12",
              "2017-02-13",
              "2017-02-14",
              "2017-02-15",
              "2017-02-16",
              "2017-02-17",
              "2017-02-18",
              "2017-02-19",
              "2017-02-20",
              "2017-02-21",
              "2017-02-22",
              "2017-02-23",
              "2017-02-24",
              "2017-02-25",
              "2017-02-26",
              "2017-02-27",
              "2017-02-28",
              "2017-03-01",
              "2017-03-02",
              "2017-03-03",
              "2017-03-04",
              "2017-03-05",
              "2017-03-06",
              "2017-03-07",
              "2017-03-08",
              "2017-03-09",
              "2017-03-10",
              "2017-03-11",
              "2017-03-12",
              "2017-03-13",
              "2017-03-14",
              "2017-03-15",
              "2017-03-16",
              "2017-03-17",
              "2017-03-18",
              "2017-03-19",
              "2017-03-20",
              "2017-03-21",
              "2017-03-22",
              "2017-03-23",
              "2017-03-24",
              "2017-03-25",
              "2017-03-26",
              "2017-03-27",
              "2017-03-28",
              "2017-03-29",
              "2017-03-30",
              "2017-03-31",
              "2017-04-01",
              "2017-04-02",
              "2017-04-03",
              "2017-04-04",
              "2017-04-05",
              "2017-04-06",
              "2017-04-07",
              "2017-04-08",
              "2017-04-09",
              "2017-04-10",
              "2017-04-11",
              "2017-04-12",
              "2017-04-13",
              "2017-04-14",
              "2017-04-15",
              "2017-04-16",
              "2017-04-17",
              "2017-04-18",
              "2017-04-19",
              "2017-04-20",
              "2017-04-21",
              "2017-04-22",
              "2017-04-23",
              "2017-04-24",
              "2017-04-25",
              "2017-04-26",
              "2017-04-27",
              "2017-04-28",
              "2017-04-29",
              "2017-04-30",
              "2017-05-01",
              "2017-05-02",
              "2017-05-03",
              "2017-05-04",
              "2017-05-05",
              "2017-05-06",
              "2017-05-07",
              "2017-05-08",
              "2017-05-09",
              "2017-05-10",
              "2017-05-11",
              "2017-05-12",
              "2017-05-13",
              "2017-05-14",
              "2017-05-15",
              "2017-05-16",
              "2017-05-17",
              "2017-05-18",
              "2017-05-19",
              "2017-05-20",
              "2017-05-21",
              "2017-05-22",
              "2017-05-23",
              "2017-05-24",
              "2017-05-25",
              "2017-05-26",
              "2017-05-27",
              "2017-05-28",
              "2017-05-29",
              "2017-05-30",
              "2017-05-31",
              "2017-06-01",
              "2017-06-02",
              "2017-06-03",
              "2017-06-04",
              "2017-06-05",
              "2017-06-06",
              "2017-06-07",
              "2017-06-08",
              "2017-06-09",
              "2017-06-10",
              "2017-06-11",
              "2017-06-12",
              "2017-06-13",
              "2017-06-14",
              "2017-06-15",
              "2017-06-16",
              "2017-06-17",
              "2017-06-18",
              "2017-06-19",
              "2017-06-20",
              "2017-06-21",
              "2017-06-22",
              "2017-06-23",
              "2017-06-24",
              "2017-06-25",
              "2017-06-26",
              "2017-06-27",
              "2017-06-28",
              "2017-06-29",
              "2017-06-30",
              "2017-07-01",
              "2017-07-02",
              "2017-07-03",
              "2017-07-04",
              "2017-07-05",
              "2017-07-06",
              "2017-07-07",
              "2017-07-08",
              "2017-07-09",
              "2017-07-10",
              "2017-07-11",
              "2017-07-12",
              "2017-07-13",
              "2017-07-14",
              "2017-07-15",
              "2017-07-16",
              "2017-07-17",
              "2017-07-18",
              "2017-07-19",
              "2017-07-20",
              "2017-07-21",
              "2017-07-22",
              "2017-07-23",
              "2017-07-24",
              "2017-07-25",
              "2017-07-26",
              "2017-07-27",
              "2017-07-28",
              "2017-07-29",
              "2017-07-30",
              "2017-07-31",
              "2017-08-01",
              "2017-08-02",
              "2017-08-03",
              "2017-08-04",
              "2017-08-05",
              "2017-08-06",
              "2017-08-07",
              "2017-08-08",
              "2017-08-09",
              "2017-08-10",
              "2017-08-11",
              "2017-08-12",
              "2017-08-13",
              "2017-08-14",
              "2017-08-15",
              "2017-08-16",
              "2017-08-17",
              "2017-08-18",
              "2017-08-19",
              "2017-08-20",
              "2017-08-21",
              "2017-08-22",
              "2017-08-23",
              "2017-08-24",
              "2017-08-25",
              "2017-08-26",
              "2017-08-27",
              "2017-08-28",
              "2017-08-29",
              "2017-08-30",
              "2017-08-31",
              "2017-09-01",
              "2017-09-02",
              "2017-09-03",
              "2017-09-04",
              "2017-09-05",
              "2017-09-06",
              "2017-09-07",
              "2017-09-08",
              "2017-09-09",
              "2017-09-10",
              "2017-09-11",
              "2017-09-12",
              "2017-09-13",
              "2017-09-14",
              "2017-09-15",
              "2017-09-16",
              "2017-09-17",
              "2017-09-18",
              "2017-09-19",
              "2017-09-20",
              "2017-09-21",
              "2017-09-22",
              "2017-09-23",
              "2017-09-24",
              "2017-09-25",
              "2017-09-26",
              "2017-09-27",
              "2017-09-28",
              "2017-09-29",
              "2017-09-30",
              "2017-10-01",
              "2017-10-02",
              "2017-10-03",
              "2017-10-04",
              "2017-10-05",
              "2017-10-06",
              "2017-10-07",
              "2017-10-08",
              "2017-10-09",
              "2017-10-10",
              "2017-10-11",
              "2017-10-12",
              "2017-10-13",
              "2017-10-14",
              "2017-10-15",
              "2017-10-16",
              "2017-10-17",
              "2017-10-18",
              "2017-10-19",
              "2017-10-20",
              "2017-10-21",
              "2017-10-22",
              "2017-10-23",
              "2017-10-24",
              "2017-10-25",
              "2017-10-26",
              "2017-10-27",
              "2017-10-28",
              "2017-10-29",
              "2017-10-30",
              "2017-10-31",
              "2017-11-01",
              "2017-11-02",
              "2017-11-03",
              "2017-11-04",
              "2017-11-05",
              "2017-11-06",
              "2017-11-07",
              "2017-11-08",
              "2017-11-09",
              "2017-11-10",
              "2017-11-11",
              "2017-11-12",
              "2017-11-13",
              "2017-11-14",
              "2017-11-15",
              "2017-11-16",
              "2017-11-17",
              "2017-11-18",
              "2017-11-19",
              "2017-11-20",
              "2017-11-21",
              "2017-11-22",
              "2017-11-23",
              "2017-11-24",
              "2017-11-25",
              "2017-11-26",
              "2017-11-27",
              "2017-11-28",
              "2017-11-29",
              "2017-11-30",
              "2017-12-01",
              "2017-12-02",
              "2017-12-03",
              "2017-12-04",
              "2017-12-05",
              "2017-12-06",
              "2017-12-07",
              "2017-12-08",
              "2017-12-09",
              "2017-12-10",
              "2017-12-11",
              "2017-12-12",
              "2017-12-13",
              "2017-12-14",
              "2017-12-15",
              "2017-12-16",
              "2017-12-17",
              "2017-12-18",
              "2017-12-19",
              "2017-12-20",
              "2017-12-21",
              "2017-12-22",
              "2017-12-23",
              "2017-12-24",
              "2017-12-25",
              "2017-12-26",
              "2017-12-27",
              "2017-12-28",
              "2017-12-29",
              "2017-12-30",
              "2017-12-31",
              "2018-01-01",
              "2018-01-02",
              "2018-01-03",
              "2018-01-04",
              "2018-01-05",
              "2018-01-06",
              "2018-01-07",
              "2018-01-08",
              "2018-01-09",
              "2018-01-10",
              "2018-01-11",
              "2018-01-12",
              "2018-01-13",
              "2018-01-14",
              "2018-01-15",
              "2018-01-16",
              "2018-01-17",
              "2018-01-18",
              "2018-01-19",
              "2018-01-20",
              "2018-01-21",
              "2018-01-22",
              "2018-01-23",
              "2018-01-24",
              "2018-01-25",
              "2018-01-26",
              "2018-01-27",
              "2018-01-28",
              "2018-01-29",
              "2018-01-30",
              "2018-01-31",
              "2018-02-01",
              "2018-02-02",
              "2018-02-03",
              "2018-02-04",
              "2018-02-05",
              "2018-02-06",
              "2018-02-07",
              "2018-02-08",
              "2018-02-09",
              "2018-02-10",
              "2018-02-11",
              "2018-02-12",
              "2018-02-13",
              "2018-02-14",
              "2018-02-15",
              "2018-02-16",
              "2018-02-17",
              "2018-02-18",
              "2018-02-19",
              "2018-02-20",
              "2018-02-21",
              "2018-02-22",
              "2018-02-23",
              "2018-02-24",
              "2018-02-25",
              "2018-02-26",
              "2018-02-27",
              "2018-02-28",
              "2018-03-01",
              "2018-03-02",
              "2018-03-03",
              "2018-03-04",
              "2018-03-05",
              "2018-03-06",
              "2018-03-07",
              "2018-03-08",
              "2018-03-09",
              "2018-03-10",
              "2018-03-11",
              "2018-03-12",
              "2018-03-13",
              "2018-03-14",
              "2018-03-15",
              "2018-03-16",
              "2018-03-17",
              "2018-03-18",
              "2018-03-19",
              "2018-03-20",
              "2018-03-21",
              "2018-03-22",
              "2018-03-23",
              "2018-03-24",
              "2018-03-25",
              "2018-03-26",
              "2018-03-27",
              "2018-03-28",
              "2018-03-29",
              "2018-03-30",
              "2018-03-31",
              "2018-04-01",
              "2018-04-02",
              "2018-04-03",
              "2018-04-04",
              "2018-04-05",
              "2018-04-06",
              "2018-04-07",
              "2018-04-08",
              "2018-04-09",
              "2018-04-10",
              "2018-04-11",
              "2018-04-12",
              "2018-04-13",
              "2018-04-14",
              "2018-04-15",
              "2018-04-16",
              "2018-04-17",
              "2018-04-18",
              "2018-04-19",
              "2018-04-20",
              "2018-04-21",
              "2018-04-22",
              "2018-04-23",
              "2018-04-24",
              "2018-04-25",
              "2018-04-26",
              "2018-04-27",
              "2018-04-28",
              "2018-04-29",
              "2018-04-30",
              "2018-05-01",
              "2018-05-02",
              "2018-05-03",
              "2018-05-04",
              "2018-05-05",
              "2018-05-06",
              "2018-05-07",
              "2018-05-08",
              "2018-05-09",
              "2018-05-10",
              "2018-05-11",
              "2018-05-12",
              "2018-05-13",
              "2018-05-14",
              "2018-05-15",
              "2018-05-16",
              "2018-05-17",
              "2018-05-18",
              "2018-05-19",
              "2018-05-20",
              "2018-05-21",
              "2018-05-22",
              "2018-05-23",
              "2018-05-24",
              "2018-05-25",
              "2018-05-26",
              "2018-05-27",
              "2018-05-28",
              "2018-05-29",
              "2018-05-30",
              "2018-05-31",
              "2018-06-01",
              "2018-06-02",
              "2018-06-03",
              "2018-06-04",
              "2018-06-05",
              "2018-06-06",
              "2018-06-07",
              "2018-06-08",
              "2018-06-09",
              "2018-06-10",
              "2018-06-11",
              "2018-06-12",
              "2018-06-13",
              "2018-06-14",
              "2018-06-15",
              "2018-06-16",
              "2018-06-17",
              "2018-06-18",
              "2018-06-19",
              "2018-06-20",
              "2018-06-21",
              "2018-06-22",
              "2018-06-23",
              "2018-06-24",
              "2018-06-25",
              "2018-06-26",
              "2018-06-27",
              "2018-06-28",
              "2018-06-29",
              "2018-06-30",
              "2018-07-01",
              "2018-07-02",
              "2018-07-03",
              "2018-07-04",
              "2018-07-05",
              "2018-07-06",
              "2018-07-07",
              "2018-07-08",
              "2018-07-09",
              "2018-07-10",
              "2018-07-11",
              "2018-07-12",
              "2018-07-13",
              "2018-07-14",
              "2018-07-15",
              "2018-07-16",
              "2018-07-17",
              "2018-07-18",
              "2018-07-19",
              "2018-07-20",
              "2018-07-21",
              "2018-07-22",
              "2018-07-23",
              "2018-07-24",
              "2018-07-25",
              "2018-07-26",
              "2018-07-27",
              "2018-07-28",
              "2018-07-29",
              "2018-07-30",
              "2018-07-31",
              "2018-08-01",
              "2018-08-02",
              "2018-08-03",
              "2018-08-04",
              "2018-08-05",
              "2018-08-06",
              "2018-08-07",
              "2018-08-08",
              "2018-08-09",
              "2018-08-10",
              "2018-08-11",
              "2018-08-12",
              "2018-08-13",
              "2018-08-14",
              "2018-08-15",
              "2018-08-16",
              "2018-08-17",
              "2018-08-18",
              "2018-08-19",
              "2018-08-20",
              "2018-08-21",
              "2018-08-22",
              "2018-08-23",
              "2018-08-24",
              "2018-08-25",
              "2018-08-26",
              "2018-08-27",
              "2018-08-28",
              "2018-08-29",
              "2018-08-30",
              "2018-08-31",
              "2018-09-01",
              "2018-09-02",
              "2018-09-03",
              "2018-09-04",
              "2018-09-05",
              "2018-09-06",
              "2018-09-07",
              "2018-09-08",
              "2018-09-09",
              "2018-09-10",
              "2018-09-11",
              "2018-09-12",
              "2018-09-13",
              "2018-09-14",
              "2018-09-15",
              "2018-09-16",
              "2018-09-17",
              "2018-09-18",
              "2018-09-19",
              "2018-09-20",
              "2018-09-21",
              "2018-09-22",
              "2018-09-23",
              "2018-09-24",
              "2018-09-25",
              "2018-09-26",
              "2018-09-27",
              "2018-09-28",
              "2018-09-29",
              "2018-09-30",
              "2018-10-01",
              "2018-10-02",
              "2018-10-03",
              "2018-10-04",
              "2018-10-05",
              "2018-10-06",
              "2018-10-07",
              "2018-10-08",
              "2018-10-09",
              "2018-10-10",
              "2018-10-11",
              "2018-10-12",
              "2018-10-13",
              "2018-10-14",
              "2018-10-15",
              "2018-10-16",
              "2018-10-17",
              "2018-10-18",
              "2018-10-19",
              "2018-10-20",
              "2018-10-21",
              "2018-10-22",
              "2018-10-23",
              "2018-10-24",
              "2018-10-25",
              "2018-10-26",
              "2018-10-27",
              "2018-10-28",
              "2018-10-29",
              "2018-10-30",
              "2018-10-31",
              "2018-11-01",
              "2018-11-02",
              "2018-11-03",
              "2018-11-04",
              "2018-11-05",
              "2018-11-06",
              "2018-11-07",
              "2018-11-08",
              "2018-11-09",
              "2018-11-10",
              "2018-11-11",
              "2018-11-12",
              "2018-11-13",
              "2018-11-14",
              "2018-11-15",
              "2018-11-16",
              "2018-11-17",
              "2018-11-18",
              "2018-11-19",
              "2018-11-20",
              "2018-11-21",
              "2018-11-22",
              "2018-11-23",
              "2018-11-24",
              "2018-11-25",
              "2018-11-26",
              "2018-11-27",
              "2018-11-28",
              "2018-11-29",
              "2018-11-30",
              "2018-12-01",
              "2018-12-02",
              "2018-12-03",
              "2018-12-04",
              "2018-12-05",
              "2018-12-06",
              "2018-12-07",
              "2018-12-08",
              "2018-12-09",
              "2018-12-10",
              "2018-12-11",
              "2018-12-12",
              "2018-12-13",
              "2018-12-14",
              "2018-12-15",
              "2018-12-16",
              "2018-12-17",
              "2018-12-18",
              "2018-12-19",
              "2018-12-20",
              "2018-12-21",
              "2018-12-22",
              "2018-12-23",
              "2018-12-24",
              "2018-12-25",
              "2018-12-26",
              "2018-12-27",
              "2018-12-28",
              "2018-12-29",
              "2018-12-30",
              "2018-12-31",
              "2019-01-01",
              "2019-01-02",
              "2019-01-03",
              "2019-01-04",
              "2019-01-05",
              "2019-01-06",
              "2019-01-07",
              "2019-01-08",
              "2019-01-09",
              "2019-01-10",
              "2019-01-11",
              "2019-01-12",
              "2019-01-13",
              "2019-01-14",
              "2019-01-15",
              "2019-01-16",
              "2019-01-17",
              "2019-01-18",
              "2019-01-19",
              "2019-01-20",
              "2019-01-21",
              "2019-01-22",
              "2019-01-23",
              "2019-01-24",
              "2019-01-25",
              "2019-01-26",
              "2019-01-27",
              "2019-01-28",
              "2019-01-29",
              "2019-01-30",
              "2019-01-31",
              "2019-02-01",
              "2019-02-02",
              "2019-02-03",
              "2019-02-04",
              "2019-02-05",
              "2019-02-06",
              "2019-02-07",
              "2019-02-08",
              "2019-02-09",
              "2019-02-10",
              "2019-02-11",
              "2019-02-12",
              "2019-02-13",
              "2019-02-14",
              "2019-02-15",
              "2019-02-16",
              "2019-02-17",
              "2019-02-18",
              "2019-02-19",
              "2019-02-20",
              "2019-02-21",
              "2019-02-22",
              "2019-02-23",
              "2019-02-24",
              "2019-02-25",
              "2019-02-26",
              "2019-02-27",
              "2019-02-28",
              "2019-03-01",
              "2019-03-02",
              "2019-03-03",
              "2019-03-04",
              "2019-03-05",
              "2019-03-06",
              "2019-03-07",
              "2019-03-08",
              "2019-03-09",
              "2019-03-10",
              "2019-03-11",
              "2019-03-12",
              "2019-03-13",
              "2019-03-14",
              "2019-03-15",
              "2019-03-16",
              "2019-03-17",
              "2019-03-18",
              "2019-03-19",
              "2019-03-20",
              "2019-03-21",
              "2019-03-22",
              "2019-03-23",
              "2019-03-24",
              "2019-03-25",
              "2019-03-26",
              "2019-03-27",
              "2019-03-28",
              "2019-03-29",
              "2019-03-30",
              "2019-03-31",
              "2019-04-01",
              "2019-04-02",
              "2019-04-03",
              "2019-04-04",
              "2019-04-05",
              "2019-04-06",
              "2019-04-07",
              "2019-04-08",
              "2019-04-09",
              "2019-04-10",
              "2019-04-11",
              "2019-04-12",
              "2019-04-13",
              "2019-04-14",
              "2019-04-15",
              "2019-04-16",
              "2019-04-17",
              "2019-04-18",
              "2019-04-19",
              "2019-04-20",
              "2019-04-21",
              "2019-04-22",
              "2019-04-23",
              "2019-04-24",
              "2019-04-25",
              "2019-04-26",
              "2019-04-27",
              "2019-04-28",
              "2019-04-29",
              "2019-04-30",
              "2019-05-01",
              "2019-05-02",
              "2019-05-03",
              "2019-05-04",
              "2019-05-05",
              "2019-05-06",
              "2019-05-07",
              "2019-05-08",
              "2019-05-09",
              "2019-05-10",
              "2019-05-11",
              "2019-05-12",
              "2019-05-13",
              "2019-05-14",
              "2019-05-15",
              "2019-05-16",
              "2019-05-17",
              "2019-05-18",
              "2019-05-19",
              "2019-05-20",
              "2019-05-21",
              "2019-05-22",
              "2019-05-23",
              "2019-05-24",
              "2019-05-25",
              "2019-05-26",
              "2019-05-27",
              "2019-05-28",
              "2019-05-29",
              "2019-05-30",
              "2019-05-31",
              "2019-06-01",
              "2019-06-02",
              "2019-06-03",
              "2019-06-04",
              "2019-06-05",
              "2019-06-06",
              "2019-06-07",
              "2019-06-08",
              "2019-06-09",
              "2019-06-10",
              "2019-06-11",
              "2019-06-12",
              "2019-06-13",
              "2019-06-14",
              "2019-06-15",
              "2019-06-16",
              "2019-06-17",
              "2019-06-18",
              "2019-06-19",
              "2019-06-20",
              "2019-06-21",
              "2019-06-22",
              "2019-06-23",
              "2019-06-24",
              "2019-06-25",
              "2019-06-26",
              "2019-06-27",
              "2019-06-28",
              "2019-06-29",
              "2019-06-30",
              "2019-07-01",
              "2019-07-02",
              "2019-07-03",
              "2019-07-04",
              "2019-07-05",
              "2019-07-06",
              "2019-07-07",
              "2019-07-08",
              "2019-07-09",
              "2019-07-10",
              "2019-07-11",
              "2019-07-12",
              "2019-07-13",
              "2019-07-14",
              "2019-07-15",
              "2019-07-16",
              "2019-07-17",
              "2019-07-18",
              "2019-07-19",
              "2019-07-20",
              "2019-07-21",
              "2019-07-22",
              "2019-07-23",
              "2019-07-24",
              "2019-07-25",
              "2019-07-26",
              "2019-07-27",
              "2019-07-28",
              "2019-07-29",
              "2019-07-30",
              "2019-07-31",
              "2019-08-01",
              "2019-08-02",
              "2019-08-03",
              "2019-08-04",
              "2019-08-05",
              "2019-08-06",
              "2019-08-07",
              "2019-08-08",
              "2019-08-09",
              "2019-08-10",
              "2019-08-11",
              "2019-08-12",
              "2019-08-13",
              "2019-08-14",
              "2019-08-15",
              "2019-08-16",
              "2019-08-17",
              "2019-08-18",
              "2019-08-19",
              "2019-08-20",
              "2019-08-21",
              "2019-08-22",
              "2019-08-23",
              "2019-08-24",
              "2019-08-25",
              "2019-08-26",
              "2019-08-27",
              "2019-08-28",
              "2019-08-29",
              "2019-08-30",
              "2019-08-31",
              "2019-09-01",
              "2019-09-02",
              "2019-09-03",
              "2019-09-04",
              "2019-09-05",
              "2019-09-06",
              "2019-09-07",
              "2019-09-08",
              "2019-09-09",
              "2019-09-10",
              "2019-09-11",
              "2019-09-12",
              "2019-09-13",
              "2019-09-14",
              "2019-09-15",
              "2019-09-16",
              "2019-09-17",
              "2019-09-18",
              "2019-09-19",
              "2019-09-20",
              "2019-09-21",
              "2019-09-22",
              "2019-09-23",
              "2019-09-24",
              "2019-09-25",
              "2019-09-26",
              "2019-09-27",
              "2019-09-28",
              "2019-09-29",
              "2019-09-30",
              "2019-10-01",
              "2019-10-02",
              "2019-10-03",
              "2019-10-04",
              "2019-10-05",
              "2019-10-06",
              "2019-10-07",
              "2019-10-08",
              "2019-10-09",
              "2019-10-10",
              "2019-10-11",
              "2019-10-12",
              "2019-10-13",
              "2019-10-14",
              "2019-10-15",
              "2019-10-16",
              "2019-10-17",
              "2019-10-18",
              "2019-10-19",
              "2019-10-20",
              "2019-10-21",
              "2019-10-22",
              "2019-10-23",
              "2019-10-24",
              "2019-10-25",
              "2019-10-26",
              "2019-10-27",
              "2019-10-28",
              "2019-10-29",
              "2019-10-30",
              "2019-10-31",
              "2019-11-01",
              "2019-11-02",
              "2019-11-03",
              "2019-11-04",
              "2019-11-05",
              "2019-11-06",
              "2019-11-07",
              "2019-11-08",
              "2019-11-09",
              "2019-11-10",
              "2019-11-11",
              "2019-11-12",
              "2019-11-13",
              "2019-11-14",
              "2019-11-15",
              "2019-11-16",
              "2019-11-17",
              "2019-11-18",
              "2019-11-19",
              "2019-11-20",
              "2019-11-21",
              "2019-11-22",
              "2019-11-23",
              "2019-11-24",
              "2019-11-25",
              "2019-11-26",
              "2019-11-27",
              "2019-11-28",
              "2019-11-29",
              "2019-11-30",
              "2019-12-01",
              "2019-12-02",
              "2019-12-03",
              "2019-12-04",
              "2019-12-05",
              "2019-12-06",
              "2019-12-07",
              "2019-12-08",
              "2019-12-09",
              "2019-12-10",
              "2019-12-11",
              "2019-12-12",
              "2019-12-13",
              "2019-12-14",
              "2019-12-15",
              "2019-12-16",
              "2019-12-17",
              "2019-12-18",
              "2019-12-19",
              "2019-12-20",
              "2019-12-21",
              "2019-12-22",
              "2019-12-23",
              "2019-12-24",
              "2019-12-25",
              "2019-12-26",
              "2019-12-27",
              "2019-12-28",
              "2019-12-29",
              "2019-12-30",
              "2019-12-31"
            ]
          }
        ],
        series: [
          {
            name: "超载",
            type: "line",
            stack: "总量",
            areaStyle: {
              normal: {}
            },
            data: [
              0,
              67,
              54,
              53,
              33,
              40,
              46,
              25,
              22,
              21,
              24,
              26,
              30,
              24,
              28,
              24,
              30,
              34,
              37,
              27,
              26,
              25,
              39,
              29,
              20,
              34,
              29,
              10,
              15,
              9,
              6,
              4,
              5,
              2,
              1,
              1,
              6,
              13,
              17,
              12,
              10,
              13,
              16,
              6,
              6,
              5,
              10,
              17,
              9,
              9,
              5,
              0,
              2,
              4,
              3,
              3,
              13,
              9,
              1,
              2,
              1,
              2,
              4,
              3,
              3,
              4,
              3,
              0,
              5,
              0,
              0,
              0,
              1,
              0,
              1,
              1,
              0,
              0,
              7,
              0,
              2,
              6,
              5,
              4,
              1,
              2,
              3,
              1,
              2,
              0,
              1,
              4,
              2,
              1,
              3,
              1,
              1,
              3,
              4,
              3,
              4,
              1,
              0,
              0,
              3,
              2,
              2,
              4,
              2,
              0,
              1,
              0,
              2,
              1,
              0,
              0,
              3,
              0,
              2,
              0,
              5,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              2,
              0,
              1,
              1,
              1,
              3,
              5,
              0,
              3,
              1,
              1,
              0,
              2,
              0,
              0,
              1,
              1,
              1,
              3,
              2,
              1,
              4,
              0,
              4,
              4,
              1,
              2,
              2,
              3,
              10,
              2,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              1,
              1,
              2,
              5,
              7,
              2,
              2,
              1,
              1,
              0,
              0,
              2,
              8,
              3,
              2,
              1,
              2,
              0,
              4,
              4,
              1,
              1,
              2,
              3,
              1,
              2,
              2,
              3,
              4,
              1,
              2,
              4,
              1,
              2,
              2,
              2,
              1,
              1,
              2,
              0,
              4,
              2,
              1,
              2,
              3,
              1,
              1,
              0,
              2,
              1,
              2,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              4,
              0,
              1,
              0,
              0,
              3,
              2,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              9,
              0,
              1,
              0,
              1,
              3,
              0,
              1,
              3,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              1,
              3,
              0,
              0,
              1,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              7,
              5,
              5,
              8,
              6,
              5,
              1,
              4,
              0,
              0,
              0,
              2,
              0,
              4,
              6,
              6,
              7,
              7,
              6,
              2,
              9,
              3,
              3,
              6,
              5,
              5,
              8,
              5,
              11,
              36,
              45,
              36,
              43,
              19,
              5,
              4,
              3,
              4,
              0,
              2,
              3,
              4,
              2,
              3,
              1,
              0,
              3,
              3,
              2,
              2,
              1,
              1,
              2,
              0,
              1,
              2,
              2,
              0,
              1,
              2,
              2,
              2,
              2,
              4,
              1,
              4,
              2,
              1,
              3,
              3,
              2,
              5,
              1,
              0,
              0,
              0,
              3,
              0,
              2,
              2,
              0,
              0,
              0,
              0,
              1,
              3,
              1,
              3,
              1,
              0,
              1,
              1,
              1,
              5,
              1,
              1,
              0,
              1,
              2,
              2,
              0,
              0,
              1,
              0,
              0,
              1,
              2,
              1,
              2,
              1,
              0,
              0,
              2,
              1,
              7,
              1,
              3,
              2,
              2,
              0,
              2,
              1,
              5,
              2,
              3,
              3,
              4,
              3,
              2,
              1,
              0,
              0,
              1,
              1,
              2,
              2,
              2,
              0,
              3,
              2,
              0,
              2,
              1,
              1,
              0,
              1,
              2,
              1,
              0,
              2,
              1,
              2,
              0,
              1,
              2,
              3,
              0,
              0,
              0,
              2,
              1,
              2,
              3,
              1,
              1,
              0,
              1,
              0,
              2,
              3,
              4,
              3,
              1,
              2,
              0,
              0,
              1,
              0,
              0,
              3,
              1,
              0,
              0,
              0,
              2,
              3,
              1,
              4,
              4,
              1,
              2,
              1,
              0,
              0,
              2,
              5,
              2,
              3,
              0,
              0,
              1,
              0,
              3,
              5,
              2,
              0,
              1,
              2,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              4,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0
            ]
          },
          {
            name: "超速",
            type: "line",
            stack: "总量",
            areaStyle: {
              normal: {}
            },
            data: [
              0,
              21,
              16,
              21,
              31,
              33,
              42,
              24,
              25,
              25,
              36,
              17,
              49,
              49,
              22,
              30,
              14,
              29,
              25,
              29,
              29,
              33,
              51,
              25,
              44,
              36,
              30,
              13,
              23,
              11,
              18,
              24,
              23,
              10,
              21,
              15,
              23,
              31,
              20,
              33,
              43,
              23,
              20,
              30,
              15,
              30,
              6,
              24,
              5,
              1,
              1,
              1,
              3,
              2,
              0,
              1,
              2,
              0,
              0,
              0,
              0,
              3,
              1,
              0,
              1,
              1,
              1,
              0,
              0,
              1,
              0,
              0,
              3,
              1,
              2,
              2,
              0,
              1,
              0,
              1,
              1,
              0,
              0,
              1,
              2,
              0,
              1,
              0,
              1,
              0,
              0,
              3,
              1,
              1,
              3,
              0,
              1,
              2,
              1,
              0,
              1,
              0,
              0,
              2,
              1,
              0,
              0,
              1,
              1,
              1,
              2,
              0,
              1,
              0,
              0,
              2,
              3,
              1,
              1,
              1,
              4,
              1,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              3,
              3,
              1,
              1,
              0,
              2,
              1,
              2,
              1,
              0,
              2,
              0,
              1,
              1,
              1,
              1,
              0,
              2,
              0,
              0,
              0,
              5,
              5,
              3,
              6,
              13,
              9,
              5,
              7,
              0,
              3,
              8,
              5,
              10,
              7,
              9,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              0,
              4,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              1,
              2,
              10,
              4,
              12,
              14,
              7,
              10,
              10,
              11,
              9,
              11,
              16,
              8,
              7,
              7,
              5,
              9,
              6,
              10,
              11,
              7,
              9,
              12,
              6,
              9,
              4,
              7,
              4,
              8,
              10,
              8,
              6,
              11,
              5,
              7,
              9,
              6,
              8,
              3,
              13,
              4,
              2,
              8,
              2,
              4,
              3,
              5,
              3,
              4,
              3,
              4,
              2,
              1,
              1,
              6,
              3,
              2,
              0,
              1,
              3,
              2,
              2,
              1,
              1,
              1,
              1,
              1,
              3,
              9,
              6,
              7,
              3,
              6,
              7,
              1,
              1,
              6,
              6,
              3,
              6,
              3,
              5,
              3,
              3,
              5,
              7,
              3,
              2,
              4,
              6,
              2,
              7,
              2,
              2,
              5,
              0,
              6,
              1,
              3,
              3,
              2,
              1,
              10,
              2,
              2,
              5,
              2,
              5,
              3,
              3,
              0,
              4,
              1,
              5,
              4,
              2,
              2,
              4,
              1,
              2,
              2,
              3,
              4,
              4,
              8,
              4,
              1,
              5,
              0,
              4,
              4,
              2,
              5,
              1,
              8,
              5,
              1,
              3,
              4,
              1,
              4,
              1,
              3,
              4,
              4,
              0,
              4,
              3,
              5,
              2,
              2,
              4,
              6,
              1,
              5,
              2,
              7,
              6,
              2,
              1,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              1,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              2,
              1,
              1,
              1,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              2,
              2,
              0,
              0,
              0,
              1,
              0,
              2,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              1,
              0,
              0,
              2,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              4,
              0,
              3,
              0,
              1,
              1,
              1,
              3,
              0,
              1,
              0,
              1,
              2,
              0,
              1,
              1,
              1,
              0,
              3,
              2,
              1,
              0,
              1,
              1,
              3,
              1,
              1,
              0,
              2,
              0,
              2,
              0,
              0,
              1,
              0,
              0,
              3,
              1,
              0,
              2,
              0,
              1,
              11,
              0,
              6,
              0,
              2,
              4,
              2,
              1,
              2,
              0,
              3,
              0,
              1,
              1,
              2,
              3,
              0,
              0,
              0,
              0,
              2,
              1,
              0,
              3,
              15,
              0,
              0,
              3,
              0,
              0,
              2,
              1,
              0,
              15,
              30,
              12,
              14,
              11,
              29,
              12,
              4,
              14,
              4,
              20,
              17,
              3,
              16,
              36,
              22,
              20,
              20,
              13,
              34,
              30,
              8,
              13,
              6,
              8,
              8,
              3,
              2,
              1,
              8,
              3,
              3,
              2,
              1,
              0,
              2,
              2,
              0,
              5,
              2,
              3,
              3,
              3,
              7,
              3,
              2,
              1,
              4,
              2,
              2,
              1,
              3,
              0,
              1,
              1,
              1,
              0,
              0,
              2,
              0,
              1,
              0,
              1,
              1,
              2,
              1,
              1,
              2,
              2,
              0,
              1,
              1,
              0,
              0,
              2,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              1,
              2,
              0,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              1,
              2,
              2,
              1,
              1,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              2,
              0,
              2,
              1,
              0,
              1,
              1,
              1,
              0,
              1,
              1,
              2,
              0,
              2,
              2,
              1,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              1,
              1,
              0,
              5,
              0,
              0,
              1,
              1,
              0,
              0,
              2,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              3,
              0,
              0,
              1,
              0,
              0,
              1,
              2,
              1,
              0,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              1,
              1,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              0,
              1,
              0,
              0,
              6,
              3,
              0,
              4,
              2,
              0,
              0,
              1,
              0,
              2,
              0,
              0,
              3,
              0,
              1,
              1,
              3,
              0,
              1,
              1,
              2,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              1,
              3,
              1,
              1,
              0,
              0,
              3,
              2,
              0,
              1,
              0,
              3,
              2,
              2,
              2,
              1,
              0,
              1,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              0,
              2,
              5,
              3,
              5,
              1,
              0,
              1,
              0,
              0,
              1,
              2,
              2,
              1,
              3,
              1,
              1,
              0,
              3,
              2,
              0,
              1,
              2,
              1,
              1,
              1,
              1,
              1,
              0,
              1,
              0,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              3,
              1,
              2,
              0,
              0,
              1,
              1,
              1,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              1,
              0,
              0,
              6,
              0,
              0,
              2,
              0,
              0,
              2,
              0,
              1,
              2,
              0,
              0,
              0,
              1,
              0,
              2,
              0,
              0,
              1,
              1,
              0,
              0,
              2,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              3,
              2,
              0,
              0,
              0,
              2,
              1,
              1,
              0,
              1,
              0,
              0,
              1,
              2,
              0,
              2,
              1,
              0,
              0,
              0,
              1,
              1,
              3,
              0,
              0,
              1,
              1,
              1,
              1,
              0,
              1,
              1,
              1,
              1,
              2,
              1,
              1,
              2,
              0,
              2,
              2,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              2,
              0,
              0,
              2,
              1,
              0,
              2,
              1,
              0,
              3,
              0,
              2,
              0,
              1,
              3,
              1,
              0,
              0,
              0,
              0,
              2,
              0,
              3,
              0,
              0,
              4,
              3,
              0,
              5,
              1,
              2,
              2,
              2,
              1,
              0,
              0,
              1,
              2,
              0,
              2,
              0,
              2,
              1,
              1,
              1,
              0,
              3,
              2,
              2,
              1,
              1,
              0,
              4,
              0,
              3,
              4,
              2,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              1,
              1,
              0,
              3,
              1,
              3,
              0,
              0,
              3,
              0,
              4
            ]
          },
          {
            name: "未备案",
            type: "line",
            stack: "总量",
            areaStyle: {
              normal: {}
            },
            data: [
              0,
              445,
              443,
              455,
              463,
              341,
              285,
              304,
              323,
              360,
              306,
              333,
              263,
              275,
              323,
              296,
              299,
              271,
              243,
              216,
              207,
              256,
              246,
              211,
              217,
              190,
              176,
              134,
              137,
              142,
              134,
              128,
              124,
              85,
              83,
              104,
              117,
              197,
              210,
              216,
              187,
              179,
              222,
              239,
              213,
              117,
              156,
              177,
              202,
              208,
              197,
              228,
              220,
              210,
              200,
              177,
              178,
              211,
              196,
              177,
              176,
              109,
              122,
              112,
              147,
              131,
              99,
              105,
              196,
              208,
              215,
              130,
              40,
              32,
              52,
              35,
              37,
              42,
              29,
              18,
              23,
              27,
              21,
              30,
              17,
              19,
              18,
              27,
              31,
              33,
              12,
              18,
              28,
              39,
              37,
              31,
              14,
              14,
              26,
              25,
              29,
              14,
              6,
              12,
              11,
              11,
              21,
              16,
              16,
              0,
              17,
              6,
              14,
              10,
              0,
              0,
              24,
              0,
              19,
              0,
              6,
              17,
              18,
              20,
              12,
              3,
              10,
              11,
              0,
              4,
              0,
              3,
              7,
              3,
              2,
              0,
              0,
              2,
              3,
              6,
              2,
              0,
              8,
              0,
              0,
              2,
              1,
              1,
              1,
              15,
              0,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              1,
              0,
              2,
              0,
              0,
              0,
              0,
              2,
              3,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              2,
              1,
              2,
              1,
              0,
              0,
              2,
              3,
              0,
              2,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              0,
              3,
              0,
              0,
              2,
              1,
              2,
              2,
              2,
              2,
              2,
              5,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              2,
              4,
              0,
              0,
              0,
              0,
              5,
              1,
              0,
              4,
              3,
              1,
              1,
              1,
              1,
              1,
              1,
              0,
              3,
              3,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              3,
              4,
              3,
              1,
              0,
              0,
              0,
              1,
              1,
              0,
              1,
              1,
              3,
              1,
              0,
              1,
              0,
              1,
              0,
              3,
              5,
              5,
              1,
              2,
              2,
              2,
              2,
              1,
              2,
              0,
              2,
              1,
              1,
              0,
              1,
              2,
              2,
              4,
              2,
              3,
              3,
              2,
              1,
              4,
              1,
              0,
              0,
              1,
              0,
              1,
              1,
              2,
              0,
              0,
              2,
              2,
              3,
              0,
              2,
              3,
              1,
              1,
              1,
              2,
              1,
              0,
              2,
              0,
              1,
              1,
              1,
              2,
              2,
              1,
              0,
              2,
              4,
              1,
              0,
              0,
              3,
              2,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              40,
              2,
              2,
              1,
              2,
              2,
              2,
              2,
              3,
              6,
              0,
              1,
              1,
              2,
              3,
              4,
              2,
              4,
              2,
              4,
              2,
              2,
              0,
              3,
              5,
              2,
              1,
              0,
              0,
              3,
              3,
              0,
              1,
              3,
              3,
              1,
              0,
              0,
              3,
              1,
              0,
              1,
              2,
              1,
              3,
              0,
              0,
              1,
              0,
              5,
              6,
              5,
              2,
              0,
              2,
              2,
              1,
              2,
              5,
              2,
              1,
              3,
              2,
              2,
              1,
              1,
              3,
              6,
              3,
              2,
              0,
              0,
              1,
              1,
              2,
              1,
              4,
              0,
              1,
              2,
              3,
              0,
              1,
              1,
              4,
              2,
              3,
              2,
              4,
              0,
              3,
              4,
              0,
              3,
              1,
              3,
              2,
              2,
              4,
              3,
              3,
              0,
              2,
              0,
              2,
              1,
              4,
              2,
              2,
              1,
              0,
              0,
              2,
              1,
              2,
              2,
              3,
              1,
              1,
              2,
              5,
              2,
              4,
              0,
              2,
              1,
              1,
              1,
              0,
              1,
              2,
              1,
              4,
              2,
              0,
              3,
              2,
              3,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              3,
              1,
              2,
              2,
              5,
              3,
              2,
              1,
              3,
              1,
              2,
              0,
              4,
              3,
              1,
              2,
              1,
              1,
              4,
              2,
              1,
              2,
              0,
              0,
              1,
              2,
              3,
              1,
              1,
              2,
              1,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              1,
              1,
              1,
              3,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              0,
              1,
              0,
              0,
              1,
              1,
              1,
              0,
              1,
              2,
              1,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              1,
              0
            ]
          },
          {
            name: "超经营范围",
            type: "line",
            stack: "总量",
            areaStyle: {
              normal: {}
            },
            data: [
              0,
              25,
              31,
              22,
              60,
              27,
              29,
              48,
              44,
              51,
              17,
              22,
              17,
              3,
              36,
              34,
              44,
              50,
              24,
              10,
              12,
              28,
              18,
              6,
              13,
              15,
              2,
              3,
              1,
              2,
              13,
              11,
              14,
              3,
              1,
              24,
              6,
              25,
              42,
              30,
              37,
              29,
              23,
              32,
              25,
              23,
              15,
              36,
              34,
              38,
              42,
              38,
              51,
              60,
              39,
              36,
              35,
              31,
              61,
              66,
              33,
              24,
              21,
              31,
              18,
              22,
              18,
              19,
              34,
              22,
              15,
              0,
              21,
              10,
              17,
              11,
              8,
              28,
              29,
              4,
              9,
              3,
              24,
              18,
              23,
              35,
              25,
              18,
              39,
              32,
              10,
              8,
              26,
              29,
              38,
              30,
              29,
              21,
              37,
              22,
              21,
              27,
              24,
              18,
              19,
              19,
              12,
              14,
              15,
              0,
              15,
              16,
              13,
              2,
              10,
              23,
              10,
              20,
              10,
              20,
              10,
              8,
              14,
              11,
              17,
              13,
              10,
              6,
              10,
              11,
              0,
              5,
              7,
              6,
              7,
              7,
              1,
              6,
              27,
              14,
              15,
              3,
              18,
              0,
              27,
              7,
              3,
              2,
              9,
              4,
              2,
              7,
              7,
              0,
              0,
              2,
              9,
              10,
              4,
              7,
              16,
              6,
              2,
              2,
              15,
              10,
              10,
              17,
              9,
              0,
              26,
              8,
              8,
              14,
              5,
              7,
              7,
              3,
              6,
              0,
              0,
              0,
              0,
              3,
              3,
              3,
              8,
              7,
              0,
              5,
              7,
              4,
              9,
              6,
              8,
              3,
              6,
              20,
              17,
              29,
              19,
              13,
              14,
              3,
              14,
              17,
              10,
              7,
              7,
              14,
              4,
              12,
              9,
              6,
              6,
              6,
              0,
              11,
              9,
              7,
              4,
              2,
              4,
              0,
              2,
              3,
              8,
              7,
              6,
              5,
              0,
              8,
              4,
              14,
              6,
              10,
              6,
              6,
              11,
              0,
              10,
              4,
              3,
              19,
              8,
              9,
              4,
              6,
              5,
              9,
              9,
              9,
              4,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              6,
              15,
              8,
              11,
              12,
              19,
              2,
              19,
              2,
              4,
              5,
              10,
              5,
              5,
              6,
              4,
              1,
              3,
              6,
              2,
              3,
              2,
              0,
              2,
              5,
              7,
              0,
              6,
              23,
              14,
              6,
              13,
              1,
              9,
              4,
              7,
              3,
              4,
              8,
              4,
              5,
              2,
              3,
              1,
              3,
              2,
              0,
              0,
              0,
              1,
              3,
              5,
              9,
              8,
              9,
              4,
              7,
              6,
              10,
              2,
              0,
              2,
              0,
              2,
              9,
              1,
              0,
              1,
              2,
              2,
              3,
              1,
              0,
              0,
              2,
              0,
              11,
              5,
              2,
              3,
              7,
              13,
              9,
              8,
              4,
              1,
              3,
              7,
              5,
              2,
              9,
              0,
              3,
              4,
              7,
              4,
              5,
              8,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              7,
              5,
              13,
              3,
              3,
              2,
              9,
              6,
              2,
              0,
              0,
              1,
              1,
              4,
              8,
              11,
              7,
              4,
              11,
              0,
              12,
              3,
              6,
              10,
              6,
              7,
              3,
              3,
              8,
              4,
              5,
              2,
              3,
              0,
              3,
              1,
              3,
              8,
              3,
              0,
              3,
              0,
              6,
              0,
              4,
              4,
              2,
              9,
              3,
              5,
              9,
              2,
              3,
              6,
              7,
              4,
              5,
              6,
              2,
              1,
              2,
              9,
              6,
              8,
              4,
              10,
              19,
              12,
              5,
              1,
              11,
              7,
              0,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              4,
              2,
              0,
              2,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              1,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              7,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0
            ]
          },
          {
            name: "无BDS",
            type: "line",
            stack: "总量",
            areaStyle: {
              normal: {}
            },
            data: [
              0,
              205,
              250,
              191,
              209,
              180,
              143,
              146,
              181,
              175,
              150,
              158,
              145,
              102,
              115,
              127,
              107,
              151,
              112,
              157,
              118,
              113,
              92,
              81,
              92,
              77,
              59,
              21,
              16,
              21,
              25,
              12,
              28,
              11,
              13,
              18,
              51,
              132,
              137,
              138,
              118,
              170,
              150,
              161,
              148,
              104,
              147,
              182,
              165,
              129,
              179,
              149,
              108,
              127,
              122,
              91,
              130,
              110,
              117,
              89,
              94,
              57,
              56,
              46,
              62,
              57,
              30,
              35,
              74,
              163,
              139,
              145,
              120,
              102,
              144,
              94,
              80,
              81,
              92,
              74,
              95,
              84,
              78,
              84,
              101,
              60,
              71,
              88,
              104,
              73,
              66,
              62,
              65,
              69,
              75,
              76,
              52,
              42,
              52,
              53,
              36,
              33,
              29,
              30,
              34,
              35,
              33,
              25,
              27,
              30,
              24,
              31,
              31,
              31,
              27,
              37,
              38,
              38,
              30,
              25,
              31,
              30,
              29,
              22,
              23,
              19,
              20,
              25,
              30,
              35,
              32,
              27,
              18,
              32,
              26,
              21,
              23,
              26,
              20,
              17,
              32,
              25,
              24,
              32,
              22,
              22,
              29,
              8,
              27,
              13,
              15,
              36,
              19,
              15,
              15,
              14,
              20,
              10,
              28,
              36,
              26,
              26,
              23,
              16,
              28,
              16,
              22,
              20,
              26,
              26,
              22,
              30,
              33,
              32,
              17,
              16,
              16,
              15,
              6,
              4,
              2,
              2,
              3,
              7,
              7,
              10,
              29,
              34,
              33,
              26,
              17,
              17,
              21,
              17,
              24,
              25,
              15,
              20,
              12,
              5,
              10,
              10,
              9,
              4,
              3,
              2,
              4,
              0,
              3,
              3,
              3,
              7,
              5,
              1,
              1,
              3,
              1,
              2,
              1,
              0,
              3,
              3,
              2,
              2,
              2,
              2,
              5,
              6,
              3,
              4,
              4,
              9,
              7,
              6,
              8,
              5,
              5,
              3,
              4,
              4,
              3,
              7,
              2,
              2,
              1,
              0,
              1,
              1,
              3,
              1,
              2,
              1,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              3,
              4,
              5,
              4,
              5,
              0,
              2,
              5,
              8,
              7,
              0,
              1,
              0,
              0,
              1,
              2,
              2,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              2,
              0,
              0,
              2,
              2,
              1,
              2,
              0,
              3,
              1,
              0,
              2,
              3,
              2,
              3,
              2,
              4,
              3,
              2,
              4,
              3,
              2,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              3,
              1,
              1,
              0,
              3,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              2,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              2,
              1,
              0,
              0,
              1,
              4,
              2,
              1,
              1,
              3,
              1,
              1,
              0,
              0,
              2,
              1,
              1,
              2,
              2,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              1,
              0,
              1,
              0,
              1,
              1,
              0,
              2,
              1,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              2,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              4,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              1,
              1,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              8,
              3,
              0,
              2,
              4,
              1,
              4,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              3,
              3,
              3,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              4,
              4,
              4,
              0,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              1,
              1,
              0,
              0,
              0,
              0,
              1,
              1,
              4,
              2,
              0,
              0,
              1,
              2,
              2,
              2,
              2,
              1,
              2,
              1,
              1,
              1,
              1,
              3,
              2,
              4,
              3,
              4,
              3,
              2,
              2,
              3,
              4,
              1,
              5,
              0,
              2,
              0,
              2,
              4,
              0,
              0,
              1,
              5,
              3,
              4,
              4,
              3,
              2,
              3,
              3,
              3,
              4,
              2,
              2,
              1,
              3,
              2,
              8,
              4,
              5,
              2,
              2,
              3,
              3,
              1,
              1,
              2,
              6,
              3,
              4,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              0,
              7,
              2,
              3,
              2,
              3,
              3,
              3,
              2,
              ,
              6,
              1,
              2,
              3,
              3,
              4,
              4,
              3,
              6,
              2,
              4,
              3,
              2,
              1,
              6,
              7,
              3,
              2,
              1,
              0,
              4,
              4,
              0,
              2,
              4,
              3,
              3,
              1,
              1,
              3,
              2,
              1,
              2,
              3,
              2,
              4,
              1,
              1,
              2,
              1,
              4,
              6,
              5,
              4,
              2,
              3,
              3,
              2,
              4,
              6,
              2,
              2,
              3,
              2,
              2,
              1,
              0,
              3,
              5,
              3,
              3,
              1,
              0,
              1,
              1,
              2,
              1,
              4,
              1,
              1,
              5,
              3,
              0,
              1,
              1,
              4,
              3,
              2,
              3,
              4,
              0,
              2,
              3,
              0,
              2,
              2,
              4,
              1,
              1,
              3,
              3,
              2,
              0,
              1,
              0,
              2,
              1,
              5,
              3,
              3,
              1,
              1,
              0,
              2,
              4,
              2,
              1,
              5,
              1,
              1,
              2,
              5,
              3,
              3,
              0,
              2,
              1,
              2,
              2,
              0,
              2,
              1,
              2,
              3,
              1,
              2,
              3,
              3,
              2,
              0,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              1,
              1,
              3,
              2,
              0,
              1,
              2,
              6,
              3,
              3,
              1,
              2,
              4,
              1,
              3,
              5,
              1,
              1,
              1,
              2,
              1,
              2,
              2,
              3,
              1,
              0,
              0,
              1,
              1,
              0,
              2,
              1,
              2,
              2,
              1,
              0,
              0,
              1,
              2,
              1,
              0,
              0,
              0,
              0,
              2,
              2,
              0,
              1,
              0,
              1,
              1,
              0,
              2,
              1,
              2,
              1,
              0,
              1,
              0,
              0,
              1,
              0,
              1,
              1,
              0,
              1,
              0,
              0,
              1,
              1,
              1,
              1,
              0,
              2,
              1,
              1,
              0,
              0,
              0,
              0,
              0,
              0,
              5,
              3,
              6,
              4,
              4
            ]
          }
        ],
        yAxis: [
          {
            type: "value"
          }
        ]
      };
      alarmChart.setOption(option);
    },
    changeChart() {
      let changeChart = this.$echarts.init(
        document.getElementById("change-chart"),
        "dark"
      );
      let option = {
        title: [
          {
            text: "人员-企业劳动雇佣关系分析",
            subtext:
              "\n服务过5家及以上企业的人员占0.15%\n\n服务过2~4家企业人员占11.21%\n\n受雇于一家企业未曾离职的人员占88.64%",
            x: "13%",
            y: "70%",
            textAlign: "left"
          },
          {
            text: "人员属地",
            x: "71.7%",
            y: "20%",
            textStyle: {
              fontSize: "14",
              color: "#fff"
            },
            textAlign: "left"
          },
          {
            text: "人员性别",
            x: "71.7%",
            y: "51%",
            textStyle: {
              fontSize: "14",
              color: "#fff"
            },
            textAlign: "left"
          },
          {
            text: "人员年龄",
            x: "71.7%",
            y: "80%",
            textStyle: {
              fontSize: "14",
              color: "#fff"
            },
            textAlign: "left"
          } /* ,
                    {
                        text: '车辆-货物变更关系分析',
                        subtext: '\n运输过10种以上货物的车辆占0.93%\n\n运输过5~9种以上货物的车辆占18.79%\n\n只运输一种货物的车辆占41.03%',
                        x: 'right',
                        y: '15%',
                        textAlign: 'right'
                } */
        ],
        tooltip: {
          trigger: "item",
          formatter: "{b} ({d}%)"
        },
        /*
                legend: {
                    x: 'center',
                    y: 'bottom'
                },*/
        toolbox: {
          show: true
        },
        series: [
          {
            name: "服务企业数",
            type: "pie",
            radius: ["25%", "45%"],
            center: ["25%", "32%"],
            data: [
              {
                value: 1233,
                name: "服务2家~4家企业"
              },
              {
                value: 10070,
                name: "只服务1家企业"
              },
              {
                value: 17,
                name: "服务5家或5家以上企业"
              }
            ]
          },
          {
            name: "人员属地统计",
            type: "pie",
            radius: ["15%", "25%"],
            center: ["75%", "22%"],
            data: [
              { name: "安徽", value: 5341 },
              { name: "河南", value: 2600 },
              { name: "江苏", value: 2297 },
              { name: "浙江", value: 1861 },
              { name: "山东", value: 1709 },
              { name: "四川", value: 712 },
              { name: "湖北", value: 601 },
              { name: "江西", value: 441 },
              { name: "黑龙江", value: 410 },
              { name: "辽宁", value: 402 }
            ]
          },
          {
            name: "性别统计",
            type: "pie",
            radius: ["15%", "25%"],
            center: ["75%", "53%"],
            data: [
              {
                value: 3378,
                name: "女"
              },
              {
                value: 14815,
                name: "男"
              }
            ]
          },
          {
            name: "年龄统计",
            type: "pie",
            radius: ["15%", "25%"],
            center: ["75%", "82%"],
            data: [
              {
                value: 13,
                name: "小于20岁"
              },
              {
                value: 708,
                name: "20-30岁"
              },
              {
                value: 3710,
                name: "30-40岁"
              },
              {
                value: 8252,
                name: "40-50岁"
              },
              {
                value: 5511,
                name: "50岁以上"
              }
            ]
          }
        ]
      };
      changeChart.setOption(option);
    }
  }
};
</script>
<style lang="scss">
.fixed-title {
  position: absolute;
  top: 0px;
  left: 0px;
  color: #fff;
  font-size: 18px;
  padding: 4px 0px 4px 10px;
  width: 280px;
  background-image: linear-gradient(
    to right,
    rgb(64, 163, 255),
    rgba(64, 163, 255, 0)
  );
  z-index: 99;
}
.left-map-title,
.center-map-title,
.right-map-title {
  position: absolute;
  top: 60px;
  left: 0px;
  color: #fff;
  font-size: 16px;
  padding: 4px 0px 4px 10px;
  width: 280px;
  background-image: linear-gradient(
    to right,
    rgb(64, 163, 255),
    rgba(64, 163, 255, 0)
  );
}

.left-map-desc,
.center-map-desc,
.right-map-desc {
  position: absolute;
  bottom: 0px;
  left: 0px;
  padding: 4px;
  width: 280px;
  color: #fff;
  font-size: 14px;
  line-height: 1.6;
  background-color: rgb(64, 163, 255);
  background-color: rgba(64, 163, 255, 0.9);
  z-index: 10;
}

.reveal small {
  display: inline-block;
  font-size: 0.6em;
  line-height: 1.2em;
  vertical-align: top;
}
</style>