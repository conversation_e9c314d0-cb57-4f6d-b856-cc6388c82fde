<template>
  <el-dialog title="茂名市生产安全事故应急救援专家名单" append-to-body :visible.sync="visibleDialog">
    <el-radio-group v-model="type" size="mini" @change="getList" style="margin-bottom:20px;">
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="化工">化工</el-radio-button>
      <el-radio-button label="非煤矿山">非煤矿山</el-radio-button>
      <el-radio-button label="职业健康">职业健康</el-radio-button>
      <el-radio-button label="其它行业">其它行业</el-radio-button>
    </el-radio-group>
    <el-table :data="dataSource" height="420px">
      <el-table-column prop="manager" label="姓名"></el-table-column>
      <el-table-column prop="teamNm" label="工作单位"></el-table-column>
      <el-table-column prop="mobile" label="联系电话"></el-table-column>
      <el-table-column prop="skill" label="专长"></el-table-column>
    </el-table>
  </el-dialog>
</template>
<script>
import SimpleTable from "@/components/SimpleTable";
export default {
  props: {
    dataSource: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  components: {
    SimpleTable
  },
  data() {
    return {
      visibleDialog: false,
      type:"",
    };
  },
  methods: {
    init() {
      this.visibleDialog = true;
    },
    getList(){
        this.$emit('expertChange',this.type)
    }
  }
};
</script>