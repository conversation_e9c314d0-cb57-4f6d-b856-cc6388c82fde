<template>
    <div style="cursor: default;">
        <div class="map-popover">
            <div class="popover-header">
                <b>装卸企业信息</b>
                <span class="popover-close" title="关闭" @click="close">×</span>
            </div>
            <div class="popover-body" style="padding:5px;">
                <table style="width:325px">
                    <tbody>
                        <tr>
                            <th width="120">统一社会信用代码：</th>
                            <td colspan="3" >
                                {{selectEntpInfo.uscCd  ||''}} 
                            </td>
                        </tr>
                        <tr>
                            <th width="70">企业名称：</th>
                            <td colspan="3" >
                                {{selectEntpInfo.entpName  ||''}} 
                            </td>
                        </tr>
                        <tr>
                            <th width="70">公司类型：</th>
                            <td colspan="3" :title="selectEntpInfo.legalRepIdType">
                                {{selectEntpInfo.legalRepIdType  ||''}} 
                            </td>
                        </tr>
                         <tr >
                            <th>企业业务分类：</th>
                            <td colspan="3" :title="selectEntpInfo.catNmCn">{{selectEntpInfo.catNmCn ||''}}</td>
                        </tr>
                        <tr >
                            <th>成立日期：</th>
                            <td colspan="3">{{selectEntpInfo.establishDate||''}}</td>
                        </tr>
                        <!-- <tr >
                            <th>营业期限</th>
                            <td colspan="3">{{selectEntpInfo.busiEndDate||''}}</td>
                        </tr> -->
                        <!-- <tr >
                            <th>企业登记注册地</th>
                            <td colspan="3">{{selectEntpInfo.entpDist||''}}</td>
                        </tr> -->
                        <!-- <tr >
                            <th>经营状态</th>
                            <td colspan="3">{{selectEntpInfo.regStat||''}}</td>
                        </tr> -->
                        <tr >
                            <th>法定代表人：</th>
                            <td colspan="3">{{selectEntpInfo.legalRepNm||''}}</td>
                        </tr>
                        <tr >
                            <th>核准日期：</th>
                            <td colspan="3">{{selectEntpInfo.aprvDate||''}}</td>
                        </tr>
                        <tr >
                            <th>注册资本：</th>
                            <td colspan="3">{{selectEntpInfo.regCaptital  ||''}}{{selectEntpInfo.regCaptitalUnit  ||''}}</td>
                        </tr>
                        <tr >
                            <th>登记机关：</th>
                            <td colspan="3" :title="selectEntpInfo.regDept">{{selectEntpInfo.regDept  ||''}}</td>
                        </tr>
                        <!-- <tr >
                            <th>紧急联系人</th>
                            <td colspan="3">{{selectEntpInfo.erNm}}</td>
                        </tr>
                        <tr >
                            <th>紧急联系人电话</th>
                            <td colspan="3">{{selectEntpInfo.erMob}}</td>
                        </tr> -->
                        <tr >
                            <th>企业地址：</th>
                            <td colspan="3" :title="selectEntpInfo.location">{{selectEntpInfo.location  ||''}}</td>
                        </tr>
                        <tr >
                            <th>营业执照经营范围：</th>
                            <td colspan="3" :title="selectEntpInfo.businessScope">{{selectEntpInfo.businessScope  ||''}}</td>
                        </tr>
                     </tbody>
                </table>
            </div>
            <div class="popover-footer">
                <a href="javascript:void(0);" @click="showStevedoringDialog(selectEntpInfo.ipPk)">装卸记录</a>
                <a href="javascript:void(0);" @click="showChemDialog(selectEntpInfo.ipPk)">货物列表</a>
                <a href="javascript:void(0);" @click="showPlaceDialog(selectEntpInfo.entpName)">周边场所</a>
                <!-- <a href="javascript:void(0);" @click="showTank(selectEntpInfo)">罐区详情</a> -->
            </div>
        </div>
        <el-dialog title="今日装卸记录信息" :visible.sync="visibleOfStevedoring" append-to-body width="80%"  top="5vh" class="detail-dialog">
            <stevedoring-info ref="stevedoringinfo" ></stevedoring-info>
        </el-dialog>
        <el-dialog title="货物列表信息" :visible.sync="visibleOfChem" append-to-body width="80%"  top="5vh" class="detail-dialog">
            <chem-info ref="cheminfo" ></chem-info>
        </el-dialog>
        <el-dialog title="周边场所" :visible.sync="rimPlaceDialog" append-to-body width="80%" top="5vh" >
            <RimPlace ref="rimPlace"></RimPlace>
        </el-dialog>
        <el-dialog title="罐区详情" :visible.sync="tankDialog" append-to-body width="40%">
            <ul class="detail-ul">
                <li>
                    <span class="detail-ul-title">企业名称：</span>{{tankInfo.entpNm}}
                </li>
                <li>
                    <span class="detail-ul-title">储罐区编号：</span>{{tankInfo.tankAreaCd}}
                </li>
                <li>
                   <span class="detail-ul-title">存储物质：</span>{{tankInfo.storeGoods}}
                </li>
                <li>
                   <span class="detail-ul-title"> 物质状态：</span>{{tankInfo.storeStatus}}
                </li>
                <li>
                   <span class="detail-ul-title"> 是否重大危险源：</span>
                   <span v-if="tankInfo.isZdwxy == 1">是</span>
                   <span v-if="tankInfo.isZdwxy == 0">否</span>
                </li>
                 <li v-if="tankInfo.isZdwxy">
                   <span class="detail-ul-title"> 重大危险源级别：</span>{{tankInfo.storeStatus}}
                </li>
                <li>
                   <span class="detail-ul-title"> 是否危化品：</span>
                   <span v-if="tankInfo.isDanger == 1">是</span>
                   <span v-if="tankInfo.isDanger == 0">否</span>
                </li>
                <li>
                   <span class="detail-ul-title"> 是否重点监督危化品：</span>
                   <span v-if="tankInfo.isZdjg == 1">是</span>
                   <span v-if="tankInfo.isZdjg == 0">否</span>
                </li>
                <li>
                    <span class="detail-ul-title">储罐数量：</span>{{tankInfo.tankCnt}}
                </li>
                <li>
                   <span class="detail-ul-title"> 储罐容积：</span>{{tankInfo.tankVol}}
                </li>
            </ul>
        </el-dialog>
    </div>
</template>

<script>
import * as Tool from '@/utils/tool'
import * as $http from "@/api/emergency";
import StevedoringInfo from '@/views/modules/mapMonit/components/stevedoring-info'
import ChemInfo from '@/views/modules/mapMonit/components/chem-info'
import RimPlace from "./rimPlace";

export default {
    name:'MonitorCpInfoWindow',
    props:{
        selectEntpInfo:{
            type:Object
        }
    },
    components:{
        StevedoringInfo,
        ChemInfo,
        RimPlace
    },
    data (){
        return {
            visibleOfStevedoring:false,
            visibleOfChem:false,
            rimPlaceDialog: false,//周边场所
            tankDialog:false,
            tankInfo:{}
        }
    },
    methods:{
        handleResult(rqs) {
            if (rqs === undefined || rqs == null) {
                return "无";
            } else {
                return rqs;
            }
        },

        close(){
            this.$emit('close');
        },
        showStevedoringDialog(ipPk){
            this.visibleOfStevedoring = true;
            this.$nextTick(() => {
                this.$refs.stevedoringinfo.getStevedorList(ipPk)
            });
        },
        // 最近10条电子运单
        showChemDialog(ipPk){
            this.visibleOfChem = true;
            this.$nextTick(() => {
                this.$refs.cheminfo.getChemList(ipPk)
            });
        },
        //周边场所
        showPlaceDialog(name) {
            this.rimPlaceDialog = true
            this.$nextTick(() => {
                this.$refs.rimPlace.getPoiList('entp',name,this.selectEntpInfo.radius)
            });
        },
        //罐区详情
        // showTank(data){
        //     let param = {
        //         entp_pk:data.ipPk
        //     }
        //     if(data.ipPk){
        //         $http.getTank(param).then(res => {
        //             if (res.code == 0 && res.page.list && res.page.list.length >0) {
        //                 this.tankDialog = true
        //                 this.tankInfo = res.page.list[0]
        //             } else {
        //                 this.$message({
        //                     message: "对不起，未找到该企业的罐体信息",
        //                     type: "error"
        //                 });
        //             }
        //         }).catch(error => {
        //             console.log(error);
        //         });
        //     }
        // }
    }
}
</script>

<style lang="scss" scoped>
.map-popover {
  /* position: absolute; */
  position:relative;
  z-index: 9;
  left:auto;
  top:auto;
  width:100%;
  /* left: 5px; */
  border: 1px solid #aaa;
  border-radius: 4px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
  background-color: #fff;

  &:before,
  &:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -20px;
    z-index: 2;
    width: 0;
    height: 0;
    margin-left: -5px;
    border-width: 10px 8px;
    border-style: solid;
    border-color: #f9f9f9 transparent transparent #f9f9f9;
  }
  &:after {
    border-width: 11px 9px;
    bottom: -23px;
    z-index: 1;
    margin-left: -6px;
    border-color: rgba(0, 0, 0, 0.2) transparent transparent rgba(0, 0, 0, 0.2);
  }
}

.detail-ul{
    color: #ffffff;
    line-height: 30px;
}
.detail-ul-title{
    color: #ccc
}
</style>
