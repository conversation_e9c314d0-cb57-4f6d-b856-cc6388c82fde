<template>
  <div style="cursor: default;">
    <div class="map-popover">
      <div class="popover-header">
        <b>车辆信息</b>
        <span class="popover-close" title="关闭" @click="close">×</span>
      </div>
      <div class="popover-body" style="padding:5px;">
        <table style="width:340px">
          <tbody>
            <tr>
              <th width="70">牵引车：</th>
              <template v-if="rteplan">
                <td width="80" style="display: table-cell;">
                  <a href="javascript:void(0)" @click="showVehicleDialog(rteplan.tracPk)">{{handleResult(selectVecInfo.vehicleNo)}}</a></td>
                <th width="70">挂车：</th>
                <td v-if="rteplan.traiPk" width="80" style="display: table-cell;"><a href="javascript:void(0)" @click="showVehicleDialog(rteplan.traiPk)">{{handleResult(rteplan.traiCd)}}</a></td>
                <td v-else width="100">无</td>
              </template>
              <td v-else>{{handleResult(selectVecInfo.vehicleNo)}}</td>
            </tr>
            <template v-if="rteplan">
              <tr>
                <th width="70">时速：</th>
                <td colspan="3">
                  {{selectVecInfo.speed}} 公里/小时
                </td>
              </tr>
              <tr>
                <th>驾驶员：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showPersDialog(rteplan.dvPk)">{{handleResult(rteplan.dvNm)}}</a> {{handleResult(rteplan.dvMob, '')}}
                </td>
              </tr>
              <tr>
                <th>押运员：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showPersDialog(rteplan.scPk)">{{handleResult(rteplan.scNm)}}</a> {{handleResult(rteplan.scMob, '')}}
                </td>
              </tr>
              <tr>
                <th>货物：</th>
                <td colspan="3">
                  <template v-if="rteplan.rtePlanItemList && rteplan.rtePlanItemList.length>0">
                    <template v-for="item in rteplan.rtePlanItemList">
                      <!-- <a :key="item.enchPk" href="javascript:void(0)" @click="showGoodsDialog(item.enchPk)" :title="handleResult(item.dangGoodsNm || item.goodsNm)">{{handleResult( item.dangGoodsNm || item.goodsNm )}}</a>（{{handleResult(item.goodsNw || item.loadQty || '0', '--')}}吨） -->
                      <a :key="item.prodPk" href="javascript:void(0)" @click="showGoodsDialog(item.prodPk)" :title="handleResult(item.dangGoodsNm)">{{handleResult(item.goodsNm )}}（{{handleResult( item.dangGoodsNm)}}）</a>
                    </template>
                  </template>
                  <template v-else>
                    <!-- <a :key="rteplan.enchPk" href="javascript:void(0)" @click="showGoodsDialog(rteplan.enchPk)" :title="handleResult( rteplan.dangGoodsNm || rteplan.goodsNm)">{{handleResult(rteplan.dangGoodsNm || rteplan.goodsNm)}}</a>（{{handleResult(rteplan.goodsNw || rteplan.loadQty || '0', '--')}}吨） -->
                    <a :key="rteplan.prodPk" href="javascript:void(0)" @click="showGoodsDialog(rteplan.prodPk)" :title="handleResult(rteplan.dangGoodsNm)">{{handleResult(rteplan.goodsNm )}}（{{handleResult( rteplan.dangGoodsNm)}}）</a>
                  </template>
                </td>
              </tr>
              <tr>
                <th>货重：</th>
                <td colspan="3">
                  <template v-if="!rteplan.goodsGw && !rteplan.unloadQty">计划装 {{rteplan.loadQty}} 吨</template>
                  <template v-else-if="rteplan.goodsGw && !rteplan.unloadQty">实装 {{rteplan.goodsGw}} 吨</template>
                  <template v-else-if="rteplan.unloadQty">实卸 {{rteplan.unloadQty}} 吨</template>
                </td>
              </tr>
              <tr>
                <th>物流公司：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showEntpDialog(rteplan.carrierPk)" :title="handleResult(rteplan.carrierNm)">{{handleResult(rteplan.carrierNm)}}</a>
                </td>
              </tr>
              <tr>
                <th>发货地：</th>
                <td colspan="3" :title="rteplan.csnorWhseDist">{{rteplan.csnorWhseDist}}</td>
              </tr>
              <tr>
                <th>收货地：</th>
                <td colspan="3" :title="rteplan.csneeWhseDist">{{rteplan.csneeWhseDist}}</td>
              </tr>
            </template>
            <template v-else>
              <tr>
                <th>电子运单：</th>
                <td colspan="3" style="color: red">无电子运单信息</td>
              </tr>
              <tr>
                <th>时速：</th>
                <td colspan="3">{{selectVecInfo.speed}}公里/小时</td>
              </tr>
              <tr>
                <th>定位时间：</th>
                <td colspan="3">{{selectVecInfo.updateTime}}(卫星定位)</td>
              </tr>
            </template>
            <template v-if="alarmInfo">
              <tr>
                <th>报警类型：</th>
                <td colspan="3" :title="alarmInfo.catNmCn" style="color: red">{{alarmInfo.catNmCn}}</td>
              </tr>
              <tr>
                <th>报警地点：</th>
                <td colspan="3" :title="alarmInfo.alarmLocation">{{alarmInfo.alarmLocation}}</td>
              </tr>
              <tr>
                <th>报警时间：</th>
                <td colspan="3" :title="alarmInfo.alarmTime">{{alarmInfo.alarmTime}}</td>
              </tr>
              <tr>
                <th>报警详情：</th>
                <td colspan="3" :title="alarmInfo.descr">{{alarmInfo.descr}}</td>
              </tr>
              <tr v-if="alarmInfo.url">
                <th>卡口抓拍：</th>
                <td colspan="3">
                  <div ref="imagesWrapper" class="images-wrapper clearfix">
                    <!-- <template v-for="(item,index) in alarmInfo.url"> -->
                    <div class="images-wrapper-item">
                      <div class="item-content">
                        <div><span><img @click="previewHandle($event)" :src="alarmInfo.url" /></span></div>
                      </div>
                      <!-- <div class="item-desc">
                        <div>{{item.vecNo}}</div>
                        <div>{{item.time?item.time.split(' ')[1]:item.time.slice(-8)}}</div>
                      </div> -->
                    </div>
                    <!-- </template> -->
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      <div class="popover-footer">
        <a @click="showHisTrack(selectVecInfo.vehicleNo)" target="_blank">历史轨迹</a>
        <a v-if="rteplan" href="javascript:void(0);" @click="showGoodsDialog(rteplan.prodPk)">应急救援</a>
        <a href="javascript:void(0);" @click="showAlarmDialog(selectVecInfo.vehicleNo)">违章记录</a>
        <a href="javascript:void(0);" @click="showPlaceDialog(selectVecInfo.vehicleNo)">周边场所</a>
        <!-- <a href="javascript:void(0);" v-if="alarmInfo" @click="enforceOnLine">异常处置</a> -->
        <!-- <a href="javascript:void(0);" @click="showVecEventDialog(selectVecInfo.vehicleNo)">车辆事件</a> -->
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" append-to-body width="80%" top="5vh">
      <simple-table :tableTitle="tableTitle" :tableHeader="tableHeader" :tablePage="tablePage" @tableRefreshByPagination="tableRefreshByPaginationHandle"></simple-table>
    </el-dialog>

    <!-- 企业详情 -->
    <el-dialog title="企业详情" :visible.sync="visibleOfEntp" append-to-body width="80%" top="5vh" class="detail-dialog">
      <entp-info ref="entpInfo" :isCompn="true"></entp-info>
    </el-dialog>
    <!-- 人员详情 -->
    <el-dialog title="人员详情" :visible.sync="visibleOfPers" append-to-body width="80%" top="5vh" class="detail-dialog">
      <pers-info ref="persInfo" :isCompn="true"></pers-info>
    </el-dialog>
    <!-- 车辆详情 -->
    <el-dialog title="车辆详情" :visible.sync="visibleOfVec" append-to-body width="80%" top="5vh" class="detail-dialog">
      <vec-info ref="vecInfo" :isCompn="true"></vec-info>
    </el-dialog>
    <!-- 危化品信息详情 -->
    <el-dialog title="危化品信息详情" :visible.sync="visibleOfChemica" append-to-body width="80%" top="5vh" class="detail-dialog">
      <chemica-info ref="chemicaInfo" :isCompn="true" :messageIpPk="prodPk"></chemica-info>
    </el-dialog>
    <!-- 在途可视 -->
    <!-- <histrack-info ref="histrackInfo" :isCompn="true"></histrack-info> -->
    <el-dialog title="周边场所" :visible.sync="rimPlaceDialog" append-to-body width="80%" top="5vh">
      <RimPlace ref="rimPlace"></RimPlace>
    </el-dialog>

    <!-- 异常停车处置窗口 -->
    <!-- <enforce-on-line-dialog ref="enForceOnLineDialog"></enforce-on-line-dialog> -->
  </div>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";

import SimpleTable from "@/components/SimpleTable";
import * as Tool from "@/utils/tool";
import * as $httpAlarm from "@/api/violationAlarm";
import * as $http from "@/api/emergency";

import EntpInfo from "@/views/modules/base/entp/entp-info";
import PersInfo from "@/views/modules/base/pers/pers-info";
import VecInfo from "@/views/modules/base/vec/vec-info";
import ChemicaInfo from "@/views/modules/base/chemica/chemica-info";
// import EnforceOnLineDialog from "./enforce-on-line-dialog";
// import HistrackInfo from '@/views/modules/mapMonit/histrack-info'
import RimPlace from "./rimPlace";
import { queryList } from "@/api/wb";

export default {
  name: "MonitorInfoWindow",
  props: {
    selectVecInfo: {
      type: Object
    },
    rteplan: {
      type: Object
    }
  },
  components: {
    SimpleTable,
    EntpInfo,
    PersInfo,
    VecInfo,
    ChemicaInfo,
    RimPlace,
    // EnforceOnLineDialog
  },
  watch: {
    "selectVecInfo.vehicleNo": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getAlarmInfo(newVal);
        }
      },
      deep: true
    },
    "rteplan.cd": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getWeigh(newVal);
        }
      },
      deep: true
    }
    // alarmInfo: {
    //   handler(newVal, oldVal) {
    //     if (newVal) {
    //       this.popoverTop = -380;
    //     } else {
    //       this.popoverTop = -280;
    //     }
    //     console.log(this.popoverTop);
    //   },
    //   deep: true
    // }
  },
  data() {
    return {
      dialogVisible: false,

      dialogTableVisible: false, // 弹窗是否可见
      dialogTitle: "", // 弹窗标题
      tableComponent: "simple-table", // table组件
      tableTitle: "", // table的标题
      tableHeader: null, // table的表头信息
      tablePage: null, // table的数据信息，包括list数据以及分页数据

      tableType: null,

      visibleOfEntp: false,
      visibleOfPers: false,
      visibleOfVec: false,
      visibleOfChemica: false,
      rimPlaceDialog: false, //周边场所

      alarmInfo: null,
      popoverTop: -280,
      loading: null,
      prodPk: null
    };
  },
  methods: {
    // 异常停车处置
    // enforceOnLine() {
    //   this.$refs.enForceOnLineDialog.init(
    //     {
    //       rteplan: this.rteplan,
    //       selectVecInfo: this.selectVecInfo
    //     },
    //     this.alarmInfo || {}
    //   );
    // },
    openFullScreen() {
      const loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      return loading;
    },
    handleResult(rqs) {
      if (rqs === undefined || rqs == null) {
        return "无";
      } else {
        return rqs;
      }
    },

    close() {
      this.$emit("close");
    },

    // 车辆信息弹窗
    showVehicleDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfVec = true;
      this.$nextTick(() => {
        this.$refs.vecInfo.initByPk(pk);
      });
    },

    // 人员信息弹窗
    showPersDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/pers/info/'+pk,'_blank');
      this.visibleOfPers = true;
      this.$nextTick(() => {
        this.$refs.persInfo.initByPk(pk);
      });
    },

    // 货物信息弹窗
    showGoodsDialog(prodPk) {
      if (!prodPk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.prodPk = prodPk;
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/chemica/info/'+pk,'_blank');
      this.visibleOfChemica = true;
      this.$nextTick(() => {
        this.$refs.chemicaInfo.getInfoByProdPk(prodPk);
      });
    },

    // 企业信息弹窗
    showEntpDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/entp/info/'+pk,'_blank');
      this.visibleOfEntp = true;
      this.$nextTick(() => {
        this.$refs.entpInfo.initByPk(pk);
      });
    },

    // 历史轨迹
    showHisTrack(vehicleNo) {
      let location = window.location;
      window.open(
        location.origin +
          location.pathname +
          "#/monit/hisTrack?v=" +
          encodeURIComponent(vehicleNo) +
          "&t=" +
          Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },

    getAlarmList(params) {
      let _this = this;
      this.loading = this.openFullScreen();
      $httpAlarm.getAlarmListBySimpleQuery(params).then(res => {
        if (res.code == 0) {
          _this.dialogTableVisible = true;
          _this.tablePage = res.data;
        } else {
          _this.tablePage = {
            list: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          _this.$message.error(res.msg);
        }
        this.loading.close();
      });
    },
    //获取最近报警信息
    getAlarmInfo(vecno) {
      let params = {
        vecno: vecno
      };
      $httpAlarm.getAlarmByVecNo(params).then(res => {
        if (res.code == 0 && res.data.length > 0) {
          this.alarmInfo = res.data[0];
        } else {
          this.alarmInfo = null;
        }
      });
    },
    getWeigh(cd) {
      let param = {
        filters: {
          groupOp: "AND",
          rules: [{ field: "argmt_cd", op: "cn", data: cd }]
        },
        page: 1,
        limit: 1
      };
      queryList(param).then(res => {
        if (res.code == 0 && res.list.length > 0) {
          this.$set(this.rteplan, "weigh", res.list[0].loadQty);
        } else {
          this.rteplan.weigh = "";
        }
      });
    },

    // 违章记录弹窗
    showAlarmDialog(vehicleNo) {
      let _this = this;
      this.tableType = "alarm";
      this.dialogTitle = `车辆违章报警和预警详情`;
      this.tableTitle = `<h5 style="color:#fff;margin-bottom:5px;">${vehicleNo}，30天内违章报警和预警数据</h5>`;
      this.tableHeader = [
        { name: "违章时间", field: "alarmTime", width: 200 },
        { name: "违章地点", field: "alarmLocation" },
        { name: "违章类型", field: "catNmCn" },
        { name: "违章细节", field: "descr" }
      ];
      let params = {
        page: 1,
        limit: 20,
        days: 30,
        tracNo: vehicleNo,
        notCatCd:'2550.160.180.150'
      };
      this.getAlarmList(params);
    },

    getVecEventList(params) {
      let _this = this;
      $httpAlarm.getAlarmListBySimpleQuery(params).then(res => {
        if (res.code == 0) {
          _this.dialogTableVisible = true;
          _this.tablePage = res.data;
        } else {
          _this.tablePage = {
            list: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          _this.$message.error(res.msg);
        }
      });
    },

    // 车辆事件弹窗
    showVecEventDialog(vehicleNo) {
      let _this = this;
      this.tableType = "vecevent";
      this.dialogTitle = `车辆当天事件详情`;
      this.tableTitle = `<h1>${vehicleNo}</h1><span>车辆当天事件信息</span>`;
      this.tableHeader = [
        { name: "事件时间", field: "alarmTime", width: 200 },
        { name: "事件类型", field: "alarmLocation" },
        { name: "事件地址", field: "catNmCn" },
        { name: "事件细节", field: "descr" }
      ];
      let params = {
        page: 1,
        limit: 20,
        tracNo: vehicleNo
      };
      this.getVecEventList(params);
    },

    // table翻页事件
    tableRefreshByPaginationHandle(paginationData) {
      if (this.tableType == "alarm") {
        let params = {
          page: 1,
          limit: 20,
          days: 30,
          tracNo: this.selectVecInfo.vehicleNo
        };
        this.getAlarmList(Object.assign({}, params, paginationData));
      } else {
        let params = {
          page: 1,
          limit: 20,
          days: 30,
          tracNo: this.selectVecInfo.vehicleNo
        };
        this.getVecEventList(Object.assign({}, params, paginationData));
      }
    },
    //周边场所
    showPlaceDialog(name) {
      this.rimPlaceDialog = true;
      this.$nextTick(() => {
        this.$refs.rimPlace.getPoiList("vec", name, this.selectVecInfo.radius);
      });
    },

    previewHandle() {
      var viewer = new Viewer(this.$refs.imagesWrapper, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        // ready() {
        //   viewer.viewer.className += " custom-lic-viewer-container";
        // },
        // viewed() {
        //   let viewCanvas = viewer.viewer.getElementsByClassName(
        //     "viewer-canvas"
        //   );
        //   if (viewCanvas.length > 0) {
        //     let imgTags = viewCanvas[0].getElementsByTagName("img");
        //     if (imgTags.length > 0) {
        //       imgTags[0].style.marginLeft =
        //         parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
        //     }
        //   }
        // },
        hidden() {
          viewer.destroy();
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.map-popover {
  /* position: absolute; */
  position:relative;
  z-index: 9;
  left:auto;
  top:auto;
  width:100%;
  /* left: 5px; */
  border: 1px solid #aaa;
  border-radius: 4px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
  background-color: #fff;

  &:before,
  &:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -20px;
    z-index: 2;
    width: 0;
    height: 0;
    margin-left: -5px;
    border-width: 10px 8px;
    border-style: solid;
    border-color: #f9f9f9 transparent transparent #f9f9f9;
  }
  &:after {
    border-width: 11px 9px;
    bottom: -23px;
    z-index: 1;
    margin-left: -6px;
    border-color: rgba(0, 0, 0, 0.2) transparent transparent rgba(0, 0, 0, 0.2);
  }
}

.images-wrapper {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  margin: 0 5px 5px 0;

  .images-wrapper-item {
    float: left;
    margin: 5px;
    text-align: center;
    .item-content {
      width: 50px;
      height: 50px;
      overflow: hidden;
      > div {
        display: table;
        width: 100%;
        height: 100%;
        > span {
          vertical-align: middle;
          text-align: center;
          display: table-cell;
          width: 50px;
          img {
            width: 100%;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
