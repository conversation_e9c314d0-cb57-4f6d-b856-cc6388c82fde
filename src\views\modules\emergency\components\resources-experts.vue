<template>
  <div>
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-form ref="form" :model="queryForm" :inline="true" label-width="85px" size="mini" @submit.native.prevent>
          <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form-item label="专家类别：">
              <el-radio-group v-model="queryForm.cat_cd" @change="getList()">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button :label="item.cd" v-for="item in expertTypeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form-item>
              <el-input v-model="queryForm.expert_nm" placeholder="请输入专家姓名" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryForm.speciality" placeholder="请输入专业特长" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryForm.work_unit" placeholder="请输入工作单位" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">查询</el-button>
              <el-button type="success" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
    <!--列表-->
    <el-table class="el-table" border :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :height="tableHeight">
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column prop="catCd" label="专家类别">
        <template slot-scope="scope">
          <template v-for="item in expertTypeList">
            <span v-if="item.cd===scope.row.catCd" :key="item.cd">{{item.nmCn}}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="expertNm" label="专家姓名"></el-table-column>
      <!-- <el-table-column prop="idCard" label="身份证号"></el-table-column> -->
      <el-table-column prop="speciality" label="专业特长" width="120"></el-table-column>
      <el-table-column prop="mobile" label="联系方式"></el-table-column>
      <el-table-column prop="workUnit" label="工作单位"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]" :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </el-col>

    <!-- 新增/编辑 -->
    <el-dialog :title="(editForm.id ? '编辑' : '新增') + '专家信息'" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" :loading="dialogLoading" label-width="80px" size="small">
        <el-form-item label="专家类别" :rules="$rulesFilter({ required: true })" prop="catCd">
          <!-- <el-radio-group v-model="editForm.catCd">
            <el-radio-button :label="item.cd" v-for="item in expertTypeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
          </el-radio-group> -->
          <el-select v-model="editForm.catCd" filterable placeholder="请选择专家类别" clearable>
            <el-option v-for="item in expertTypeList" :key="item.cd" :label="item.nmCn" :value="item.cd"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专家姓名" :rules="$rulesFilter({ required: true })" prop="expertNm">
          <el-input type="text" v-model="editForm.expertNm" laceholder="请输入专家姓名"></el-input>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input type="text" v-model="editForm.idCard" laceholder="请输入身份证号"></el-input>
        </el-form-item>
        <!-- <el-form-item label="身份证号" :rules="$rulesFilter({ required: true, type:'ID'})" prop="idCard">
          <el-input type="text" v-model="editForm.idCard" laceholder="请输入身份证号"></el-input>
        </el-form-item> -->
        <el-form-item label="专业特长" prop="speciality">
          <el-input type="text" v-model="editForm.speciality" laceholder="请输入专业特长"></el-input>
        </el-form-item>
        <el-form-item label="联系方式" :rules="$rulesFilter({ required: true, type: 'mobile' })" prop="mobile">
          <el-input type="text" v-model="editForm.mobile" laceholder="请输入联系方式"></el-input>
        </el-form-item>
        <el-form-item label="工作单位" prop="workUnit">
          <el-input type="text" v-model="editForm.workUnit" laceholder="请输入工作单位"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="editformSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/emergency";
export default {
  name: "resource-experts",
  props:{
    defaultSearchParams:{
      type:Object,
    }
  },
  data() {
    return {
      queryForm: {
        cat_cd: "",
        expert_nm: "",
        speciality: "",
        work_unit: ""
      },
      tableHeight: Tool.getTableHeight() - 60,
      expertTypeList: [],
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },

      dialogLoading: false,
      dialogVisible: false,
      editForm: {
        id: "",
        catCd: "",
        expertNm: "",
        idCard: "",
        speciality: "",
        mobile: "",
        workUnit: ""
      }
    };
  },
  created() {
    if(this.defaultSearchParams){
      let keys = Object.keys(this.defaultSearchParams)
      keys.forEach(key=>{
        if(this.queryForm.hasOwnProperty(key)){
          this.queryForm[key] = this.defaultSearchParams[key];
        }
      })
    }
    this.getExpertTypeList();
    this.getList();
  },
  mounted: function() {
    const _this = this;
    this.$nextTick(()=>{
      this.tableHeight = Tool.getTableHeight() - 60;
    })
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight() - 60;
    });
  },
  methods: {
    getExpertTypeList() {
      $http
        .getEmergencyExpertTypeList()
        .then(response => {
          if (response.code == 0) {
            this.expertTypeList = response.data;
          } else {
            this.expertTypeList = [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      let filters = { groupOp: "AND", rules: [] };
      param = param || Object.assign({}, this.pagination);
      delete param.total;
      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "cn",
            data: _this.queryForm[filed]
          };
          if (filed == "cat_cd") {
            rule.op = "eq";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;
      this.listLoading = true;
      $http
        .getEmergencyExpertList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    refresh() {
      this.pagination.page = 1;
      this.getList();
    },
    clearEditForm(row) {
      let _this = this;
      let keys = Object.keys(this.editForm);
      keys.forEach(key => {
        _this.$set(_this.editForm, key, row && row[key] ? row[key] : "");
      });
    },
    add() {
      this.clearEditForm();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm.resetFields();
      });
    },
    update(row) {
      this.clearEditForm(row);
      this.dialogVisible = true;
    },
    del(row) {
      let _this = this;
      this.$confirm("您确认删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http
            .delEmergencyExpert([row.id])
            .then(res => {
              this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    editformSubmit() {
      let _this = this;
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.dialogLoading = true;
          $http[
            _this.editForm.id ? "updEmergencyExpert" : "addEmergencyExpert"
          ](_this.editForm)
            .then(res => {
              _this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.$refs.editForm.resetFields();
                _this.clearEditForm();
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
