<template>
  <div>
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-form ref="form" :model="queryForm" :inline="true" label-width="100px" size="mini" @submit.native.prevent>
          <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form-item label="救援力量类别">
              <el-radio-group v-model="queryForm.cat_cd" @change="getList()">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form-item>
              <el-input v-model="queryForm.unit_name" placeholder="请输入单位名称" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">查询</el-button>
              <el-button type="success" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
    <!--列表-->
    <el-table class="el-table" border :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :height="tableHeight">
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="form-expand-in-table"  label-width="80px">
            <el-form-item label="救援装备">
              <span>{{ props.row.equipment }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column prop="catCd" label="救援力量类别">
        <template slot-scope="scope">
          <template v-for="item in typeList">
            <span v-if="item.cd===scope.row.catCd" :key="item.cd">{{item.nmCn}}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单位名称"></el-table-column>
      <el-table-column prop="leader" label="应急联系人">
        <template slot-scope="scope">
          <template v-if="scope.row.leader && scope.row.leader.length>0">
            <template v-for="(item,index) in scope.row.leader.split(';')">
              <div :key="index"><el-tag type="success">{{item}}</el-tag></div>
            </template>
          </template>
          <template v-else>{{scope.row.leader}}</template>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="mobile" label="联系电话"></el-table-column> -->
      <el-table-column prop="address" label="单位地址"></el-table-column>
      <el-table-column prop="personNum" label="队员人数"></el-table-column>
      <!-- <el-table-column prop="equipment" label="救援装备"></el-table-column> -->
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]" :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </el-col>

    <!-- 新增/编辑 -->
    <el-dialog :title="(editForm.id ? '编辑' : '新增') + '应急救援力量信息'" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" :loading="dialogLoading" label-width="100px" size="small">
        <el-form-item label="力量类别" :rules="$rulesFilter({ required: true })" prop="catCd">
          <!-- <el-radio-group v-model="editForm.catCd">
            <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
          </el-radio-group> -->
          <el-select v-model="editForm.catCd" filterable placeholder="请选择应急救援力量类别" clearable>
            <el-option v-for="item in typeList" :key="item.cd" :label="item.nmCn" :value="item.cd"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" :rules="$rulesFilter({ required: true })" prop="unitName">
          <el-input type="text" v-model="editForm.unitName" laceholder="请输入单位名称"></el-input>
        </el-form-item>
        <el-form-item label="应急联系人" prop="leader">
          <el-input type="text" v-model="editForm.leader" laceholder="请输入应急联系人及联系电话"></el-input>
        </el-form-item>
        <!-- <el-form-item label="联系电话" prop="mobile" :rules="$rulesFilter({ required: true, type: 'mobile' })">
          <el-input type="text" v-model="editForm.mobile" laceholder="请输入联系电话"></el-input>
        </el-form-item> -->
        <el-form-item label="单位地址" prop="address" :rules="$rulesFilter({ required: true })">
          <el-input v-model="editForm.address" laceholder="请输入单位地址"></el-input>
        </el-form-item>
        <el-form-item label="经纬度" prop="location" :rules="$rulesFilter({ required: true })">
          <set-point-lat-and-lon v-model="editForm.location" laceholder="请选择单位地址所在经纬度"></set-point-lat-and-lon>
        </el-form-item>
        <el-form-item label="队员人数" prop="personNum">
          <el-input type="number" v-model="editForm.personNum" laceholder="请输入工作单位"></el-input>
        </el-form-item>
        <el-form-item label="救援装备" prop="equipment">
          <el-input type="textarea" :rows="6" v-model="editForm.equipment" laceholder="请输入救援装备"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="editformSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/emergency";
import setPointLatAndLon from "@/components/baiduMap/setPointLatAndLon";
export default {
  name: "resource-experts",
  components: {
    setPointLatAndLon
  },
  data() {
    return {
      queryForm: {
        cat_cd: "",
        expert_nm: "",
      },
      tableHeight: Tool.getTableHeight() - 60,
      typeList: [],
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },

      dialogLoading: false,
      dialogVisible: false,
      editForm: {
        id: "",
        catCd: "",
        unitName: "",
        leader: "",
        mobile: "",
        address: "",
        location: "",
        personNum: "",
        equipment: ""
      }
    };
  },
  created() {
    this.getForceTypeList();
    this.getList();
  },
  mounted: function() {
    const _this = this;
    this.tableHeight = Tool.getTableHeight() - 60;
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight() - 60;
    });
  },
  methods: {
    getForceTypeList() {
      $http
        .getEmergencyForceTypeList()
        .then(response => {
          if (response.code == 0) {
            this.typeList = response.data;
          } else {
            this.typeList = [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      let filters = { groupOp: "AND", rules: [] };
      param = param || Object.assign({}, this.pagination);
      delete param.total;
      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "cn",
            data: _this.queryForm[filed]
          };
          if (filed == "cat_cd") {
            rule.op = "eq";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;
      this.listLoading = true;
      $http
        .getEmergencyForceList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    refresh() {
      this.pagination.page = 1;
      this.getList();
    },
    clearEditForm(row) {
      let _this = this;
      let keys = Object.keys(this.editForm);
      keys.forEach(key => {
        _this.$set(_this.editForm, key, row && row[key] ? row[key] : "");
      });
    },
    add() {
      this.clearEditForm();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm.resetFields();
      });
    },
    update(row) {
      this.clearEditForm(row);
      this.dialogVisible = true;
    },
    del(row) {
      let _this = this;
      this.$confirm("您确认删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http
            .delEmergencyForce([row.id])
            .then(res => {
              this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    editformSubmit() {
      let _this = this;
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.dialogLoading = true;
          $http[_this.editForm.id ? "updEmergencyForce" : "addEmergencyForce"](
            _this.editForm
          )
            .then(res => {
              _this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.$refs.editForm.resetFields();
                _this.clearEditForm();
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
