<template>
  <div>
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-form ref="form" :model="queryForm" :inline="true" label-width="80px" :size="size" class="search-form" @submit.native.prevent>
          <el-col :span="24" style="padding-bottom: 0px;">
            <el-form-item label="防护用品">
              <el-radio-group v-model="queryForm.cat_cd" :size="size" @change="queryForm.supplies_nm='';getList()">
                <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.id">{{item.nmCn}}</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="用品名称" v-if="queryForm.cat_cd">
              <el-select v-model="queryForm.supplies_nm" placeholder="请选择用品名称" @change="getList()">
                <el-option v-for="item in searchMaterialTypeList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
              <!-- <el-radio-group v-model="queryForm.supplies_nm" :size="size" @change="getList()">
                <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.id">{{item.nmCn}}</el-radio-button>
              </el-radio-group> -->
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">查询</el-button>
              <el-button type="success" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
    <!--列表-->
    <el-table class="el-table" border :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :height="tableHeight">
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column prop="catNmCn" label="物资类别"></el-table-column>
      <el-table-column prop="suppliesNm" label="用品名称"></el-table-column>
      <el-table-column prop="suppliesNum" label="储备数量"></el-table-column>
      <el-table-column prop="reserveUnit" label="储备单位" width="120"></el-table-column>
      <el-table-column prop="contactPerson" label="联系人">
        <template slot-scope="scope">
          {{scope.row.contactPerson}}<span v-if="scope.row.mobile"> （{{scope.row.mobile}}）</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="mobile" label="联系方式"></el-table-column> -->
      <el-table-column prop="address" label="单位地址"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]" :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </el-col>

    <!-- 新增/编辑 -->
    <el-dialog :title="(editForm.id ? '编辑' : '新增') + '应急救援物资信息'" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" :loading="dialogLoading" label-width="80px" size="small">
        <el-form-item label="物资类别" :rules="$rulesFilter({ required: true })" prop="catCd">
          <!-- <el-radio-group v-model="editForm.catCd">
            <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
          </el-radio-group> -->
          <el-select v-model="editForm.catCd" filterable placeholder="请选择应急救援物资类别" clearable @change="setCatNmCn">
            <el-option v-for="item in typeList" :key="item.catCd" :label="item.nmCn" :value="item.cd"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用品名称" :rules="$rulesFilter({ required: true })" prop="suppliesNm">
          <!-- <el-select v-model="editForm.suppliesNm" filterable placeholder="请选择" clearable>
            <el-option v-for="item in selectedMaterialTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option> 
          </el-select>-->
          <el-autocomplete class="inline-input" v-model="editForm.suppliesNm" :fetch-suggestions="querySuppliesNm" placeholder="请输入用品名称" @select="handleSelectSuppliesNm">
            <template slot-scope="{ item }">
              <div class="name">{{ item }}</div>
            </template>
          </el-autocomplete>
          <!-- <el-input type="text" v-model="editForm.suppliesNm" laceholder="请输入单位名称"></el-input> -->
        </el-form-item>
        <el-form-item label="储备数量" prop="suppliesNum">
          <el-input type="number" v-model="editForm.suppliesNum" laceholder="请输入储备数量" clearable></el-input>
        </el-form-item>
        <el-form-item label="储备单位" prop="reserveUnit">
          <el-input type="text" v-model="editForm.reserveUnit" laceholder="请输入储备单位" clearable></el-input>
        </el-form-item>
        <el-form-item label="联系人名" prop="contactPerson">
          <el-input type="text" v-model="editForm.contactPerson" laceholder="请输入联系人名" clearable></el-input>
        </el-form-item>
        <el-form-item label="单位地址" prop="address">
          <el-input v-model="editForm.address" laceholder="请输入单位地址"></el-input>
        </el-form-item>
        <el-form-item label="经纬度" prop="location" :rules="$rulesFilter({ required: true })">
          <set-point-lat-and-lon v-model="editForm.location" laceholder="请选择单位地址所在经纬度"></set-point-lat-and-lon>
        </el-form-item>
        <el-form-item label="联系电话" prop="mobile" :rules="$rulesFilter({ required: true, type: 'mobile' })">
          <el-input type="text" v-model="editForm.mobile" laceholder="请输入联系电话"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="editformSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/emergency";
import setPointLatAndLon from "@/components/baiduMap/setPointLatAndLon";
export default {
  name: "resource-experts",
  components: {
    setPointLatAndLon
  },
  data() {
    return {
      size: "small",
      queryForm: {
        cat_cd: "",
        supplies_nm: ""
      },
      tableHeight: Tool.getTableHeight() - 30,
      typeList: [],
      materialTypeList: [],
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },

      dialogLoading: false,
      dialogVisible: false,
      editForm: {
        id: "",
        catCd: "",
        suppliesNm: "",
        suppliesNum: "",
        reserveUnit: "",
        contactPerson: "",
        address: "",
        location: "",
        mobile: ""
      }
    };
  },
  computed: {
    searchMaterialTypeList() {
      let _this = this;
      let res = [];
      if (this.queryForm.cat_cd) {
        res = this.materialTypeList.filter(item => {
          return item.cd === _this.queryForm.cat_cd;
        });
      } else {
      }
      return res.length ? res[0].subItems : [];
    },
    selectedMaterialTypeList() {
      let _this = this;
      let res = [];
      if (this.editForm.catCd) {
        res = this.materialTypeList.filter(item => {
          return item.cd === _this.editForm.catCd;
        });
      } else {
      }
      return res.length ? res[0].subItems : [];
    }
  },
  created() {
    this.getMaterialTypeList();
    this.getEmergencyMaterialTypeDetail();
    this.getList();
  },
  mounted: function() {
    const _this = this;
    _this.tableHeight = Tool.getTableHeight() - 30;
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight() - 30;
    });
  },
  methods: {
    getMaterialTypeList() {
      $http
        .getEmergencyMaterialTypeList()
        .then(response => {
          if (response.code == 0) {
            this.typeList = response.data;
          } else {
            this.typeList = [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getEmergencyMaterialTypeDetail() {
      $http
        .getEmergencyMaterialTypeDetail()
        .then(response => {
          if (response.code == 0) {
            this.materialTypeList = response.data;
          } else {
            this.materialTypeList = [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      let filters = { groupOp: "AND", rules: [] };
      param = param || Object.assign({}, this.pagination);
      delete param.total;
      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "cn",
            data: _this.queryForm[filed]
          };
          if (filed == "cat_cd") {
            rule.op = "eq";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;
      this.listLoading = true;
      $http
        .getEmergencyMaterialList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    refresh() {
      this.pagination.page = 1;
      this.getList();
    },
    setCatNmCn(val) {
      let res = this.typeList.filter(item => {
        return item.cd === val;
      });
      if (res.length) {
        this.editForm.catNmCn = res[0].nmCn;
      } else {
        this.editForm.catNmCn = "";
      }
      this.editForm.suppliesNm = "";
    },
    querySuppliesNm(queryString, cb) {
      var selectedMaterialTypeList = this.selectedMaterialTypeList;
      var results = queryString
        ? selectedMaterialTypeList.filter(item => {
            return item.indexOf(queryString) >= 0;
          })
        : selectedMaterialTypeList;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    handleSelectSuppliesNm(item) {
      if (item) {
        this.editForm.suppliesNm = item;
      }
    },
    clearEditForm(row) {
      let _this = this;
      let keys = Object.keys(this.editForm);
      keys.forEach(key => {
        _this.$set(_this.editForm, key, row && row[key] ? row[key] : "");
      });
    },
    add() {
      this.clearEditForm();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm.resetFields();
      });
    },
    update(row) {
      this.clearEditForm(row);
      this.dialogVisible = true;
    },
    del(row) {
      let _this = this;
      this.$confirm("您确认删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http
            .delEmergencyMaterial([row.id])
            .then(res => {
              this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    editformSubmit() {
      let _this = this;
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.dialogLoading = true;
          $http[
            _this.editForm.id ? "updEmergencyMaterial" : "addEmergencyMaterial"
          ](_this.editForm)
            .then(res => {
              _this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.$refs.editForm.resetFields();
                _this.clearEditForm();
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
// .search-form /deep/ {
//   .el-form-item {
//     margin-bottom: 5px;
//   }
// }
</style>
