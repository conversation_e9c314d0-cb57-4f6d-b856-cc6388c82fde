<template>
    <div>
         <simple-table :tableTitle="tableTitle" :tableHeader="tableHeader" :tablePage="tablePage"></simple-table>       
    </div>
</template>
<script>
import * as $http from "@/api/emergency"
import SimpleTable from '@/components/SimpleTable'
export default {
    data(){
        return {
            tableHeader:[
                {name:"名称",field:"name"},
                {name:"距离",field:"distants"},
                {name:"地址",field:"address"}
            ],
            tableTitle: null,
            tablePage:{
                list:[],
                currPage:0,
                pageSize:100,
                totalPage:0
            }
        }
    },
    components:{
        SimpleTable
    },
    methods:{
        //获取周边场所设施信息
        getPoiList(type,name,radiusData){
          let radius = radiusData ? radiusData:1000
          this.tableTitle = `<h5 style="color:#fff;margin-bottom:5px;">${name}，附近${radius}米内的POI信息</h5>`
          if(type == 'entp'){
            let param = {
                entpname: name,
                radius: radius
            }
            $http.getEntpnamepoi(param).then(response => {
                if (response.code === 0 && response.data.poi) {
                    this.tablePage.list = response.data.poi
                } 
            }); 
          }else if(type == 'vec'){
            let param = {
                vecno: name,
                radius: radius
            }
            $http.getVecnopoi(param).then(response => {
                if (response.code === 0 && response.data.poi) {
                    this.tablePage.list = response.data.poi
                } 
            }); 
          }
        }
    }
}
</script>