<template>
  <div class="app-main-content">
    <!-- <div class="grid-search-bar clearfix">
      <el-row>
        <el-form ref="form" :model="queryForm" :inline="true" label-width="100px" size="mini" @submit.native.prevent>
          <el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
            <el-form-item>
              <el-input v-model="queryForm.drill_nm" placeholder="请输入预案名称" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker type="daterange" value-format="yyyy-MM-dd mm:dd:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="演练开始时间" end-placeholder="演练结束时间" v-model="queryForm.drill_tm" placeholder="请输入演练时间" @change="getList()" clearable></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryForm.address" placeholder="请输入演练地点" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryForm.manager" placeholder="请输入总指挥" @keyup.enter.native="getList()" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">查询</el-button>
              <el-button type="success" @click="add()">新增</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div> -->
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :max-height="tableHeight">
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column prop="drillNm" label="预案名称"></el-table-column>
      <el-table-column prop="drillTm" label="演练时间"></el-table-column>
      <el-table-column prop="catCd" label="预演类型">
        <template slot-scope="scope">
          <template v-for="item in typeList">
            <span v-if="item.cd===scope.row.catCd" :key="item.cd">{{item.nmCn}}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="演练地点"></el-table-column>
      <el-table-column prop="manager" label="总指挥"></el-table-column>
      <el-table-column prop="orgUnit" label="组织单位"></el-table-column>
      <el-table-column prop="joinUnit" label="参演单位"></el-table-column>
      <el-table-column prop="personNum" label="参加人数"></el-table-column>
      <el-table-column prop="drillUrl" label="演练视频">
        <template slot-scope="scope">
          <template v-for="(item,index) in (scope.row.drillUrl?scope.row.drillUrl.split(','):[])">
            <el-button size="mini" v-if="item" @click="openUrl(item)" :key="index">查看</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="drillRecord" label="演练记录">
        <template slot-scope="scope">
          <template v-for="(item,index) in (scope.row.drillRecord?scope.row.drillRecord.split(','):[])">
            <el-button size="mini" v-if="item" @click="openUrl(item)" :key="index">下载</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" title="编辑" @click="update(scope.row)">编辑</el-button>
          <el-button type="text" title="删除" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="success" size="small" @click="add()">新增</el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]" :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </div>
    <!-- 新增/编辑 -->
    <el-dialog :title="(editForm.id ? '编辑' : '新增') + '应急演练记录信息'" :visible.sync="dialogVisible" :close-on-click-modal="false" width="80%">
      <el-form :model="editForm" ref="editForm" :loading="dialogLoading" label-width="80px" size="small">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="演练时间" :rules="$rulesFilter({ required: true })" prop="drillTm">
              <el-date-picker type="datetime" value-format="yyyy-MM-dd HH:mm:dd" v-model="editForm.drillTm" placeholder="请选择演练时间" @change="getList()" clearable></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="演练类型" :rules="$rulesFilter({ required: true })" prop="catNm">
              <!-- <el-radio-group v-model="editForm.catCd">
            <el-radio-button :label="item.cd" v-for="item in typeList" :key="item.cd">{{item.nmCn}}</el-radio-button>
          </el-radio-group>
          <el-select v-model="editForm.catCd" filterable placeholder="请选择应急演练记录类别" clearable>
            <el-option v-for="item in typeList" :key="item.cd" :label="item.nmCn" :value="item.cd"></el-option>
          </el-select> -->
              <el-input type="text" v-model="editForm.catNm" laceholder="请输入演练类型"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="预案名称" :rules="$rulesFilter({ required: true })" prop="drillNm">
              <el-input type="text" v-model="editForm.drillNm" laceholder="请输入预案名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="演练地点" prop="address">
              <el-input type="text" v-model="editForm.address" laceholder="请输入演练地点"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="总指挥" prop="manager">
              <el-input v-model="editForm.manager" laceholder="请输入总指挥"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="组织单位" prop="orgUnit">
              <el-input type="text" v-model="editForm.orgUnit" laceholder="请输入组织单位"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="参演单位" prop="joinUnit">
              <el-input type="text" v-model="editForm.joinUnit" laceholder="请输入参演单位"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="参演人数" prop="personNum">
              <el-input type="number" v-model="editForm.personNum" laceholder="请输入参演人数"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="演练视频" prop="drillUrl">
              <file-upload v-model="editForm.drillUrl" :limit="1" :acceptFileType="['wmv','asf','asx','mp3','mp4']"></file-upload>
              <!-- <el-input type="textarea" :rows="6" v-model="editForm.drillUrl" laceholder="请输入救援装备"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-form-item label="演练记录" prop="drillRecord">
              <file-upload v-model="editForm.drillRecord"></file-upload>
              <!-- <el-input type="textarea" :rows="6" v-model="editForm.drillRecord" laceholder="请输入救援装备"></el-input> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="editformSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/emergency";
import fileUpload from "@/components/FileUpload";
export default {
  name: "resource-experts",
  components: {
    fileUpload
  },
  data() {
    return {
      queryForm: {
        drill_nm: "",
        drill_tm: "",
        address: "",
        manager: ""
      },
      tableHeight: 500,
      typeList: [],
      list: [],
      listLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },

      dialogLoading: false,
      dialogVisible: false,
      editForm: {
        id: "",
        drillTm: "",
        catCd: "",
        catNm: "",
        drillNm: "",
        address: "",
        manager: "",
        orgUnit: "",
        joinUnit: "",
        personNum: "",
        drillRecord: "",
        drillUrl: ""
      }
    };
  },
  created() {
    // this.getDrillRecordList();
    this.getList();
  },
  mounted: function() {
    const _this = this;
    this.tableHeight = Tool.getTableHeight();
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight();
    });
  },
  methods: {
    // getDrillRecordList() {
    //   $http
    //     .getDrillRecordTypeList()
    //     .then(response => {
    //       if (response.code == 0) {
    //         this.typeList = response.data;
    //       } else {
    //         this.typeList = [];
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      let filters = { groupOp: "AND", rules: [] };
      param = param || Object.assign({}, this.pagination);
      delete param.total;
      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "cn",
            data: _this.queryForm[filed]
          };
          if (filed == "drill_tm") {
            rule.op = "bt";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;
      this.listLoading = true;
      $http
        .getDrillRecordList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    refresh() {
      this.pagination.page = 1;
      this.getList();
    },
    clearEditForm(row) {
      let _this = this;
      let keys = Object.keys(this.editForm);
      keys.forEach(key => {
        _this.$set(_this.editForm, key, row && row[key] ? row[key] : "");
      });
    },
    add() {
      this.clearEditForm();
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm.resetFields();
      });
    },
    update(row) {
      this.clearEditForm(row);
      this.dialogVisible = true;
    },
    del(row) {
      let _this = this;
      this.$confirm("您确认删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http
            .delDrillRecord([row.id])
            .then(res => {
              this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    editformSubmit() {
      let _this = this;
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.dialogLoading = true;
          $http[_this.editForm.id ? "updDrillRecord" : "addDrillRecord"](
            _this.editForm
          )
            .then(res => {
              _this.dialogVisible = false;
              if (res.code === 0) {
                _this.$message.success("提交成功");
                _this.dialogVisible = false;
                _this.$refs.editForm.resetFields();
                _this.clearEditForm();
                _this.refresh();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.dialogVisible = false;
            });
        }
      });
    },
    openUrl(drillUrl) {
      if (!drillUrl) return;
      window.open(drillUrl, "_blank");
    }
  }
};
</script>

<style lang="scss" scoped></style>
