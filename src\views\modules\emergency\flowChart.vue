<template>
  <!-- 事故发生流程图 如果要自行画图，复制整个页面，修改 initPolygon 方法即可（如要修改，尽量保证通用性，本页面没有模板）-->
  <div>
    <el-radio-group v-model="show">
      <el-radio-button :label="1" :key="1">事故预警</el-radio-button>
      <el-radio-button :label="2" :key="2">气象预警</el-radio-button>
      <el-radio-button :label="3" :key="3">应急救援</el-radio-button>
    </el-radio-group>
    <div v-if="show==1">
      <flowChartEvent></flowChartEvent>
    </div>
    <div v-if="show==2">
      <flowChartMete></flowChartMete>
    </div>
    <div v-if="show==3">
      <flowChartRescue></flowChartRescue>
    </div>
  </div>
</template>

<script>
import flowChartEvent from "./flowChartEvent";
import flowChartMete from "./flowChartMete";
import flowChartRescue from "./flowChartRescue";
export default {
  name: "flowChart",
  components: {
    flowChartEvent,
    flowChartMete,
    flowChartRescue
  },
  data() {
     return {
       show: 1
     }
  },
  mounted() {

  },
  methods: {
 
  },
};
</script>