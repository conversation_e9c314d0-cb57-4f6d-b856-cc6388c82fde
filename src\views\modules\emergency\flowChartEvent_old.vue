<template>
<!-- 事故发生流程图 如果要自行画图，复制整个页面，修改 initPolygon 方法即可（如要修改，尽量保证通用性，本页面没有模板）-->
<div>
  <canvas
    id="myCanvas"
    ref="myCanvas"
    @click="getLeftClick($event)"
    @contextmenu.prevent.stop="getRightClick($event)"
  >
    您的浏览器不支持 HTML5 canvas 标签。
  </canvas>
  <el-card v-show="contextVisible" :style="{width: '320px',position:'fixed',left:contextLeft+'px',top:contextTop+'px'}" >
    <div slot="header" class="clearfix">
      <span>{{currentContext}}</span>
    </div>
    <div :style="{marginBottom:'18px',textAlign:'right'}">
      <el-slider
        v-model="global_text_size"
        :max = "30"
        @change="initCanvas()"
      >
      </el-slider>
    </div>
    <div :style="{marginBottom:'18px',textAlign:'right'}">
      <el-radio-group v-model="formStatus" @change="changeColor">
        <el-radio-button v-for="statusBtn in statusBtns" :label="statusBtn.value" :key="statusBtn.value">{{statusBtn.label}}</el-radio-button>
      </el-radio-group>
    </div>
    <!-- <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('success')" type="success">标为已完成</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('warning')" type="warning">标为进行中</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('default')" type="default">标为未完成</el-button></div> -->
  </el-card>
</div>
</template>

<script>
export default {
  name: "flowChartEvent",
  components: {},
  data() {
    return {
      formStatus: "default",
      statusBtnsAll: {

      },
      statusBtns: [
        {
          label: "未完成",
          value: "default"
        },{
          label:"进行中",
          value:"warning"
        },{
          label:"已完成",
          value:"success"
        },
      ],
      eventArr: [],
      font_color_default: "#000000",
      back_colors:{
        "success":"#5daf34",
        "warning":"#E6A23C",
        "danger":"#F56C6C",
        "default":"#ecf0f5"
      },
      canvas: null,
      ctx: null,
      contextVisible: false,
      contextLeft: null,
      contextTop: null,
      currentContext: null,

      global_center: null,
      global_compont_width: null,
      global_compont_width_half: null,
      global_compont_width_double: null,
      global_compont_height: null,
      global_compont_height_half: null,
      global_compont_height_double: null,
      global_arrow_width: null,
      global_arrow_height: null,
      global_text_size: null,
    };
  },
  mounted() {
    this.resetSize();
    this.initCanvas();
    // 当调整窗口大小时重绘this.canvas
    window.onresize = () => {
      this.resetSize();
      this.initCanvas();
    };
  },
  methods: {
    resetSize() {
      this.global_center = (window.innerWidth - 200) / 2.0;

      //（用于计算起点）普通组件的宽度，它一半的宽度，两倍的宽度
      // this.global_compont_width = (window.innerWidth - 200) * 0.18; 
      // this.global_compont_width_half = (window.innerWidth - 200) * 0.09;
      // this.global_compont_width_double = (window.innerWidth - 200) * 0.36;

      this.global_compont_height = (window.innerHeight - 54) * 0.05;
      this.global_compont_height_half = (window.innerHeight - 54) * 0.025;
      this.global_compont_height_double = (window.innerHeight - 54) * 0.1;

      // 全屏宽度太宽了，改成75%
      this.global_compont_width = (window.innerWidth - 200) * 0.135; 
      this.global_compont_width_half = (window.innerWidth - 200) * 0.0675;
      this.global_compont_width_double = (window.innerWidth - 200) * 0.27;
      

      this.global_arrow_width = 6;
      this.global_arrow_height = 16;

      this.global_text_size = 26;
      this.global_arrow_text_size = 18;
    },
    initCanvas() {
      this.canvas = document.getElementById("myCanvas");
      this.canvas.width = window.innerWidth - 200;
      this.canvas.height = window.innerHeight * 1.3;
      this.initPolygon();
    },
    setClick(x, y, x2, y2, text) {
      this.eventArr.push({ name: text, area: [x, y, x2, y2] });
    },
    getBackGround(text){
      for (let i = 0; i < this.eventArr.length; i++) {
        if(this.eventArr[i].name == text){
          return this.eventArr[i].color || this.back_colors.default;
        }
      }
      return this.back_colors.default;
    },
    getLeftClick($event){
      this.contextVisible = false;
    },
    getRightClick($event) {
      let x = $event.offsetX;
      let y = $event.offsetY;
      for (let i = 0; i < this.eventArr.length; i++) {
        let target = this.eventArr[i];
        let targetArea = target.area;
        if (
          targetArea[0] <= x &&
          targetArea[2] >= x &&
          targetArea[1] <= y &&
          targetArea[3] >= y
        ) {
          this.currentContext = target.name;
          this.formStatus = this.statusBtnsAll[this.currentContext];
          if(!this.formStatus){
            this.formStatus = 'default';
          }
          this.contextLeft = $event.clientX;
          this.contextTop = $event.clientY;
          if(this.contextTop > window.innerHeight - 220){
            this.contextTop = window.innerHeight - 220
          }
          if(this.contextLeft > window.innerWidth - 320){
            this.contextLeft = window.innerWidth - 320
          }
          this.contextVisible = true;
          return false;
        }
      }
      this.contextVisible = false;
      return true;
    },
    changeColor(type){
      this.statusBtnsAll[this.currentContext] = type;
      this.eventArr.find(t=>t.name==this.currentContext).color=this.back_colors[type];
      this.initCanvas();
      this.contextVisible = false;
    },
    drawPolygon(text){
      this.ctx.closePath();
      this.ctx.stroke();
      this.ctx.fillStyle=this.getBackGround(text);
      this.ctx.fill();
      this.ctx.fillStyle=this.font_color_default;
    },
    drawCircle(x, y, r, text) {
      let step = 1 / this.global_compont_width_half;
      this.ctx.beginPath();
      this.ctx.moveTo(x + this.global_compont_width_half, y);
      for (let i = 0; i < 2 * Math.PI; i += step) {
        this.ctx.lineTo(
          x + this.global_compont_width_half * Math.cos(i),
          y + r * Math.sin(i)
        );
      }
      this.drawPolygon(text);
      this.setClick(
        x - this.global_compont_width_half,
        y - r,
        x + this.global_compont_width_half,
        y + r,
        text
      );
      this.drawText(x, y, text);
    },
    drawRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x, y + this.global_compont_height);
      this.ctx.lineTo(x, y);
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width,y+this.global_compont_height,text);
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawDiamond(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + this.global_compont_height);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height_double
      );
      this.ctx.lineTo(
        x + 2 * this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(x, y + this.global_compont_height);//TODO
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width_double,y+this.global_compont_height_double,text);
      this.drawText(
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
    },
    drawComplexRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + 0.3 * this.global_compont_height);
      this.ctx.lineTo(x + 0.07 * this.global_compont_width, y);
      this.ctx.lineTo(x + 0.93 * this.global_compont_width, y);
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.3 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.7 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.93 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.07 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(x, y + 0.7 * this.global_compont_height);
      this.ctx.lineTo(x, y + 0.3 * this.global_compont_height);
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width,y+this.global_compont_height,text);
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawArrow(x, y, len, dir, noArrow) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        y = y - len;
      }
      if (dir == "down") {
        y = y + len;
      }
      if (dir == "left") {
        x = x - len;
      }
      if (dir == "right") {
        x = x + len;
      }
      this.ctx.lineTo(x, y);
      this.ctx.stroke();
      if (!noArrow) {
        this.drawArrowEnd(x, y, len, dir);
      }
    },
    arrowText(x, y, len, dir, textDir, text) {
      if (dir == "up") {
        y = y - len * 0.5;
      }
      if (dir == "down") {
        y = y + len * 0.5;
      }
      if (dir == "left") {
        x = x - len * 0.5;
      }
      if (dir == "right") {
        x = x + len * 0.5;
      }

      if (textDir == "up") {
        y = y - this.global_arrow_text_size;
      }
      if (textDir == "down") {
        y = y + this.global_arrow_text_size;
      }
      if (textDir == "left") {
        x = x - this.global_arrow_text_size*text.length*0.54;
      }
      if (textDir == "right") {
        x = x + this.global_arrow_text_size*text.length*0.54;
      }
      this.drawText(x, y, text, this.global_arrow_text_size);
    },
    drawArrowEnd(x, y, len, dir) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y - this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "down") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y + this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "left") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x - this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      if (dir == "right") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x + this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      this.ctx.fill();
    },
    drawText(x, y, text, size) {
      this.ctx.beginPath();
      this.ctx.textBaseline = "middle"; //设置文本的垂直对齐方式
      this.ctx.textAlign = "center";
      if(!size){
        size = this.global_text_size;
        if(text.length * size > this.global_compont_width){
          size = this.global_compont_width / text.length * 0.8
        }
      }
      this.ctx.font = size + "px Arial";
      this.ctx.fillText(text, x, y);
    },

    initPolygon() {
      this.ctx = this.canvas.getContext("2d");
      this.ctx.lineWidth='4'

      let lastX = this.global_center;
      let lastY = this.global_compont_height_half+20 ;

      this.drawCircle(lastX, lastY, this.global_compont_height_half, "事故发生");

      //向下
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX, lastY, 30, "down", true);

      //向左
      lastY = lastY + 30;
      this.drawArrow(lastX, lastY, this.global_compont_width, "left", true);
      //向右
      this.drawArrow(lastX, lastY, this.global_compont_width, "right", true);

      //（左）再向下
      lastX = lastX - this.global_compont_width;
      this.drawArrow(lastX, lastY, this.global_compont_height - this.global_arrow_height, "down");

      //（右）再向下
      lastX = lastX + this.global_compont_width_double;
      this.drawArrow(lastX, lastY, this.global_compont_height - this.global_arrow_height, "down");

      // (左) 巡逻组方块
      lastX = lastX - this.global_compont_width_double - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"巡逻组");

      // (右) 分局指挥接警
      lastX = lastX + this.global_compont_width_double ;
      this.drawRect(lastX,lastY,"分局指挥接警");

      //（左）再向下
      lastX = lastX - this.global_compont_width - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);

      //（右）再向下
      lastX = lastX + this.global_compont_width_double;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);

      //（左）向右
      lastX = lastX - this.global_compont_width_double;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_width, "right", true);

      //（右）向左
      lastX = lastX + this.global_compont_width_double;
      this.drawArrow(lastX, lastY, this.global_compont_width, "left", true);

      //合并，下箭头
      lastX = lastX - this.global_compont_width;
      this.drawArrow(lastX, lastY, this.global_compont_height - this.global_arrow_height, "down");
      this.arrowText(lastX, lastY, this.global_compont_height - this.global_arrow_height, "down", "left", "现场信息反馈");

      //大队指挥中心
      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"大队指挥中心");

      //下箭头
      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height - this.global_arrow_height, "down");


      //事态判断
      lastX = lastX - this.global_compont_width;
      lastY = lastY + this.global_compont_height;
      this.drawDiamond(lastX, lastY, "事态判断");

      //左分支
      this.leftSingle(lastX,lastY);

      //下分支
      //下箭头
      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_double;
      this.drawArrow(lastX, lastY, this.global_compont_height*6 - this.global_arrow_height, "down");
      this.arrowText(lastX, lastY, this.global_compont_height, "down", "right","存在危险性:");
      this.arrowText(lastX, lastY, this.global_compont_height*2, "down", "right","易燃、易爆、剧毒等;");
      this.arrowText(lastX, lastY, this.global_compont_height*3, "down", "right","影响范围扩散;");
      this.arrowText(lastX, lastY, this.global_compont_height*4, "down", "right","车辆技术特征;");
      this.arrowText(lastX, lastY, this.global_compont_height*5, "down", "right","是否有现场人员伤亡;");
      this.arrowText(lastX, lastY, this.global_compont_height*6, "down", "right","次生伤害等;");
      this.arrowText(lastX, lastY, this.global_compont_height*7, "down", "right","......");

      lastY = lastY + this.global_compont_height * 4;
      this.drawArrow(lastX, lastY, this.global_compont_width_half - this.global_arrow_height, "right");
      this.drawArrow(lastX, lastY, this.global_compont_width_half - this.global_arrow_height, "left");


      //联系值班领导
      lastX = lastX - this.global_compont_width_half - this.global_compont_width;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX,lastY,"联系值班领导");

      //全员到岗到位
      lastX = lastX + this.global_compont_width_double;
      this.drawRect(lastX,lastY,"全员到岗到位");

      //应急启动
      lastX = lastX - this.global_compont_width;
      lastY = lastY + this.global_compont_height*2.5;
      this.drawRect(lastX,lastY,"应急启动");

      this.rightSingle(lastX,lastY);

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      //进入系统应急指挥模块
      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"进入系统应急指挥模块");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width_half-this.global_arrow_height,"left");

      //通知
      lastX = lastX - this.global_compont_width * 1.5;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX,lastY,"系统微信、短信等自动通知相关单位");

      lastX = lastX + this.global_compont_width * 2.5;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width_half-this.global_arrow_height,"right");

      //申请支援
      lastX = lastX + this.global_compont_width_half;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX,lastY,"申请支援");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      //扩大应急
      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"扩大应急");

      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width_half,"right",true);

      lastX = lastX - this.global_compont_width_double;
      lastY = lastY - this.global_compont_height_half*3;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX,lastY,"巡逻组到达现场");
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"交通管制");
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"撤离疏散");
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"其他措施");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"应急结束");

      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width_double,"right",true);
      lastX = lastX + this.global_compont_width_double;
      this.drawArrow(lastX,lastY,this.global_compont_height*8-this.global_arrow_height,"up");
    },

    leftSingle(lastX, lastY){
      //左箭头
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_width - this.global_arrow_height, "left");

      //交警现场处置
      lastX = lastX - this.global_compont_width_double;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX,lastY,"交警现场处置");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height*15.5,"down",true);

      lastY = lastY + this.global_compont_height*15.5;
      this.drawArrow(lastX,lastY,this.global_compont_width_double-this.global_arrow_height,"right");
    },
    rightSingle(lastX, lastY){
      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX, lastY, this.global_compont_width_half*3-this.global_arrow_height,"right");
      lastX = lastX + this.global_compont_width_half*3;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX,lastY,"信息上报");
    }
  },
};
</script>