<template>
  <!-- 事故发生流程图 如果要自行画图，复制整个页面，修改 initPolygon 方法即可（如要修改，尽量保证通用性，本页面没有模板）-->
  <div>
    <canvas
      id="myCanvas"
      ref="myCanvas"
      @click="getLeftClick($event)"
      @contextmenu.prevent.stop="getRightClick($event)"
    >
      您的浏览器不支持 HTML5 canvas 标签。
    </canvas>
    <el-card
      v-show="contextVisible"
      :style="{
        width: '320px',
        position: 'fixed',
        left: contextLeft + 'px',
        top: contextTop + 'px',
      }"
    >
      <div slot="header" class="clearfix">
        <span>{{ currentContext }}</span>
      </div>
      <div :style="{ marginBottom: '18px', textAlign: 'right' }">
        <el-slider v-model="global_text_size" :max="30" @change="initCanvas()">
        </el-slider>
      </div>
      <div :style="{ marginBottom: '18px', textAlign: 'right' }">
        <el-radio-group v-model="formStatus" @change="changeColor">
          <el-radio-button
            v-for="statusBtn in statusBtns"
            :label="statusBtn.value"
            :key="statusBtn.value"
            >{{ statusBtn.label }}</el-radio-button
          >
        </el-radio-group>
      </div>
      <!-- <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('success')" type="success">标为已完成</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('warning')" type="warning">标为进行中</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('default')" type="default">标为未完成</el-button></div> -->
    </el-card>
  </div>
</template>

<script>
export default {
  name: "flowChartMete",
  components: {},
  data() {
    return {
      isSecond: false,
      arrStatic: [],
      formStatus: "default",
      statusBtnsAll: {},
      statusBtns: [
        {
          label: "未完成",
          value: "default",
        },
        {
          label: "进行中",
          value: "warning",
        },
        {
          label: "已完成",
          value: "success",
        },
      ],
      eventArr: [],
      font_color_default: "#000000",
      back_colors: {
        success: "#5daf34",
        warning: "#E6A23C",
        danger: "#F56C6C",
        default: "#ecf0f5",
      },
      canvas: null,
      ctx: null,
      contextVisible: false,
      contextLeft: null,
      contextTop: null,
      currentContext: null,

      global_center: null,
      global_compont_width: null,
      global_compont_width_half: null,
      global_compont_width_double: null,
      global_compont_height: null,
      global_compont_height_half: null,
      global_compont_height_double: null,
      global_arrow_width: null,
      global_arrow_height: null,
      global_text_size: null,
    };
  },
  mounted() {
    this.resetSize();
    this.initCanvas();
    // 当调整窗口大小时重绘this.canvas
    window.onresize = () => {
      this.resetSize();
      this.initCanvas();
    };
  },
  methods: {
    resetSize() {
      this.global_center = (window.innerWidth - 200) / 2.0;

      //（用于计算起点）普通组件的宽度，它一半的宽度，两倍的宽度
      // this.global_compont_width = (window.innerWidth - 200) * 0.18;
      // this.global_compont_width_half = (window.innerWidth - 200) * 0.09;
      // this.global_compont_width_double = (window.innerWidth - 200) * 0.36;

      this.global_compont_height = (window.innerHeight - 54) * 0.05;
      this.global_compont_height_half = (window.innerHeight - 54) * 0.025;
      this.global_compont_height_double = (window.innerHeight - 54) * 0.1;

      // 全屏宽度太宽了，改成75%
      this.global_compont_width = (window.innerWidth - 200) * 0.135;
      this.global_compont_width_half = (window.innerWidth - 200) * 0.0675;
      this.global_compont_width_double = (window.innerWidth - 200) * 0.27;

      this.global_arrow_width = 6;
      this.global_arrow_height = 16;

      this.global_text_size = 26;
      this.global_arrow_text_size = 18;
    },
    initCanvas() {
      this.canvas = document.getElementById("myCanvas");
      this.canvas.width = window.innerWidth - 200;
      this.canvas.height = window.innerHeight * 1.5;
      if(this.isSecond){
        this.arrStatic=[];
      }
      this.initPolygon();
    },
    setClick(x, y, x2, y2, text) {
      if(this.isSecond){
        //如果是第二次，重新设置text后面的&
        if(this.arrStatic.filter(t=>t.name==text).length > 0){
          text = text+"&"
          return this.setClick(x, y, x2, y2, text);
        }
        this.arrStatic.push({name:text})
        return text;
      }

      if(this.eventArr.filter(t=>t.name==text).length > 0){
        text = text+"&"
        return this.setClick(x, y, x2, y2, text);
      }else{
        this.eventArr.push({ name: text, area: [x, y, x2, y2] });
      }
      return text;
    },
    getBackGround(text) {
      for (let i = 0; i < this.eventArr.length; i++) {
        if (this.eventArr[i].name == text) {
          return this.eventArr[i].color || this.back_colors.default;
        }
      }
      return this.back_colors.default;
    },
    getLeftClick($event) {
      this.contextVisible = false;
    },
    getRightClick($event) {
      let x = $event.offsetX;
      let y = $event.offsetY;
      for (let i = 0; i < this.eventArr.length; i++) {
        let target = this.eventArr[i];
        let targetArea = target.area;
        if (
          targetArea[0] <= x &&
          targetArea[2] >= x &&
          targetArea[1] <= y &&
          targetArea[3] >= y
        ) {
          this.currentContext = target.name;
          this.formStatus = this.statusBtnsAll[this.currentContext];
          if (!this.formStatus) {
            this.formStatus = "default";
          }
          this.contextLeft = $event.clientX;
          this.contextTop = $event.clientY;
          if (this.contextTop > window.innerHeight - 220) {
            this.contextTop = window.innerHeight - 220;
          }
          if (this.contextLeft > window.innerWidth - 320) {
            this.contextLeft = window.innerWidth - 320;
          }
          this.contextVisible = true;
          return false;
        }
      }
      this.contextVisible = false;
      return true;
    },
    changeColor(type) {
      this.statusBtnsAll[this.currentContext] = type;
      let t = this.eventArr.find(
        (t) => t.name == this.currentContext
      ).color = this.back_colors[type];
      this.initCanvas();
      this.contextVisible = false;
    },
    drawPolygon(text) {
      this.ctx.closePath();
      this.ctx.stroke();
      this.ctx.fillStyle = this.getBackGround(text);
      this.ctx.fill();
      this.ctx.fillStyle = this.font_color_default;
    },
    drawCircle(x, y, r, text) {
      let step = 1 / this.global_compont_width_half;
      this.ctx.beginPath();
      this.ctx.moveTo(x + this.global_compont_width_half, y);
      for (let i = 0; i < 2 * Math.PI; i += step) {
        this.ctx.lineTo(
          x + this.global_compont_width_half * Math.cos(i),
          y + r * Math.sin(i)
        );
      }
      text = this.setClick(
        x - this.global_compont_width_half,
        y - r,
        x + this.global_compont_width_half,
        y + r,
        text
      );
      this.drawPolygon(text);
      this.drawText(x, y, text);
    },
    drawRect(x, y, text, rateX, rateY) {
      if (!rateX) {
        rateX = 1;
      }
      if (!rateY) {
        rateY = 1;
      }
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.lineTo(x + this.global_compont_width * rateX, y);
      this.ctx.lineTo(
        x + this.global_compont_width * rateX,
        y + this.global_compont_height * rateY
      );
      this.ctx.lineTo(x, y + this.global_compont_height * rateY);
      this.ctx.lineTo(x, y);
      text = this.setClick(
        x,
        y,
        x + this.global_compont_width * rateX,
        y + this.global_compont_height * rateY,
        text
      );
      this.drawPolygon(text);

      this.drawText(
        x + this.global_compont_width_half * rateX,
        y + this.global_compont_height_half * rateY,
        text
      );
    },
    drawDiamond(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + this.global_compont_height);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height_double
      );
      this.ctx.lineTo(
        x + 2 * this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(x, y + this.global_compont_height); //TODO
      text = this.setClick(
        x,
        y,
        x + this.global_compont_width_double,
        y + this.global_compont_height_double,
        text
      );
      this.drawPolygon(text);
      this.drawText(
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
    },
    drawComplexRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + 0.3 * this.global_compont_height);
      this.ctx.lineTo(x + 0.07 * this.global_compont_width, y);
      this.ctx.lineTo(x + 0.93 * this.global_compont_width, y);
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.3 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.7 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.93 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.07 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(x, y + 0.7 * this.global_compont_height);
      this.ctx.lineTo(x, y + 0.3 * this.global_compont_height);

      text = this.setClick(
        x,
        y,
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
      this.drawPolygon(text);
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawArrow(x, y, len, dir, noArrow) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        y = y - len;
      }
      if (dir == "down") {
        y = y + len;
      }
      if (dir == "left") {
        x = x - len;
      }
      if (dir == "right") {
        x = x + len;
      }
      this.ctx.lineTo(x, y);
      this.ctx.stroke();
      if (!noArrow) {
        this.drawArrowEnd(x, y, len, dir);
      }
    },
    arrowText(x, y, len, dir, textDir, text) {
      if (dir == "up") {
        y = y - len * 0.5;
      }
      if (dir == "down") {
        y = y + len * 0.5;
      }
      if (dir == "left") {
        x = x - len * 0.5;
      }
      if (dir == "right") {
        x = x + len * 0.5;
      }

      if (textDir == "up") {
        y = y - this.global_arrow_text_size;
      }
      if (textDir == "down") {
        y = y + this.global_arrow_text_size;
      }
      if (textDir == "left") {
        x = x - this.global_arrow_text_size * text.length * 0.54;
      }
      if (textDir == "right") {
        x = x + this.global_arrow_text_size * text.length * 0.54;
      }
      this.drawText(x, y, text, this.global_arrow_text_size);
    },
    drawArrowEnd(x, y, len, dir) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y - this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "down") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y + this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "left") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x - this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      if (dir == "right") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x + this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      this.ctx.fill();
    },
    drawText(x, y, text, size) {
      let real_text = text;
      if(real_text.match(/&+/)){
        real_text = real_text.substring(0,real_text.match(/&+/).index)
      }
      this.ctx.beginPath();
      this.ctx.textBaseline = "middle"; //设置文本的垂直对齐方式
      this.ctx.textAlign = "center";
      if (!size) {
        size = this.global_text_size;
        if (real_text.length * size > this.global_compont_width) {
          size = (this.global_compont_width / real_text.length) * 0.8;
        }
      }
      this.ctx.font = size + "px Arial";
      this.ctx.fillText(real_text, x, y);
    },

    virtualLineRect(x, y, yRate) {
      x = x - this.global_compont_width * 2;
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.save();
      this.ctx.lineDashOffset = 3;
      this.ctx.setLineDash([20, 5]);
      this.ctx.lineTo(x + this.global_compont_width * 4, y);
      this.ctx.lineTo(
        x + this.global_compont_width * 4,
        y + this.global_compont_height * yRate
      );
      this.ctx.lineTo(x, y + this.global_compont_height * yRate);
      this.ctx.lineTo(x, y);
      this.drawPolygon();
      this.ctx.restore();
    },

    initPolygon() {
      this.ctx = this.canvas.getContext("2d");
      this.ctx.lineWidth = "4";

      let lastX = this.global_center;
      let lastY = this.global_compont_height_half + 20;

      this.drawCircle(
        lastX,
        lastY,
        this.global_compont_height_half,
        "恶劣天气应急预案"
      );
      //向下
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "恶劣天气发生前");
      //向下
      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "稽查大队");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);

      //左右各3
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_width * 3, "left", true);
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 3,
        "right",
        true
      );

      lastX = lastX - this.global_compont_width * 3;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width * 2;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width * 2.4;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width * 1.6;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width*0.3;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "大仓系统",0.6);

      lastX = lastX - this.global_compont_width*0.2 - this.global_compont_width * 1.6;
      this.drawRect(lastX, lastY, "值班巡逻中队");

      lastX = lastX - this.global_compont_width * 2.4;
      this.drawRect(lastX, lastY, "指挥中心");

      lastX = lastX - this.global_compont_width * 1.8;
      this.drawRect(lastX, lastY, "办证大厅",0.6);

      lastX = lastX + this.global_compont_width*0.3;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX + this.global_compont_width * 2;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half,
        "down",
        true
      );

      //指挥中心箭头
      lastX = lastX - this.global_compont_width * 0.8;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 1.6,
        "right",
        true
      );

      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half - this.global_arrow_height,
        "down"
      );

      lastX = lastX + this.global_compont_width * 0.8;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width * 0.8;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half - this.global_arrow_height,
        "down"
      );


      //值班巡逻箭头
      lastX = lastX + this.global_compont_width * 1.6;
      lastY = lastY - this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half,
        "down",
        true
      );

      lastX = lastX - this.global_compont_width * 0.6;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 1.2,
        "right",
        true
      );
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width * 1.2;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half - this.global_arrow_height,
        "down"
      );

      lastX = lastX + this.global_compont_width;
      lastY = lastY - this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width * 6.5;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 1, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 0.5,
        "提前将特殊情况"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 1.5,
        "通过QQ、微信群"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 2.5,
        "等通知运输、生"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 3.5,
        "产企业，建议合理"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 4.5,
        "安排运输生产计划"
      );

      lastX = lastX + this.global_compont_width * 1.3;

      this.drawRect(lastX, lastY, "", 0.7, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 0.5,
        "根据特殊气"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 1.5,
        "象时间，制"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 2.5,
        "定值班表，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 3.5,
        "强化值班力"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 4.5,
        "量至平时2倍"
      );

      lastX = lastX + this.global_compont_width * 0.8;
      this.drawRect(lastX, lastY, "", 0.7, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 0.5,
        "联系高速"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 1.5,
        "交警，确"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 2.5,
        "定高速路"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 3.5,
        "口等通行"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 4.5,
        "情况"
      );

      lastX = lastX + this.global_compont_width * 0.85;
      this.drawRect(lastX, lastY, "", 0.7, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 0.5,
        "提前联系应"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 1.5,
        "急物资储备"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 2.5,
        "单位，做好"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 3.5,
        "物资清点、"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 4.5,
        "整备"
      );


      lastX = lastX + this.global_compont_width ;
      this.drawRect(lastX, lastY, "", 0.7, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 1,
        "加强巡逻力量"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 2,
        "至平日1.5倍"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 3,
        "并制定重点路"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 4,
        "段巡逻计划"
      );

      lastX = lastX + this.global_compont_width * 1.2;
      this.drawRect(lastX, lastY, "", 0.7, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 0.5,
        "根据气候类"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 1.5,
        "型，携带不同"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 2.5,
        "应急救援装"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 3.5,
        "备，巡逻并上"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.35,
        lastY + this.global_compont_height * 4.5,
        "报路况"
      );

      lastX = lastX + this.global_compont_width*0.85;
      this.drawRect(lastX, lastY, "", 1, 5);
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 0.5,
        "通过自动短信系统"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 1.5,
        "通知危运车辆司机"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 2.5,
        "押运员，提前做好"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 3.5,
        "特殊天气下车辆"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 4.5,
        "的准备工作"
      );

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height*5;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"down",true);
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width*6,"left",true);

      lastX = lastX - this.global_compont_width*6;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"up",true);

      lastX = lastX + this.global_compont_width*3;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX, lastY, "恶劣天气发生时");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width*3,"left",true);
      this.drawArrow(lastX,lastY,this.global_compont_width*2.7,"right",true);

      lastX = lastX + this.global_compont_width*2.7;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width*5.7;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width * 0.3;
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX, lastY, "办证大厅",0.6);

      lastX = lastX + this.global_compont_width*3;
      this.drawRect(lastX, lastY, "指挥中心",0.6);

      lastX = lastX + this.global_compont_width*2.5;
      this.drawRect(lastX, lastY, "值班巡逻中队");

      lastX = lastX + this.global_compont_width * 0.5;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"down", true);

      lastX = lastX - this.global_compont_width * 2.7;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"down", true);

      lastX = lastX - this.global_compont_width * 3;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width*0.5;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 1,5);
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 0.5,
        "根据指挥中心反"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 1.5,
        "馈，实时通告生"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 2.5,
        "产、运输企业天"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 3.5,
        "气变化情况，提"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.5,
        lastY + this.global_compont_height * 4.5,
        "出合理建议"
      );

      lastX = lastX + this.global_compont_width * 1.5;
      lastY = lastY - this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      this.drawArrow(lastX,lastY,this.global_compont_width*3.25,"right",true);
      lastX = lastX + this.global_compont_width * 0.65;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      lastX = lastX + this.global_compont_width * 0.65;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      lastX = lastX + this.global_compont_width * 0.65;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      lastX = lastX + this.global_compont_width * 0.65;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      lastX = lastX + this.global_compont_width * 0.65;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width * (3.25+0.325);
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 0.5,
        "实时跟进高"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1.5,
        "速道路通行"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2.5,
        "状况，及时"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3.5,
        "通知值班巡"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4.5,
        "逻中队"
      );


      lastX = lastX + this.global_compont_width * 0.65;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 0.5,
        "利用巡逻中"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1.5,
        "队汇报信息"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2.5,
        "与卡口监控，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3.5,
        "实时掌握地"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4.5,
        "面道路路况"
      );


      lastX = lastX + this.global_compont_width * 0.65;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 0.5,
        "通过大仓平"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1.5,
        "台，掌握区"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2.5,
        "内在途危运"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3.5,
        "车辆运行情"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4.5,
        "况"
      );

      lastX = lastX + this.global_compont_width * 0.65;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 0.5,
        "安排机动巡"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1.5,
        "逻，如巡逻"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2.5,
        "中队力量不"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3.5,
        "足，可及时"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4.5,
        "协助、支援"
      );

      lastX = lastX + this.global_compont_width * 0.65;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 0.5,
        "实时跟进高"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1.5,
        "速道路通行"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2.5,
        "状况。并指"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3.5,
        "挥巡逻中队"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4.5,
        "进行疏导"
      );

      lastX = lastX + this.global_compont_width * 0.65;
      this.drawRect(lastX,lastY,"",0.63,5);
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 1,
        "值班领导"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 2,
        "在指挥中"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 3,
        "心到岗指"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.315,
        lastY + this.global_compont_height * 4,
        "挥"
      );

      lastX = lastX + this.global_compont_width*(0.65+0.6875);
      lastY = lastY - this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width*0.875,"right",true);
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");
      lastX = lastX + this.global_compont_width*0.875;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width*(0.875+0.4375-0.025);
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX,lastY,"",0.8,5);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1,
        "在海天路、庄南"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2,
        "路各安排一组巡"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3,
        "逻车全天巡逻，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4,
        "实时回复路况"
      );

      lastX = lastX + this.global_compont_width*0.875;
      this.drawRect(lastX,lastY,"",0.8,5);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 0.5,
        "根据高速口通行"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "情况疏导车辆。"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "如蛟川口封道，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "则疏导至临江沙"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "和九龙湖等地"
      );

      lastX = lastX + this.global_compont_width*0.4375;
      lastY = lastY + this.global_compont_height*5;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"down",true);

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX,lastY,this.global_compont_width*6.15,"left",true);

      lastX = lastX - this.global_compont_width*6.15;
      this.drawArrow(lastX,lastY,this.global_compont_height_half,"up",true);

      lastX = lastX + this.global_compont_width*3.075;
      this.drawArrow(lastX,lastY,this.global_compont_height_half-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height_half;
      this.drawRect(lastX,lastY,"恶劣天气解除");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX,lastY,this.global_compont_height-this.global_arrow_height,"down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX,lastY,"恢复正常勤务");
      this.isSecond = true;

    },
  },
};
</script>
