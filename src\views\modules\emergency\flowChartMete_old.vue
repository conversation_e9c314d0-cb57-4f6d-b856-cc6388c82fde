<template>
  <!-- 事故发生流程图 如果要自行画图，复制整个页面，修改 initPolygon 方法即可（如要修改，尽量保证通用性，本页面没有模板）-->
  <div>
    <canvas
      id="myCanvas"
      ref="myCanvas"
      @click="getLeftClick($event)"
      @contextmenu.prevent.stop="getRightClick($event)"
    >
      您的浏览器不支持 HTML5 canvas 标签。
    </canvas>
    <el-card
      v-show="contextVisible"
      :style="{
        width: '320px',
        position: 'fixed',
        left: contextLeft + 'px',
        top: contextTop + 'px',
      }"
    >
      <div slot="header" class="clearfix">
        <span>{{ currentContext }}</span>
      </div>
      <div :style="{ marginBottom: '18px', textAlign: 'right' }">
        <el-slider v-model="global_text_size" :max="30" @change="initCanvas()">
        </el-slider>
      </div>
      <div :style="{ marginBottom: '18px', textAlign: 'right' }">
        <el-radio-group v-model="formStatus" @change="changeColor">
          <el-radio-button
            v-for="statusBtn in statusBtns"
            :label="statusBtn.value"
            :key="statusBtn.value"
            >{{ statusBtn.label }}</el-radio-button
          >
        </el-radio-group>
      </div>
      <!-- <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('success')" type="success">标为已完成</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('warning')" type="warning">标为进行中</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('default')" type="default">标为未完成</el-button></div> -->
    </el-card>
  </div>
</template>

<script>
export default {
  name: "flowChartMete",
  components: {},
  data() {
    return {
      formStatus: "default",
      statusBtnsAll: {},
      statusBtns: [
        {
          label: "未完成",
          value: "default",
        },
        {
          label: "进行中",
          value: "warning",
        },
        {
          label: "已完成",
          value: "success",
        },
      ],
      eventArr: [],
      font_color_default: "#000000",
      back_colors: {
        success: "#5daf34",
        warning: "#E6A23C",
        danger: "#F56C6C",
        default: "#ecf0f5",
      },
      canvas: null,
      ctx: null,
      contextVisible: false,
      contextLeft: null,
      contextTop: null,
      currentContext: null,

      global_center: null,
      global_compont_width: null,
      global_compont_width_half: null,
      global_compont_width_double: null,
      global_compont_height: null,
      global_compont_height_half: null,
      global_compont_height_double: null,
      global_arrow_width: null,
      global_arrow_height: null,
      global_text_size: null,
    };
  },
  mounted() {
    this.resetSize();
    this.initCanvas();
    // 当调整窗口大小时重绘this.canvas
    window.onresize = () => {
      this.resetSize();
      this.initCanvas();
    };
  },
  methods: {
    resetSize() {
      this.global_center = (window.innerWidth - 200) / 2.0;

      //（用于计算起点）普通组件的宽度，它一半的宽度，两倍的宽度
      // this.global_compont_width = (window.innerWidth - 200) * 0.18;
      // this.global_compont_width_half = (window.innerWidth - 200) * 0.09;
      // this.global_compont_width_double = (window.innerWidth - 200) * 0.36;

      this.global_compont_height = (window.innerHeight - 54) * 0.05;
      this.global_compont_height_half = (window.innerHeight - 54) * 0.025;
      this.global_compont_height_double = (window.innerHeight - 54) * 0.1;

      // 全屏宽度太宽了，改成75%
      this.global_compont_width = (window.innerWidth - 200) * 0.135;
      this.global_compont_width_half = (window.innerWidth - 200) * 0.0675;
      this.global_compont_width_double = (window.innerWidth - 200) * 0.27;

      this.global_arrow_width = 6;
      this.global_arrow_height = 16;

      this.global_text_size = 26;
      this.global_arrow_text_size = 18;
    },
    initCanvas() {
      this.canvas = document.getElementById("myCanvas");
      this.canvas.width = window.innerWidth - 200;
      this.canvas.height = window.innerHeight * 1.8;
      this.initPolygon();
    },
    setClick(x, y, x2, y2, text) {
      this.eventArr.push({ name: text, area: [x, y, x2, y2] });
    },
    getBackGround(text) {
      for (let i = 0; i < this.eventArr.length; i++) {
        if (this.eventArr[i].name == text) {
          return this.eventArr[i].color || this.back_colors.default;
        }
      }
      return this.back_colors.default;
    },
    getLeftClick($event) {
      this.contextVisible = false;
    },
    getRightClick($event) {
      let x = $event.offsetX;
      let y = $event.offsetY;
      for (let i = 0; i < this.eventArr.length; i++) {
        let target = this.eventArr[i];
        let targetArea = target.area;
        if (
          targetArea[0] <= x &&
          targetArea[2] >= x &&
          targetArea[1] <= y &&
          targetArea[3] >= y
        ) {
          this.currentContext = target.name;
          this.formStatus = this.statusBtnsAll[this.currentContext];
          if (!this.formStatus) {
            this.formStatus = "default";
          }
          this.contextLeft = $event.clientX;
          this.contextTop = $event.clientY;
          if (this.contextTop > window.innerHeight - 220) {
            this.contextTop = window.innerHeight - 220;
          }
          if (this.contextLeft > window.innerWidth - 320) {
            this.contextLeft = window.innerWidth - 320;
          }
          this.contextVisible = true;
          return false;
        }
      }
      this.contextVisible = false;
      return true;
    },
    changeColor(type) {
      this.statusBtnsAll[this.currentContext] = type;
      this.eventArr.find(
        (t) => t.name == this.currentContext
      ).color = this.back_colors[type];
      this.initCanvas();
      this.contextVisible = false;
    },
    drawPolygon(text) {
      this.ctx.closePath();
      this.ctx.stroke();
      this.ctx.fillStyle = this.getBackGround(text);
      this.ctx.fill();
      this.ctx.fillStyle = this.font_color_default;
    },
    drawCircle(x, y, r, text) {
      let step = 1 / this.global_compont_width_half;
      this.ctx.beginPath();
      this.ctx.moveTo(x + this.global_compont_width_half, y);
      for (let i = 0; i < 2 * Math.PI; i += step) {
        this.ctx.lineTo(
          x + this.global_compont_width_half * Math.cos(i),
          y + r * Math.sin(i)
        );
      }
      this.drawPolygon(text);
      this.setClick(
        x - this.global_compont_width_half,
        y - r,
        x + this.global_compont_width_half,
        y + r,
        text
      );
      this.drawText(x, y, text);
    },
    drawRect(x, y, text, rateX, rateY) {
      if (!rateX) {
        rateX = 1;
      }
      if (!rateY) {
        rateY = 1;
      }
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.lineTo(x + this.global_compont_width * rateX, y);
      this.ctx.lineTo(
        x + this.global_compont_width * rateX,
        y + this.global_compont_height * rateY
      );
      this.ctx.lineTo(x, y + this.global_compont_height * rateY);
      this.ctx.lineTo(x, y);
      this.drawPolygon(text);
      this.setClick(
        x,
        y,
        x + this.global_compont_width * rateX,
        y + this.global_compont_height * rateY,
        text
      );
      this.drawText(
        x + this.global_compont_width_half * rateX,
        y + this.global_compont_height_half * rateY,
        text
      );
    },
    drawDiamond(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + this.global_compont_height);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height_double
      );
      this.ctx.lineTo(
        x + 2 * this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(x, y + this.global_compont_height); //TODO
      this.drawPolygon(text);
      this.setClick(
        x,
        y,
        x + this.global_compont_width_double,
        y + this.global_compont_height_double,
        text
      );
      this.drawText(
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
    },
    drawComplexRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + 0.3 * this.global_compont_height);
      this.ctx.lineTo(x + 0.07 * this.global_compont_width, y);
      this.ctx.lineTo(x + 0.93 * this.global_compont_width, y);
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.3 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.7 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.93 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.07 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(x, y + 0.7 * this.global_compont_height);
      this.ctx.lineTo(x, y + 0.3 * this.global_compont_height);
      this.drawPolygon(text);
      this.setClick(
        x,
        y,
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawArrow(x, y, len, dir, noArrow) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        y = y - len;
      }
      if (dir == "down") {
        y = y + len;
      }
      if (dir == "left") {
        x = x - len;
      }
      if (dir == "right") {
        x = x + len;
      }
      this.ctx.lineTo(x, y);
      this.ctx.stroke();
      if (!noArrow) {
        this.drawArrowEnd(x, y, len, dir);
      }
    },
    arrowText(x, y, len, dir, textDir, text) {
      if (dir == "up") {
        y = y - len * 0.5;
      }
      if (dir == "down") {
        y = y + len * 0.5;
      }
      if (dir == "left") {
        x = x - len * 0.5;
      }
      if (dir == "right") {
        x = x + len * 0.5;
      }

      if (textDir == "up") {
        y = y - this.global_arrow_text_size;
      }
      if (textDir == "down") {
        y = y + this.global_arrow_text_size;
      }
      if (textDir == "left") {
        x = x - this.global_arrow_text_size * text.length * 0.54;
      }
      if (textDir == "right") {
        x = x + this.global_arrow_text_size * text.length * 0.54;
      }
      this.drawText(x, y, text, this.global_arrow_text_size);
    },
    drawArrowEnd(x, y, len, dir) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y - this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "down") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y + this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "left") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x - this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      if (dir == "right") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x + this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      this.ctx.fill();
    },
    drawText(x, y, text, size) {
      this.ctx.beginPath();
      this.ctx.textBaseline = "middle"; //设置文本的垂直对齐方式
      this.ctx.textAlign = "center";
      if (!size) {
        size = this.global_text_size;
        if (text.length * size > this.global_compont_width) {
          size = (this.global_compont_width / text.length) * 0.8;
        }
      }
      this.ctx.font = size + "px Arial";
      this.ctx.fillText(text, x, y);
    },

    initPolygon() {
      this.ctx = this.canvas.getContext("2d");
      this.ctx.lineWidth = "4";

      let lastX = this.global_center;
      let lastY = this.global_compont_height_half + 20;

      this.drawCircle(
        lastX,
        lastY,
        this.global_compont_height_half,
        "特殊气象预警"
      );
      //向下
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "稽查大队");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastY = lastY + this.global_compont_height;
      this.virtualLineRect(lastX,lastY, 13);
      //特殊天气发生前

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "指挥中心");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);

      lastY = lastY + this.global_compont_height;
      lastX = lastX - this.global_compont_width * 1.5;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 3,
        "right",
        true
      );

      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width * 3.4;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 0.8, 8);
      //为什么是0.5? 因为居中应该要选择给定高度的一半为中心，用整数就会偏离中心
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "根据特殊气"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "象预报时"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "间，强化值"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "班力量，制"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 5.5,
        "定对应值班"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 6.5,
        "表。"
      );

      lastX = lastX + this.global_compont_width;
      this.drawRect(lastX, lastY, "", 0.8, 8);
      // this.drawText(lastX+this.global_compont_width*0.4, lastY+this.global_compont_height*2, "根据特殊气");
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "联系高速交"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "警，确定道"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "路通行情"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 5.5,
        "况。"
      );
      // this.drawText(lastX+this.global_compont_width*0.4, lastY+this.global_compont_height*7, "表。");

      lastX = lastX + this.global_compont_width;
      this.drawRect(lastX, lastY, "办公室", 0.8, 1);

      lastX = lastX + this.global_compont_width * 0.4;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width * 0.4;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 0.5,
        "根据气象预报"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "时间，提前联"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "系危运、生产"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "企业，合理安"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "排运输和生产"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 5.5,
        "计划。"
      );

      lastX = lastX + this.global_compont_width;
      lastY = lastY - this.global_compont_height_double;
      this.drawRect(lastX, lastY, "系统", 0.8, 1);

      lastX = lastX + this.global_compont_width * 0.4;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width * 0.4;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 0.5,
        "系统通过微信"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "短信通知危运"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "企业与司机，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "显示灾害天气"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "救援物资分"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 5.5,
        "布。"
      );

      
      lastX = lastX - this.global_compont_width * 1.1;
      lastY = lastY + this.global_compont_height * 7;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastY = lastY + this.global_compont_height;
      this.virtualLineRect(lastX,lastY, 14);
      //特殊天气发生时
      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "指挥中心");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);

      lastY = lastY + this.global_compont_height;
      lastX = lastX - this.global_compont_width * 1.5;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 3,
        "right",
        true
      );

      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );
      lastX = lastX + this.global_compont_width;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width * 3.4;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      //为什么是0.5? 因为居中应该要选择给定高度的一半为中心，用整数就会偏离中心
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "通过巡逻组与"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "卡口监控，实"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "时掌握地面道"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "路路况。"
      );

      lastX = lastX + this.global_compont_width;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "通过系统区内"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "掌握在途危运"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "车辆运行状"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "况。"
      );

      lastX = lastX + this.global_compont_width;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 0.5,
        "路面巡逻组根"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 1.5,
        "据气候类型，"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "携带不同应急"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "救援装备，巡"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "逻并上报路"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 5.5,
        "况。"
      );

      lastX = lastX + this.global_compont_width;
      this.drawRect(lastX, lastY, "", 0.8, 6);
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 2.5,
        "实时跟进高速"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 3.5,
        "道路通行状"
      );
      this.drawText(
        lastX + this.global_compont_width * 0.4,
        lastY + this.global_compont_height * 4.5,
        "况。"
      );

      lastX = lastX - this.global_compont_width * 2.6;
      lastY = lastY + this.global_compont_height * 6;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastX = lastX + this.global_compont_width;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastX = lastX + this.global_compont_width;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastX = lastX + this.global_compont_width;
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_width * 3, "left", true);
      lastX = lastX - this.global_compont_width * 1.5;
      this.drawArrow(lastX, lastY, this.global_compont_height-this.global_arrow_height, "down");


      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height
      this.drawRect(lastX, lastY, "道路疏导");
      
      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height*2;
      this.drawArrow(lastX, lastY, this.global_compont_height-this.global_arrow_height, "down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height
      this.drawRect(lastX, lastY, "特殊气象解除");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_compont_height-this.global_arrow_height, "down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height
      this.drawRect(lastX, lastY, "恢复正常勤务");
    },

    virtualLineRect(x,y, yRate){
      x = x - this.global_compont_width*2;
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.save();
      this.ctx.lineDashOffset = 3;
      this.ctx.setLineDash([20, 5]);
      this.ctx.lineTo(x + this.global_compont_width*4 , y);
      this.ctx.lineTo(
        x + this.global_compont_width * 4,
        y + this.global_compont_height * yRate
      );
      this.ctx.lineTo(x, y + this.global_compont_height * yRate );
      this.ctx.lineTo(x, y);
      this.drawPolygon();
      this.ctx.restore();
    }
  },
};
</script>