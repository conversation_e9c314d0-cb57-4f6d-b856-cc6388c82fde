<template>
<!-- 应急救援流程图  如果要自行画图，复制整个页面，修改 initPolygon 方法即可（如要修改，尽量保证通用性，本页面没有模板）-->
<div>
  <canvas
    id="myCanvas"
    ref="myCanvas"
    @click="getLeftClick($event)"
    @contextmenu.prevent.stop="getRightClick($event)"
  >
    您的浏览器不支持 HTML5 canvas 标签。
  </canvas>
  <el-card v-show="contextVisible" :style="{width: '320px',position:'fixed',left:contextLeft+'px',top:contextTop+'px'}" >
    <div slot="header" class="clearfix">
      <span>{{currentContext}}</span>
    </div>
    <div :style="{marginBottom:'18px',textAlign:'right'}">
      <el-slider
        v-model="global_text_size"
        :max = "30"
        @change="initCanvas()"
      >
      </el-slider>
    </div>
    <div :style="{marginBottom:'18px',textAlign:'right'}">
      <el-radio-group v-model="formStatus" @change="changeColor">
        <el-radio-button v-for="statusBtn in statusBtns" :label="statusBtn.value" :key="statusBtn.value">{{statusBtn.label}}</el-radio-button>
      </el-radio-group>
    </div>
    <!-- <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('success')" type="success">标为已完成</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('warning')" type="warning">标为进行中</el-button></div>
    <div :style="{marginBottom:'18px',textAlign:'right'}"><el-button @click="changeColor('default')" type="default">标为未完成</el-button></div> -->
  </el-card>
</div>
</template>

<script>
export default {
  name: "flowChartRescue",
  components: {},
  data() {
    return {
      formStatus: "default",
      statusBtnsAll: {

      },
      statusBtns: [
        {
          label: "未完成",
          value: "default"
        },{
          label:"进行中",
          value:"warning"
        },{
          label:"已完成",
          value:"success"
        },
      ],
      eventArr: [],
      font_color_default: "#000000",
      back_colors:{
        "success":"#5daf34",
        "warning":"#E6A23C",
        "danger":"#F56C6C",
        "default":"#ecf0f5"
      },
      canvas: null,
      ctx: null,
      contextVisible: false,
      contextLeft: null,
      contextTop: null,
      currentContext: null,

      global_center: null,
      global_compont_width: null,
      global_compont_width_half: null,
      global_compont_width_double: null,
      global_compont_height: null,
      global_compont_height_half: null,
      global_compont_height_double: null,
      global_arrow_width: null,
      global_arrow_height: null,
      global_text_size: null,
    };
  },
  mounted() {
    this.resetSize();
    this.initCanvas();
    // 当调整窗口大小时重绘this.canvas
    window.onresize = () => {
      this.resetSize();
      this.initCanvas();
    };
  },
  methods: {
    resetSize() {
      this.global_center = (window.innerWidth - 200) / 2.0;

      //（用于计算起点）普通组件的宽度，它一半的宽度，两倍的宽度
      // this.global_compont_width = (window.innerWidth - 200) * 0.18; 
      // this.global_compont_width_half = (window.innerWidth - 200) * 0.09;
      // this.global_compont_width_double = (window.innerWidth - 200) * 0.36;

      this.global_compont_height = (window.innerHeight - 54) * 0.05;
      this.global_compont_height_half = (window.innerHeight - 54) * 0.025;
      this.global_compont_height_double = (window.innerHeight - 54) * 0.1;

      // 全屏宽度太宽了，改成75%
      this.global_compont_width = (window.innerWidth - 200) * 0.135; 
      this.global_compont_width_half = (window.innerWidth - 200) * 0.0675;
      this.global_compont_width_double = (window.innerWidth - 200) * 0.27;
      

      this.global_arrow_width = 6;
      this.global_arrow_height = 16;

      this.global_text_size = 26;
    },
    initCanvas() {
      this.canvas = document.getElementById("myCanvas");
      this.canvas.width = window.innerWidth - 200;
      this.canvas.height = window.innerHeight - 54;
      this.initPolygon();
    },
    setClick(x, y, x2, y2, text) {
      this.eventArr.push({ name: text, area: [x, y, x2, y2] });
    },
    getBackGround(text){
      for (let i = 0; i < this.eventArr.length; i++) {
        if(this.eventArr[i].name == text){
          return this.eventArr[i].color || this.back_colors.default;
        }
      }
      return this.back_colors.default;
    },
    getLeftClick($event){
      this.contextVisible = false;
    },
    getRightClick($event) {
      let x = $event.offsetX;
      let y = $event.offsetY;
      for (let i = 0; i < this.eventArr.length; i++) {
        let target = this.eventArr[i];
        let targetArea = target.area;
        if (
          targetArea[0] <= x &&
          targetArea[2] >= x &&
          targetArea[1] <= y &&
          targetArea[3] >= y
        ) {
          this.currentContext = target.name;
          this.formStatus = this.statusBtnsAll[this.currentContext];
          if(!this.formStatus){
            this.formStatus = 'default';
          }
          this.contextLeft = $event.clientX;
          this.contextTop = $event.clientY;
          if(this.contextTop > window.innerHeight - 220){
            this.contextTop = window.innerHeight - 220
          }
          if(this.contextLeft > window.innerWidth - 320){
            this.contextLeft = window.innerWidth - 320
          }
          this.contextVisible = true;
          return false;
        }
      }
      this.contextVisible = false;
      return true;
    },
    changeColor(type){
      this.statusBtnsAll[this.currentContext] = type;
      this.eventArr.find(t=>t.name==this.currentContext).color=this.back_colors[type];
      this.initCanvas();
      this.contextVisible = false;
    },
    drawPolygon(text){
      this.ctx.closePath();
      this.ctx.stroke();
      this.ctx.fillStyle=this.getBackGround(text);
      this.ctx.fill();
      this.ctx.fillStyle=this.font_color_default;
    },
    drawCircle(x, y, r, text) {
      let step = 1 / this.global_compont_width_half;
      this.ctx.beginPath();
      this.ctx.moveTo(x + this.global_compont_width_half, y);
      for (let i = 0; i < 2 * Math.PI; i += step) {
        this.ctx.lineTo(
          x + this.global_compont_width_half * Math.cos(i),
          y + r * Math.sin(i)
        );
      }
      this.drawPolygon(text);
      this.setClick(
        x - this.global_compont_width_half,
        y - r,
        x + this.global_compont_width_half,
        y + r,
        text
      );
      this.drawText(x, y, text);
    },
    drawRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x, y + this.global_compont_height);
      this.ctx.lineTo(x, y);
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width,y+this.global_compont_height,text);
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawDiamond(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + this.global_compont_height);
      this.ctx.lineTo(
        x + this.global_compont_width,
        y + this.global_compont_height_double
      );
      this.ctx.lineTo(
        x + 2 * this.global_compont_width,
        y + this.global_compont_height
      );
      this.ctx.lineTo(x + this.global_compont_width, y);
      this.ctx.lineTo(x, y + this.global_compont_height);//TODO
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width_double,y+this.global_compont_height_double,text);
      this.drawText(
        x + this.global_compont_width,
        y + this.global_compont_height,
        text
      );
    },
    drawComplexRect(x, y, text) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y + 0.3 * this.global_compont_height);
      this.ctx.lineTo(x + 0.07 * this.global_compont_width, y);
      this.ctx.lineTo(x + 0.93 * this.global_compont_width, y);
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.3 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 1.0 * this.global_compont_width,
        y + 0.7 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.93 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(
        x + 0.07 * this.global_compont_width,
        y + 1.0 * this.global_compont_height
      );
      this.ctx.lineTo(x, y + 0.7 * this.global_compont_height);
      this.ctx.lineTo(x, y + 0.3 * this.global_compont_height);
      this.drawPolygon(text);
      this.setClick(x,y,x+this.global_compont_width,y+this.global_compont_height,text);
      this.drawText(
        x + this.global_compont_width_half,
        y + this.global_compont_height_half,
        text
      );
    },
    drawArrow(x, y, len, dir, noArrow) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        y = y - len;
      }
      if (dir == "down") {
        y = y + len;
      }
      if (dir == "left") {
        x = x - len;
      }
      if (dir == "right") {
        x = x + len;
      }
      this.ctx.lineTo(x, y);
      this.ctx.stroke();
      if (!noArrow) {
        this.drawArrowEnd(x, y, len, dir);
      }
    },
    arrowText(x, y, len, dir, textDir, text) {
      if (dir == "up") {
        y = y - len * 0.5;
      }
      if (dir == "down") {
        y = y + len * 0.5;
      }
      if (dir == "left") {
        x = x - len * 0.5;
      }
      if (dir == "right") {
        x = x + len * 0.5;
      }

      if (textDir == "up") {
        y = y - this.global_text_size;
      }
      if (textDir == "down") {
        y = y + this.global_text_size;
      }
      if (textDir == "left") {
        x = x - this.global_text_size;
      }
      if (textDir == "right") {
        x = x + this.global_text_size;
      }

      this.drawText(x, y, text);
    },
    drawArrowEnd(x, y, len, dir) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
      if (dir == "up") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y - this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "down") {
        this.ctx.lineTo(x - this.global_arrow_width, y);
        this.ctx.lineTo(x, y + this.global_arrow_height);
        this.ctx.lineTo(x + this.global_arrow_width, y);
        this.ctx.lineTo(x, y);
      }
      if (dir == "left") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x - this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      if (dir == "right") {
        this.ctx.lineTo(x, y + this.global_arrow_width);
        this.ctx.lineTo(x + this.global_arrow_height, y);
        this.ctx.lineTo(x, y - this.global_arrow_width);
        this.ctx.lineTo(x, y);
      }
      this.ctx.fill();
    },
    drawText(x, y, text) {
      this.ctx.beginPath();
      this.ctx.textBaseline = "middle"; //设置文本的垂直对齐方式
      this.ctx.textAlign = "center";
      this.ctx.font = this.global_text_size + "px Arial";
      this.ctx.fillText(text, x, y);
    },

    initPolygon() {
      this.ctx = this.canvas.getContext("2d");
      this.ctx.lineWidth='4'
      // this.drawSmile();
      let lastX = this.global_center;
      let lastY = this.global_compont_height_half+20 ;
      this.drawCircle(lastX, lastY, this.global_compont_height_half, "事故发生");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(lastX, lastY, 30, "down");

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + 30 + this.global_arrow_height;
      this.drawComplexRect(lastX, lastY, "接警");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "left"
      );

      lastX = lastX - this.global_compont_width * 2;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX, lastY, "报警");

      lastX = lastX + this.global_compont_width * 3;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "right"
      );
      this.arrowText(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "right",
        "down",
        "N"
      );

      lastX = lastX + this.global_compont_width;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX, lastY, "信息反馈");

      lastX = lastX + this.global_compont_width_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half*3,
        "up",
        true
      );
      lastY = lastY - this.global_compont_height_half*3;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width * 1.5 - this.global_arrow_height,
        "left"
      );

      lastX = lastX - this.global_compont_width * 2;
      lastY =
        lastY + this.global_compont_height_half*5;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "down"
      );
      this.arrowText(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "down",
        "right",
        "Y"
      );

      lastX = lastX - this.global_compont_width;
      lastY = lastY + this.global_compont_height_half * 3;
      this.drawDiamond(lastX, lastY, "警情判断");

      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_double;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "应急启动");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "left"
      );

      lastX = lastX - this.global_compont_width + this.global_arrow_height * 3;
      this.drawArrow(lastX, lastY, this.global_compont_height, "up", true);
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastY = lastY - this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_arrow_height * 2, "left");
      lastY = lastY + this.global_compont_height * 2;
      this.drawArrow(lastX, lastY, this.global_arrow_height * 2, "left");

      lastX = lastX - this.global_arrow_height * 3 - this.global_compont_width;
      lastY = lastY - this.global_compont_height_half * 3;
      this.drawRect(lastX, lastY, "应急物资到位");

      lastY = lastY - this.global_compont_height;
      this.drawRect(lastX, lastY, "信息网络开通");

      lastY = lastY + this.global_compont_height * 2;
      this.drawRect(lastX, lastY, "现场指挥到位");

      lastX = lastX + this.global_compont_width_half * 5;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height_half * 3;
      this.drawRect(lastX, lastY, "救援行动");

      lastX = lastX + this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width;
      lastY = lastY + this.global_compont_height;
      this.drawDiamond(lastX, lastY, "事态控制");

      lastY = lastY + this.global_compont_height;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width_half - this.global_arrow_height,
        "left"
      );
      this.arrowText(
        lastX,
        lastY,
        this.global_compont_width_half - this.global_arrow_height,
        "left",
        "down",
        "N"
      );

      lastX = lastX - this.global_compont_width_half * 3;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX, lastY, "申请支援");

      lastX = lastX + this.global_compont_width_half;
      // lastY = lastY - this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "up"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY - this.global_compont_height_half * 5;
      this.drawRect(lastX, lastY, "扩大应急");

      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "right"
      );

      lastX = lastX + this.global_compont_width_half * 3;
      lastY = lastY + this.global_compont_height_half * 7;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "down"
      );
      this.arrowText(
        lastX,
        lastY,
        this.global_compont_height_half * 3 - this.global_arrow_height,
        "down",
        "right",
        "Y"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height_half * 3;
      this.drawRect(lastX, lastY, "应急恢复");

      lastX = lastX + this.global_compont_width;
      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "right"
      );

      lastX = lastX + this.global_compont_width - this.global_arrow_height * 3;
      this.drawArrow(lastX, lastY, this.global_compont_height * 2, "up", true);
      this.drawArrow(lastX, lastY, this.global_compont_height, "down", true);
      lastY = lastY - this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_arrow_height * 2, "right");
      lastY = lastY - this.global_compont_height;
      this.drawArrow(lastX, lastY, this.global_arrow_height * 2, "right");
      lastY = lastY + this.global_compont_height * 3;
      this.drawArrow(lastX, lastY, this.global_arrow_height * 2, "right");

      lastX = lastX + this.global_arrow_height * 3;
      lastY = lastY - this.global_compont_height_half * 5;
      this.drawRect(lastX, lastY, "现场清理");

      lastY = lastY - this.global_compont_height;
      this.drawRect(lastX, lastY, "解除警报");

      lastY = lastY + this.global_compont_height * 2;
      this.drawRect(lastX, lastY, "善后处理");

      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "事故处理");

      lastX = lastX - this.global_compont_width_half * 3;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_height - this.global_arrow_height,
        "down"
      );

      lastX = lastX - this.global_compont_width_half;
      lastY = lastY + this.global_compont_height;
      this.drawRect(lastX, lastY, "应急结束");

      lastY = lastY + this.global_compont_height_half;
      this.drawArrow(
        lastX,
        lastY,
        this.global_compont_width - this.global_arrow_height,
        "left"
      );

      lastX = lastX - this.global_compont_width * 2;
      lastY = lastY - this.global_compont_height_half;
      this.drawRect(lastX, lastY, "事故评审");
    },
  },
};
</script>