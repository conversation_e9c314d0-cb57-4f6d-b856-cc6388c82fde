<template>
  <div class="app-main-content" style="position:relative;margin:0;padding:0;">
    <div class="map" ref="mapNode" :style="{ height: modelHeight+'px'}"></div>
    <div class="layer-control">
      <div class="title">
        <h3>图层控制</h3>
      </div>
      <div class="con">
        <ul class="list">
          <li id="layer1" v-for="(item,index) in objList" :key="index">
            <div class="lf" @click="showOrhide(item)">
              <span class="on" :style="{'background': 'url('+ (item.isShow ? onImg : offImg)+')'}"></span>
            </div>
            <div class="rt">
              <!-- <i class="ico1"></i> -->
              <span class="fnt">{{item.name}}</span>
            </div>
          </li>
        </ul>
      </div>
      <div class="btm"></div>
    </div>
    <div class="term">
      <div class="top"></div>
      <div class="cnt">
        <el-form ref="termForm" :model="termForm" :rules="rules" label-width="80px" style="font-size:12px">
          <el-form-item label="事故定位">
            <el-radio-group v-model="termForm.type" @change="typeHandle()">
              <el-radio label="0">企业</el-radio>
              <el-radio label="1">车牌号</el-radio>
              <el-radio label="2">自动拾取</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="18">
              <template v-if="termForm.type == '0'">
                <el-form-item label="企业名称" :rules="$rulesFilter({required:true})">
                  <!-- <el-autocomplete
                    size="mini"
                    class="inline-input"
                    v-model="termForm.name"
                    :fetch-suggestions="queryEntpSearch"
                    value-key="name"
                    placeholder="请输入企业名称"
                    @select="handleEntpSelect"
                  ></el-autocomplete>-->

                  <el-select v-model="termForm.name" value-key="ipPk" filterable remote reserve-keyword placeholder="请输入企业名称" :remote-method="getEntpList" size="mini" @change="handleEntpSelect">
                    <el-option v-for="(item,index) in entpList" :key="index" :label="item.entpName" :value="item"></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '1'">
                <el-form-item label="车牌号" :rules="$rulesFilter({required:true})">
                  <el-select v-model="termForm.name" value-key="vecPk" filterable remote reserve-keyword placeholder="请输入车牌号" :remote-method="getVecList" size="mini" @change="handleVecSelect">
                    <el-option v-for="(item,index) in vecList" :key="index" :label="item.vecNo" :value="item"></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '0' || termForm.type == '1'">
                <el-form-item label="事故介质" :rules="$rulesFilter({required:true})">
                  <el-autocomplete size="mini" class="inline-input" v-model="termForm.media" :fetch-suggestions="queryChemSearch" value-key="name" placeholder="请输入货物名称" @select="handleChemSelect">
                    <el-button slot="append" icon="el-icon-search" title="查看MSDS信息" @click="showDetails()"></el-button>
                  </el-autocomplete>
                </el-form-item>
              </template>
              <template v-if="termForm.type == '2'">
                <div style="color:#27d1ea;margin-left:40px;">注：双击地图拾取点</div>
                <el-form-item label="经度" :rules="$rulesFilter({required:true})">
                  <el-input v-model="termForm.lng" size="mini" placeholder="请输入经度" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="纬度" :rules="$rulesFilter({required:true})">
                  <el-input v-model="termForm.lat" size="mini" placeholder="请输入纬度" :disabled="true"></el-input>
                </el-form-item>
              </template>
              <el-form-item label="范围" :rules="$rulesFilter({required:true})">
                <el-select size="mini" v-model="termForm.distance" placeholder="请选择附近范围">
                  <el-option label="1km" value="1"></el-option>
                  <el-option label="3km" value="3"></el-option>
                  <el-option label="5km" value="5"></el-option>
                  <el-option label="10km" value="10"></el-option>
                  <el-option label="15km" value="15"></el-option>
                  <el-option label="20km" value="20"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div class="btn" @click="submit()">
                <img src="~@/assets/emergency-img/download.png" alt />
                <span>确定</span>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="btm"></div>
    </div>
    <div class="satellite" :class="[satellite? 'active':'']" @click="satelliteMap">卫星地图</div>
    <div class="distance" :class="[distance? 'active':'']" @click="distanceMap">测距工具</div>
    <div class="route" :class="[route? 'active':'']" @click="routeMap">路况信息</div>
    <expert-list ref="expertList" :data-source="expertList" @expertChange="expertChange" />
    <div v-show="false">
      <monitor-info-window ref="monitorInfoWindow" :selectVecInfo="selectVecInfo" :rteplan="rteplan" @close="closeInfoWindow"></monitor-info-window>
      <monitor-cp-info-window ref="monitorCpInfoWindow" :selectEntpInfo="selectEntpInfo" @close="closeInfoWindow"></monitor-cp-info-window>
    </div>
    <el-dialog :title="chemName" :visible.sync="messageDialog" width="1000px">
      <ChemicaInfo :isCompn="true" :messageIpPk="messageIpPk"></ChemicaInfo>
    </el-dialog>
  </div>
</template>

<script>
import "@/styles/mapMonit.css";

import BMap from "BMap";
import BMapLib from "BMapLib";
import HashMap from "@/utils/hashmap";
import * as Tool from "@/utils/tool";
// import BMapStyleV2 from "@/utils/bmap-style-v2.json";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import ExpertList from "./components/expert-list";
import * as $http from "@/api/emergency-old";
import { getLastRtePlanByTracCd } from "@/api/rtePlan";
import MonitorCpInfoWindow from "../mapMonit/components/monitor-cp-info-window";
import MonitorInfoWindow from "../mapMonit/components/monitor-info-window";
import ChemicaInfo from "../base/chemica/chemica-info";

// import {
//   fireTeam,
//   rescueTeam,
//   hospitalTeam,
//   policeTeam,
//   syRescueExpertList
// } from "@/utils/rescue.js";
export default {
  name: "MapMonit",
  components: {
    ExpertList,
    MonitorCpInfoWindow,
    MonitorInfoWindow,
    ChemicaInfo
  },
  data() {
    return {
      onImg: imgsConfig.rescue.eye_on,
      offImg: imgsConfig.rescue.eye_off,
      modelHeight: Tool.getClientHeight() - 50,
      map: null,
      mapNodeLoading: null,
      objList: [
        // {
        //   name: "医院",
        //   isShow: true
        // },
        {
          name: "镇海消防",
          isShow: true
        },
        {
          name: "稽查大队",
          isShow: true
        },
        {
          name: "救援队伍",
          isShow: true
        }
        // {
        //   name: "生产使用企业",
        //   isShow: true
        // },
        // {
        //   name: "罐区",
        //   isShow: true
        // },
        // {
        //   name: "应急专家",
        //   isShow: true
        // }
      ],

      rescueTeamMap: new HashMap(), //应急救援队
      rescuceComplexCusTomOverlay: null,
      showRescueMaker: true,
      fireTeam: "",

      fireTeamMap: new HashMap(), //消防队
      showFireMaker: true,

      hospitalTeamMap: new HashMap(), //医院
      showHospitalMaker: true,

      policeTeamMap: new HashMap(), //公安
      showPoliceMaker: true,

      gmpEntpMap: new HashMap(), //生产使用企业
      showGmpEntpMaker: true,

      tankMap: new HashMap(), //罐体
      showTankMaker: true,

      // expertList: syRescueExpertList,
      expertListAll: [],
      expertList: [],
      termForm: {
        type: "0",
        name: "",
        media: "",
        distance: "1",
        lng: "",
        lat: "",
        pk: ""
      },
      entpList: [],
      vecList: [],
      chemList: [],
      chem: null,

      infoBox: null,
      infoBoxCache: null,

      selectVecInfo: { vehicleNo: "" }, // InfoBox:已选车辆信息
      selectEntpInfo: {}, // _InfoBox:已选企业信息
      rteplan: null, // InfoBox：已选车辆电子路单信息

      rules: {
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        media: [{ required: true, message: "请输入货物名称", trigger: "blur" }],
        distance: [
          { required: true, message: "请输入附近距离", trigger: "blur" }
        ]
      },
      messageDialog: false,
      messageIpPk: "",
      chemName: "基本信息", //mdsd信息弹框title
      satellite: false, //卫星地图
      distance: false, //测距
      route: false, //路况
      myDistanceToolObject: null, //测距

      target: new HashMap(), //附近参照
      TrafficControl: null
    };
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", function() {
      _this.modelHeight = Tool.getClientHeight() - 50;
    });
    // this.getEntpList(); //获取企业列表
    // this.getChemList(); // 获取货物列表
    // this.getVecList(); // 获取车辆列表
    this.$nextTick(() => {
      this.initMap(); // init map info
      this.getBoundary();
      this.getList(); //获取左侧列表
      // this.getGmpEntp(); //获取左侧生产使用企业列表
      // this.getTank()//获取左侧罐区
      // this.createRescueMap(); //添加救援图标
      // this.createFireMap(); //添加消防图标
      // this.createHospitalMap(); //添加医院图标
      // this.createPoliceMap(); //添加公安图标
    });
  },
  methods: {
    // map init
    initMap() {
      let _this = this;
      this.map = new BMap.Map(this.$refs.mapNode, { enableMapClick: false });
      let point = new BMap.Point(121.66386, 30.001186);

      this.map.centerAndZoom(point, 10);
      this.map.enableScrollWheelZoom();
      this.map.addControl(
        new BMap.NavigationControl({
          type: BMAP_NAVIGATION_CONTROL_SMALL,
          anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
          offset: new BMap.Size(20, 20),
          enableGeolocation: false
        })
      );
      // 添加定位控件
      var geolocationControl = new BMap.GeolocationControl();
      geolocationControl.addEventListener("locationSuccess", function(e) {
        // 定位成功事件
        var address = "";
        address += e.addressComponent.province;
        address += e.addressComponent.city;
        address += e.addressComponent.district;
        address += e.addressComponent.street;
        address += e.addressComponent.streetNumber;
        //     alert("当前定位地址为：" + address);
      });
      geolocationControl.addEventListener("locationError", function(e) {
        // 定位失败事件
        //     alert(e.message);
      });
      this.map.addControl(geolocationControl);
      this.myDistanceToolObject = new BMapLib.DistanceTool(this.map, {
        lineStroke: 2
      });
      this.myDistanceToolObject.addEventListener("drawend", function(e) {
        _this.myDistanceToolObject.close();
        _this.distance = false;
      });
      //添加路况控件
      this.TrafficControl = new BMapLib.TrafficControl({
        showPanel: false //是否显示路况提示面板
      });
      this.map.addControl(this.TrafficControl);
      this.TrafficControl.setAnchor(BMAP_ANCHOR_BOTTOM_RIGHT);
      this.map.addEventListener("dblclick", function(e) {
        _this.map.clearOverlays();
        _this.termForm.lng = e.point.lng;
        _this.termForm.lat = e.point.lat;
        if (_this.termForm.type == "2") {
          var marker = new BMap.Marker(e.point); // 创建标注
          _this.map.addOverlay(marker); // 将标注添加到地图中
        }
      });
      // BMap v2版本 仅支持现代浏览器
      // this.map.setMapStyleV2({
      //   styleJson: BMapStyleV2
      // });
    },
    // 卫星地图
    satelliteMap() {
      this.satellite = !this.satellite;
      if (this.satellite) {
        this.map.setMapType(BMAP_SATELLITE_MAP);
      } else {
        this.map.setMapType(BMAP_NORMAL_MAP);
      }
    },
    //测距
    distanceMap() {
      this.distance = !this.distance;
      if (this.distance) {
        this.myDistanceToolObject.open();
      }
    },
    //路况
    routeMap() {
      this.route = !this.route;
      if (this.route) {
        this.TrafficControl.showTraffic();
      } else {
        this.TrafficControl.hideTraffic();
      }
    },
    //获取行政区域
    getBoundary(callback) {
      let bdary = new BMap.Boundary();
      let map = this.map;
      let _this = this;

      this.bdary = bdary;
      bdary.get("浙江省宁波市镇海区", function(rs) {
        //获取行政区域
        // map.clearOverlays();        //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域"
          });
          return;
        }

        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polyline(rs.boundaries[i], {
            strokeWeight: 3,
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
          pointArray = pointArray.concat(ply.getPath());
        }
        _this.pointArray = pointArray;
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
    getMapNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: "el-icon-loading"
      });
      return loading;
    },
    getList() {
      let _this = this;
      // $http.getList().then(response => {
      let response = {
        code: 0,
        data: [
          [
            {
              catCd: "999.100",
              catNm: "救援队伍",
              type: "地方专职",
              teamNm: "浙江镇石物流有限责任公司",
              administrations: "茂名石化",
              teamBuildTm: "1958-01-01",
              teamPrps: "企业",
              location: "宁波市镇海区招宝山街道西侧通港路199号危化品停车场",
              skill: "危化品事故处置",
              teamNum: 37,
              manager: "刘智勐",
              mobile: "13586837756",
              tel: "2243956",
              equipment:
                "施救指挥车1辆、施救抢险厢式车1辆、汽车吊5辆、施救抢险设备运输车1辆、大型液压平板车1辆、低平板车1辆、氮气置换设备1套、氮气钢瓶2个、不锈钢常压罐车5辆、压力容器槽车5辆、自吸式离心泵1台等",
              lng: 121.703998,
              lat: 29.989853
            },
            {
              catCd: "999.100",
              catNm: "救援队伍",
              type: "地方专职",
              teamNm: "宁波金洋化工物流有限公司",
              administrations: "茂名石化",
              teamBuildTm: "1958-01-01",
              teamPrps: "企业",
              location: "海天路28号北门",
              skill: "危化品事故处置",
              teamNum: 10,
              manager: "翁永祥",
              mobile: "13958222665",
              tel: "2243956",
              equipment:
                "应急救援车1辆、备用应急救援车1辆、安全帽8顶、防化服8件、反光背心8件、塑胶靴8双、防护眼镜8副、正压式空气呼吸器5台、半面罩防毒面具1套、防毒口罩8个等",
              lng: 121.709428,
              lat: 29.984313
            }
          ],
          [
            {
              catCd: "999.102",
              catNm: "稽查大队",
              type: null,
              teamNm: "镇海区道路运输安全稽查大队",
              administrations: "宁波市公安局",
              teamBuildTm: null,
              teamPrps: "稽查大队",
              location: "大通路388号",
              skill: null,
              teamNum: null,
              manager: null,
              mobile: null,
              tel: "(0574)86296572",
              equipment: null,
              lng: 121.713583,
              lat: 29.979703
            }
          ],
          [
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "骆驼消防救援站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "宁波市镇海区东河巷路",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.594019,
              lat: 29.959741
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "高新区消防大队贵驷中队",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "宁波市镇海区贵光段14号",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.619193,
              lat: 29.983009
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "蛟川消防救援站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "浙江省宁波市镇海区雄镇路999号",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.69391,
              lat: 29.965809
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "宁波港公安局消防大队",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "浙江省宁波市镇海区江塘路67号",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.729425,
              lat: 29.960918
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "贵驷村义务消防队",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "中河西路28号附近",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.623159,
              lat: 29.980424
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "钟包村微型消防站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location:
                "宁波市镇海区明海南路与澄衷路交叉路口往东北约100米(新钟包人居住小区)",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.633718,
              lat: 29.940052
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "蛟川街道陈家村微型消防站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location:
                "宁波市镇海区陈家路与陈慈线交叉路口西北侧(摩根轴承公司东北侧约50米)",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.660138,
              lat: 29.97069
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "陈倪路社区微型消防站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "浙江省宁波市镇海区兴海南路",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.62285,
              lat: 29.943631
            },
            {
              catCd: "999.103",
              catNm: "救援队伍",
              type: "森林灭火专业救援队伍",
              teamNm: "兴庄路社区微型消防站",
              administrations: "宁波市应急管理局",
              teamBuildTm: "2017-01-01",
              teamPrps: "合同制",
              location: "浙江省宁波市镇海区兴庄路268号西北60米",
              skill: "森林防灭火及综合应急救援保障",
              teamNum: 30,
              manager: "马成兴",
              mobile: "13828655273",
              tel: "(0574)86266639",
              equipment:
                "运兵车1辆，对讲机：8台，手电筒50支，无人机1台，安全绳25条，警用水壶30个；风力灭火20台，各类水泵4台，背式喷水枪8台，组合工具包3套，割灌机4台，扑火拖把500把",
              lng: 121.620361,
              lat: 29.925799
            }
          ]
        ]
      };
      if (response.code === 0) {
        response.data.forEach(item => {
          if (item[0].catCd == "999.100") {
            //救援队
            _this.createRescueMap(item);
          }
          if (item[0].catCd == "999.103") {
            //消防队
            _this.createFireMap(item);
          }
          if (item[0].catCd == "999.101") {
            //医院
            _this.createHospitalMap(item);
          }
          if (item[0].catCd == "999.102") {
            //公安
            _this.createPoliceMap(item);
          }
          if (item[0].catCd == "999.104") {
            //应急专家
            _this.expertList = item;
            _this.expertListAll = item;
          }
        });
      } else {
        this.entpList.length = 0;
      }
      // });
    },
    //获取左侧生产使用企业
    getGmpEntp() {
      let _this = this;
      $http.getGmpEntp().then(response => {
        if (response.code === 0) {
          this.createGmpEntp(response.data);
        }
      });
    },
    //罐区
    getTank() {
      let _this = this;
      $http.getTank({ page: 1, limit: 999 }).then(response => {
        if (response.code === 0) {
          this.createTank(response.page.list);
        }
      });
    },
    showOrhide(item) {
      if (item.name !== "应急专家") {
        item.isShow = !item.isShow;
        switch (item.name) {
          case "医院":
            this.showHospital();
            break;
          case "镇海消防":
            this.showFire();
            break;
          case "稽查大队":
            this.showPolice();
            break;
          case "生产使用企业":
            this.showGmpEntp();
            break;
          case "罐区":
            this.showTank();
            break;
          case "救援队伍":
            this.showRescue();
            break;
        }
      } else {
        this.showExpertList(); // 显示应急专家名单
      }
    },
    // 显示救援队伍
    showRescue() {
      this.showRescueMaker = !this.showRescueMaker;
      this.hideRescueDialog();
      if (this.showRescueMaker) {
        this.rescueTeamMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.rescueTeamMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    // 显示消防队
    showFire() {
      this.showFireMaker = !this.showFireMaker;
      this.hideRescueDialog();
      if (this.showFireMaker) {
        this.fireTeamMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.fireTeamMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    // 显示医院
    showHospital() {
      this.showHospitalMaker = !this.showHospitalMaker;
      this.hideRescueDialog();
      if (this.showHospitalMaker) {
        this.hospitalTeamMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.hospitalTeamMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    // 显示公安
    showPolice() {
      this.showPoliceMaker = !this.showPoliceMaker;
      this.hideRescueDialog();
      if (this.showPoliceMaker) {
        this.policeTeamMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.policeTeamMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    //显示生产企业
    showGmpEntp() {
      this.showGmpEntpMaker = !this.showGmpEntpMaker;
      this.hideRescueDialog();
      if (this.showGmpEntpMaker) {
        this.gmpEntpMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.gmpEntpMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    //显示罐区
    showTank() {
      this.showTankMaker = !this.showTankMaker;
      this.hideRescueDialog();
      if (this.showTankMaker) {
        this.tankMap.values().forEach(item => {
          item.show();
        });
      } else {
        this.tankMap.values().forEach(item => {
          item.hide();
        });
      }
    },
    // 显示应急专家名单
    showExpertList() {
      this.$refs.expertList.init();
    },
    hideRescueDialog() {
      this.fireTeam = "";
      if (this.rescuceComplexCusTomOverlay) {
        this.map.removeOverlay(this.rescuceComplexCusTomOverlay.hide());
        this.rescuceComplexCusTomOverlay = null;
      }
      // if(this.complexCustomOverlay){
      //     this.map.removeOverlay(this.complexCustomOverlay.hide())
      //     this.complexCustomOverlay = null;
      // }
    },
    // 创建生产使用企业
    createGmpEntp(list) {
      let _this = this;
      list.forEach(item => {
        if (item.center_point) {
          let lnglat = item.center_point.split(",");
          let icon = new BMap.Icon(
            imgsConfig.rescue.gmpEntp,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(lnglat[0], lnglat[1]);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.name
          });
          marker.hide();
          marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.openCpInfoWindow(item.entp_pk, this);
          });
          _this.gmpEntpMap.put(item.name, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    //创建罐区
    createTank(list) {
      let _this = this;
      list.forEach(item => {
        if (item.bdtankAreaFence) {
          let points = JSON.parse(item.bdtankAreaFence);
          let marker = null;
          let icon = new BMap.Icon(
            imgsConfig.rescue.tank,
            new BMap.Size(35, 36)
          );
          let pointsArr = [];
          for (let i = 0, len = points.length; i < len; i++) {
            pointsArr.push(new BMap.Point(points[i].lng, points[i].lat));
          }
          var polygon = new BMap.Polygon(pointsArr, {
            strokeColor: "blue",
            strokeWeight: 2,
            strokeOpacity: 0.3
          }); //创建多边形
          let pts = _this.getCenterPoint(pointsArr);
          marker = new BMap.Marker(pts, { icon: icon });
          marker.hide();
          marker.disableMassClear();
          polygon.hide();
          polygon.disableMassClear();
          marker.addEventListener("click", function() {
            _this.hideRescueDialog();
            let points = this.getPosition();
            let _dom = _this.createTankInfo(item);
            _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);
            let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
              points,
              _dom
            ));
            _this.map.addOverlay(createComplexCustomOverlay);
            _this.map.centerAndZoom(points, 18);
          });

          _this.tankMap.put(item.id + "tank", marker);
          _this.tankMap.put(item.id + "polygon", polygon);
          _this.map.addOverlay(polygon); //增加多边形
          _this.map.addOverlay(marker);
          marker.show();
          polygon.show();
        }
      });
    },
    // 创建医院信息
    createHospitalMap(hospitalTeam) {
      let _this = this;
      let hospitalTeamMap = this.hospitalTeamMap;
      hospitalTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.hospital,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm
          });

          marker.hide();
          marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.hideRescueDialog();

            let points = this.getPosition();
            let _dom = _this.createHospitalInfo(item);
            _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

            let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
              points,
              _dom
            ));

            _this.map.addOverlay(createComplexCustomOverlay);
            _this.map.setCenter(points);
          });
          hospitalTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建公安信息
    createPoliceMap(policeTeam) {
      let _this = this;
      let policeTeamMap = this.policeTeamMap;
      policeTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.police,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm
          });

          marker.hide();
          marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.hideRescueDialog();

            let points = this.getPosition();
            let _dom = _this.createPoliceTeamMapInfo(item);
            _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

            let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
              points,
              _dom
            ));

            _this.map.addOverlay(createComplexCustomOverlay);
            _this.map.setCenter(points);
          });
          policeTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建救援信息
    createRescueMap(rescueTeam) {
      let _this = this;
      let rescueTeamMap = this.rescueTeamMap;
      rescueTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.entpFire,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm
          });

          marker.hide();
          marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.hideRescueDialog();

            let points = this.getPosition();
            let _dom = _this.createEntpRescueInfo(item);
            _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

            let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
              points,
              _dom
            ));

            _this.map.addOverlay(createComplexCustomOverlay);
            _this.map.setCenter(points);
          });
          rescueTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建消防信息
    createFireMap(subordinateUnits) {
      let _this = this;
      let fireTeamMap = this.fireTeamMap;
      subordinateUnits.forEach(subitem => {
        if (subitem.lng && subitem.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.fire,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(subitem.lng, subitem.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: subitem.teamNm
          });

          marker.hide();
          marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.hideRescueDialog();
            _this.fireTeam = subitem.unitShortNm;

            let points = this.getPosition();
            let _dom = _this.createFireInfo(subitem);
            _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

            let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
              points,
              _dom
            ));

            _this.map.addOverlay(createComplexCustomOverlay);
            _this.map.setCenter(points);
          });
          fireTeamMap.put(subitem.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 消防队信息
    createFireInfo(subitem) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(230, 86, 71, 0.6);width:320px;border:1px solid rgb(201, 74, 74);
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>消防队 : ${subitem.teamNm || ""}</div>
        <div>地址 : ${subitem.location || ""}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      let arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:rgb(201, 74, 74);";

      _dom.appendChild(arrow);
      return _dom;
    },

    // 救援队伍
    createEntpRescueInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>单位名称 : ${item.teamNm || ""}</div>
        <div>负责人 : ${item.manager || ""}</div>
        <div>联系电话 : ${item.mobile}</div>
        <div>地址 : ${item.location || ""}</div>
        <div>队员 : ${item.teamNum || ""} 人</div>
        <div>装备 : ${item.equipment || ""}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    // 医院信息
    createHospitalInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>医院名称 : ${item.teamNm || ""}</div>
        <div>院长 : ${item.manager || ""}</div>
        <div>联系电话 : ${item.tel || ""}</div>
        <div>地址 : ${item.location || ""}</div>
        <div>医务人员 : ${item.teamNum || ""} 人</div>

      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    // 公安信息
    createPoliceTeamMapInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>单位名称 : ${item.teamNm || ""}</div>
        <div>联系电话 : ${item.tel || ""}</div>
        <div>地址 : ${item.location || ""}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    //罐区详情
    createTankInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>企业名称 : ${item.entpNm || ""}</div>
        <div>储罐区编号 : ${item.tankAreaCd || ""}</div>
        <div>存储物质 : ${item.storeGoods || ""}</div>
        <div>物质状态 : ${item.storeStatus || ""} </div>
        <div>储罐数量 : ${item.tankCnt || ""}</div>
        <div>储罐容积 : ${item.tankVol || ""}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    //创建详情框
    createComplexCustomOverlay(points, labelText, bgColor, state) {
      let _this = this;
      let mp = this.map;
      let _dom = arguments.length == 2 ? arguments[1] : null;

      bgColor = bgColor || "#EE5D5B";
      state = state || "";

      function ComplexCustomOverlay(points, labelText) {
        this._point = points;
        this._text = labelText;
      }

      ComplexCustomOverlay.prototype = new BMap.Overlay();

      ComplexCustomOverlay.prototype.initialize = function(map) {
        this._map = map;
        this._domOffsetHeight = 0;
        let div;

        if (_dom) {
          div = this._div = _dom;
          this._arrow = _dom.querySelector(".arrow");
        } else {
          div = this._div = document.createElement("div");
          div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
          div.style.cssText =
            "position:absolute;background-color:" +
            bgColor +
            ";border:1px solid " +
            bgColor +
            ";border-radius:3px;padding:4px;color:#fff;width:106px;font-size:12px;text-align:center;";

          var span = (this._span = document.createElement("span"));
          div.appendChild(span);
          span.appendChild(document.createTextNode(this._text + " " + state));

          var arrow = (this._arrow = document.createElement("div"));
          arrow.style.cssText =
            "position:absolute;top:22px;left:46px;width:0px;height:0px;border:6px solid transparent;border-top-color:" +
            bgColor +
            ";";

          div.appendChild(arrow);
        }
        let closeBtn = div.querySelector(".close");
        if (closeBtn) {
          closeBtn.addEventListener("click", () => {
            _this.hideRescueDialog();
          });
        }

        mp.getPanes().labelPane.appendChild(div);
        this._domOffsetHeight = div.offsetHeight;
        return div;
      };

      ComplexCustomOverlay.prototype.draw = function() {
        let map = this._map;
        let pixel = map.pointToOverlayPixel(this._point);

        this._div.style.left =
          pixel.x - parseInt(this._arrow.style.left) - 6 + "px";
        this._div.style.top =
          pixel.y - parseFloat(this._domOffsetHeight) - 26 + "px";
      };

      let myCompOverlay = new ComplexCustomOverlay(points, labelText);

      return myCompOverlay;
    },
    //选择类型
    typeHandle(e) {
      this.termForm.name = "";
      this.termForm.media = "";
      this.termForm.lng = "";
      this.termForm.lat = "";
    },
    // // 从企业列表中查询
    // queryEntpSearch(queryString, cb) {
    //   // 调用 callback 返回建议列表的数据
    //   cb(this.queryByEntpList(queryString));
    // },
    // queryByEntpList(queryString) {
    //   let _this = this
    //   return this.entpList
    //     .filter(item => {
    //       return item.entpName.indexOf(queryString) > -1;
    //     })
    //     .map(item => {
    //       return { name: item.entpName, value: item.ipPk };
    //     });
    // },

    // 从货物列表中查询
    queryChemSearch(queryString, cb) {
      // 调用 callback 返回建议列表的数据
      cb(this.queryByChemList(queryString));
    },
    queryByChemList(queryString) {
      return this.chemList
        .filter(item => {
          return item.chem_nm.indexOf(queryString) > -1;
        })
        .map(item => {
          return { name: item.chem_nm, value: item.prod_pk };
        });
    },
    handleVecSelect(item) {
      this.termForm.name = item.vecNo;
      this.termForm.media = "";
      this.getChemList(item.ownedCompany);
      getLastRtePlanByTracCd(item.vecNo).then(response => {
        if (response.code === 0 && response.data) {
          this.chem.value = response.data.prodPk;
          this.termForm.media = response.data.goodsNm;
        }
      });
    },
    // 选择介质
    handleEntpSelect(item) {
      this.termForm.name = item.entpName;
      this.termForm.pk = item.ipPk;
      this.termForm.media = "";
      this.getChemList(item.entpName);
    },
    handleChemSelect(item) {
      this.chem = item;
      this.chemName = item.name;
    },
    //获取企业列表
    getEntpList: function(name) {
      if (name) {
        $http
          .getEntpList({
            filters: {
              groupOp: "AND",
              rules: [
                { field: "cat_cd", op: "eq", data: "2100.185.150.150" },
                { field: "entp_name", op: "cn", data: name }
              ]
            },
            page: 1,
            limit: 2000
          })
          .then(response => {
            if (response.code === 0) {
              this.entpList = response.page.list;
            } else {
              this.entpList.length = 0;
            }
          });
      }
    },
    //获取车辆列表
    getVecList: function(name) {
      if (name.length > 1) {
        $http
          .getVecList({
            filters: {
              groupOp: "AND",
              rules: [{ field: "vec_no", op: "cn", data: name }]
            },
            page: 1,
            limit: 10000
          })
          .then(response => {
            if (response.code === 0) {
              let list = [];
              response.page.list.forEach(item => {
                if (item.vecNo.indexOf("挂") < 0) {
                  list.push(item);
                }
              });
              this.vecList = list;
            } else {
              this.vecList.length = 0;
            }
          });
      }
    },
    //获取货物列表
    getChemList: function(entpname) {
      $http.getChemList(entpname).then(response => {
        if (response.code === 0) {
          this.chemList = response.data;
        } else {
          this.chemList.length = 0;
        }
      });
    },
    submit() {
      // 目标事物
      this.target.clear();
      this.map.clearOverlays();
      // 圆形覆盖物
      var point = new BMap.Point(this.termForm.lng, this.termForm.lat);
      // console.log(point,this.termForm.distance)
      // var circle = new BMap.Circle(
      //   point,
      //   Number(this.termForm.distance) * 1000,
      //   {
      //     fillColor: "blue",
      //     strokeWeight: 1,
      //     fillOpacity: 0.3,
      //     strokeOpacity: 0.3,
      //   }
      // );
      // this.map.addOverlay(circle);
      if (this.termForm.type == 0) {
        //企业
        this.submitEntp();
      }
      if (this.termForm.type == 1) {
        //'车辆'
        this.submitVec();
      }
      if (this.termForm.type == 2) {
        //'坐标'
        var marker = new BMap.Marker(point); // 创建标注
        this.map.addOverlay(marker); // 将标注添加到地图中
        this.submitPoint();
        // 绘制辐射范围
        this.target.put("point", marker);
        this.map.addOverlay(this.createCanvasLayer());
      }
    },
    //根据企业提交
    submitEntp() {
      let radius = Number(this.termForm.distance) * 1000;
      let param = {
        entpname: this.termForm.name,
        radius: radius
      };
      this.$refs.termForm.validate(valid => {
        if (valid) {
          $http.getEntpnamepoi(param).then(response => {
            if (response.code === 0 && response.data.lnglat) {
              this.loadAreaEntp(
                response.data.lnglat,
                this.termForm.name,
                radius
              );
              this.nearbyVec(response.data.nearcar, radius);
              this.poiList(response.data.poi);
            } else {
              this.$message({
                message: "对不起，无法找到该企业",
                type: "error"
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //根据车辆提交
    submitVec() {
      let radius = Number(this.termForm.distance) * 1000;
      let param = {
        vecno: this.termForm.name,
        radius: radius
      };
      this.$refs.termForm.validate(valid => {
        if (valid) {
          $http.getVecnopoi(param).then(response => {
            if (response.code === 0 && response.data.lnglat) {
              this.loadAreaVec(
                response.data.lnglat,
                this.termForm.name,
                radius
              );
              this.nearbyVec(response.data.nearcar, radius);
              this.poiList(response.data.poi);
            } else {
              this.$message({
                message: "对不起，无法找到该车辆",
                type: "error"
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //根据坐标提交
    submitPoint() {
      let radius = Number(this.termForm.distance) * 1000;
      let param = {
        lng: this.termForm.lng,
        lat: this.termForm.lat,
        radius: radius
      };
      this.$refs.termForm.validate(valid => {
        if (valid) {
          $http.getpoi(param).then(response => {
            if (response.code === 0) {
              this.poiList(response.data.poi);
              this.nearbyVec(response.data.nearcar, radius);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 展示企业围栏
    loadAreaEntp(points, name, radius) {
      let _this = this;
      let marker = null;
      let lng = null;
      let lat = null;
      let pointsView = [];
      let icon = new BMap.Icon(imgsConfig.vec.entp, new BMap.Size(24, 24));
      points = points.split(";");
      let pointsArr = [];
      for (let i = 0, len = points.length; i < len; i++) {
        let item = points[i].split(",");
        if (!item[0]) {
          continue;
        }
        lng = Number(item[0]);
        lat = Number(item[1]);
        pointsArr.push(new BMap.Point(lng, lat));
      }
      var polygon = new BMap.Polygon(pointsArr, {
        strokeColor: "blue",
        strokeWeight: 2,
        strokeOpacity: 0.3
      }); //创建多边形
      _this.map.addOverlay(polygon); //增加多边形
      let pts = _this.getCenterPoint(pointsArr);
      pointsView.push(pts);
      marker = new BMap.Marker(pts, { icon: icon });
      marker.addEventListener("click", function() {
        _this.openCpInfoWindow(_this.termForm.pk, this, radius);
      });
      _this.map.addOverlay(marker);
      _this.map.setViewport(pointsView); //将所有的点放置在最佳视野内
      window.clearTimeout(_this.refreshTask);
      _this.refreshNextTime = 0;

      this.target.put(name, marker);
      // 绘制辐射范围
      this.map.addOverlay(this.createCanvasLayer());
    },
    //企业详情
    openCpInfoWindow(pk, marker, radius) {
      let _this = this;
      this.mapNodeLoading = this.getMapNodeLoading();
      this.map.panTo(marker.getPosition());
      // 获取企业详情
      $http
        .getEntpInfoBypk(pk)
        .then(res => {
          if (res.code == 0 && res.data) {
            _this.selectEntpInfo = res.data.entp;
            _this.selectEntpInfo.radius = radius;
            var infoBox = new BMapLib.InfoBox(
              _this.map,
              _this.$refs.monitorCpInfoWindow.$el,
              {
                boxStyle: {
                  width: "310px",
                  marginBottom: "120px",
                  marginRight: "18px"
                },
                closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
            });
            infoBox.open(marker);
            infoBox.show();
            infoBox = null;
          } else {
            this.$message({
              message: "对不起，无法查看该企业信息",
              type: "error"
            });
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    //展示车辆
    loadAreaVec(points, name, radius) {
      let _this = this;
      let marker = null;
      let pointsView = [];
      let icon = new BMap.Icon(
        imgsConfig.rescue.claimcar,
        new BMap.Size(24, 24)
      );
      points = points.split(",");
      let pts = new BMap.Point(Number(points[1]), Number(points[0]));
      pointsView.push(pts);
      _this.map.setViewport(pointsView); //将所有的点放置在最佳视野内
      marker = new BMap.Marker(pts, { icon: icon });
      marker.addEventListener("click", function() {
        _this.openInfoWindow(name, this, radius);
      });
      _this.map.addOverlay(marker);
      _this.map.setViewport(pointsView); //将所有的点放置在最佳视野内

      this.target.put(name, marker);
      // 绘制辐射范围
      this.map.addOverlay(this.createCanvasLayer());
    },
    // 附近车辆
    nearbyVec(veclist, radius) {
      let _this = this;
      veclist.forEach((item, i) => {
        let point = new BMap.Point(item.lonBd, item.latBd);
        let runStatus = item.speed > 0 ? "run" : "stop";
        let updateTime = new Date().getTime() - item.updateTimeStamp;
        if (item.state == 0 || updateTime > 10 * 1000 * 60) {
          // 离线
          runStatus = "offline";
        }
        // carType：0：空车，1：重车，2：未知
        let icon = new BMap.Icon(
          imgsConfig.vec[
            `${runStatus}_${item.carType == 4 ? 2 : item.carType}`
          ],
          new BMap.Size(24, 24)
        );
        let marker = new BMap.Marker(point, { icon: icon });
        marker.setRotation(item.direction);
        _this.map.addOverlay(marker);

        marker.addEventListener("click", function() {
          _this.openInfoWindow(item._id, this, radius);
        });
      });
    },
    poiIconImg(data) {
      switch (data) {
        case data.indexOf("公司") > -1:
          return imgsConfig.poi.entp;
          break;
        case data.indexOf("教育") > -1:
          return imgsConfig.poi.teach;
          break;
        case data.indexOf("交通") > -1:
          return imgsConfig.poi.traffic;
          break;
        case data.indexOf("政府") > -1:
          return imgsConfig.poi.government;
          break;
        case data.indexOf("医") > -1:
          return imgsConfig.poi.hospital;
          break;
        case data.indexOf("广场") > -1:
          return imgsConfig.poi.arder;
          break;
        default:
          return imgsConfig.poi.weizhi;
          break;
      }
    },
    //展示poi
    poiList(data) {
      let _this = this;
      data.forEach((item, i) => {
        let point = new BMap.Point(item.location.lng, item.location.lat);
        let icon = new BMap.Icon(
          _this.poiIconImg(item.name),
          new BMap.Size(35, 35)
        );
        let marker = new BMap.Marker(point, { icon: icon });
        _this.map.addOverlay(marker);
        marker.addEventListener("click", function() {
          _this.hideRescueDialog();
          let points = this.getPosition();
          let _dom = _this.createPoiInfo(item);
          _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

          let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
            points,
            _dom
          ));

          _this.map.addOverlay(createComplexCustomOverlay);
          _this.map.setCenter(points);
        });
      });
    },
    // poi信息详情
    createPoiInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>名称 : ${item.name}</div>
        <div>距离 : ${item.distants.toFixed(2)}米</div>
        <div>地区 : ${item.province}${item.city}${item.area}</div>
        <div>详细地址 : ${item.address}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    openInfoWindow(name, marker, radius) {
      let _this = this;
      this.map.panTo(marker.getPosition());
      this.mapNodeLoading && this.mapNodeLoading.close();
      this.mapNodeLoading = this.getMapNodeLoading();
      // 获取车辆详情
      $http
        .getVecInfo(name)
        .then(res => {
          if (res.code == 0) {
            let rteplan = res.data;
            _this.selectVecInfo.vehicleNo = name;
            _this.selectVecInfo.radius = radius;
            _this.rteplan = rteplan;
            var infoBox = new BMapLib.InfoBox(
              _this.map,
              _this.$refs.monitorInfoWindow.$el,
              {
                boxStyle: {
                  width: "310px",
                  marginBottom: rteplan ? "28px" : "-76px",
                  marginRight: "15px"
                },
                closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
            });
            infoBox.open(marker);
            infoBox.show();
            infoBox = null;
          } else {
            this.$message({
              message: "对不起，无法查看该车信息",
              type: "error"
            });
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    //百度地图多边形计算中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;
      return new BMap.Point(x, y);
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoBoxCache) {
        this.infoBoxCache.close();
        this.infoBoxCache = null;
      }
    },
    showDetails() {
      if (this.chem.value) {
        this.messageDialog = true;
        this.messageIpPk = this.chem.value;
      } else {
        if (this.termForm.type == 0) {
          if (!this.termForm.name) {
            this.$message({
              message: "请先选择企业，再关联事故介质",
              type: "error"
            });
          } else {
            this.$message({
              message: "请在事故介质的下拉列表中选择",
              type: "error"
            });
          }
        }
        if (this.termForm.type == 1) {
          if (!this.termForm.name) {
            this.$message({
              message: "请先选择车辆,自动关联事故介质",
              type: "error"
            });
          } else {
            this.$message({
              message: "请在事故介质的下拉列表中选择",
              type: "error"
            });
          }
        }
      }
    },
    //应急专家过滤
    expertChange(data) {
      let _this = this;
      this.expertList = [];
      if (data) {
        this.expertListAll.forEach(item => {
          if (data == item.type) {
            _this.expertList.push(item);
          }
        });
      } else {
        this.expertList = this.expertListAll;
      }
    },
    // 创建百度地图canvasLayer
    createCanvasLayer() {
      let _this = this;
      this.bmapCanvasLayer = new BMap.CanvasLayer({
        paneName: "mapPane",
        update: function() {
          _this.canvasLayerUpdate(this.canvas);
        }
      });

      return this.bmapCanvasLayer;
    },

    canvasLayerUpdate(canvas) {
      this.radarMethod = this.radar(canvas, 0, 2 * Math.PI);
      this.radarMethod && this.radarMethod();
    },

    radar(canvas, startAng, endAng) {
      let _this = this;
      var perDeg = 1;
      var raf = null;
      var deg = 0;

      let ctx, pts, pixel, endPts, endPixel, x, y, radius;
      let overlays = _this.target.values();
      /* if(_this.target.size() == 0) {
        return false;
      } */
      ctx = this.bmapCanvasLayerCtx = canvas.getContext("2d");
      ctx.strokeStyle = "rgba(0,255,0,1)";
      function init() {
        /*  ctx.fillStyle = 'rgba(0,50,0,1)';
          ctx.arc(x, x, radius, startAng, endAng);
          ctx.fill(); */
        loop();
        // raf = window.requestAnimationFrame(loop);
      }

      function loop() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        deg = deg + perDeg;
        overlays.forEach((overlay, index) => {
          pts = overlay.getPosition();
          pixel = _this.map.pointToPixel(pts);
          endPts = _this.getPointWidthDistance(
            pts.lat,
            pts.lng,
            _this.termForm.distance,
            180
          );
          endPixel = _this.map.pointToPixel(endPts);
          x = pixel.x;
          y = pixel.y;
          radius = endPixel.y - pixel.y;
          cover();
          drawPosLine();
          drawRadar(deg);
        });

        raf = window.requestAnimationFrame(loop);
      }

      function cover() {
        ctx.save();
        ctx.beginPath();
        ctx.fillStyle = "rgba(0,200,0,.1)";
        ctx.arc(x, y, radius, startAng, endAng);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
      }

      function drawPosLine() {
        ctx.beginPath();
        ctx.moveTo(x, y - radius);
        ctx.lineTo(x, y + radius);
        ctx.closePath();
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(x - radius, y);
        ctx.lineTo(x + radius, y);
        ctx.closePath();
        ctx.stroke();

        ctx.moveTo(x, y);
        ctx.beginPath();
        ctx.arc(x, y, radius / 3, 0 * Math.PI, 2 * Math.PI);
        ctx.closePath();
        ctx.stroke();

        ctx.moveTo(x, y);
        ctx.beginPath();
        ctx.arc(x, y, (radius * 2) / 3, 0 * Math.PI, 2 * Math.PI);
        ctx.closePath();
        ctx.stroke();
      }

      function drawRadar(iDeg) {
        ctx.fillStyle = "rgba(0,200,0,0.7)";
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.arc(
          x,
          y,
          radius,
          ((-2 * perDeg + iDeg) / 180) * Math.PI,
          ((0 + iDeg) / 180) * Math.PI
        );
        ctx.closePath();
        ctx.fill();
      }

      return init;
    },
    getPointWidthDistance(GLAT, GLON, distance, angle) {
      let Ea = 6378137; //赤道半径
      let Eb = 6356725; //极半径

      let dx = distance * 1000 * Math.sin((angle * Math.PI) / 180.0);
      let dy = distance * 1000 * Math.cos((angle * Math.PI) / 180.0);

      let ec = Eb + ((Ea - Eb) * (90.0 - GLAT)) / 90.0;
      let ed = ec * Math.cos((GLAT * Math.PI) / 180);

      let BJD = ((dx / ed + (GLON * Math.PI) / 180.0) * 180.0) / Math.PI;

      let BWD = ((dy / ec + (GLAT * Math.PI) / 180.0) * 180.0) / Math.PI;

      return { lng: BJD, lat: BWD };
    }
  }
};
</script>

<style scoped>
.layer-control {
  position: fixed;
  width: 131px;
  float: left;
  left: 12px;
  top: 70px;
  box-shadow: 0 0 38px #3f97e2;
  border-radius: 10px;
  z-index: 2;
}
.layer-control .title {
  background: url("~@/assets/emergency-img/layer_title.png");
  width: 100%;
  height: 28px;
}
.layer-control .title h3 {
  color: #9de8fe;
  font-size: 12px;
  line-height: 28px;
  margin: 0;
}
.layer-control .title h3:before {
  content: "";
  background: url("~@/assets/emergency-img/layer_title_ico.png");
  float: left;
  width: 3px;
  height: 17px;
  margin: 5px 8px 0 13px;
  color: #9de8fe;
}
.layer-control .con {
  background: url("~@/assets/emergency-img/layer_bg.png");
  width: 100%;
  float: left;
}
.layer-control ul {
  padding: 0;
}
.layer-control .list,
.layer-control .list li {
  float: left;
  width: 129px;
  margin: 0 1px;
}
.layer-control .list li {
  border-bottom: solid 1px #163863;
  cursor: pointer;
}
.layer-control .list li:last-child {
  border: none;
}
.layer-control .list li .lf {
  border-right: solid 1px #163863;
  width: 38px;
}
.layer-control .list li .lf,
.layer-control .list li .rt {
  float: left;
  height: 38px;
  line-height: 38px;
}
.layer-control .list li .lf .on {
  float: left;
  width: 24px;
  height: 24px;
  margin: 8px;
}
.layer-control .list li .fnt {
  color: #9de8fe;
  font-size: 12px;
  margin-left: 10px;
}
.layer-control .list li .ico1 {
  background: url("~@/assets/emergency-img/layer_ico1.png");
  float: left;
  width: 24px;
  height: 24px;
  margin: 8px 4px 0;
}

.layer-control .btm {
  background: url("~@/assets/emergency-img/layer_btn.png");
  float: left;
  width: 131px;
  height: 12px;
}
.term {
  position: fixed;
  width: 320px;
  right: 20px;
  top: 60px;
  z-index: 1;
  padding: 10px;
}
.el-form-item__label {
  font-size: 12px !important;
}
.term .top,
.term .btm {
  width: 320px;
  height: 3px;
}
.term .top {
  background: url("~@/assets/emergency-img/panel_box_top.png");
  background-size: 100% 100%;
}
.term .btm {
  background: url("~@/assets/emergency-img/panel_box_btm.png");
  background-size: 100% 100%;
}
.term .cnt {
  width: 320px;
  background: #102556;
  border-left: solid 1px #073252;
  border-right: solid 1px #073252;
  position: relative;
}
.term .btn {
  background: url("~@/assets/emergency-img/btn_bg3.png");
  width: 50px;
  height: 80px;
  float: left;
  cursor: pointer;
  margin: 6px 0 0 2px;
}
.term .btn img {
  display: block;
  margin: 10px auto 6px;
}
.term .btn span {
  display: block;
  width: 100%;
  text-align: center;
  color: #27d1ea;
  font-size: 12px;
}
.el-form .el-form-item {
  margin-bottom: 0px !important;
}
.satellite,
.distance,
.route {
  position: absolute;
  z-index: 3;
  right: 90px;
  bottom: 5px;
  width: 80px;
  padding: 0px 4px;
  background: #fff;
  color: #333;
  border-radius: 0 0 3px 3px;
  border: 1px solid #e6e6e6;
  font-size: 14px;
  vertical-align: middle;
  cursor: pointer;
  line-height: 26px;
  text-align: center;
}
.satellite.active,
.distance.active,
.route.active {
  color: #409eff;
}
.distance {
  right: 170px;
  border-radius: 3px 3px 0 0;
}
.route {
  right: 10px;
}
</style>
<style>
.el-input-group__append,
.el-input-group__prepend {
  padding: 0 8px;
  border: 1px solid #4b72a9;
  background-color: #0a264e;
  color: #27d1ea;
}
.term .el-form-item__label {
  color: #fff;
}
.term .el-radio,
.el-radio__label {
  font-size: 12px;
}
.term .el-radio {
  color: #89defe;
  margin-right: 0;
}
.term .el-radio + .el-radio {
  margin-left: 10px;
}
.maplibTc {
  z-index: 2000;
}
</style>
