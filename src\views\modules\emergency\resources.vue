<template>
  <div class="app-main-content" style="padding-top:0;">
    <el-tabs v-model="activeName">
      <el-tab-pane label="应急救援专家" name="experts" :lazy="true">
        <experts></experts>
      </el-tab-pane>
      <el-tab-pane label="应急救援力量" name="force" :lazy="true">
        <force></force>
      </el-tab-pane>
      <el-tab-pane label="应急物资保障" name="material" :lazy="true">
        <material></material>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import experts from "./components/resources-experts";
import force from "./components/resources-force";
import material from "./components/resources-material";
export default {
  name: "flowChart",
  components: { experts, force, material },
  data() {
    return {
      activeName: "experts"
    };
  },
  mounted: function() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
</style>