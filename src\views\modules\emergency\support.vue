<template>
  <div style="position: relative">
    <div class="search-wape">
      <el-input v-show="!leftLayerIsFold" v-model="searchItem" placeholder="请输入物资" @change="handleChange" clearable>
      </el-input>
    </div>
    <bmap-comp :param="mapConfig" ref="bmap"></bmap-comp>
    <div class="left-top-layer" :class="{ 'is-fold': leftLayerIsFold ? true : false }">
      <h5 @click="leftLayerIsFold = !leftLayerIsFold">
        救援资源&nbsp;<i :class="
            leftLayerIsFold ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
          "></i>
      </h5>
      <div class="content">
        <ul class="list">
          <li v-for="(item, index) in forceTypeList" :key="index" v-show="item.label !== '其他'" @click="showOrhide(item)">
            <div class="lf">
              <img :alt="item.label" :src="imgsConfig.rescue[item.nmEn]" />
              <span class="fnt">{{ item.label }}</span>
            </div>
            <div class="rt">
              <span class="on" :style="{
                  background: 'url(' + (item.isShow ? onImg : offImg) + ')',
                }"></span>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="right-top-layer" :class="{ 'is-fold': rightLayerIsFold ? true : false }">
      <div class="layer-btn">
        <span @click="rightLayerIsFold = false" v-if="rightLayerIsFold">
          <svg-icon icon-class="arrow-left" class-name="svg-icon"></svg-icon>
        </span>
        <span @click="rightLayerIsFold = true" v-else>
          <svg-icon icon-class="arrow-right" class-name="svg-icon"></svg-icon>
        </span>
      </div>
      <div class="right-top-layer-box">
        <h5>泄露范围预测条件</h5>
        <div class="padding-tb">
          <el-form :model="queryForm" :inline="true" label-width="60px" :size="size" class="detail-form">
            <el-form-item label="泄露量">
              <el-radio-group v-model="queryForm.size" @change="queryFormChange">
                <el-radio :label="0">大&nbsp;&nbsp;&nbsp;</el-radio>
                <el-radio :label="1">小</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="时间">
              <el-radio-group v-model="queryForm.time" @change="queryFormChange">
                <el-radio :label="0">白天</el-radio>
                <el-radio :label="1">夜晚</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="范围">
            <el-select v-model="queryForm.radius" style="width:100px;">
              <el-option :value="1000" label="1km"></el-option>
              <el-option :value="3000" label="3km">3km</el-option>
              <el-option :value="5000" label="5km">5km</el-option>
              <el-option :value="10000" label="10km">10km</el-option>
              <el-option :value="15000" label="15km">15km</el-option>
              <el-option :value="20000" label="20km">20km</el-option>
            </el-select>
          </el-form-item> -->
          </el-form>
        </div>
        <!-- <div>
          <el-button @click="linkToVideo" style="width:100%;">链接现场视频</el-button>
        </div> -->
      </div>
      <div class="right-top-layer-box responsive">
        <div class="header-module">
          <!-- <el-autocomplete popper-class="my-autocomplete" v-model="queryForm.vecno" :fetch-suggestions="querySearchVecNoAsync" placeholder="请输入事故车牌号" @select="selectVecno" clearable size="mini" style="width:100%;">
            <template slot-scope="{ item }">
              <div class="vec-no">{{ item.label }}</div>
              <span class="vec-compony">{{ item.ownedCompany }}</span>
            </template>
          </el-autocomplete> -->
          <!-- <el-select v-model="queryForm.vecno" :remote-method="querySearchVecNoAsync" :loading="vecNoLoading" filterable remote placeholder="请输入事故车牌号" size="mini" clearable required @change="getRteplanInfo" style="width:100%;">
            <el-option v-for="item in vecNoListOptions" :key="item.value" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}</span>
            </el-option>
          </el-select> -->
          <el-form label-width="80px" size="mini" class="detail-form" :loading="rteplanLoading" :model="queryForm" ref="queryForm">
            <el-form-item label="事故车牌:" :rules="$rulesFilter({ required: !checkedCar, type: 'LPN' })" prop="vecno">
              <!-- <span style="font-weight:bold;" v-if="queryForm.vecno">{{ queryForm.vecno }}</span>
              <span style="color:#ccc;" v-else>请先输入事故车牌号</span> -->
              <el-autocomplete popper-class="my-autocomplete" v-model="queryForm.vecno" :disabled="checkedCar" :fetch-suggestions="querySearchVecNoAsync" placeholder="请输入事故车牌号" @select="selectVecno"
                clearable size="mini" style="width: 66%">
                <template slot-scope="{ item }">
                  <div class="vec-no">{{ item.label }}</div>
                  <span class="vec-compony">{{ item.ownedCompany }}</span>
                </template>
              </el-autocomplete>
              <el-checkbox @change="carChange" v-model="checkedCar">无车牌</el-checkbox>
            </el-form-item>
            <el-form-item label="货物名称:" :rules="$rulesFilter({ required: true })" prop="prodPk">
              <el-select v-model="queryForm.prodPk" :remote-method="querySearchGoodsAsync" :loading="goodsLoading" filterable remote placeholder="货物名称" size="mini" clearable required
                @change="goodsSelectHandle">
                <el-option v-for="(item, index) in goodsListOptions" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="疏散半径">
              <el-input type="number" v-model="diffusion" @change="renderVecEmergencyPoi" placeholder="疏散半径（米）">
                <template slot="append">米</template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item label="GPS时间:" v-if="gpsinfo.updateTime">
              {{ gpsinfo.updateTime }}
            </el-form-item> -->
          </el-form>
        </div>
        <div class="content-module">
          <el-form label-width="75px" size="mini" class="detail-form">
            <el-form-item label="卫星定位:" v-if="queryForm.vecno&&gpsinfo.lonBd && gpsinfo.latBd||checkedCar">
              <span v-if="gpsinfo.lonBd && gpsinfo.latBd">{{
                gpsinfo.location
              }}</span>
              <span v-else>
                <el-button type="primary" size="mini" @click="manualPosition">
                  <span v-show="!isStartManualPosition">开启手动拾取定位</span>
                  <span v-show="isStartManualPosition">双击地图进行车辆定位</span>
                </el-button>
              </span>
            </el-form-item>
          </el-form>
          <template v-if="rteplanInfo">
            <el-form label-width="75px" size="mini" class="detail-form" :loading="rteplanLoading">
              <!-- <el-form-item label="牵引车号:">
                <span style="font-weight:bold;">{{rteplanInfo.tracCd}}</span>
              </el-form-item>
              <el-form-item label="货物名称:">
                <el-select v-model="rteplanInfo.prodPk" :remote-method="querySearchGoodsAsync" :loading="goodsLoading" filterable remote placeholder="货物名称" size="mini" clearable required @change="goodsSelectHandle">
                  <el-option v-for="item in goodsListOptions" :key="item.value" :label="item.label" :value="item.value">
                    {{item.label}}
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="货物数量:">{{ rteplanInfo.loadQty }}吨</el-form-item>
              <el-form-item label="运输公司:">{{
                rteplanInfo.carrierNm
              }}</el-form-item>
              <el-form-item label="牵引车号:">{{
                rteplanInfo.tracCd
              }}</el-form-item>
              <el-form-item label="挂车牌号:">{{
                rteplanInfo.traiCd
              }}</el-form-item>
              <el-form-item label="驾驶人员:">{{ rteplanInfo.dvNm
                }}<span v-if="rteplanInfo.dvMob">
                  （{{ rteplanInfo.dvMob }}）</span></el-form-item>
              <el-form-item label="押运人员:">{{ rteplanInfo.scNm
                }}<span v-if="rteplanInfo.scMob">
                  （{{ rteplanInfo.scMob }}）</span></el-form-item>
              <el-form-item label="托运企业:">{{
                rteplanInfo.consignorAddr
              }}</el-form-item>
              <el-form-item label="装货企业:">{{
                rteplanInfo.csnorWhseAddr
              }}</el-form-item>
              <el-form-item label="装货地点:">{{
                rteplanInfo.csnorWhseDist
              }}</el-form-item>
              <el-form-item label="收货企业:">{{
                rteplanInfo.csneeWhseAddr
              }}</el-form-item>
              <el-form-item label="收货地点:">{{
                rteplanInfo.csneeWhseDist
              }}</el-form-item>
            </el-form>
          </template>
          <!-- <template v-else>
            <div class="tips">
              <span v-if="queryForm.vecno" style="color:#76a6ff;">无电子运单信息</span>
              <span v-else>请先选择事故车牌号</span>
            </div>
          </template> -->
        </div>
        <div class="footer-module">
          <!-- <el-button size="mini" type="primary">运单信息</el-button> -->
          <el-popover placement="top-start" trigger="hover">
            <div class="text-center">
              <el-button size="mini" @click="historyTrackHandle" :disabled="!queryForm.vecno">历史轨迹</el-button>
            </div>
            <!--<el-button
              size="mini"
              slot="reference"
              :disabled="!queryForm.vecno"
              @click="currentTrackHandle"
              >当日轨迹
            </el-button>-->

          </el-popover>
          <el-button size="mini" @click="openSMSDialog">应急短信</el-button>
          <el-popover placement="top-start" trigger="hover">
            点击查看货物的应急处置
            <el-button size="mini" slot="reference" @click="enforceBtnHandle" :disabled="!queryForm.prodPk">应急处置</el-button>
          </el-popover>
          <el-popover placement="top-start" trigger="hover">
            点击查看应急专家信息
            <el-button size="mini" slot="reference" @click="showExpertList">应急专家</el-button>
          </el-popover>
        </div>
      </div>
    </div>
    <el-dialog title="应急救援专家名单" append-to-body :visible.sync="expertsDialogVisable" fullscreen @closed="expertsDialogVisable = false">
      <!-- 默认显示危化品专家 -->
      <expert-list :defaultSearchParams="{ cat_cd: 'expert.02' }"></expert-list>
    </el-dialog>
    <!-- 进度条 -->
    <timeline ref="timeline"></timeline>
    <!-- 应急处置 -->
    <el-dialog title="应急处置" :visible.sync="chemicaDialogvisible" append-to-body width="80%" class="detail-dialog">
      <chemica-info ref="chemicaInfo" :isCompn="true"></chemica-info>
    </el-dialog>
    <div v-show="false">
      <monitor-info-window ref="monitorInfoWindow" :selectVecInfo="selectVecInfo" :rteplan="selectVecRteplan" @close="closeInfoWindow"></monitor-info-window>
      <monitor-cp-info-window ref="monitorCpInfoWindow" :selectEntpInfo="selectEntpInfo" @close="closeInfoWindow"></monitor-cp-info-window>
    </div>
    <!-- <div style="position: absolute; width: 330px; height: 600px; right: 350px;  top: 20px;">
      <aliyunccc ref="aliyunccc" ></aliyunccc>
    </div> -->
    <el-dialog title="应急短信发送" append-to-body :visible.sync="smsDialogVisible" @closed="smsDialogVisible = false" :close-on-click-modal="false" :loading="smsDialogLoading">
      <el-form ref="smsForm" :model="smsForm" label-width="100px" size="small">
        <el-form-item prop="tracCd" label="车牌号：" :rules="$rulesFilter({required:true,type:'LPN'})">
          <el-input v-model="smsForm.tracCd"></el-input>
        </el-form-item>
        <el-form-item prop="accidentType" label="事故类型：" :rules="$rulesFilter({required:true})" :class="{'is-error':smsForm.accidentType==='其他' && smsForm.accidentTypeInputVal.length===0}">
          <el-radio v-model="smsForm.accidentType" :label="type" v-for="(type,index) in accidentTypeOptions" :key="index">
            <template v-if="type==='其他' && smsForm.accidentType==='其他'">其他 &nbsp;<el-input v-model="smsForm.accidentTypeInputVal" style="width:200px;" placeholder="请输入事故类型" clearable></el-input>
            </template>
          </el-radio>
        </el-form-item>
        <el-form-item label="默认接收人：">
          <el-tag
            style="margin-right: 8px"
            v-for="(tag,index) in notifyList"
            :key="index"
            :title=tag.mob
            type="success"
            @close="moveNotifyPer(index)"
            closable>
            {{tag.name}}
          </el-tag>
        </el-form-item>
        <el-form-item label="附加接收人：">
          <el-tag
            style="margin-right: 8px"
            v-for="(mob,index) in notifyListNew"
            :key="index"
            closable
            @close="moveNotifyNewPer(index)">
            {{mob}}
          </el-tag>
          <el-input
            style="width:120px;"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <el-button  v-else class="button-new-tag" size="small" @click="showInput">+ 接收人手机号</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="smsDialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="sendSMS" size="small">发送短信</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import bmapComp from "@/components/BMapComp";
import * as Tool from "@/utils/tool";
import imgsConfig from "static/jsonConfig/emergency.json";
import * as $http from "@/api/emergency";
import { getEntpByEntpPk } from "@/api/entp";
import * as $httpVec from "@/api/vec";
import { isLPN } from "@/utils/validate";
import HashMap from "@/utils/hashmap";
import expertList from "./components/resources-experts";
import timeline from "./components/timeline";
import chemicaInfo from "@/views/modules/base/chemica/chemica-info";
import MonitorInfoWindow from "./components/monitor-info-window";
import MonitorCpInfoWindow from "./components/monitor-cp-info-window";
import Aliyunccc from "@/components/aliyunccc";
import {debounce} from 'lodash'
export default {
  name: "support",
  components: {
    bmapComp,
    expertList,
    timeline,
    chemicaInfo,
    MonitorInfoWindow,
    MonitorCpInfoWindow,
    Aliyunccc
  },
  data() {
    return {
      diffusion:null,
      imgsConfig: imgsConfig,
      mapConfig: {
        scrollWheelZoom: true, //是否开启滚动
        isShowSatellite: true, // 是否显示卫星地图
        isShowDistance: true, // 是否显示测距
        isShowTraffic: true, // 是否显示路况
        mapHeight() {
          return (Tool.getClientHeight() - 50)+'px';
        },
        boundary: {
          strokeColor: "#4576EC",
          fillColor: "#DEE6F4",
          fillOpacity: 0.3
        }
      },
      size: "mini",
      searchItem: "",
      filterData: [], //物资过滤存放数组
      checkedCar: false, //是否知道车牌
      queryForm: {
        size: 1, // 泄露量大小 大0 小1 默认为0
        time: 0, // 时间 白天0 夜晚1 默认为0
        vecno: "", // 车牌
        prodPk: "" // 危险品pk
      },
      mapNodeLoading: null,

      // 左侧layer配置
      leftLayerIsFold: false,
      onImg: imgsConfig.rescue.eye_on,
      offImg: imgsConfig.rescue.eye_off,
      forceTypeList: [], // 救援力量列表
      resourceMap: {}, // 资源数据列表（根据资源类型分类，图例开关）
      overlayTeamMap: {}, // 对应于resourceMap的覆盖物（根据资源类型分类，图例开关）

      // 应急专家
      expertsDialogVisable: false,

      // 右侧layer配置
      rightLayerIsFold: false,

      // 选中的车辆运单信息
      vecNoLoading: false,
      vecNoListOptions: [], // 车牌号下拉列表

      rteplanLoading: false, // 电子运单loading
      rteplanInfo: null, // 电子运单信息
      goodsLoading: false, // 货物列表下拉loading
      goodsListOptions: [], // 货物列表

      // gps信息
      gpsinfo: {
        lonBd: null, // 经度
        latBd: null, // 纬度
        lonLat: null, // 经度，纬度
        location: null // 车辆定位
        // updateTime:null  // gps更新时间
      },
      manualPointMarker: null, // 手动拾取定位的marker
      isStartManualPosition: false,

      infoBox: null, // InfoBox弹窗
      infoBoxCache: null,
      selectVecInfo: {
        // InfoBox:已选车辆信息
        vehicleNo: "",
        speed: null,
        updateTime: null
      },
      selectVecRteplan: {
        // InfoBox：已选车辆电子路单信息
        vehicleNo: "",
        speed: null,
        updateTime: null
      },

      selectEntpInfo: {}, // 选择的生产使用企业信息
      chemicaDialogvisible: false, // 应急处置弹窗

      smsDialogVisible: false, // 应急短信弹窗
      smsDialogLoading: false,
      smsForm: {
        // 应急短信
        tracCd: "", // 车牌号
        accidentType: "", // 事故类型
        accidentTypeInputVal: "" // 手动输入事故类型
      },
      accidentTypeOptions: ["泄露", "侧翻", "碰撞", "其他"],
      notifyListJSON:'',//原始默认接收人数据
      notifyList:[],//默认接收人列表
      notifyListNew:[],//附加接收人列表
      inputVisible: false,
      inputValue: ''
    };
  },
  created() {
    this.getForceTypeList(); // 获取应急救援力量类型
    this.getNotifyList(); // 获取应急短信通知人列表
  },
  provide() {
    return {
      getMap: this.getMap
    };
  },
  watch: {
    "queryForm.vecno": {
      handler(newVal, oldVal) {
        this.resetData();
        this.resetLocation();
        if (isLPN(this.queryForm.vecno)) {
          this.getRteplanInfo(this.queryForm.vecno);
        }
      }
    }
  },
  mounted: function() {},
  destroyed() {},
  methods: {
    call(phone) {
      var value = {
        callee: phone,
        caller: "057422762615",
        publicKey: "",
        useVirNumCall: true,
        random: Math.random()
      };
      localStorage.setItem("workbenchSdkCall", JSON.stringify(value));
    },
    getMap() {
      return this.$refs.bmap.map;
    },
    getMapNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.bmap,
        spinner: "el-icon-loading"
      });
      return loading;
    },
    // 获取应急救援力量类型
    getForceTypeList() {
      let _this = this;
      $http
        .getEmergencyForceTypeList()
        .then(response => {
          _this.getRescouceList(); // 获取应急救援力量列表
          let res;
          if (response.code == 0) {
            res = response.data.map(it => {
              return {
                label: it.nmCn,
                value: it.cd,
                nmEn: it.nmEn,
                isShow: true
              };
            });
          } else {
            res = [];
          }
          this.forceTypeList = res.concat([
            {
              label: "物资",
              value: "supplies",
              nmEn: "supplies",
              isShow: true
            }
          ]);
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取应急短信通知人列表
    getNotifyList(){
      let _this = this;
      $http
        .getNotifyList()
        .then(res=>{
          if (res.code === 0 && res.data.paramValue){
            this.notifyListJSON = res.data.paramValue
          }
        })
    },
    // 移除默认接收人
    moveNotifyPer(index){
      this.notifyList.splice(index,1)
    },
    // 移除附加接收人
    moveNotifyNewPer(index){
      this.notifyListNew.splice(index,1)
    },
    // 显示输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 聚焦输入框
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        if (this.isPhoneAvailable(inputValue)){
          this.notifyListNew.push(inputValue);
        }else{
          this.$message.error('请输入正确手机号！');
        }
      }
      this.inputVisible = false;
      this.inputValue = '';
    },
    // 手机号正则验证
    isPhoneAvailable(phone) {
      const reg = /^1[3-9]\d{9}$/;
      return reg.test(phone);
    },
    // 获取应急救援力量列表
    getRescouceList() {
      let _this = this;
      Promise.all([
        this.getEmergencyForceList(),
        this.getEmergencyMaterialList()
      ]).then(results => {
        this.renderResourceData();
      });
    },
    // 获取救援力量数据
    getEmergencyForceList() {
      let _this = this;
      return new Promise((resolve, reject) => {
        $http
          .getEmergencyForceList({ limit: 999, page: 1 })
          .then(response => {
            if (response.code === 0) {
              response.page.list.forEach(item => {
                let catCd = item.catCd;
                // 存储各类应急救援资源数据
                if (!_this.resourceMap[catCd]) {
                  _this.$set(_this.resourceMap, catCd, []);
                }
                _this.resourceMap[catCd].push(item);
              });
              let keys = Object.keys(_this.resourceMap);
              // 绘制救援力量并隐藏
              keys.forEach(key => {
                _this.createForceOverlay(key, _this.resourceMap[key]);
              });
              resolve(response);
            } else {
              reject(response);
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 获取救援物资数据
    getEmergencyMaterialList() {
      let _this = this;
      return new Promise((resolve, reject) => {
        $http
          .getEmergencyMaterialList({ limit: 999, page: 1 })
          .then(response => {
            if (response.code === 0) {
              _this.$set(_this.resourceMap, "supplies", response.page.list);
              // console.log(_this.resourceMap)
              _this.createForceOverlay(
                "supplies",
                _this.resourceMap["supplies"]
              );
              resolve(response);
            } else {
              reject(response);
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 地图渲染的回调事件， 在地图上渲染应急救援力量图标（左侧的图例开关）
    renderResourceData() {
      let _this = this;
      // 绘制救援力量并隐藏
      // keys.forEach(key => {
      //   _this.createForceOverlay(key, _this.resourceMap[key]);
      // });
      // 设置默认显示的救援力量
      this.forceTypeList.forEach(it => {
        let catCd = it.value;
        let isShow = it.isShow;
        switch (catCd) {
          // 物资
          case "supplies":
            _this.showOrHideForce("supplies", isShow);
            break;
          default:
            //公安"power.01"
            //消防"power.02"
            //医院"power.03"
            //社会"power.04"
            //其他"power.10"
            _this.showOrHideForce(catCd, isShow);
            break;
        }
      });
    },
    // 显示/隐藏救援力量（类型）/救援物资
    showOrHideForce(catCd, isShow) {
      this.hideRescueDialog();
      let teamMap = this.overlayTeamMap[catCd];
      if (isShow) {
        if (teamMap) {
          if (this.searchItem != "") {
            this.filterData.forEach(obj => {
              let item = teamMap.get(obj.id);
              item.show();
            });
          } else {
            teamMap.keySet().forEach(key => {
              let item = teamMap.get(key);
              item.show();
            });
          }
        }
      } else {
        if (teamMap) {
          teamMap.keySet().forEach(key => {
            let item = teamMap.get(key);
            item.hide();
          });
        }
      }
    },
    // 创建救援力量（类型）/救援物资 覆盖物
    createForceOverlay(catCd, resourceArrayData) {
      let _this = this;
      let bmap = this.$refs.bmap;
      let teamMap = this.overlayTeamMap[catCd];
      if (!teamMap) {
        teamMap = this.overlayTeamMap[catCd] = new HashMap();
      }
      let typeArr = this.forceTypeList.filter(it => {
        return it.value === catCd;
      });
      let iconSrc;
      let type;
      if (typeArr.length) {
        type = typeArr[0].nmEn;
        iconSrc = imgsConfig.rescue[type];
      } else {
        return;
      }
      let overlay = [];
      resourceArrayData.forEach(subitem => {
        let icon = new BMap.Icon(iconSrc, new BMap.Size(32, 36));
        let location = subitem.location;
        if (!location) return;
        let lnglat = location.split(",");
        if (lnglat.length !== 2) {
          return;
        }
        let lng = lnglat[0],
          lat = lnglat[1];
        let point = new BMap.Point(lng, lat);
        let marker = new BMap.Marker(point, {
          icon: icon,
          title: subitem.catNmCn
        });
        marker.hide();
        marker.disableMassClear();
        marker.addEventListener("click", function() {
          _this.hideRescueDialog();
          let points = this.getPosition();
          let _dom = _this.createByType(catCd, subitem);
          _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);
          let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
            points,
            _dom
          ));
          bmap.addOverlay(createComplexCustomOverlay, "emergencyResource");
          // _this.$refs.bmap.map.setCenter(points);
        });
        teamMap.put(subitem.id, marker);
        // _this.$refs.bmap.map.addOverlay();
        // marker.show();
        overlay.push(marker);
      });
      if (overlay.length) {
        bmap.addOverlay(overlay, "emergencyResource");
      }
    },
    // 车牌号模糊搜索
    // querySearchVecNoAsync(queryString) {
    //   const _this = this;
    //   if (queryString) {
    //     this.vecNoLoading = true;
    //     $http
    //       .getFuzzyVecList(queryString)
    //       .then(response => {
    //         _this.vecNoLoading = false;
    //         if (response && response.code === 0) {
    //           _this.vecNoListOptions = response.page.list.map(it => {
    //             return {
    //               label: it.vecNo,
    //               value: it.vecNo,
    //               trailerNo: it.trailerNo,
    //               ownedCompany: it.ownedCompany
    //             };
    //           });
    //           _this.vecNoLoading = false;
    //         } else {
    //           _this.vecNoListOptions = [];
    //           _this.$message({
    //             message: response.msg,
    //             type: "error"
    //           });
    //         }
    //       })
    //       .catch(error => {
    //         console.log(error);
    //       });
    //   }
    // },
    // 车牌号模糊搜索
    querySearchVecNoAsync(queryString, cb) {
      const _this = this;
      if (queryString) {
        this.vecNoLoading = true;
        $http
          .getFuzzyVecList(queryString)
          .then(response => {
            _this.vecNoLoading = false;
            if (response && response.code === 0) {
              _this.vecNoListOptions = response.page.list.map(it => {
                return {
                  label: it.vecNo,
                  value: it.vecPk,
                  ownedCompany: it.ownedCompany
                };
              });
              _this.vecNoLoading = false;
            } else {
              _this.vecNoListOptions = [];
              _this.$message({
                message: response.msg,
                type: "error"
              });
            }
            cb(_this.vecNoListOptions);
          })
          .catch(error => {
            cb([]);
            console.log(error);
          });
      } else {
        cb([]);
      }
    },
    // 货物模糊搜索
    querySearchGoodsAsync(queryString) {
      const _this = this;
      if (queryString) {
        this.goodsLoading = true;
        $http
          .getChemList(queryString)
          .then(response => {
            _this.goodsLoading = false;
            if (response.code == 0) {
              _this.goodsListOptions = response.page.list.map(it => {
                return {
                  label: it.fullNmCn,
                  value: it.prodPk
                };
              });
            } else {
              _this.goodsListOptions = [];
            }
          })
          .catch(error => {
            _this.goodsLoading = false;
            console.log(error);
          });
      }
    },
    // 恢复初始值
    resetData() {
      // 关闭弹窗
      this.closeInfoWindow();
      // 清除地图上绘制的所有事故信息
      this.$refs.bmap.removeOverlayByName("accident");

      // 电子运单信息恢复
      this.rteplanInfo = null;
      // 货物信息恢复
      this.queryForm.prodPk = null;
      this.diffusion = null;
    },
    // 车辆定位
    resetLocation() {
      // 车辆GSP信息恢复
      this.gpsinfo.lonBd = null;
      this.gpsinfo.latBd = null;
      this.gpsinfo.lonLat = null;
      this.gpsinfo.location = null;
      // this.gpsinfo.updateTime = null;

      // 重置车辆定位
      this.removeManualPosition();
      this.isStartManualPosition = false;
    },
    // 点击选取下拉的车辆
    selectVecno(vecInfo) {
      this.queryForm.vecno = vecInfo.label;
      // this.getRteplanInfo(vecInfo.label);
    },
    // 获取车辆Gps
    getLatestGpsInfo(vecNos) {
      let _this = this;
      let bmap = this.$refs.bmap;
      return new Promise((resolve, reject) => {
        $httpVec
          .getLatestGpsTime(vecNos)
          .then(response => {
            if (response && response.length) {
              let res = response[0];
              // _this.gpsinfo.updateTime = res.updateTime;
              _this.gpsinfo.lonBd = res.lonBd;
              _this.gpsinfo.latBd = res.latBd;
              _this.gpsinfo.lonLat = res.lonBd + "," + res.latBd;

              bmap.geocoder(new BMap.Point(res.lonBd, res.latBd), str => {
                if (str) _this.gpsinfo.location = str;
              });
              resolve(res);
            } else {
              resolve(null);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取运单的详情
    getRteplanInfo(tracCd) {
      let _this = this;
      this.rteplanLoading = true;
      $http
        .getEmergencyLastRteplan(tracCd)
        .then(response => {
          _this.rteplanLoading = false;
          if (response.code == 0) {
            let data = response.data;
            if (data && data.length) {
              _this.rteplanInfo = data[0];
              // _this.querySearchGoodsAsync(_this.rteplanInfo.goodsNm);
              let hasCurrentProduct =
                _this.goodsListOptions.filter(it => {
                  return it.prodPk === _this.rteplanInfo.prodPk;
                }).length > 0;
              if (!hasCurrentProduct) {
                _this.goodsListOptions.push({
                  label: _this.rteplanInfo.goodsNm,
                  value: _this.rteplanInfo.prodPk
                });
              }
              _this.queryForm.prodPk = _this.rteplanInfo.prodPk;
              _this.renderVecEmergencyPoi();
            } else {
              _this.rteplanInfo = null;
              _this
                .getLatestGpsInfo(tracCd)
                .then(res => {
                  if (res) {
                    _this.renderVecEmergencyPoi({
                      location: res.lonBd + "," + res.latBd
                    });
                  } else {
                    _this.renderVecEmergencyPoi();
                  }
                })
                .catch(err => {
                  _this.renderVecEmergencyPoi();
                });
              // _this.renderVecEmergencyPoi();
            }
          } else {
            _this.rteplanInfo = null;
            _this.renderVecEmergencyPoi();
            _this.getLatestGpsInfo(tracCd).then(res => {
              if (res) {
                _this.renderVecEmergencyPoi({
                  location: res.lonBd + "," + res.latBd
                });
              } else {
                _this.renderVecEmergencyPoi();
              }
            });
          }
        })
        .catch(error => {
          _this.rteplanLoading = false;
          console.log(error);
        });
    },
    // 货物下拉切换
    goodsSelectHandle(value) {
      // const obj = this.goodsListOptions.find(item => {
      //   return item.value === value; // 筛选出匹配数据
      // });
      // if (obj) {
      //   this.queryForm.prodPk = obj.prodPk;
      // } else {
      //   this.queryForm.prodPk = "";
      // }
      this.queryForm.prodPk = value;
      this.renderVecEmergencyPoi();
    },
    // 泄露范围预测条件改变
    queryFormChange() {
      this.renderVecEmergencyPoi();
    },
    // 车辆实时位置及周边poi及扩散范围
    renderVecEmergencyPoi(extraParam) {
      let _this = this;
      let postdata = null;
      if (extraParam) {
        postdata = Object.assign({}, this.queryForm, extraParam);
      } else {
        postdata = Object.assign({}, this.queryForm);
      }
      if (this.rteplanInfo) {
        postdata.vecno = this.rteplanInfo.tracCd; // 通过牵引车去查询
      }
      if (this.manualPointMarker) {
        let position = this.manualPointMarker.getPosition();
        postdata.location = position.lng + "," + position.lat;
      }
      this.$refs.queryForm.validate(valid => {
        if (valid) {
          // 清除事故模拟绘制的地物及周边车辆
          this.$refs.bmap.removeOverlayByName("accident");

          // 获取周边地物
          $http
            .getVecEmergencyPoi(postdata)
            .then(response => {
              if (response.code == 0) {
                let res = response.data;
                if (res.lnglat == "") {
                  _this.$message.error("请手动拾取车辆定位");
                  return;
                }
                if (postdata.radius) {
                  // 若手动输入了扩散半径
                  _this.drawDiffusionRange(res.lnglat, null, postdata.radius); // 绘制疏散半径
                  _this.drawNearbyVec(res.nearcar); // 绘制附近车辆
                  _this.drawNearbyPoi(res.poi); // 绘制周边地物
                  _this.drawNearbyEntp(res.lnglat, postdata.radius); // 绘制周边企业
                } else {
                  if (res.initial === 0 && res.diffusion === 0) {
                  //   // 没有扩散半径
                  //   _this
                  //     .$prompt(
                  //       "该危化品无扩散半径，请手动选择扩散半径(米)",
                  //       "提示",
                  //       {
                  //         confirmButtonText: "确定",
                  //         // cancelButtonText: "取消",
                  //         showCancelButton: false,
                  //         inputPattern: /^\d+$/,
                  //         inputErrorMessage: "扩散半径的格式不正确"
                  //       }
                  //     )
                  //     .then(({ value }) => {
                        
                         !_this.diffusion && (_this.diffusion = 300) && _this.$message({
                          message:'该危化品无疏散半径，已默认设置疏散半径为300米',
                          type:'info',
                          duration:6000
                         })
                         
                        let tempData = { radius: _this.diffusion };
                        if (postdata.location) {
                          tempData.location = postdata.location;
                        }
                        _this.renderVecEmergencyPoi(tempData);
                      // })
                      // .catch(() => {});
                  } else {
                    !_this.diffusion && (_this.diffusion = res.diffusion)
                    _this.drawDiffusionRange(
                      res.lnglat,
                      res.initial,
                      _this.diffusion
                    ); // 绘制疏散半径
                    _this.drawNearbyVec(res.nearcar); // 绘制附近车辆
                    _this.drawNearbyPoi(res.poi); // 绘制周边地物
                    _this.drawNearbyEntp(res.lnglat, _this.diffusion); // 绘制周边企业
                  }
                }
              } else {
                _this.$message.error(response.msg);
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          _this.$message.error("请选择事故车辆及货物信息！");
        }
      });
    },
    /**
     * 显示疏散范围
     * initial紧急疏散范围
     * diffusion疏散距离
     *
     */
    drawDiffusionRange(lnglat, initial, diffusion) {
      if (!lnglat) return;
      let lnglatArr = lnglat.split(",");
      if (lnglatArr.length !== 2) return;
      let _this = this;
      let bmap = this.$refs.bmap;
      let centerPoint = new BMap.Point(lnglatArr[0], lnglatArr[1]);
      this.gpsinfo.lonBd = lnglatArr[0];
      this.gpsinfo.latBd = lnglatArr[1];
      let icon = new BMap.Icon(imgsConfig.vec.alarmMark, new BMap.Size(48, 48));
      let labelMsg = !initial
        ? `
        <div>疏散距离:${diffusion}米</div>
      `
        : `
        <div>初始隔离:${initial}米</div>
        <div>疏散距离:${diffusion}米</div>
      `;
      let label = new BMap.Label(labelMsg, {
        offset: new BMap.Size(35, 0)
      });
      label.setStyle({
        fontSize: "14px",
        backgroundColor: "rgba(255,0,0,0.48)",
        color: "#fff"
      });
      let marker = new BMap.Marker(centerPoint, { icon: icon });
      marker.setLabel(label);
      bmap.addOverlay(marker, "accident");
      marker.setAnimation(BMAP_ANIMATION_BOUNCE); //跳动的动画
      // 经纬度反解析
      bmap.geocoder(centerPoint, str => {
        if (str) _this.gpsinfo.location = str;
      });
      if (!initial) {
        // 初始距离不存在
        let circleOuter = new BMap.Circle(centerPoint, diffusion, {
          fillColor: "#FFE478",
          fillOpacity: 0.48,
          strokeWeight: 1,
          strokeColor: "transparent"
        });
        bmap.addOverlay(circleOuter, "accident");
        bmap.setViewport(circleOuter.getBounds());
        bmap.setCenter(centerPoint);
        return;
      }
      let circleOuter = new BMap.Circle(centerPoint, diffusion, {
        fillColor: "#FFE478",
        fillOpacity: 0.48,
        strokeWeight: 1,
        strokeColor: "transparent"
      });
      bmap.addOverlay(circleOuter, "accident");

      let circleInner = new BMap.Circle(centerPoint, initial, {
        fillColor: "#F73939",
        fillOpacity: 0.48,
        strokeWeight: 1,
        strokeColor: "transparent"
      });
      bmap.addOverlay(circleInner, "accident");
      bmap.setViewport(circleOuter.getBounds());
      bmap.setCenter(centerPoint);
    },
    // 显示附近车辆
    drawNearbyVec(veclist) {
      let _this = this;
      let bmap = this.$refs.bmap;
      veclist.forEach((item, i) => {
        let point = new BMap.Point(item.lonBd, item.latBd);
        let runStatus = item.speed > 0 ? "run" : "stop";
        let updateTime = new Date().getTime() - item.updateTimeStamp;
        if (item.state == 0 || updateTime > 10 * 1000 * 60) {
          // 离线
          runStatus = "offline";
        }
        // carType：0：空车，1：重车，2：未知
        let icon = new BMap.Icon(
          imgsConfig.vec[
            `${runStatus}_${item.carType == 4 ? 2 : item.carType}`
          ],
          new BMap.Size(24, 24)
        );
        let marker = new BMap.Marker(point, { icon: icon });
        marker.setRotation(item.direction);
        bmap.addOverlay(marker, "accident");

        marker.addEventListener("click", function() {
          _this.openVecInfoWindow(item._id, item, this);
        });
      });
    },
    // 显示附近生产使用企业
    drawNearbyEntp(lnglat, radius) {
      let _this = this;
      let bmap = this.$refs.bmap;
      if (!lnglat) return;
      let lnglatArr = lnglat.split(",");
      if (lnglatArr.length !== 2) return;
      $http.getCplistNearbyVec(lnglatArr[0], lnglatArr[1], radius).then(res => {
        if (res.code == 0) {
          res.data.forEach((item, i) => {
            let center_point = item.center_point;
            if (!center_point) {
              return;
            }
            let lonLatArr = center_point.split(",");
            if (lonLatArr.length !== 2) {
              return;
            }
            let point = new BMap.Point(lonLatArr[0], lonLatArr[1]);
            let icon = new BMap.Icon(
              imgsConfig.rescue.company,
              new BMap.Size(32, 36)
            );
            let marker = new BMap.Marker(point, { icon: icon });
            bmap.addOverlay(marker, "accident");
            marker.addEventListener("click", function() {
              _this.openEntpInfoWindow(item.entp_pk, item, this);
            });
          });
        }
      });
    },
    // 打开车辆信息
    openVecInfoWindow(name, vecinfo, marker) {
      let _this = this;
      let map = this.$refs.bmap.map;
      if (marker) {
        map.panTo(marker);
      }
      // this.mapNodeLoading && this.mapNodeLoading.close();
      // this.mapNodeLoading = this.getMapNodeLoading();
      // 获取车辆详情
      $http
        .getEmergencyLastRteplan(name)
        .then(res => {
          if (res.code == 0) {
            let rteplan = res.data && res.data.length > 0 ? res.data[0] : null;
            _this.selectVecInfo.vehicleNo = name;
            _this.selectVecInfo.speed = vecinfo.speed;
            _this.selectVecInfo.updateTime = vecinfo.updateTime;
            _this.selectVecRteplan = rteplan;
            var infoBox = new BMapLib.InfoBox(
              map,
              _this.$refs.monitorInfoWindow.$el,
              {
                boxStyle: {
                  width: "340px",
                  marginBottom: "15px",
                  marginLeft: "10px"
                },
                closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
            });
            infoBox.open(marker);
            infoBox.show();
            infoBox = null;
          } else {
            this.$message({
              message: "对不起，无法查看该车信息",
              type: "error"
            });
          }
          // _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          // _this.mapNodeLoading.close();
        });
    },
    // 打开车辆周边生产使用企业详情
    openEntpInfoWindow(pk, entpInfo, marker) {
      let _this = this;
      let map = this.$refs.bmap.map;
      if (marker) {
        map.panTo(marker);
      }
      // 获取企业详情
      getEntpByEntpPk(pk)
        .then(res => {
          if (res.code == 0 && res.data) {
            _this.selectEntpInfo = res.data.entp;
            var infoBox = new BMapLib.InfoBox(
              map,
              _this.$refs.monitorCpInfoWindow.$el,
              {
                boxStyle: {
                  width: "340px",
                  marginBottom: "30px",
                  marginRight: "18px"
                },
                // closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
            });
            infoBox.open(marker);
            infoBox.show();
            infoBox = null;
          } else {
            this.$message({
              message: "对不起，无法查看该企业信息",
              type: "error"
            });
          }
          // _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          // _this.mapNodeLoading.close();
        });
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoBoxCache) {
        this.infoBoxCache.close();
        this.infoBoxCache = null;
      }
    },
    // 周边地物图标
    poiIconImg(data) {
      switch (data) {
        case data.indexOf("公司") > -1:
          return imgsConfig.poi.entp;
          break;
        case data.indexOf("教育") > -1:
          return imgsConfig.poi.teach;
          break;
        case data.indexOf("交通") > -1:
          return imgsConfig.poi.traffic;
          break;
        case data.indexOf("政府") > -1:
          return imgsConfig.poi.government;
          break;
        case data.indexOf("医") > -1:
          return imgsConfig.poi.hospital;
          break;
        case data.indexOf("广场") > -1:
          return imgsConfig.poi.arder;
          break;
        default:
          return imgsConfig.poi.weizhi;
          break;
      }
    },
    //创建详情框
    createComplexCustomOverlay(points, labelText, bgColor, state) {
      let _this = this;
      let mp = this.$refs.bmap.map;
      let _dom = arguments.length == 2 ? arguments[1] : null;

      bgColor = bgColor || "#67C23A";
      state = state || "";

      function ComplexCustomOverlay(points, labelText) {
        this._point = points;
        this._text = labelText;
      }

      ComplexCustomOverlay.prototype = new BMap.Overlay();

      ComplexCustomOverlay.prototype.initialize = function(map) {
        this._map = map;
        this._domOffsetHeight = 0;
        let div;

        if (_dom) {
          div = this._div = _dom;
          this._arrow = _dom.querySelector(".arrow");
        } else {
          div = this._div = document.createElement("div");
          div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
          div.style.cssText =
            "position:absolute;background-color:" +
            bgColor +
            ";border:1px solid " +
            bgColor +
            ";border-radius:3px;padding:4px;color:#fff;width:106px;font-size:12px;text-align:center;";

          var span = (this._span = document.createElement("span"));
          div.appendChild(span);
          span.appendChild(document.createTextNode(this._text + " " + state));

          var arrow = (this._arrow = document.createElement("div"));
          arrow.style.cssText =
            "position:absolute;top:22px;left:46px;width:0px;height:0px;border:6px solid transparent;border-top-color:" +
            bgColor +
            ";";

          div.appendChild(arrow);
        }
        let closeBtn = div.querySelector(".close");
        if (closeBtn) {
          closeBtn.addEventListener("click", () => {
            _this.hideRescueDialog();
          });
        }

        mp.getPanes().labelPane.appendChild(div);
        this._domOffsetHeight = div.offsetHeight;
        return div;
      };

      ComplexCustomOverlay.prototype.draw = function() {
        let map = this._map;
        let pixel = map.pointToOverlayPixel(this._point);

        this._div.style.left =
          pixel.x - parseInt(this._arrow.style.left) - 6 + "px";
        this._div.style.top =
          pixel.y - parseFloat(this._domOffsetHeight) - 26 + "px";
      };

      let myCompOverlay = new ComplexCustomOverlay(points, labelText);

      return myCompOverlay;
    },
    //展示poi
    drawNearbyPoi(data) {
      let _this = this;
      let bmap = this.$refs.bmap;
      data.forEach((item, i) => {
        let point = new BMap.Point(item.location.lng, item.location.lat);
        let icon = new BMap.Icon(
          _this.poiIconImg(item.name),
          new BMap.Size(35, 35)
        );
        let marker = new BMap.Marker(point, { icon: icon });
        bmap.addOverlay(marker, "accident");
        marker.addEventListener("click", function() {
          _this.hideRescueDialog();
          let points = this.getPosition();
          let _dom = _this.createPoiInfo(item);
          _dom.style.zIndex = BMap.Overlay.getZIndex(points.lat);

          let createComplexCustomOverlay = (_this.rescuceComplexCusTomOverlay = _this.createComplexCustomOverlay(
            points,
            _dom
          ));

          bmap.addOverlay(createComplexCustomOverlay, "accident");
          bmap.setCenter(points);
        });
      });
    },
    // poi信息详情
    createPoiInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(220, 105, 38, 0.6);width:320px;border:1px solid #e07130;
      border-radius:3px;padding:10px;font-size:13px;color:#fff;line-height: 1.6;
      `;

      let _html = `
        <div>名称 : ${item.name}</div>
        <div>距离 : ${item.distants.toFixed(2)}米</div>
        <div>地区 : ${item.province}${item.city}${item.area}</div>
        <div>详细地址 : ${item.address}</div>
      `;
      _dom.innerHTML = _html;

      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#fff;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);

      var arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#e07130;";

      _dom.appendChild(arrow);

      return _dom;
    },
    // 根据救援力量（类型）/救援物资，展示弹窗内容
    createByType(type, subitem) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `position:absolute;background-color:rgba(113, 179, 255, 0.9);width:320px;border:1px solid #ccc;
      border-radius:3px;padding:10px;font-size:13px;color:#000;line-height: 1.6;
      `;
      let _html;
      switch (type) {
        //公安
        case "power.01":
          _html = `
                  <div>单位名称 : ${subitem.unitName || ""}</div>
                  <div>分管领导 : ${subitem.leader || ""}</div>
                  <div>所在地址 : ${subitem.address || ""}</div>
                  <div>队员人数 : ${subitem.personNum || 0}人</div>
                  <div>救援装备 : ${subitem.equipment || ""}</div>
                `;
          break;
        //消防
        case "power.02":
          _html = `
                  <div>消防队 : ${subitem.unitName || ""}</div>
                  <div>分管领导 : ${subitem.leader || ""}</div>
                  <div>所在地址 : ${subitem.address || ""}</div>
                  <div>队员人数 : ${subitem.personNum || 0}人</div>
                  <div>救援装备 : ${subitem.equipment || ""}</div>
                `;
          break;
        //医院
        case "power.03":
          _html = `
                  <div>医院名称 : ${subitem.unitName || ""}</div>
                  <div>联系电话 : ${subitem.leader || ""}</div>
                  <div>所在地址 : ${subitem.address || ""}</div>
                  <div>队员人数 : ${subitem.personNum || 0}人</div>
                `;
          break;
        //社会
        case "power.04":
          _html = `
                  <div>单位名称 : ${subitem.unitName || ""}</div>
                  <div>分管领导 : ${subitem.leader || ""}</div>
                  <div>所在地址 : ${subitem.address || ""}</div>
                  <div>队员人数 : ${subitem.personNum || 0}人</div>
                  <div>救援装备 : ${subitem.equipment || ""}</div>
                `;
          break;
        //其他
        case "power.10":
          _html = `
                  <div>单位名称 : ${subitem.unitName || ""}</div>
                  <div>分管领导 : ${subitem.leader || ""}</div>
                  <div>所在地址 : ${subitem.address || ""}</div>
                  <div>队员人数 : ${subitem.personNum || 0}人</div>
                  <div>救援装备 : ${subitem.equipment || ""}</div>
                `;
          break;
        // 物资
        case "supplies":
          _html = `
                  <div>用品名称 : ${subitem.suppliesNm || ""}</div>
                  <div>储备数量 : ${subitem.suppliesNum || ""}</div>
                  <div>储备单位 : ${subitem.reserveUnit || ""}</div>
                  <div>联系人名 : ${subitem.contactPerson || ""}</div>
                  <div>联系电话 : ${subitem.mobile || ""}</div>
                  <div>单位地址 : ${subitem.address || ""}</div>
                `;
          break;
      }
      _dom.innerHTML = _html;
      let closeBtn = document.createElement("div");
      closeBtn.className = "close";
      closeBtn.innerHTML = "×";
      closeBtn.style.cssText = `position:absolute;right:8px;top:-3px;font-size:17px;color:#000;cursor:pointer;
      `;
      _dom.appendChild(closeBtn);
      let arrow = document.createElement("div");
      arrow.className = "arrow";
      arrow.style.cssText =
        "position:absolute;bottom:-12px;left:154px;width:0px;height:0px;border:6px solid transparent;border-top-color:#71b3ff;";
      _dom.appendChild(arrow);
      return _dom;
    },
    // 隐藏救援力量弹窗
    hideRescueDialog() {
      this.fireTeam = "";
      if (this.rescuceComplexCusTomOverlay) {
        this.$refs.bmap.map.removeOverlay(
          this.rescuceComplexCusTomOverlay.hide()
        );
        this.rescuceComplexCusTomOverlay = null;
      }
    },
    // 左侧图例开关的点击事件
    showOrhide(item) {
      item.isShow = !item.isShow;
      this.showOrHideForce(item.value, item.isShow);
    },
    // 显示应急专家名单
    showExpertList() {
      this.expertsDialogVisable = true;
    },
    // 历史轨迹
    historyTrackHandle() {
      window.open(
        location.origin +
          location.pathname +
          "#/monit/hisTrack?v=" +
          encodeURIComponent(this.queryForm.vecno) +
          "&t=" +
          Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },
    // 当日轨迹
    currentTrackHandle() {
      if (isLPN(this.queryForm.vecno)) {
        this.getVehicleTraceData(
          // 获取第一个日期的轨迹
          this.queryForm.vecno,
          Tool.formatDate(new Date(), "yyyy-MM-dd")
          // this.rteplanInfo.vecDespTm
        );
      } else {
        this.$message.error("车牌号不正确，请输入正确的车牌号！");
      }
    },
    // 获取车辆轨迹数据，并绘制车辆轨迹
    getVehicleTraceData(vehicleNo, date) {
      let _this = this;
      let param = {
        startTime: date + " 00:00:00",
        endTime: date + " 23:59:59",
        vehicleNo: vehicleNo
      };
      this.loading = true;
      // 获取轨迹
      $http
        .getTrack(param)
        .then(response => {
          this.loading = false;
          if (response.code == 0) {
            if (response.data.length) {
              _this.$refs.timeline.showTrace(vehicleNo, date, response.data);
            } else {
              _this.$message.info("车辆无轨迹信息！");
            }
          } else {
            _this.$message.error(response.msg);
          }
        })
        .catch(error => {
          this.loading = false;
          console.log(error);
        });
    },
    // 应急处置
    enforceBtnHandle() {
      this.chemicaDialogvisible = true;
      this.$nextTick(() => {
        this.$refs.chemicaInfo.getInfoByProdPk(this.queryForm.prodPk);
      });
    },
    // 移除手动拾取的定位
    removeManualPosition() {
      this.isStartManualPosition = false;
      if (this.manualPointMarker) {
        this.manualPointMarker.removeEventListener(
          "dragend",
          this._pointDragHandle
        );
        this.manualPointMarker = null;
      }
      this.$refs.bmap.map.removeEventListener(
        "dblclick",
        this.getPointLatAndLon
      );
    },
    // 绑定手动拾取定位事件
    manualPosition() {
      this.isStartManualPosition = true;
      this.$refs.bmap.map.addEventListener("dblclick", this.getPointLatAndLon);
    },
    // 手动拾取定位操作
    getPointLatAndLon(e) {
      let bmap = this.$refs.bmap;
      if (!this.manualPointMarker) {
        let icon = new BMap.Icon(
          imgsConfig.vec.alarmMark,
          new BMap.Size(48, 48)
        );
        let marker = new BMap.Marker(e.point, { icon: icon }); // 创建标注
        let msg = "当前位置经纬度：" + e.point.lng + "," + e.point.lat;
        let label = new BMap.Label(msg, {
          offset: new BMap.Size(20, -10)
        });
        marker.setLabel(label);
        this.manualPointMarker = marker;
        bmap.addOverlay(marker, "accident"); // 将标注添加到地图中
        // marker.disableDragging();// 不可拖拽
        this.manualPointMarker.enableDragging(); // 可拖拽
        this.manualPointMarker.addEventListener(
          "dragend",
          this._pointDragHandle
        );
        this.gpsinfo.location = this.manualPointMarker.getPosition();
      } else {
        this.manualPointMarker.setPosition(e.point);
      }
      this.renderVecEmergencyPoi();
    },
    // 拖拽手动拾取定位的经纬度的回调函数
    _pointDragHandle(e) {
      let map = this.$refs.bmap.map;
      map.removeOverlay(e.target.getLabel());
      let msg = "当前位置经纬度：" + e.point.lng + "," + e.point.lat;
      let label = new BMap.Label(msg, {
        offset: new BMap.Size(20, -10)
      });
      // label.setStyle({
      //   display: "none"
      // }); //对label 样式隐藏
      this.manualPointMarker.setLabel(label);

      this.renderVecEmergencyPoi();
    },
    //搜索物资
    handleChange(item) {
      this.getSuppliesList(item);
    },
    //获取列表
    getSuppliesList(param) {
      this.showOrHideForce("supplies", false);
      let arr = this.resourceMap["supplies"];
      let data = (this.filterData = arr.filter(item => {
        return item.suppliesNm.indexOf(param) > -1;
      }));
      if (!data.length) {
        this.$message.error("未搜索到相关物资");
        return;
      }
      let teamMap = this.overlayTeamMap["supplies"];

      if (teamMap) {
        if (this.searchItem == "") {
          teamMap.keySet().forEach(key => {
            let item = teamMap.get(key);
            item.show();
          });
        } else {
          data.forEach(obj => {
            let item = teamMap.get(obj.id);
            item.show();
          });
        }
      }
    },
    carChange(e) {
      if (e) {
        this.queryForm.vecno = "";
      }
    },
    // 打开应急短信弹窗
    openSMSDialog() {
      this.notifyList = JSON.parse(this.notifyListJSON)
      this.smsDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.smsForm.resetFields();
        if (this.queryForm.vecno.length) {
          this.smsForm.tracCd = this.queryForm.vecno;
        }
      });
    },
    // 发送应急短信
    sendSMS:debounce(function(){
      this.$refs.smsForm.validate(valid => {
        if (valid) {
          let postdata = {
            tracCd: this.smsForm.tracCd,
            accidentType: this.smsForm.accidentType
          };
          if (
            this.smsForm.accidentType === "其他" &&
            this.smsForm.accidentTypeInputVal.length === 0
          ) {
            this.$message.error("请手动输入事故类型");
            return;
          }
          this.smsForm.accidentType === "其他" &&
            (postdata.accidentType = this.smsForm.accidentTypeInputVal);
          // 默认接收人 & 附加接收人 手机号组合
          let mobs = this.notifyList.map(item => {
            return item.mob
          })
          mobs = [...mobs, ...this.notifyListNew]
          if (!mobs.length){
            this.$message.error("无接收人信息！");
            return
          }
          postdata.mobs = mobs.join(",")
          let _this = this;
          _this.smsDialogLoading = true;
          $http
            .sendSMS(postdata)
            .then(res => {
              _this.smsDialogLoading = false;
              if (res.code === 0) {
                _this.smsDialogVisible = false;
                _this.$message.success("提交成功");
                _this.$refs.smsForm.resetFields();
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch(() => {
              _this.smsDialogLoading = false;
              _this.smsDialogVisible = false;
            });
        } else {
          this.$message.error("请正确填写信息！");
        }
      });
    },1000,{ leading: true, trailing: false })
  }
};
</script>

<style lang="scss" scoped>
.left-top-layer {
  box-sizing: border-box;
  position: absolute;
  width: 130px;
  left: 10px;
  top: 10px;
  overflow: hidden;
  h5 {
    padding: 8px 0;
    text-align: center;
    margin: 0;
    background: #296ae3;
    color: #fff;
    border-radius: 5px 5px 0 0;
    border: 1px solid #296ae3;
    border-bottom: none;
    z-index: 10;
    cursor: pointer;
  }
  .content {
    padding: 5px 2px;
    border: 1px solid #296ae3;
    border-top: none;
    background-color: #2b2828;
    border-radius: 0 0 5px 5px;
    ul.list {
      padding: 0;
      margin: 0;
      width: 100%;
      box-sizing: border-box;
      li {
        display: flex;
        align-items: center;
        cursor: pointer;
        &:last-child {
          border: none;
        }
        .lf.rt {
          line-height: 38px;
        }
        .lf {
          padding-left: 10px;
          display: flex;
          align-items: center;
          .fnt {
            color: #fff;
            font-size: 15px;
            padding-left: 5px;
            line-height: 24px;
          }
          img {
            width: 24px;
            height: 24px;
          }
          flex: 1 auto;
        }
        .rt {
          flex: 0 auto;
          width: 38px;
          .on {
            display: block;
            width: 24px;
            height: 24px;
            margin: 8px;
          }
        }
      }
    }
  }

  &.is-fold {
    h5 {
      position: absolute;
      left: 0;
      right: 0;
      border-radius: 5px;
    }
    .content {
      transform: translateY(-100%);
      transition: transform 0.2s linear;
    }
  }
}
.padding-tb {
  padding: 10px 0;
}
.right-top-layer {
  position: absolute;
  width: 280px;
  right: 10px;
  top: 10px;
  bottom: 40px;
  display: flex;
  flex-direction: column;
  &.is-fold {
    transform: translateX(100%);
    transition: transform 0.2s linear;
  }
  h5 {
    padding: 10px;
    text-align: center;
    margin: 0;
    background: #296ae3;
    color: #fff;
    border-radius: 10px 10px 0 0;
  }
  .layer-btn {
    cursor: pointer;
    position: absolute;
    left: -30px;
    top: 10px;
    // color: #333;
    // background: #fff;
    padding: 5px;
    box-shadow: 0 0 38px #3f97e2;
    border-radius: 10px;
    background: #296ae3;
    color: #fff;
  }

  &-box {
    box-shadow: 0 0 18px #3f97e2;
    border-radius: 10px;
    z-index: 2;
    background-color: #fff;
    color: #333;
    // padding: 10px;
    margin-top: 10px;

    :first-child {
      margin-top: 0;
    }

    &.responsive {
      overflow: hidden;
      flex: 1 auto;
      display: flex;
      flex-direction: column;
      .header-module,
      .content-module,
      .footer-module {
        padding: 10px;
      }
      .header-module,
      .footer-module {
        text-align: center;
        flex: 0 auto;
        // background-color: #e2eaf9;
      }
      .content-module {
        box-sizing: border-box;
        position: relative;
        height: calc(100vh - 400px);
        flex: 1 auto;
        overflow-y: auto;
        background-color: #f5f5f5;
        .tips {
          position: absolute;
          width: calc(100% - 20px);
          top: 50%;
          margin-top: -20px;
          color: #ccc;
          text-align: center;
          box-sizing: border-box;
        }
      }
      .footer-module {
        padding: 5px 10px;
      }
    }
  }
}
.my-autocomplete {
  .el-autocomplete-suggestion li {
    line-height: normal !important;
    padding: 7px !important;
  }
  li {
    line-height: normal !important;
    padding: 7px !important;

    .vec-no {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .vec-compony {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
.el-form.detail-form /deep/ {
  .el-form-item {
    margin-bottom: 10px !important;
    .el-form-item__label,
    .el-form-item__content {
      font-size: 13px;
      line-height: 20px;
    }
  }
}
.search-wape {
  position: absolute;
  top: 260px;
  z-index: 1000;
  margin-left: 10px;
  padding: 0px;
  width: 130px;
  overflow: hidden;
  padding-bottom: 5px;
}
/deep/ .el-checkbox__label {
  padding-left: 1px;
}
</style>
