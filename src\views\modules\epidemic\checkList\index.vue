<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getDataList"
      class="grid-search-bar"
    >
    </searchbar>
    <el-table
      :data="dataList"
      border
      :max-height="tableHeight"
      v-loading="dataListLoading"
      style="width: 100%;"
    >
      <el-table-column prop="chkAddress" :label="chkType===1?'卡口名称':'企业名称'"></el-table-column>
      <el-table-column prop="chkTm" label="日期"></el-table-column>
      <el-table-column prop="usrNm" label="姓名" width="90">
        <template slot-scope="scope">
          <el-button type="text" @click="showCode(scope.row)">{{scope.row.usrNm}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="usrIdcard" label="身份证号"></el-table-column>
      <el-table-column prop="jobNm" label="岗位"></el-table-column>
      <el-table-column prop="tracCd" label="牵引车"></el-table-column>
      <el-table-column prop="traiCd" label="挂车"></el-table-column>
      <el-table-column prop="upPoint" label="位置" v-if="chkType===2">
        <template slot-scope="scope">
          <div v-if="scope.row.upPoint" style="display:flex;cursor:pointer" @click="position(scope.row)">
            位置<img class="position" src="@/assets/zhenCode/positionIcon.png" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="goodsNm" label="货物名称"></el-table-column>
      <el-table-column prop="scResult" label="浙运安全码"></el-table-column>
      <el-table-column prop="jkmResult" label="健康码">
        <!--     <template slot-scope="scope">
               <el-button type="text" @click="showImg(scope.row.hcUrl)">{{scope.row.jkmResult}}</el-button>
        </template>-->
      </el-table-column>
      <!--<el-table-column prop="xcmResult" label="行程码"></el-table-column>-->
      <el-table-column prop="hsjcResult" label="核酸检测报告">
        <!--        <template slot-scope="scope">-->
        <!--          <el-button type="text" @click="showImg(scope.row.naUrl)">{{scope.row.hsjcResult}}</el-button>-->
        <!--        </template>-->
      </el-table-column>
      <el-table-column prop="zymStatus" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.zymStatus===1" type="success">正常</el-tag>
          <el-tag v-if="scope.row.zymStatus===9" type="danger">异常</el-tag>
          <el-tag v-if="scope.row.zymStatus===5 || !scope.row.zymStatus" type="info">未申领</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="sizes, prev, pager, next, total"
      :page-sizes="[20, 30, 50, 100, 200]"
      style="float:right;"
      :page-size="pagination.limit"
      :current-page.sync="pagination.page"
      :total="pagination.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
    <!--  镇疫码弹窗  -->
    <el-dialog
      title="危运疫码通"
      :visible.sync="codeDialogVisible"
      :show-close="true"
      width="450px">
      <code-dialog ref="codeDialogRef" :zhzymInfo="zhzymInfo"  v-loading="loading" :realTime="false"></code-dialog>
      <span slot="footer" class="dialog-footer">
<!--        <el-button type="primary" @click="codeDialogVisible = false" size="mini">确 定</el-button>-->
      </span>
    </el-dialog>
    <!--    地图-->
    <el-dialog
      title="位置"
      :visible.sync="dialogVisible"
      width="60%">
      <div style="height:500px;" class="map-container">
        <div class="map-content" ref="mapRef"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import * as API from "@/api/epidemic";
  import * as Tool from "@/utils/tool";
  import Searchbar from "@/components/Searchbar";
  import {getFuzzyTracCd} from "@/api/vec";
  import Viewer from "viewerjs";
  import dayjs from 'dayjs'
  import CodeDialog from "../components/codeDialog"
  import imgsConfig from "../../../../../static/jsonConfig/parking.icon.json";
  export default {
    name: 'checkList',
    data() {
      return {
        searchItems: {
          normal: [
            {
              name: "时间",
              field: "chkTm",
              type: "daterange",
              dbfield: "chk_tm",
              dboper: "bt",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              default: [
                dayjs().format("YYYY-MM-DD") + " 00:00:00",
                dayjs().format("YYYY-MM-DD") + " 23:59:59"
              ]
            },
            {name: '姓名', field: 'usrNm', type: 'text', dbfield: 'usr_nm', dboper: 'cn'},
            {name: '身份证号', field: 'usrIdcard', type: 'text', dbfield: 'usr_idcard', dboper: 'cn'},
            {
              name: "牵引车",
              field: "tracCd",
              type: "fuzzy",
              dbfield: "trac_cd",
              dboper: "eq",
              api: this.getTracCd
            },
            {
              name: "状态",
              field: "zymStatus",
              type: "select",
              dbfield: "zym_status",
              dboper: "eq",
              options: [
                {
                  label: "正常",
                  value: 1,
                },
                {
                  label: "异常",
                  value: 9,
                },
                {
                  label: "未申领",
                  value: 5,
                }
              ],
            },
            // {
            //   name: "挂车号",
            //   field: "traiCd",
            //   type: "fuzzy",
            //   dbfield: "trai_cd",
            //   dboper: "eq",
            //   api: this.getTraiCd
            // }

          ]
        },
        tableHeight: Tool.getClientHeight() - 210,
        dataList: [],
        pagination: {
          page: 1,
          limit: 15,
          total: 0
        },
        dataListLoading: false,
        loading: false,
        dataListSelections: [],
        tracList: [], //牵引车模糊搜索列表
        traiList: [], //挂车模糊搜索列表
        codeDialogVisible: false,
        zhzymInfo: [],
        dialogVisible: false,
        map: null,
      };
    },
    components: {
      Searchbar,
      CodeDialog
    },
    computed: {
      chkType() {
        //chkType =1 卡口; chkType =2 场所
        return this.$route.name.indexOf('checkPoint') > -1 ? 1 : 2;
      }
    },
    watch: {
      chkType(newValue, oldValue) {
        this.getDataList();
        this.setTableHeight();
      }
    },
    mounted: function () {
      window.addEventListener("resize", this.setTableHeight);

      let query = this.$route.query;
      this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
      this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
      this.pagination.total = this.pagination.page * this.pagination.limit;
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getDataList();
    },
    methods: {
      // 改变搜索框的高度
      resizeSearchbar() {
        this.setTableHeight();
      },
      setTableHeight() {
        this.$nextTick(() => {
          this.tableHeight =
            Tool.getClientHeight() - 170 - this.$refs.searchbar.$el.offsetHeight;
        });
      },

      // 导出表格
      exportTable() {
        let param = {filters: this.$refs.searchbar.get()}
        API.download(param).then(res => {
          let a = document.createElement('a');
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download = '作业人员体温信息_' + (_date.getFullYear() + '-' + (_date.getMonth() + 1) + '-' + _date.getDate()) + '.xlsx';
          a.click();
          window.URL.revokeObjectURL(url);
        })
          .catch(err => {

          })
      },
      // 获取数据列表
      getDataList(data) {
        let filters;
        if (data) {
          if (data.resetCurrentPage) {
            this.pagination.page = 1;
          }
          if (data.searchData) {
            filters = data.searchData;
          }
        } else {
          filters = this.$refs.searchbar.get();
        }
        //区分类型
        filters.rules.push({field: 'chk_type', op: 'eq', data: this.chkType})
        this.dataListLoading = true;
        let params = Object.assign({filters: filters}, this.pagination);
        API.zhzymcheckList(params).then(response => {
          if (response && response.code === 0) {
            this.pagination.total = response.page.totalCount;
            this.dataList = response.page.list;
          } else {
            this.dataList = [];
            this.pagination.total = 0;
          }
          this.dataListLoading = false;
        });
      },
      // 分页跳转
      handleCurrentChange: function (val) {
        this.pagination.page = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      // 分页条数修改
      handleSizeChange: function (val) {
        this.pagination.limit = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      //牵引车模糊搜索
      async getTracCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.tracList = [];
            return;
          }
          this.tracList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.tracList);
        }
      },
      //挂车模糊搜索
      async getTraiCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.traiList = [];
            return;
          }
          this.traiList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.traiList);
        }
      },
      //显示图片
      showImg(url) {
        let divNode = document.createElement("div");
        divNode.style.display = "none";
        let imageNode = document.createElement("img");
        imageNode.setAttribute("src", url);
        imageNode.setAttribute("alt", "图片");
        divNode.appendChild(imageNode);
        document.body.appendChild(divNode);
        let viewer = new Viewer(divNode, {
          zIndex: 3020,
          url(image) {
            return image.src.replace(/\@\w+\.src$/, "");
          },
          hidden() {
            viewer.destroy();
            divNode.remove();
          }
        });
        imageNode.click();
      },
      //显示镇疫码
      showCode(row) {
        let _this = this
        _this.codeDialogVisible = true
        _this.loading = true
        API.zhzymcheckInfo(row.id).then(res =>{
          if (res.code !==0 || !res.data)return
          res.data.status = res.data.zymStatus
          this.zhzymInfo = [res.data]
          _this.loading = false
          this.$nextTick(() => {
            _this.$refs.codeDialogRef.init()
          })
        })
      },
      position(row){
        if(row.upPoint){
          row.longitude = row.upPoint.split(",")[0]*1
          row.latitude= row.upPoint.split(",")[1]*1
        }else{
          return
        }
        //   row.latitude=30.010566532019467
        //   row.longitude = 121.662166935592
        this.dialogVisible = true
        this.$nextTick(()=>{
          this.initMap(row);
        })
      },
      initMap(item) {
        this.map = new BMap.Map(this.$refs.mapRef);
        this.map.centerAndZoom(new BMap.Point(item.longitude, item.latitude), 16);
        this.map.enableScrollWheelZoom();
        this.map.setMapType(BMAP_SATELLITE_MAP);
        this.map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP, BMAP_HYBRID_MAP]
          })
        );
        this.addLoc([item])
      },
      //画点的集合
      addLoc(arr){
        let iconUrl = "static/zhenCode/red-point.png";
        const icon = new BMap.Icon(iconUrl,new BMap.Size(20, 20));
        arr.forEach((item) => {
          const point = new BMap.Point(item.longitude, item.latitude);
          const mark = new BMap.Marker(point, {
            icon: icon,
            offset: new BMap.Size(0, -14.5),
          });
          const label = new BMap.Label(item.chkAddress, {
            position: point,
            offset: new BMap.Size(-57, 5),
          });
          label.setStyle({
            fontSize: "12px",
            color: "#E3E7F0",
            height: "20px",
            lineHeight: "20px",
            minWidth: "74px",
            textAlign: "center",
            background: "rgba(27,41,63,0.66)",
            border: "0",
            padding: "0 10px",
            // display: "none",
          });
          this.map.addOverlay(label);
          this.map.addOverlay(mark);
          this.map.setCenter(point)
        });
      },
    }
  };
</script>
<style scoped>
  .position{
    width: 20px;
    height: 20px;
    margin-left: 5px;

  }

  .map-container {
    height: 100%;
    background: #132a5f;
    position: relative;
    overflow: hidden;
  }
  .map-content {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
</style>
