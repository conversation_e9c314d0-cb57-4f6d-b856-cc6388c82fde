<template>
  <div>
    <div class="card_wrapper">
      <div class="box-card" v-for="(item,index) in zhzymInfo" :key="index"
           :style="{'height': client == 'ck' ? '710px': '680px'}">
        <!--        无状态-->
        <div v-if="!item.status || item.status===5">
          <div class="blue_background">
            <div class="card-title">危运疫码通</div>
          </div>
          <div class="content_wrapper">
            <!--              基本信息-->
            <div class="block_inside" style="box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);">
              <el-row :gutter="10" type="flex" justify="center">
                <el-col :span="24">
                  <div style="text-align: center">
                    <div class="person_name">{{item.usrNm}}</div>
                    <div style="color: #9fa0a3;">暂未申请危运疫码通</div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div>
              <div class="miniapp_wrapper">
                <img src="@/assets/zhenCode/miniapp.jpg"/>
              </div>
              <div style="text-align:center;font-size: 20px">扫码申领<i class="el-icon-top"></i></div>
            </div>
          </div>
          <div class="card_footer">
            <div class="card_footer_content">
              <img class="phone_icon" src="@/assets/zhenCode/hot-line.png"></img>
              <span class="hot_line">
                  服务热线：
                </span>
              <span class="phone_number">0574-55865951</span>
            </div>
          </div>
        </div>
        <!--        有状态：正常或异常-->
        <div v-else>
          <div class="blue_background">
            <div class="card-title">危运疫码通</div>
            <div>
              <div v-if="realTime"><i class="el-icon-time"></i>{{ dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss')}}</div>
              <div v-else>
                <div><i class="el-icon-time"></i>{{item.updTm}}</div>
                <div>(生成于{{timeFrom(item.updTm)}})</div>
              </div>
            </div>
          </div>
          <div class="content_wrapper">
            <!--              基本信息-->
            <div class="block_inside">
              <div class="tag_wrapper">
                <div class="tag"></div>
                <div class="tag_words"
                     :style="{'background': item.status===1? '#09cc26': item.status===9?'#ff7900':'#cccccc'}">
                  {{item.status===1?'正常':item.status===9?'异常':'无码'}}
                </div>
              </div>
              <el-row :gutter="10">
                <el-col :span="8">
                  <div class="code_wrapper" @click="switchShow(index)">
                    <el-image v-show="item.presignedUrl && showAvatar[index]" style="width: 110px; height: 110px"
                              :src="item.presignedUrl" fit="fill"></el-image>
                    <div v-show="!item.presignedUrl || !showAvatar[index]" :ref="'zhenQrcode'+index"
                         :id="'zhenQrcode' + index" align="center" title="危运疫码通"/>
                  </div>
                </el-col>
                <el-col :span="16">
                  <div class="person_name">
                    {{item.usrNm}}({{item.jobNm}})
                  </div>
                  <div class="person_item">
                    <img class="person_icon" src="@/assets/zhenCode/phone.png"></img>
                    <data-masking :rawData="item.usrMob" :dataType="'mobile'"></data-masking>
                  </div>
                  <div class="person_item">
                    <img class="person_icon" src="@/assets/zhenCode/id-card.png"></img>
                    <data-masking :rawData="item.usrIdcard" :dataType="'idCard'"></data-masking>
                  </div>
                  <div class="person_item">
                    <img class="person_icon" src="@/assets/zhenCode/license-plate.png"></img>
                    {{item.tracCd}} / {{item.traiCd}}
                  </div>
                  <div class="err_reason" v-if="item.status===9">*异常原因：
                    <div class="small_bnt bnt_orange" @click="showMsg(item.errMsg)">立即查看</div>
                  </div>
                </el-col>
              </el-row>
              <!--              <el-row :span="10">-->
              <!--                <div class="goodsNm_info">货物名称：{{item.goodsNm}}</div>-->
              <!--              </el-row>-->
              <el-row :span="10" v-if="client==='ck'">
                <div class="record_info">检验地：{{item.regPot}}</div>
              </el-row>
            </div>
            <!--              行程码 （弃用）-->
            <div v-if="false">
              <div class="img_container">
                <img class="trip_block_img" src="@/assets/zhenCode/top-blue.png"></img>
                <div class="trip_code_wrapper">
                  <div class="trip_code_title">行程码</div>
                  <!--  1：正常，2：行程码过期，8，未获取行程码，9：去过中高风险，-->
                  <div class="trip_img_wrapper">
                    <img v-if="item.xcmStatus===1" class="trip_img" :src="tripGreen"/>
                    <img v-if="item.xcmStatus===9" class="trip_img" :src="tripYellow"/>
                    <img v-if="item.xcmStatus===2 || item.xcmStatus===8" class="trip_img" :src="tripGrey"/>
                  </div>
                  <div v-if="item.xcmStatus === 1 || item.xcmStatus === 9">
                    <span>近14天涉足地区：</span>
                    <span class="trip_status" :class="[(item.xcmResult === '正常') ? 'trip_normal' : 'trip_abnormal']">{{item.xcmResult}}</span>
                  </div>
                  <div v-if="item.xcmStatus === 2">
                    <span class="trip_status trip_abnormal">{{item.xcmResult}}&nbsp;{{item.xcmTm}}</span>
                  </div>
                  <div v-if="item.xcmStatus === 8">
                    <span class="trip_status trip_abnormal">{{item.xcmResult}}</span>
                  </div>
                </div>
              </div>
            </div>
            <!--        车辆行程卡-->
            <div>
              <div class="img_container">
                <img class="trip_block_img" src="@/assets/zhenCode/top-blue.png"></img>
                <div class="trip_code_wrapper">
                  <div class="trip_code_title">车辆行程卡</div>
                <!--  trajectory：牵引车去过中高风险地区的记录，例如“镇海区，鄞州区”
                  trajectoryStatus：牵引车是否去过中高风险，0否，1是-->
                  <div class="trip_img_wrapper">
                    <img v-if="item.trajectoryStatus==0" class="trip_img" :src="vecGreen"/>
                    <img v-else-if="item.trajectoryStatus==1" class="trip_img" :src="vecYellow"/>
                    <img v-else class="trip_img" :src="vecGrey"/>
                  </div>
                  <div class="state_wrapper" v-if="item.trajectoryStatus">
                    <div style="margin-bottom: 6px">
                      <span>状态：</span>
                      <span class="trip_status" :class="[(item.trajectoryStatus==0) ? 'trip_normal' : 'trip_abnormal']">{{item.trajectoryStatus==0?'正常':'异常'}}</span>
                    </div>
                    <div :style="{'color': item.trajectoryStatus==0 ? '#000': '#ff7900'}">{{item.trajectory}}</div>
                     <!--<el-tooltip class="item" effect="dark" :content="item.trajectory" placement="right">
                      <img style="width:16px;height:16px" v-if="item.trajectoryStatus==0" src="@/assets/zhenCode/question.png"/>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" :content="item.trajectory" placement="right">
                      <img style="width:16px;height:16px" v-if="item.trajectoryStatus==1" src="@/assets/zhenCode/question.png"/>
                    </el-tooltip>-->

                  </div>
                </div>
              </div>
            </div>
            <!--              健康码 & 核酸检测 & 浙运安全码 -->
            <div class="blocks-wrapper">
              <el-row :gutter="10">
                <el-col :span="item.jobNm?(item.jobNm.indexOf('驾驶员')>-1?8:12):12">
                  <div class="one_block">
                    <div class="block_item">
                      <img class="block_icon"
                           :src="item.jkmResult==='绿码'?codeGreen:item.jkmResult==='黄码'?codeYellow:item.jkmResult==='红码'?codeRed:codeGrey"></img>
                      健康码
                    </div>
                    <div class="block_state"
                         :style="{'color': item.jkmResult == '绿码'? '#789f84':item.jkmResult == '黄码'? '#ff7900':item.jkmResult==='红码'?'#fa2828':'#cccccc'}">
                      {{item.jkmResult || '无码'}}
                    </div>
                    <div class="block_time">{{item.jkmTm?item.jkmTm.split(" ")[0]:''}}</div>
                    <div class="block_time">{{item.jkmTm?item.jkmTm.split(" ")[1]:''}}</div>
                    <div class="block_info">&nbsp;</div>
                  </div>
                </el-col>
                <el-col :span="item.jobNm?(item.jobNm.indexOf('驾驶员')>-1?8:12):12">
                  <div class="one_block">
                    <div class="tag_province_wrapper" v-if="item.hsjcUrl">
                      <div class="tag_province"></div>
                      <div class="tag_province_words">
                        省 外
                      </div>
                    </div>
                    <div class="block_item">
                      <img class="block_icon"
                           :src="item.hsjcResult==='阴性'?acidGreen:item.hsjcResult == '阳性'?acidRed:acidGrey"></img>
                      <span v-if="item.hsjcUrl">核酸检测</span>
                      <span v-else>（浙）核酸</span>
                    </div>
                    <div class="block_state"
                         :style="{'color': item.hsjcResult == '阴性'? '#789f84': item.hsjcResult == '阳性'?'#fa2828':'#cccccc'}">
                      {{item.hsjcValid || '未检测'}}
                    </div>
                    <div class="block_time"
                         :style="{'color': isOvertime(item.hsjcTm, item.hsjcValidTm) ? '#ff7818': '#9fa0a3'}">
                      {{item.hsjcTm?item.hsjcTm.split(" ")[0]:''}}
                    </div>
                    <div class="block_time"
                         :style="{'color': isOvertime(item.hsjcTm, item.hsjcValidTm) ? '#ff7818': '#9fa0a3'}">
                      {{item.hsjcTm?item.hsjcTm.split(" ")[1]:''}}
                    </div>
                    <div class="block_overtime">
                      <div v-if="isOvertime(item.hsjcTm, item.hsjcValidTm)">
                        <img class="overtime_img" src="@/assets/zhenCode/error-circle.png"/>
                        <span class="overtime">报告超过{{item.hsjcValidTm}}小时</span>
                      </div>
                      <div v-else>
                        <div class="overtime">&nbsp;</div>
                      </div>
                    </div>
                    <div class="block_info">{{item.hsjcOrig}}</div>
                    <span v-if="item.hsjcUrl" class="small_bnt bnt_blue" @click="showImg(item.hsjcUrl)">立即查看</span>
                  </div>
                </el-col>
                <el-col :span="item.jobNm?(item.jobNm.indexOf('驾驶员')>-1?8:12):12"
                        v-if="item.jobNm?item.jobNm.indexOf('驾驶员')>-1:false">
                  <div class="one_block">
                    <div class="block_item">
                      <img class="block_icon"
                           :src="item.scResult==='蓝码'?codeBlue:item.scResult==='黄码'?codeYellow:item.scResult==='红码'?codeRed:codeGrey"></img>
                      浙运安全码
                    </div>
                    <div class="block_state"
                         :style="{'color': item.scResult == '蓝码'? '#5786f0': item.scResult == '黄码'?'#ff7900':item.scResult==='红码'?'#fa2828':'#cccccc'}">
                      {{item.scResult || '无码'}}
                    </div>
                    <div class="block_time">&nbsp;{{item.scTm?item.scTm.split(" ")[0]:''}}&nbsp;</div>
                    <div class="block_time">&nbsp;{{item.scTm?item.scTm.split(" ")[1]:''}}&nbsp;</div>
                    <div class="block_overtime">
                      <div class="overtime">&nbsp;</div>
                    </div>
                    <div class="block_info" :style="{'color': item.isControl ? '#fa2828': '#9fa0a3'}">
                      {{item.isControl?'特殊管控货物':'非特殊管控货物'}}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="card_footer">
            <div class="card_footer_content">
              <img class="phone_icon" src="@/assets/zhenCode/hot-line.png"></img>
              <span class="hot_line">
                  服务热线：
                </span>
              <span class="phone_number">0574-55865951</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  // import tripGreen from '@/assets/zhenCode/trip-green.png'
  // import tripYellow from '@/assets/zhenCode/trip-yellow.png'
  // import tripGrey from '@/assets/zhenCode/trip-grey.png'

  import vecGreen from '@/assets/zhenCode/vec-green.png'
  import vecYellow from '@/assets/zhenCode/vec-yellow.png'
  import vecGrey from '@/assets/zhenCode/vec-grey.png'

  import codeGreen from '@/assets/zhenCode/code-green.png'
  import codeBlue from '@/assets/zhenCode/code-blue.png'
  import codeYellow from '@/assets/zhenCode/code-yellow.png'
  import codeRed from '@/assets/zhenCode/code-red.png'
  import codeGrey from '@/assets/zhenCode/code-grey.png'

  import acidGreen from '@/assets/zhenCode/acid-green.png'
  import acidRed from '@/assets/zhenCode/acid-red.png'
  import acidGrey from '@/assets/zhenCode/acid-grey.png'

  import DataMasking from "@/components/dataMasking"
  import QRCode from "qrcodejs2";
  import dayjs from 'dayjs'
  import Viewer from "viewerjs";

  export default {
    name: "codeDialog",
    data() {
      return {
        // tripGreen: tripGreen,
        // tripYellow: tripYellow,
        // tripGrey: tripGrey,
        vecGreen: vecGreen,
        vecYellow: vecYellow,
        vecGrey: vecGrey,
        codeGreen: codeGreen,
        codeBlue: codeBlue,
        codeYellow: codeYellow,
        codeRed: codeRed,
        codeGrey: codeGrey,
        acidGreen: acidGreen,
        acidRed: acidRed,
        acidGrey: acidGrey,
        showAvatar: {},
        timer: null,
        dayjs: dayjs,
        currentTime: new Date()
      }
    },
    props: {
      zhzymInfo: {
        type: Array,
        default: [],
        required: true
      },
      client: {
        type: String,
        default: '',
        required: false
      },
      realTime: {
        type: Boolean,
        default: true,
        required: false
      }
    },
    components: {
      DataMasking
    },
    methods: {
      init() {
        let _this = this
        this.zhzymInfo.forEach((per, index) => {
          this.$set(this.showAvatar, index, true);
          if (per && per.status && per.status !== 5) {
            document.getElementById("zhenQrcode" + index).innerHTML = "";
            new QRCode(document.getElementById("zhenQrcode" + index), {
              text: per.usrIdcard,
              width: 110,
              height: 110,
              colorDark: per.status === 1 ? '#75db39' : per.status === 9 ? '#131312' : '#cccccc',
              colorLight: "#ffffff",
              correctLevel: QRCode.CorrectLevel.L,
            });
          }

        })
      },
      /**
       * 时间戳转为多久之前
       * @param String timestamp 时间戳
       **/
      timeFrom(time) {
        let dateTime = dayjs(time).unix() * 1000
        // 如果为null,则格式化当前时间
        if (!dateTime) dateTime = Number(new Date());
        // 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
        if (dateTime.toString().length == 10) dateTime *= 1000;
        let timestamp = +new Date(Number(dateTime));

        let timer = (Number(new Date()) - timestamp) / 1000;
        // 如果小于5分钟,则返回"刚刚",其他以此类推
        let tips = '';
        switch (true) {
          // case timer < 300:
          //   tips = '刚刚';
          //   break;
          case timer < 3600:
            tips = parseInt(timer / 60) + '分钟前';
            break;
          case timer >= 3600 && timer < 86400:
            tips = parseInt(timer / 3600) + '小时' + parseInt((timer - 3600 * parseInt(timer / 3600)) / 60) + '分钟前';
            break;
          case timer >= 86400 && timer < 2592000:
            tips = parseInt(timer / 86400) + '天前';
            break;
          default:
            if (timer >= 2592000 && timer < 365 * 86400) {
              tips = parseInt(timer / (86400 * 30)) + '个月前';
            } else {
              tips = parseInt(timer / (86400 * 365)) + '年前';
            }
        }
        return tips;
      },
      //判断核酸检测时间超过24小时
      isOvertime(hsjcTm, hsjcValidTm) {
        return (dayjs().unix() - dayjs(hsjcTm).unix()) / 3600 > Number(hsjcValidTm);
      },
      //显示异常原因
      showMsg(msg) {
        this.$alert(msg, '异常原因', {
          confirmButtonText: '确定',
        });
      },
      //显示图片
      showImg(url) {
        console.log(url)
        let divNode = document.createElement("div");
        divNode.style.display = "none";
        let imageNode = document.createElement("img");
        imageNode.setAttribute("src", url);
        imageNode.setAttribute("alt", "图片");
        divNode.appendChild(imageNode);
        document.body.appendChild(divNode);
        let viewer = new Viewer(divNode, {
          zIndex: 3020,
          url(image) {
            return image.src.replace(/\@\w+\.src$/, "");
          },
          hidden() {
            viewer.destroy();
            divNode.remove();
          }
        });
        imageNode.click();
      },
      //头像&二维码切换显示
      switchShow(index) {
        this.showAvatar[index] = !this.showAvatar[index]
      },
    },
    mounted: function () {
      const _this = this; //声明一个变量指向Vue实例this，保证作用域一致
      this.timer = setInterval(function () {
        _this.currentTime = new Date(); //修改数据date
      }, 1000)
    },
    beforeDestroy: function () {
      if (this.timer) {
        clearInterval(this.timer); //在Vue实例销毁前，清除我们的定时器
      }
    }
  }
</script>

<style scoped>
  .card_wrapper {
    display: flex;
    min-height: 680px;
  }

  .box-card {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    border: 1px solid #EBEEF5;
    color: #ffffff;
    transition: .3s;
    position: relative;
    /*height:660px;*/
    flex: 1;
  }

  .blue_background {
    text-align: center;
    background: #308eee;
    height: 140px;
    padding: 16px;
    color: #d2ebf8;
  }

  .card-title {
    margin-bottom: 12px;
    font-size: 20px;
  }

  .content_wrapper {
    position: absolute;
    left: 50%;
    top: 90px;
    transform: translate(-50%, 0%);
    width: 400px;
    background: #ffffff;
    color: #000000;
  }

  .block_inside {
    /*border:1px solid #f7f8fa;*/
    padding: 14px 10px 10px 10px;
    position: relative;
    overflow: hidden;
  }

  .tag_wrapper {
    width: 140px;
    height: 105px;
    position: absolute;
    top: -70px;
    right: -37px;
    transform: rotate(30deg);
  }

  .tag_words {
    position: absolute;
    bottom: 0;
    display: block;
    font-size: 18px;
    color: #fff;
    width: 180px;
    text-align: center;
    padding: 3px;
  }

  .tag_province_wrapper {
    width: 120px;
    height: 114px;
    position: absolute;
    top: -86px;
    right: -68px;
    transform: rotate(30deg);
  }

  .tag_province_words {
    position: absolute;
    bottom: 0;
    display: block;
    font-size: 12px;
    transform: scale(0.70);
    color: #fff;
    width: 120px;
    text-align: center;
    padding: 3px;
    background: #268aef;
  }

  .code_wrapper {
    width: 80px;
  }

  .person_item {
    display: flex;
    color: #9fa0a3;
    font-size: 10px;
    margin-bottom: 8px;
    align-items: center;
  }

  .person_icon {
    width: 15px;
    height: 11px;
    margin-right: 3px;
  }

  .person_name {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .err_reason {
    color: #ff7900;
    display: flex;
  }

  .small_bnt {
    width: 89px;
    height: 19px;
    border-radius: 4px;
    color: #ffffff;
    padding: 3px 18px;
    font-size: 12px;
    cursor: pointer;
  }

  .bnt_orange {
    background: #ff7900;
  }

  .bnt_blue {
    background: #278aef;
  }

  .goodsNm_info {
    font-size: 13px;
    color: #9fa0a3;
    text-align: center;
    margin-top: 8px;
  }

  .record_info {
    font-size: 15px;
    color: #9fa0a3;
    text-align: center;
    margin-top: 10px;
  }

  .code_img {
    width: 100%
  }

  .img_container {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
  }

  .trip_block_img {
    height: 190px;
    width: 100%
  }

  .trip_img {
    width: 99px;
    height: 80px;
  }

  .trip_code_wrapper {
    position: absolute;
    left: 50%;
    top: 3px;
    transform: translate(-50%, 0%);
    width: 100%;
    text-align: center;
  }

  .trip_code_title {
    color: #ffffff;
    margin-bottom: 20px;
  }

  .trip_img_wrapper {
    margin-bottom: 20px;
  }
  .state_wrapper{
    /*display:flex;*/
    justify-content: center;
  }
  .trip_status {
    margin-right: 8px;
  }

  .trip_normal {
    color: #65a172
  }

  .trip_abnormal {
    color: #ff7900
  }

  .blocks-wrapper {
  }

  .one_block {
    border: 1px solid #EBEEF5;
    padding: 20px 8px 0px 8px;
    text-align: center;
    height: 192px;
    position: relative;
    overflow: hidden;
  }

  .block_icon {
    width: 15px;
    height: 15px;
  }

  .block_item {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 16px;

  }

  .block_state {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .block_time {
    color: #9fa0a3;
    font-size: 15px;
  }

  .block_overtime {
    margin-top: 4px;
    margin-bottom: 8px;
  }

  .overtime_img {
    width: 12px;
    height: 12px;
  }

  .overtime {
    color: #ff7818;
    font-size: 12px;
  }

  .block_info {
    color: #9fa0a3;
    font-size: 10px;
    margin-bottom: 8px;
  }

  .card_footer {
    position: absolute;
    left: 50%;
    bottom: 8px;
    transform: translate(-50%, 0%);
    border-top: 1px solid #e5e5e5;
    width: 100%;
  }

  .card_footer_content {
    text-align: center;
    color: #000000;
    padding-top: 8px;
  }

  .phone_icon {
    width: 16px;
    height: 16px
  }

  .hot_line {
    margin-left: 8px;
  }

  .phone_number {
    color: #6aa1dd;
  }

  .miniapp_wrapper {
    width: 100%;
    padding: 48px 0px;
    font-size: 20px;
    text-align: center;
  }
</style>
