<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getDataList"
      class="grid-search-bar"
    >
    </searchbar>
    <el-table
      :data="dataList"
      border
      :max-height="tableHeight"
      v-loading="dataListLoading"
      style="width: 100%;"
    >
      <el-table-column prop="crtTm" label="预警时间" width="150"></el-table-column>
      <el-table-column prop="rtePlanCd" label="运单号" width="210">
        <template slot-scope="scope">
          <el-button
            @click.native.prevent="showBills(scope.row.rtePlanPk)"
            type="text"
          >{{ scope.row.rtePlanCd }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="alarmReason" label="预警原因" width="210"></el-table-column>
      <el-table-column prop="dvStatus" label="驾驶员" width="210">
        <template slot-scope="scope">
          <el-button type="text" @click="showCode(scope.row.dvStatus)">{{scope.row.dvStatus}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="scStatus" label="押运员" width="210">
        <template slot-scope="scope">
          <el-button type="text" @click="showCode(scope.row.scStatus)">{{scope.row.scStatus}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="vecStatus" label="车牌号" width="90"></el-table-column>
      <el-table-column prop="handleTm" label="处置时间"></el-table-column>
      <el-table-column prop="handleContent" label="处置内容"></el-table-column>
      <el-table-column prop="handleNm" label="处置人"></el-table-column>
      <el-table-column prop="handleStatus" label="处置状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.handleStatus===1" type="success">已处理</el-tag>
          <el-tag v-if="scope.row.handleStatus===0" type="danger">未处理</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="dispose(scope.row)">处置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="sizes, prev, pager, next, total"
      :page-sizes="[20, 30, 50, 100, 200]"
      style="float:right;"
      :page-size="pagination.limit"
      :current-page.sync="pagination.page"
      :total="pagination.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
    <!-- 运单详情 -->
    <el-dialog
      title="运单详情"
      :visible.sync="visibleOfRteplan"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <rteplan-info ref="rteplanInfo" :isCompn="true"></rteplan-info>
    </el-dialog>
    <!--  镇疫码弹窗  -->
    <el-dialog
      title="危运疫码通"
      :visible.sync="codeDialogVisible"
      :show-close="true"
      width="450px">
      <code-dialog ref="codeDialogRef" :zhzymInfo="zhzymInfo" v-loading="loading" :realTime="false"></code-dialog>
      <span slot="footer" class="dialog-footer">
      </span>
    </el-dialog>
    <!--  处置弹窗  -->
      <el-dialog
        title="处置"
        :visible.sync="dialogVisible"
        width="40%">
        <el-form style="margin-top:20px;" ref="dataForm" label-width="120px" :model="dataForm" size="small">
          <el-form-item label="处置人:" prop="handleNm" :rules="$rulesFilter({ required: true })">
            <el-autocomplete class="inline-input" v-model="dataForm.handleNm" :fetch-suggestions="querySearch" placeholder="请输入内容" clearable @select="handleSelect"></el-autocomplete>
          </el-form-item>
          <el-form-item label="处置内容:" prop="handleContent" :rules="$rulesFilter({ required: true })">
            <el-input type="textarea" :rows="3" v-model="dataForm.handleContent"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
      </el-dialog>
  </div>
</template>

<script>
  import * as API from "@/api/epidemic"
  import { oprlist } from "@/api/mapMonit"
  import * as Tool from "@/utils/tool"
  import Searchbar from "@/components/Searchbar"
  import {getFuzzyTracCd} from "@/api/vec"
  import Viewer from "viewerjs"
  import CodeDialog from "../components/codeDialog"
  import RteplanInfo from "@/views/modules/base/rtePlan/rtePlan-bills";

  export default {
    name: 'forecast',
    data() {
      return {
        searchItems: {
          normal: [
            {name: '运单号', field: 'rtePlanCd', type: 'text', dbfield: 'rte_plan_cd', dboper: 'cn'},
            {name: '驾驶员', field: 'dvStatus', type: 'text', dbfield: 'dv_status', dboper: 'cn'},
            {name: '车牌号', field: 'vecStatus', type: 'text', dbfield: 'vec_status', dboper: 'cn'},
            // {name: "车牌号", field: "vecStatus", type: "fuzzy", dbfield: "vec_status", dboper: "eq", api: this.getTracCd},
            {
              name: "状态",
              field: "handleStatus",
              type: "select",
              dbfield: "handle_status",
              dboper: "eq",
              options: [
                {
                  label: "所有",
                  value: '',
                },
                {
                  label: "已处理",
                  value: 1,
                },
                {
                  label: "未处理",
                  value: 0,
                },
              ],
            },
          ],
          more:[]
        },
        tableHeight: Tool.getClientHeight() - 210,
        dataList: [],
        pagination: {
          page: 1,
          limit: 15,
          total: 0
        },
        dataListLoading: false,
        loading: false,
        dataListSelections: [],
        tracList: [], //牵引车模糊搜索列表
        traiList: [], //挂车模糊搜索列表
        codeDialogVisible: false,
        dialogVisible: false,
        zhzymInfo: [],
        dataForm:{
          id:null,
          handleNm:'',
          handleContent:''
        },
        opratorList: [], // 操作员列表
        visibleOfRteplan: false,
      };
    },
    components: {
      Searchbar,
      CodeDialog,
      RteplanInfo
    },
    mounted: function () {
      window.addEventListener("resize", this.setTableHeight);

      let query = this.$route.query;
      this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
      this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
      this.pagination.total = this.pagination.page * this.pagination.limit;
      this.$refs.searchbar.init(query);
      this.setTableHeight()
      this.getDataList()
      this.getOprlist()
    },
    methods: {
      //获取操作人员列表
      getOprlist(){
        oprlist()
          .then(res => {
            if (res.code == 0) {
              this.opratorList = res.list.map(item => {
                return { value: item, label: item };
              });
            } else {
              this.opratorList = [];
            }
          })
          .catch(err => {
            this.opratorList = [];
          });
      },
      // 改变搜索框的高度
      resizeSearchbar() {
        this.setTableHeight();
      },
      setTableHeight() {
        this.$nextTick(() => {
          this.tableHeight =
            Tool.getClientHeight() - 170 - this.$refs.searchbar.$el.offsetHeight;
        });
      },

      // 导出表格
      exportTable() {
        let param = {filters: this.$refs.searchbar.get()}
        API.download(param).then(res => {
          let a = document.createElement('a');
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download = '作业人员体温信息_' + (_date.getFullYear() + '-' + (_date.getMonth() + 1) + '-' + _date.getDate()) + '.xlsx';
          a.click();
          window.URL.revokeObjectURL(url);
        })
          .catch(err => {

          })
      },
      // 获取数据列表
      getDataList(data) {
        let filters;
        if (data) {
          if (data.resetCurrentPage) {
            this.pagination.page = 1;
          }
          if (data.searchData) {
            filters = data.searchData;
          }
        } else {
          filters = this.$refs.searchbar.get();
        }

        this.dataListLoading = true;
        let params = Object.assign({filters: filters}, this.pagination);
        API.zhzymforecastList(params).then(response => {
          if (response && response.code === 0) {
            this.pagination.total = response.page.totalCount;
            this.dataList = response.page.list;
          } else {
            this.dataList = [];
            this.pagination.total = 0;
          }
          this.dataListLoading = false;
        });
      },
      // 分页跳转
      handleCurrentChange: function (val) {
        this.pagination.page = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      // 分页条数修改
      handleSizeChange: function (val) {
        this.pagination.limit = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      //牵引车模糊搜索
      async getTracCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.tracList = [];
            return;
          }
          this.tracList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.tracList);
        }
      },
      //挂车模糊搜索
      async getTraiCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.traiList = [];
            return;
          }
          this.traiList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.traiList);
        }
      },
      //显示镇疫码
      showCode(nameId) {
        let _this = this
        _this.codeDialogVisible = true
        _this.loading = true
        //名字（身份证）字符串 提取身份证
        let SIndex = nameId.indexOf('(')
        let EIndex = nameId.indexOf(')')
        let idCard = nameId.substr(SIndex+1,(EIndex-SIndex-1))
        API.zhzymregFindByIdCard(idCard).then(res =>{
          if (res.code !==0 || !res.data)return
          this.zhzymInfo = [res.data]
          _this.loading = false
          this.$nextTick(() => {
            _this.$refs.codeDialogRef.init()
          })
        })
      },
      //显示图片
      showImg(url) {
        let divNode = document.createElement("div");
        divNode.style.display = "none";
        let imageNode = document.createElement("img");
        imageNode.setAttribute("src", url);
        imageNode.setAttribute("alt", "图片");
        divNode.appendChild(imageNode);
        document.body.appendChild(divNode);
        let viewer = new Viewer(divNode, {
          zIndex: 3020,
          url(image) {
            return image.src.replace(/\@\w+\.src$/, "");
          },
          hidden() {
            viewer.destroy();
            divNode.remove();
          }
        });
        imageNode.click();
      },
      //处置弹窗
      dispose(row){
        this.dataForm = {
          id: row.id,
          handleNm: '',
          handleContent: ''
        }
        // this.dataForm = Object.assign({}, row);
        this.dialogVisible = true
      },
      //提交处置
      submit() {
        let _this = this;
        this.$refs["dataForm"].validate(valid => {
          if (valid) {
            API.forecastHandle(this.dataForm).then(res => {
              if (res && res.code == 0) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                  duration: 2000,
                  onClose: () => {
                    _this.dialogVisible = false;
                    _this.getDataList();
                  }
                });
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      //
      querySearch(queryString, cb) {
        var opratorList = this.opratorList;
        var results = queryString
          ? opratorList.filter(this.createFilter(queryString))
          : opratorList;

        cb(results);
      },
      createFilter(queryString) {
        return opratorList => {
          return (
            opratorList.value.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
          );
        };
      },
      handleSelect(item) {},
      // 运单详情
      showBills: function (argmtPk) {
        if (!argmtPk) {
          this.$message.error("对不起，该信息不能打开");
          return;
        }
        this.visibleOfRteplan = true;
        this.$nextTick(() => {
          this.$refs.rteplanInfo.rtePlanNewByPk(argmtPk);
        });
      },
    }
  };
</script>

