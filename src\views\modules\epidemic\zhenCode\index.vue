<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getDataList"
      class="grid-search-bar"
    >
    </searchbar>
    <el-table
      :data="dataList"
      border
      :max-height="tableHeight"
      v-loading="dataListLoading"
      style="width: 100%;"
    >
      <el-table-column prop="usrNm" label="姓名">
        <template slot-scope="scope">
          <el-button type="text" @click="showCode(scope.row)">{{scope.row.usrNm}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="usrIdcard" label="身份证号"></el-table-column>
      <el-table-column prop="usrMob" label="手机号"></el-table-column>
      <el-table-column prop="jobNm" label="岗位"></el-table-column>
      <el-table-column prop="tracCd" label="牵引车"></el-table-column>
      <el-table-column prop="traiCd" label="挂车"></el-table-column>
      <!-- <el-table-column prop="goodsNm" label="货物名称"></el-table-column> -->
      <el-table-column prop="scResult" label="浙运安全码"></el-table-column>
      <el-table-column prop="jkmResult" label="健康码">
        <!--    <template slot-scope="scope">
              <el-button type="text" @click="showImg(scope.row.hcUrl)">{{scope.row.jkmResult}}</el-button>
        </template>-->
      </el-table-column>
   <!--   <el-table-column prop="xcmResult" label="行程码"></el-table-column>-->
      <el-table-column prop="hsjcResult" label="核酸检测报告">
        <!--  <template slot-scope="scope">
            <el-button type="text" @click="showImg(scope.row.naUrl)">{{scope.row.hsjcResult}}</el-button>
        </template>-->
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status===1" type="success">正常</el-tag>
          <el-tag v-if="scope.row.status===9" type="danger">异常</el-tag>
          <el-tag v-if="scope.row.status===5 || !scope.row.status" type="info">未申领</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="unbind(scope.row)">解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="sizes, prev, pager, next, total"
      :page-sizes="[20, 30, 50, 100, 200]"
      style="float:right;"
      :page-size="pagination.limit"
      :current-page.sync="pagination.page"
      :total="pagination.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
    <!--  镇疫码弹窗  -->
    <el-dialog
      title="危运疫码通"
      :visible.sync="codeDialogVisible"
      :show-close="true"
      width="450px"
    >
      <code-dialog ref="codeDialogRef" :zhzymInfo="zhzymInfo" v-loading="loading"></code-dialog>
      <span slot="footer" class="dialog-footer">
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import * as API from "@/api/epidemic";
  import * as Tool from "@/utils/tool";
  import Searchbar from "@/components/Searchbar";
  import {getFuzzyTracCd} from "@/api/vec";
  import Viewer from "viewerjs";
  import CodeDialog from "../components/codeDialog"

  export default {
    data() {
      return {
        searchItems: {
          normal: [
            {name: '姓名', field: 'usrNm', type: 'text', dbfield: 'usr_nm', dboper: 'cn'},
            {name: "牵引车", field: "tracCd", type: "fuzzy", dbfield: "trac_cd", dboper: "eq", api: this.getTracCd},
            {name: '手机号', field: 'usrMob', type: 'text', dbfield: 'usr_mob', dboper: 'cn'},
            // {
            //   name: "状态",
            //   field: "status",
            //   type: "select",
            //   dbfield: "status",
            //   dboper: "eq",
            //   options: [
            //     {
            //       label: "正常",
            //       value: 1,
            //     },
            //     {
            //       label: "异常",
            //       value: 9,
            //     },
            //   ],
            // },
            // {
            //   name: "挂车号",
            //   field: "traiCd",
            //   type: "fuzzy",
            //   dbfield: "trai_cd",
            //   dboper: "eq",
            //   api: this.getTraiCd
            // }
          ]
        },
        tableHeight: Tool.getClientHeight() - 210,
        dataList: [],
        pagination: {
          page: 1,
          limit: 15,
          total: 0
        },
        dataListLoading: false,
        loading: false,
        dataListSelections: [],
        tracList: [], //牵引车模糊搜索列表
        traiList: [], //挂车模糊搜索列表
        codeDialogVisible: false,
        zhzymInfo: [],
      };
    },
    components: {
      Searchbar,
      CodeDialog
    },
    mounted: function () {
      window.addEventListener("resize", this.setTableHeight);

      let query = this.$route.query;
      this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
      this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
      this.pagination.total = this.pagination.page * this.pagination.limit;
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getDataList();
    },
    methods: {
      // 改变搜索框的高度
      resizeSearchbar() {
        this.setTableHeight();
      },
      setTableHeight() {
        this.$nextTick(() => {
          this.tableHeight =
            Tool.getClientHeight() - 170 - this.$refs.searchbar.$el.offsetHeight;
        });
      },

      // 导出表格
      exportTable() {
        let param = {filters: this.$refs.searchbar.get()}
        API.download(param).then(res => {
          let a = document.createElement('a');
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download = '作业人员体温信息_' + (_date.getFullYear() + '-' + (_date.getMonth() + 1) + '-' + _date.getDate()) + '.xlsx';
          a.click();
          window.URL.revokeObjectURL(url);
        })
          .catch(err => {

          })
      },
      // 获取数据列表
      getDataList(data) {
        let filters;
        if (data) {
          if (data.resetCurrentPage) {
            this.pagination.page = 1;
          }
          if (data.searchData) {
            filters = data.searchData;
          }
        } else {
          filters = this.$refs.searchbar.get();
        }

        this.dataListLoading = true;
        let params = Object.assign({filters: filters}, this.pagination);
        API.zhzymregList(params).then(response => {
          if (response && response.code === 0) {
            this.pagination.total = response.page.totalCount;
            this.dataList = response.page.list;
          } else {
            this.dataList = [];
            this.pagination.total = 0;
          }
          this.dataListLoading = false;
        });
      },
      // 分页跳转
      handleCurrentChange: function (val) {
        this.pagination.page = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      // 分页条数修改
      handleSizeChange: function (val) {
        this.pagination.limit = val;
        // this.getList();
        this.$refs.searchbar.searchHandle(true);
      },
      //牵引车模糊搜索
      async getTracCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.tracList = [];
            return;
          }
          this.tracList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.tracList);
        }
      },
      //挂车模糊搜索
      async getTraiCd(queryString, cb) {
        if (queryString.length <= 2) {
          cb([]);
          return;
        }
        const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
          cb([]);
          console.log(error);
        });
        if (res) {
          if (res.code !== 0) {
            this.traiList = [];
            return;
          }
          this.traiList = res.data.map(item => {
            return {value: item.name};
          });
          cb(this.traiList);
        }
      },
      //显示镇疫码
      showCode(row) {
        let _this = this
        _this.codeDialogVisible = true
        _this.loading = true
        API.zhzymregInfo(row.id).then(res => {
          if (res.code !== 0 || !res.data) return
          // res.data.presignedUrl = 'https://cdn.uviewui.com/uview/album/1.jpg'
          // res.data.hsjcUrl = 'https://cdn.uviewui.com/uview/album/1.jpg'
          // res.data.xcmStatus = 9
          // res.data.xcmResult = '去过中高风险'
          // res.data.xcmOvertime = '2022.03.09 12:12:12'
          // res.data.trajectoryStatus = "1"
          // res.data.trajectory = "镇海区"
          _this.zhzymInfo = [res.data]
          _this.loading = false
          _this.$nextTick(() => {
            _this.$refs.codeDialogRef.init()
          })
        })
      },
      //解绑
      unbind(row) {
        let _this = this
        this.$prompt('请输入姓名后删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator(value) {
            if (value !== row.usrNm) {
              return '输入姓名不匹配'
            }
          },
        }).then(({value}) => {
          API.zhzymregUnbind(row.id).then(res => {
            if (res.code === 0) {
              this.$message.success("解绑成功!");
              setTimeout(function () {
                _this.getDataList();
              }, 1000);
            } else {
              this.$message.error(res.msg || "服务器错误，请联系管理员");
            }
          })
        }).catch((err) => {
        });
      },
      //显示图片
      showImg(url) {
        let divNode = document.createElement("div");
        divNode.style.display = "none";
        let imageNode = document.createElement("img");
        imageNode.setAttribute("src", url);
        imageNode.setAttribute("alt", "图片");
        divNode.appendChild(imageNode);
        document.body.appendChild(divNode);
        let viewer = new Viewer(divNode, {
          zIndex: 3020,
          url(image) {
            return image.src.replace(/\@\w+\.src$/, "");
          },
          hidden() {
            viewer.destroy();
            divNode.remove();
          }
        });
        imageNode.click();
      },
    }
  };
</script>

