<template>
    <div class="left-panel">
        <div class="searchbox">
            <div class="searchbox-top">
              <div class="search-form">
                  <el-form>
                    <el-form-item>
                      <el-input size="small"  placeholder="请输入地点名称" v-model="searchLocalNm" @keyup.enter.native="searchLocal"></el-input>
                    </el-form-item>
                  </el-form>
              </div>
            </div>
            <div  class="searchbox-bottom" v-pre>
                <h5 class="h5">
                  围栏绘制方法简介：
                </h5>
                <p>
                  1. 输入搜索地名称，如：<em>平海路</em> ，然后回车/点击右侧的<em>“搜索”- <span class="el-icon-search"></span></em> 按钮
                </p>
                <p>
                    2. 在地图中选择右上角多边形和矩形按钮进行区域绘制，滚轮放大缩小地图，双击鼠标结束绘制。
                </p>
                <p>
                   <em>3. 一次绘制一个区域，请勿同时勾画多个区域</em>
                </p>
                <p>
                    4. 绘制完区域后点击右侧<em>“保存路线”- <span class="el-icon-upload"></span></em> 按钮进行保存
                </p>
            </div>
            <div class="clear-btn text-right">
                <el-button type="success" icon="el-icon-refresh" size="small" @click="clearMap">清除区域</el-button>
            </div>
            <div class="searchbox-oprat-btn">
              <div class="oprat-btn" title="搜索" @click="searchLocal">
                <span class="el-icon-search" ></span>
              </div>
              <div class="oprat-btn"  title="保存" @click="saveArea">
                <span class="el-icon-upload"></span>
              </div>
            </div>
        </div>
        <div class="card-list"  v-loading="loading">
            <div class="search-bar">
                <el-input type="text" size="small" v-model="queryObj.label" @keyup.native.enter="search" placeholder="请输入关键词搜索线路">
                  <template slot="append">
                    <el-button @click="search" type="success" size="small" icon="el-icon-search">搜索</el-button>
                  </template>
                </el-input>
            </div>
            <div class="poly-list">
                <ul>
                  <li v-for="item in list " :key="item.crtTm">
                      <span class="road-name" :title="item.label">{{item.label}} </span>
                      <div class="fl-r">
                        <el-button-group>
                          <el-button type="primary" size="mini" icon="el-icon-search" title="查看" @click="viewRoad(item)"></el-button>
                          <el-button type="success" size="mini" icon="el-icon-edit" title="修改" @click="updateArea(item)"></el-button>
                          <el-button  type="danger" size="mini" icon="el-icon-delete" title="删除" @click="removeRoad(item)"></el-button>
                        </el-button-group>
                      </div>
                  </li>
                </ul>
            </div>
            <div class="pagingation text-center" >
                <el-button size="small"   @click="lastPage" :disabled="lastPageDis" >上一页</el-button>
                <el-button size="small" @click="nextPage" :disabled="nextPageDis" >下一页</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import * as $http from '@/api/passport'
export default {
  name: "plygon",
  data() {
    return {
      loading:false,
      searchLocalNm: "", //搜索区域名称
      crtAreaName: "", //保存路线时的名称
      local: null,
      map: null,
      myDrawingManagerObject:null,//自定义绘制路线插件
      bdary:null,//获取行政区域
      list: [],
      routePoints: "", //保存路径坐标点
      overlay: null, //保存绘制的路径
      path: "", //保存绘制的路径的坐标值
      queryObj:{
          label:'',
          limit:10,
          page:1,
          totalPage:2
      }
    };
  },
  components: {
    "msgbox-form": {
      template: `<div>
        <el-form>
        <el-form-item>
          <el-input size="small" v-model="areaName" @change="getAreaName"></el-input>
          </el-form-item>
        </el-form>
        <p style="font-size:12px;line-height:1.3;" v-pre>提示：请添加线路名称描述，如：<em style="font-style:normal;color:red;">平海路 （全路段） </em><br>
        使用全角括号（）对线路进行细节描述，道路名称间用半角空格隔开，如
        <em  style="font-style:normal;color:red;">（俞范东路 至 隧道北路）</em><br>
        </p>
        </div>`,
      data() {
        return {
          areaName: ""
        };
      },
      methods: {
        getAreaName() {
          this.$emit("getname", this.areaName);
          this.areaName = "";
        }
      }
    }
  },
  created() {
     //获取地图组件
    this.map = this.$store.state.maps.map;
    //自定义绘制路线插件
    this.myDrawingManagerObject = this.$store.state.maps.mapPlugins.myDrawingManagerObject;
    //获取行政区域插件
    this.bdary = this.$store.state.maps.mapPlugins.bdary;
    
  },
  mounted() {
    this.$nextTick(() => {
      let _this = this;
      let map = this.map;

      //区域搜索功能
      this.local = new BMap.LocalSearch(map, {
          renderOptions:{map: map}
      });

      //手动绘制围栏区域
      let myDrawingManagerObject = this.myDrawingManagerObject;

      myDrawingManagerObject.addEventListener("overlaycomplete", function(o) {
        let path = o.overlay.getPath();
        let paths = "";
        let centerPoint;

        if (_this.overlay) {
          map.removeOverlay(_this.overlay);
        }
        _this.overlay = o.overlay;
        _this.path = JSON.stringify(path);
      });

      let currPage = (this.$route.query && this.$route.query.pageSize) || 1;
   
      if(currPage){
        this.queryObj.page = currPage;
      }

      //获取围栏列表
      this.getList();

    });
  },
  computed:{
    nextPageDis(){
        return !(this.queryObj.totalPage >1 && this.queryObj.page < this.queryObj.totalPage);
    },
    lastPageDis(){
        return !(this.queryObj.page >1 && this.queryObj.totalPage > 1);
    }
  },
  methods: {
    //保存围栏区域
    saveArea() {
        let _this = this;
        const vElm = this.$createElement;
        let rtPath = this.path; //路线坐标

        if (!rtPath) {
            return this.$message({
            type: "error",
            message: "请先绘制完围栏区域后再提交！"
            });
        }
        this.$msgbox({
            title: "设置区域名称",
            message: vElm("msgbox-form", {
            on: { getname: this.getAreaName }
            }),
            showCancelButton: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            beforeClose(action, instance, done) {
            if (action == "confirm") {
                setTimeout(() => {
                if (!_this.crtAreaName) {
                    this.$message({
                    type: "info",
                    message: "请输入路线名称"
                    });
                } else {
                  //提交新增的线路数据
                  _this.addRteArea(()=>{
                    //新增成功后关闭窗口
                    done();
                    //新增成功后刷新列表
                    _this.getList();
                    //新增成功后刷新地图
                    _this.getBoundary();
                    //初始化坐标 覆盖物 区域名称
                    this.clearAllGeoData();
                  });
                }
                }, 500);
            } else {
                done();
            }
            }
        })
        .catch(error => {});
    },
    //获取弹窗的线路名称
    getAreaName(payload) {
      this.crtAreaName = payload;
    },
    //搜索区域
    searchLocal() {
      let _this = this;
      let localName = this.searchLocalNm;
      this.local.search(localName,{forceLocal:true});
    },
    search(){
        if(!this.loading)
        {
            this.queryObj.page =1;
            this.getList();
        }
    },
    //下一页
    nextPage(){
        if(this.queryObj.page < this.queryObj.totalPage && !(this.loading))
        {
            this.queryObj.page += 1;
            this.getList();
        }
    },
    //上一页
    lastPage(){
        if(this.queryObj.page > 1 && this.queryObj.totalPage >1 && !(this.loading))
        {
            this.queryObj.page -= 1;
            this.getList();
        }
    },
    //清除地图
    clearMap(){
        /* let overlay = this.overlay;
        this.path = '';
        if(overlay){
            this.map.removeOverlay(overlay);
            this.overlay = null;
        } */
      this.getBoundary();
    },
    //获取行政区域
    getBoundary(callback){
        var bdary = this.bdary || new BMap.Boundary();
        var map = this.map;
        
        bdary.get("宁波市镇海区", function(rs){       //获取行政区域
            map.clearOverlays();        //清除地图覆盖物
            var count = rs.boundaries.length; //行政区域的点有多少个
            if (count === 0) {
            this.$message({
                type:'error',
                message:'未能获取当前输入行政区域'
            });
            return ;
            }

            var pointArray = [];
            for (var i = 0; i < count; i++) {
            var ply = new BMap.Polygon(rs.boundaries[i], {
                strokeWeight: 2,
                fillOpacity: 0.0,
                fillColor: "none",
                strokeColor: "#ff0000",
                strokeOpacity: 0.8,
                strokeStyle: "dashed"
            }); //建立多边形覆盖物
            map.addOverlay(ply);  //添加覆盖物
            pointArray = pointArray.concat(ply.getPath());
            }
            if(callback){
            callback.call();
            }else{
            map.setViewport(pointArray);
            }
        });
    },
    //查看围栏区域方法
    viewRoad(item) {
        var _this = this;
        var lnglat = item.line;
        var lines = lnglat.match(/(\b(\w+\.?\w{0,})\b(?!['":]))/g); //正则匹配坐标值，返回坐标数组
        var points = [],
          j = 0,
          len = lines.length; //坐标点容器，每次清空后存储两个值

        for (; j < len; j += 2) {
          var point = new BMap.Point(lines[j], lines[j + 1]);
          points.push(point);
        }

        var polygon = new BMap.Polygon(points,{
                strokeWeight: 2,
                fillOpacity: 0.3,
                fillColor: "#ff1f3e",
                strokeColor: "#ff0000"
            });

        this.clearAllGeoData();

        this.getBoundary(function() {
          _this.map.addOverlay(polygon);
          _this.map.setViewport(points);
        });
    },
    //删除围栏区域方法
    removeRoad(item) {
      this.$confirm('是否确认删除该围栏?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let param = [item.rteLinePk]
        this.delRteArea(param,()=>{
          this.queryObj.label = '';
          //删除成功后刷新地图
          this.getBoundary();
          //删除成功后刷新列表
          this.getList();
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });          
      });
    },
    //修改通行证
    updateArea(item){
      let _this = this;
      let param = {
        "catCd":item.catCd,
        "label":item.label,
        "line":_this.path,
        "rteLinePk":item.rteLinePk
      };
      if(!param.line){
        return this.$message({
          type:'error',
          message:'请先绘制区域，再点击修改按钮'
        })
      }
      this.$confirm('是否确认修改 <strong>'+item.label+'</strong> 围栏?', '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.updRteArea(param,() => {
          this.queryObj.label = '';
          this.getBoundary();
          this.getList();
          this.clearAllGeoData();
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消修改'
        });          
      });
      
    },
    //获取通行证路线列表
    getList(){
        let _this = this;

        this.$router.push({
          path:"/fences/polygon",
          query:{"pageSize":_this.queryObj.page}
        });

        this.getRteArea(function(data){
            _this.list = data.list;
            _this.queryObj.page = data.currPage;
            _this.queryObj.totalPage = data.totalPage;
        });
    },
    // 从数据库获取路线下拉选项
    getRteArea(callback){
        let _this = this;
        let searchLabel = this.queryObj.label;
        let page = this.queryObj.page;
        let limit = this.queryObj.limit;
        let param= {
                limit:limit,
                page:page,
                filters: {
                    "groupOp":"AND",
                    "rules":[{"field":"cat_cd","op":"cn","data":"1107.160"}]}};

        if(this.queryObj.label){
            param.filters.rules.push({"field":"label","op":"cn","data":searchLabel});
        }

        this.loading = true;
        $http.getPassportRteLine(param).then(response => {
            if (response && response.code === 0) {
                callback(response.page);
            }else {
                _this.$message({
                    message: response.msg,
                    type: 'error'
                });
            }
            this.loading = false;
        }).catch(error => {
            console.log(error);
            this.loading = false;
        });
    },
    //新增围栏区域
    addRteArea(callback){
      let label = this.crtAreaName;//围栏名称
      let line = this.path;
      let param = {catCd:"1107.160",line:line,label:label};
      $http.addPassportRteLine(param).then( res => {
        if(res.code == 0){
          this.$message({
            type:"success",
            message:res.msg || '新增线路成功'
          });
          callback && callback();
        }else{
          this.$message({
            type:"error",
            message:res.msg || '新增线路失败'
          });
        }
      })
      .catch( err => {

      });
    },
    //删除通行证线路
    delRteArea(param,callback){
      $http.delPassportRteLine(param).then( res => {
          if(res.code == 0){
            callback && callback();
            this.$message({
              type: 'success',
              message: res.msg || '删除成功!'
            });
          }else{
            this.$message({
              type: 'error',
              message: res.msg ||  '删除失败!'
            });
          }
        })
        .catch( err => {

        });
    },
    //修改围栏区域
    updRteArea(param,callback){
      $http.updPassportRteLine(param).then( res => {
        if(res.code == 0){
          callback && callback();
          this.$message({
            type: 'success',
            message: res.msg || '更新成功!'
          });
        }else{
          this.$message({
            type: 'error',
            message: res.msg ||  '更新失败!'
          });
        }
      })
      .catch( err => {

      });
    },
    //清空绘制地图的数据，坐标，覆盖物，路线名称
    clearAllGeoData(){
      this.path = "";
      this.overlay = null;
      this.crtAreaName = '';
    },
  }
};
</script>
<style scoped>
.left-panel {
  /* pointer-events: none; */
  position: absolute;
  left: 20px;
  top: 20px;
  /* overflow: hidden; */
}
.left-panel .card-list {
  position: relative;
  z-index: 6;
  width: 368px;
  height: 400px;
  padding: 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}
.left-panel .card-list ul {
  padding: 4px;
  margin: 10px 0px;
  height: 281px;
  overflow-y: auto;
  border-radius: 5px;
  box-shadow: 0 0 5px #dfdfdf inset;
  -webkit-box-shadow: 0 0 5px #dfdfdf inset;
}
.left-panel .card-list ul li {
  padding: 6px 4px;
  overflow: hidden;
}
.fl-r {
  float: right;
}

.left-panel .road-name {
  display: inline-block;
  width: 60%;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #666;
}
.left-panel .pagingation {
  padding-top: 6px;
}
.left-panel .searchbox {
  position: relative;
  z-index: 6;
  width: 368px;
  min-height: 220px;
  margin-bottom: 20px;
  padding: 10px 10px 10px 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}
.left-panel .searchbox .searchbox-top {
  position: relative;
  height: auto;
  width: 100%;
  overflow: hidden;
  padding-left: 10px;
  padding-right: 10px;
}
.left-panel .searchbox .search-form {
  width: 100%;
}
.left-panel .searchbox .left-icon,
.left-panel .searchbox .right-icon {
  position: absolute;
  width: 30px;
  height: 100%;
  z-index: 6;
}
.left-panel .searchbox .left-icon {
  top: 50%;
  left: 6px;
  margin-top: -15px;
}
.left-panel .searchbox .right-icon {
  top: 50%;
  right: -10px;
  margin-top: -15px;
  cursor: pointer;
}
.left-panel .el-form .el-form-item {
  margin-bottom: 3px;
}
.left-panel .searchbox .searchbox-bottom {
  padding: 0 10px;
}
.left-panel .searchbox .searchbox-bottom h5 {
  margin-bottom: 3px;
  margin-top: 5px;
}
.left-panel .searchbox .searchbox-bottom p {
  font-size: 12px;
  line-height: 1.3;
  margin: 4px 0;
}
.left-panel .searchbox .searchbox-bottom p em {
  color: red;
  font-style: normal;
}
.searchbox-oprat-btn {
  position: absolute;
  width: 68px;
  right: -68px;
  top: 10px;
  z-index: -1;
}
.searchbox-oprat-btn .oprat-btn {
  padding: 10px;
  width: 60px;
  margin-bottom: 10px;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  cursor: pointer;
  background: #3385ff;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.left-panel .searchbox .searchbox-bottom .clear-btn {
  margin-top: 12px;
}
</style>
