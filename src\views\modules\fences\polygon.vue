<template>
  <div
    class="panel"
    :style="{
      height: mapSetting.mapHeight + 'px',
      margin: '0px',
      width: '100%'
    }"
  >
    <keep-alive>
      <component :is="map" :compname="'plygon'" :param="mapSetting">
        <polygon-comp></polygon-comp>
      </component>
    </keep-alive>
  </div>
</template>
<script>
import BMapComp from "@/components/BMapComp";
import polygonComp from "./polygon-comp";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      map: "BMapComp",
      mapSetting: {
        scrollWheelZoom: true,
        mapHeight: "768px",
        mapDrawing: true,
        drawingModes: [BMAP_DRAWING_POLYGON, BMAP_DRAWING_RECTANGLE]
      }
    };
  },
  created() {
    let mapHeight = Tool.getClientHeight();
    let _this = this;

    this.mapSetting.mapHeight = mapHeight + "px";
    window.addEventListener("resize", function() {
      _this.mapSetting.mapHeight = Tool.getClientHeight();
    });
  },
  components: {
    BMapComp,
    polygonComp
  },
  methods: {}
};
</script>
<style scoped></style>
