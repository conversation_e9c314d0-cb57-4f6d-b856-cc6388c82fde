<template>
  <div class="left-panel">
    <div class="searchbox" v-if="false">
      <div class="searchbox-top">
        <div class="search-form">
          <el-form>
            <el-form-item>
              <el-input size="small" id="startRoute" placeholder="请输入起点" v-model="startRouteName">
                <i class="el-icon-location" slot="prefix"> </i>
                <i class="el-icon-circle-plus suffix-add-btn" slot="suffix" @click="addRoute"></i>
              </el-input>
            </el-form-item>
            <el-form-item v-for="(item, index) in routes" :key="index">
              <el-input size="small" placeholder="请输入途经点" v-model="item.routeName" v-autocomplete="{ index: index, vnode: that }">
                <i class="el-icon-location-outline" slot="prefix"> </i>
                <i class="el-icon-remove suffix-remove-btn" slot="suffix" @click="removeRoute(index)"></i>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                size="small"
                id="endRoute"
                placeholder="请输入终点"
                v-model="endRouteName"
              >
                <i class="el-icon-location" slot="prefix"> </i>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="left-icon">
          <span
            class="el-icon-sort"
            title="调换起点和终点"
            @click="reverseStartEnd"
          ></span>
        </div>
        <div class="right-icon">
          <span
            class="el-icon-delete"
            @click="clearSearchRoute"
            title="清空路线"
          ></span>
        </div>
      </div>
      <div class="searchbox-bottom" v-pre>
        <h5 class="h5">线路绘制方法简介：</h5>
        <p>
          1. 输入路线起点和终点，如：<em>宁波 平海路</em
          >，然后回车/点击右侧的<em
            >“搜索”- <span class="el-icon-search"></span
          ></em>
          按钮
        </p>
        <p>2. 在地图中拖动起点、途径点、终点设置/修改线路</p>
        <p>
          3. 绘制完线路后点击右侧<em
            >“保存路线”- <span class="el-icon-upload"></span
          ></em>
          按钮进行保存
        </p>
      </div>
      <div class="searchbox-oprat-btn">
        <div class="oprat-btn" title="搜索" @click="searchRoutes">
          <span class="el-icon-search"></span>
        </div>
        <div class="oprat-btn" title="保存" @click="saveRoutes">
          <span class="el-icon-upload"></span>
        </div>
      </div>
    </div>
    <div class="card-list">
      <div class="search-bar">
        <el-input
          type="text"
          size="small"
          v-model="queryObj.label"
          @keyup.native.enter="search"
          placeholder="请输入关键词搜索线路"
        >
          <template slot="append">
            <el-button
              @click="search"
              type="success"
              size="small"
              icon="el-icon-search"
              >搜索</el-button
            >
          </template>
        </el-input>
      </div>
      <div class="poly-list" v-loading="loading">
        <ul>
          <li v-for="item in list" :key="item.crtTm">
            <span class="road-name" :title="item.label">{{ item.label }} </span>
            <div class="fl-r">
              <el-button-group>
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-search"
                  title="查看"
                  @click="viewRoad(item)"
                ></el-button>
<!--                <el-button
                  type="success"
                  size="mini"
                  icon="el-icon-edit"
                  title="修改"
                  @click="updateRoad(item)"
                ></el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  title="删除"
                  @click="removeRoad(item)"
                ></el-button>-->
              </el-button-group>
            </div>
          </li>
        </ul>
      </div>
      <div class="pagingation text-center">
        <el-button size="small" @click="lastPage" :disabled="lastPageDis"
          >上一页</el-button
        >
        <el-button size="small" @click="nextPage" :disabled="nextPageDis"
          >下一页</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import * as $http from "@/api/passport";
import * as Tool from "@/utils/tool";
export default {
  name: "plyline",
  data() {
    return {
      loading: false,
      startRouteName: "", //路径起始点名称
      endRouteName: "", //路径结束点名称
      crtRouteName: "", //保存路线时的名称
      startAc: null,
      endAc: null,
      map: null,
      bdary: null, //获取行政区域
      driving: null, //驾驶路线导航插件
      myDrawingManagerObject: null, //自定义绘制路线插件
      that: this,
      list: [],
      routes: [], //途经点集合
      autocompleteInstance: [], //保存百度地图自动搜索实例
      routePoints: "", //保存路径坐标点
      overlay: null, //保存绘制的路径
      path: "", //保存绘制的路径的坐标值
      queryObj: {
        label: "",
        limit: 15,
        page: 1,
        totalPage: 2,
      },
    };
  },
  directives: {
    autocomplete: {
      inserted(el, binding, vnode) {
        let _this = binding.value.vnode || vnode.context;
        let throughInp = el.getElementsByTagName("input");
        let _index = binding.value.index;

        _this.bindAutocomplete(throughInp, _index);
      },
    },
  },
  components: {
    "msgbox-form": {
      template: `<div>
        <el-form>
        <el-form-item>
          <el-input size="small" v-model="routeName" @change="getRouteName"></el-input>
          </el-form-item>
        </el-form>
        <p style="font-size:12px;line-height:1.3;" v-pre>提示：请添加线路名称描述，如：<em style="font-style:normal;color:red;">平海路 （全路段） </em><br>
        使用全角括号（）对线路进行细节描述，道路名称间用半角空格隔开，如
        <em  style="font-style:normal;color:red;">（俞范东路 至 隧道北路）</em><br>
        </p>
        </div>`,
      data() {
        return {
          routeName: "",
        };
      },
      methods: {
        getRouteName() {
          this.$emit("getname", this.routeName);
          this.routeName = "";
        },
      },
    },
  },
  created() {
    //获取地图组件
    this.map = this.$store.state.maps.map;
    //获取地图
    this.driving = this.$store.state.maps.mapPlugins.driving;
    //自定义绘制路线插件
    this.myDrawingManagerObject = this.$store.state.maps.mapPlugins.myDrawingManagerObject;
    //获取行政区域插件
    this.bdary = this.$store.state.maps.mapPlugins.bdary;
  },
  destroyed() {
    //销毁自动搜索实例
    if (this.autocompleteInstance.length > 0) {
      this.autocompleteInstance.filter((item) => {
        item.dispose();
      });
    }
    this.startAc ? (this.startAc.dispose(), (this.startAc = null)) : null;
    this.endAc ? (this.endAc.dispose(), (this.endAc = null)) : null;
  },
  mounted() {
    // this.$nextTick( () => {
    let _this = this;
    let map = this.map;
    let startRouteInp = document.getElementById("startRoute");
    let endRouteInp = document.getElementById("endRoute");
    //起点自动搜索
    let startAc = new BMap.Autocomplete({
      input: startRouteInp,
      location: map,
    });

    //终点自动搜索
    let endAc = new BMap.Autocomplete({ input: endRouteInp, location: map });

    this.startAc = startAc;
    this.endAc = endAc;

    this.$set(this.$data, "map", map);
    //起点搜索结果选中监听
    startAc.addEventListener("onconfirm", function (evt) {
      var _valueObj = evt.item.value;
      var loc =
        _valueObj.province +
        _valueObj.city +
        _valueObj.district +
        _valueObj.business +
        _valueObj.street +
        _valueObj.streetNumber;
      _this.startRouteName = loc;
    });
    //终点搜索结果选中监听
    endAc.addEventListener("onconfirm", function (evt) {
      var _valueObj = evt.item.value;
      var loc =
        _valueObj.province +
        _valueObj.city +
        _valueObj.district +
        _valueObj.business +
        _valueObj.street +
        _valueObj.streetNumber;
      _this.endRouteName = loc;
    });

    //手动绘制通行证线路
    let myDrawingManagerObject = this.myDrawingManagerObject;

    myDrawingManagerObject.addEventListener("overlaycomplete", function (o) {
      let path = o.overlay.getPath();
      let paths = "";
      let centerPoint;

      _this.driving &&
        _this.driving.getResults() &&
        _this.driving.clearResults(); //清空自动搜索结果
      _this.path = "";
      if (_this.overlay) {
        _this.map.removeOverlay(_this.overlay);
      }
      _this.overlay = o.overlay;
      _this.path = JSON.stringify(path);
    });

    let currPage = (this.$route.query && this.$route.query.pageSize) || 1;

    if (currPage) {
      this.queryObj.page = currPage;
    }

    //获取线路列表
    this.getList();
    // });
  },
  computed: {
    nextPageDis() {
      return !(
        this.queryObj.totalPage > 1 &&
        this.queryObj.page < this.queryObj.totalPage
      );
    },
    lastPageDis() {
      return !(this.queryObj.page > 1 && this.queryObj.totalPage > 1);
    },
  },
  methods: {
    //绑定途经点的搜索路线功能
    bindAutocomplete(el, index) {
      this.$nextTick(() => {
        let _this = this;
        let _index = index;
        let map = this.map;
        //实例化地图路线搜索插件
        let throughInpAc = new BMap.Autocomplete({
          input: el[0],
          location: map,
        });

        this.autocompleteInstance.push(throughInpAc);
        //途经点选择路线结果监听事件
        throughInpAc.addEventListener("onconfirm", function (evt) {
          var _valueObj = evt.item.value;
          var loc =
            _valueObj.province +
            _valueObj.city +
            _valueObj.district +
            _valueObj.business +
            _valueObj.street +
            _valueObj.streetNumber;
          _this.routes[_index]["routeName"] = loc;
        });
      });
    },
    //保存线路
    saveRoutes() {
      let _this = this;
      const vElm = this.$createElement;
      let rtPath = this.path; //路线坐标

      if (!rtPath) {
        return this.$message({
          type: "error",
          message:
            "请先通过搜地点找到对应路线，通过拖动起始点绘制完线路后再提交！",
        });
      }
      this.$msgbox({
        title: "设置线路名称",
        message: vElm("msgbox-form", {
          on: { getname: this.getRouteName },
        }),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose(action, instance, done) {
          if (action == "confirm") {
            setTimeout(() => {
              if (!_this.crtRouteName) {
                this.$message({
                  type: "info",
                  message: "请输入路线名称",
                });
              } else {
                //提交新增的线路数据
                _this.addRteLine(() => {
                  //关闭窗口
                  done();
                  //新增成功后刷新地图
                  _this.getBoundary();
                  //新增成功后刷新线路列表
                  _this.getList();
                  //初始化坐标 覆盖物 路线名称
                  this.clearAllGeoData();
                });
              }
            }, 500);
          } else {
            done();
          }
        },
      }).catch((error) => {});
    },
    //获取弹窗的线路名称
    getRouteName(payload) {
      this.crtRouteName = payload;
    },
    //搜索路线
    searchRoutes() {
      let _this = this;
      let queryRoutes = [{ routeName: this.startRouteName }];

      this.routes.filter((item) => {
        queryRoutes.push({ routeName: item.routeName });
      });

      queryRoutes.push({ routeName: this.endRouteName });

      this.drivingSearch(queryRoutes);
    },
    drivingSearch(queryRoutes) {
      let _this = this;
      var driving = this.driving;
      var map = this.map;
      var routeStartInputVal = queryRoutes[0].routeName, // 起始点
        routeEndInputVal =
          queryRoutes[queryRoutes.length - 1].routeName || routeStartInputVal; // 终点

      if (!routeStartInputVal) {
        this.$message({
          type: "info",
          message: "请先输入起点",
        });
        return false;
      }
      // 途经点
      var routeThroughInputVals = [];

      for (var i = 1, len = queryRoutes.length - 1; i < len; i++) {
        var item = queryRoutes[i].routeName;
        if (item) {
          routeThroughInputVals.push(item);
        }
      }

      driving.search(routeStartInputVal, routeEndInputVal, {
        waypoints: routeThroughInputVals,
      }); //waypoints表示途经点
      driving.setSearchCompleteCallback(function (results) {
        //清空现有的覆盖物
        if (_this.overlay) map.clearOverlays(_this.overlay);
        //清空保存的路径坐标
        if (_this.path) _this.path = "";
        if (driving.getStatus() == BMAP_STATUS_SUCCESS) {
          // 获取第一条方案
          var plan = results.getPlan(0);
          var points = "",
            pointsArray = [];

          var dragPot = plan.getDragPois();

          for (var i = 0; i < plan.getNumRoutes(); i++) {
            var pathTemp = plan.getRoute(i).getPath();
            points = points + "," + JSON.stringify(pathTemp);
            pointsArray = pointsArray.concat(pathTemp);
          }

          if (points.substr(0, 1) == ",") {
            points = points.substr(1);
          }
          points = "[" + points + "]";

          _this.path = points;
          // 设置地图视野
          map.setViewport(pointsArray);
        } else {
          map.clearOverlays(); //清除地图覆盖物
        }
      });
    },
    //清空搜索数据
    clearSearchRoute() {
      this.startRouteName = "";
      this.endRouteName = "";
      if (this.routes.length) {
        this.routes.filter((item) => {
          item.routeName = "";
        });
      }
    },
    //调换起点和终点
    reverseStartEnd() {
      let temp = this.startRouteName;
      this.startRouteName = this.endRouteName;
      this.endRouteName = temp;
    },
    //删除途经点
    removeRoute(index) {
      this.routes.splice(index, 1);
      this.autocompleteInstance[index].dispose();
      this.autocompleteInstance.splice(index, 1);
    },
    //新增途经点
    addRoute() {
      if (this.routes.length == 10) {
        this.$message({
          type: "info",
          message: "最多添加10个途经点",
        });
        return false;
      }
      this.routes.push({
        routeName: "",
      });
    },
    //搜索通行证路线
    search() {
      if (!this.loading) {
        this.queryObj.page = 1;
        this.getList();
      }
    },
    //下一页
    nextPage() {
      if (this.queryObj.page < this.queryObj.totalPage && !this.loading) {
        this.queryObj.page += 1;
        this.getList();
      }
    },
    //上一页
    lastPage() {
      if (
        this.queryObj.page > 1 &&
        this.queryObj.totalPage > 1 &&
        !this.loading
      ) {
        this.queryObj.page -= 1;
        this.getList();
      }
    },
    // 绘制通行证线路
    viewRoad(item) {
      var _this = this;
      var res = Tool.createPolylineByJSON({
        map: this.map,
        lines: item.line,
      });
      this.getBoundary(function () {
        if (res && res.lines.length) {
          res.lines.forEach((it) => {
            _this.map.addOverlay(it);
          });
          _this.map.setViewport(res.pointes);
        }
      });
    },
    //清空绘制地图的数据，坐标，覆盖物，路线名称
    clearAllGeoData() {
      this.path = "";
      this.overlay = null;
      this.crtRouteName = "";
      this.routes = [];
    },
    //获取行政区域
    getBoundary(callback) {
      var bdary = this.bdary || new BMap.Boundary();
      var map = this.map;

      bdary.get("宁波市镇海区", function (rs) {
        //获取行政区域
        map.clearOverlays(); //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域",
          });
          return;
        }

        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polyline(rs.boundaries[i], {
            strokeWeight: 2,
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
            enableMassClear: false,
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          pointArray = pointArray.concat(ply.getPath());
        }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
    //删除路线方法
    removeRoad(item) {
      this.$confirm("是否确认删除该线路?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = [item.rteLinePk];
          this.delRteLine(param, () => {
            this.queryObj.label = "";
            this.getBoundary();
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
      /* this.$emit("handledisp", {
        data: item,
        lastHandleFn: function() {
          console.log(this.map);
        }
      }); */
    },
    //修改通行证
    updateRoad(item) {
      let _this = this;
      let param = {
        catCd: item.catCd,
        label: item.label,
        line: _this.path,
        rteLinePk: item.rteLinePk,
      };
      if (!param.line) {
        return this.$message({
          type: "error",
          message: "请先绘制路线，再点击修改按钮",
        });
      }
      this.$confirm(
        "是否确认修改该<strong> " + item.label + " </strong>通行证线路?",
        "提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.updRteLine(param, () => {
            //清空搜索关键词
            this.queryObj.label = "";
            //初始化地图
            this.getBoundary();
            //刷新通行证列表
            this.getList();
            //初始化坐标、覆盖物、路线名称
            this.clearAllGeoData();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消修改",
          });
        });
    },
    //获取通行证路线列表
    getList() {
      let _this = this;

      this.$router.push({
        path: "/fences/polyline",
        query: { pageSize: _this.queryObj.page },
      });

      this.getRoute(function (data) {
        _this.list = data.list;
        _this.queryObj.page = data.currPage;
        _this.queryObj.totalPage = data.totalPage;
      });
    },
    // 从数据库获取路线下拉选项
    getRoute(callback) {
      let _this = this;
      let searchLabel = this.queryObj.label;
      let page = this.queryObj.page;
      let limit = this.queryObj.limit;
      let param = {
        limit: limit,
        page: page,
        filters: {
          groupOp: "AND",
          rules: [{
            field: "cat_cd", op: "eq", data: "1107.150"
          },{"field":"cd","op":"eq","data":"road.01"}],
        },
      };

      if (this.queryObj.label) {

        /* param.filters.rules.push({
          field: "label",
          op: "cn",
          data: searchLabel,
        }); */
        param.filters.rules.push({
          field: "label_new",
          op: "nao",
          data: searchLabel,
        });

      }

      this.loading = true;
      $http
        .getPassportRteLine(param)
        .then((response) => {
          if (response && response.code === 0) {
            callback(response.page);
          } else {
            _this.$message({
              message: response.msg,
              type: "error",
            });
          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error);
          this.loading = false;
        });
    },
    //新增通行证路线
    addRteLine(callback) {
      let label = this.crtRouteName; //路线名称
      let line = this.path;
      let param = { catCd: "1107.150", line: line, label: label };
      $http
        .addPassportRteLine(param)
        .then((res) => {
          if (res.code == 0) {
            this.$message({
              type: "success",
              message: res.msg || "新增线路成功",
            });
            callback && callback();
          } else {
            this.$message({
              type: "error",
              message: res.msg || "新增线路失败",
            });
          }
        })
        .catch((err) => {});
    },
    //删除通行证线路
    delRteLine(param, callback) {
      $http
        .delPassportRteLine(param)
        .then((res) => {
          if (res.code == 0) {
            callback && callback();
            this.$message({
              type: "success",
              message: res.msg || "删除成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: res.msg || "删除失败!",
            });
          }
        })
        .catch((err) => {});
    },
    //修改通行证线路
    updRteLine(param, callback) {
      $http
        .updPassportRteLine(param)
        .then((res) => {
          if (res.code == 0) {
            callback && callback();
            this.$message({
              type: "success",
              message: res.msg || "更新成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: res.msg || "更新失败!",
            });
          }
        })
        .catch((err) => {});
    },
  },
};
</script>
<style scoped>
.left-panel {
  /* pointer-events: none; */
  position: absolute;
  left: 20px;
  top: 20px;
  /* overflow: hidden; */
}
.left-panel .card-list {
  position: relative;
  z-index: 6;
  width: 368px;
  /*height: 400px;*/
  padding: 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}
.left-panel .card-list ul {
  padding: 4px;
  margin: 10px 0px;
  height: 610px;
  overflow-y: auto;
  border-radius: 5px;
  box-shadow: 0 0 5px #dfdfdf inset;
  -webkit-box-shadow: 0 0 5px #dfdfdf inset;
}
.left-panel .card-list ul li {
  padding: 6px 4px;
  overflow: hidden;
}
.fl-r {
  float: right;
}

.left-panel .road-name {
  display: inline-block;
  width: 60%;
  vertical-align: middle;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #666;
}
.left-panel .pagingation {
  padding-top: 6px;
}
.left-panel .searchbox {
  position: relative;
  z-index: 6;
  width: 368px;
  min-height: 220px;
  margin-bottom: 20px;
  padding: 10px 10px 10px 10px;
  background-color: #fefefe;
  border-radius: 5px;
  box-shadow: 0 0 15px #d5d5d5;
  -webkit-box-shadow: 0 0 15px #d5d5d5;
}
.left-panel .searchbox .searchbox-top {
  position: relative;
  height: auto;
  width: 100%;
  overflow: hidden;
  padding-left: 30px;
  padding-right: 30px;
}
.left-panel .searchbox .search-form {
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  overflow-x: hidden;
}
.left-panel .searchbox .left-icon,
.left-panel .searchbox .right-icon {
  position: absolute;
  width: 30px;
  height: 100%;
  z-index: 6;
}
.left-panel .searchbox .left-icon {
  top: 50%;
  left: 6px;
  margin-top: -15px;
  cursor: pointer;
}
.left-panel .searchbox .right-icon {
  top: 50%;
  right: -10px;
  margin-top: -15px;
  cursor: pointer;
}
.left-panel .el-form .el-form-item {
  margin-bottom: 3px;
}
.left-panel .searchbox .searchbox-bottom {
  padding: 0 10px;
}
.left-panel .searchbox .searchbox-bottom h5 {
  margin-bottom: 5px;
  margin-top: 6px;
}
.left-panel .searchbox .searchbox-bottom p {
  font-size: 12px;
  line-height: 1.3;
  margin: 4px 0;
}
.left-panel .searchbox .searchbox-bottom p em {
  color: red;
  font-style: normal;
}
.searchbox-oprat-btn {
  position: absolute;
  width: 68px;
  right: -68px;
  top: 10px;
  z-index: -1;
}
.searchbox-oprat-btn .oprat-btn {
  padding: 10px;
  width: 60px;
  margin-bottom: 10px;
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  cursor: pointer;
  background: #3385ff;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.suffix-add-btn {
  font-size: 16px;
  color: #65ca3d;
  cursor: pointer;
}
.suffix-remove-btn {
  font-size: 16px;
  color: #ff4e4e;
  cursor: pointer;
}
</style>
