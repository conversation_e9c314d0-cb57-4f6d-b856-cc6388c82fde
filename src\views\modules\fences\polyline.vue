<template>
  <div
    class="panel"
    :style="{ height: mapSetting.mapHeight + 'px', margin: '0px' }"
  >
    <keep-alive>
      <component :is="map" :compname="'plyline'" :param="mapSetting">
        <polyline-comp></polyline-comp>
      </component>
    </keep-alive>
  </div>
</template>
<script>
import BMapComp from "@/components/BMapComp";
import polylineComp from "./polyline-comp";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      map: "BMapComp",
      mapSetting: {
        scrollWheelZoom: true,
        mapHeight: "768px",
        mapDrawing: true,
        drawingModes: []
      }
    };
  },
  created() {
    let mapHeight = Tool.getClientHeight();
    let _this = this;

    this.mapSetting.mapHeight = mapHeight + "px";
    window.addEventListener("resize", function() {
      _this.mapSetting.mapHeight = Tool.getClientHeight();
    });
  },
  components: {
    BMapComp,
    polylineComp
  },
  methods: {}
};
</script>
<style scoped></style>
