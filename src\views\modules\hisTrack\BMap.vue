<template>
  <div class="bmap-container"
       :loading="loading">
    <component :is="bmapConfig.mapType"
               ref="bmap"
               :height="mapHeight"
               :config="mapComponentConfig"
               @mapReadyCallback="mapReadyCallback"></component>
    <el-button-group class="tool-btns"
                     v-if="bmapConfig.isShowTools"
                     :style="bmapConfig.toolsStyle">
      <slot name="btns-before"></slot>
      <el-button size="mini"
                 v-if="bmapConfig.isSwitchMapType"
                 :class="[bmapConfig.mapType === 'tiandi' ? 'active' : '']"
                 @click="switchMapType">{{ bmapConfig.mapType === "tiandi" ? "转百度地图" : "转天地图" }}
      </el-button>
      <el-button size="mini"
                 v-if="bmapConfig.isShowSatellite"
                 :class="[controllConfig.isActiveSatellite ? 'active' : '']"
                 @click="switchSatellite">卫星地图</el-button>
      <el-button size="mini"
                 v-if="bmapConfig.isShowDistance"
                 :class="[controllConfig.isActiveDistance ? 'active' : '']"
                 @click="switchDistance">测距工具</el-button>
      <el-button size="mini"
                 v-if="bmapConfig.isShowTraffic"
                 :class="[controllConfig.isActiveTraffic ? 'active' : '']"
                 @click="switchTraffic">路况信息</el-button>
      <slot name="btns-after"></slot>
    </el-button-group>
  </div>
</template>
<script>
import Baidu from "./components/baidu";
import Tiandi from "./components/tiandi";
import * as Tool from "@/utils/tool";
export default {
  name: "BMap",
  components: {
    Baidu,
    Tiandi
  },
  props: {
    // 地图信息配置包括如下：
    // mapType:'baidu',
    // mapHeight:
    // city: null,
    // drawingModes: [
    //   BMAP_DRAWING_POLYGON, // 多边形
    //   BMAP_DRAWING_RECTANGLE,
    //   BMAP_DRAWING_CIRCLE,
    // ],
    // isSwitchMapType:true,
    // isShowSatellite: true,
    // isShowDistance: true,
    // isShowTraffic: true,
    // isShowDrowing: false,
    config: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 经纬度属性配置
    LngLatSetting: {
      type: Object,
      default: () => {
        return {
          baidu: {
            lng: "lonBd",
            lat: "latBd"
          },
          tiandi: {
            lng: "longitude",
            lat: "latitude"
          }
        };
      }
    }
  },
  inject: ["getMap"],
  data () {
    return {
      loading: false,
      markerList: [],
      bmap: null, // 地图对象
      bmapConfig: {
        mapType: "baidu", //地图类型：['baidu','tiandi']
        mapHeight: Tool.getClientHeight() + "px",
        city: "",
        isShowTools: true, // 是否显示工具条
        isSwitchMapType: true, // 地图切换
        isShowSatellite: true, // 是否显示卫星地图
        isShowDistance: true, // 是否显示测距
        isShowTraffic: true, // 是否显示路况

        drawingModes: []
      },
      controllConfig: {
        // 控件状态配置
        isActiveSatellite: false, // 是否高亮卫星图按钮
        isActiveDistance: false, // 是否高亮测距功能按钮
        isActiveTraffic: false // 是否高亮路况按钮
      },

      mapHeight: Tool.getClientHeight() + "px"
    };
  },
  watch: {
    "bmapConfig.mapHeight": {
      handler (newMapHeightFun) {
        window.removeEventListener("resize", this.resizeHeight);
        if (
          Object.prototype.toString.call(newMapHeightFun) ===
          "[object Function]"
        ) {
          this.mapHeight = newMapHeightFun();
        } else if (
          Object.prototype.toString.call(newMapHeightFun) === "[object Number]"
        ) {
          this.mapHeight = newMapHeightFun + "px";
        } else {
          this.mapHeight = newMapHeightFun;
        }
        window.addEventListener("resize", this.resizeHeight);
      },
      immediate: true
    }
  },
  computed: {
    mapComponentConfig () {
      return {
        city: this.bmapConfig.city,
        isShowSatellite: this.bmapConfig.isShowSatellite,
        isShowDistance: this.bmapConfig.isShowDistance,
        isShowTraffic: this.bmapConfig.isShowTraffic,
        drawingModes: this.bmapConfig.drawingModes
      };
    },
    lngKey () {
      return this.LngLatSetting[this.bmapConfig.mapType].lng;
    },
    latKey () {
      return this.LngLatSetting[this.bmapConfig.mapType].lat;
    }
  },
  created () {
    let param = this.$props.config;
    this.bmapConfig = Object.assign({}, this.bmapConfig, param);
  },
  destroyed () {
    window.removeEventListener("resize", this.resizeHeight);
  },
  methods: {
    mapReadyCallback () {
      this.$emit("mapReadyCallback");
    },
    // 将一个或多个对象的内容合并到目标对象
    extendObj () {
      /*
       *target被扩展的对象
       *length参数的数量
       *deep是否深度操作
       */
      var options,
        name,
        src,
        copy,
        copyIsArray,
        clone,
        target = arguments[0] || {},
        i = 1,
        length = arguments.length,
        deep = false; // target为第一个参数，如果第一个参数是Boolean类型的值，则把target赋值给deep // deep表示是否进行深层面的复制，当为true时，进行深度复制，否则只进行第一层扩展 // 然后把第二个参数赋值给target

      if (typeof target === "boolean") {
        deep = target;
        target = arguments[1] || {}; // 将i赋值为2，跳过前两个参数

        i = 2;
      } // target既不是对象也不是函数则把target设置为空对象。

      if (typeof target !== "object" && getObjType(target) !== "function") {
        target = {};
      } // 开始遍历需要被扩展到target上的参数

      for (; i < length; i++) {
        // 处理第i个被扩展的对象，即除去deep和target之外的对象
        if ((options = arguments[i]) != null) {
          // 遍历第i个对象的所有可遍历的属性
          for (name in options) {
            // 根据被扩展对象的键获得目标对象相应值，并赋值给src
            src = target[name]; // 得到被扩展对象的值
            copy = options[name]; // 这里为什么是比较target和copy？不应该是比较src和copy吗？

            if (target === copy) {
              continue;
            } // 当用户想要深度操作时，递归合并 // copy是纯对象或者是数组

            if (
              deep &&
              copy &&
              (getObjType(copy) === "object" ||
                (copyIsArray = getObjType(copy) === "array"))
            ) {
              // 如果是数组
              if (copyIsArray) {
                // 将copyIsArray重新设置为false，为下次遍历做准备。
                copyIsArray = false; // 判断被扩展的对象中src是不是数组
                clone = src && getObjType(src) == "array" ? src : [];
              } else {
                // 判断被扩展的对象中src是不是纯对象
                clone = src && getObjType(src) == "object" ? src : {};
              } // 递归调用extend方法，继续进行深度遍历

              target[name] = extendObj(deep, clone, copy); // 如果不需要深度复制，则直接把copy（第i个被扩展对象中被遍历的那个键的值）
            } else if (copy !== undefined) {
              target[name] = copy;
            }
          }
        }
      } // 原对象被改变，因此如果不想改变原对象，target可传入{}
      return target;
    },
    // 设置地图高度
    resizeHeight () {
      if (
        Object.prototype.toString.call(this.bmapConfig.mapHeight) ===
        "[object Function]"
      ) {
        this.mapHeight = this.bmapConfig.mapHeight();
      } else if (
        Object.prototype.toString.call(this.bmapConfig.mapHeight) ===
        "[object Number]"
      ) {
        this.mapHeight = this.bmapConfig.mapHeight + "px";
      } else {
        this.mapHeight = this.bmapConfig.mapHeight;
      }
    },
    switchMapType () {
      this.bmap = null;
      this.bmapConfig.mapType =
        this.bmapConfig.mapType === "baidu" ? "tiandi" : "baidu";
    },
    // 卫星地图
    switchSatellite () {
      this.controllConfig.isActiveSatellite = !this.controllConfig
        .isActiveSatellite;
      if (this.controllConfig.isActiveSatellite) {
        this.$refs.bmap.openSatellite();
      } else {
        this.$refs.bmap.closeSatellite();
      }
    },
    //测距
    switchDistance () {
      this.controllConfig.isActiveDistance = !this.controllConfig
        .isActiveDistance;
      if (this.controllConfig.isActiveDistance) {
        this.$refs.bmap.openDistance();
      } else {
        this.$refs.bmap.closeDistance();
      }
    },
    //路况
    switchTraffic () {
      this.controllConfig.isActiveTraffic = !this.controllConfig
        .isActiveTraffic;
      if (this.controllConfig.isActiveTraffic) {
        this.$refs.bmap.openTraffic();
      } else {
        this.$refs.bmap.closeTraffic();
      }
    },
    // 设置zoom
    setCenter (pt, zoom) {
      this.$refs.bmap.setCenter(pt, zoom);
    },
    // 设置zoom
    setZoom (val) {
      this.$refs.bmap.setZoom(val);
    },
    setViewport (pointArray) {
      if (!pointArray) return;
      this.$refs.bmap.setViewport(pointArray);
    },
    addOverlays (overlays, name) {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      this.$refs.bmap.addOverlays(overlays, name);
    },
    removeAllOverlays () {
      if (!this.$refs.bmap) return;
      this.$refs.bmap.removeAllOverlays();
    },
    removeOverlays (overlays) {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      this.$refs.bmap.removeOverlays(overlays);
    },
    removeOverlaysByName (name) {
      if (!this.$refs.bmap) return;
      this.$refs.bmap.removeOverlaysByName(name);
    },
    // 获取围栏中心点
    getCenterPoint (path) {
      let _this = this;
      let paramsPath = path.map(item => {
        return {
          lng: item[_this.lngKey],
          lat: item[_this.latKey]
        };
      });

      return this.$refs.bmap.getCenterPoint(paramsPath);
    },
    getWheelList (data) {
      let map = this.getMap();
      this.markerList.forEach(item => {
        this.$refs.bmap.removeOverlays(item)
      })
      data.forEach(item => {
        let marker = this.$refs.bmap.createCarMarker(item.lonBd, item.latBd);
        let label = this.$refs.bmap.createLabel(item.turn + item.speed + "km/h", -20, -20);
        label.setStyle({
          color: "#000000",
          borderWidth: "1px",
          borderColor: "#cdcdcd",
          backgroundColor: "#ffffff",
          borderRadius: "3px",
          padding: "1px 3px",
        });
        marker.setLabel(label);
        map.addOverlays(marker, "");
        this.markerList.push(marker)
      });
    },
    createLabel () {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (
        arguments.length === 1 ||
        arguments.length === 3 ||
        arguments.length === 5
      ) {
        // 3个参数分别是msg, offsetX, offsetY
        // return this.$refs.bmap.createLabel(
        //   arguments[0],
        //   arguments[1],
        //   arguments[2]
        // );
        return this.$refs.bmap.createLabel.apply(
          this,
          Array.prototype.slice.call(arguments, 0)
        );
        // }else if (arguments.length === 5) {
        //   // 3个参数分别是msg, offsetX, offsetY
        //   // return this.$refs.bmap.createLabel(
        //   //   arguments[0],
        //   //   arguments[1],
        //   //   arguments[2],
        //   //   arguments[3],
        //   //   arguments[4]
        //   // );
      } else {
        throw "BMap中createLabel方法传参错误！";
      }
    },
    // 创建点：1个参数则必须是对象(里面包含百度和天地图的经纬度)，2个参数则-经度+纬度（未指明百度还是天地图，需要在调用的时候自己判断）
    createPoint () {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 1) {
        let paramsType = Object.prototype.toString.apply(arguments[0]);
        if (paramsType === "[object Object]") {
          let param = arguments[0];
          let lng = param[this.lngKey];
          let lat = param[this.latKey];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createPoint(lng, lat);
          } else {
            throw "BMap中createPoint方法传参中不存在经纬度参数，或经纬度参数未传递！";
          }
        } else {
          throw "BMap中createPoint方法传1个参数应该是对象参数！";
        }
      } else if (arguments.length === 2) {
        return this.$refs.bmap.createPoint(arguments[0], arguments[1]);
      } else {
        throw "BMap中createPoint方法传参错误！";
      }
    },
    // 创建marker：2个参数-经纬度对象+icon图标，3个参数-经度+纬度+icon图标
    createMarker () {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let paramsType = Object.prototype.toString.apply(arguments[0]);
        if (paramsType === "[object Object]") {
          let param = arguments[0];
          let lng = param[this.lngKey];
          let lat = param[this.latKey];
          let icon = arguments[1];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createMarker(lng, lat, icon);
          } else {
            console.log(arguments);
            throw "BMap中createMarker方法传参中不存在经纬度参数，或经纬度参数未传递！";
          }
        } else {
          throw "BMap中createMarker方法传2个参数应该是对象参数！";
        }
      } else if (arguments.length === 3) {
        // 3个参数分别是lng, lat, icon
        return this.$refs.bmap.createMarker(
          arguments[0],
          arguments[1],
          arguments[2]
        );
      } else {
        throw "BMap中createMarker方法传参错误！";
      }
    },
    createCarMarker () {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let paramsType = Object.prototype.toString.apply(arguments[0]);
        if (paramsType === "[object Object]") {
          let param = arguments[0];
          let lng = param[this.lngKey];
          let lat = param[this.latKey];
          let icon = arguments[1];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createCarMarker(lng, lat, icon);
          } else {
            console.log(arguments);
            throw "BMap中createCarMarker方法传参中不存在经纬度参数，或经纬度参数未传递！";
          }
        } else {
          throw "BMap中createCarMarker方法传2个参数应该是对象参数！";
        }
      } else if (arguments.length === 3) {
        // 3个参数分别是lng, lat, icon
        return this.$refs.bmap.createCarMarker(
          arguments[0],
          arguments[1],
          arguments[2]
        );
      } else {
        throw "BMap中createCarMarker方法传参错误！";
      }
    },
    // 创建icon：必须3个参数-图片url+图片宽度wSize+图片高度hSize
    createIcon (url, wSize, hSize) {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 3) {
        return this.$refs.bmap.createIcon(
          arguments[0],
          arguments[1],
          arguments[2]
        );
      } else {
        throw "BMap中createIcon方法传参错误！";
      }
    },
    // 创建多边形：必须是2个参数，点数组+多边形style
    createPolygon () {
      if (!this.$refs.bmap) return;
      let _this = this;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let pointArr = arguments[0];
        let style = Object.assign({}, arguments[1]);
        let pArr = pointArr.map(iitem => {
          let lng = iitem[_this.lngKey];
          let lat = iitem[_this.latKey];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createPoint(lng, lat);
          } else {
            throw "BMap中createPolygon方法中传参点对象数组中不存在经纬度属性！";
          }
        });
        return this.$refs.bmap.createPolygon(pArr, style);
      } else {
        throw "BMap中createPolygon方法传参错误！";
      }
    },
    // 创建折线：必须是2个参数，点数组+折线style
    createPolyline (pointArr, styleObj) {
      if (!this.$refs.bmap) return;
      let _this = this;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let pointArr = arguments[0];
        let style = Object.assign({}, arguments[1]);
        let pArr = pointArr.map(iitem => {
          let lng = iitem[_this.lngKey];
          let lat = iitem[_this.latKey];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createPoint(lng, lat);
          } else {
            console.log(arguments);
            throw "BMap中createPolyline方法中传参点对象数组中不存在经纬度属性！";
          }
        });
        return this.$refs.bmap.createPolyline(pArr, style);
      } else {
        throw "BMap中createPolyline方法传参错误！";
      }
    },
    // 获取折线或多边形覆盖物的点数组：必须是1个参数，折线/多边形
    getPath () {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 1) {
        return this.$refs.bmap.getPath(arguments[0]);
      } else {
        throw "BMap中getPath方法传参错误！";
      }
    },
    // 创建弹窗
    // 创建弹窗：2个参数-经纬度对象+_dom，3个参数-经度+纬度+_dom
    createInfoWindow (lng, lat, _dom) {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let paramsType = Object.prototype.toString.apply(arguments[0]);
        if (paramsType === "[object Object]") {
          let param = arguments[0];
          let lng = param[this.lngKey];
          let lat = param[this.latKey];
          let _dom = arguments[1];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createInfoWindow(lng, lat, _dom);
          } else {
            throw "BMap中createInfoWindow方法传参中不存在经纬度参数，或经纬度参数未传递！";
          }
        } else {
          throw "BMap中createInfoWindow方法传2个参数应该是对象参数！";
        }
      } else if (arguments.length === 3) {
        // 3个参数分别是lng, lat, _dom
        return this.$refs.bmap.createInfoWindow(
          arguments[0],
          arguments[1],
          arguments[2]
        );
      } else {
        throw "BMap中createInfoWindow方法传参错误！";
      }
    },
    // 关闭弹窗信息
    closeInfoWindow () {
      if (!this.$refs.bmap) return;
      this.$refs.bmap.closeInfoWindow();
    },
    // 创建弹窗：2个参数-经纬度对象+_dom，3个参数-经度+纬度+_dom
    createInfoBox (lng, lat, _dom) {
      if (!this.$refs.bmap) return;
      if (arguments.length === 0) return;
      else if (arguments.length === 2) {
        let paramsType = Object.prototype.toString.apply(arguments[0]);
        if (paramsType === "[object Object]") {
          let param = arguments[0];
          let lng = param[this.lngKey];
          let lat = param[this.latKey];
          let _dom = arguments[1];
          if (lng != undefined && lat != undefined) {
            return this.$refs.bmap.createInfoBox(lng, lat, _dom);
          } else {
            throw "BMap中createInfoBox方法传参中不存在经纬度参数，或经纬度参数未传递！";
          }
        } else {
          throw "BMap中createInfoBox方法传2个参数应该是对象参数！";
        }
      } else if (arguments.length === 3) {
        // 3个参数分别是lng, lat, _dom
        return this.$refs.bmap.createInfoBox(
          arguments[0],
          arguments[1],
          arguments[2]
        );
      } else {
        throw "BMap中createInfoBox方法传参错误！";
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.bmap-container {
  position: relative;
  margin: 0;
  padding: 0;
  z-index: 1;

  .tool-btns {
    position: absolute;
    z-index: 10;
    right: 10px;
    bottom: 5px;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      z-index: 9999;
    }

    /deep/ {
      .el-button {
        width: 100%;
        display: block;
        color: #333;
        background-color: #fff;
        border-radius: 0;
        &.active {
          color: #fff;
          background-color: #409eff;
        }
      }
      &.el-button-group > .el-button {
        float: none;
      }
    }
  }
}
</style>
