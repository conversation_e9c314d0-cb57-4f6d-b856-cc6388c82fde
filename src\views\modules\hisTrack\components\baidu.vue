<template>
  <div class="map-content"
       v-loading="loading">
    <div class="map"
         ref="mapNode"
         :style="{ height: height}"></div>
    <slot></slot>
  </div>
</template>

<script>
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
BMap.Label.prototype.setText = function (text) {
  this.setContent(text);
}
export default {
  name: "BaiduMap",
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
    height: {
      type: String,
      required: true,
    },
  },
  data () {
    return {
      loading: false,
      map: null,
      mapConfig: {
        city: null,
        drawingModes: [
          BMAP_DRAWING_POLYGON, // 多边形
          BMAP_DRAWING_RECTANGLE,
          BMAP_DRAWING_CIRCLE,
        ],
        isShowSatellite: true,
        isShowDistance: true,
        isShowTraffic: true,
        isShowDrowing: false,
      },

      distanceControl: null, // 测距工具
      trafficControl: null, // 路况工具
      drawingControl: null, // 绘图工具
      geocControl: null, // 经纬度解析工具
    };
  },
  created () {
    let param = this.$props.config;
    this.mapConfig = Object.assign({}, this.mapConfig, param);
  },
  mounted () {
    this.$nextTick(() => {
      this._initMap(); // init map info
    });
  },
  methods: {
    _initMap () {
      this.map = new BMap.Map(this.$refs.mapNode, { enableMapClick: false });
      let point = new BMap.Point(121.680125, 29.984771);
      this.map.centerAndZoom(point, 13);
      this.map.enableScrollWheelZoom();

      this._setBoundary();
      //加载地图控件
      this._addMapControl();

      this.$emit("mapReadyCallback");
    },
    // 设置行政区域
    _setBoundary () {
      if (!this.mapConfig.city) {
        return;
      }
      let _this = this;
      let map = this.map;
      let bdary = new BMap.Boundary();
      bdary.get(this.mapConfig.city, function (rs) {
        //获取行政区域
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域",
          });
          return;
        }
        let pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polyline(rs.boundaries[i], {
            strokeWeight: 3,
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
          pointArray = pointArray.concat(ply.getPath());
        }
        _this.map && _this.map.setViewport(pointArray);
      });
    },
    _addMapControl () {
      let _this = this;
      // 添加地图类型控件
      this.map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
        })
      );
    },
    // 打开卫星图
    openSatellite () {
      this.map.setMapType(BMAP_SATELLITE_MAP);
    },
    // 关闭卫星图
    closeSatellite () {
      this.map.setMapType(BMAP_NORMAL_MAP);
    },
    // 打开测距
    openDistance () {
      if (!this.distanceControl) {
        let _this = this;
        // 添加测距工具
        this.distanceControl = new BMapLib.DistanceTool(this.map, {
          lineStroke: 2,
        });
        this.distanceControl.addEventListener("drawend", function (e) {
          _this.distanceControl.close();
          _this.$parent.$data.controllConfig.isActiveDistance = false; // 测距功能关闭
        });
      }
      this.distanceControl.open();
    },
    // 关闭测距
    closeDistance () {
      this.distanceControl && this.distanceControl.close();
    },
    // 打开路况
    openTraffic () {
      if (!this.trafficControl) {
        // 添加路况控件
        this.trafficControl = new BMapLib.TrafficControl({
          showPanel: true, //是否显示路况提示面板
        });
        this.map.addControl(this.trafficControl);
        this.trafficControl.setAnchor(BMAP_ANCHOR_BOTTOM_RIGHT);
      }
      this.trafficControl.showTraffic();
    },
    // 关闭路况
    closeTraffic () {
      this.trafficControl && this.trafficControl.hideTraffic();
    },
    // 打开绘图功能
    openDrawing () {
      if (!this.drawingControl) {
        // let _this = this;
        // // 绘图工具
        // let polyOption = {
        //   strokeWeight: 2,
        //   strokeColor: "#078aff",
        // };
        // this.drawingControl = new BMapLib.DrawingManager(this.map, {
        //   isOpen: false,
        //   enableDrawingTool: true,
        //   drawingToolOptions: {
        //     anchor: BMAP_ANCHOR_TOP_RIGHT,
        //     offset: new BMap.Size(10, 40),
        //     enableCalculate: true,
        //     drawingModes: _this.mapConfig.drawingModes,
        //   },
        //   polygonOptions: polyOption,
        //   circleOptions: polyOption,
        //   rectangleOptions: polyOption,
        // });
        this._initDrawing(this.map);
      } else {
        this.drawingControl.open();
        this.drawingControl.setDrawingMode(BMAP_DRAWING_POLYGON);
      }
    },
    closeDrawing () {
      this.drawingControl && this.drawingControl.close();
    },
    // 初始化绘制模式
    _initDrawing (map) {
      var _this = this;
      var styleOptions = {
        strokeColor: "#d00", //边线颜色。
        fillColor: "#d00", //填充颜色。当参数为空时，圆形将没有填充效果。
        strokeWeight: 2, //边线的宽度，以像素为单位。
        strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
        strokeStyle: "solid", //边线的样式，solid或dashed。
      };
      //实例化鼠标绘制工具
      this.drawingControl = new BMapLib.DrawingManager(map, {
        isOpen: false, //是否开启绘制模式
        enableDrawingTool: true, //是否显示工具栏
        drawingToolOptions: {
          anchor: BMAP_ANCHOR_TOP_RIGHT, //位置
          offset: new BMap.Size(10, 25), //偏离值
          drawingModes: [
            BMAP_DRAWING_POLYGON, // 多边形
          ],
        },
        polygonOptions: styleOptions,
      });
      this.drawingControl.addEventListener("overlaycomplete", function (e) {
        let overlay = e.overlay;
        overlay.name = "drawing";
        _this.drawingControl.close();
        _this.$message({
          message: "绘制完成",
          type: "success",
        });
      });
    },
    // 地址反解析
    geocoder (point, cb) {
      if (!this.geocControl) {
        this.geocControl = new BMap.Geocoder();
      }
      this.geocControl.getLocation(point, function (rs) {
        let addComp = rs.addressComponents;
        let str = "";
        if (addComp.district) {
          str += addComp.district;
        }
        if (addComp.street) {
          str += addComp.street;
        }
        if (addComp.streetNumber) {
          str += addComp.streetNumber;
        }
        cb(str);
      });
    },
    //获取地图缩放级别
    getCurrentMapZoom () {
      return this.map.getZoom();
    },
    // 设置zoom
    setCenter (pt, zoom) {
      if (zoom) this.map.setZoom(zoom);
      if (pt) this.map.panTo(pt);
    },
    // 设置zoom
    setZoom (val) {
      this.map.setZoom(val);
    },
    setViewport (pointArray) {
      if (!pointArray) return;
      this.map.setViewport(pointArray);
    },
    addOverlays (overlays, name) {
      if (!this.map) {
        return;
      }
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach((overlay) => {
          _this.map.addOverlay(overlay);
          if (name) {
            overlay.name = name;
          }
        });
      } else {
        this.map.addOverlay(overlays);
        if (name) {
          overlays.name = name;
        }
      }
    },
    removeOverlays (overlays) {
      if (!this.map) {
        return;
      }
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach((overlay) => {
          overlay.removeEventListener('click');
          _this.map.removeOverlay(overlay);
        });
      } else {
        this.map.removeOverlay(overlays);
      }
    },
    removeOverlaysByName (name) {
      if (!this.map) {
        return;
      }
      var allOverlay = this.map.getOverlays();
      allOverlay.map((overlay) => {
        if (overlay.name === name) {
          overlay.removeEventListener('click')
          this.map.removeOverlay(overlay);
        }
      });
    },
    removeAllOverlays () {
      if (!this.map) {
        return;
      }
      var allOverlay = this.map.getOverlays();
      allOverlay.map((overlay) => {
        overlay.removeEventListener('click')
        this.map.removeOverlay(overlay);
      });
      this.map.clearOverlays();
    },
    // 获取围栏中心点
    getCenterPoint (path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return [x, y];
    },
    createLabel (msg, offsetX, offsetY) {
      let setting = {}
      if (offsetX != undefined && offsetY != undefined) {
        setting.offset = new BMap.Size(offsetX, offsetY)
      }
      return new BMap.Label(msg, setting);
    },
    // 创建点
    createPoint (lng, lat) {
      return new BMap.Point(lng, lat);
    },
    createMarker (lng, lat, icon) {
      let _this = this;
      //创建标注对象
      let marker = new BMap.Marker(this.createPoint(lng, lat));
      marker.setIcon(icon);
      return marker;
    },
    createCarMarker (lng, lat, icon) {
      let _this = this;
      //创建标注对象
      let marker = new BMap.Marker(this.createPoint(lng, lat));
      marker.setIcon(icon);
      return marker;
    },
    createIcon (url, wSize, hSize) {
      // let icon = new BMap.Icon(url, new BMap.Size(wSize, hSize),{anchor: new BMap.Size(wSize/2, hSize)});
      // icon.setImageSize(new BMap.Size(wSize, hSize)); //设置图标大小
      let icon = new BMap.Icon(url, new BMap.Size(wSize, hSize));
      return icon;
    },
    createPolygon (pointArr, styleObj) {
      let style = Object.assign({}, styleObj);
      return new BMap.Polygon(pointArr, style);
    },
    createPolyline (pointArr, styleObj) {
      let style = Object.assign({}, styleObj);
      return new BMap.Polyline(pointArr, style);
    },
    getPath (overlay) {
      if (!overlay) return;
      if (overlay.getPath) {
        return overlay.getPath();
      } else {
        throw "对不起，该覆盖物在百度地图上没有getPath方法！";
      }
    },
    // 创建弹窗
    createInfoWindow (lng, lat, _dom) {
      if (!this.map) {
        return;
      }
      if (this.infoWin) {
        this.closeInfoWindow();
      }
      let infoWin = (this.infoWin = new BMap.InfoWindow(_dom));
      this.map.openInfoWindow(infoWin, new BMap.Point(lng, lat));
      return this.infoWin;
    },
    // 关闭弹窗信息
    closeInfoWindow () {
      if (this.infoWin) {
        this.infoWin.close && this.infoWin.close();
        this.infoWin.closeInfoWindow && this.infoWin.closeInfoWindow();
        this.infoWin = null;
      }
    },
    // 创建弹窗
    createInfoBox (lng, lat, _dom) {
      if (!this.map) {
        return;
      }
      if (this.infoWin) {
        this.closeInfoWindow();
      }
      let infoWin = (this.infoWin = new BMapLib.InfoBox(this.map, _dom, {
        boxStyle: {
          width: "340px",
          marginBottom: "20px",
          marginLeft: "4px",
        },
        closeIconMargin: "-274px 0 0 0",
        closeIconUrl: imgsConfig.vec.close_btn,
      }));
      this.map.openInfoWindow(infoWin, new BMap.Point(lng, lat));
      return this.infoWin;
    },
  },
  destroyed () {
    this.map = null;
  },
};
</script>

<style lang="scss" scoped>
</style>