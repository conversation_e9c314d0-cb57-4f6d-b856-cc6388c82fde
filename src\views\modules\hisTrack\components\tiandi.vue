<template>
  <div class="map-content" v-loading="loading">
    <div class="map" ref="mapNode" :style="{ height: height}"></div>
    <slot></slot>
  </div>
</template>

<script>
// import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
// try {
//   T.carMarker = T.Overlay.extend({
//     initialize: function (lnglat, options) {
//       this.lnglat = lnglat;
//       if (options) {
//         this.setOptions(options);
//         this.options = options;
//       }
//     },
//     onAdd: function (map) {
//       this.map = map;
//       var div = (this.div = document.createElement("div"));
//       var img = (this.img = document.createElement("img"));
//       div.style.position = "absolute";
//       div.style.width = this.options.width + "px";
//       div.style.height = this.options.height + "px";
//       div.style.marginLeft = -this.options.width / 2 + "px";
//       div.style.marginTop = -this.options.height / 2 + "px";
//       div.style.zIndex = 200; // this._container.style.zIndex = zIndex;
//       img.style.width = this.options.width + "px";
//       img.style.height = this.options.height + "px";
//       img.src = this.options.iconUrl;
//       div.appendChild(img);
//       map.getPanes().overlayPane.appendChild(this.div);
//       if(this.markerLabel){
//         this.markerLabel.isShow = true;
//         map.addOverLay(this.markerLabel);
//       }
//       this.update(this.lnglat);
//     },
//     onRemove: function () {
//       var parent = this.div.parentNode;
//       if (parent) {
//         parent.removeChild(this.div);
//         this.map = null;
//         this.div = null;
//       }
//     },
//     /**
//      * 每个浏览器的偏转兼容
//      * @returns {string}
//      * @constructor
//      */
//     CSS_TRANSFORM: function () {
//       var div = document.createElement("div");
//       var props = [
//         "transform",
//         "WebkitTransform",
//         "MozTransform",
//         "OTransform",
//         "msTransform",
//       ];
//       for (var i = 0; i < props.length; i++) {
//         var prop = props[i];
//         if (div.style[prop] !== undefined) {
//           return prop;
//         }
//       }
//       return props[0];
//     },
//     /**
//      * 偏转角度
//      * @param rotate
//      */
//     setRotation: function (rotate) {
//       this.img.style[this.CSS_TRANSFORM()] = "rotate(" + rotate + "deg)";
//     },
//     setPosition: function (point) {
//       this.lnglat = point;
//       if(this.markerLabel){
//         this.markerLabel.setLngLat(point);
//       }
//       this.update();
//     },
//     // setLnglat: function (lnglat) {
//     //   this.lnglat = lnglat;
//     //   this.update();
//     // },
//     // getLnglat: function () {
//     //   return this.lnglat;
//     // },
//     getLabel:function(){
//       return this.markerLabel || null;
//     },
//     setLabel: function (label) {
//       this.markerLabel = label;
//       let point = this.lnglat;
//       if (point) {
//         label.setLngLat(point);
//       }
//     },
//     setPos: function (pos) {
//       this.lnglat = this.map.layerPointToLngLat(pos);
//       this.update();
//     },
//     // /**
//     //  * 更新位置
//     //  */
//     update: function () {
//       var pos = this.map.lngLatToLayerPoint(this.lnglat);
//       this.div.style.left = pos.x + "px";
//       this.div.style.top = pos.y + "px";
//     },
//   });
// } catch (error) {
//   window.top.T = function T(){}
//   console.log(error)
// }
export default {
  name: "TiandiMap",
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
    height: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      map: null,
      mapConfig: {
        city: null,
        drawingModes: [
          BMAP_DRAWING_POLYGON, // 多边形
          BMAP_DRAWING_RECTANGLE,
          BMAP_DRAWING_CIRCLE,
        ],
        isShowSatellite: true,
        isShowDistance: true,
        isShowTraffic: true,
        isShowDrowing: false,
      },

      distanceControl: null, // 测距工具
      trafficControl: null, // 路况工具
      drawingControl: null, // 绘图工具
      geocControl: null, // 经纬度解析工具
    };
  },
  created() {
    let param = this.$props.config;
    this.mapConfig = Object.assign({}, this.mapConfig, param);
  },
  mounted() {
    this.$nextTick(() => {
      this._initMap(); // init map info
    });
  },
  methods: {
    _initMap() {
      let _this = this;
      this.map = new T.Map(this.$refs.mapNode, { projection: "EPSG:4326" });
      let point = new T.LngLat(110.93499, 21.663152);
      this.map.centerAndZoom(point, 13);
      this.map.enableScrollWheelZoom();

      // 设置默认的方法 》》》》》》》》》》
      T.Marker.prototype.setPosition = function (point) {
        this.setLngLat(point);
      };
      T.Marker.prototype.getPosition = function () {
        return this.getLngLat();
      };
      T.Marker.prototype.getLabel = function () {
        return this.markerLabel || null;
      };
      T.Marker.prototype.setLabel = function (label) {
        this.markerLabel = label;
        let point = this.getLngLat();
        if (point) {
          label.setLngLat(point);
        }
      };
      T.Label.prototype.setText = function (text) {
        if(text){
          // this.setOpacity(1);
          this.setLabel(text);
          if(!this.isShow){
            _this.map.addOverLay(this);
          }
        }else{
          this.setLabel('');
          if(this.isShow){
            _this.map.removeOverLay(this);
          }
        }
        return;
      };
      T.Label.prototype.setStyle = function (styleObj) {
        if (styleObj.zIndex != undefined) {
          this.setZindex(styleObj.zIndex);
        }
        if (styleObj.size != undefined) {
          this.setFontSize(styleObj.size);
        }
        if (styleObj.color != undefined) {
          this.setFontColor(styleObj.color);
        }
        if (styleObj.backgroundColor != undefined) {
          this.setBackgroundColor(styleObj.backgroundColor);
        }
        if (styleObj.borderWidth != undefined) {
          this.setBorderLine(styleObj.borderWidth);
        }
        if (styleObj.borderColor != undefined) {
          this.getBorderColor(styleObj.borderColor);
        }
        if (styleObj.opacity != undefined) {
          this.setOpacity(styleObj.opacity);
        }
        if (styleObj.display === "block") {
          _this.addOverlays(this);
        } else if (styleObj.display === "none") {
          _this.removeOverlays(this);
        }
      };
      // 设置默认的方法《《《《《《《《《《《《

      // let control = new T.Control.MapType();
      // control.setPosition(T_ANCHOR_BOTTOM_RIGHT);
      // this.map.addControl(control); //地图类型

      //加载地图控件
      this._setBoundary();
      //加载地图控件
      this._addMapControl();

      this.$emit("mapReadyCallback");
    },
    // 设置行政区域
    _setBoundary(tm) {
      if (!this.mapConfig.city) {
        return;
      }
      let _this = this;
      let map = this.map;
      function getBoundary() {
        var bdary = new T.AdministrativeDivision();
        var config = {
          needSubInfo: true,
          needAll: true,
          needPolygon: true,
          needPre: true,
          searchType: 1,
          searchWord: _this.mapConfig.city,
        };
        bdary.search(config, function (rs) {
          //获取行政区域
          //   _this.clearOverLays(); //清除地图覆盖物
          let data = rs.getData()[0];
          var count = data.points; //行政区域的点有多少个
          if (count && count.length > 0) {
            var pointsArr = [];
            for (var i = 0; i < count.length; i++) {
              var regionLngLats = [];
              var regionArr = count[i].region.split(",");
              for (var m = 0; m < regionArr.length; m++) {
                var lnglatArr = regionArr[m].split(" ");
                var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);
                regionLngLats.push(lnglat);
                pointsArr.push(lnglat);
              }
              //创建面对象
              var polygon = new T.Polygon(regionLngLats, {
                color: "#FF6919",
                weight: 3,
                opacity: 0.8,
                fillColor: "transparent",
                // fillOpacity: 0,
                lineStyle: "dashed",
                name: "disableMassClear",
              });
              //向地图上添加行政区划面
              map.addOverLay(polygon);
            }
            //显示最佳比例尺
            map.setViewport(pointsArr);
          }
        });
      }

      setTimeout(function () {
        getBoundary();
      }, tm || 1000);
    },
    _addMapControl() {
      let _this = this;
      // 添加地图类型控件
      // this.map.addControl(
      //   new BMap.MapTypeControl({
      //     mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP],
      //   })
      // );
    },
    // 打开卫星图
    openSatellite() {
      this.map.setMapType(TMAP_HYBRID_MAP);
    },
    // 关闭卫星图
    closeSatellite() {
      this.map.setMapType(TMAP_NORMAL_MAP);
    },
    // 打开测距
    openDistance() {
      if (!this.distanceControl) {
        let _this = this;
        this.distanceControl = new T.PolylineTool(this.map, {
          showLabel: true,
        });
        this.distanceControl.addEventListener("draw", function () {
          _this.$parent.$data.controllConfig.isActiveDistance = false; // 测距功能关闭
        });
      }
      this.distanceControl.open();
    },
    // 关闭测距
    closeDistance() {
      this.distanceControl && this.distanceControl.close();
    },
    // 打开路况
    openTraffic() {
      if (!this.trafficControl) {
        // 添加路况控件
      }
    },
    // 关闭路况
    closeTraffic() {
      this.trafficControl && this.trafficControl.hideTraffic();
    },
    // 打开绘图功能
    openDrawing() {
      if (!this.drawingControl) {
      }
    },
    // 地址反解析
    geocoder(point, cb) {
      if (!this.geocControl) {
      }
    },
    //获取地图缩放级别
    getCurrentMapZoom() {
      return this.map.getZoom();
    },
    // 设置zoom
    setCenter(pt, zoom) {
      if (zoom) this.map.setZoom(zoom);
      if (pt) this.map.panTo(pt);
    },
    // 设置zoom
    setZoom(val) {
      this.map.setZoom(val);
    },
    setViewport(pointArray) {
      if (!pointArray) return;
      this.map.setViewport(pointArray);
    },
    addOverlays(overlays, name) {
      if (!this.map) {
        return;
      }
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach((overlay) => {
          _this.map.addOverLay(overlay);
          if (name) {
            overlay.name = name;
          }
        });
      } else {
        this.map.addOverLay(overlays);
        if (name) {
          overlays.name = name;
        }
      }
    },
    removeOverlays(overlays) {
      if (!this.map) {
        return;
      }
      if (arguments.length === 0) return;
      let _this = this;
      if (Object.prototype.toString.call(overlays) === "[object Array]") {
        overlays.forEach((overlay) => {
          overlay.removeEventListener('click');
          _this.map.removeOverLay(overlay);
        });
      } else {
        this.map.removeOverLay(overlays);
      }
    },
    removeOverlaysByName(name) {
      if (!this.map) {
        return;
      }
      var allOverlay = this.map.getOverlays();
      allOverlay.map((overlay) => {
        if (overlay.name === name) {
          overlay.removeEventListener('click')
          this.map.removeOverLay(overlay);
        }
      });
    },
    removeAllOverlays() {
      if (!this.map) {
        return;
      }
      var allOverlay = this.map.getOverlays();
      allOverlay.map((overlay) => {
        overlay.removeEventListener('click')
        this.map.removeOverLay(overlay);
      });
      this.map.clearOverLays();
    },
    // 获取围栏中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return [x, y];
    },
    createLabel(msg, offsetX, offsetY, lng, lat) {
      let setting = {};
      if (msg != undefined) {
        setting.text = msg;
      }
      if (offsetX != undefined && offsetY != undefined) {
        // setting.offset = new T.Point(offsetX, offsetY);
        setting.offset = new T.Point(5,0);
      }
      if (lng != undefined && lat != undefined) {
        setting.position = new T.LngLat(lng, lat);
      }
      return new T.Label(setting);
      // let label = new T.Label(setting);
      // // this.map.addOverLay(label);
      // return label;
    },
    // 创建点
    createPoint(lng, lat) {
      return new T.LngLat(lng, lat);
    },
    createMarker(lng, lat, icon) {
      let _this = this;
      //创建标注对象
      let marker = new T.Marker(this.createPoint(lng, lat));
      marker.setIcon(icon);
      return marker;
    },
    createCarMarker(lng, lat, icon) {
      let _this = this;
      let point = icon.getIconSize();
      let options = {
        display: true,
        iconUrl: icon.options.iconUrl,
      };
      if (point) {
        options.width = point.x;
        options.height = point.y;
      }
      //创建标注对象
      let marker = new T.carMarker(this.createPoint(lng, lat), options);
      return marker;
    },
    createIcon(url, wSize, hSize) {
      return new T.Icon({
        iconUrl: url,
        iconSize: new T.Point(wSize, hSize),
        iconAnchor:new T.Point(wSize/2, hSize)
      });
    },
    // styleObj包含的属性:color,weight,opacity,lineStyle,fillColor,fillOpacity
    createPolygon(pointArr, styleObj) {
      let style = Object.assign({}, styleObj);
      return new T.Polygon(pointArr, style);
    },
    // styleObj包含的属性:color,weight,opacity,lineStyle
    createPolyline(pointArr, styleObj) {
      let style = Object.assign({}, styleObj);
      return new T.Polyline(pointArr, style);
    },
    getPath(overlay) {
      if (!overlay) return;
      if (overlay.getLngLats) {
        return overlay.getLngLats();
      } else {
        throw "对不起，该覆盖物在天地图上没有getLngLats方法！";
      }
    },
    // 创建弹窗
    createInfoWindow(lng, lat, _dom) {
      if (!this.map) {
        return;
      }
      if (this.infoWin) {
        this.closeInfoWindow();
      }
      let infoWin = (this.infoWin = new T.InfoWindow(_dom,{
        minWidth:300
      }));
      infoWin.setLngLat(new T.LngLat(lng, lat));
      // infoWin.setContent(_dom);
      this.map.addOverLay(infoWin);
      return this.infoWin;
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoWin) {
        this.infoWin.closeInfoWindow();
        this.infoWin = null;
      }
    },
    // 创建弹窗
    createInfoBox(lng, lat, _dom) {
      this.createInfoWindow(lng, lat, _dom);
    },
  },
  destroyed() {
    this.map = null;
  },
};
</script>

<style lang="scss" scoped>
</style>