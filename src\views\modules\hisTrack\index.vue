<template>
  <div v-loading="loading">
    <!--    地图-->
    <bmap :config="mapconfig"
          @mapReadyCallback="mapReadyCallback"
          :wheelList="wheelList"
          ref="bmapNode"></bmap>
    <!-- 可视化围栏一张图 -->
    <!-- <div style="position:fixed;top:10px;right:10px;z-index:10;" @mouseover="isShowVisualFence = true" @mouseout="isShowVisualFence = false;">
      <div style="padding:0 10px;box-shadow: 1px 1px 2px 1px #cbcbcb;background-color: rgba(0,37,82,.9);line-height:35px;text-align:center;color:#fff;font-size:13px;cursor:pointer;border-radius:5px;">
        <svg-icon icon-class="eye-on"></svg-icon> 围栏可视化配置 <svg-icon icon-class="arrow-bottom" class-name="shezhi-svg"></svg-icon>
      </div>
      <visual-fence ref="visualFence" v-model="visualFenceList" v-show="isShowVisualFence" style="position:absolute;top:100%;width:195px;right:0;left:auto;"></visual-fence>
    </div> -->
    <!--    时间轴-->
    <timeline ref="timeline"
              v-if="!height"></timeline>
    <div style="position:absolute;left:10px;top:10px;z-index:10;width:230px;">
      <monitor-searchbar ref="monitorSearchbar"
                         title="车辆轨迹"
                         :selectVecInfo="selectVecInfo"
                         :showSearchDate="true"
                         :vecListPage="vecListPage"
                         @searchVecNo="searchVecNoHandle"
                         @selectVec="selectVecHandle"
                         :defaultStyle="{
          left: '10px',
        }"></monitor-searchbar>
      <div style="border-top:1px solid #fff;">
        <div class="flex-panel">
          <div class="flex-panel-header">
            <span>{{selectVecInfo.vehicleNo}} 转向记录</span>
            <i class="header-icon"
               :class="isWheel?'el-icon-arrow-down':'el-icon-arrow-up'"
               @click="isWheel=!isWheel"></i>
          </div>
          <collapse-transition>
            <div class="flex-panel-body"
                 v-show="!isWheel"
                 style="max-height:calc(100vh - 186px);overflow-y:auto;padding:10px 8px 0 10px;">
              <el-input size="mini"
                        style="margin-bottom:10px"
                        placeholder="有效距离 例：100(米)"
                        v-model="wheelInfo.d"></el-input>
              <el-input size="mini"
                        style="margin-bottom:10px"
                        placeholder="速度 例：30(KM/H)"
                        v-model="wheelInfo.speed"></el-input>
              <el-input size="mini"
                        style="margin-bottom:10px"
                        placeholder="转向角度1 例：60(度)"
                        v-model="wheelInfo.r1"></el-input>
              <el-input size="mini"
                        style="margin-bottom:10px"
                        placeholder="转向角度2 例：80(度)"
                        v-model="wheelInfo.r2"></el-input>
              <el-button size="mini"
                         @click="getWheelList">查询转向记录</el-button>
            </div>
          </collapse-transition>
        </div>
      </div>
      <div style="border-top:1px solid #fff;">
        <div class="flex-panel">
          <div class="flex-panel-header">
            <span>{{selectVecInfo.vehicleNo}} 途径记录</span>
            <i class="header-icon"
               :class="isCollapse?'el-icon-arrow-down':'el-icon-arrow-up'"
               @click="isCollapse=!isCollapse"></i>
          </div>
          <collapse-transition>
            <div class="flex-panel-body"
                 v-show="!isCollapse"
                 style="max-height:calc(100vh - 386px);overflow-y:auto;padding:10px 0 0 10px;">
              <!-- 途径记录 -->
              <passingPath ref="passingPath" />
            </div>
          </collapse-transition>
        </div>
      </div>
    </div>
    <!--    电子运单弹框-->
    <rteplan-dialog ref="rteplanDialog"
                    :date-range="[selectVecInfo.searchDate,selectVecInfo.searchDate]"
                    :vec-no="selectVecInfo.vehicleNo"
                    :data-source="rteplanList"></rteplan-dialog>
    <div class="hidden"
         style="font-size:12px;">
      <table v-if="selectCameraInfo"
             ref="cameraDom">
        <tbody>
          <tr>
            <th width="70">卡口名称：</th>
            <td colspan="3">
              {{ selectCameraInfo.routeName || "无" }}
            </td>
          </tr>
          <tr v-if="!selectCameraInfo || !selectCameraInfo.pics || selectCameraInfo.pics.length == 0">
            <th width="70">抓拍图片：</th>
            <td colspan="3">无</td>
          </tr>
          <tr v-else>
            <th width="70">抓拍图片：</th>
            <td colspan="2">
              <div ref="imagesWrapper"
                   class="images-wrapper clearfix">
                <template v-for="(item, index) in selectCameraInfo.pics">
                  <div class="images-wrapper-item"
                       :key="index">
                    <div class="item-content">
                      <div>
                        <span><img @click="previewHandle('imagesWrapper')"
                               :src="item.vehiclepicurl"
                               :alt="'车牌号：'+item.plateNo+'，抓拍时间：'+item.passTime" /></span>
                      </div>
                    </div>
                    <div class="item-desc">
                      <div>{{item.plateNo}}</div>
                      <div>
                        {{
                          item.passTime
                            ? item.passTime.split(" ")[1]
                            : item.passTime.slice(-8)
                        }}
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script>
import * as Tool from "@/utils/tool";
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";

import bmap from "./BMap";
import MonitorSearchbar from "./monitor-searchbar";
import passingPath from "./passingPath";
// import visualFence from "@/views/mapMonit/components/visualFence";
import timeline from "./timeline.vue";
import collapseTransition from "@/components/CollapseTransition";
import rteplanDialog from "./rteplan-dialog";

import * as $http from "@/api/mapMonit";
import * as $httpVideo from "@/api/video";
import * as $httpAlarm from "@/api/violationAlarm";
import { getRtePlanListByVec } from "@/api/rtePlan";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import { transformFromBaiduToWGS } from "@/utils/coordinateTransform";
// import testJSON from "./test.json";
export default {
  name: "hisTrack",
  components: {
    bmap,
    MonitorSearchbar,
    passingPath,
    // visualFence,
    timeline,
    collapseTransition,
    rteplanDialog
  },
  data () {
    return {
      loading: false,
      mapconfig: {
        mapType: "baidu", //true: 天地图，false：百度图
        city: "浙江省镇海区",
        // mapHeight: "100vh",
        mapHeight: this.height ? this.height + "px" : "100vh",
        isSwitchMapType: false,
        toolsStyle: {
          width: "100px"
        }
      },

      selectVecInfo: {
        vehicleNo: "", // 车牌号
        searchDate: "" // 查询时间
      },
      wheelInfo: {
        v: '',
        t: '',
        d: null,
        speed: null,
        r1: null,
        r2: null
      },
      vecListPage: {
        result: [],
        pageNo: 0,
        pageSize: 10,
        totalPage: 0
      },

      selectCameraInfo: null,

      timer: null,
      vecMarker: null,
      isWheel: false,
      isCollapse: false,
      travelLoading: false,
      travels: [],
      wheelList: [],
      isShowVisualFence: false,
      visualFenceList: [],
      rteplanList: []
    };
  },
  props: {
    height: {
      type: Number
    }
  },
  created () {
    let _this = this;
    let query = this.$route.query;
    if (query.v) {
      this.selectVecInfo.vehicleNo = query.v;
      this.wheelInfo.v = query.v
    }
    if (query.t) {
      this.selectVecInfo.searchDate = query.t;
      this.wheelInfo.t = query.t
    } else {
      this.selectVecInfo.searchDate = Tool.formatDate(new Date(), "yyyy-MM-dd");
    }
  },
  provide () {
    return {
      getMap: this.getMap
    };
  },
  mounted () {
    this.$nextTick(() => {
      let vehicleNo = this.selectVecInfo.vehicleNo;
      let searchDate = this.selectVecInfo.searchDate;
      this.initSearchBar(vehicleNo, searchDate); // init searchBar
      // 获取车辆途经点
      this.getTravelOfVec(vehicleNo, searchDate);
      // 获取电子运单
      this.getRteplan(vehicleNo, searchDate);
    });
  },
  methods: {
    getMap () {
      return this.$refs.bmapNode;
    },
    mapReadyCallback () {
      this.$refs.timeline && this.$refs.timeline.reset();
      this.$refs.bmapNode.removeAllOverlays();
      let vehicleNo = this.selectVecInfo.vehicleNo;
      let searchDate = this.selectVecInfo.searchDate;
      // 获取历史轨迹
      this.getVecGpsTrace(vehicleNo, searchDate);
      // 获取途径卡口信息
      this.getVecCameras(vehicleNo, searchDate);
      // 获取车辆gps信息
      this.getVecGps(vehicleNo);
      //获取停车点
      this.getStop(vehicleNo, searchDate);
      // 显示围栏信息
      // this.$refs.visualFence && this.$refs.visualFence.showVisualFence();
    },
    // init monitor searchbar on the left
    initSearchBar (vehicleNo, searchDate) {
      this.searchVecNoList(vehicleNo, searchDate);
    },
    getWheelList () {
      $http.getVecGpsTurnByDate(this.wheelInfo).then(res => {
        if (res.code === 0) {
          this.wheelList = res.data
          this.$refs.bmapNode.getWheelList(this.wheelList)
        }
      })
    },
    // search vec number list for the left bar component
    searchVecNoList (vecNo, searchDate, pageNo, pageSize) {
      let param = {
        vehicleNo: vecNo || "", // vecNo
        time: searchDate, // 日期
        pageNo: pageNo || 1,
        pageSize: pageSize || 10
      };
      $http.getVecListOfGpsTraceByPage(param).then(res => {
        if (res.code == 0) {
          this.vecListPage = res.page;
        } else {
          this.vecListPage = {
            result: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          this.$message.error(res.msg);
        }
        this.$refs.monitorSearchbar.$data.loading = false;
      });
    },

    // function for left bar component
    searchVecNoHandle (vecNo, searchDate, pageNo, pageSize) {
      // this.$refs.bmapNode.removeOverlaysByName('timeline');
      this.$refs.timeline.hide();
      // this.selectVecInfo.vehicleNo = vecNo;
      // this.selectVecInfo.searchDate = searchDate;
      this.$nextTick(() => {
        this.searchVecNoList(vecNo, searchDate, pageNo, pageSize);
      });
    },

    // select vec monitor line
    selectVecHandle (vecNo, searchDate) {
      // this.$refs.bmapNode.removeOverlaysByName('timeline');
      this.$refs.timeline.hide();
      this.removeCameras();
      this.selectVecInfo.vehicleNo = vecNo;
      this.wheelInfo.v = vecNo;
      this.selectVecInfo.searchDate = searchDate;
      this.wheelInfo.t = searchDate;
      this.$nextTick(() => {
        this.getVecGpsTrace(vecNo, searchDate);
        this.getVecCameras(vecNo, searchDate);
        this.getStop(vecNo, searchDate);
        this.getTravelOfVec(vecNo, searchDate); // 车辆途径记录
        this.getRteplan(vecNo, searchDate); // 获取车辆电子运单信息
      });
    },

    // 获取车辆的历史轨迹
    getVecGpsTrace (vecNo, searchDate) {
      let _this = this;
      this.loading = true;
      $http
        .getVecGpsTraceByDate({
          t: searchDate,
          v: vecNo
        })
        .then(res => {
          // res.data = testJSON;
          if (res.code === 0) {
            if (res.data.length == 0) {
              _this.$message.warning(`${vecNo}，${searchDate}日无轨迹信息！`)
            } else {
              _this.$refs.timeline.showTrace(vecNo, searchDate, res.data); // 绘制车辆历史轨迹
            }
          } else {
            _this.$message.error(res.msg);
          }
          _this.loading = false;
        })
        .catch(error => {
          console.log(error);
          _this.loading = false;
        });
    },
    getVecGps (vecNo) {
      clearTimeout(this.timer);
      let _this = this;
      $http
        .findGpsByVecNo(vecNo)
        .then(res => {
          if (res && res.code === 0 && res.data) {
            let mapNode = _this.$refs.bmapNode;
            _this.vecMarker && mapNode.removeOverlays(_this.vecMarker);
            _this.vecMarker = null;
            let data = res.data;
            let runStatus = data.speed > 0 ? "run" : "stop";
            let icon = (icon = mapNode.createIcon(
              imgsConfig.vec[`${runStatus}_${data.carType}`],
              24,
              24
            ));
            let vecMarker = mapNode.createMarker(data, icon);
            _this.vecMarker = vecMarker;
            // vecMarker.setRotation(data.direction)
            // marker.addEventListener("click", function () {
            //     _this.openInfoWindow(data, this);
            // });

            mapNode.addOverlays(vecMarker);
            _this.startFreshGps(vecNo);
          }
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    startFreshGps (vec) {
      clearTimeout(this.timer);
      let fresh = () => {
        clearTimeout(this.timer);
        this.interv--;
        if (this.interv == 0) {
          this.getVecGps(vec);
          this.interv = 15;
        }
        this.timer = setTimeout(fresh, 1000);
      };
      this.timer = setTimeout(fresh, 50);
    },
    //获取车辆历史轨迹中的卡口摄像头
    getVecCameras (vecNo, searchDate) {
      let _this = this;
      this.removeCameras();
      $httpVideo
        .getPassByDeviceOfVec(
          searchDate + " 00:00:00",
          searchDate + " 23:59:59",
          vecNo
        )
        .then(res => {
          if (res.code == 0) {
            _this.addCameras(res.list, vecNo, searchDate);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 添加卡口
    addCameras (data, vecNo, searchDate) {
      let _this = this;
      if (!data || !data.length) return false;
      let map = this.getMap();
      let kakouList = data.map(it => {
        return Object.assign({}, it, {
          lonBd: it.bdLong,
          latBd: it.bdLat,
          longitude: it.longitude,
          latitude: it.latitude
        });
      });
      kakouList.forEach(it => {
        let marker;
        marker = map.createMarker(
          it,
          map.createIcon(imgsConfig.vec.bayonet, 24, 24)
        );
        marker.addEventListener("click", function () {
          _this.$set(_this, "selectCameraInfo", it);
          // _this.selectCameraInfo = it;
          // let points = market.getPosition();
          _this.openCameraInfoWindow(marker, it, vecNo, searchDate);
        });
        map.addOverlays(marker, "camera");
      });
    },
    //卡口详情
    openCameraInfoWindow (marker, data, plateNo, searchDate) {
      let _this = this;
      let map = this.getMap();
      map.setCenter(marker.getPosition());
      // 获取图片
      $httpVideo
        .getDevicePics({
          plateNo: plateNo,
          dvsNum: data.number,
          startDate: searchDate + " 00:00:00",
          endDate: searchDate + " 23:59:59"
        })
        .then(res => {
          if (res.code == 0) {
            _this.$set(_this.selectCameraInfo, "pics", res.page.list);
            // _this.selectCameraInfo.pics = res.page.list
            _this.createCameraInfoWindow(data);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    createCameraInfoWindow (data) {
      let map = this.getMap();
      this.$nextTick(() => {
        let _dom = this.$refs.cameraDom || "";
        map.createInfoBox(data, _dom);
      });
    },
    removeCameras () {
      let _this = this;
      let map = this.getMap();
      map.removeOverlaysByName("camera");
    },
    previewHandle (wrapperName) {
      var viewer = new Viewer(this.$refs[wrapperName], {
        zIndex: 9999,
        url (image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        // ready() {
        //   viewer.viewer.className += " custom-lic-viewer-container";
        // },
        // viewed() {
        //   let viewCanvas = viewer.viewer.getElementsByClassName(
        //     "viewer-canvas"
        //   );
        //   if (viewCanvas.length > 0) {
        //     let imgTags = viewCanvas[0].getElementsByTagName("img");
        //     if (imgTags.length > 0) {
        //       imgTags[0].style.marginLeft =
        //         parseFloat(imgTags[0].style.marginLeft) - 200 + "px";
        //     }
        //   }
        // },
        title: (image, imageData) => `${image.alt}`,
        hidden () {
          viewer.destroy();
        }
      });
    },
    //获取停车点
    getStop (vecNo, searchDate) {
      let _this = this;
      let mapNode = _this.$refs.bmapNode;
      let param = {
        page: 1,
        limit: 1000,
        filters: {
          groupOp: "AND",
          rules: [
            { field: "cat_cd", op: "eq", data: "2550.170.275" },
            { field: "tractor_no", op: "eq", data: vecNo },
            {
              field: "alarm_time",
              op: "bt",
              data: [searchDate + " 00:00:00", searchDate + " 23:59:59"]
            }
          ]
        }
      };
      $httpAlarm
        .getViolationAlarmPerList(param)
        .then(res => {
          let data = res.page.list;
          if (res.code === 0 && data && data.length > 0) {
            data.reverse().forEach(item => {
              if (!item.alarmLongitude || !item.alarmLatitude) {
                return;
              }
              let tiandiGps = transformFromBaiduToWGS(
                item.alarmLongitude,
                item.alarmLatitude
              );
              let gps = {
                lonBd: item.alarmLongitude,
                latBd: item.alarmLatitude,
                longitude: tiandiGps.longitude,
                latitude: tiandiGps.latitude
              };
              _this.stopMarker = mapNode.createMarker(
                gps,
                mapNode.createIcon("static/img/monitor-img/stop.png", 30, 40)
              );
              mapNode.addOverlays(_this.stopMarker);
              // let _dom =
              //   "报警时间：" +
              //   item.alarmTime +
              //   "</br>停车时长：" +
              //   item.alarmValue +
              //   "分钟";
              let _dom = item.descr;
              _this.stopMarker.addEventListener("click", function () {
                mapNode.createInfoWindow(gps, _dom); //开启信息窗口
              });
            });
          }
        })
        .catch(error => { });
    },
    // 获取车辆途经点信息
    getTravelOfVec (vehicleNo, searchDate) {
      this.$refs.passingPath.open(vehicleNo, searchDate);
      // this.travels = [];
      // if (!vehicleNo || !searchDate) {
      //   return;
      // }
      // this.travelLoading = true;
      // $http
      //   .getVecTrack({ v: vehicleNo, start: searchDate })
      //   .then(res => {
      //     if (res.code === 0) {
      //       this.travels = res.data;
      //       this.travelLoading = false;
      //     }
      //   })
      //   .catch(error => {
      //     console.log(error);
      //     this.travelLoading = false;
      //   });
    },

    // 获取电子运单
    getRteplan (vehicleNo, searchDate) {
      let _this = this;
      let param = {
        filters: {
          groupOp: "AND",
          rules: [
            { field: "trac_cd", op: "cn", data: vehicleNo },
            {
              field: "vec_desp_tm",
              op: "bt",
              data: [searchDate + " 00:00:00", searchDate + " 23:59:59"]
            }
          ]
        },
        page: 1,
        limit: 999
      };
      let map = this.getMap();
      $http
        .getRtePlanListByVec(param)
        .then(response => {
          if (response.code == 0) {
            _this.rteplanList = response.list || [];
          } else {
            _this.rteplanList = [];
          }
          this.$refs.rteplanDialog.show();
          _this.rteplanList.forEach(item => {
            if (item.csnorPoint) {
              // 装货地
              let csnorPoint = map.createMarker(
                item.csnorPoint,
                // map.createIcon(imgsConfig.vec.load, 30, 40)
                map.createIcon(imgsConfig.vec.loadOrNot, 30, 40)
              );
              map.addOverlays(csnorPoint);
            }
            if (item.csneePoint) {
              // 卸货地
              let csneePoint = map.createMarker(
                item.csneePoint,
                // map.createIcon(imgsConfig.vec.unload, 30, 40)
                map.createIcon(imgsConfig.vec.loadOrNot, 30, 40)
              );
              map.addOverlays(csneePoint);
            }
          });
        })
        .catch(error => {
          _this.rteplanList = [];
          console.log(error);
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.flex-panel {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #fff;

  &-header {
    flex: 0 0 40px;
    box-sizing: border-box;
    height: 40px;
    line-height: 40px;
    background-color: rgba(28, 91, 250, 0.7);
    box-shadow: 0 2px 2px #aaa;
    color: #fff;
    box-sizing: border-box;
    // border-radius: 5px 5px 0 0;
    font-size: 14px;
    text-align: center;

    .header-icon {
      float: right;
      line-height: 32px;
      cursor: pointer;
      margin-right: 8px;
    }
  }
  &-body {
    flex: 1 1 auto;
    // overflow-y: auto;
    // display: flex;
    // flex-direction: column;
  }
}
.images-wrapper {
  width: 100%;
  // max-height: 300px;
  overflow-y: auto;
  margin: 0 5px 5px 0;

  .images-wrapper-item {
    float: left;
    margin: 5px;
    text-align: center;
    border: 1px solid #d7d2d2;
    .item-content {
      width: 90px;
      height: 50px;
      overflow: hidden;
      > div {
        display: table;
        width: 100%;
        height: 100%;
        > span {
          vertical-align: middle;
          text-align: center;
          display: table-cell;
          width: 50px;
          img {
            width: 100%;
            cursor: pointer;
          }
        }
      }
    }
    .item-desc {
      > div {
        background: #f5f5f5;
        padding: 3px 0;
      }
    }
  }
}
</style>
