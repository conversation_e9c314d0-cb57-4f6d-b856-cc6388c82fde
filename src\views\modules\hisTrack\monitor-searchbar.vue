<template>
  <div class="float-panel" :style="defaultStyle" ref="monitorSearchBar">
    <div class="float-panel-header" :style="{'border-radius':collapseIcon=='el-icon-arrow-up'?'5px 5px 0 0':'5px'}">
      <span>{{title}}</span>
      <i class="header-icon" :class="collapseIcon" @click="collapseHandle"></i>
    </div>
    <collapse-transition>
      <div class="float-panel-body" v-show="collapseIcon=='el-icon-arrow-up'">
        <div class="track-search">
          <el-date-picker v-model="searchDate" @change="searchDateChange" value-format="yyyy-MM-dd" type="date" placeholder="查询日期" size="mini" style="width:100%;margin-bottom:8px;"></el-date-picker>
          <el-select v-model="vehicleNo" @change="searchDateChange" :remote-method="searchHandle" :loading="loading" class="searchInputMonitor" style="width:100%;" placeholder="车牌号" size="mini" filterable remote reserve-keyword clearable>
            <el-option v-for="(item) in vecListPage.result" :key="item._id" :label="item._id" :value="item._id">
            </el-option>
          </el-select>
        </div>
      </div>
    </collapse-transition>
  </div>
</template>

<script>
import collapseTransition from "@/components/CollapseTransition";
export default {
  name: "MonitorSearchbar",
  props: {
    title: {
      type: String,
      default: ""
    },

    // default style
    defaultStyle: {
      type: Object,
      default: function() {
        return {
          position: "absolute",
          top: "10px",
          left: "10px",
          // height:'500px',
          width: "200px",
          zIndex: 40
        };
      }
    },
    // vec data
    vecListPage: {
      type: Object,
      default: function() {
        return {
          result: [],
          pageNo: 0,
          pageSize: 10,
          totalPage: 0
        };
      }
    },

    /* 以下属性用于车辆轨迹页面 */
    selectVecInfo: {
      type: Object,
      default: function() {
        return {
          searchDate: null,
          vehicleNo: null
        };
      }
    }
    // showSearchDate: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {
      collapseIcon: "el-icon-arrow-up",
      vehicleNo: "",
      searchDate: null,
      loading: null
    };
  },
  components: {
    collapseTransition
  },
  created() {
    if (this.selectVecInfo.vehicleNo) {
      this.vehicleNo = this.selectVecInfo.vehicleNo;
    }
    if (this.selectVecInfo.searchDate) {
      this.searchDate = this.selectVecInfo.searchDate;
    }
  },
  watch: {
    selectVecInfo: {
      handler(newVal, oldVal) {
        if (newVal.vehicleNo) {
          this.vehicleNo = newVal.vehicleNo;
        }
        if (newVal.searchDate) {
          this.searchDate = newVal.searchDate;
        }
      },
      deep: true
    },
    // vehicleNo() {
    //   if (this.vehicleNo) {
    //     const d = this.vecListPage.result.find(
    //       item => item._id === this.vehicleNo
    //     );
    //     this.selectVec(d);
    //   }
    // }
  },
  methods: {
    // collapse panel effect event
    collapseHandle() {
      if (this.collapseIcon == "el-icon-arrow-down") {
        this.collapseIcon = "el-icon-arrow-up";
      } else {
        this.collapseIcon = "el-icon-arrow-down";
      }
    },

    // search vec list event
    searchHandle(query) {
      if (query.length >= 3) {
        this.loading = true;
        this.$emit(
          "searchVecNo",
          query,
          this.searchDate,
          1,
          30
        );
      }
    },

    searchDateChange(){
      if(this.vehicleNo && this.searchDate){
        this.$emit("selectVec", this.vehicleNo, this.searchDate);
      }
    },
    // selectVec() {
    //   this.$emit("selectVec", this.vehicleNo, this.searchDate);
    // }
  }
};
</script>

<style scoped>
.float-panel .float-panel-header {
  position: relative;
  height: 42px;
  line-height: 32px;
  background-color: rgba(28, 91, 250, 0.7);
  box-shadow: 0 2px 2px #aaa;
  color: #fff;
  box-sizing: border-box;
  padding: 5px 8px;
  border-radius: 5px 5px 0 0;
  font-size: 14px;
  text-align: center;
}
.float-panel .float-panel-header .header-icon {
  float: right;
  line-height: 32px;
  cursor: pointer;
}

.float-panel .float-panel-body {
  background: #fff;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
}
.track-search {
  padding: 10px 8px;
}
.track-content {
  padding: 0;
  height: 15vh;
  overflow-y: auto;
}
.track-footer {
  box-sizing: border-box;
  box-sizing: border-box;
  line-height: 30px;
  margin-top: 10px;
  font-size: 12px;
  color: #333;
  padding-right: 10px;
  text-align: right;
  padding-bottom: 10px;
}
.page-no {
  margin-right: 10px;
}
.prev-page,
.next-page {
  display: inline-block;
  border: 1px solid #eee;
  width: 30px;
  height: 25px;
  line-height: 25px;
  margin: 0;
  padding: 0;
  text-align: center;
  cursor: pointer;
  color: #2c81ff;
}
.prev-page:hover,
.next-page:hover {
  background-color: #f5f5f5;
}
.prev-page.disabled,
.next-page.disabled {
  color: #cecece;
  cursor: no-drop;
}
.vec-list-ul {
  /* color: #0e800c; */
  padding: 0;
  margin: 0;
}
.vec-list-ul > li {
  box-sizing: border-box;
  position: relative;
  height: 35px;
  line-height: 34px;
  border-bottom: 1px dashed #eee;
  font-size: 12px;
  cursor: pointer;
  padding: 0 10px;
}
.vec-list-ul > li:hover {
  background-color: #f4f4f4;
}
.vec-list-ul .vec-no {
  float: left;
}
.vec-list-ul .vec-time {
  float: right;
}
</style>
