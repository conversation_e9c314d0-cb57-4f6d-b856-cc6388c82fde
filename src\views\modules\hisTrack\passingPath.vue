<template>
  <div>
    <span v-if="timeList.length===0">无途记录</span>
    <el-timeline>
      <el-timeline-item v-for="(item, index) in timeList" :key="index" :timestamp="item.time" placement="top">
        <p>{{ item.action }} {{ item.position }}</p>
        <el-image
          style="width: 150px"
          fit="contain"
          :src="item.desc"
          :preview-src-list="[item.desc]"
          v-if="(item.desc || '').includes('http')"
        >
        </el-image>
        <p v-else>{{ item.desc }}</p>
      </el-timeline-item>
    </el-timeline>
    <div style="height: 20px;"></div>
  </div>
</template>

<script>
import * as $http from "@/api/mapMonit";
export default {
  data() {
    return {
      dialogVisible: false,
      timeList: []
    }
  },
  methods: {
    /**
     * 打开途经点信息
     * v:车牌号
     * d:日期
     */
    open(vecNo,time) {
      this.getSchedule(vecNo,time)
    },
    async getSchedule(vecNo,time) {
      const res = await $http.getSchedule({
        vecNo:vecNo,
        time:time
      })
      // const res = await $http.getSchedule('浙B7E105' || vecNo)
      if (res.code === 0) {
      this.dialogVisible = true
        // console.log('[ res.data ]', res.data)
        this.timeList = res.data
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.description {
}
</style>
