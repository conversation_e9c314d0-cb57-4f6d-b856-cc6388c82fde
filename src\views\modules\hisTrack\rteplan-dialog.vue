<template>
  <div class="passable-route-dialog monit-follow" v-show="isShow" ref="dialog">
    <div class="dialog-body" :class="{ 'is-fold': isFold }">
      <el-tabs v-show="dateRangeArr.length && Object.keys(formarDataSource).length" v-model="activeName" @tab-click="tabsClick">
        <el-tab-pane :label="
            formarDataSource[it] ? it + '（' + formarDataSource[it].length + '条电子运单）' : it
          " :name="index + 1 + ''" v-for="(it, index) in dateRangeArr" :key="index" class="tabs-panel-content">
          <template v-if="formarDataSource[it]">
            <div class="box-card" v-for="(item, jindex) in formarDataSource[it]" :key="jindex">
              <el-row :gutter="10">
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <label>运单号：</label>
                  <el-button type="text" style="padding: 0" @click="showBills(item.argmtPk)">{{
                    item.cd
                  }}</el-button>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24">
                  <label>承运商：</label>{{ item.carrierNm }}
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>货物：</label>
                  <template v-if="item.goodsInfoJson">
                    <div v-for="(subitem,index) in JSON.parse(item.goodsInfoJson)" :key="index">
                      <a href="javascript:void(0)" @click="showChemicaDetail(subitem.prodPk)">
                        {{ `${subitem.goodsNm ? "（" + subitem.goodsNm + "），" : "（空），"}` }}
                        {{ `${subitem.prodCategory ? +subitem.prodCategory + "类，" : "未分类，"}` }}
                        {{ `${subitem.prodPackKind ? "PG " + subitem.prodPackKind + "，" : ""}` }}
                        {{ `${subitem.packType ? subitem.packType + "，" : ""}` }}
                        {{ `${subitem.loadQty}吨` }}
                      </a>
                    </div>
                  </template>
                  <a v-else href="javascript:void(0)" @click="showChemicaDetail(item.prodPk)">
                    <!-- {{ item.goodsNm }} -->
                    {{ `${item.dangGoodsNm ?  '（'+ item.dangGoodsNm +'），':'（空），'}`}}
                    {{ `${item.prodCategory ?  + item.prodCategory +'类，':'未分类，'}`}}
                    {{ `${item.prodPackKind ? ('PG '+item.prodPackKind+'，') : ''}`}}
                    {{ `${item.packType ? ( item.packType+'，') : ''}`}}
                    {{ `${item.loadQty}吨`}}
                  </a>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>实装：</label>{{ item.goodsGw }}
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>牵引车：</label>
                  <a href="javascript:void(0)" @click="showVecDetail(item.tracPk)">{{
                    item.tracCd
                  }}</a>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>挂车号：</label>
                  <a href="javascript:void(0)" @click="showVecDetail(item.traiPk)">{{
                    item.traiCd
                  }}</a>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>驾驶员：</label>
                  <a href="javascript:void(0)" @click="showPerDetail(item.dvPk)">{{ item.dvNm }}&nbsp;/&nbsp;{{ item.dvMob }}</a>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>押运员：</label>
                  <a href="javascript:void(0)" @click="showPerDetail(item.scPk)">{{ item.scNm }}&nbsp;/&nbsp;{{ item.scMob }}</a>
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>起运地：</label>{{ item.csnorWhseLoc }} {{ item.csnorWhseAddr }}
                </el-col>
                <el-col :xs="24" :sm="24" :md="12" :lg="12">
                  <label>目的地：</label>{{ item.csneeWhseLoc }} {{ item.csneeWhseAddr }}
                </el-col>
              </el-row>
            </div>
            <div v-if="formarDataSource[it].length === 0" style="color: #fff; text-align: center; padding: 15px; padding-top: 0">
              无电子运单数据
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="dialog-footer">
      <div class="oper-bar left">
        <span @click="hide">
          <svg-icon icon-class="close" class-name="svg-icon"></svg-icon>
        </span>
      </div>
      <span class="title"> {{ vecNo }} 电子运单信息 </span>
      <div class="oper-bar right">
        <span @click="toggleOpenAndFold" :class="{ 'is-fold': isFold }">
          <svg-icon icon-class="arrow-bottom" class-name="svg-icon"></svg-icon>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/ench";
export default {
  props: {
    dataSource: {
      // 电子运单数据
      type: Array,
      required: true
    },
    dateRange: {
      // 电子运单时间列表
      type: Array,
      required: true
    },
    vecNo: {
      // 电子运单车牌号
      type: String,
      default() {
        return "";
      }
    }
  },
  watch: {
    dateRange: {
      handler(val, oldVal) {
        if (!this.dateRange || this.dateRange.length != 2) {
          this.dateRangeArr = [];
          return;
        }
        let oneday = 24 * 60 * 60 * 1000;
        let startDate = this.dateRange[0];
        let endDate = this.dateRange[1];
        let formatStartDate = startDate.slice(0, 10);
        let formatEndDate = endDate.slice(0, 10);

        let days =
          (new Date(formatEndDate).getTime() -
            new Date(formatStartDate).getTime()) /
          oneday;
        let dateArr = [formatStartDate];
        for (let i = 1, len = days; i <= len; i++) {
          dateArr.push(
            Tool.formatDate(
              new Date(formatStartDate).getTime() + oneday * i,
              "yyyy-MM-dd"
            )
          );
        }
        this.dateRangeArr = dateArr;
      },
      deep: true,
      immediate: true
    },
    dataSource: {
      handler(val, oldVal) {
        let res = {};
        this.dateRangeArr.forEach(it => {
          res[it] = val.filter(item => {
            return item.vecDespTm && item.vecDespTm === it;
          });
        });
        this.$set(this, "formarDataSource", res);
        // this.formarDataSource = res;
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      isShow: false,
      // isDrop: false,
      // _clientX: 0,
      // _clientY: 0,
      activeName: "1",
      isFold: false,
      dateRangeArr: [],
      formarDataSource: {}
    };
  },
  methods: {
    show() {
      this.activeName = "1";
      this.isShow = true;
      this.isFold = true;
    },
    hide() {
      this.isShow = false;
      this.reset();
    },
    reset() {
      this.activeName = "1";
      this.isFold = false;
    },
    toggleOpenAndFold() {
      this.isFold = !this.isFold;
    },
    // moveDialog() {
    //   let _this = this;
    //   let dialog = this.$refs.dialog;
    //   document.onmousemove = function(event) {
    //     if (_this.isDrop) {
    //       let e = e || window.event;
    //       let moveX = e.clientX - _this._clientX;
    //       let moveY = e.clientY - _this._clientY;

    //       let maxX = document.documentElement.clientWidth - dialog.offsetWidth;
    //       let maxY =
    //         document.documentElement.clientHeight - dialog.offsetHeight;

    //       moveX = Math.min(maxX, Math.max(0, moveX));

    //       moveY = Math.min(maxY, Math.max(0, moveY));
    //       dialog.style.left = moveX + "px";
    //       dialog.style.top = moveY + "px";
    //     }

    //     return false;
    //   };
    // },
    // mouseupHandle() {
    //   let _this = this;
    //   document.onmouseup = function() {
    //     _this.isDrop = false;
    //   };
    // },
    // mouseDownHandle(event) {
    //   let dialog = this.$refs.dialog;
    //   let e = e || window.event;
    //   this._clientX = e.clientX - dialog.offsetLeft;
    //   this._clientY = e.clientY - dialog.offsetTop;

    //   this.isDrop = true;

    //   this.moveDialog();
    //   this.mouseupHandle();
    // },
    tabsClick(tab, event) {
      // console.log(this.dataSource[tab.name - 1]);
      // this.$emit("tabClick", this.dataSource[tab.name - 1]);
      let rteplan = this.formarDataSource[tab.name - 1] || [];
      if (rteplan.length > 0) {
        console.log(this.vecNo, rteplan[0].vecDespTm);
        this.$emit("tabClick", this.vecNo, rteplan[0].vecDespTm);
      } else {
        console.log(this.vecNo, tab.label);
        this.$emit("tabClick", this.vecNo, tab.label.slice(0, 10));
      }
    },
    // 单据
    showBills(argmtPk) {
      // this.$router.push({
      //   path: "/base/rteplan/bills/" + argmtPk
      // });
      window.open(
        location.origin + location.pathname + "#/base/rteplan/bills/" + argmtPk
      );
    },
    showVecDetail(pk) {
      window.open(
        location.origin + location.pathname + "#/base/vec/info/" + pk
      );
    },
    showPerDetail(pk) {
      window.open(
        location.origin + location.pathname + "#/base/pers/info/" + pk
      );
    },
    showChemicaDetail(pk) {
      window.open(
        location.origin + location.pathname + "#/base/chemica/info/" + pk
      );
      // $http.getChemByProdPk(pk).then(response => {
      //   if (response && response.code === 0) {
      //     // _this.licData = response.data.items;
      //     const d = response.chem.chemNewEntity;
      //     if (!d.id) {
      //       return;
      //     }
      //     window.open(location.origin + location.pathname + "#/base/chemica/info/" + d.id);
      //   }
      // });
    }
  }
};
</script>

<style lang="scss" scoped>
.passable-route-dialog {
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  width: 100%;
  // height: 300px;
  margin: 0 auto;
  z-index: 999;
  border-radius: 3px;
  color: #333;
  font-size: 14px;
  // display: flex;
  // flex-direction: column;

  &::-webkit-scrollbar-track-piece {
    background-color: #0a284a;
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #0a284a;
    border-radius: 20px;
  }

  // .dialog-head {
  //   flex: 0 0 36px;
  //   cursor: move;
  //   padding: 4px 6px;
  //   background-color: #2c6dce;
  //   overflow: hidden;

  //   .closeBtn {
  //     cursor: pointer;
  //     font-size: 16px;
  //     color: #fff;
  //   }
  // }
  .dialog-body {
    flex: 1 1 auto;
    overflow-y: auto;
    // height: 200px;
    background: #12305a;
    transition: 2s all ease;
    padding: 0 5px;

    &.is-fold {
      margin-top: -100%;
    }

    .box-card {
      line-height: 24px;
      font-size: 13px;
      color: #ccc;
      padding: 0 15px 15px;

      label {
        font-size: 12px;
        color: #fff;
      }
    }
    .tabs-panel-content {
      max-height: calc(100vh - 90px);
      overflow-y: auto;
    }
  }
  .dialog-footer {
    flex: 0 0 40px;
    position: absolute;
    overflow: hidden;
    bottom: 0;
    left: 50%;
    height: 40px;
    line-height: 40px;
    width: 300px;
    -webkit-transform: translateX(-50%) translateY(40px);
    transform: translateX(-50%) translateY(40px);
    background: #112a4e;
    border-radius: 0 0 20px 20px;
    text-align: center;
    .title {
      position: relative;
      color: #fff;
      font-size: 18px;
    }
    .oper-bar {
      position: absolute;
      line-height: 40px;
      top: 0;
      color: #ccc;
      font-size: 18px;
      .svg-icon {
        cursor: pointer;
        transform: rotate(180deg);
        transition: 1s all ease;
        // transition-delay: 1s;
        &:hover {
          color: #fff;
        }
      }

      .is-fold {
        .svg-icon {
          transform: rotate(0);
        }
      }

      &.left {
        margin-left: 10px;
        left: 0;
      }

      &.right {
        margin-right: 10px;
        right: 0;
      }
    }
  }
}
</style>

<style lang="scss">
.monit-follow {
  .el-button--text,
  a {
    color: #fff;
    text-decoration: underline;
    &:hover {
      color: #aaa;
    }
  }
  .el-tabs__item.is-active {
    color: #fff;
  }
}
</style>
