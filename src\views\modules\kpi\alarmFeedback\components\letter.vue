<template>
  <div class="file" v-if="file">
    <div class="subtitle">宁波市镇海区道路运输安全稽查大队</div>
    <div class="title">危险货物道路运输违规行为处理通知书</div>
    <div class="title-tips">甬镇稽 【{{file.year}}】{{file.num}}号</div>
    <div class="content">
      <div>{{file.entpName}}：</div>
      <div class="paragraph">你单位车辆在宁波市镇海区从事危险化学品运输作业，{{file.date}}被监测发现以下违规行为：</div>
      <div class="paragraph">
        {{file.content}}
      </div>
      <div class="paragraph">
        请你单位在处理通知书送达5个工作日内，<span v-if="file.content && file.content.indexOf('无卫星定位')>=0">立即联系卫星定位运营商，将车辆卫星定位数据对接至镇海区道路运输监管系统</span><span v-else>按照公司内部规章制度对相关司押人员进行安全培训和绩效处罚</span>，并将处理结果以照片形式上传至镇海区危险化学品道路运输监管系统物流企业端。逾期未回复，将影响该车辆通行证办理。
      </div>
      <div class="paragraph">
        稽查大队指挥中心：0574—86620110
      </div>
      <div style="position:relative;height:200px;padding: 30px 0;">
        <img src="static/img/signet.png" width="200" class="signet" />
        <div class="align-right" style="margin-top:30px;">
          （宁波市镇海区道路运输安全稽查大队盖章）
        </div>
        <div class="align-right">
          {{file.date}}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    file: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
.file {
  width: 80%;
  margin: 0 auto;
  font-size: 16px;
  line-height: 38px;
  text-align: left;
  padding: 25px;
  border: 1px solid #ebe3e3;
  .title {
    text-align: center;
    color: #d00;
    font-size: 25px;
    line-height: 58px;
    font-weight: bolder;
  }
  .subtitle {
    text-align: center;
    color: #d00;
    font-size: 20px;
    line-height: 25px;
  }
  .title-tips {
    color: #000;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    font-weight: bold;
  }
  .content {
    border-top: 4px solid #d00;
    padding-top: 10px;
    margin-top: 10px;
    .paragraph {
      text-indent: 2em;
    }
  }
  // .has-signet {
  //   // background: url("/static/img/signet.png") center right;
  //   background: url("~@/assets/other-imgs/signet.png") center right no-repeat;
  // }
  .signet {
    position: absolute;
    right: 120px;
    top: 0;
    transform: rotate(-70deg);
  }
}
</style>