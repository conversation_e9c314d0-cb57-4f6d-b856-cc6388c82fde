<template>
  <div class="app-main-content">
    <searchbar ref="searchbar"
    :searchItems="searchItems"
    :pagination="pagination"
    @resizeSearchbar="resizeSearchbar"
    @search="getList"></searchbar>
    <!-- <div class="grid-search-bar clearfix">
      <el-row>
        <el-col :span="24" class="toolbar text-right" style="padding-bottom: 0px;">
          <el-form ref="queryForm" :model="queryForm" :inline="true" label-width="80px" size="mini">
            <el-form-item prop="tracCd">
              <el-input clearable v-model="queryForm.tracCd" placeholder="请输入牵引车牌号" style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="carrierNm">
              <el-input clearable v-model="queryForm.carrierNm" placeholder="请输入企业名称" style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="startTm">
              <el-date-picker clearable v-model="queryForm.startTm" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd" placeholder="选择开始日期" style="width:150px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTm">
              <el-date-picker clearable v-model="queryForm.endTm" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd" placeholder="选择结束日期" style="width:150px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="oprNm">
              <el-input clearable v-model="queryForm.oprNm" placeholder="请输入操作员" style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="isHandle">
              <el-select v-model="queryForm.isHandle" placeholder="请选择处理结果" style="width:150px;">
                <el-option v-for="item in dealTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="catCd">
              <el-select v-model="queryForm.catCd" clearable>
                <el-option label="所有违章处置" value=""></el-option>
                <el-option label="异常停车处置" value="2550.170.275"></el-option>
                <el-option label="疲劳驾驶处置" value="2550.7120.180"></el-option>
                <el-option label="偏离路线处置" value="2550.170.170"></el-option>
                <el-option label="超速报警处置" value="2550.160.150"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="default" @click="resetQueryForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div> -->
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :max-height="tableHeight">
      <el-table-column label="#" type="index"></el-table-column>
      <!-- <el-table-column prop="catNmCn" label="违章类别"></el-table-column>
      <el-table-column prop="tracCd" label="牵引车号"></el-table-column> -->
      <el-table-column prop="entpName" label="企业名称" align="center"></el-table-column>
      <!-- <el-table-column prop="sendUrl" label="发函url"></el-table-column> -->
      <el-table-column prop="sendTm" label="发函时间" align="center"></el-table-column>
      <el-table-column prop="sendContent" label="发函内容" align="center">
        <template slot-scope="scope">
          <div>
            <el-button v-if="scope.row.sendContent" type="text" size="small" @click="showSendContent(scope.row.sendContent)" title="查看详情" style="margin-right: 10px">查看发函内容</el-button>
            <filePreview v-if="scope.row.sendUrl" :files="scope.row.sendUrl" :imageShow="false">
              <template slot="showName">查看发函内容</template>
            </filePreview>
          </div>
        </template>
      </el-table-column>
    <!--  <el-table-column prop="replyUrl" label="回函内容"  >
        <template slot-scope="scope">
          <filePreview :files="scope.row.replyUrl" :imageShow="false">
            <template slot="showName">回函内容</template>
          </filePreview>
        </template>
      </el-table-column>-->
<!--      <el-table-column prop="replyTm" label="回函时间" align="center"></el-table-column>-->
    <!--  <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.status===0">待回函</span>
          <span v-if="scope.row.status===9">未回函（已超过72小时）</span>
          <span v-else-if="scope.row.status===1">已回函未处理</span>
          <span v-else-if="scope.row.status===2">已驳回</span>
          <span v-else-if="scope.row.status===3">已通过</span>
          <span v-else-if="scope.row.status===4">已撤回</span>
        </template>
      </el-table-column>-->
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status===1 || scope.row.status===2" type="text" size="small" @click="dealHandle(scope.row.id,scope.row.replyUrl,scope.row.entpRemark,scope.row.sendContent)" title="查看详情">处理</el-button>
          <el-button type="text" size="small" @click="recall(scope.row.id)" title="查看详情">撤回</el-button>
          <el-button type="text" size="small" @click="del(scope.row.id,scope.row)" title="删除">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]" :total="pagination.total" :current-page="pagination.page" style="float:right;">
      </el-pagination>
    </el-col>

    <el-dialog :visible.sync="sendDcVisible" title="发函内容" width="80%">
      <letter :file="letterContent"></letter>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="default" size="small" @click="sendDcVisible = false">关闭</el-button>
      </div> -->
    </el-dialog>
    <!-- 弹窗, 详情 -->
    <!-- <info v-if="infoVisible" ref="info"></info> -->
    <el-dialog :visible.sync="visible" title="回函处置" width="80%">
      <letter :file="letterContent"></letter>
      <!-- <div style="width:80%;margin:15px auto;text-align:left;">
        <strong>企业回函内容：</strong>
        <template v-if="info.replyUrl">
          <upload-images ref="uploadImagesNode" :data-source="info.replyUrl" operType="read" />
        </template>
        <template v-else>无</template>
      </div>
      <div style="width:80%;margin:15px auto;text-align:left;">
        <strong>企业备注：</strong>
        <template v-if="info.entpRemark">
          {{info.entpRemark}}
        </template>
        <template v-else>无</template>
      </div> -->
      <div style="width:80%;margin:0 auto;">
        <el-form size="small" label-width="95px" :model="dealForm" ref="dealForm" @submit.native.prevent>
          <el-row class="detail-container">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="企业回函：">
                <template v-if="info.replyUrl">
                  <!-- <filePreview :files="replyUrl" :imageShow=true></filePreview> -->
                  <upload-images ref="uploadImagesNode" :data-source="info.replyUrl" operType="read" />
                </template>
                <template v-else>无</template>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="企业备注：">
                <template v-if="info.entpRemark">
                  {{info.entpRemark}}
                </template>
                <template v-else>无</template>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="hidden">
              <el-form-item label="id" prop="id">
                <el-input clearable v-model="dealForm.id"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="处置操作：" prop="status" :rules="$rulesFilter({required:true})">
                <!-- <el-select v-model="dealForm.isHandle" placeholder="请选择">
                  <el-option v-for="item in dealTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-radio-group v-model="dealForm.status" class="custom-radio-group">
                  <el-radio :label="2">驳回</el-radio>
                  <el-radio :label="3">通过</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="详情描述：" prop="remark">
                <el-input clearable v-model="dealForm.remark" type="textarea" rows="5"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="align-right">
              <el-button type="default" size="small" @click="visible = false">取消</el-button>
              <el-button type="primary" size="small" @click="formSubmit">提交</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import filePreview from "@/components/FilesPreview";
import UploadImages from "@/components/UploadImages";
import letter from "./components/letter";
import * as $http from "@/api/alarmFeedback";
import * as Tool from "@/utils/tool";
import Cookies from 'js-cookie';
import info from "./info";
export default {
  name: "alarmFeedback",
  components: {
    Searchbar,
    filePreview,
    UploadImages,
    letter
  },
  data() {
    return {
      queryForm: {
        startTm: "",
        endTm: "",
        carrierNm: "",
        tracCd: "",
        catCd: ""
      },

      tableHeight: Tool.getClientHeight() - 210,
      list: [],
      listLoading: false,
      addLoading: false,
      dataForm: {},
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },

      // infoVisible: false,
      // editVisible: false
      visible: false,
      dealForm: {
        id: null,
        status: null,
        remark: null
      },
      // letterContent: {
      //   date: "2020年09月14日",
      //   entpName: "靖江中源运输有限公司",
      //   scNm: null,
      //   dvNm: null,
      //   content: "疲劳驾驶。牵引车号苏MF6340"
      // },
      letterContent: null,
      info: {
        replyUrl: null,
        entpRemark: ""
      },

      sendDcVisible: false
    };
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    this.getAlarmTypelist();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  computed: {
    searchItems() {
      let rolesName = localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
      if (rolesName.indexOf("gov_dljc") >= 0) {
        return {
          normal: [
           /* {
              name: "报警类型",
              field: "catCd",
              type: "radio",
              options: [
                { label: "全部违章", value: "" },
                //{ label: "超速报警", value: "2550.160.150" },
                // { label: "超速预警", value: "2550.170.150" },
                //{ label: "偏离路线预警", value: "2550.170.170" },
                //{ label: "超载", value: "2550.160.180" },
                //{ label: "疲劳驾驶", value: "2550.7120.180" },
                // { label: "超经营范围报警", value: "2550.160.185" },
                // { label: '无电子运单报警', value: '2550.7120.190' },
                //{ label: "异常停车预警", value: "2550.170.275" },
                //{ label: "无GPS", value: "2550.7120.160" },
                // { label: 'GPS离线', value: '2550.7120.170' },
                //{ label: "未备案报警", value: "2550.7120.155" },
                //{ label: "GPS轨迹缺失报警", value: "2550.7120.165" },
                //{ label: "临江互通闯禁报警", value: "2550.7120.200" },
              ],
              dbfield: "cat_cd",
              dboper: "eq",
              default: "2550.160.150",
            },*/
            // {
          //   name: "牵引车号",
          //   field: "tracCd",
          //   type: "text",
          //   dbfield: "trac_cd",
          //   dboper: "cn"
          // },
          {
            name: "运输企业",
            field: "entpName",
            type: "text",
            dbfield: "entp_name",
            dboper: "cn"
          },
          /*{
            name: "状态",
            field: "status",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待回函", value: "0" },
              { label: "未回函（已超过72小时）", value: "9" },
              { label: "已回函未处理", value: "1" },
              { label: "已撤回", value: "4" },
              { label: "已驳回", value: "2" },
              { label: "已通过", value: "3" }
            ],
            dbfield: "status",
            dboper: "eq"
          }*/
          ],
          more: [],
        };
      }
      return {
        normal: [
          /*{
            name: "报警类型",
            field: "catCd",
            type: "radio",
            options: [
              { label: "全部违章", value: "" },
              //{ label: "超速报警", value: "2550.160.150" },
              // { label: "超速预警", value: "2550.170.150" },
              //{ label: "偏离路线预警", value: "2550.170.170" },
              //{ label: "超载", value: "2550.160.180" },
              //{ label: "疲劳驾驶", value: "2550.7120.180" },
              //{ label: "超经营范围报警", value: "2550.160.185" },
              // { label: '无电子运单报警', value: '2550.7120.190' },
              //{ label: "异常停车预警", value: "2550.170.275" },
              //{ label: "无GPS", value: "2550.7120.160" },
              // { label: 'GPS离线', value: '2550.7120.170' },
              //{ label: "未备案报警", value: "2550.7120.155" },
              //{ label: "GPS轨迹缺失报警", value: "2550.7120.165" },
              //{ label: "临江互通闯禁报警", value: "2550.7120.200" },
            ],
            dbfield: "cat_cd",
            dboper: "eq",
            default: "2550.160.150",
          },*/
          // {
          //   name: "牵引车号",
          //   field: "tracCd",
          //   type: "text",
          //   dbfield: "trac_cd",
          //   dboper: "cn"
          // },
          {
            name: "运输企业",
            field: "entpName",
            type: "text",
            dbfield: "entp_name",
            dboper: "cn"
          },
          /*{
            name: "状态",
            field: "status",
            type: "radio",
            options: [
              { label: "全部", value: "" },
              { label: "待回函", value: "0" },
              { label: "未回函（已超过72小时）", value: "9" },
              { label: "已回函未处理", value: "1" },
              { label: "已驳回", value: "2" },
              { label: "已通过", value: "3" }
            ],
            dbfield: "status",
            dboper: "eq"
          }*/
        ],
        more: [],
      };
    },
  },
  methods: {
    //获取报警类型
    getAlarmTypelist(){
      let _this = this
      $http
      .getAlarmType()
      .then((response) => {
        let list = response.data;
        if (response.code == 0 && list) {
          let alarmList = list.map((item) => {
            return { label: item.nmCn, value: item.cd };
          });
          //判断角色是否为稽查大队gov_dljc，否则删除"超经营范围报警"
          let rolesName = localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
          if (!rolesName || rolesName.indexOf("gov_dljc") == -1){
            alarmList.forEach((item,index) => {
              if (item.label.indexOf("超经营范围")>= 0){
                alarmList.splice(index,1)
              }
            });
          }

          _this.searchItems.normal[0].options = _this.searchItems.normal[0].options.concat(
            alarmList
          );
          _this.$refs.searchbar.init();
        }
      })
      .catch((error) => {
        console.log(error);
      });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 撤回回函
    recall(id){
     this.listLoading = true;
       $http.noticeRecall(id)
        .then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "撤回成功!"
              });
           }
          this.getList();
          this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.listLoading = false;
        });
    },
    // 删除回函
    del(id,row){
      this.$confirm(`确定删除${row.entpName}的违章处置记录吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.listLoading = true;
        $http.noticeDelete(id).then(res => {
          if (res.code == 0) {
            this.$message({
              type: "success",
              message: "删除成功!"
            });
            this.getList();
           }else {
            this.$message.error(msg);
          }
          this.listLoading = false;
        }).catch(error => {
          console.log(error);
          this.listLoading = false;
        });;
      });
    },
    // 获取数据
    getList: function(data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;
      this.listLoading = true;
      $http
        .list(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function() {
      this.pagination.page = 1;
      this.getList();
    },
    // 详情
    // infoHandle(id) {
    //   this.infoVisible = true;
    //   this.$nextTick(() => {
    //     this.$refs.info.init(id);
    //   });
    // },
    // 新增 / 修改
    resetQueryForm() {
      this.$refs["queryForm"].resetFields();
    },
    showSendContent(content) {
      this.letterContent = JSON.parse(content);
      this.sendDcVisible = true;
    },
    dealHandle(id, replyUrl, entpRemark, content) {
      this.visible = true;
      this.info.replyUrl = replyUrl || null;
      this.info.entpRemark = entpRemark || null;
      // content =
      //   '{"date":"2020年09月14日","entpName":"靖江中源运输有限公司","scNm":null,"dvNm":null,"content":"疲劳驾驶。牵引车号苏MF6340"}';
      this.letterContent = content ? JSON.parse(content) : null;
      this.$nextTick(() => {
        this.$refs.dealForm.resetFields();
        this.dealForm.id = id;
      });
    },
    formSubmit() {
      this.$refs.dealForm.validate(valid => {
        if (valid) {
          this.loading = true;
          let postData = this.dealForm;
          $http
            .deal(postData)
            .then(res => {
              this.loading = false;
              if (res.code == 0) {
                this.$message({
                  type: "success",
                  message: "提交成功!"
                });
                this.refreshGrid();
                this.visible = false;
              } else {
                this.$message.info(res.msg || "服务器错误，请联系管理员");
              }
            })
            .catch(err => {
              this.loading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.file {
  font-size: 16px;
  line-height: 38px;
  text-align: left;
  padding: 15px;
  border: 1px solid #ebe3e3;
  .title {
    text-align: center;
    color: #d00;
    font-size: 25px;
    line-height: 58px;
    font-weight: bolder;
  }
  .subtitle {
    text-align: center;
    color: #d00;
    font-size: 20px;
    line-height: 25px;
  }

  .content {
    border-top: 4px solid #d00;
    padding-top: 10px;
    margin-top: 10px;
    .paragraph {
      text-indent: 2em;
    }
  }
  .has-signet {
    background: url("/static/img/signet.png") center right;
  }
}
</style>
