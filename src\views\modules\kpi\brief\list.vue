<!--违章处置简报-->
<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar" @search="getList">
      <el-button slot="button" icon="el-icon-plus" type="primary" size="small" @click="add">新增
      </el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable" style="width: 100%;" :max-height="tableHeight">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column prop="date" label="日期" width="100"></el-table-column>
      <el-table-column prop="briefUrl" label="内容">
        <template slot-scope="scope">
          <filePreview :files="scope.row.briefUrl">
            <template slot="showName">
              <span>{{scope.row.briefNm}}</span>
            </template>
          </filePreview>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="upd(scope.row)">编辑</el-button>
          <el-button type="text" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange" @size-change="handleSizeChange" :page-size="pagination.limit" :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>
    <!-- 新增对话框 -->
    <el-dialog :visible.sync="visible">
      <el-form style="margin-top:20px;" ref="dataForm" label-width="120px" :model="dataForm" size="small">
        <el-form-item label="日期:" prop="date" :rules="$rulesFilter({ required: true })">
          <el-date-picker v-model="dataForm.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="简报名称:" prop="briefNm" :rules="$rulesFilter({ required: true })">
          <el-input type="text" v-model="dataForm.briefNm"></el-input>
        </el-form-item>
        <el-form-item label="内容:" prop="briefUrl" :rules="$rulesFilter({ required: true })">
          <file-upload v-model="dataForm.briefUrl" :acceptFileType="['pdf','doc','docx']" :multiple="true"></file-upload>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button type="primary" size="small" @click="submit">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import fileUpload from "@/components/FileUpload";
import filePreview from "@/components/FilesPreview";
import * as $http from "@/api/brief";
import * as Tool from "@/utils/tool";
export default {
  name: "briefList",
  components: {
    Searchbar,
    fileUpload,
    filePreview
  },
  data: function() {
    return {
      todayStamp: "", //当前日期时间戳
      tableHeight: 500,
      listLoading: false,
      list: [],
      dataForm: {},
      searchItems: {
        normal: [
          {
            name: "日期",
            field: "date",
            type: "daterange",
            dbfield: "date",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      visible: false
    };
  },
  mounted: function() {
    // const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    // this.$refs.searchbar.init();
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    getList: function(data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;
      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    //查看详细内容
    openUrl(url) {
      if (!url) return;
      window.open(url, "_blank");
    },
    add() {
      this.dataForm = {
        id: "",
        date: Tool.formatDate(new Date(), "yyyy-MM-dd"),
        briefUrl: ""
      };
      this.visible = true;
    },
    //提交
    submit() {
      let _this = this;
      // console.log('form',this.dataForm)
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          $http[this.dataForm.id ? "upd" : "add"](this.dataForm).then(res => {
            if (res && res.code == 0) {
              this.$message({
                message: "提交成功",
                type: "success",
                duration: 2000,
                onClose: () => {
                  _this.visible = false;
                  _this.getList();
                }
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    upd(data) {
      this.visible = true;
      this.dataForm = Object.assign({}, data);
    },
    // 删除
    del(id) {
      let _this = this;
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http.del(id).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
                duration: 2000,
                onClose: () => {
                  _this.getList();
                }
              });
            }
          });
        })
        .catch(() => {});
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    }
  }
};
</script>

<style scoped>
</style>
