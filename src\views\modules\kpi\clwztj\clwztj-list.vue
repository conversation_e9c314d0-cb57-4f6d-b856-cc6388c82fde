<template>
   <div class="app-main-content">
        <div class="grid-search-bar clearfix">
            <el-row>
                <el-col :span="4" class="toolbar" style="padding-bottom: 0px;">
                    <span class="el-icon-date">月内车辆违章汇总表</span>
                </el-col>
                <el-col :span="20" class="toolbar text-right" style="padding-bottom: 0px;">
                   <el-form ref="form" :model="queryForm" :inline="true" label-width="80px" size="mini">
                    
                        <el-form-item >
                            <el-date-picker
                                v-model="queryForm.date"
                                type="month"
                                @change="getList()"
                                value-format="yyyy-MM"
                                placeholder="选择月份">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item >
                                <el-input v-model="queryForm.vecNo" placeholder="请输入车牌号"></el-input>
                        </el-form-item>
                        <el-form-item >
                                <el-input v-model="queryForm.entpName" placeholder="请输入企业名称"></el-input>
                        </el-form-item>
                        <el-form-item >
                            <el-button type="primary" @click="getList">查询</el-button>
                            <el-button type="success" @click="handleDownload">导出</el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </div>
        <!--列表-->
        <el-table class="el-table" :data="list"  show-summary highlight-current-row v-loading="listLoading" style="width: 100%;" :max-height="tableHeight">
            <el-table-column  label="#" type="index">

            </el-table-column>
            <el-table-column
                    prop="vecNo"
                    label="车牌号">
        
            </el-table-column>
            <el-table-column
                    prop="entpName"
                    label="企业">
            </el-table-column>
            <el-table-column
                    prop="date"
                    label="统计月份">
            </el-table-column>
            <el-table-column
                    prop="overSpeedCnt"
                    label="超速">
            </el-table-column>
            <el-table-column
                    prop="overBusinessCnt"
                    label="超经营范围">
            </el-table-column>
            <el-table-column
                    prop="overLoadCnt"
                    label="超载">
            </el-table-column>
            <el-table-column
                    prop="stopCnt"
                    label="停车预警">
            </el-table-column>
            <el-table-column
                    prop="noRecordCnt"
                    label="未备案">
            </el-table-column>
            <el-table-column
                    prop="noRteplanCnt"
                    label="无电子运单">
            </el-table-column>
        </el-table>

        <!--工具条-->
        <el-col :span="24" class="toolbar">
            <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
                           @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200, 500]"
                           :total="pagination.total" :current-page="pagination.page" style="float:right;">
            </el-pagination>
        </el-col>
    </div>
</template>

<script>
import * as $http from "@/api/monthStat";
import * as Tool from "@/utils/tool";

export default {
  name: "PersList",
  data() {
    return {
      tableHeight: 500,
      list: [],

      listLoading: false,
      addLoading: false,

      queryForm: {
        vecNo: "",
        entpName: "",
        date: ""
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  mounted: function() {
    const _this = this;
    this.tableHeight = Tool.getTableHeight();
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight();
    });
  },
  methods: {
    //报表下载
    handleDownload(){
        let _this = this;
        let param = {};

        for(var filed in _this.queryForm){
            if(_this.queryForm[filed] !=="undefined" &&
              _this.queryForm[filed] !==null && 
              /\S/.test(_this.queryForm[filed])){
                var data = _this.queryForm[filed];
                var filed = filed.replace(/([A-Z])/g,'_$1').toLowerCase();
                param[filed] = data;
            }
        }
        $http.downloadVecExcel(param).then( response => {
            let a = document.createElement('a');
            let blob = new Blob([response]);
            let url = window.URL.createObjectURL(blob);

            a.href = url;
            a.download = '车辆违章汇总统计_'+this.queryForm.date+'.xlsx';
            a.click();
            window.URL.revokeObjectURL(url);
        })
        .catch( error =>{
            throw new Error(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign( {}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign( {}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      let filters = {"groupOp":"AND","rules":[]}
      param = param || {};
      delete param.total;

      for(var filed in _this.queryForm){
          if(_this.queryForm[filed] !=="undefined" &&
                 _this.queryForm[filed] !==null && 
                 /\S/.test(_this.queryForm[filed])){
                  var rule ={"field":filed.replace(/([A-Z])/g,'_$1').toLowerCase(),"op":"eq","data":_this.queryForm[filed]};
                  if(filed == "entpName"){
                      rule.op = "cn";
                  }
                filters.rules.push(rule);
          }
      }

      param.filters = filters;

      this.listLoading = true;
      $http.queryVecList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    }
  }
};
</script>

<style>
</style>
