<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix" ref="searchbar">
      <el-row>
        <el-col :span="4" class="toolbar" style="padding-bottom: 0px;">
          <span class="el-icon-date">运营商汇总表</span>
        </el-col>
        <el-col :span="20" class="toolbar text-right" style="padding-bottom: 0px;">
          <el-form ref="form" :model="queryForm" :inline="true" label-width="80px" size="mini">
            <el-form-item>
              <el-input v-model="queryForm.company" placeholder="请输入运营商名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="success" @click="handleDownload">导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!--列表-->
    <el-table show-summary :summary-method="getSummaries" class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :max-height="tableHeight">
      <el-table-column prop="company" label="运营商名称"></el-table-column>
      <el-table-column prop="onlinecar" label="在线车辆数"></el-table-column>
      <el-table-column prop="allcar" label="车辆总数"></el-table-column>
      <el-table-column prop="onlinerate" label="在线率"></el-table-column>
    </el-table>
    <!--工具条-->
  </div>
</template>

<script>
import * as $http from "@/api/monthStat";
import * as Tool from "@/utils/tool";

export default {
  name: "GPSList",
  data() {
    return {
      queryForm: {
        company: ""
      },
      tableHeight: 500,
      list: [],

      listLoading: false,
      addLoading: false,

      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  mounted: function() {
    // const _this = this;
    // this.tableHeight = Tool.getTableHeight();
    // window.addEventListener("resize", function() {
    //   _this.tableHeight = Tool.getTableHeight();
    // });
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() -
          50 -
          10 * 2 -
          20 * 2 -
          this.$refs.searchbar.offsetHeight -
          15;
      });
    },
    //统计报表导出
    handleDownload() {
      $http
        .downloadGpsExcel("")
        .then(response => {
          let a = document.createElement("a");
          let blob = new Blob([response]);
          let url = window.URL.createObjectURL(blob);

          a.href = url;
          a.download = "卫星定位质量汇总统计.xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      this.getList();
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      this.getList();
    },
    // 获取数据
    getList: function() {
      let _this = this;

      this.listLoading = true;
      $http
        .queryGpsList(this.queryForm)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.data.length;
            _this.list = response.data;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        }
      });
      sums[3] = ((sums[1] * 100) / sums[2]).toFixed(2) + "%";
      sums[1] = sums[1] + "辆";
      sums[2] = sums[2] + "辆";
      return sums;
    }
  }
};
</script>

<style>
</style>
