/* * @Author: z<PERSON><PERSON>f * @Date: 2022-08-Fr 03:10:04 * @Last Modified by: zhaoyf *
@Last Modified time: 2022-08-Fr 03:10:04 */

<template>
  <div class="app-main-content">
    <!-- 搜索 -->
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    >
      <el-button
        slot="button"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="add"
        >新增
      </el-button>
    </searchbar>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      border
      v-loading="listLoading"
      ref="singleTable"
      style="width: 100%"
      :max-height="tableHeight"
    >
      <!-- v-loading="listLoading" -->
      <el-table-column type="index" label="#" width="100"></el-table-column>
      <!-- todo：违章类别功能待确认 -->
      <!-- <el-table-column prop="catNmCn" label="违章类别"></el-table-column> -->
      <el-table-column prop="tracCd" label="牵引车号 "> </el-table-column>
      <el-table-column prop="carrierNm" label="运输企业"></el-table-column>
      <el-table-column prop="alarmTime" label="报警时间"></el-table-column>
      <el-table-column prop="oprNm" label="操作员"></el-table-column>
      <!-- <el-table-column prop="catCd" label="违章类别"></el-table-column> -->
      <el-table-column prop="isHandle" label="处置操作">
        <template slot-scope="scope">
          <template v-if="scope.row.isHandle <= 2">
            <span>已受理</span>
          </template>
          <span v-else>
            <span
              v-if="scope.row.isHandle === null || !scope.row.isHandle"
            ></span>
            <span
              v-else-if="scope.row.isHandle === 13 || scope.row.isHandle === 18"
              class="cicle fill red-edge"
            ></span>
            <span v-else class="cicle fill green-edge"></span>
            {{
              (scope.row.isHandle &&
                alarmDealActions[scope.row.isHandle] &&
                alarmDealActions[scope.row.isHandle].label) ||
                ""
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="detail(scope.row.id)">详情</el-button>
          <el-button type="text" @click="update(scope.row)">修改</el-button>
          <el-button type="text" @click="del(scope.row.id)">删除</el-button>
          <el-button type="text" @click="showOpLog(scope.row.oprDetail)"
            >操作日志</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right"
      >
      </el-pagination>
    </div>
    <!-- 新增对话框 -->
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="1200px"
    >
      <el-form
        class="ill-form"
        style="margin-top: 20px"
        ref="dataForm"
        label-width="120px"
        :model="dataForm"
        size="small"
      >
        <div class="flex-box-roll">
          <el-form-item
            label="运输企业:"
            prop="carrierNm"
            :rules="$rulesFilter({ required: true })"
          >
            <el-autocomplete
              v-model="dataForm.carrierNm"
              :fetch-suggestions="queryCarrierNmAsync"
              placeholder="请输入运输企业"
              @select="changeCarrierNm"
              @clear="handleClear"
              clearable
            ></el-autocomplete>
          </el-form-item>
          <el-form-item
            label="牵引车"
            prop="tracCd"
            :rules="$rulesFilter({ required: true, type: 'LPN' })"
          >
            <el-autocomplete
              v-model="dataForm.tracCd"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入牵引车牌号"
              @select="handleSelect"
              @clear="handleClear"
              clearable
            ></el-autocomplete>
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="企业联系人:" prop="erNm">
            <el-input
              disabled
              type="text"
              v-model="dataForm.erNm"
              placeholder="请输入企业联系人"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话:" prop="erMob">
            <el-input
              disabled
              type="text"
              v-model="dataForm.erMob"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="驾驶员姓名:" prop="dvNm">
            <el-input
              c
              type="text"
              v-model="dataForm.dvNm"
              placeholder="请输入驾驶员姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话:" prop="dvMob">
            <el-input
              type="text"
              v-model="dataForm.dvMob"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="押送员姓名:" prop="scNm">
            <el-input
              type="text"
              v-model="dataForm.scNm"
              placeholder="请输入押送员姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话:" prop="scMob">
            <el-input
              type="text"
              v-model="dataForm.scMob"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="货物:" prop="goodsNm">
            <el-input
              type="text"
              v-model="dataForm.goodsNm"
              placeholder="请输入货物名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="货物重量(吨):" prop="loadQty">
            <el-input
              type="text"
              v-model="dataForm.loadQty"
              placeholder="请输入货物重量"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="报警地点:" prop="alarmLocation">
            <el-input
              type="text"
              v-model="dataForm.alarmLocation"
              placeholder="请输入报警地点"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="报警时间:"
            prop="alarmTime"
            :rules="$rulesFilter({ required: true })"
          >
            <el-date-picker
              v-model="dataForm.alarmTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
            >
            </el-date-picker>
            <!-- <el-input type="text" v-model="dataForm.alarmTime" placeholder="请输入报警时间"></el-input> -->
          </el-form-item>
        </div>
        <div class="flex-box-roll">
          <el-form-item label="处置时间:" prop="foundTm">
            <el-date-picker
              v-model="dataForm.foundTm"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
            >
            </el-date-picker>
            <!-- <el-input type="text" v-model="dataForm.foundTm" placeholder="请输入处置时间"></el-input> -->
          </el-form-item>
          <!-- <el-form-item
            label="操作员:"
            prop="oprNm"
            :rules="$rulesFilter({ required: true })"
          >
            <el-select
              :disabled="isDisabled"
              filterable
              clearable
              v-model="dataForm.oprNm"
              placeholder="请选择操作员"
            >
              <el-option
                v-for="(item, index) in opratorList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="扣分类别:" prop="alarmLawId" :rules="$rulesFilter({ required: true })">
            <el-select
              filterable
              clearable
              v-model="dataForm.alarmLawId"
              placeholder="请选择扣分类别"
            >
              <el-option
                v-for="(item, index) in penaltyTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item
          label="处理结果:"
          prop="isHandle"
          :rules="$rulesFilter({ required: true })"
        >
          <el-radio-group v-model="dataForm.isHandle" size="medium">
            <el-radio
              :label="item.value"
              v-for="(item, index) in alarmDealActions"
              :key="index"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="详情描述:" prop="oprContent">
          <el-input
            placeholder="请输入详情描述"
            type="textarea"
            v-model="dataForm.oprContent"
            :rows="4"
          ></el-input>
        </el-form-item>
        <el-form-item label="图片:" prop="imgUrls">
          <file-upload
            v-model="dataForm.imgUrls"
            :acceptFileType="['png', 'gif', 'jpg', 'jpeg']"
            :multiple="true"
          ></file-upload>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          size="small"
          :loading="subLoading"
          @click="submit"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <!-- 操作日志详情 -->
    <el-dialog
      title="操作日志详情"
      append-to-body
      :visible.sync="opLogVisible"
      :close-on-click-modal="false"
      :destory-on-close="true"
      class="mod-loadBefCheck-edit"
      width="40%"
    >
      <div style="height: 500px; overflow-y: auto" ref="imgHandel">
        <el-steps direction="vertical" :active="1" style="padding: 0 20px 20px">
          <template v-for="(item, index) in opLogArr">
            <el-step
              :status="
                item.isHandle == 2
                  ? 'success'
                  : item.isHandle === null || !item.isHandle
                  ? 'process'
                  : item.isHandle === 13 || item.isHandle === 18
                  ? 'error'
                  : 'success'
              "
              :key="index"
              :title="item.time"
            >
              <div slot="description">
                <div>操作员: {{ item.oprNm }}</div>
                <div>操作时间: {{ item.time }}</div>
                <div class="handleDesc" v-if="item.handler_content">
                  操作内容：
                  <span
                    v-html="item.handler_content"
                    @click="showImage()"
                  ></span>
                </div>
                <div>扣分类别: {{ getPenaltyTypeName(item.alarmLawId) }}</div>
                <div>
                  处置操作:
                  {{
                    item.isHandle <= 2
                      ? "已受理"
                      : (item.isHandle &&
                          alarmDealActions[item.isHandle] &&
                          alarmDealActions[item.isHandle].label) ||
                        ""
                  }}
                </div>
                <div>详情描述: {{ item.oprContent }}</div>
                <div v-if="item.imgUrls && item.imgUrls.length">
                  上传图片:
                  <span style="vertical-align: top;">
                      <el-image class="ill-img" style="height: 120px;" v-for="(item, index) in item.imgUrls" :key="index" :src="item" :preview-src-list="[item]"></el-image>
                  </span>
                </div>
              </div>
            </el-step>
          </template>
        </el-steps>
      </div>
    </el-dialog>
    <!-- 详情弹窗 -->
    <info ref="info" v-if="infoVisible" :selectCarrList="selectCarrList" :penaltyTypeList="penaltyTypeList"></info>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Searchbar from "@/components/Searchbar";
import fileUpload from "./components/FileUpload";
import filePreview from "@/components/FilesPreview";
import * as $http from "@/api/illegalVec";
import * as Tool from "@/utils/tool";
import info from "./info.vue";
import alarmDealJson from "./data.json";
import { getFuzzyTracCd } from "@/api/vec";
export default {
  name: "briefList",
  components: {
    Searchbar,
    fileUpload,
    filePreview,
    info
  },
  provide() {
    return {
      // opratorList: [],
      // selectCarrList: [],
      // penaltyTypeList: this.penaltyTypeList,
    };
  },
  data: function() {
    return {
      opLogArr: [], //操作日志列表
      opLogVisible: false, //操作日志弹窗
      visible: false, //新增修改弹窗
      infoVisible: false, // 详情弹窗
      detailVisible: false, // 详情弹窗
      isDisabled: false, //修改禁用操作员
      title: "", // diglog title
      todayStamp: "", //当前日期时间戳
      tableHeight: 500,
      listLoading: false,
      subLoading: false, //新增/修改弹窗loading
      options: [],
      carrierNmOptions: [],
      list: [],
      tracList: [], //牵引车模糊搜索列表
      dataForm: {
        carrierNm: "",
        imgUrls: "",
        alarmLawId:'',
      },
      searchItems: {
        normal: [
          {
            name: "企业名称",
            field: "carrierNm",
            dbfield: "carrierNm",
            type: "text"
          },
          // {
          //   name: "牵引车牌号",
          //   field: "tracCd",
          //   dbfield: "tracCd",
          //   type: "text"
          // },
          {
            name: "牵引车",
            field: "tracCd",
            type: "fuzzy",
            dbfield: "trac_cd",
            dboper: "eq",
            api: this.getTracCd
          },
          // 取消违章类别功能
          // {
          //   name: "违章类别",
          //   field: "catCd",
          //   dbfield: "cat_cd",
          //   type: "select",
          //   options: []
          // },
          {
            name: "处理结果",
            field: "isHandle",
            dbfield: "is_handle",
            type: "select",
            options: []
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      opratorList: [], //操作员列表
      penaltyTypeList: [], //违章类型列表
      selectCarrList: [],
      disposeList: [], // 处理结果列表
      detailForm: {}, // 详情表单
      alarmTypesOptions: {}, // 报警类型
      alarmDealActions: alarmDealJson.alarmDealActions //处理结果
    };
  },
  computed: {
    ...mapGetters(["allAlarmDealOptions", "alarmDealOptions"])
  },
  mounted: function() {
    // const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    // this.$refs.searchbar.init();
    this.setTableHeight();
    this.getList();
    this.getOpList();
    this.getAlarmTypelist().then(res => {
      this.searchItems = this.initSearchData();
    });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 初始化搜索下拉数据
    initSearchData: function() {
      return {
        normal: [
          {
            name: "企业名称",
            field: "carrierNm",
            dbfield: "carrierNm",
            type: "text",
            dboper: "cn"
          },
          {
            name: "牵引车",
            field: "tracCd",
            type: "fuzzy",
            dbfield: "tracCd",
            dboper: "eq",
            api: this.getTracCd
          },
          // 取消违章类别功能
          // {
          //   name: "所有违章",
          //   field: "catCd",
          //   dbfield: "catCd",
          //   type: "select",
          //   options: this.alarmTypesOptions,
          //   dboper: "cn"
          // },
          {
            name: "处理结果",
            field: "isHandle",
            type: "select",
            dbfield: "isHandle",

            options: this.alarmDealActions,
            dboper: "cn"
          }
        ]
      };
    },
    //操作日志弹窗
    showOpLog(oprDetail) {
      if (oprDetail) {
        let arr = JSON.parse(oprDetail);
        arr.forEach(item => {
          if (item.imgUrls) {
            item.imgUrls = item.imgUrls.split(",");
          }
        })
        this.opLogArr = arr;
        this.opLogVisible = true;
      } else {
        this.$message({
          message: "无操作日志",
          type: "info"
        });
      }
    },
    getPenaltyTypeName(id) {
      let name = '';
      this.penaltyTypeList.map((item) => {
        if (id == item.value) {
          name = item.label;
        }        
      })
      return name;
    },
    //牵引车模糊搜索（搜索框）
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    // 关闭详情dialog
    handleConfirmDetailVisible: function() {
      this.detailVisible = false;
    },
    // 修改
    update: function(data) {
      $http.getDetail(data.id).then(res => {
        this.dataForm = Object.assign({}, res.data);
        this.getErNm();
        this.visible = true;
        this.isDisabled = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      });
    },
    // 详情
    detail: function(id) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info.init(id);
      });
    },
    // 获取操作人员列表
    // getOpList: function() {
    //   $http.illegalVecSelectPers().then(res => {
    //     this.opratorList = res.list.map(item => {
    //       return { label: item, value: item };
    //     });
    //   });
    // },

    // 获取扣分类别列表
    getOpList: function() {
      $http.getGov3AlarmLaw({ type: 4 }).then((res) => {
        if (res.code === 0 && res.data.length > 0) {
          this.penaltyTypeList = res.data.map(item => {
            return {
              label: item.catNmCn,
              value: item.id,
            };
          });
        }
      });
    },
    // 选择运输企业，获取企业人联系人+电话
    changeCarrierNm(e) {
      let obj = this.selectCarrList.find(item => {
        return item.label == e.value;
      });
      if (obj) {
        this.dataForm.erNm = obj.erNm;
        this.dataForm.erMob = obj.erMob;
      }
    },
    getErNm() {
      $http
        .illegalVecSelectCarr({
          carrName: this.dataForm.carrierNm
        })
        .then(res => {
          this.selectCarrList = res.data;
          this.changeCarrierNm(this.dataForm.carrierNm);
        });
    },
    // 输入运输企业开启搜索功能
    queryCarrierNmAsync(query, cb) {
      if (query) {
        setTimeout(() => {
          // 接口请求的操作内容
          $http
            .illegalVecSelectCarr({
              carrName: query
            })
            .then(res => {
              res.data.forEach((item, index) => {
                this.carrierNmOptions.push({
                  value: item.entpName,
                  value2: item.uscCd,
                  label: item.entpName
                });
              });
              var entpNames = res.data;
              this.selectCarrList = entpNames.map(item => {
                return {
                  label: item.entpName,
                  value: item.uscCd,
                  erNm: item.erNm,
                  erMob: item.erMob
                };
              });
              var results = entpNames.map(item => {
                return { value: item.entpName };
              });
              cb(results);
            });
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 输入开启搜索功（新增弹窗）
    querySearchAsync(query, cb) {
      if (query) {
        if (query.length <= 2) {
          cb([]);
          return;
        }
        setTimeout(() => {
          // 接口请求的操作内容
          //   {
          //   filters: {
          //     groupOp: "AND",
          //     rules: [{ field: "vec_no", op: "cn", data: query }]
          //   }
          // }
          getFuzzyTracCd("1180.154", query).then(res => {
            res.data.forEach((item, index) => {
              this.options.push({
                value: item.name,
                value2: item.ownedCompany,
                label: item.name
              });
            });
            var names = res.data;
            var results = names.map(item => {
              return { value: item.name };
            });
            cb(results);
          });
        }, 1000);
      } else {
        cb([]);
      }
    },
    // 选择时触发
    handleSelect(val) {
      if (val.value) {
        let nm = this.options.filter(item => {
          return item.value == val.value;
        });
        this.dataForm.entpNm = nm[0].value2;
      }
    },
    // 清空时触发
    handleClear() {
      this.dataForm.entpNm = "";
    },
    //获取报警类型
    getAlarmTypelist() {
      return $http
        .getAlarmType()
        .then(response => {
          let list = response.data;
          if (response.code == 0 && list) {
            let alarmList = list.map(item => {
              return { label: item.nmCn, value: item.cd };
            });
            //判断角色是否为稽查大队gov_dljc，否则删除"超经营范围报警"
            let rolesName =
              localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
            if (!rolesName || rolesName.indexOf("gov_dljc") == -1) {
              alarmList.forEach((item, index) => {
                if (item.label.indexOf("超经营范围") >= 0) {
                  alarmList.splice(index, 1);
                }
              });
            }
            this.alarmTypesOptions = alarmList;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 获取数据
    getList: function() {
      let _this = this;
      let para = this.$refs.searchbar.get();
      let x = para.rules.map(item => {
        let params = {};
        params[item["field"]] = item["data"];
        return params;
      });
      let p = x.reduce((acc, cur) => {
        return Object.assign(acc, cur);
      }, {});
      let param = Object.assign({}, this.pagination, this.queryForm, p);
      delete param.total;
      this.listLoading = true;
      $http
        .getList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    //查看详细内容
    openUrl(url) {
      if (!url) return;
      window.open(url, "_blank");
    },
    add() {
      this.dataForm = {
        id: ""
      };

      this.visible = true;
      this.isDisabled = false;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //提交
    submit() {
      let _this = this;
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.subLoading = true;
          $http[this.dataForm.id ? "upd" : "add"](this.dataForm)
            .then(res => {
              if (res && res.code == 0) {
                this.$message({
                  message: "提交成功",
                  type: "success",
                  duration: 2000,
                  onClose: () => {
                    this.subLoading = false;
                    _this.visible = false;
                    _this.getList();
                  }
                });
              }
            })
            .catch(() => {
              this.subLoading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    upd(data) {
      this.visible = true;
      this.dataForm = Object.assign({}, data);
    },
    // 删除
    del(id) {
      let _this = this;
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http.illegalVecDelete(id).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
                duration: 2000,
                onClose: () => {
                  _this.getList();
                }
              });
            }
          });
        })
        .catch(() => {});
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    }
  }
};
</script>

<style scoped>
.ill-form .el-radio {
  margin-bottom: 10px;
}
.ill-form .flex-box-roll {
  display: flex;
}
.ill-form .flex-box-roll > * {
  width: 90%;
}
.ill-img {
  box-sizing: border-box;
  padding: 10px 10px 0 0;
}
.ill-img /deep/ .el-icon-circle-close {
    color: white;
  }
</style>
