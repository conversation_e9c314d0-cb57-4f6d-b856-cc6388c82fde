<template>
  <el-dialog
    title="修改违章预警处置"
    class="mod-loadBefCheck-edit"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="80%"
  >
    <div v-loading="detailLoading" class="detail-container">
      <div class="panel" style="background: #eef2f7;">
        <div class="panel-header">
          <span class="panel-heading-inner"
            >修改{{ formData.catNmCn }}处置</span
          >
        </div>
        <div class="panel-body">
          <el-form
            size="small"
            label-width="160px"
            v-loading="detailLoading"
            ref="dataForm"
            :model="formData"
          >
            <el-row>
              <el-col :span="11">
                <el-form-item label="运输企业：">
                  <el-input v-model="formData.carrierNm" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="牵引车车牌：">
                  <el-input
                    :style="{ width: isVecShow ? '80%' : '' }"
                    v-model="formData.tracCd"
                    disabled
                  ></el-input>
                  <span v-if="isVecShow" style="color: red; font-size: 14px;">
                    <i class="el-icon-warning-outline"></i>重点车辆锁定
                  </span>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="挂车车牌：">
                  <el-input v-model="formData.traiCd" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="驾驶员：">
                  <el-input v-model="formData.dvNm" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="押运员：">
                  <el-input v-model="formData.scNm" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="当前定位：">
                  <el-input v-model="lnglat" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11" v-show="false">
                <el-form-item label="经度：">
                  <el-input
                    v-model="formData.alarmLongitude"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11" v-show="false">
                <el-form-item label="纬度：">
                  <el-input
                    v-model="formData.alarmLatitude"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="当前时间：">
                  <!-- <el-date-picker
                  v-model="formData.foundTm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期时间"
                ></el-date-picker> -->
                  <el-input v-model="formData.foundTm" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="当前位置：">
                  <el-input
                    v-model="formData.alarmLocation"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="货物：">
                  <el-input v-model="formData.goodsNm" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="货物重量(吨)：">
                  <el-input v-model="formData.loadQty" disabled></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="发货地：">
                  <el-input
                    v-model="formData.csnorWhseDist"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="卸货地：">
                  <el-input
                    v-model="formData.csneeWhseDist"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="11">
                <el-form-item label="当前速度(km/小时)：">
                  <el-input v-model="formData.speed" disabled></el-input>
                </el-form-item>
              </el-col>

              <!-- <el-col :span="11">
                <el-form-item
                  label="操作员："
                  :rules="$rulesFilter({ required: true })"
                  prop="oprNm"
                >
                  <el-autocomplete
                    class="inline-input"
                    v-model="formData.oprNm"
                    :fetch-suggestions="querySearch"
                    placeholder="请输入内容"
                    clearable
                  ></el-autocomplete>
                </el-form-item>
              </el-col> -->

              <el-col :span="24">
                <div
                  v-if="msgAlarm"
                  style="color: red; font-size: 14px;margin-left: 5%;"
                >
                  <i class="el-icon-warning-outline"></i>
                  {{ msgAlarm }}
                </div>
                <el-form-item
                  label="处理结果："
                  :rules="$rulesFilter({ required: true })"
                  prop="isHandle"
                >
                  <el-radio-group
                    v-model="formData.isHandle"
                    class="custom-radio-group"
                  >
                    <el-radio
                      v-for="(item, index) in alarmDealOptions[
                        formData.catCd
                      ] || alarmDealOptions['default']"
                      :key="alarmDealActions[item].value"
                      :label="alarmDealActions[item].value"
                      >{{ alarmDealActions[item].label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <!-- <el-form-item label="备注" prop="oprContent"> -->
                <multiple-editor v-model="formData.oprContent" />
                <!-- <wangeditor
                    ref="wangeditor"
                    v-model="formData.oprContent"
                    placeholder="请输入备注"
                  ></wangeditor> -->
                <!-- </el-form-item> -->
              </el-col>
              <!-- <el-col :span="22">
                <el-form-item label="操作反馈：" :rules="$rulesFilter({required:true})" prop="oprContent">
                  <el-input clearable v-model="formData.oprContent" type="textarea"></el-input>
                </el-form-item>
              </el-col> -->

              <!-- <el-col :span="22" style="text-align:right;">
                <el-form-item>
                  <el-button type="primary" size="large" @click="upd">保存</el-button>
                </el-form-item>
              </el-col> -->
            </el-row>
          </el-form>
        </div>
        <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      </div>
      <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="upd">保存</el-button>
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  offsiteInfo,
  offsiteUpdate,
  getSelectOuterCityTips
} from "@/api/offsite";
// import wangeditor from "@/components/editor/wangeditor";
import MultipleEditor from "@/components/MultipleEditor";
import { mapGetters } from "vuex";
import { getList } from "@/api/vecMonitor";

export default {
  name: "offsiteForm",
  inject: ["opratorList"],
  components: {
    // wangeditor,
    MultipleEditor
  },
  data() {
    return {
      visible: false,
      detailLoading: false,
      id: "",
      lnglat: "",
      formData: {},
      msgAlarm: "", // 违章预警操作提示语
      isVecShow: false //是否是重点锁定车辆
    };
  },
  computed: {
    ...mapGetters([
      "alarmDealActions",
      "allAlarmDealOptions",
      "alarmDealOptions"
    ])
  },
  methods: {
    init(row) {
      this.id = row.id || "";
      this.visible = true;
      this.isVecShow = false;
      this.$nextTick(() => {
        this.$refs.dataForm &&
          this.$refs.dataForm.resetFields() &&
          this.$refs.dataForm.clearValidate();
        this.formData.oprContent = "";
        if (this.id) {
          this.getInfo(this.id);
        }
        //违章预警操作提示
        if (row.alarmPk) {
          this.getTips(row.alarmPk);
        }
        if (row && row.tracCd) {
          this.isVecMonitor(row.tracCd);
        }
      });
    },
    getInfo(id) {
      offsiteInfo(id)
        .then(res => {
          this.detailLoading = false;
          if (res.code == 0) {
            this.$set(this, "formData", res.data);
            this.lnglat =
              res.data.alarmLongitude + "," + res.data.alarmLatitude;
          } else {
            this.formData = {};
            this.lnglat = "";
          }
        })
        .catch(err => {
          this.formData = {};
          this.lnglat = "";
          this.detailLoading = false;
        });
    },
    upd() {
      let _this = this;
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.$confirm("确定提交吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              this.detailLoading = true;
              let postData = Object.assign({}, this.formData);
              offsiteUpdate(postData)
                .then(res => {
                  this.detailLoading = false;
                  if (res.code == 0) {
                    this.$message({
                      type: "success",
                      message: "提交成功!",
                      onClose() {
                        _this.visible = false;
                        _this.$emit("refreshDataList");
                      }
                    });
                    // this.getInfo(id);
                  } else {
                    this.$message.info(res.msg || "服务器错误，请联系管理员");
                  }
                })
                .catch(err => {
                  this.detailLoading = false;
                });
            })
            .catch(() => {
              this.detailLoading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    },
    querySearch(queryString, cb) {
      let opratorList = this.opratorList;
      let results = queryString
        ? opratorList.filter(this.createFilter(queryString))
        : opratorList;

      cb(results);
    },
    createFilter(queryString) {
      return opratorList => {
        return (
          opratorList.value.toLowerCase().indexOf(queryString.toLowerCase()) !=
          -1
        );
      };
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    //违章预警操作提示
    getTips(pk) {
      let param = { alarmPk: pk };
      getSelectOuterCityTips(param)
        .then(res => {
          if (res.code == 0) {
            if (res.flag) {
              this.msgAlarm = res.msg;
            } else {
              this.msgAlarm = "";
            }
          } else {
            this.$message({
              message: res.msg,
              type: "error"
            });
          }
        })
        .catch(error => console.log(error));
    },
    // 判断是否是‘重点车辆锁定’的车
    isVecMonitor(tracCd) {
      let param = {
        filters: {
          groupOp: "AND",
          rules: [
            { field: "vec_no", op: "cn", data: `${tracCd}` },
            { field: "sys_id", data: "330211", op: "eq" }
          ]
        }
      };

      //查询
      getList(param)
        .then(response => {
          if (response.code == 0) {
            let listLength = response.page.list.length;
            if (listLength) {
              this.isVecShow = true;
            } else {
              this.isVecShow = false;
            }
          } else {
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/ {
  .custom-radio-group.el-radio-group {
    line-height: 28px;

    .el-radio {
      margin-right: 15px;
      min-width: 108px;
    }
    .el-radio + .el-radio {
      margin-left: 0;
    }
  }
}
</style>
