<template>
  <el-dialog title="违章预警处置详情" class="mod-info-container" :close-on-click-modal="false" :visible.sync="visible"
    :append-to-body="true" width="80%">
    <div class="panel">
      <div class="panel-body">
        <div class="print-panel" v-loading="detailLoading">
          <div class="print-panel-header">
            <div class="panel-heading-content">
              <h3>{{ info.catNmCn }}处置详情</h3>
              <div v-show="info" style="font-weight: initial; font-size: 15px; line-height: 30px">
                <div :title="'发现日期'">
                  {{ info.foundTm | FormatDate("yyyy年MM月dd日") }}
                </div>
              </div>
            </div>
          </div>
          <div class="print-panel-body">
            <table class="custom-table" v-imgViewer>
              <tbody>
                <tr>
                  <th>运输企业名称</th>
                  <td colspan="3">{{ info.carrierNm }}</td>
                </tr>
                <tr>
                  <th>牵引车</th>
                  <td>{{ info.tracCd }}</td>
                  <th>挂车号</th>
                  <td>{{ info.traiCd }}</td>
                </tr>

                <tr>
                  <th>货物名称</th>
                  <td>{{ info.goodsNm === null ? "无" : info.goodsNm }}</td>
                  <th>货物重量(吨)</th>
                  <td>{{ info.loadQty }}</td>
                </tr>
                <tr>
                  <th>企业联系人</th>
                  <td>{{ info.erNm }}</td>
                  <th>联系电话</th>
                  <td>{{ info.erMob }}</td>
                </tr>
                <tr>
                  <th>驾驶员姓名</th>
                  <td>{{ info.dvNm }}</td>
                  <th>联系电话</th>
                  <td>{{ info.dvMob }}</td>
                </tr>

                <tr>
                  <th>押运员姓名</th>
                  <td>{{ info.scNm }}</td>
                  <th>押运员联系电话</th>
                  <td>{{ info.scMob }}</td>
                </tr>

                <tr>
                  <th>报警地点</th>
                  <td colspan="3">{{ info.alarmLocation }}</td>
                </tr>

                <!-- <tr>
                  <th>经度</th>
                  <td>{{info.alarmLongitude}}</td>
                  <th>纬度</th>
                  <td>{{info.alarmLatitude}}</td>
                </tr> -->

                <tr>
                  <th>报警时间</th>
                  <td colspan="3">{{ info.alarmTime }}</td>
                </tr>

                <tr>
                  <th>处置时间</th>
                  <td>{{ info.foundTm }}</td>
                  <th>处置操作</th>
                  <td>{{ handleName }}</td>
                </tr>
                <tr>
                  <th>操作员</th>
                  <td colspan="3">{{ info.oprNm }}</td>
                </tr>
                <tr v-if="info.isReply === 2 && showReplyLetter">
                  <th>司机举证</th>
                  <td colspan="3">
                    <div><strong>司机反馈：</strong>{{ info.replyRemark }}</div>
                    <div>
                      <div><strong>举证图片：</strong></div>
                      <div class="limit-img-width">
                        <template v-if="info.replyUrl && info.replyUrl.length">
                          <template v-for="item in info.replyUrl.split(',')">
                            <img :src="item" :alt="item" width="60" height="60" style="margin:10px;display: inline-block;">
                          </template>
                        </template>
                      </div>
                    </div>
                  </td>
                </tr>
                <!-- <tr>
                  <th>司机反馈描述</th>
                  <td colspan="3">{{ info.replyRemark }}</td>
                </tr>
                <tr v-show="info.isReply === 2" class="limit-img-width">
                  <th>司机举证图片</th>
                  <td colspan="3" v-if="info.isReply === 2">
                    <img :src="info.replyUrl" :alt="info.replyUrl" width="60" height="60"
                      style="margin:10px;display: inline-block;">
                  </td>
                </tr> -->
                <!-- <tr>
                  <th>处置详情</th>
                  <td  colspan="3" style="padding:10px;" class="limit-img-width">
                    <el-timeline :reverse="true">
                      <el-timeline-item
                        :timestamp="'处置时间：'+activity.time"
                        placement="top"
                        color="#0bbd87"
                        v-for="(activity, index) in info.oprDetail"
                        :key="index">
                        <div style="font-weight:bold;">
                          <div >处置操作:{{ alarmDealActions[activity.isHandle].label }}</div>
                          <div>操作员:{{ activity.oprNmManual }}</div>
                        </div>
                        <div v-if="isHtmlText(activity.oprContent)" >
                          <div v-html="activity.oprContent" ></div>
                        </div>
                        <div v-else>
                          <p>{{ activity.oprContent.oprContent }}</p>
                          <div>
                            <p v-if="activity.oprContent && activity.oprContent.oprFiles">
                              <img :src="item" :alt="item" width="60" height="60" style="margin:10px;display: inline-block;" v-for="(item,index) in activity.oprContent.oprFiles" :key="index">
                            </p>
                          </div>
                        </div>

                      </el-timeline-item>
                    </el-timeline>
                  </td>
                </tr> -->
                <tr>
                  <th>详情描述</th>
                  <td colspan="3">
                    <div v-if="isHtmlText(info.oprContent)">
                      <div v-html="info.oprContent" class="limit-img-width"></div>
                    </div>
                    <div v-else-if="info.oprContent && info.oprContent.oprContent">
                      <p>{{ info.oprContent.oprContent }}</p>
                    </div>
                    <div v-else>
                      <p>{{ info.oprContent }}</p>
                    </div>
                  </td>
                </tr>
                <tr v-if="!isHtmlText(info.oprContent) && info.oprContent && info.oprContent.oprFiles">
                  <th>证据上传</th>
                  <td colspan="3" class="limit-img-width">
                    <p style="font-size: 60px;">
                      <template v-for="(item, index) in info.oprContent.oprFiles" >
                        <svg-icon 
                        v-if="/\.(mp4|avi|mov|flv|wmv|mkv)$/i.test(item)"
                        :key="index" 
                        @click.native="showVideo(item)" 
                        icon-class="video" 
                        class-name="svg-icon"
                        ></svg-icon>
                        <img v-else :src="item" 
                        :alt="item" 
                        width="60" 
                        height="60" 
                        style="margin:10px;display: inline-block;"
                        :key="index">
                      </template>
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- panel-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    </div>
    <!-- panel <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>

    <el-dialog title="举证视频" :visible.sync="videoDialogVisible" width="500px" append-to-body @close="closeCbkHandle">
      <video v-if="videoUrl" width="100%" height="300px" controls autoplay>
        <source :src="videoUrl"  type="video/mp4">
        您的浏览器不支持 HTML5 video 标签。
      </video>
    </el-dialog>

  </el-dialog>
</template>

<script>
import { offsiteInfo } from "@/api/offsite";
import { mapGetters } from "vuex";
export default {
  name: "offsiteInfo",
  inject: ["opratorList"],
  data() {
    return {
      visible: false,
      detailLoading: false,
      id: "",
      info: {},
      videoDialogVisible: false,
      videoUrl:""
    };
  },
  computed: {
    ...mapGetters([
      "alarmDealActions",
      "allAlarmDealOptions",
      "alarmDealOptions"
    ]),
    handleName() {
      if (this.info && this.info.isHandle) {
        let t = this.alarmDealActions[this.info.isHandle];
        if (t) {
          return t.label;
        } else {
          return "";
        }
      } else {
        return "";
      }
    },
    showReplyLetter() {
      let initValue = true;
      let state = this.info.govHandlerType;
      let replyLetterAudit = this.info.replyStatus;
      if (state == "14" || replyLetterAudit === 2) {
        initValue = false;
      }
      return initValue;
    },
    // isHtmlText() {
    //   try {
    //     let oprContent = JSON.parse(this.info.oprDetail)
    //     oprContent && this.$set(this.info, 'oprContent', oprContent)
    //     return false;
    //   }
    //   catch (err) {
    //     return true;
    //   }
    // }
  },
  methods: {
    showVideo(url){
      this.videoDialogVisible = true;
      this.videoUrl = url;
    },
    closeCbkHandle(){
      this.videoUrl = "";
    },
    init(id) {
      this.id = id || "";
      this.visible = true;
      this.$nextTick(() => {
        if (this.id) {
          this.getInfo(this.id);
        }
      });
    },
    getInfo(id) {
      this.detailLoading = true;
      offsiteInfo(id)
        .then(res => {
          this.detailLoading = false;
          if (res.code == 0) {
            let infoData = Object.assign({}, res.alarmData, res.data)
            try {
              infoData.oprContent = JSON.parse(infoData.oprContent)
            } catch (error) { }
            
            if (infoData.oprDetail) {
              try {
                infoData.oprDetail = JSON.parse(infoData.oprDetail)
              } catch (error) {}
              infoData.oprDetail.forEach(item => {
                try {
                  item.oprContent = JSON.parse(item.oprContent)
                } catch (error) {}
              })
            }

            this.$set(this, "info", infoData);
          } else {
            this.info = {};
          }
        })
        .catch(err => {
          this.info = {};
          this.detailLoading = false;
        });
    },
    isHtmlText(content) {
      if (typeof content === 'string') {
        return true;
      } else {
        return false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.custom-table {
  th {
    min-width: 100px;
  }

  .limit-img-width /deep/ {
    img {
      width: 100px;
      height: 100px;
      display: block;
      // max-width: 500px !important;
    }
  }
}
</style>