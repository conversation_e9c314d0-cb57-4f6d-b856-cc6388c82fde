<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-col
          :span="24"
          class="toolbar text-right"
          style="padding-bottom: 0px"
        >
          <el-form
            ref="queryForm"
            :model="queryForm"
            :inline="true"
            label-width="80px"
            size="mini"
          >
            <el-form-item prop="carrierNm">
              <el-input
                clearable
                v-model="queryForm.carrierNm"
                placeholder="请输入企业名称"
                style="width: 150px"
                @input="updateMark"
              ></el-input>
            </el-form-item>
            <el-form-item prop="tracCd">
              <el-input
                clearable
                v-model="queryForm.tracCd"
                placeholder="请输入牵引车牌号"
                style="width: 150px"
                @input="updateMark"
              ></el-input>
            </el-form-item>
            <el-form-item prop="startTm">
              <el-date-picker
                v-model="daterange"
                type="daterange"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="updateMark"
              >
              </el-date-picker>
            </el-form-item>

            <!-- <el-form-item prop="startTm">
              <el-date-picker
                clearable
                v-model="queryForm.startTm"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择开始日期"
                style="width: 150px"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTm">
              <el-date-picker
                clearable
                v-model="queryForm.endTm"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择结束日期"
                style="width: 150px"
              >
              </el-date-picker>
            </el-form-item> -->
            <!-- <el-form-item prop="oprNm">
              <el-input clearable v-model="queryForm.oprNm" placeholder="请输入操作员" style="width:150px;"></el-input>
            </el-form-item> -->

            <el-form-item prop="catCd">
              <el-select v-model="queryForm.catCd" clearable @change="updateMark">
                <el-option label="所有违章" value=""></el-option>
                <el-option
                  v-for="item in alarmTypesOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="isHandle">
              <el-select
                v-model="queryForm.isHandle"
                clearable
                placeholder="请选择处理结果"
                style="width: 150px"
                @change="updateMark"
              >
                <el-option label="所有处置结果" value=""></el-option>
                <el-option
                  v-for="item in allAlarmDealOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>

              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="warning" @click="exportExcel">导出</el-button>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="default" @click="resetQueryForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!--列表-->
    <el-table
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      :max-height="tableHeight"
      :row-class-name="tableRowClassName"
    >
      <el-table-column label="#" type="index"></el-table-column>
      <el-table-column prop="catNmCn" label="违章类别"></el-table-column>
      <el-table-column prop="tracCd" label="牵引车号"></el-table-column>
      <el-table-column prop="carrierNm" label="运输企业"></el-table-column>
      <el-table-column prop="alarmTime" label="报警时间"></el-table-column>
      <el-table-column prop="alarmLocation" label="报警地点"></el-table-column>
      <el-table-column prop="isHandle" label="处置操作">
        <template slot-scope="scope">
          <template v-if="scope.row.isHandle <= 2">
            <span>已受理</span>
          </template>
          <span v-else>
            <span
              v-if="scope.row.isHandle === null || !scope.row.isHandle"
            ></span>
            <span
              v-else-if="scope.row.isHandle === 13 || scope.row.isHandle === 18"
              class="cicle fill red-edge"
            ></span>
            <span v-else class="cicle fill green-edge"></span>
            {{
              (scope.row.isHandle &&
                alarmDealActions[scope.row.isHandle] &&
                alarmDealActions[scope.row.isHandle].label) ||
                ""
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="oprNm" label="操作员"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="infoHandle(scope.row.id)"
            title="查看详情"
            >详情</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="editHandle(scope.row)"
            title="修改"
            v-permission="'alarm:save'"
            >修改</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="showOpLog(scope.row.oprDetail)"
            title="操作日志"
            >操作日志</el-button
          >
          <!-- <el-button type="text" size="small" @click="delHandle(scope.row)" title="删除">删除</el-button> -->
          <!-- <router-link :to="{'name':'offsiteInfo',params:{id:scope.row.id}}">
            <el-button type="text" @click="info(scope.row)">详情</el-button>
          </router-link> -->
          <!-- <router-link :to="{'name':'offsiteForm',params:{id:scope.row.id}}">
            <el-button size="small" type="text">修改</el-button>
          </router-link>
          <el-button size="small" type="text" @click="del(scope.row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="pagination.total"
        :current-page="pagination.page"
        style="float: right"
      >
      </el-pagination>
    </el-col>

    <!-- 弹窗, 详情 -->
    <info v-if="infoVisible" ref="info"></info>
    <!-- 弹窗, 操作日志 -->
    <edit v-if="editVisible" ref="edit" @refreshDataList="getList"></edit>
    <el-dialog
      title="操作日志详情"
      append-to-body
      :visible.sync="opLogVisible"
      :close-on-click-modal="false"
      :destory-on-close="true"
      class="mod-loadBefCheck-edit"
      width="40%"
    >
      <div class="log-wrapper" v-imgViewer ref="imgHandel">
        <!-- <el-steps direction="vertical" :active="1" style="padding: 0 20px 20px">
          <template v-for="(item, index) in opLogArr">
            <el-step
              :status="
                item.isHandle == 2
                  ? 'success'
                  : item.isHandle === null || !item.isHandle
                  ? 'process'
                  : item.isHandle === 13 || item.isHandle === 18
                  ? 'error'
                  : 'success'
              "
              :key="index"
              :title="item.time"
            >
              <div slot="description">
                <div class="handleDesc" v-if="item.handler_content">
                  操作内容：
                  <span
                    v-html="item.handler_content"
                    @click="showImage()"
                  ></span>
                </div>
                <div>操作员: {{ item.oprNm }}</div>
                <div>详情描述: {{ item.oprContent }}</div>
                <div>操作时间: {{ item.time }}</div>
                <div>
                  处置操作:
                  {{
                    item.isHandle <= 2
                      ? "已受理"
                      : (item.isHandle &&
                          alarmDealActions[item.isHandle] &&
                          alarmDealActions[item.isHandle].label) ||
                        ""
                  }}
                </div>
              </div>
            </el-step>
          </template>
        </el-steps> -->
        <el-timeline :reverse="true">
          <el-timeline-item
            :timestamp="'处置时间：' + activity.time"
            placement="top"
            color="#0bbd87"
            v-for="(activity, index) in opLogArr"
            :key="index"
          >
            <div>
              <div>
                <label class="label">处置操作：</label
                >{{ alarmDealActions[activity.isHandle].label }}
              </div>
              <div>
                <!-- <label class="label">操作员：</label
                >{{ activity.oprNmManual || activity.oprNm }} -->
                <label class="label">操作员：</label>{{ activity.oprNm }}
              </div>
            </div>
            <div v-if="isHtmlText(activity.oprContent)">
              <label class="label">处置详情：</label>
              <div v-html="activity.oprContent"></div>
            </div>
            <template v-else>
              <div>
                <label class="label">处置详情：</label
                >{{ activity.oprContent.oprContent }}
              </div>
              <div>
                <label class="label">详情图片：</label>
                <p v-if="activity.oprContent && activity.oprContent.oprFiles">
                  <img
                    :src="item"
                    :alt="item"
                    width="60"
                    height="60"
                    style="margin:10px;display: inline-block;"
                    v-for="(item, index) in activity.oprContent.oprFiles"
                    :key="index"
                  />
                </p>
              </div>
            </template>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as $http from "@/api/offsite";
import * as Tool from "@/utils/tool";
import info from "./info";
import edit from "./form";
import { oprlist } from "@/api/mapMonit";
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
export default {
  name: "offsiteList",
  components: {
    info,
    edit
  },
  provide() {
    return {
      opratorList: []
    };
  },
  data() {
    return {
      daterange: "",
      queryForm: {
        startTm: "",
        endTm: "",
        carrierNm: "",
        tracCd: "",
        catCd: "",
        oprNm: "",
        isHandle: ""
      },

      tableHeight: Tool.getClientHeight(),
      list: [],
      listLoading: false,
      addLoading: false,

      dataForm: {},
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pageMark:false, // 搜索条件修改标识
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },

      infoVisible: false,
      editVisible: false,
      opLogVisible: false, //操作日志弹窗
      opLogArr: [], //操作日志列表
      alarmTypesOptions: [
        // { label: "异常停车", value: "2550.170.275" },
        // { label: "超速", value: "2550.160.150" },
        // { label: "无电子运单", value: "2550.7120.190" },
        // { label: "疲劳驾驶", value: "2550.7120.180" },
        // { label: "偏离路线", value: "2550.170.170" },
        // { label: "超载", value: "2550.160.180" },
        // { label: "超经营范围", value: "2550.160.185" },
        // { label: "无GPS", value: "2550.7120.160" },
        // { label: "未备案", value: "2550.7120.155" }
      ]
    };
  },
  computed: {
    ...mapGetters([
      "alarmDealActions",
      "allAlarmDealOptions",
      "alarmDealOptions"
    ])
  },
  created() {
    let query = this.$route.query;
    if (query ){
      if(query.catCd) {
        this.queryForm.catCd = query.catCd || '14';
      }
      if (query.isHandle) {
        this.queryForm.isHandle = query.isHandle;
      }
    }else{
      this.queryForm.isHandle = "14"; //默认处理结果 已经处罚
    }
    this.getOprlist();
    this.getAlarmTypelist();
  },
  mounted: function() {
    const _this = this;
    this.tableHeight = Tool.getTableHeight() + 20;
    window.addEventListener("resize", function() {
      _this.tableHeight = Tool.getTableHeight() + 20;
    });
    
    // this.getOprlist();
    // this.getAlarmTypelist();
    this.getList();
  },
  methods: {
    getOprlist() {
      oprlist()
        .then(res => {
          if (res.code == 0) {
            this._provided.opratorList = res.list.map(item => {
              return { value: item, label: item };
            });
          } else {
            this._provided.opratorList = [];
          }
        })
        .catch(err => {
          this._provided.opratorList = [];
        });
    },
    //获取报警类型
    getAlarmTypelist() {
      $http
        // .getAlarmType()
        .getAlarmTypeForOffsite()
        .then(response => {
          let list = response.data;
          if (response.code == 0 && list) {
            let alarmList = list.map(item => {
              return { label: item.nmCn, value: item.cd };
            });
            //判断角色是否为稽查大队gov_dljc，否则删除"超经营范围报警"
            let rolesName =
              localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
            if (!rolesName || rolesName.indexOf("gov_dljc") == -1) {
              alarmList.forEach((item, index) => {
                if (item.label.indexOf("超经营范围") >= 0) {
                  alarmList.splice(index, 1);
                }
              });
            }
            this.alarmTypesOptions = alarmList;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 修改标识
    updateMark() {
      // console.log("修改");
      this.pageMark = true;
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      // param = Object.assign({},this.pagination);
      this.getList();
    },
    // 获取数据
    getList: function() {
      let _this = this;
      if (this.daterange) {
        this.queryForm.startTm = this.daterange[0];
        this.queryForm.endTm = this.daterange[1];
      } else {
        this.queryForm.startTm = "";
        this.queryForm.endTm = "";
      }
      let param = Object.assign({}, this.pagination, this.queryForm);
      delete param.total;
      if (this.pageMark) {
        param.page = 1;
      }
      this.listLoading = true;
      $http
        .offsiteList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
          _this.pageMark = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
          _this.pageMark = false;
        });
    },
    // 详情
    infoHandle(id) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info.init(id);
      });
    },
    // 新增 / 修改
    editHandle(row) {
      this.editVisible = true;
      this.$nextTick(() => {
        // if (!id) {
        //   id = null;
        // }
        this.$refs.edit.init(row);
      });
    },
    //操作日志弹窗
    showOpLog(oprDetail) {
      if (oprDetail) {
        let list = JSON.parse(oprDetail);
        list.forEach(item => {
          try {
            item.oprContent = JSON.parse(item.oprContent);
          } catch (error) {}
        });
        this.opLogArr = list;
        this.opLogVisible = true;
      } else {
        this.$message({
          message: "无操作日志",
          type: "info"
        });
      }
    },
    // 删除
    delHandle(row) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.listLoading = true;
          $http
            .offsiteDelete([row.id])
            .then(res => {
              this.listLoading = false;
              if (res.code == 0) {
                this.$message.success("删除成功!");
              } else {
                this.$message.info("fuw");
              }
              this.getList();
            })
            .catch(err => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    resetQueryForm() {
      this.$refs["queryForm"].resetFields();
    },
    /**
     * @Date: 2023-08-28 17:00:11
     * @Author: SangShuaiKang
     * @description: 根据条件来给行添加class名
     * @param {*} row
     * @param {*} rowIndex
     * @return {*}
     */
    tableRowClassName({ row, rowIndex }) {
      if (row.isOverDue || row.isCq === 1) {
        return "overtime-row";
      }
      return "";
    },
    isHtmlText(content) {
      if (typeof content === "string") {
        return true;
      } else {
        return false;
      }
    },
    // 导出Excel表格
    exportExcel() {
      let _this = this;
      if (this.daterange) {
        this.queryForm.startTm = this.daterange[0];
        this.queryForm.endTm = this.daterange[1];
      } else {
        this.queryForm.startTm = "";
        this.queryForm.endTm = "";
      }
      let param = Object.assign({}, this.queryForm);
      $http.exportExcel(param)
        .then(response => {
          if (response) {
            let url = window.URL.createObjectURL(new Blob([response]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;

            let alarmTime = Tool.formatDate(new Date(), "yyyy-MM-dd_HHmmss");
            if (_this.daterange) {
              let startDate = this.queryForm.startTm;
              let endDate = this.queryForm.endTm;
              alarmTime = `${startDate}至${endDate}`;
            }
            link.setAttribute("download", `违章预警数据${alarmTime}.xls`);
            document.body.appendChild(link);
            link.click();
            window.URL.revokeObjectURL(url);

            this.$message({
              message: '导出成功！',
              type: 'success'
            });
          } else {
             this.$message.error('导出失败！请重新导出');
          }
        })
        .catch(error => {
          this.$message.error('导出失败：' + error);
        });

    }
  }
};
</script>

<style lang="scss" scoped>
.app-main-content{
   & /deep/ .el-table{
     .overtime-row {
      color: #e94829 !important;
    }
  }
}
.cicle {
  position: relative;
  top: 3px;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin-right: 3px;

  &.fill {
    background: #ccc;
    border: 1px solid #ccc;
  }

  &.stroke {
    background: none;
    border: 1px solid #ccc;
  }

  // 半圆形
  &.semi {
    &:after {
      position: absolute;
      content: "";
      height: 16px;
      width: 8px;
      border-radius: 20px 0 0 20px;
      background: #ccc;
    }
  }

  &.green-edge {
    border: 1px solid #5daf34;

    &.fill {
      background: #5daf34;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #5daf34;
      }
    }
  }

  &.yellow-edge {
    border: 1px solid #ffd04b;

    &.fill {
      background: #ffd04b;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #ffd04b;
      }
    }
  }

  &.red-edge {
    border: 1px solid #d00;

    &.fill {
      background: #d00;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #d00;
      }
    }
  }
}


.log-wrapper {
  height: 500px;
  overflow-y: auto;
  line-height: 28px;

  .label {
    width: 5em;
  }
}
</style>
