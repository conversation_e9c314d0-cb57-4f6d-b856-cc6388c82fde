<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-col :span="4" class="toolbar" style="padding-bottom: 0px">
          <span class="el-icon-date">月内企业违章汇总表</span>
        </el-col>
        <el-col
          :span="20"
          class="toolbar text-right"
          style="padding-bottom: 0px"
        >
          <el-form
            ref="form"
            :model="queryForm"
            :inline="true"
            label-width="80px"
            size="mini"
          >
            <el-form-item>
              <el-select
                v-model="queryForm.entpDist"
                placeholder="请选择区域"
                @change="getList"
                clearable
              >
                <el-option
                  v-for="item in selectOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="queryForm.date"
                value-format="yyyy-MM"
                @change="getList()"
                type="month"
                placeholder="请选择月份"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryForm.entpName"
                placeholder="请输入企业名称"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="success" @click="handleDownload">导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column label="#" type="index"></el-table-column>
      <el-table-column prop="entpName" label="企业名称"> </el-table-column>
      <el-table-column
        prop="overSpeedCnt"
        sortable
        label="超速报警"
      ></el-table-column>
      <el-table-column
        prop="overBusinessCnt"
        sortable
        label="超经营范围"
        width="120"
      ></el-table-column>
      <el-table-column prop="overLoadCnt" sortable label="超载报警">
      </el-table-column>
      <el-table-column prop="deviateRouteCnt" sortable label="偏离路线">
      </el-table-column>
      <el-table-column
        prop="stopCnt"
        sortable
        label="停车报警"
      ></el-table-column>
      <!-- el-table-column prop="alarmNotHandleCnt" sortable label="违章次数"></el-table-column -->
      <el-table-column
        prop="noRteplanCnt"
        sortable
        label="无电子路单"
      ></el-table-column>
      <el-table-column
        prop="alarmRate"
        sortable
        label="违章率"
      ></el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="pagination.total"
        :current-page="pagination.page"
        style="float: right"
      >
      </el-pagination>
    </el-col>
  </div>
</template>

<script>
import * as $http from "@/api/monthStat";
import * as Tool from "@/utils/tool";

export default {
  name: "PersList",
  data () {
    return {
      selectOptions: [
        {
          value: 0,
          label: "区内企业"
        },
        {
          value: 1,
          label: "区外企业"
        }
      ],
      queryForm: {
        entpDist: "",
        date: "",
        entpName: ""
      },
      tableHeight: Tool.getClientHeight() - 210,
      list: [],

      listLoading: false,
      addLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  created () {
    this.getList();
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();

  },
  destroyed () {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210
      });
    },
    //统计报表导出
    handleDownload () {
      let _this = this;
      let param = {};

      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var data = _this.queryForm[filed];
          var filed = filed.replace(/([A-Z])/g, "_$1").toLowerCase();
          param[filed] = data;
        }
      }

      $http
        .downloadEntpListExcel(param)
        .then(response => {
          let a = document.createElement("a");
          let blob = new Blob([response]);
          let url = window.URL.createObjectURL(blob);

          a.href = url;
          a.download = "企业违章汇总统计_" + this.queryForm.date + ".xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function (param) {
      let _this = this;
      let filters = { groupOp: "AND", rules: [] };
      param = param || Object.assign({}, this.pagination);
      delete param.total;
      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "eq",
            data: _this.queryForm[filed]
          };
          if (filed == "entpDist") {
            rule.op = "nao";
          } else if (filed == "entpName") {
            rule.op = "cn";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;

      this.listLoading = true;
      $http
        .entpList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    }
  }
};
</script>

<style>
</style>
