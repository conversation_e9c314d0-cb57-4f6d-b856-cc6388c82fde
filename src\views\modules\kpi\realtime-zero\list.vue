<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <searchbar
        ref="searchbar"
        :searchItems="searchItems"
        :pagination="pagination"
        :isActiveUrl="isActiveUrl"
        @resizeSearchbar="resizeSearchbar"
        @search="getList"
      >
        <el-button
          slot="button"
          type="primary"
          icon="el-icon-document"
          size="small"
          @click="exportLog"
          >导出</el-button
        >
      </searchbar>
    </div>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      :max-height="tableHeight"
      @sort-change="handleSort"
    >
      <el-table-column label="#" type="index"></el-table-column>
      <!-- <el-table-column prop="catNmCn" label="违章类别"></el-table-column> -->
      <el-table-column
        prop="tracCd"
        label="牵引车号"
        width="100"
      ></el-table-column>
      <!-- <el-table-column prop="carrierNm" label="企业名称"></el-table-column> -->
      <el-table-column
        prop="updTm"
        label="处置时间"
        width="150"
        sortable="custom"
      ></el-table-column>
      <el-table-column prop="oprNm" label="操作员" width="90"></el-table-column>
      <el-table-column prop="isHandle" label="处置操作" width="90">
        <template slot-scope="scope">
          <!-- 0无需处理 5已经发送短信 10需要打电话 15已经打过电话 20需要现场处置 25已经现场处置 -->
          <template v-if="scope.row.isHandle === 15">电话询问</template>
          <template v-else-if="scope.row.isHandle === 25">现场处置</template>
        </template>
      </el-table-column>
      <el-table-column prop="oprContent" label="处置详情">
        <template slot-scope="scope">
          <div v-html="scope.row.oprContent"></div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width=100>
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="infoHandle(scope.row.id)" title="查看详情">详情</el-button>
          <el-button type="text" size="small" @click="editHandle(scope.row.id)" title="修改">修改</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :page-sizes="[20, 30, 50, 100, 200]"
        :total="pagination.total"
        style="float: right"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $httpVec from "@/api/vec";
import * as $httpMonitor from "@/api/mapMonitor";

import * as Tool from "@/utils/tool";
import dayjs from "dayjs";
import { debounce } from "lodash";
export default {
  name: "realtimeZeroList",
  components: {
    Searchbar
  },
  props: {
    // 表单元素大小
    isPage: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableHeight: 400,
      list: [],
      listLoading: false,
      opratorList: [], // 操作员
      searchItems: {
        normal: [
          {
            name: "牵引车号",
            field: "tracCd",
            type: "selectSearch",
            remoteMethod: this.querySearchVecAsync /*  */,
            options: [],
            dbfield: "trac_cd",
            dboper: "eq"
          },
          {
            name: "处置日期",
            field: "updTm",
            type: "daterange",
            dbfield: "upd_tm",
            dboper: "bt",
            default: [
              dayjs().format("YYYY-MM-DD") + " 00:00:00",
              dayjs().format("YYYY-MM-DD") + " 23:59:59"
            ],
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ],
        more: [
          {
            name: "操作员",
            field: "oprNm",
            type: "select",
            dbfield: "opr_nm",
            dboper: "eq",
            options: []
          },
          {
            name: "处置结果",
            field: "isHandle",
            type: "select",
            dbfield: "is_handle",
            dboper: "eq",
            options: [
              {
                label: "电话询问",
                value: 15
              },
              {
                label: "现场处置",
                value: 25
              }
            ]
          }
        ]
      },
      defaultSearch: [{ field: "opr_nm", op: "ne", data: "监管平台" }],
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      }
    };
  },
  created() {
    this.getOprlist();
  },
  mounted: function() {
    if (this.isPage) {
      window.addEventListener("resize", this.setTableHeight);
      let query = this.$route.query;
      this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
      this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
      this.pagination.total = this.pagination.page * this.pagination.limit;
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getList();
    }
  },
  computed: {
    isActiveUrl() {
      return this.isPage;
    }
  },
  watch: {
    opratorList: {
      handler(val) {
        this.searchItems.more[0].options = val;
      },
      deep: true
    }
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    //牵引车号过滤
    querySearchVecAsync: debounce(
      function(queryString) {
        let _this = this;
        if (queryString) {
          queryString = queryString.trim();
          this.getVecTracCd("1180.154", queryString, function(data) {
            _this.searchItems.normal[0].options = data;
          });
        } else {
          _this.searchItems.normal[0].options = [];
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 从数据库获取车号下拉选项
    getVecTracCd(vecType, queryString, callback) {
      let _this = this;
      $httpVec
        .getFuzzyTracCd(vecType, queryString)
        .then(response => {
          if (response && response.code === 0) {
            callback(
              response.data.map(it => {
                return { label: it.name, value: it.name };
              })
            );
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    init() {
      this.$refs.searchbar.init();
      this.getList();
    },
    getOprlist() {
      // 获取操作员列表
      $httpMonitor
        .getOprlist()
        .then(res => {
          if (res.code == 0) {
            this.opratorList = res.list.map(item => {
              return { value: item, label: item };
            });
          } else {
            this.opratorList = [];
          }
        })
        .catch(err => {
          this.opratorList = [];
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取数据
    getList: function(data, sortParam) {
      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      filters.rules = filters.rules.concat(this.defaultSearch);
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;

      this.listLoading = true;
      $httpMonitor
        .getPageOffsite(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 导出表格
    exportLog() {
      let param = { filters: this.$refs.searchbar.get() };
      $httpMonitor
        .realtimeZeroDownload(param)
        .then(res => {
          let a = document.createElement("a");
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download =
            "车辆实时处置记录_" +
            (_date.getFullYear() +
              "-" +
              (_date.getMonth() + 1) +
              "-" +
              _date.getDate()) +
            ".xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(err => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.cicle {
  position: relative;
  top: 3px;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin-right: 3px;

  &.fill {
    background: #ccc;
    border: 1px solid #ccc;
  }
  &.stroke {
    background: none;
    border: 1px solid #ccc;
  }

  // 半圆形
  &.semi {
    &:after {
      position: absolute;
      content: "";
      height: 16px;
      width: 8px;
      border-radius: 20px 0 0 20px;
      background: #ccc;
    }
  }

  &.green-edge {
    border: 1px solid #5daf34;

    &.fill {
      background: #5daf34;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #5daf34;
      }
    }
  }
  &.yellow-edge {
    border: 1px solid #ffd04b;

    &.fill {
      background: #ffd04b;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #ffd04b;
      }
    }
  }
  &.red-edge {
    border: 1px solid #d00;
    &.fill {
      background: #d00;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #d00;
      }
    }
  }
}
</style>
