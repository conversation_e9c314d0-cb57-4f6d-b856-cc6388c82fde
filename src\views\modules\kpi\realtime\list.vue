<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-col :span="24"
                class="toolbar"
                style="padding-bottom: 0px;">
          <el-form ref="queryForm"
                   :model="queryForm"
                   :inline="true"
                   label-width="80px"
                   size="mini">
            <el-form-item prop="tracCd">
              <el-input clearable
                        v-model="queryForm.tracCd"
                        placeholder="请输入牵引车号"
                        style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="carrierNm">
              <el-input clearable
                        v-model="queryForm.carrierNm"
                        placeholder="请输入企业名称"
                        style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="startTm">
              <el-date-picker clearable
                              v-model="queryForm.startTm"
                              type="date"
                              value-format="yyyy-MM-dd"
                              format="yyyy-MM-dd"
                              placeholder="选择开始日期"
                              style="width:150px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTm">
              <el-date-picker clearable
                              v-model="queryForm.endTm"
                              type="date"
                              value-format="yyyy-MM-dd"
                              format="yyyy-MM-dd"
                              placeholder="选择结束日期"
                              style="width:150px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="oprNm">
              <el-input clearable
                        v-model="queryForm.oprNm"
                        placeholder="请输入操作员"
                        style="width:150px;"></el-input>
            </el-form-item>
            <el-form-item prop="isHandle">
              <el-select v-model="queryForm.isHandle"
                         placeholder="请选择处理结果"
                         style="width:150px;"
                         clearable>
                <el-option v-for="item in (alarmDealOptions.realtime)"
                           :key="alarmDealActions[item].value"
                           :value="alarmDealActions[item].value"
                           :label="alarmDealActions[item].label"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary"
                         @click="getList">查询</el-button>
              <el-button type="default"
                         @click="resetQueryForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!--列表-->
    <el-table class="el-table"
              :data="list"
              highlight-current-row
              v-loading="listLoading"
              style="width: 100%;"
              :max-height="tableHeight">
      <el-table-column label="#"
                       type="index"></el-table-column>
      <el-table-column prop="catNmCn"
                       label="违章类别"></el-table-column>
      <el-table-column prop="tracCd"
                       label="牵引车号"></el-table-column>
      <el-table-column prop="carrierNm"
                       label="企业名称"></el-table-column>
      <el-table-column prop="foundTm"
                       label="处置时间"></el-table-column>
                       <el-table-column prop="isHandle"
                       label="处置操作">
        <template slot-scope="scope">
          <template v-if="scope.row.isHandle<=2">
            <span>已受理</span>
          </template>
          <span v-else>
            <span v-if="scope.row.isHandle===null || !scope.row.isHandle"></span>
            <span v-else-if="scope.row.isHandle===13 || scope.row.isHandle===18"
                  class="cicle fill red-edge"></span>
            <span v-else
                  class="cicle fill green-edge"></span>
            {{scope.row.isHandle && alarmDealActions[scope.row.isHandle] &&alarmDealActions[scope.row.isHandle].label || ""}}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="oprNm"
                       label="操作员"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text"
                     size="small"
                     @click="infoHandle(scope.row.id)"
                     title="查看详情">详情</el-button>
          <el-button type="text"
                     size="small"
                     @click="editHandle(scope.row.id)"
                     title="修改">修改</el-button>
          <!-- <el-button type="text" size="small" @click="delHandle(scope.row)" title="删除">删除</el-button> -->
          <!-- <router-link :to="{'name':'offsiteInfo',params:{id:scope.row.id}}">
            <el-button type="text" @click="info(scope.row)">详情</el-button>
          </router-link> -->
          <!-- <router-link :to="{'name':'offsiteForm',params:{id:scope.row.id}}">
            <el-button size="small" type="text">修改</el-button>
          </router-link>
          <el-button size="small" type="text" @click="del(scope.row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-col :span="24"
            class="toolbar">
      <el-pagination background
                     layout="sizes, prev, pager, next, total"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange"
                     :page-size="pagination.limit"
                     :page-sizes="[20, 30, 50, 100, 200, 500]"
                     :total="pagination.total"
                     :current-page="pagination.page"
                     style="float:right;">
      </el-pagination>
    </el-col>

    <!-- 弹窗, 详情 -->
    <info v-if="infoVisible"
          ref="info"></info>
    <!-- 弹窗, 新增 / 修改 -->
    <edit v-if="editVisible"
          ref="edit"
          @refreshDataList="getList"></edit>
  </div>
</template>

<script>
import * as $http from "@/api/offsite";
import * as Tool from "@/utils/tool";
import info from "./info";
import edit from "./form";
import { oprlist } from "@/api/mapMonit";
import dayjs from 'dayjs'
import { mapGetters } from "vuex";
export default {
  // name: "realtimeList",
  components: {
    info,
    edit
  },
  provide () {
    return {
      opratorList: []
    };
  },
  data () {
    return {
      queryForm: {
        startTm: "",
        endTm: "",
        carrierNm: "",
        tracCd: "",
        catCd: "",
        oprNm: "",
        isHandle: ""
      },

      tableHeight: Tool.getClientHeight(),
      list: [],
      listLoading: false,
      addLoading: false,

      dataForm: {},
      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function (picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function (picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },

      infoVisible: false,
      editVisible: false,
      alarmTypesOptions: [
        { label: "异常停车", value: "2550.170.275" },
        { label: "超速", value: "2550.160.150" },
        { label: "无电子运单", value: "2550.7120.190" },
        { label: "疲劳驾驶", value: "2550.7120.180" },
        { label: "偏离路线", value: "2550.170.170" },
        { label: "超载", value: "2550.160.180" },
        { label: "超经营范围", value: "2550.160.185" },
        { label: "无卫星定位", value: "2550.7120.160" },
        { label: "未备案", value: "2550.7120.155" }
      ]
    };
  },
            computed: {
    ...mapGetters(["alarmDealActions", "allAlarmDealOptions","alarmDealOptions"])
  },
  mounted: function () {
    const _this = this;
    this.tableHeight = Tool.getTableHeight() + 20;
    window.addEventListener("resize", function () {
      _this.tableHeight = Tool.getTableHeight() + 20;
    });
    this.queryForm.isHandle = this.alarmDealOptions.realtime[0]+''//默认处理结果 立即劝离
    this.getOprlist();
    this.queryForm.startTm = dayjs().format('YYYY-MM-DD')
    this.queryForm.endTm = dayjs().format('YYYY-MM-DD')
    this.getList();

  },
  methods: {
    getOprlist () {
      oprlist()
        .then(res => {
          if (res.code == 0) {
            this._provided.opratorList = res.list.map(item => {
              return { value: item, label: item };
            });
          } else {
            this._provided.opratorList = [];
          }
        })
        .catch(err => {
          this._provided.opratorList = [];
        });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      let param = {};
      this.pagination.limit = val;
      // param = Object.assign({},this.pagination);
      this.getList();
    },
    // 获取数据
    getList: function () {
      let _this = this;
      let param = Object.assign({}, this.pagination, this.queryForm);
      delete param.total;
      this.listLoading = true;
      $http
        .offsiteRealtimeList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 详情
    infoHandle (id) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info.init(id);
      });
    },
    // 新增 / 修改
    editHandle (id) {
      this.editVisible = true;
      this.$nextTick(() => {
        if (!id) {
          id = null;
        }
        this.$refs.edit.init(id);
      });
    },
    // 删除
    delHandle (row) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.listLoading = true;
          $http
            .offsiteDelete([row.id])
            .then(res => {
              this.listLoading = false;
              if (res.code == 0) {
                this.$message.success("删除成功!");
              } else {
                this.$message.info("fuw");
              }
              this.getList();
            })
            .catch(err => {
              this.listLoading = false;
            });
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    resetQueryForm () {
      this.$refs["queryForm"].resetFields();
    }
  }
};
</script>

<style lang="scss" scoped>
.cicle {
  position: relative;
  top: 3px;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin-right: 3px;

  &.fill {
    background: #ccc;
    border: 1px solid #ccc;
  }
  &.stroke {
    background: none;
    border: 1px solid #ccc;
  }

  // 半圆形
  &.semi {
    &:after {
      position: absolute;
      content: "";
      height: 16px;
      width: 8px;
      border-radius: 20px 0 0 20px;
      background: #ccc;
    }
  }

  &.green-edge {
    border: 1px solid #5daf34;

    &.fill {
      background: #5daf34;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #5daf34;
      }
    }
  }
  &.yellow-edge {
    border: 1px solid #ffd04b;

    &.fill {
      background: #ffd04b;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #ffd04b;
      }
    }
  }
  &.red-edge {
    border: 1px solid #d00;
    &.fill {
      background: #d00;
    }
    &.stroke {
      background: none;
    }
    &.semi {
      &:after {
        background: #d00;
      }
    }
  }
}
</style>
