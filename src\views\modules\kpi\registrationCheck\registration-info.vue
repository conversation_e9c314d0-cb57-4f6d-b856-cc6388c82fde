<template>
  <el-dialog
    :title="title + '详情'"
    class="mod-info-container"
    :close-on-click-modal="false"
    :visible.sync="visible"
    :append-to-body="true"
    width="80%"
  >
    <div class="panel">
      <div class="panel-body">
        <div class="print-panel" v-loading="detailLoading">
          <div class="print-panel-body">
            <table class="custom-table">
              <tbody>
                <tr>
                  <th>登记点点位</th>
                  <td colspan="4">{{ info.regPot }}</td>
                </tr>
                <tr>
                  <th>检查人员</th>
                  <td colspan="2">{{ info.inspectors }}</td>
                  <th>检查时间</th>
                  <td>{{ info.inspTm }}</td>
                </tr>

                <tr>
                  <th rowspan="6">检查内容</th>
                  <th class="sub-title">1.人员到岗情况</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.persArrival == 1" disabled
                      >全员到岗</el-checkbox
                    >
                    <span class="text-mark" v-if="info.persArrival == 1">{{
                      info.persArrivalRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.persArrival == 2" disabled
                      >到岗不全</el-checkbox
                    >
                    <span class="text-mark" v-if="info.persArrival == 2">{{
                      info.persArrivalRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">2.办公区、内务、厕所等卫生情况</th>

                  <td colspan="4">
                    <el-checkbox v-model="info.sanitary == 1" disabled
                      >好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.sanitary == 1">{{
                      info.sanitaryRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.sanitary == 2" disabled
                      >良好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.sanitary == 2">{{
                      info.sanitaryRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.sanitary == 3" disabled
                      >差</el-checkbox
                    >
                    <span class="text-mark" v-if="info.sanitary == 3">{{
                      info.sanitaryRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">3.警容风纪情况</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.discipline == 1" disabled
                      >好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.discipline == 1">{{
                      info.disciplineRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.discipline == 2" disabled
                      >良好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.discipline == 2">{{
                      info.disciplineRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.discipline == 3" disabled
                      >差</el-checkbox
                    >
                    <span class="text-mark" v-if="info.discipline == 3">{{
                      info.disciplineRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">4.是否节能减排</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.reduction == 1" disabled
                      >是</el-checkbox
                    >
                    <span class="text-mark" v-if="info.reduction == 1">{{
                      info.reductionRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.reduction == 2" disabled
                      >否</el-checkbox
                    >
                    <span class="text-mark" v-if="info.reduction == 2">{{
                      info.reductionRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">5.人员执勤情况</th>

                  <td colspan="4">
                    <el-checkbox v-model="info.persDuty == 1" disabled
                      >好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.persDuty == 1">{{
                      info.persDutyRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.persDuty == 2" disabled
                      >良好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.persDuty == 2">{{
                      info.persDutyRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.persDuty == 3" disabled
                      >差</el-checkbox
                    >
                    <span class="text-mark" v-if="info.persDuty == 3">{{
                      info.persDutyRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">6.是否按规定使用执法记录仪</th>

                  <td colspan="4">
                    <el-checkbox v-model="info.cameraUse == 1" disabled
                      >好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.cameraUse == 1">{{
                      info.cameraUseRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.cameraUse == 2" disabled
                      >良好</el-checkbox
                    >
                    <span class="text-mark" v-if="info.cameraUse == 2">{{
                      info.cameraUseRmks
                    }}</span>
                    &nbsp;
                    <el-checkbox v-model="info.cameraUse == 3" disabled
                      >差</el-checkbox
                    >
                    <span class="text-mark" v-if="info.cameraUse == 3">{{
                      info.cameraUseRmks
                    }}</span>
                  </td>
                </tr>

                <tr>
                  <th>备注</th>
                  <td colspan="4">{{ info.rmks }}</td>
                </tr>
                <tr>
                  <th>图片</th>
                  <td colspan="4">
                    <el-image
                      class="ill-img ill-pics"
                      v-for="(item, index) in info.imgs"
                      :key="index"
                      :src="item"
                      :preview-src-list="info.imgs"
                    >
                    </el-image>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getRegistrationInfo } from "@/api/registrationCheck";
export default {
  name: "RegistrationInfo",
  data() {
    return {
      visible: false,
      detailLoading: false,
      id: "",
      info: {},
      title: "" //弹窗名称
    };
  },
  computed: {},
  methods: {
    init(row) {
      this.title = row.regPot || "";
      this.id = row.id;
      this.visible = true;
      this.$nextTick(() => {
        if (this.id) {
          this.getInfo(this.id);
        }
      });
    },
    getInfo(id) {
      this.detailLoading = true;
      getRegistrationInfo(id)
        .then(res => {
          this.detailLoading = false;
          if (res.code == 0) {
            this.$set(this, "info", res.data);

            if (this.info.imgs) {
              this.info.imgs = this.info.imgs.split(",");
            }
          } else {
            this.info = {};
          }
        })
        .catch(err => {
          this.info = {};
          this.detailLoading = false;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.ill-img {
  /deep/ .el-icon-circle-close {
    color: white;
  }
}
.ill-pics {
  width: 160px;
  height: 160px;
  margin: 10px 5px 0;
  border: 2px solid rgb(177, 176, 176);
  border-radius: 8px;
}

.custom-table {
  th {
    min-width: 150px;
  }
  td {
    min-width: 250px;
  }
  .sub-title {
    text-align: left !important;
  }

  .limit-img-width /deep/ {
    img {
      width: 100px;
      height: 100px;
      display: block;
      // max-width: 500px !important;
    }
  }
}

.el-checkbox /deep/ {
  margin-right: 10px;

  .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #333;
  }

  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #fff;
  }
}
.print-panel .print-panel-body .custom-table tbody th /deep/ {
  text-align: center;
}

.text-mark {
  text-decoration: underline;
  color: #333;
}
</style>
