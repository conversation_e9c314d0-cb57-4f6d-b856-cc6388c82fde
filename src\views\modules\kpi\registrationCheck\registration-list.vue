<!--
 * @Desc: 闭环处置 > 登记点检查
 * @Author: fanls
 * @Date: 2023-10-11
-->

<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <el-button slot="button" icon="el-icon-plus" type="primary" size="small" @click="add">新增</el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable"
      style="width: 100%;" :max-height="tableHeight" @sort-change="handleSort">
      <el-table-column type="index" label="序号" width="50" align="center">
      </el-table-column>
      <el-table-column prop="regPot" label="登记点名称" align="center">
      </el-table-column>
      <el-table-column prop="inspectors" label="检查人员" align="center">
      </el-table-column>
      <el-table-column prop="rmks" label="备注" header-align="center"></el-table-column>
      <el-table-column prop="inspTm" label="检查时间" align="center" />

      <el-table-column prop="crtTm" label="提交时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.crtTm | FormatDate("yyyy-MM-dd HH:mm") }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="showDetails(scope.row)">详情</el-button>
          <el-button v-if="username == scope.row.crtBy" type="text" @click="update(scope.row)">编辑</el-button>
          <el-button v-if="username == scope.row.crtBy" type="text" @click="del(scope.row.id)">删除</el-button>
          <el-button type="text" @click="exportTable(scope.row)">导出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf"></div>
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>

    <!-- 新增、编辑 弹框 -->
    <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" width="1400px">
      <el-form class="ill-form" style="margin-top: 20px" ref="dataForm" label-width="240px" :model="dataForm" size="small"
        :rules="rules">
        <el-form-item label="登记点点位选择:" prop="regPot" :rules="$rulesFilter({ required: true })">
          <el-select filterable clearable v-model="dataForm.regPot" placeholder="请选择登记点点位">
            <el-option v-for="(item, index) in registrationPointList" :key="index" :label="item.regPot"
              :value="item.regPot"></el-option>
          </el-select>
        </el-form-item>
        <div class="flex-box-roll">
          <el-form-item label="检查人员:" prop="inspectors" :rules="$rulesFilter({ required: true })">
            <el-select multiple clearable v-model="dataForm.inspectors" placeholder="请选择检查人员姓名">
              <el-option v-for="(item, index) in persNmList" :key="index" :label="item.label" :value="item.value">
              </el-option></el-select>
          </el-form-item>

          <el-form-item label="检查时间:" prop="inspTm" :rules="$rulesFilter({ required: true })">
            <el-date-picker v-model="dataForm.inspTm" type="datetime" value-format="yyyy-MM-dd HH:mm" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="人员到岗情况:" prop="persArrival" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.persArrival" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in persArrivalContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="人员到岗情况备注:" prop="persArrivalRmks" :rules="$rulesFilter({
            required: dataForm.persArrival == 1 ? false : true
          })
            ">
            <el-input type="textarea" v-model="dataForm.persArrivalRmks" placeholder="请备注人员到岗情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="办公区、内务、厕所等卫生情况:" prop="sanitary" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.sanitary" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in sanitaryContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="卫生情况备注:" prop="sanitaryRmks" :rules="$rulesFilter({ required: dataForm.sanitary == 1 ? false : true })
            ">
            <el-input type="textarea" v-model="dataForm.sanitaryRmks" placeholder="请备注卫生情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="警容风纪情况:" prop="discipline" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.discipline" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in sanitaryContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="警容风纪情况备注:" prop="disciplineRmks" :rules="$rulesFilter({
            required: dataForm.discipline == 1 ? false : true
          })
            ">
            <el-input type="textarea" v-model="dataForm.disciplineRmks" placeholder="请备注警容风纪情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="是否节能减排:" prop="reduction" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.reduction" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in reductionContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="节能减排情况备注:" prop="reductionRmks" :rules="$rulesFilter({ required: dataForm.reduction == 1 ? false : true })
            ">
            <el-input type="textarea" v-model="dataForm.reductionRmks" placeholder="请备注节能减排情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="人员执勤情况:" prop="persDuty" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.persDuty" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in sanitaryContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="人员执勤情况备注:" prop="persDutyRmks" :rules="$rulesFilter({ required: dataForm.persDuty == 1 ? false : true })
            ">
            <el-input type="textarea" v-model="dataForm.persDutyRmks" placeholder="请备注人员执勤情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <div class="flex-box-roll">
          <el-form-item label="按规定使用执法记录仪情况:" prop="cameraUse" :rules="$rulesFilter({ required: true })">
            <el-radio-group v-model="dataForm.cameraUse" size="medium">
              <el-radio :label="item.value" v-for="(item, index) in sanitaryContent" :key="index">{{ item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="使用执法记录仪情况备注:" prop="cameraUseRmks" :rules="$rulesFilter({ required: dataForm.cameraUse == 1 ? false : true })
            ">
            <el-input type="textarea" v-model="dataForm.cameraUseRmks" placeholder="请备注使用执法记录仪情况" :rows="1"></el-input>
          </el-form-item>
        </div>

        <el-form-item label="现场照片:" prop="imgs">
          <el-button type="success" @click="uploadImg" icon="el-icon-full-screen">扫码上传</el-button>
          <el-upload action="" accept="'png', 'gif', 'jpg', 'jpeg'" :on-remove="handleRemove"
            :on-preview="handlePictureCardPreview" :on-change="handleChange" list-type="picture-card" :auto-upload="false"
            :limit="10" :file-list="fileList">
            <i slot="default" class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :modal-append-to-body="false" :append-to-body="true" :visible.sync="dialogVisible" top="12vh">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>

        <el-form-item label="备注:" prop="rmks">
          <el-input placeholder="请输入备注" type="textarea" v-model="dataForm.rmks" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button type="primary" size="small" :loading="subLoading" @click="submit">提交</el-button>
      </span>
    </el-dialog>

    <!-- 详情 弹框 -->
    <info ref="info" v-if="infoVisible"></info>

    <!-- 二维码扫描上传磅单 -->
    <el-dialog width="500px" title="请用手机扫描二维码现场照片" :visible.sync="picVisible">
      <div ref="qrcode" align="center" title="上传现场照片" />
      <div align="center">
        <p style="padding: 10px">
          如已上传完毕，请点击下方 “
          <span style="color: #409eff">我已上传</span>
          ” 按钮
        </p>
        <el-button type="primary" @click="getPicInfoHandle()">我已上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/registrationCheck";
import { getPicsInfo, uploadSign, getPersList } from "@/api/transEntpCheck";
import Info from "./registration-info.vue";
import fileUpload from "@/components/FileUpload";
import QRCode from "qrcodejs2";
import Viewer from "viewerjs";
import { mapGetters } from "vuex";

export default {
  components: {
    Searchbar,
    Info,
    fileUpload
  },
  data: function () {
    return {
      todayStamp: "", //当前日期时间戳
      tableHeight: 500,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "登记点名称",
            field: "regNumber",
            type: "select",
            dbfield: "reg_number",
            dboper: "cn",
            options: []
          },
          {
            name: "检查时间",
            field: "inspTm",
            type: "daterange",
            dbfield: "insp_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm",
            default: []
          }
        ],
        more: []
      },

      pagination: {
        page: 1,
        limit: 15,
        total: 0
      },

      visible: false,
      dataForm: {
        cameraUseRmks: null,
        disciplineRmks: null,
        persArrivalRmks: null,
        persDutyRmks: null,
        reductionRmks: null,
        sanitaryRmks: null,
        imgs: [], //现场照片
        regPot: "",
        inspectors: "",
        inspTm: "",
        persArrival: "",
        sanitary: "",
        discipline: "",
        reduction: "",
        persDuty: "",
        cameraUse: "",
        rmks: ""
      },
      rules: {
        // inspectors: [
        //   {
        //     required: true,
        //     pattern: /^[\u4e00-\u9fa5a-zA-Z0-9|\u3001]+$/,
        //     message: "多个人员姓名之间用顿号(、)隔开",
        //     trigger: "change"
        //   }
        // ]
      },

      infoVisible: false, //详情弹窗
      title: "", //新增编辑弹窗
      subLoading: false, //提交按钮
      registrationPointList: [], //登记点点位列表
      persArrivalContent: [
        { label: "全员到岗", value: 1 },
        { label: "到岗不全", value: 2 }
      ], //人员到岗情况
      sanitaryContent: [
        { label: "好", value: 1 },
        { label: "良好", value: 2 },
        { label: "差", value: 3 }
      ], //卫生情况、警容风纪情况、人员执勤情况、是否按规定使用执法记录仪
      reductionContent: [
        { label: "是", value: 1 },
        { label: "否", value: 2 }
      ], //节能减排
      // 上传图片
      dialogImageUrl: "",
      dialogVisible: false,
      fileList: [],
      // 检查人员 2024-05-30 persNmList已走后端接口，通过数据字典alarmDisposalUserList获取
      persNmList: [],
      // persNmList: [
      //   {
      //     label: "庄益",
      //     value: "庄益"
      //   },
      //   {
      //     label: "潘春立",
      //     value: "潘春立"
      //   },
      //   {
      //     label: "陈瑞荣",
      //     value: "陈瑞荣"
      //   },
      //   {
      //     label: "孙仕良",
      //     value: "孙仕良"
      //   },
      //   {
      //     label: "陆忠伟",
      //     value: "陆忠伟"
      //   },
      //   {
      //     label: "周赫男",
      //     value: "周赫男"
      //   },
      //   {
      //     label: "赵波章",
      //     value: "赵波章",
      //   },
      // ],
      picVisible: false,
      uuid: null
    };
  },
  computed: {
    ...mapGetters(["username"])
  },
  mounted: function () {
    console.log("username", this.username);
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
    this.getRegPots();
    this.getPersNmList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    async getPersNmList() {
      let res = await getPersList().catch(e => { console.log(e) })
      if (res && res.code == 0) {
        this.persNmList = (res.data || []).map(it => {
          return { label: it.nmCn, value: it.cd };
        });;
      } else {
        this.persNmList = [];
      }
    },
    getList: function (data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      // filters.rules.push({
      //   field: "sys_id",
      //   data: "330211",
      //   op: "eq"
      // });

      sortParam = sortParam || {};
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getRegistrationList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 登记点点位选择
    getRegPots() {
      $http.getRegPots().then(res => {
        if (res.code == 0) {
          this.registrationPointList = res.data;
          this.searchItems.normal[0].options = res.data.map(item => {
            return { label: item.regPot, value: item.regNumber };
          });
        } else {
          this.registrationPointList = [];
        }
      });
    },
    // 新增
    add() {
      let self = this;

      this.title = "新增";
      this.visible = true;

      this.$nextTick(() => {
        if (this.$refs.dataForm) {
          this.fileList = [];
          self.$refs["dataForm"].clearValidate();
          this.dataForm = this.$options.data().dataForm;
          self.$refs["dataForm"].resetFields();
          this.dataForm.imgs = [];
        }
      });
    },

    // 详情
    showDetails(row) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info.init(row);
      });
    },
    // 编辑
    update(data) {
      this.title = "编辑";
      this.visible = true;
      this.fileList = [];
      this.dataForm = Object.assign({}, data);

      // 编辑时，将检查人员的格式转换为数组形式，避免输入框置空
      if (this.dataForm.inspectors) {
        this.dataForm.inspectors = this.dataForm.inspectors.split("、");
      }

      if (this.dataForm.imgs) {
        this.dataForm.imgs.split(",").forEach(item => {
          this.fileList.push({ url: item });
        });
        let type = Object.prototype.toString.apply(this.dataForm.imgs);

        if (type === "[object String]") {
          this.dataForm.imgs = this.dataForm.imgs.split(",");
        } else if (type === "[object Array]") {
          this.dataForm.imgs = this.dataForm.imgs;
        } else {
          this.dataForm.imgs = this.dataForm.imgs;
        }
      }
    },
    // 清空数据
    // beforePersArrivalEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.persArrivalRmks = "";
    //   }
    // },
    // beforeSanitaryEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.sanitaryRmks = "";
    //   }
    // },
    // beforeDdisciplineEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.disciplineRmks = "";
    //   }
    // },
    // beforeReductionEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.reductionRmks = "";
    //   }
    // },
    // beforePersDutyEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.persDutyRmks = "";
    //   }
    // },
    // beforeCameraUseEvent(value) {
    //   if (value == 1) {
    //     this.dataForm.cameraUseRmks = "";
    //   }
    // },
    //提交
    submit() {
      let self = this;
      let list = [];
      list = this.dataForm;

      // 获取登记点number
      let regNum = this.registrationPointList.filter(item => {
        return item.regPot == this.dataForm.regPot;
      });
      list.regNumber = regNum[0].regNumber;

      // 新增编辑 检查人员姓名用、拼接
      list.inspectors = this.dataForm.inspectors.join("、");

      if (this.dataForm.imgs) {
        let type = Object.prototype.toString.apply(this.dataForm.imgs);

        if (type === "[object String]") {
          list.imgs = self.dataForm.imgs;
        } else if (type === "[object Array]") {
          list.imgs = self.dataForm.imgs.join(",");
        } else {
          list.imgs = self.dataForm.imgs;
        }
      }

      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.subLoading = true;
          $http[this.title == "新增" ? "saveData" : "updateData"](list)
            .then(res => {
              if (res && res.code == 0) {
                // 提交成功时，将检查人的格式仍转换为数组形式，避免输入框置空
                if (this.dataForm.inspectors) {
                  self.dataForm.inspectors = self.dataForm.inspectors.split(
                    "、"
                  );
                }
                this.$message({
                  message: this.title + "成功",
                  type: "success",
                  duration: 2000,
                  onClose: () => {
                    this.subLoading = false;
                    self.visible = false;
                    self.getList();
                  }
                });
              } else {
                this.subLoading = false;
              }
            })
            .catch(() => {
              this.subLoading = false;
            });
        } else {
          // 未提交成功时，将检查人的格式仍转换为数组形式，避免输入框置空
          if (this.dataForm.inspectors) {
            self.dataForm.inspectors = self.dataForm.inspectors.split("、");
          }
          console.log("error submit!!");
          return false;
        }
      });
    },

    // 删除
    del(id) {
      let _this = this;
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http.deleteData([id]).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
                duration: 2000,
                onClose: () => {
                  _this.getList();
                }
              });
            }
          });
        })
        .catch(() => { });
    },
    // 导出
    exportTable(row) {
      $http.exportWord(row.id).then(res => {
        let dataTime = Tool.formatDate(row.inspTm, "yyyy年MM月dd日HH时mm分");
        let link = document.createElement("a");
        let blob = new Blob([res]);

        let url = window.URL.createObjectURL(blob);
        link.style.display = "none";
        // link.href = url;

        link.href = url;
        link.download = `${row.regPot}` + "_" + dataTime + ".docx";
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);

        this.$message({
          message: "导出成功！",
          type: "success"
        });
      });
    },

    //现场照片调取二维码上传
    getPicInfoHandle() {
      // console.log("扫码上传");
      getPicsInfo({ key: this.uuid })
        .then(response => {
          if (response.code == 0) {
            let data = response.data;
            //手机上传回调
            if (data) {
              this.dataForm.imgs.push(data);
              this.picVisible = false;

              data.split(",").forEach(item => {
                this.fileList.push({ url: item });
              });
            } else {
              this.$message({
                type: "error",
                message: "请用手机扫码上传现场图片"
              });
            }
          }
        })
        .catch(error => { });
    },
    //现场照片调取手机拍照上传二维码
    uploadImg() {
      this.picVisible = true;
      this.$nextTick(() => {
        this.createdQRCode();
      });
    },
    createdQRCode() {
      let _this = this;
      // let apiHost = "https://stag-zhys3.dacyun.com";
      let apiHost = process.env.VUE_APP_zzdLoginUrl;
      apiHost = apiHost.split("/gov")[0];
      this.$refs.qrcode.innerHTML = "";
      this.uuid = Tool.getUUID();

      new QRCode(this.$refs.qrcode, {
        text: `${apiHost}/h5/check/?id=${_this.uuid}&token=${_this.$store.state.user.token}&regPot=${_this.dataForm.regPot}`,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L
      });
      this.$refs.qrcode.title = "";
    },

    // 前端导出配置
    exportConfig(row) {
      const header1 = [
        "稽查大队危化车辆登记点检查记录表",
        "",
        "",
        "",
        "",
        "",
        "",
        ""
      ];
      const header2 = ["检查点位", "", "", "", "", "", "", ""];
      const header3 = ["检查人员", "", "", "", "检查时间", "", "", ""];
      const header4 = [
        "检查内容",
        "1.人员到岗情况",
        "",
        "",
        "全员到岗",
        "到岗不全",
        "",
        ""
      ];
      const header5 = [
        "",
        "2.办公区、内务、厕所等卫生情况",
        "",
        "",
        "好",
        "良好",
        "差",
        ""
      ];
      const header6 = ["", "3.警容风纪情况", "", "", "好", "良好", "差", ""];
      const header7 = ["", "4.是否节能减排", "", "", "是", "否", "", ""];
      const header8 = ["", "5.人员执勤情况", "", "", "好", "良好", "差", ""];
      const header9 = [
        "",
        "6.是否按规定使用执法记录仪",
        "",
        "",
        "是",
        "否",
        "",
        ""
      ];
      const header10 = ["备注", "", "", "", "", "", "", ""];
      const header11 = ["现场照片", "", "", "", "", "", "", ""];
      // merges的参数说明：【开始行，开始列，合并单元格的行数，合并单元格的列数】
      let merges = [
        { row: 0, col: 0, rowspan: 1, colspan: 8 },
        { row: 1, col: 1, rowspan: 1, colspan: 7 },
        { row: 2, col: 1, rowspan: 1, colspan: 3 },
        { row: 2, col: 5, rowspan: 1, colspan: 4 },
        { row: 3, col: 0, rowspan: 6, colspan: 1 },
        { row: 3, col: 1, rowspan: 1, colspan: 3 },
        { row: 3, col: 5, rowspan: 1, colspan: 3 },
        { row: 4, col: 1, rowspan: 1, colspan: 3 },
        { row: 4, col: 6, rowspan: 1, colspan: 2 },
        { row: 5, col: 1, rowspan: 1, colspan: 3 },
        { row: 5, col: 6, rowspan: 1, colspan: 2 },
        { row: 6, col: 1, rowspan: 1, colspan: 3 },
        { row: 6, col: 5, rowspan: 1, colspan: 3 },
        { row: 7, col: 1, rowspan: 1, colspan: 3 },
        { row: 7, col: 6, rowspan: 1, colspan: 2 },
        { row: 8, col: 1, rowspan: 1, colspan: 3 },
        { row: 8, col: 5, rowspan: 1, colspan: 3 },
        { row: 9, col: 1, rowspan: 1, colspan: 7 },
        // { row: 9, col: 0, rowspan: 4, colspan: 1 },
        { row: 10, col: 1, rowspan: 1, colspan: 7 }
        // { row: 10, col: 2, rowspan: 4, colspan: 4 }
      ];
      const config = {
        data: [row],
        fields: [
          "",
          "regPot"
          // "reason",
          // "startTm",
          // "expireTm",
          // "expireTm",
          // "expireTm",
          // "expireTm",
          // "expireTm"
        ],
        headers: [
          header1,
          header2,
          header3,
          header4,
          header5,
          header6,
          header7,
          header8,
          header9,
          header10,
          header11
        ],
        merges: merges,
        attrs: [],
        columnsWidth: [20, 20, 30, 30, 30],
        sheetName: row.regPot + "危化车辆检查记录表"
      };

      // 设置单元格样式
      config.attrs.push({
        rowStart: 0,
        rowEnd: config.data.length - 1,
        // rowEnd: 0,

        colStart: 0,
        colEnd: config.fields.length,

        attr: {
          alignment: { vertical: "middle", horizontal: "center" },
          border: {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" }
          }
        }
      });
      // 设置表头填充颜色，字体加粗
      config.attrs.push({
        rowStart: 0,
        rowEnd: 0,
        colStart: 0,
        colEnd: config.fields.length - 1,
        attr: {
          fill: {
            type: "pattern",
            pattern: "solid"
            // fgColor: { argb: "99CCFF" }
          },
          font: {
            bold: true
            // fontSize: 24
          }
        }
      });
      return config;
    },

    // 移除现场照片
    handleRemove(file, fileList) {
      const index = this.fileList.findIndex(item => {
        return item.uid === file.uid;
      });
      let type = Object.prototype.toString.apply(this.dataForm.imgs);
      if (type === "[object String]") {
        this.dataForm.imgs = this.dataForm.imgs.split(",");
      }

      this.dataForm.imgs.splice(index, 1);
      this.fileList.splice(index, 1);
    },
    // 图片预览
    handlePictureCardPreview: function (file, index) {
      // this.dialogImageUrl = file.url;
      // this.dialogVisible = true;

      let url = file.url;

      const divNode = document.createElement("div");
      divNode.style.display = "none";
      const imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        url(image) {
          let src = image.src;
          if (/\@\w+\.src$/.test(src)) {
            src = src.replace(/\@\w+\.src$/, "") + "";
          }
          if (/\@\w+$/.test(src)) {
            src = src.replace(/@\S+/, "") + "@2o";
          }
          return src;
        },
        zIndex: 2999,
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    handleChange(file, fileList) {
      let _this = this;
      let reader = new FileReader();
      // 将图片将转成 base64 格式
      reader.readAsDataURL(file.raw);
      reader.onload = function () {
        let imgsUrl = reader.result.split(",")[1];
        _this.uploadSign(imgsUrl, fileList);
      };
    },
    // 图片转化
    uploadSign(url) {
      let params = { signBase64: url };
      let self = this;
      uploadSign(params)
        .then(res => {
          if (res.code == 0) {
            let urlArr = res.data.map(item => {
              return item.fileUrl;
            });
            self.fileList.push({ url: urlArr[0] });

            self.dataForm.imgs.push(urlArr[0]);
          }
        })
        .catch(error => console.log(error));
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    }
  }
};
</script>

<style scoped>
.ill-form .el-radio {
  margin-bottom: 10px;
}

.ill-form .flex-box-roll {
  display: flex;
}

.ill-form .flex-box-roll>* {
  width: 90%;
}

.ill-img {
  box-sizing: border-box;
  padding: 10px 10px 0 0;
}

.ill-img /deep/ .el-icon-circle-close {
  color: white;
}
</style>
