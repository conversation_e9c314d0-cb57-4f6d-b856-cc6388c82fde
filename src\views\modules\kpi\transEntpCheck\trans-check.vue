<!--
 * @Desc: 闭环处置 > 运输企业检查
 * @Author: fanls
 * @Date: 2023-10-11
-->

<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <el-button slot="button" icon="el-icon-plus" type="primary" size="small" @click="add">新增</el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable"
      style="width: 100%;" :max-height="tableHeight" @sort-change="handleSort">
      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="entpName" label="企业名称" header-align="center" min-width="140">
      </el-table-column>
      <el-table-column prop="visitor" label="走访人" align="center" min-width="140" />
      <el-table-column prop="interviewDate" label="走访日期" align="center" min-width="100"></el-table-column>
      <el-table-column prop="crtTm" label="提交日期" align="center" min-width="110">
        <template slot-scope="scope">
          {{ scope.row.crtTm | formatDate("yyyy-MM-dd hh:mm") }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="rmks"
        label="备注"
        min-width="180px"
        header-align="center"
      ></el-table-column> -->

      <el-table-column label="操作" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="showDetails(scope.row)">详情</el-button>
          <el-button v-if="username == scope.row.crtBy" type="text" @click="update(scope.row)">编辑</el-button>
          <el-button v-if="username == scope.row.crtBy" type="text" @click="del(scope.row.id)">删除</el-button>
          <el-button type="text" @click="exportTable(scope.row)">导出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf"></div>
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>

    <!-- 新增/编辑 弹框 -->
    <el-dialog :title="dialogTitle" :visible.sync="visible" :close-on-click-modal="false" width="1600px">
      <el-form class="ill-form" style="margin-top: 20px;padding:0 100px" ref="dataForm" label-width="300px"
        :model="dataForm" size="small" :rules="rules">
        <el-form-item label="企业名称" prop="entpName" :rules="$rulesFilter({ required: true })">
          <el-select v-model="dataForm.entpName" value-key="ip_pk" filterable remote reserve-keyword placeholder="请选择企业名称"
            :remote-method="getEntpList" size="mini">
            <el-option v-for="(item, index) in entpList" :key="item.ip_pk" :label="item.entp_name"
              :value="item.entp_name"></el-option>
          </el-select>
        </el-form-item>

        <div class="flex-box-roll">
          <el-form-item label="走访日期:" prop="interviewDate" :rules="$rulesFilter({ required: true })">
            <el-date-picker v-model="dataForm.interviewDate" type="date" value-format="yyyy-MM-dd" placeholder="选择走访日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="走访人:" prop="visitor" :rules="$rulesFilter({ required: true })">
            <el-select multiple clearable v-model="dataForm.visitor" placeholder="请选择走访人姓名">
              <el-option v-for="(item, index) in persNmList" :key="index" :label="item.label" :value="item.value">
              </el-option></el-select>
          </el-form-item>
        </div>
        <el-collapse v-model="activeNames">
          <el-collapse-item title="企业管理" name="1">
            <div class="flex-box-roll">
              <el-form-item label="安全管理制度健全，部门、人员、工作制度:" prop="entpSafety" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.entpSafety" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in entpSafetyContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="entpSafetyRmks" :rules="$rulesFilter({
                required: dataForm.entpSafety == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.entpSafetyRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="内部管理台账是否健全:" prop="entpStandingBook" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.entpStandingBook" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in entpSafetyContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="entpStandingBookRmks" :rules="$rulesFilter({
                required: dataForm.entpStandingBook == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.entpStandingBookRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="是否落实动态监控制度，设备有效:" prop="entpMonitor" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.entpMonitor" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in ententpMonitorContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="entpMonitorRmks" :rules="$rulesFilter({
                required: dataForm.entpMonitor == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.entpMonitorRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>
          </el-collapse-item>
          <el-collapse-item title="车辆管理" name="2">
            <div class="flex-box-roll">
              <el-form-item label="是否存在与车辆登记不符(改装)危化品车辆:" prop="vecRegist" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.vecRegist" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in vecRegistContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="vecRegistRmks" :rules="$rulesFilter({
                required: dataForm.vecRegist == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.vecRegistRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="是否使用卫星定位装置车辆:" prop="vecBds" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.vecBds" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in vecBdsContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="vecBdsRmks" :rules="$rulesFilter({
                required: dataForm.vecBds == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.vecBdsRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="逾期未报废、未年检危化品运输车辆:" prop="vecAnnualInsp" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.vecAnnualInsp" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in vecRegistContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="vecAnnualInspRmks" :rules="$rulesFilter({
                required: dataForm.vecAnnualInsp == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.vecAnnualInspRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="车辆违法清零情况:" prop="vecIllegal" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.vecIllegal" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in vecAnnualInspContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="vecIllegalRmks" :rules="$rulesFilter({
                required: dataForm.vecIllegal == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.vecIllegalRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>
          </el-collapse-item>
          <el-collapse-item title="人员管理" name="3">
            <div class="flex-box-roll">
              <el-form-item label="驾驶员档案管理及从业资格审核:" prop="persArchives" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.persArchives" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in persArchivesContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="persArchivesRmks" :rules="$rulesFilter({
                required: dataForm.persArchives == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.persArchivesRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="参加交通安全教育培训落实情况:" prop="persTrain" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.persTrain" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in persTrainContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="persTrainRmks" :rules="$rulesFilter({
                required: dataForm.persTrain == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.persTrainRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="驾驶员违法清零情况:" prop="persIllegalClearing" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.persIllegalClearing" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in vecAnnualInspContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="persIllegalClearingRmks" :rules="$rulesFilter({
                required: dataForm.persIllegalClearing == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.persIllegalClearingRmks" placeholder="请输入"
                  :rows="1"></el-input>
              </el-form-item>
            </div>

            <div class="flex-box-roll">
              <el-form-item label="驾驶员违法违规内部处置落实情况:" prop="persDispose" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dataForm.persDispose" size="medium">
                  <el-radio :label="item.value" v-for="(item, index) in persTrainContent" :key="index">{{ item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="具体情况:" prop="persDisposeRmks" :rules="$rulesFilter({
                required: dataForm.persDispose == 1 ? false : true
              })
                ">
                <el-input type="textarea" v-model="dataForm.persDisposeRmks" placeholder="请输入" :rows="1"></el-input>
              </el-form-item>
            </div>
          </el-collapse-item>
        </el-collapse>

        <!-- <el-form-item label="备注:" prop="rmks">
          <el-input
            placeholder="请输入备注"
            type="textarea"
            v-model="dataForm.rmks"
            :rows="4"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="现场照片:" prop="imgs" :rules="$rulesFilter({ required: true })">
          <el-button type="success" @click="uploadImg" icon="el-icon-full-screen">扫码上传</el-button>
          <el-upload action="" accept="'png', 'gif', 'jpg', 'jpeg'" :on-remove="handleRemoveImgs"
            :on-preview="handlePictureCardPreviewImgs" :on-change="handleChangeImgs" list-type="picture-card"
            :auto-upload="false" :limit="10" :file-list="fileListImgs">
            <i slot="default" class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :modal-append-to-body="false" :append-to-body="true" :visible.sync="imgsDialogVisible" top="12vh">
            <img width="100%" :src="imgsDialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="负责人签名照:" prop="sign">
          <!-- <file-upload
            v-model="dataForm.sign"
            :acceptFileType="['png', 'gif', 'jpg', 'jpeg']"
            :multiple="false"
          ></file-upload> -->
          <el-upload action="" accept="'png', 'gif', 'jpg', 'jpeg'" :on-remove="handleRemove"
            :on-preview="handlePictureCardPreview" :on-change="handleChange" list-type="picture-card" :auto-upload="false"
            :limit="10" :file-list="fileList">
            <i slot="default" class="el-icon-plus"></i>

            <div class="el-upload__tip" slot="tip">
              <span>只能上传 'png', 'gif', 'jpg', 'jpeg' 格式文件<span>，最多上传10个</span></span>
            </div>
          </el-upload>

          <el-dialog :modal-append-to-body="false" :append-to-body="true" :visible.sync="dialogVisible" top="12vh">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
          <!-- <upload-images
            ref="uploadImagesNode"
            :data-source="dataForm.sign"
            operType="edit"
          /> -->
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button type="primary" size="small" :loading="subLoading" @click="submit">提交</el-button>
      </span>
    </el-dialog>

    <!-- 详情 弹框 -->
    <info ref="info" v-if="infoVisible"></info>

    <!-- 二维码扫描上传磅单 -->
    <el-dialog width="500px" title="请用手机扫描二维码现场照片" :visible.sync="picVisible">
      <div ref="qrcode" align="center" title="上传现场照片" />
      <div align="center">
        <p style="padding: 10px">
          如已上传完毕，请点击下方 “
          <span style="color: #409eff">我已上传</span>” 按钮
        </p>
        <el-button type="primary" @click="getPicInfoHandle()">我已上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/transEntpCheck";
import Info from "./trans-info.vue";
import fileUpload from "@/components/FileUpload";
import UploadImages from "@/components/UploadImages";
import Viewer from "viewerjs";
import QRCode from "qrcodejs2";
import { mapGetters } from "vuex";

export default {
  components: {
    Searchbar,
    Info,
    fileUpload,
    UploadImages
  },
  data: function () {
    return {
      todayStamp: "", //当前日期时间戳
      tableHeight: 500,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "企业名称",
            field: "entpName",
            type: "selectSearch",
            dbfield: "entp_name",
            dboper: "eq",
            options: []
          },
          {
            name: "走访日期",
            field: "interviewDate",
            type: "daterange",
            dbfield: "interview_date",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd"
          }
        ],
        more: []
      },

      pagination: {
        page: 1,
        limit: 15,
        total: 0
      },

      visible: false,
      dataForm: {
        entpName: "", //
        interviewDate: "", //
        visitor: "", //
        entpSafety: "", //
        entpStandingBook: "", //
        entpMonitor: "", //
        vecRegist: "", //
        vecBds: "", //
        vecAnnualInsp: "", //
        vecIllegal: "", //
        persArchives: "", //
        persTrain: "", //
        persIllegalClearing: "", //
        persDispose: "", //
        rmks: "", //
        entpSafetyRmks: "", //缺失制度
        entpStandingBookRmks: "", //缺失台账
        entpMonitorRmks: "", //存在问题
        vecRegistRmks: null, //存在数量
        vecBdsRmks: null, //未安装使用车次
        vecAnnualInspRmks: null, //存在数量
        vecIllegalRmks: null, //未清零数量(车、次)
        persArchivesRmks: null, //不具备资格人数
        persTrainRmks: null, //次、人
        persIllegalClearingRmks: null, //未清零数量(人、次)
        persDisposeRmks: null, //次、人
        sign: [], //负责人签名
        imgs: []
        // status: 1 //状态(1.草稿,2.正式)
      },
      dialogTitle: "", //弹窗标题
      infoVisible: false, //详情弹窗
      subLoading: false, //提交按钮
      rules: {
        // visitor: [
        //   {
        //     required: true,
        //     pattern: /^[\u4e00-\u9fa5a-zA-Z0-9|\u3001]+$/,
        //     message: "多个人员姓名之间用顿号隔开",
        //     trigger: "change"
        //   }
        // ]
      },
      registrationPointList: [], //登记点点位列表
      activeNames: ["1", "2", "3"],
      entpSafetyContent: [
        { label: "健全", value: 1 },
        { label: "不健全", value: 2 }
      ], //安全制度、内部管理台账、
      ententpMonitorContent: [
        { label: "落实", value: 1 },
        { label: "未有效落实", value: 2 }
      ], //动态监控
      vecRegistContent: [
        { label: "不存在", value: 1 },
        { label: "存在", value: 2 }
      ], //车辆登记、逾期未报废
      vecBdsContent: [
        { label: "全部安装使用", value: 1 },
        { label: "部分安装使用", value: 2 }
      ], //卫星定位
      vecAnnualInspContent: [
        { label: "全部清零", value: 1 },
        { label: "未清零", value: 2 }
      ], //车辆违法清零、驾驶员违法清零
      persArchivesContent: [
        { label: "具备资格", value: 1 },
        { label: "不具备", value: 2 }
      ], //驾驶员档案管理及从业资格审核
      persTrainContent: [
        { label: "落实", value: 1 },
        { label: "未落实", value: 2 }
      ], //交通安全
      entpList: [], //企业名称列表
      // 上传签名
      dialogImageUrl: "",
      dialogVisible: false,
      fileList: [],
      // 走访人 2024-05-30 persNmList已走后端接口，通过数据字典alarmDisposalUserList获取
      persNmList: [],
      // persNmList: [
      //   {
      //     label: "庄益",
      //     value: "庄益"
      //   },
      //   {
      //     label: "潘春立",
      //     value: "潘春立"
      //   },
      //   {
      //     label: "陈瑞荣",
      //     value: "陈瑞荣"
      //   },
      //   {
      //     label: "孙仕良",
      //     value: "孙仕良"
      //   },
      //   {
      //     label: "陆忠伟",
      //     value: "陆忠伟"
      //   },
      //   {
      //     label: "周赫男",
      //     value: "周赫男"
      //   },
      //   {
      //     label: "赵波章",
      //     value: "赵波章",
      //   },
      // ],
      picVisible: false, //现场照片扫码上传
      imgsDialogImageUrl: "",
      imgsDialogVisible: false,
      fileListImgs: [],
      uuid: null
    };
  },
  computed: {
    ...mapGetters(["username"])
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
    this.getEntpList(); //企业名称下拉
    this.getPersNmList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    async getPersNmList() {
      let res = await $http.getPersList().catch(e => { console.log(e) })
      if (res && res.code == 0) {
        this.persNmList = (res.data || []).map(it => {
          return { label: it.nmCn, value: it.cd };
        });;
      } else {
        this.persNmList = [];
      }
    },
    getList: function (data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      // filters.rules.push({
      //   field: "sys_id",
      //   data: "330211",
      //   op: "eq"
      // });

      sortParam = sortParam || {};
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getTransChencList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    //获取企业列表
    getEntpList: function (query) {
      let params = { entpName: query };

      $http.getEntpList(params).then(response => {
        if (response.code == 0) {
          this.entpList = response.data;
          this.searchItems.normal[0].options = response.data.map(item => {
            return { value: item.entp_name, lable: item.ip_pk };
          });
        } else {
          this.entpList = [];
        }
      });
    },
    // 新增
    add() {
      let self = this;
      this.dialogTitle = "新增";

      this.visible = true;
      this.$nextTick(() => {
        this.fileListImgs = [];
        this.fileList = [];
        if (this.$refs.dataForm) {
          self.$refs["dataForm"].clearValidate();
          this.dataForm = this.$options.data().dataForm;
          self.$refs["dataForm"].resetFields();
        }
      });
    },

    // 详情
    showDetails(row) {
      this.infoVisible = true;
      this.$nextTick(() => {
        this.$refs.info.init(row);
      });
    },
    // 编辑
    update(data) {
      this.dialogTitle = "编辑";
      this.visible = true;
      this.fileList = [];
      this.fileListImgs = [];

      this.dataForm = Object.assign({}, data);

      // 编辑时，将走访人员的格式转换为数组形式，避免输入框置空
      if (this.dataForm.visitor) {
        this.dataForm.visitor = this.dataForm.visitor.split("、");
      }

      if (this.dataForm.sign) {
        this.dataForm.sign.split(",").forEach(item => {
          this.fileList.push({ url: item });
        });
        let type = Object.prototype.toString.apply(this.dataForm.sign);

        if (type === "[object String]") {
          this.dataForm.sign = this.dataForm.sign.split(",");
        } else if (type === "[object Array]") {
          this.dataForm.sign = this.dataForm.sign;
        } else {
          this.dataForm.sign = this.dataForm.sign;
        }
      }

      if (this.dataForm.imgs) {
        this.dataForm.imgs.split(",").forEach(item => {
          this.fileListImgs.push({ url: item });
        });
        let typeImgs = Object.prototype.toString.apply(this.dataForm.imgs);

        if (typeImgs === "[object String]") {
          this.dataForm.imgs = this.dataForm.imgs.split(",");
        } else if (typeImgs === "[object Array]") {
          this.dataForm.imgs = this.dataForm.imgs;
        } else {
          this.dataForm.imgs = this.dataForm.imgs;
        }
      }
    },

    //提交
    submit() {
      let _this = this;
      let list = [];
      list = this.dataForm;

      // 新增编辑 走访人姓名用、拼接
      list.visitor = this.dataForm.visitor.join("、");

      let type = Object.prototype.toString.apply(this.dataForm.sign);
      if (type === "[object String]") {
        list.sign = _this.dataForm.sign;
      } else if (type === "[object Array]") {
        list.sign = _this.dataForm.sign.join(",");
      } else {
        list.sign = _this.dataForm.sign;
      }

      let typeImgs = Object.prototype.toString.apply(this.dataForm.imgs);
      if (typeImgs === "[object String]") {
        _this.dataForm.imgs = _this.dataForm.imgs;
      } else if (typeImgs === "[object Array]") {
        _this.dataForm.imgs = _this.dataForm.imgs.join(",");
      } else {
        _this.dataForm.imgs = _this.dataForm.imgs;
      }

      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.subLoading = true;
          $http[this.dialogTitle == "新增" ? "saveData" : "updateData"](list)
            .then(res => {
              if (res && res.code == 0) {
                // 提交成功时，将走访人的格式仍转换为数组形式，避免输入框置空
                if (this.dataForm.visitor) {
                  _this.dataForm.visitor = _this.dataForm.visitor.split("、");
                }
                this.$message({
                  message: this.dialogTitle + "成功",
                  type: "success",
                  duration: 2000,
                  onClose: () => {
                    this.subLoading = false;
                    _this.visible = false;
                    _this.getList();
                  }
                });
              } else {
                _this.subLoading = false;
              }
            })
            .catch(() => {
              this.subLoading = false;
            });
        } else {
          // 提交失败时，将走访人的格式仍转换为数组形式，避免输入框置空
          if (this.dataForm.visitor) {
            _this.dataForm.visitor = _this.dataForm.visitor.split("、");
          }
          console.log("error submit!!");
          return false;
        }
      });
    },

    // 删除
    del(id) {
      let _this = this;
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http.deleteData([id]).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
                duration: 2000,
                onClose: () => {
                  _this.getList();
                }
              });
            }
          });
        })
        .catch(() => { });
    },
    // 导出
    exportTable(row) {
      $http.exportExcel(row.id).then(res => {
        let link = document.createElement("a");
        let blob = new Blob([res]);

        let url = window.URL.createObjectURL(blob);
        link.style.display = "none";
        // link.href = url;

        link.href = url;
        link.download =
          "危化运输企业走访情况登记表_" +
          `${row.entpName}` +
          "_" +
          row.interviewDate +
          ".xlsx";
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(url);

        this.$message({
          message: "导出成功！",
          type: "success"
        });
      });
    },

    // 移除现场照片
    handleRemoveImgs(file, fileList) {
      const index = this.fileListImgs.findIndex(item => {
        return item.uid === file.uid;
      });
      let type = Object.prototype.toString.apply(this.dataForm.imgs);
      if (type === "[object String]") {
        this.dataForm.imgs = this.dataForm.imgs.split(",");
      }

      this.dataForm.imgs.splice(index, 1);
      this.fileListImgs.splice(index, 1);
    },
    // 图片预览
    handlePictureCardPreviewImgs: function (file, index) {
      this.showImage(file.url);
    },
    handleChangeImgs(file, fileList) {
      let _this = this;
      let reader = new FileReader();
      // 将图片将转成 base64 格式
      reader.readAsDataURL(file.raw);
      reader.onload = function () {
        let imgsUrl = reader.result.split(",")[1];
        _this.uploadSignImgs(imgsUrl, fileList);
      };
    },
    // 现场图片转化
    uploadSignImgs(url) {
      let params = { signBase64: url };
      let self = this;
      $http
        .uploadSign(params)
        .then(res => {
          if (res.code == 0) {
            let urlArr = res.data.map(item => {
              return item.fileUrl;
            });
            self.fileListImgs.push({ url: urlArr[0] });
            self.dataForm.imgs.push(urlArr[0]);
          }
        })
        .catch(error => console.log(error));
    },

    //现场照片调取二维码上传
    getPicInfoHandle() {
      // console.log("扫码上传");
      $http
        .getPicsInfo({ key: this.uuid })
        .then(response => {
          if (response.code == 0) {
            let data = response.data;
            //手机上传回调
            if (data) {
              this.dataForm.imgs.push(data);
              this.picVisible = false;

              data.split(",").forEach(item => {
                this.fileListImgs.push({ url: item });
              });
            } else {
              this.$message({
                type: "error",
                message: "请用手机扫码上传现场图片"
              });
            }
          }
        })
        .catch(error => { });
    },
    //现场照片调取手机拍照上传二维码
    uploadImg() {
      this.picVisible = true;
      this.$nextTick(() => {
        this.createdQRCode();
      });
    },
    createdQRCode() {
      let _this = this;
      // let apiHost = "https://stag-zhys3.dacyun.com/gov";
      let apiHost = process.env.VUE_APP_zzdLoginUrl;
      apiHost = apiHost.split("/gov")[0];

      this.$refs.qrcode.innerHTML = "";
      this.uuid = Tool.getUUID();

      new QRCode(this.$refs.qrcode, {
        text: `${apiHost}/h5/check?id=${_this.uuid}&token=${this.$store.state.user.token}&entpName=${_this.dataForm.entpName}`,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.L
      });
      this.$refs.qrcode.title = "";
    },

    // 移除签名图片
    handleRemove(file, fileList) {
      const index = this.fileList.findIndex(item => {
        return item.uid === file.uid;
      });
      let type = Object.prototype.toString.apply(this.dataForm.sign);
      if (type === "[object String]") {
        this.dataForm.sign = this.dataForm.sign.split(",");
      }

      this.dataForm.sign.splice(index, 1);
      this.fileList.splice(index, 1);
    },
    // 预览签名
    handlePictureCardPreview: function (file, index) {
      this.showImage(file.url);
      // this.dialogImageUrl = file.url;
      // this.dialogVisible = true;
    },
    // 签名图片预览
    showImage(url) {
      const divNode = document.createElement("div");
      divNode.style.display = "none";
      const imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      let viewer = new Viewer(divNode, {
        url(image) {
          var src = image.src;
          if (/\@\w+\.src$/.test(src)) {
            src = src.replace(/\@\w+\.src$/, "") + "";
          }
          if (/\@\w+$/.test(src)) {
            src = src.replace(/@\S+/, "") + "@2o";
          }
          return src;
        },
        zIndex: 2999,
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    handleChange(file, fileList) {
      let _this = this;
      let reader = new FileReader();
      // 将图片将转成 base64 格式
      reader.readAsDataURL(file.raw);
      reader.onload = function () {
        let imgsUrl = reader.result.split(",")[1];
        _this.uploadSign(imgsUrl, fileList);
      };
    },
    // 签名图片转化
    uploadSign(url) {
      let params = { signBase64: url };

      $http
        .uploadSign(params)
        .then(res => {
          if (res.code == 0) {
            let urlArr = res.data.map(item => {
              return item.fileUrl;
            });

            this.fileList.push({ url: urlArr[0] });

            this.dataForm.sign.push(urlArr[0]);
          }
        })
        .catch(error => console.log(error));
    },
    // 清空数据
    // beforeEventEntpSafety(value) {
    //   if (value == 1) {
    //     this.dataForm.entpSafetyRmks = "";
    //   }
    // },
    // beforeEventEntpStandingBook(value) {
    //   if (value == 1) {
    //     this.dataForm.entpStandingBookRmks = "";
    //   }
    // },
    // beforeEventEntpMonitor(value) {
    //   if (value == 1) {
    //     this.dataForm.entpMonitorRmks = "";
    //   }
    // },
    // beforeEventVecRegist(value) {
    //   if (value == 1) {
    //     this.dataForm.vecRegistRmks = "";
    //   }
    // },
    // beforeEventVecBds(value) {
    //   if (value == 1) {
    //     this.dataForm.vecBdsRmks = "";
    //   }
    // },
    // beforeEventVecAnnualInsp(value) {
    //   if (value == 1) {
    //     this.dataForm.vecAnnualInspRmks = "";
    //   }
    // },
    // beforeEventVecIllegal(value) {
    //   if (value == 1) {
    //     this.dataForm.vecIllegalRmks = "";
    //   }
    // },
    // beforeEventPersArchives(value) {
    //   if (value == 1) {
    //     this.dataForm.persArchivesRmks = "";
    //   }
    // },
    // beforeEventPersTrain(value) {
    //   if (value == 1) {
    //     this.dataForm.persTrainRmks = "";
    //   }
    // },
    // beforeEventPersIllegalClearing(value) {
    //   if (value == 1) {
    //     this.dataForm.persIllegalClearingRmks = "";
    //   }
    // },
    // beforeEventPersDispose(value) {
    //   if (value == 1) {
    //     this.dataForm.persDisposeRmks = "";
    //   }
    // },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    }
  }
};
</script>

<style scoped>
.ill-form .el-radio {
  margin-bottom: 10px;
}

.ill-form .flex-box-roll {
  display: flex;
}

.ill-form .flex-box-roll>* {
  width: 90%;
}

.ill-img {
  box-sizing: border-box;
  padding: 10px 10px 0 0;
}

.ill-img /deep/ .el-icon-circle-close {
  color: white;
}
</style>
