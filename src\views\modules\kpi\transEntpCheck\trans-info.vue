<template>
  <el-dialog
    top="3vh"
    :title="title + '详情'"
    class="mod-info-container"
    :close-on-click-modal="false"
    :visible.sync="visible"
    :append-to-body="true"
    width="85%"
  >
    <div class="panel">
      <div class="panel-body">
        <div class="print-panel" v-loading="detailLoading">
          <!-- <div class="print-panel-header">
            <div class="panel-heading-content">
              <h3>{{ info.catNmCn }}处置详情</h3>
              <div
                v-show="info"
                style="font-weight: initial; font-size: 15px; line-height: 30px"
              >
                <div :title="'发现日期'">
                  {{ info.foundTm | FormatDate("yyyy年MM月dd日") }}
                </div>
              </div>
            </div>
          </div> -->
          <div class="print-panel-body">
            <table class="custom-table">
              <tbody>
                <tr>
                  <th>企业名称</th>
                  <td colspan="2">{{ info.entpName }}</td>
                  <th>走访日期</th>
                  <td colspan="2">{{ info.interviewDate }}</td>
                  <th>走访人</th>
                  <td colspan="1">{{ info.visitor }}</td>
                </tr>

                <tr>
                  <th></th>
                  <th colspan="2">走访内容</th>
                  <th colspan="3">走访结果</th>
                  <th colspan="3">具体情况</th>
                </tr>

                <tr>
                  <th rowspan="3">企业管理</th>
                  <th class="sub-title">
                    1.安全管理制度健全，部门、人员、工作制度
                  </th>
                  <td colspan="4">
                    <el-checkbox v-model="info.entpSafety == 1" disabled
                      >健全</el-checkbox
                    >
                    <el-checkbox v-model="info.entpSafety == 2" disabled
                      >不健全</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.entpSafetyRmks }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">2.内部管理台账</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.entpStandingBook == 1" disabled
                      >健全</el-checkbox
                    >
                    <el-checkbox v-model="info.entpStandingBook == 2" disabled
                      >不健全</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{
                      info.entpStandingBookRmks
                    }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">3.动态监控制度，设备有效</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.entpMonitor == 1" disabled
                      >落实</el-checkbox
                    >
                    <el-checkbox v-model="info.entpMonitor == 2" disabled
                      >未有效落实</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.entpMonitorRmks }}</span>
                  </td>
                </tr>

                <tr>
                  <th rowspan="4">车辆管理</th>
                  <th class="sub-title">
                    1.是否存在与车辆登记不符(改装)危化品车辆
                  </th>
                  <td colspan="4">
                    <el-checkbox v-model="info.vecRegist == 1" disabled
                      >不存在</el-checkbox
                    >
                    <el-checkbox v-model="info.vecRegist == 2" disabled
                      >存在</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.vecRegistRmks }}</span>
                  </td>
                </tr>

                <tr>
                  <th class="sub-title">2.是否使用卫星定位装置车辆</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.vecBds == 1" disabled
                      >不存在</el-checkbox
                    >
                    <el-checkbox v-model="info.vecBds == 2" disabled
                      >存在</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.vecBdsRmks }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">3.逾期未报废、未年检危化品运输车辆</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.vecAnnualInsp == 1" disabled
                      >不存在</el-checkbox
                    >
                    <el-checkbox v-model="info.vecAnnualInsp == 2" disabled
                      >存在</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.vecAnnualInspRmks }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">4.车辆违法清零情况</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.vecIllegal == 1" disabled
                      >全部清零</el-checkbox
                    >
                    <el-checkbox v-model="info.vecIllegal == 2" disabled
                      >未清零</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.vecIllegalRmks }}</span>
                  </td>
                </tr>

                <tr>
                  <th rowspan="4">人员管理</th>
                  <th class="sub-title">1.驾驶员档案管理及从业资格审核</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.persArchives == 1" disabled
                      >具备资格</el-checkbox
                    >
                    <el-checkbox v-model="info.persArchives == 2" disabled
                      >不具备</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.persArchivesRmks }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">
                    2.驾驶员参加交通安全教育培训落实情况
                  </th>
                  <td colspan="4">
                    <el-checkbox v-model="info.persTrain == 1" disabled
                      >落实</el-checkbox
                    >
                    <el-checkbox v-model="info.persTrain == 2" disabled
                      >未落实</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.persTrainRmks }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">3.驾驶员违法清零情况</th>
                  <td colspan="4">
                    <el-checkbox
                      v-model="info.persIllegalClearing == 1"
                      disabled
                      >全部清零</el-checkbox
                    >
                    <el-checkbox
                      v-model="info.persIllegalClearing == 2"
                      disabled
                      >未清零</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{
                      info.persIllegalClearingRmks
                    }}</span>
                  </td>
                </tr>
                <tr>
                  <th class="sub-title">4.驾驶员违法违规内部处置落实情况</th>
                  <td colspan="4">
                    <el-checkbox v-model="info.persDispose == 1" disabled
                      >落实</el-checkbox
                    >
                    <el-checkbox v-model="info.persDispose == 2" disabled
                      >未落实</el-checkbox
                    >
                  </td>
                  <td colspan="2">
                    <span class="text-mark">{{ info.persDisposeRmks }}</span>
                  </td>
                </tr>

                <tr>
                  <th>现场图片</th>
                  <td colspan="8">
                    <el-image
                      class="ill-img ill-pics"
                      v-for="(item, index) in info.imgs"
                      :key="index"
                      :src="item"
                      :preview-src-list="info.imgs"
                    ></el-image>
                  </td>
                </tr>
                <tr>
                  <th>负责人签名</th>
                  <td colspan="8">
                    <el-image
                      class="ill-img ill-pics"
                      v-for="(item, index) in info.sign"
                      :key="index"
                      :src="item"
                      :preview-src-list="info.sign"
                    ></el-image>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getTransChencInfo } from "@/api/transEntpCheck";
export default {
  name: "RegistrationInfo",
  data() {
    return {
      visible: false,
      detailLoading: false,
      id: "",
      info: {},
      title: "" //弹窗名称
    };
  },
  methods: {
    init(row) {
      this.title = row.entpName || "";
      this.id = row.id;
      this.visible = true;
      this.$nextTick(() => {
        if (this.id) {
          this.getInfo(this.id);
        }
      });
    },
    getInfo(id) {
      this.detailLoading = true;
      getTransChencInfo(id)
        .then(res => {
          this.detailLoading = false;
          if (res.code == 0) {
            this.$set(this, "info", res.data);
            if (this.info.sign) {
              this.info.sign = this.info.sign.split(",");
            }
            if (this.info.imgs) {
              this.info.imgs = this.info.imgs.split(",");
            }
          } else {
            this.info = {};
          }
        })
        .catch(err => {
          this.info = {};
          this.detailLoading = false;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.ill-img {
  /deep/ .el-icon-circle-close {
    color: white;
  }
}
.ill-pics {
  width: 160px;
  height: 160px;
  margin: 10px 5px 0;
  border: 2px solid rgb(177, 176, 176);
  border-radius: 8px;
}

.custom-table {
  th {
    min-width: 100px;
  }
  td {
    min-width: 300px;
  }
  .sub-title {
    text-align: left !important;
  }
  .limit-img-width /deep/ {
    img {
      width: 100px;
      height: 100px;
      display: block;
      // max-width: 500px !important;
    }
  }
}
.el-checkbox /deep/ {
  margin-right: 10px;
  .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #333;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #fff;
  }
}
.print-panel .print-panel-body .custom-table tbody th /deep/ {
  text-align: center;
}
.text-mark {
  text-decoration: underline;
  color: #333;
}
</style>
