<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <el-button slot="button" icon="el-icon-plus" type="primary" size="small" @click="add">新增</el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable"
      style="width: 100%;" :max-height="tableHeight" @sort-change="handleSort">
      <el-table-column prop="vecNo" label="车牌号"> </el-table-column>
      <el-table-column prop="reason" label="原因"> </el-table-column>
      <el-table-column prop="type" label="类型" min-width="90" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.type">
            <el-tag size="mini" v-for="(item, index) in scope.row.type.split(',')" :key="index">
              {{ item == '1' ? '入镇报警' : item == '2' ? '登记点检查' : ''}}
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="startTm" label="开始时间" />
      <el-table-column prop="expireTm" label="过期时间">
        <template slot-scope="scope">
          <span v-if="Date.parse(scope.row.expireTm) < todayStamp" style="color:red">{{ scope.row.expireTm }}</span>
          <span v-else>{{ scope.row.expireTm }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="remark" label="备忘"> </el-table-column> -->
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="notesDetails(scope.row)" v-if="!scope.row.remark">无备忘</el-button>
          <el-button type="text" @click="notesDetails(scope.row)" v-else>有备忘</el-button>
          <el-button type="text" @click="upd(scope.row)">编辑</el-button>
          <el-button type="text" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="text" size="small" class="link_btn" style="font-size:16px" title="查看来镇预警列表" @click="getVecAlarm">
          <svg-icon icon-class="link" class-name="svg-icon" />
          来镇预警
        </el-button>
      </div>
      <div class="grid-operbar ft-lf"></div>
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>
    <!-- 新增对话框 -->
    <el-dialog :visible.sync="visible">
      <el-form style="margin-top:20px;" ref="dataForm" label-width="120px" :model="dataForm" size="small">
        <!-- <el-form-item label="车牌号:" prop="vecNo" :rules="$rulesFilter({required:true,type:'LPN'})">
                    <el-input v-model="dataForm.vecNo" :disabled="dataForm.id?true:false" placeholder="请输入车牌号"></el-input>
                </el-form-item> -->
        <el-form-item label="车牌号:" prop="vecNo" :rules="$rulesFilter({ required: true, type: 'LPN' })">
          <el-select v-model="dataForm.vecNo" placeholder="请输入车牌号" clearable filterable remote allow-create
            :remote-method="getVecNolist" :loading="VecNoLoading">
            <el-option v-for="item in vecNoList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因:" prop="reason" :rules="$rulesFilter({ required: true })">
          <el-input v-model="dataForm.reason" type="textarea" placeholder="请输入原因"></el-input>
        </el-form-item>
        <el-form-item label="类型:" prop="type" :rules="$rulesFilter({ required: true })">
          <el-select v-model="dataForm.type" style="width: 100%;" multiple placeholder="请选择类型" >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间:" prop="startTm">
          <el-date-picker v-model="dataForm.startTm" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="过期时间:" prop="expireTm">
          <el-date-picker v-model="dataForm.expireTm" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
            :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="备忘:" prop="remark" >
                    <el-input v-model="dataForm.remark" type="textarea" placeholder="请输入备忘"></el-input>
                </el-form-item> -->
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button type="primary" size="small" @click="submit">提交</el-button>
      </span>
    </el-dialog>
    <!-- 备忘弹出框 -->
    <el-dialog title="备忘" :visible.sync="remarkVisible" width="30%">
      <el-form :model="remarkForm" ref="remarkForm">
        <el-form-item>
          <el-input type="textarea" v-model="remarkForm.remark" rows="3" placeholder="请输入备忘内容"
            :rules="$rulesFilter({ required: true })"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="remarkSubmit">提交</el-button>
        </el-form-item>
      </el-form>
      <el-collapse>
        <el-collapse-item title="备忘历史记录">
          <div v-for="(item, index) in remark" :key="index">
            <h3 style="margin: 0">{{ item.key }}</h3>
            <span>{{ item.value }}</span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/vecMonitor";
import * as API from "@/api/vec";
import { cloneDeep } from "lodash"

export default {
  components: {
    Searchbar
  },
  data: function () {
    return {
      todayStamp: "", //当前日期时间戳
      tableHeight: 500,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn"
          },
          {
            name: "原因",
            field: "reason",
            type: "text",
            dbfield: "reason",
            dboper: "cn"
          }
        ],
        more: []
      },
      typeList:[{
        label: "入镇报警",
        value: '1'
      },{
        label: "登记点检查",
        value: '2'
      }],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 1000 * 24;
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "一周后",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          },
          {
            text: "两周后",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() + 3600 * 1000 * 24 * 14);
              picker.$emit("pick", date);
            }
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 15,
        total: 0
      },

      visible: false,
      dataForm: {},
      vecNoList: [],
      VecNoLoading: false,
      remarkVisible: false,
      remarkForm: {},
      remark: [] //备忘记录
    };
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.getTodayStamp();
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //获取今天时间戳
    getTodayStamp() {
      let todayFormat = Tool.formatDate(new Date(), "yyyy-MM-dd");
      this.todayStamp = Date.parse(todayFormat);
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 来镇预警页面
    getVecAlarm() {
      this.$router.push({
        path: "kpi/vecMonitor/listAlarm"
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    getList: function (data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      filters.rules.push({
        field: "sys_id",
        data: "330211",
        op: "eq"
      });

      sortParam = sortParam || {};
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    add() {
      this.dataForm = {
        id: "",
        vecNo: "",
        reason: "",
        remark: "",
        startTm: Tool.formatDate(new Date(), "yyyy-MM-dd"),
        sysId: "330211",
        type:[],
      };
      this.visible = true;
    },
    // 远程车牌号 列表获取
    getVecNolist(search) {
      if (search !== "") {
        this.VecNoLoading = true;
        setTimeout(() => {
          this.VecNoLoading = false;
          if (search.length >= 3) {
            this.getVecListData(search);
          } else {
            this.VecNoLoading = true;
          }
        }, 200);
      } else {
        this.vecNoList = [];
      }
    },
    // 获取车辆列表
    getVecListData(search) {
      let filters = {
        groupOp: "AND",
        rules: [{ field: "vec_no", op: "cn", data: search }]
      };
      let param = Object.assign({}, { filters: filters });
      API.getFuzzyTracCd(1180.154, search)
        .then(response => {
          if (response.code == 0) {
            // console.log(response);
            this.vecNoList = response.data.map(item => {
              return { value: item.name, label: item.name };
            });
          } else {
            this.vecNoList = [];
          }
          this.VecNoLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.VecNoLoading = false;
        });
    },
    upd(data) {
      this.visible = true;
      let obj = {};
      if (data.type) {
        obj.type = data.type.split(',');
      }
      this.dataForm = Object.assign({}, data,obj);
    },
    submit() {
      let _this = this;
      
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          const param = cloneDeep(this.dataForm);
          param.type = param.type.join(',');
          $http[this.dataForm.id ? "upd" : "add"](param).then(res => {
            if (res && res.code == 0) {
              this.$message({
                message: "提交成功",
                type: "success",
                duration: 2000,
                onClose: () => {
                  _this.visible = false;
                  _this.getList();
                }
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 备忘
    notesDetails(row) {
      this.remarkVisible = true;
      this.remarkForm.id = row.id;
      this.remark = row.remark ? JSON.parse(row.remark) : [];
    },
    remarkSubmit() {
      let _this = this;
      this.$refs["remarkForm"].validate(valid => {
        if (valid) {
          $http.upd(this.remarkForm).then(res => {
            if (res && res.code == 0) {
              this.$message({
                message: "提交成功",
                type: "success",
                duration: 2000,
                onClose: () => {
                  _this.remarkVisible = false;
                  _this.getList();
                }
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    // 删除
    del(id) {
      let _this = this;
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $http.del([id]).then(res => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
                duration: 2000,
                onClose: () => {
                  _this.getList();
                }
              });
            }
          });
        })
        .catch(() => { });
    }
  }
};
</script>

<style scoped></style>
