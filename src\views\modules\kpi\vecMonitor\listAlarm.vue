<template>
  <div class="detail-container">
    <div v-fixed class="mod-container-oper">
      <el-button-group>
        <el-button type="warning" @click="goBack">
          <i class="el-icon-back" />&nbsp;返回</el-button
        >
      </el-button-group>
    </div>
    <!--列表-->
    <div class="app-main-content">
      <searchbar
        ref="searchbar"
        :search-items="searchItems"
        :pagination="pagination"
        @resizeSearchbar="resizeSearchbar"
        @search="getAlarmList"
      >
      </searchbar>
      <el-table
        class="el-table"
        cell-class-name="custom-el-table_column"
        :data="dataList"
        highlight-current-row
        v-loading="listLoading"
        border
        style="width: 100%;"
        :max-height="tableHeight"
        @sort-change="handleSort"
      >
        <el-table-column prop="crtTm" label="日期"> </el-table-column>
        <el-table-column prop="vecNo" label="车牌号"> </el-table-column>
        <el-table-column prop="argmtCd" label="运单号" />
        <el-table-column prop="vecDespTm" label="发车时间"></el-table-column>
      </el-table>
      <!--工具条-->
      <div class="toolbar clearfix">
        <el-pagination
          background
          layout="sizes, prev, pager, next, total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :page-size="pagination.limit"
          :current-page.sync="pagination.page"
          :total="total"
          :page-sizes="[10, 20, 30, 50, 100, 200]"
          style="float:right;"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as $http from "@/api/vecMonitor";
export default {
  components: {
    Searchbar
  },
  data() {
    return {
      listLoading: false,
      dataList: [],
      tableHeight: 640,
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "vecNo",
            type: "text",
            dbfield: "vec_no",
            dboper: "cn"
          },
          {
            name: "运单号",
            field: "argmtCd",
            type: "text",
            dbfield: "argmt_cd",
            dboper: "cn"
          }
        ],
        more: []
      },
      pagination: {
        page: 1,
        limit: 20
      },
      total: 0
    };
  },
  mounted() {
    this.getAlarmList();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.push({ path: "/kpi/vecMonitor-list" });
    },
    getAlarmList() {
      this.listLoading = true;

      let listPage = {
        page: this.pagination.page,
        limit: this.pagination.limit
      };
      let filters = this.$refs.searchbar.get();
      let param = Object.assign({}, { filters: filters }, listPage);
      $http
        .getAlarmList(param)
        .then(({ page: res }) => {
          this.dataList = res.list;
          this.total = res.totalCount;
          this.pagination.page = res.pageNumber;
          this.pagination.limit = res.pageSize;

          this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.listLoading = false;
        });
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      // this.getAlarmList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      // this.getAlarmList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 改变搜索框的高度
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 后端排序
    handleSort(sort) {
      const orderType = sort.order === "ascending" ? "ASC" : "DESC"; // 升序或降序
      const fieldNm = sort.prop; // 排序字段名
      const sortParam = { sidx: fieldNm, order: orderType };
      this.getAlarmList(null, sortParam);
    }
  }
};
</script>

<style scoped>
.app-main-content {
  margin: 0;
}
</style>
