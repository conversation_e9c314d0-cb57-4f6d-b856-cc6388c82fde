<!--
@pageTitle:运输企业统计
-->
<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix">
      <el-row>
        <el-col :span="4" class="toolbar" style="padding-bottom: 0px">
          <span class="el-icon-date">月内运输企业汇总表</span>
        </el-col>
        <el-col
          :span="20"
          class="toolbar text-right"
          style="padding-bottom: 0px"
        >
          <el-form
            ref="form"
            :model="queryForm"
            :inline="true"
            label-width="80px"
            size="mini"
          >
            <el-form-item>
              <el-select
                v-model="queryForm.entpDist"
                placeholder="请选择区域"
                @change="getList()"
                clearable
              >
                <el-option
                  v-for="item in selectOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryForm.entpName"
                placeholder="请输入企业名称"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="queryForm.date"
                type="month"
                @change="getList()"
                value-format="yyyy-MM"
                placeholder="选择月份"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList">查询</el-button>
              <el-button type="success" @click="handleDownload">导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      show-summary
      highlight-current-row
      v-loading="listLoading"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column label="#" type="index"></el-table-column>
      <el-table-column
        prop="entpName"
        label="企业名称"
        min-width="220"
      ></el-table-column>
      <el-table-column prop="entpDist" label="所属区域" width="180">
        <template slot-scope="scope">
           {{ scope.row.entpDist == 0 ? '区内' : scope.row.entpDist == 1 ? '区外市内' : scope.row.entpDist == 2 ? '市外省内' : scope.row.entpDist == 3 ? '省外' : '' }}
        </template>
      </el-table-column>
      <el-table-column prop="date" label="月份" width="180" align="center">
      </el-table-column>
      <el-table-column
        prop="rteplanCnt"
        label="运单数量"
        width="160"
        align="center"
      >
      </el-table-column>
      <el-table-column prop="transCnt" label="运输车次" width="180" align="center">
      </el-table-column>
      <el-table-column prop="transQty" label="运输货量" width="180" align="center">
      </el-table-column>
      <el-table-column prop="goodsTypeCnt" label="运输货物种类" width="220" align="center">
      </el-table-column>

      <!-- <el-table-column prop="overSpeedCnt" label="超速" width="120">
      </el-table-column> -->
      <!-- <el-table-column prop="overBusinessCnt" label="超经营范围" width="120">
      </el-table-column> -->
      <!-- <el-table-column prop="overLoadCnt" label="超载" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="stopCnt" label="停车" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="noRecordCnt" label="未备案" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="noRteplanCnt" label="无电子运单" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="rteplanCnt" label="电子运单量" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="txzCnt" label="通行证量" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="changeGoodsCnt" label="货品更换" width="130">
      </el-table-column> -->
      <!-- <el-table-column prop="alarmRate" width="90" label="违章率"></el-table-column> -->
    </el-table>

    <!--工具条-->
    <el-col :span="24" class="toolbar">
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :page-sizes="[20, 30, 50, 100, 200, 500]"
        :total="pagination.total"
        :current-page="pagination.page"
        style="float: right"
      >
      </el-pagination>
    </el-col>
  </div>
</template>

<script>
import * as $http from "@/api/monthStat";
import * as Tool from "@/utils/tool";

export default {
  name: "PersList",
  data() {
    return {
      selectOptions: [
        {
          value: 0,
          label: "区内"
        },
        {
          value: 1,
          label: "市内区外"
        },
        {
          value: 2,
          label: "省内市外"
        },
        {
          value: 3,
          label: "省外"
        }
      ],
      queryForm: {
        entpDist: "",
        date: "",
        entpName: ""
      },
      tableHeight: 500,
      list: [],

      listLoading: false,
      addLoading: false,

      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "今天",
            onClick: function(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick: function(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      }
    };
  },
  created() {
    let nowD = new Date();
    let nowYear = nowD.getFullYear();
    let nowMonth = nowD.getMonth();
    /* let nowDate;
    if(nowMonth == 0){
        nowYear -= 1;
        nowDate = nowYear+'-'+12;
    }else{
        nowDate = nowYear+'-'+(nowMonth>9 ? nowMonth : '0'+nowMonth);
    }
    this.queryForm.date = nowDate; */
    this.getList();
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 210;
      });
    },
    //统计报表导出
    handleDownload() {
      let _this = this;
      let param = {};

      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var data = _this.queryForm[filed];
          var filed = filed.replace(/([A-Z])/g, "_$1").toLowerCase();
          param[filed] = data;
        }
      }
      $http
        .downloadEntpExcel(param)
        .then(response => {
          let a = document.createElement("a");
          let blob = new Blob([response]);
          let url = window.URL.createObjectURL(blob);

          a.href = url;
          a.download = "运输企业汇总统计_" + this.queryForm.date + ".xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(error => {
          throw new Error(error);
        });
    },
    // 分页跳转
    handleCurrentChange: function(val) {
      let param = {};
      this.pagination.page = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 分页条数修改
    handleSizeChange: function(val) {
      let param = {};
      this.pagination.limit = val;
      param = Object.assign({}, this.pagination);
      this.getList(param);
    },
    // 获取数据
    getList: function(param) {
      let _this = this;
      this.listLoading = true;
      let filters = { groupOp: "AND", rules: [] };

      param = param || Object.assign({}, this.pagination);
      delete param.total;

      for (var filed in _this.queryForm) {
        if (
          _this.queryForm[filed] !== "undefined" &&
          _this.queryForm[filed] !== null &&
          /\S/.test(_this.queryForm[filed])
        ) {
          var rule = {
            field: filed.replace(/([A-Z])/g, "_$1").toLowerCase(),
            op: "eq",
            data: _this.queryForm[filed]
          };
          if (filed == "entpName") {
            rule.op = "cn";
          }
          filters.rules.push(rule);
        }
      }

      param.filters = filters;

      this.listLoading = true;
      $http
        .queryEntpList(param)
        .then(response => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.pagination.page = response.page.currPage;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    }
  }
};
</script>

<style></style>
