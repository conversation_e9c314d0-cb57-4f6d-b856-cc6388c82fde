<template>
  <transition name="el-fade-in-linear">
    <div>
      <div class="app-main-content">
        <el-row>
          <!-- <el-col :sm="4"> -->
          <el-form :inline="true" size="mini">
            <el-form-item>
              <el-select v-model="timeout" @change="timeoutFresh">
                <el-option v-for="item in timeoutOpts"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button class="el-icon el-icon-refresh" @click="refresh">刷新</el-button>
            </el-form-item>
            <el-form-item>
              <el-input placeholder="输入企业名称" v-model="entpName" @keyup.native="reset" @keyup.enter.native="search"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="downloadExcel">导出今日装卸数据</el-button>
            </el-form-item>
          </el-form>
          <!-- </el-col> -->
        </el-row>
        <el-row :gutter="10">
          <el-col class="card-grid" :span="4" v-for="(item, index) in totalList" :key="index">
            <el-card :body-style="{ padding: '0px' }" v-bind:style="{'background-color':colors[index],'color':'#fff'}">
              <div style="padding: 14px;overflow:hidden;">
                <div style="float:left;" class="icon">
                  <svg-icon :icon-class="icon[index]" class-name="menu-svg-icon"></svg-icon>
                </div>
                <div style="float:left;">
                  <span class="item-count">{{item.total | doubles}}</span>
                  <div class="bottom clearfix">
                    <span class="item-name">{{item.name}}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div class="app-main-content" v-loading="loading">
        <el-row :gutter="10">
          <el-col class="card-grid" :span="4" v-for="(oItem, index) in loadList" :key="index">
            <el-card :body-style="{ padding: '10px' }">
              <el-row :gutter="4">
                <el-col :sm="8" class="text-left card-item">
                  <div class="item-count" v-bind:style="{'color':colors[0]}">{{oItem[1]}}</div>
                  <span class="item-name">
                                    装车次/辆
                                </span>
                </el-col>
                <el-col :sm="8" class="text-center card-item">
                  <div class="item-count" v-bind:style="{'color':colors[1]}">{{oItem[2]}}</div>
                  <span class="item-name">
                                    卸车次/辆
                                </span>
                </el-col>
                <el-col :sm="8" class="text-right card-item">
                  <div class="item-count" v-bind:style="{'color':colors[2]}">{{oItem[5]}}</div>
                  <span class="item-name">
                                    卫星定位进入车次
                                </span>
                </el-col>
              </el-row>
              <el-row :gutter="4">
                <el-col :sm="12" class="text-left card-item">
                  <div class="item-count" v-bind:style="{'color':colors[3]}">{{oItem[4]}}</div>
                  <span class="item-name">
                                    卸货量/吨
                                </span>
                </el-col>
                <el-col :sm="12" class="text-right card-item">
                  <div class="item-count" v-bind:style="{'color':colors[4]}">{{oItem[3]}}</div>
                  <span class="item-name">
                                    装货量/吨
                                </span>
                </el-col>
              </el-row>
              <div slot="header" class="clearfix text-center card-title" :title="oItem[0]">{{oItem[0]}}</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </transition>
</template>
<script>
  import {getDayMonit,stevedorDownload} from "@/api/loadMonit";

  export default {
    data() {
      return {
        currentDate: new Date(),
        icon: ["entp", "load-and-unload", "load-and-unload", "data-statistics", "data-statistics"],
        loadList: [],
        dataSource:[],
        timeout: 300,
        entpName:"",
        timer: null,
        loading: false,
        timeoutOpts: [
          {"value": 5, "label": "5秒"},
          {"value": 15, "label": "15秒"},
          {"value": 30, "label": "30秒"},
          {"value": 60, "label": "1分钟"},
          {"value": 120, "label": "2分钟"},
          {"value": 300, "label": "5分钟"}
        ],
        totalList: [
          {name: "当日活跃装卸企业数", total: "0"},
          {name: "当日累计装货车数", total: "0"},
          {name: "当日累计卸货车数", total: "0"},
          {name: "当日累计装货量(吨)", total: "0"},
          {name: "当日累计卸货量(吨)", total: "0"}
        ],
        colors: ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#f46ba6"]
      };
    },
    filters: {
      doubles(val) {
        //截取三位小数
        if (!typeof val == 'number' && !typeof (val += 0) == 'number') {
          return false;
        }

        val = (val + '').replace(/(^\d*(\.{1}\d{3})?)(\d*)$/, '$1');

        return val
      }
    },
    created() {
      this.loading = true;
      this.getList();
      this.timeoutFresh();
    },
    destroyed() {
      clearTimeout(this.timer);
    },
    methods: {
      timeoutFresh() {
        let t = this.timeout * 1000;
        let fresh = () => {
          this.loading = true;
          this.getList();
          this.timer = setTimeout(fresh, t)
        }

        clearTimeout(this.timer);
        this.timer = setTimeout(fresh, t)
      },
      refresh() {
        this.loading = true;
        setTimeout(() => {
          this.getList();
        }, 1000);
      },
      getList() {
        let entpTotal = 0;
        let onlineEntpTotal = 0;
        let loadVecTotal = 0;
        let unloadVecTotal = 0;
        let loadCountTotal = 0;
        let unloadCountTotal = 0;
        getDayMonit().then(response => {
          if (response.code == 0) {
            this.loadList = response.data;
            this.dataSource = response.data;
            this.loadList.filter((item, index) => {
              // entpTotal += 1;
              loadVecTotal += item[1];
              unloadVecTotal += item[2];
              loadCountTotal += item[3];
              unloadCountTotal += item[4];

              for (let i = 0, len = item.length; i < len; i++) {
                  if(i == 1 || i == 2){
                      if (item[i] > 0) {
                          onlineEntpTotal += 1;
                          break;
                      }
                  }
              }

            });
            this.totalList[0]["total"] = onlineEntpTotal;
            this.totalList[1]["total"] = loadVecTotal;
            this.totalList[2]["total"] = unloadVecTotal;
            this.totalList[3]["total"] = loadCountTotal;
            this.totalList[4]["total"] = unloadCountTotal;
          }
          this.loading = false;
        })
          .catch(error => {
            this.loading = false;
          });
      },
      search(){
        let query = this.entpName;
       
        if(/\S/.test(query)){
          this.loadList = [];
          this.dataSource.filter(item => {
            let name = item[0];
            if(name.indexOf(query) > -1){
              this.loadList.push(item);
            }
          });
        }
        
      },
      reset(){
        let query = this.entpName;
        if(!/\S/.test(query)){
          this.loadList = this.dataSource;
        }
      },
      downloadExcel(){
        stevedorDownload().then( res => {
            let a = document.createElement('a');
            let blob = new Blob([res]);
            let url = window.URL.createObjectURL(blob);
            var _date = new Date();

            a.href = url;
            a.download = '今日装卸数据统计汇总统计_'+(_date.getFullYear()+'-'+(_date.getMonth()+1)+'-'+_date.getDate())+'.xlsx';
            a.click();
            window.URL.revokeObjectURL(url);
        })
        .catch( err => {

        })

      }
    }
  };
</script>

<style scoped>
  .card-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .card-grid {
    width: 20%;
    margin-bottom: 10px;
  }

  .panel {
    margin-bottom: 0;
    border-bottom: none;
  }

  .item-name {
    font-size: 12px;
  }

  .item-count {
    font-size: 20px;
    font-weight: 600;
  }

  .card-item {
    margin-bottom: 16px;
  }

  .icon svg.menu-svg-icon {
    font-size: 36px;
    margin-top: 4px;
    margin-right: 6px;
  }
</style>
