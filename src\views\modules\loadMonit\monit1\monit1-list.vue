<template>
  <transition name="el-fade-in-linear">
    <div>
      <div class="app-main-content">
        <el-row>
          <!-- <el-col :sm="4"> -->
          <el-form :inline="true" size="mini">
            <el-form-item>
              <el-select v-model="timeout" @change="timeoutFresh">
                <el-option
                  v-for="item in timeoutOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button class="el-icon el-icon-refresh" @click="refresh"
                >刷新</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="输入企业名称"
                v-model="entpName"
                @keyup.native="reset"
                @keyup.enter.native="search"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="downloadExcel"
                >导出今日装卸数据</el-button
              >
            </el-form-item>
          </el-form>
          <!-- </el-col> -->
        </el-row>
        <el-row :gutter="10">
          <el-col
            class="card-grid"
            :span="4"
            v-for="(item, index) in totalList"
            :key="index"
          >
            <el-card
              :body-style="{ padding: '0px' }"
              v-bind:style="{
                'background-color': colors[index],
                color: '#fff',
              }"
            >
              <div style="padding: 14px; overflow: hidden">
                <div style="float: left" class="icon">
                  <svg-icon
                    :icon-class="icon[index]"
                    class-name="menu-svg-icon"
                  ></svg-icon>
                </div>
                <div style="float: left">
                  <span class="item-count">{{ item.total | doubles }}</span>
                  <span v-if="index===0" class="item-count">/{{dataSource.length}}</span>
                  <div class="bottom clearfix">
                    <span class="item-name">{{ item.name }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div class="app-main-content" v-loading="loading">
        <el-row :gutter="10">
          <el-col
            class="card-grid"
            :span="4"
            v-for="(oItem, index) in loadList"
            :key="index"
          >
            <el-card :body-style="{ padding: '10px' }">
              <el-row :gutter="4">
                <el-col :sm="8" class="text-left card-item">
                  <div class="item-count" v-bind:style="{ color: colors[0] }">
                    {{ oItem[1] }}
                  </div>
                  <span class="item-name"> 装车次/辆 </span>
                </el-col>
                <el-col :sm="8" class="text-center card-item">
                  <div class="item-count" v-bind:style="{ color: colors[1] }">
                    {{ oItem[2] }}
                  </div>
                  <span class="item-name"> 卸车次/辆 </span>
                </el-col>
                <el-col :sm="8" class="text-right card-item">
                  <div class="item-count" v-bind:style="{ color: colors[2] }">
                    {{ oItem[5] }}
                  </div>
                  <span class="item-name"> 卫星定位进入车次 </span>
                </el-col>
              </el-row>
              <el-row :gutter="4">
                <el-col :sm="12" class="text-left card-item">
                  <div class="item-count" v-bind:style="{ color: colors[3] }">
                    {{Number.isInteger(oItem[4])?oItem[4]:oItem[4].toFixed(2)  }}
                  </div>
                  <span class="item-name"> 卸货量/吨 </span>
                </el-col>
                <el-col :sm="12" class="text-right card-item">
                  <div class="item-count" v-bind:style="{ color: colors[4] }">
                    {{Number.isInteger(oItem[3])?oItem[3]:oItem[3].toFixed(2)  }}
                  </div>
                  <span class="item-name"> 装货量/吨 </span>
                </el-col>
              </el-row>
              <div slot="header" class="card-title" @click="detailsHandle(oItem)" :title="oItem[0]">
              <div style="width:25px;margin-right:5px">
                <img v-if="oItem[7]" :src="oItem[7]" alt />
                <img v-else src="static/img/monitor-img/entp.png" alt />
              </div>
              <div style="flex:1;">
                <!-- <div class="nowrap" :style="{'color':colors[0]}">{{oItem[6] ? oItem[6] : oItem[0]}}</div> -->
                <div class="nowrap" :style="{'color':colors[0]}">{{oItem[0]}}</div>
                <div class="nowrap" style="font-size:12px;color:#999;">{{oItem[0]}}</div>
              </div>
            </div>
            <!-- <div
                slot="header"
                class="clearfix text-center card-title"
                :title="oItem[0]"
              >
                {{ oItem[0] }}
              </div> -->
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-dialog
        :title="comDgHasPaginationTitle"
        :visible.sync="comDgHasPaginationVisible"
        width="80%"
        append-to-body
      >
        <el-tabs v-model="detailsName" @tab-click="gpsClick">
          <el-tab-pane label="装卸记录" name="load">
            <simple-table
              v-loading="comTbHasPaginationLoading"
              :tableHeader="comTbHasPaginationHeader"
              :tablePage="comTbHasPaginationPage"
              @tableRefreshByPagination="tableRefreshByPaginationHandle"
            ></simple-table>
          </el-tab-pane>
          <!-- <el-tab-pane label="查验记录" name="check">
            <simple-table
              v-loading="comTbHasPaginationLoading"
              :tableHeader="checkPaginationHeader"
              :tablePage="comTbHasPaginationPage"
              @tableRefreshByPagination="tableRefreshByPaginationHandle"
            ></simple-table>
          </el-tab-pane> -->
          <el-tab-pane label="卫星定位进出记录" name="gps">
            <simple-table
              v-loading="comTbGpsLoading"
              :tableHeader="gpsPaginationHeader"
              :tablePage="comTbGpsPage"
              @tableRefreshByPagination="tableRefreshByPaginationHandle"
            ></simple-table>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>

    </div>
  </transition>
</template>
<script>
import { getDayMonit, stevedorDownload, allListNoCount, getEntpGps } from "@/api/loadMonit";
import { formatDate, getWeekDate, getMonthDate, getQuarterDate, getYearDate } from "@/utils/tool";
import SimpleTable from "@/components/SimpleTable";
export default {
  data() {
    return {
      currentDate: new Date(),
      detailsName: "load",
      activeEntp: null,
      dateTime: [
        formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00",
        formatDate(new Date(), "yyyy-MM-dd") + " 23:59:59"
      ],
      comDgHasPaginationTitle: "",
      comDgHasPaginationVisible: false,
      comTbHasPaginationLoading: false,
      comTbHasPaginationHeader: [],
      comTbHasPaginationPage: {
        list: [],
        currPage: 0,
        pageSize: 0,
        totalPage: 0
      },
      gpsPagination:{
        page:1,limit:20
      },
      comTbGpsLoading: false,
      gpsPaginationHeader: [
        { name: "车牌号", field: "vehicle_no",width:'100' },
        { name: "企业名称", field: "owned_company",width:'220' },
        { name: "进入时间", field: "enter_date" },
        { name: "离开时间", field: "leave_date" },
        { name: "时长（分钟）", field: "minutes" },
        { name: "操作", field: "vehicle_no", handle: "轨迹" }
      ],
      comTbGpsPage: {
        list: [],
        currPage: 1,
        pageSize: 20,
        totalPage: 1
      },
      icon: [
        "entp",
        "load-and-unload",
        "load-and-unload",
        "data-statistics",
        "data-statistics",
      ],
      loadList: [],
      dataSource: [],
      timeout: 300,
      entpName: "",
      timer: null,
      loading: false,
      timeoutOpts: [
        { value: 5, label: "5秒" },
        { value: 15, label: "15秒" },
        { value: 30, label: "30秒" },
        { value: 60, label: "1分钟" },
        { value: 120, label: "2分钟" },
        { value: 300, label: "5分钟" },
      ],
      totalList: [
        { name: "当日活跃装卸企业数", total: "0" },
        { name: "当日累计装货车数", total: "0" },
        { name: "当日累计卸货车数", total: "0" },
        { name: "当日累计装货量(吨)", total: "0" },
        { name: "当日累计卸货量(吨)", total: "0" },
      ],
      colors: ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#f46ba6"],
    };
  },
  components: {
    SimpleTable
  },
  filters: {
    doubles(val) {
      //截取三位小数
      if (!typeof val == "number" && !typeof (val += 0) == "number") {
        return false;
      }

      val = (val + "").replace(/(^\d*(\.{1}\d{3})?)(\d*)$/, "$1");

      return val;
    },
  },
  created() {
    this.loading = true;
    this.getList();
    this.timeoutFresh();
  },
  destroyed() {
    clearTimeout(this.timer);
  },
  methods: {
    timeoutFresh() {
      let t = this.timeout * 1000;
      let fresh = () => {
        this.loading = true;
        this.getList();
        this.timer = setTimeout(fresh, t);
      };

      clearTimeout(this.timer);
      this.timer = setTimeout(fresh, t);
    },
    refresh() {
      this.loading = true;
      setTimeout(() => {
        this.getList();
      }, 1000);
    },
    getList() {
      let onlineEntpTotal = 0;
      let loadVecTotal = 0;
      let unloadVecTotal = 0;
      let loadCountTotal = 0;
      let unloadCountTotal = 0;
      getDayMonit()
        .then((response) => {
          if (response.code == 0) {
            this.loadList = response.data;
            this.dataSource = response.data;
            response.data.filter((item, index) => {
              loadVecTotal += item[1];
              unloadVecTotal += item[2];
              loadCountTotal += item[3];
              unloadCountTotal += item[4];

              for (let i = 0, len = item.length; i < len; i++) {
                if (i == 1 || i == 2) {
                  if (item[i] > 0) {
                    onlineEntpTotal += 1;
                    break;
                  }
                }
              }
            });
            this.totalList[0]["total"] = onlineEntpTotal;
            this.totalList[1]["total"] = loadVecTotal;
            this.totalList[2]["total"] = unloadVecTotal;
            this.totalList[3]["total"] = loadCountTotal;
            this.totalList[4]["total"] = unloadCountTotal;
          }
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    search() {
      let query = this.entpName;

      if (/\S/.test(query)) {
        this.loadList = [];
        this.dataSource.filter((item) => {
          let name = item[0];
          if (name.indexOf(query) > -1) {
            this.loadList.push(item);
          }
        });
      }
    },
    reset() {
      let query = this.entpName;
      if (!/\S/.test(query)) {
        this.loadList = this.dataSource;
      }
    },
    downloadExcel() {
      stevedorDownload()
        .then((res) => {
          let a = document.createElement("a");
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download =
            "今日装卸数据统计汇总统计_" +
            (_date.getFullYear() +
              "-" +
              (_date.getMonth() + 1) +
              "-" +
              _date.getDate()) +
            ".xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((err) => {});
    },
    //点击查看详情
    detailsHandle(param) {
      let _this = this;
      this.detailsName = 'load'
      this.activeEntp = param;

      let postData = {
        filters: {
          groupOp: "AND",
          rules: [
            { field: "ip_pk", op: "eq", data: param[6] },
            { field: "wt_tm", op: "bt", data: this.dateTime }
          ]
        }
      };
      this.comDgHasPaginationTitle = "装卸记录";
      this.comDgHasPaginationVisible = true;
      this.comTbHasPaginationHeader = [
        { name: "承运商", field: "entpNmCn",width:'160' },
        { name: "电子运单号", field: "argmtCd" ,width:'190'},
        { name: "牵引车", field: "cd",width:'90' },
        { name: "挂车", field: "traiCd",width:'90' },
        { name: "货物", field: "prodNm" ,width:'120'},
        { name: "重量（KG）", field: "loadQty", width:'100'},
        { name: "装货地", field: "csnorWhseDist",width:'140' },
        { name: "卸货地", field: "csneeWhseDist",width:'140' },
        // { name: "装货单位", field: "csnorNmCn" },
        // { name: "卸货单位", field: "csneeNmCn" },
        { name: "驾驶员", field: "dvNm" },
        { name: "驾驶员联系方式", field: "dvMob",width:'100' },
        { name: "押运员", field: "scNm" },
        { name: "押运员联系方式", field: "scMob",width:'100' },
        { name: "查验结果", field: "status" },
        { name: "查验记录", field: "confimItm", tag: true ,width:'160'}
      ];
      // this.checkPaginationHeader = [
      //   { name: "车牌号", field: "cd" },
      //   { name: "电子运单号", field: "argmtCd" },
      //   { name: "查验结果", field: "status" },
      //   { name: "查验记录", field: "confimItm", tag: true }
      // ];

      this.comTbHasPaginationLoading = true;
      allListNoCount(postData)
        .then(res => {
          _this.comTbHasPaginationLoading = false;
          // res.list.forEach(item => {
          //   if (item.status == 0) {
          //     item.status = "通过";
          //   } else if (item.status == 1) {
          //     item.status = "不通过";
          //   }
          //   let confimItmList = [];
          //   item.confimItm.split(",").forEach(confimItmItem => {
          //     let index = Number(confimItmItem) - 1;
          //     confimItmList.push({
          //       tag: confimItmItem,
          //       content: _this.checkItems[index]
          //     });
          //   });
          //   item.confimItm = confimItmList;
          // });
          _this.$set(_this, "comTbHasPaginationPage", {
            list: res.list
            // currPage:0,
            // pageSize:0,
            // totalPage:0
          });
        })
        .catch(error => {
          _this.comTbHasPaginationLoading = false;
          console.log(error);
        });
    },
    gpsClick(){
      let _this = this
      if (this.detailsName == "gps") {
        //进出企业gps
        _this.comTbGpsLoading = true;
        let param = Object.assign({},{entpPk: this.activeEntp[6]},this.gpsPagination)
        getEntpGps(param).then(res => {
            _this.comTbGpsLoading = false;
            if (res) {
              _this.$set(_this, "comTbGpsPage",res);
            }else{
              this.$set(_this, "comTbGpsPage", {
                list: [],
                currPage: 1,
                pageSize: 20,
                totalPage: 1
              });
            }
          }).catch(error => {
            console.log(error);
            _this.comTbGpsLoading = false;
          });
      }
    },
     // table翻页事件
    tableRefreshByPaginationHandle(paginationData) {
      this.gpsPagination = Object.assign({},paginationData)
      this.gpsClick()
    }
  },
};
</script>

<style scoped>
.card-title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;

}

.card-grid {
  width: 20%;
  margin-bottom: 10px;
}

.panel {
  margin-bottom: 0;
  border-bottom: none;
}

.item-name {
  font-size: 12px;
}

.item-count {
  font-size: 20px;
  font-weight: 600;
}

.card-item {
  margin-bottom: 16px;
}

.icon svg.menu-svg-icon {
  font-size: 36px;
  margin-top: 4px;
  margin-right: 6px;
}
.el-card {
  border: 0 !important;
  cursor: pointer;
}
</style>
