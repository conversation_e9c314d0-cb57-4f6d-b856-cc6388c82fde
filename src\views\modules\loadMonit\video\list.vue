<template>
  <div class="app-main-content"
       style="position: relative;height:92vh; padding-top: 5px;">
    <el-row>
      <!--  左侧-->
      <el-col :span="18">
        <!--   上排按钮-->
        <!--<el-row type="flex" align="middle" style="margin-bottom: 5px">
          <el-col :span="24" v-if="videoOptionsList.length">
            <el-row type="flex" align="middle">
              <el-col :span="3"><span style="white-space: nowrap;">画面大小：</span></el-col>
              <el-col :span="4">
                <el-slider
                  v-model="screenSize"
                  :step="-1"
                  :min="1"
                  :max="3"
                  @change="changeScreen"
                  :format-tooltip="formatTooltip">
                </el-slider>
              </el-col>
              <el-col :span="4">
              </el-col>
            </el-row>
          </el-col>
        </el-row>-->
        <!--  视频列表窗口-->
        <el-row :style="{'height': videoListHeight - 5 + 'px','overflow-y': 'scroll'}">
          <div>
            <el-row>
              <el-col :span="24/sqrtScreen"
                      v-for="(item,index) in videoOptionsList"
                      :key="index"
                      style="position: relative;padding: 0 10px;margin-bottom: 20px">
                <div class="videoBox"
                     :class="[selectedIndex === index ? 'red_box': 'white_box']"
                     @click="selectVideo(index)">
                  <el-button class="close"
                             @click="close(index)"
                             size="mini"
                             type="danger"
                             style="position: absolute;right: 17px;top: 8px; z-index: 1">{{item.id ? '关闭' : '选择通道'}}
                  </el-button>
                  <!--   <video-player
                    :ref="'player'+ index"
                    :posterHeight="(videoListHeight-56.8*sqrtScreen)/sqrtScreen-8"
                    :posterWidth="((videoListHeight-56.8*sqrtScreen)/sqrtScreen)*1.37-10"
                    :data-source="item"
                  >
                    <template slot="append">
                      <div class="video-title" style="background: #f1f1f1;">
                        <div v-if="item.camName" :title="item.camName"><i class="el-icon-camera-solid"></i>{{item.camName}}</div>
                        <div v-if="item.entpName" class="entp" :title="item.entpName"><i class="el-icon-office-building"></i>{{item.entpName}}</div>
                      </div>
                    </template>
                  </video-player>-->
                  <player :ref="'player'+ index"
                          :posterHeight="(videoListHeight-56.8*sqrtScreen)/sqrtScreen-8"
                          :posterWidth="((videoListHeight-56.8*sqrtScreen)/sqrtScreen)*1.37"
                          :API="videoHttp"
                          :loadingSpinner="false"
                          :data-source="item"
                          getStreamDataFieldName="streamUrl">
                    <template slot="append">
                      <div class="video-title"
                           style="background: #f1f1f1;">
                        <div v-if="item.camName"
                             :title="item.camName"><i class="el-icon-camera-solid"></i>{{item.camName}}
                        </div>
                        <div v-if="item.entpName"
                             class="entp"
                             :title="item.entpName"><i class="el-icon-office-building"></i>{{item.entpName}}
                        </div>
                      </div>
                    </template>
                  </player>
                  <el-button class="play_back"
                             v-if="item.channelID"
                             @click="playBack(index)"
                             size="mini"
                             type="primary"
                             style="position: absolute;right: 17px;bottom: 7px;">录播回放
                  </el-button>
                </div>
              </el-col>
            </el-row>

          </div>
        </el-row>
      </el-col>
      <!--            右侧-->
      <el-col :span="6">
        <div style="margin-bottom:10px;text-align:right;font-size: 12px;">一共{{entpList.length}}家企业，已接入{{entpCount.onLineCount+entpCount.offLineCount}}家（已离线{{entpCount.offLineCount}}家），未接入{{entpCount.unConnectLineCount}}家</div>
        <el-row style="display: flex;">
          <div style="flex:1 auto;padding-left: 9px">
            <el-input style=""
                      size="mini"
                      placeholder="搜索"
                      v-model="filterText"
                      clearable>
            </el-input>
          </div>
          <div style="width:81px;margin-left:5px;">
            <el-button :type="playState==='playing'?'danger':'success'"
                       size="mini"
                       @click="oneKey">
              {{playState==='playing'?'一键暂停':'一键播放'}}
            </el-button>
          </div>
        </el-row>
        <el-row v-loading="loading"
                style="overflow-y: scroll"
                :style="{'height': videoListHeight-30 + 'px'}">
          <el-tree node-key="id"
                   ref="tree"
                   :filter-node-method="filterNode"
                   :data="videoListTree"
                   :props="defaultProps"
                   @node-click="handleNodeClick">
            <span class="slot-t-node" slot-scope="{node,data}">
              <span :title="data.title" :class="[data.streamingAddress ? 'text_green': '']">
                <!--企业图标-->
                <i v-if="data.children" class="el-icon-office-building"></i>
                <!--设备图标-->
                <i v-else class="el-icon-camera-solid"></i>
                <!--企业名称-->
                <span v-if="data.children" :class="[data.stat=='在线' ? 'text_blue': 'text_gray']">{{data.shortLabel}}</span>
                <!--设备名称-->
                <span v-else>{{node.label}}</span>
              </span>
            </span>
          </el-tree>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog title="录播回放"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               width="60%">
      <play-back-list ref="playListRef"
                      :entpList="entpList"
                      :selectedInfo="selectedInfo"></play-back-list>
    </el-dialog>

  </div>
</template>

<script>
import * as $http from "@/api/video";
import videoPlayer from "@/components/Video/index";
import player from "@/components/flvPlayer/index"
import playBackList from "./playBackList"
import * as Tool from "@/utils/tool"
import NoSignalSrc from '@/assets/video-img/no-signal.jpg'
import loadingSrc from '@/assets/video-img/loading.jpg'

export default {
  name: "video-list",
  components: {
    videoPlayer,
    player,
    playBackList
  },
  data () {
    return {
      selectedIndex: 0,
      videoListHeight: Tool.getClientHeight() - 95,
      loading: false,
      dialogVisible: false,
      screenSize: 1,
      splitScreen: 9,
      videoHttp: $http,
      videoListChecked: [],//直播视频列表
      playState: 'stop',//stop,pause,playing 直播视频状态
      filterText: '',//关键字搜索
      entpList: [],
      entpCount:{
        onLineCount:0,// 在线
        offLineCount:0,// 离线
        unConnectLineCount:0,// 未接入
      },
      videoListTree: [],
      checkedcamList: [],//已选中视频节点
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'leaf'
      },
      selectedInfo: {
        entp: '',
        number: '',
        channelID: '',
      },
      loadingCam: {
        channelID: '',
        channelName: '',
        camName: '',
        entpId: '',
        entpName: '',
        isOnline: 'OFF', //能返回都是在线状态  status:1、在线，2、离线
        id: '',
        type: '',
        number: '',
        snapUrl: loadingSrc,
        streamingAddress: '',//flv直播流
        streamingAddressHLS: '' //m3u8直播流
      },//加载中视频
      emptyCam: {
        channelID: '',
        channelName: '',
        camName: '',
        entpId: '',
        entpName: '',
        isOnline: 'OFF', //能返回都是在线状态  status:1、在线，2、离线
        id: '',
        type: '',
        number: '',
        snapUrl: NoSignalSrc,
        streamingAddress: '',//flv直播流
        streamingAddressHLS: '' //m3u8直播流
      },//无信号视频
      timeoutId: '',
      allVideoList :[]
    }
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  computed: {
    sqrtScreen () {
      return Math.sqrt(this.splitScreen)
    },
    videoOptionsList () {
      let res = this.videoListChecked.map((item, index) => {
        return {
          camName: item.channelName,
          entpName: item.entpName,
          channelID: item.channelID,
          isOnLine: item.streamingAddress ? "在线" : "离线", //能返回都是在线状态  status:1、在线，2、离线
          id: item.channelID,
          type: item.type || '',
          number: item.number,
          playerOption: {
            poster: item.snapUrl
          },
          streamingAddress: item.streamingAddress,//flv直播流
          streamingAddressHLS: item.streamingAddressHLS //m3u8直播流
        };
      });
      return res;
    },
  },
  created () {
    this.getEntpList()
    //创建9个空视频数组
    this.videoListChecked = new Array(9).fill(this.loadingCam)
  },
  mounted () {
    window.addEventListener("resize", this.setVideoListHeight);
  },
  destroyed () {
    window.removeEventListener("resize", this.setVideoListHeight);
  },
  methods: {
    //设置视频播放高度
    setVideoListHeight () {
      this.$nextTick(() => {
        this.videoListHeight = Tool.getClientHeight() - 95
      });
    },
    //调整视频画面大小
    formatTooltip (val) {
      switch (val) {
        case 3:
          return '大'
          break;
        case 2:
          return '中'
          break;
        case 1:
          return '小'
          break;
      }
    },
    //更换画面个数
    changeScreen () {
      this.splitScreen = Math.pow((4 - this.screenSize), 2)
    },
    //获取企业列表
    getEntpList () {
      this.loading = true;
      $http.getEntpList().then(res => {
        this.loading = false;
        if (res && res.code === 0) {
          this.entpList = res.data
          this.entpCount.onLineCount = this.entpList.filter(entp => entp.stat === '在线' ).length;
          this.entpCount.offLineCount = this.entpList.filter(entp => entp.stat === '离线' ).length;
          this.entpCount.unConnectLineCount = this.entpList.filter(entp => entp.stat === '未接入' ).length;
          let entpList = []
          this.entpList.forEach(item => {
            entpList.push({
              label: item.entpName,
              name: item.entpName,
              id: item.entpId,
              initStatus: item.initStatus,
              stat:item.stat, // 在线，离线，未接入
              onLineCount: 0,
              offLineCount: 0,
              allLineCount: 0,
              children: []
            })
          })
          this.getVideoList(entpList)//获取所有摄像头列表
        }
      })
    },
    //获取所有摄像头列表
    getVideoList (entpList) {
      this.loading = true;
      $http.getAllVideoList().then(res => {
        this.loading = false;
        this.allVideoList = res.data
        //无设备企业特殊处理
        entpList.forEach((entp, entpIndex) => {
          const Index = res.data.findIndex(video => {
            return video.entpId == entp.id
          })
          if (Index === -1 || entp.stat === '离线'){
            entpList[entpIndex].shortLabel = this.ellipsis(entpList[entpIndex].name)+ `[${entp.stat}]`
            entpList[entpIndex].label = entpList[entpIndex].name
            entpList[entpIndex].title = entpList[entpIndex].name
          }
        })
        //有设备企业处理
        res.data.forEach(video => {
          entpList.forEach((entp, entpIndex) => {
            if (video.entpId == entp.id && entp.stat === '在线') {
              if (video.streamingAddress) {
                entpList[entpIndex].onLineCount = entpList[entpIndex].onLineCount + 1
              } else {
                entpList[entpIndex].offLineCount = entpList[entpIndex].offLineCount + 1
              }
              entpList[entpIndex].allLineCount = entpList[entpIndex].onLineCount + entpList[entpIndex].offLineCount
              entpList[entpIndex].shortLabel = this.ellipsis(entpList[entpIndex].name) + `[${entpList[entpIndex].onLineCount}/${entpList[entpIndex].allLineCount}]`
              entpList[entpIndex].label = entpList[entpIndex].name
              entpList[entpIndex].title = entpList[entpIndex].name
              video.label = video.channelName || '未命名'
              video.name = video.channelName || '未命名'
              video.id = video.channelID
              video.leaf = true
              entp.children.push(video)
            }
          })
        })
        this.videoListTree = entpList
        //展示默认视频替换空视频
        this.showDefault()
      })
    },
    //展示默认视频替换空视频
    showDefault () {
      let initVideos = this.allVideoList.filter(item => item.initStatus == '1')
      if (initVideos.length === 0) return

      if (initVideos.length > 9) {
        initVideos = initVideos.slice(0, 9)
      } else if (initVideos.length < 9) {
        let length = initVideos.length
        initVideos.length = 9
        initVideos.fill(this.emptyCam, length, 9)
      }
      initVideos.forEach(item => {
        if (item.channelID && item.number && !item.streamingAddress){
          this.streamStart(item).then( streamingAddressUrl =>{
            item.streamingAddress = streamingAddressUrl;
          })
        }
      })
      this.videoListChecked = initVideos
    },
    //点击摄像头节点
    handleNodeClick (node) {
      let _this = this
      if (node.children) return

      //超时自动停止播放
      this.autoDestroy()

      //先销毁选中位置视频
      if (this.$refs['player' + this.selectedIndex][0].videoPlayer) {
        this.$refs['player' + this.selectedIndex][0].destroyPlayer()
        this.$refs['player' + this.selectedIndex][0].isShowVideo = false
      }
      //如果是离线视频，则先调用激活接口
      if (!node.streamingAddress){
        this.streamStart(node).then( streamingAddressUrl =>{
          node.streamingAddress = streamingAddressUrl;
          _this.readyPlay(node)
        })
      } else {
        _this.readyPlay(node)
      }
    },
    //准备播放
    readyPlay(node){
      let _this = this
      //后替换选中项
      this.videoListChecked.splice(this.selectedIndex, 1, node)
      this.$nextTick(() => {
        //自动播放
        if (node.streamingAddress) {
          if (_this.$refs['player' + _this.selectedIndex][0].videoPlayer) {
            _this.$refs['player' + _this.selectedIndex][0].onPlayerPlay()
          } else {
            _this.$refs['player' + _this.selectedIndex][0].loadVideoPlayer()
          }
        }
        //红框往后跳动
        if (this.selectedIndex + 1 >= this.splitScreen) {
          this.selectedIndex = 0
        } else {
          this.selectedIndex = this.selectedIndex + 1
        }
      })
    },
    //视频假死激活接口
    async streamStart(node){
      let str = `${node.number}_${node.channelID}`
      const res = await $http.getStreamStart(str)
      if (res.code !== 0) return null
      return res.streamingAddress
    },
    //自动销毁视频
    autoDestroy () {
      let _this = this
      //停止播放接口
      setTimeout(function () {
        _this.videoListChecked.forEach((item, index) => {
          if (_this.$refs['player' + index][0].videoPlayer) {
            let path = _this.videoListChecked[index].streamingAddress
            let path1 = path.substr(path.lastIndexOf('/') + 1); //文件名称，去掉路径
            let str = path1.substring(0, path1.indexOf('.')); //文件名称去掉路径和后缀名
            $http.setExpire(str)
          }
        })
      }, 1000);

      //取消超时任务
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
      }
      //设置超时任务
      this.timeoutId = setTimeout(function () {
        //10分钟后无操作销毁视频（节省资源）
        _this.videoListChecked.forEach((item, index) => {
          if (_this.$refs['player' + index][0].videoPlayer) {
            _this.$refs['player' + index][0].destroyPlayer()
            _this.$refs['player' + index][0].isShowVideo = false
            _this.playState = 'stop'
          }
        })
      }, 600000);
    },
    //关闭
    close (index) {
      this.$refs['player' + index][0].destroyPlayer()
      this.$nextTick(() => {
        this.videoListChecked.splice(index, 1, this.emptyCam)//删除1个
      })
    },
    //选中视频，显示红框
    selectVideo (index) {
      this.autoDestroy()
      this.selectedIndex = index
    },
    //列表搜索
    filterNode (value, data, node) {
      if (!value) return true;
      let parentNode = node.parent, labels = [node.label], level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        parentNode = parentNode.parent
        level++
      }
      return labels.some(label => label.indexOf(value) !== -1)
    },
    //一键播放暂停
    oneKey () {
      let _this = this

      //超时30分钟自动停止播放
      _this.autoDestroy()

      if (_this.playState !== 'playing') {
        //第一次加载播放或加载后播放
        const videoElement = this.videoListChecked
        if (videoElement && videoElement.length > 0) {
          videoElement.forEach((video, index) => {
            if (video.id && video.streamingAddress) {
              if (_this.$refs['player' + index][0].videoPlayer) {
                _this.$refs['player' + index][0].onPlayerPlay()
              } else {
                _this.$refs['player' + index][0].loadVideoPlayer()
              }
            }
          })
        }
        _this.playState = 'playing'
      } else if (_this.playState === 'playing') {
        //暂停
        const videoElement = _this.videoListChecked
        if (videoElement && videoElement.length > 0) {
          videoElement.forEach((video, index) => {
            if (video.id && video.streamingAddress) {
              if (_this.$refs['player' + index][0].videoPlayer) {
                _this.$refs['player' + index][0].onPlayerPause()
              }
            }
          })
        }
        _this.playState = 'pause'
      }

    },
    //录播回放
    playBack (index) {
      this.selectedInfo.entp = this.videoListChecked[index].entpName
      this.selectedInfo.number = this.videoListChecked[index].number
      this.selectedInfo.channelID = this.videoListChecked[index].channelID

      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.playListRef.mode = 'video'
        this.$refs.playListRef.init()
      })
    },
    //企业名+省略号
    ellipsis (name) {
      let res = name
      if (name && name.length > 13) {
        res = name.slice(0, 12) + '...'
      }
      return res
    }
  }
}
</script>

<style scoped>
.video-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 37px;
}

.nolist {
  background: #fbfbfb;
  text-align: center;
}

.close {
  opacity: 0;
  background-color: #404040;
  border-color: #404040;
  /*color: #7f8099;*/
  transition: 0.3s;
}

.play_back {
  opacity: 0;
  transition: 0.3s;
}

.videoBox:hover .close {
  opacity: 1;
  /*color: #ffffff;*/
  transition: 0.5s;
}

.videoBox:hover .play_back {
  opacity: 1;
  transition: 0.5s;
}

.red_box {
  border: 2px solid red;
}

.white_box {
  border: 2px solid #fff;
}

.text_green {
  color: #00a65a !important;
}
.text_blue{
  color: #1092e4;
  font-size: 14px;
}
.text_gray{
  font-size: 14px;
}
</style>
