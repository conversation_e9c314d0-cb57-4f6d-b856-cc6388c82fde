<template>
  <div>
    <el-card>
      <el-form ref="form" :model="form" label-width="80px" :inline="true">
        <el-form-item label="企业名称">
          <el-select style="width:150px" v-model="form.entpName" placeholder="请选择企业名称" size="mini" @change="changeEntp">
            <el-option
                v-for="item in entpOptionsList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="摄像头">
          <el-select style="width:120px" v-model="form.channelID" placeholder="请选择摄像头" size="mini"
                     @change="changeCam($event)">
            <el-option
                v-for="item in camOptionsList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
              v-model="form.videoTime"
              size="mini" type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              @change="changeDate"
              :picker-options="pickerOptions"
              style="width: 130px;">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button style="margin-left: 30px" size="mini" type="primary" @click="preDownload()">下载视频</el-button>
        </el-form-item>
        <!--    <el-form-item v-if="mode=='video'">
              <el-button style="margin-left: 50px" size="mini" type="primary" @click="switchMode()" icon="el-icon-s-grid">列表视图</el-button>
            </el-form-item>-->
      </el-form>
      <div v-loading="loading">
        <div v-if="mode==='video'">
          <div v-for="(item, index) in videoOptionsList" :key="item.channelID">
            <video-player
                v-show="index === curIndex"
                :ref="'player'+ index"
                :posterHeight="300"
                :posterWidth="300*1.37"
                :data-source="item">
              <template slot="append">
                <div class="video-title" style="background: #f1f1f1;">
                  <div :title="item.camName">{{item.camName}}</div>
                  <div class="entp" :title="item.entpName">{{item.entpName}}</div>
                </div>
              </template>
              >
            </video-player>
            <!--                    <player
                                        v-show="index === curIndex"
                                        :ref="'player'+ index"
                                        :posterHeight="300"
                                        :posterWidth="300*1.37"
                                        :API="videoHttp"
                                        :data-source="item"
                                        ajaxName="getLoadAndUnloadVideoStreamUrl"
                                        getStreamDataFieldName="url">
                                    <template slot="append">
                                        <div class="video-title" style="background: #f1f1f1;">
                                            <div :title="item.camName">{{item.camName}}</div>
                                            <div class="entp" :title="item.entpName">{{item.entpName}}</div>
                                        </div>
                                    </template>
                                </player>-->

          </div>
          <div v-show="videoOptionsList.length == 0 || curTmCamera.camName == ' '" class="noplaylist">该时间段无录播记录</div>
          <div class="timeline" v-show="videoOptionsList.length">
            <!--时间轴-->
            <div class="timelineMain" @mouseover="showTimeTips($event)" @mouseleave="hideTimeTips($event)"
                 @click="renderCurrentTimeTrace">
              <div class="clearfix timeHour-wrapper" ref="timeHourWrapper">
                <div class="timeHour timeHourFirst"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour"></div>
                <div class="timeHour timeHourFinal"></div>
              </div>
              <div @mouseleave="hideTimeTips($event)">
                <div class="timeNumber" style="left: -2px;">0</div>
                <div class="timeNumber" style="left: 28px;">1</div>
                <div class="timeNumber" style="left: 58px;">2</div>
                <div class="timeNumber" style="left: 88px;">3</div>
                <div class="timeNumber" style="left: 118px;">4</div>
                <div class="timeNumber" style="left: 148px;">5</div>
                <div class="timeNumber" style="left: 178px;">6</div>
                <div class="timeNumber" style="left: 208px;">7</div>
                <div class="timeNumber" style="left: 238px;">8</div>
                <div class="timeNumber" style="left: 268px;">9</div>
                <div class="timeNumber" style="left: 292px;">10</div>
                <div class="timeNumber" style="left: 322px;">11</div>
                <div class="timeNumber" style="left: 352px;">12</div>
                <div class="timeNumber" style="left: 382px;">13</div>
                <div class="timeNumber" style="left: 412px;">14</div>
                <div class="timeNumber" style="left: 442px;">15</div>
                <div class="timeNumber" style="left: 472px;">16</div>
                <div class="timeNumber" style="left: 502px;">17</div>
                <div class="timeNumber" style="left: 532px;">18</div>
                <div class="timeNumber" style="left: 562px;">19</div>
                <div class="timeNumber" style="left: 592px;">20</div>
                <div class="timeNumber" style="left: 622px;">21</div>
                <div class="timeNumber" style="left: 652px;">22</div>
                <div class="timeNumber" style="left: 682px;">23</div>
                <div class="timeNumber" style="left: 712px;">24</div>
                <div ref="timelineProgress" class="timelineProgress" :style="{'left':progressLeft+'px'}"
                     @mousedown="mousedownHandle($event)">
                </div>
                <div class="gpsRunTrace" title="当前速度是0"
                     :style="{'left':gpsRunTrace.left+'px','width':gpsRunTrace.width+'px'}"></div>
                <template v-for="(runpartItem,runpartIndex) in runpartList">
                  <div class="runPart" :key="runpartIndex" :title="runpartItem.title"
                       :style="{left:runpartItem.left+'px','width':runpartItem.width+'px'}">
                  </div>
                </template>
              </div>
              <div class="timelineLabel"
                   :style="{display:isShowTimeLineLabel?'block':'none',left:timeLineLabelLeft?timeLineLabelLeft+'px':0}">
                <div class="timelineLabelcontent">
                  {{timeLineLabel}}
                </div>
                <div class="timelineLabelpointer"></div>
              </div>
              <div class="caliperA" style="left: 0px;">
                <div class="caliperLine"></div>
                <div class="caliperPointerA"></div>
              </div>
              <div class="caliperB" style="left: 721px;">
                <div class="caliperLine"></div>
                <div class="caliperPointerB"></div>
              </div>
              <div class="caliperPartA" style="width: 0px;"></div>
              <div class="caliperPartB" style="width: 0px;"></div>
            </div>
          </div>
        </div>
        <div v-else>
          <el-table :max-height="tableHeight"
                    :data="videoList"
                    style="width: 100%">
            <el-table-column
                prop="entpName"
                label="企业名称"
            >
            </el-table-column>
            <el-table-column
                prop="camName"
                label="摄像头"
            >
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
            >
              <template slot-scope="scope">
                {{scope.row.status === "ONLINE"?"在线":"离线"}}
              </template>
            </el-table-column>
            <el-table-column
                prop="startTm"
                label="开始时间"
            >
            </el-table-column>
            <el-table-column
                prop="endTm"
                label="结束时间"
            >
            </el-table-column>

            <el-table-column
                align="center"
                label="操作"
                width="180">
              <template slot-scope="scope">
                <el-button @click="play(scope)" type="primary" size="mini">播放</el-button>
                <el-button @click="download(scope.row)" type="success" size="mini">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="toolbar clearfix">
          <el-pagination
              v-if="false"
              layout="sizes, prev, pager, next, total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :page-size="pagination.limit"
              :page-sizes="[20, 30, 50, 100, 200]"
              :total="pagination.total">
          </el-pagination>

        </div>
      </div>
    </el-card>
    <el-dialog
        title="视频下载"
        append-to-body
        :visible.sync="downLoadVisible"
        :close-on-click-modal="false"
        width="20%"
    >
      <el-time-picker
          style="width: 100%"
          is-range
          size="mini"
          v-model="downLoadTime"
          value-format="yyyyMMddHHmmss"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          @change="timePickerChange($event)"
      >
      </el-time-picker>
      <span slot="footer" class="dialog-footer">
            <el-button @click="downLoadVisible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="download()" size="mini" :loading="downLoadLoading">{{downLoadLoading?'转码中，请耐心等待……':'下 载'}}</el-button>
          </span>
    </el-dialog>
  </div>
</template>

<script>
  import player from "@/components/flvPlayer/index"; //flv
  import videoPlayer from "@/components/Video/index"; //m3u8
  import * as $http from "@/api/video";
  import * as Tool from "@/utils/tool";

  export default {
    name: "playBackList",
    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        },
        loading: false,
        tableHeight: Tool.getClientHeight() * 0.5,
        mode: 'video',
        videoHttp: $http,
        form: {
          entpName: '',
          channelID: '',
          number: '',
          videoTime: '',
        },
        videoList: [],
        videoOptionsList: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10
        },
        camOptionsList: [],//摄像头选项
        curCameraVideoList: [
          // {
          //   startTm: '2021-08-16 13:00:00',
          //   endTm:'2021-08-16 18:00:00',
          //   camName: "A有限公司CAM01",
          //   entpName: "A有限公司",
          //   id: "33000000000105020103000001451659",
          //   isOnLine: "在线",
          //   status:"ONLINE",
          //   number: "33000000000105020103000001451659",
          //   snapUrl: "https://whjk.oss-cn-hangzhou.aliyuncs.com/cp-ipc-screenshot/f63b6f0b-7b47-41e9-90a9-b8b47267c543.jpg",
          //   type: "1",
          // }, {
          //   startTm:'2021-08-16 19:00:00',
          //   endTm:'2021-08-16 23:00:00',
          //   camName: "A有限公司CAM02",
          //   entpName: "A有限公司",
          //   id: "33000000000105020103000001451664",
          //   isOnLine: "在线",
          //   status:"ONLINE",
          //   number: "33000000000105020103000001451664",
          //   snapUrl: "https://whjk.oss-cn-hangzhou.aliyuncs.com/cp-ipc-screenshot/f63b6f0b-7b47-41e9-90a9-b8b47267c543.jpg",
          //   type: "1",
          // }
        ],
        startDrag: false,//拖拽
        progressLeft: 0, // 播放器播放的距离
        gpsRunTrace: {
          left: 0,
          width: 0
        },
        curTmCamera: {}, //当前选中时间的摄像头
        curIndex: '',//当前摄像头索引
        //1小时width:30,1秒 1/120格
        runpartList: [], // 有速度的播放长度
        isShowTimeLineLabel: false,
        timeLineLabelLeft: 0,
        timeLineLabel: "00:00",
        timeSpeed: 500, //定时器时间
        intervalId: '',//定时器id
        timeoutId: '',//下载定时器id
        downLoadVisible: false,
        downLoadTime: ['', ''],//下载时间段
        downLoadLoading: false
      };
    },
    props: {
      entpList: {
        type: Array,
        default: []
      },
      selectedInfo: {
        type: Object,
        default: {
          entp: '',
          number: '',
          channelID: '',
        }
      },
    },
    computed: {
      entpOptionsList() {
        let res = this.entpList.map(item => {
          return {label: item.entpName, value: item.entpName}
        })
        return res
      },
    },
    components: {
      player,
      videoPlayer
    },
    mounted() {
      this.init()
    },
    methods: {

      //【---------通用--------------】

      //初始化
      init() {
        this.form.entpName = this.selectedInfo.entp
        this.form.number = this.selectedInfo.number
        this.form.channelID = this.selectedInfo.channelID
        this.form.videoTime = Tool.formatDate(new Date(), "yyyy-MM-dd")
        //获取企业查询摄像头列表
        this.changeEntp(this.form.entpName, true)
        //获取单个摄像头播放列表
        this.getVideoList(this.form.number, this.form.channelID, this.form.videoTime)
      },
      //切换列表或视频模式
      switchMode() {
        this.mode = 'list'
      },
      //获取单个摄像头播放列表
      getVideoList(number, channelID, videoTime) {
        let _this = this;
        this.loading = true;
        $http.getCamVideoList(number, channelID, videoTime).then(res => {
          _this.loading = false;
          if (res && res.code === 0) {
            _this.videoList = res.data
            _this.curCameraVideoList = res.data
            if (_this.curCameraVideoList.length) {
              _this.curIndex = 0
            }
            _this.formatVideoList()
          } else {
            _this.videoList = []
            _this.pagination.total = 0;
          }
        })
      },
      //格式化摄像头播放列表
      formatVideoList() {
        let res = this.curCameraVideoList.map((item, index) => {
          return {
            startTm: Date.parse(new Date(item.startTm)) / 1000,
            endTm: Date.parse(new Date(item.endTm)) / 1000,
            camName: item.camName,
            entpName: item.entpName,
            isOnLine: item.isOnLine,
            id: item.channelID,
            type: item.type,
            number: item.number,
            playerOption: {
              poster: item.snapUrl || ''
            },
            videoUrl: item.videoUrl
          };
        });
        this.videoOptionsList = res
        this.showTimeBlock()//显示进度条中视频时间块
      },
      //选中企业查询摄像头
      changeEntp(entpName, init) {
        $http.getCamList(entpName).then(res => {
          if (res && res.code === 0) {
            if (!init) {
              this.form.channelID = ''
            }
            this.camOptionsList = []
            let camList = res.data
            camList.forEach(item => {
              this.camOptionsList.push({label: item.channelName, value: item.channelID, number: item.number})
            })
          }
        })
      },
      //选中摄像头查询当天视频列表
      changeCam(value) {
        this.videoOptionsList.length = 0
        this.camOptionsList.forEach(item => {
          if (item.value == value) {
            this.form.number = item.number
          }
        })
        this.searchVideo()
      },
      //选中日期
      changeDate() {
        this.searchVideo()
      },
      //选中摄像头或日期查询当天视频列表
      searchVideo() {
        this.loading = true;
        if (!this.form.number || !this.form.channelID || !this.form.videoTime) {
          this.loading = false;
          return
        }
        $http.getCamVideoList(this.form.number, this.form.channelID, this.form.videoTime).then(res => {
          this.loading = false;
          if (res && res.code === 0) {
            this.curCameraVideoList = []
            this.curCameraVideoList = res.data
            this.videoList = res.data
            if (this.curCameraVideoList.length) {
              this.curIndex = 0
            }
            this.formatVideoList()
          }
        })
      },

      //【-------------列表功能-------------------】
      //播放
      play(e) {
        let index = e.$index
        if (e.row.startTm) {
          //当前日期时间戳
          let todayTime = Date.parse(new Date(this.form.videoTime + " 00:00:00"));
          let startTm = Date.parse(new Date(e.row.startTm)) / 1000
          let distance = (startTm - todayTime / 1000) / 120
          //跳到该位置播放
          this.renderDragedTrace(distance)
        }
        this.curTmCamera = this.videoOptionsList[index]
        this.curIndex = index
        this.mode = 'video'
      },

      // 分页跳转
      handleCurrentChange: function (val) {
        this.pagination.page = val;
        this.getVideoList();
      },
      // 分页条数修改
      handleSizeChange: function (val) {
        this.pagination.limit = val;
        this.getVideoList();
      },

      //【------------------视频功能-------------------】
      //显示进度条中视频时间块
      showTimeBlock() {
        //当前日期时间戳
        let todayTime = Date.parse(new Date(this.form.videoTime + " 00:00:00"));
        if (this.videoOptionsList[0]) {
          this.curTmCamera = this.videoOptionsList[0]
          this.curIndex = 0
          this.progressLeft = (this.curTmCamera.startTm - todayTime / 1000) / 120
        }
        //显示进度条中视频时间块
        this.videoOptionsList.forEach(item => {
          //把时间转换为位置
          let startPosition = Math.round((item.startTm - todayTime / 1000) / 120)
          let endPosition = Math.round((item.endTm - todayTime / 1000) / 120)
          let widthPosition = endPosition - startPosition;
          this.runpartList.push({left: startPosition, width: widthPosition})
        })
      },
      // 显示时间轴上的时间信息
      showTimeTips(e) {
        let distance =
          e.clientX - this.$refs.timeHourWrapper.getBoundingClientRect().left;
        if (distance < 0) {
          distance = 0;
        } else if (distance > 720) {
          distance = 720;
        }
        this.timeLineLabelLeft = distance;
        // 设置当前时间
        let todayTime = Date.parse(
          new Date(this.form.videoTime + " 00:00:00")
        ); // 获取毫秒数
        let currentTime = todayTime + 120 * distance * 1000;
        this.timeLineLabel = Tool.formatDate(new Date(currentTime), "HH:mm");
        this.isShowTimeLineLabel = true;
      },
      // 隐藏时间轴上的时间信息
      hideTimeTips(e) {
        this.isShowTimeLineLabel = false;
      },
      //点击时间轴
      renderCurrentTimeTrace(e) {
        let distance =
          e.clientX - this.$refs.timeHourWrapper.getBoundingClientRect().left;
        if (distance < 0) {
          distance = 0;
        } else if (distance > 720) {
          distance = 720;
        }

        this.timeLineLabelLeft = distance;
        this.isShowTimeLineLabel = false;
        // 点击时间轴后 播放视频并定位到该时间点
        this.renderDragedTrace(distance);
      },
      // 点击时间轴后 播放视频并定位到该时间点
      renderDragedTrace(distance) {
        this.progressLeft = distance;
        // 设置当前时间
        let todayTime = Date.parse(
          new Date(this.form.videoTime + " 00:00:00")
        );
        let currentTime = (todayTime + 120 * distance * 1000) / 1000;
        //判断该时间点是否存在录播记录
        let selTmCamera, selIndex
        this.videoOptionsList.forEach((item, index) => {
          if (item.startTm < currentTime && currentTime < item.endTm) {
            selTmCamera = item
            selIndex = index
          }
        })
        this.curIndex = selIndex
        if (!selTmCamera) {
          // 该时间点不存在录播记录
          this.curTmCamera = {camName: ' ', entpName: ' '}
          return
        }

        if (selTmCamera === this.curTmCamera) {
          //无需切换视频片段
          this.playVideo(distance)
        } else {//切换视频片段
          this.curTmCamera = selTmCamera
          this.playVideo(distance)
        }
      },
      // 播放视频并定位到该时间点
      playVideo(distance) {
        let _this = this
        let todayTime = Date.parse(
          new Date(this.form.videoTime + " 00:00:00")
        );
        let currentTime = (todayTime + 120 * distance * 1000) / 1000;
        //等待数据载入播放器
        this.$nextTick(() => {
          let playerIndex = _this.$refs['player' + _this.curIndex][0]
          let playerId = playerIndex.playerId
          let videoPlayer = playerIndex.$refs[playerId]
          videoPlayer.currentTime = currentTime - _this.curTmCamera.startTm
          if (playerIndex.isShowVideo === false) {
            playerIndex.loadVideoPlayer()
          }

          clearInterval(_this.intervalId)

          //启动计时器 进度条同步
          _this.intervalId = setInterval(function () {
            _this.progressLeft = (_this.curTmCamera.startTm - todayTime / 1000 + videoPlayer.currentTime) / 120
          }, 1000);
        })
      },
      // 拖动进度点
      mousedownHandle(e) {
        // 当时间轴是否可点击开始的标识为false时，不允许拖拽
        let _this = this,
          x = e.clientX,
          el = e.target;

        let oldLeft = parseFloat(el.style.left);
        this.startDrag = true;
        document.onmousemove = function (e) {
          if (_this.startDrag) {
            let nx = e.clientX - x;
            let distance = nx + oldLeft;
            if (distance < 0) {
              distance = 0;
            } else if (distance > 720) {
              distance = 720;
            }
            el.style.left = distance + "px";

            // 渲染当前时间，显示当前车辆位置
            _this.renderDragedTrace(distance);
          }
          e.preventDefault();
        };
        document.onmouseup = function (e) {
          _this.mouseupHandle(e);
        };
        e.stopPropagation();
      },
      // 鼠标松开的时候
      mouseupHandle(e) {
        this.startDrag = false;
        document.onmousemove = document.onmouseup = null;
      },

      //时间限制
      timePickerChange() {
        let latestTm = Tool.formatDate(this.curCameraVideoList[this.curCameraVideoList.length - 1].endTm, "yyyyMMddHHmmss")
        if (this.downLoadTime[0] > latestTm) {
          this.downLoadTime[0] = latestTm
          this.$set(this.downLoadTime, "0", latestTm);
          this.$message.error('开始时间不得晚于最后记录时间');
        }
        if (this.downLoadTime[1] > latestTm) {
          this.downLoadTime[1] = latestTm
          this.$set(this.downLoadTime, "1", latestTm);
          this.$message.error('结束时间不得晚于最后记录时间');
        }
        let tmDifference = this.downLoadTime[1] - this.downLoadTime[0]
        if (tmDifference > 2000) {
          this.$set(this.downLoadTime, "1", (this.downLoadTime[0] * 1 + 2000).toString());
          this.$message.error('视频下载时长限制20分钟');
        }
      },
      //准备下载,调整下载日期
      preDownload() {
        this.$set(this.downLoadTime, "0", Tool.formatDate(this.form.videoTime, "yyyyMMdd") + '000000');
        this.$set(this.downLoadTime, "1", Tool.formatDate(this.form.videoTime, "yyyyMMdd") + '002000');
        this.downLoadVisible = true
      },
      //下载
      download() {
        let _this = this
        let number = this.form.number
        let channelID = this.form.channelID
        let downLoadST = this.downLoadTime[0]
        let downLoadET = this.downLoadTime[1]
        let waitTime = downLoadET - downLoadST
        let url = `http://**************:10000/api/v1/cloudrecord/video/play/${number}/${channelID}/${downLoadST}/${downLoadET}/video.mp4`
        window.open(url, "_self");
        this.downLoadLoading = true
        this.timeoutId = setTimeout(() => {
          _this.downLoadVisible = false
          _this.downLoadLoading = false
        }, waitTime * 20);
      },
    },
    destroyed() {
      //取消定时任务
      clearTimeout(this.timeoutId)
      clearInterval(this.intervalId)
    }

  }
</script>

<style scoped>
  .noplaylist {
    height: 300px;
    width: 100%;
    line-height: 300px;
    background: #fbfbfb;
    text-align: center;
  }

  .toolbar {
    float: right;
  }

  .video-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
  }

  .timeline {
    position: relative;
    margin-left: 0;
    left: 0;
    bottom: auto;
    width: 100%;
    height: 4vw;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    -webkit-box-shadow: 1px 1px 3px 1px #cbcbcb;
    box-shadow: 1px 1px 3px 1px #cbcbcb;
  }

  .timelineControl {
    width: 42px;
    height: 100%;
    float: left;
  }

  .timelineMain {
    width: 730px;
    height: 100%;
    cursor: pointer;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .timelineLabel {
    position: absolute;
    height: 30px;
    width: 44px;
    margin-left: -22px;
    z-index: 20;
    top: -16px;
    transition: all 0.1s ease;
  }

  .timelineLabelpointer:after {
    content: "";
    display: block;
    height: 6px;
    width: 1px;
    background: #494848;
    margin-left: -0.5px;
  }

  .caliperPartA,
  .caliperPartB {
    position: absolute;
    height: 100%;
    top: 0;
    background-color: rgba(40, 40, 40, 0.21);
  }

  .caliperPartA {
    left: 0;
  }

  .caliperPartB {
    right: 0;
  }

  .timelineLabelcontent {
    width: 100%;
    height: 25px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 2px;
    color: #efefef;
    line-height: 27px;
    text-align: center;
    font-size: 12px;
  }

  .timelineLabelpointer {
    height: 0;
    width: 0;
    border: 6px solid rgba(0, 0, 0, 0.7);
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
    margin-left: 16px;
  }

  .timeHour {
    box-sizing: border-box;
    height: 6px;
    border-bottom: 1px solid #c2c2c4;
    border-top: 1px solid #c2c2c4;
    border-left: 1px solid #a3a3a4;
    width: 30px;
    float: left;
    margin-top: 16px;
    background-color: #c2c2c4;
  }

  .caliperA,
  .caliperB {
    height: 60px;
    position: absolute;
    width: 20px;
    margin-left: -10px;
  }

  .caliperPointerA,
  .caliperPointerB {
    height: 0;
    width: 0;
    border: 10px solid #45a1f3;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-left-width: 10px;
    border-right-width: 10px;
    cursor: pointer;
  }

  .caliperPointerA {
    margin-left: -9px;
    border-right-color: #45a1f3;
  }

  .caliperPointerB {
    margin-left: 9px;
    border-left-color: #45a1f3;
  }

  .caliperLine {
    height: 50px;
    width: 2px;
    margin: 0 auto;
    background-color: #45a1f3;
  }

  .caliperA {
    left: 0;
    display: none;
  }

  .caliperB {
    left: 721px;
    display: none;
  }

  .timeHourFirst {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
  }

  .timeHourFinal {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    border-right: 1px solid #a3a3a4;
  }

  .timeNumber {
    position: absolute;
    height: 15px;
    width: 15px;
    font-size: 11px;
    bottom: 8px;
    color: #222;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .timelineProgress {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAACxMAAAsTAQCanBgAAAGcSURBVDjLnZTNTsJAEMcb4wVPihw46M3EhMRX8arRxDciQQqojyGacG6hJz48GTAGvVBOQoyFGkNhnH+3pdvSKrrJdCednV9mZ3ZGISIFousNWQ5Z8iwtFkfXWISe92zR84oSAaVwuNls02BgkmVZtFgsXIGOf7B5wFQS6Jil1u+/0Hw+p6QFG85oWqPm+QQghFuvGzQej2ndhbPwYd8DCWSoCPuvCz4MUV0Qf47a7Y6bB3mZH0QXd0T7N0R716zfEw2tMAg+rVYHsBxAhWg0T3zD7RLRRlHsO2XWL1nnvfsWG1Ve4dL2bNsOGc+qRJvsmKkEslsW/06rYdB0OgXoERF9Ra+1pRKly8I54+2QdEXA5DWbOQC9J4NKHkiKKg7kOAzSGhZAz7b9GTKe3LJDMYjEBwICW8LVDNU0hyFjdyQS6ydYTnZvFJvsgsIvNL78XOpzTmyWS5+9EgWIKz988YT8l70S1foP0lDlFsn9p0UYQmIaGHFN+7pW0+LsStMmjZHJZLIEQBdjpPPrGAlEWw62B9bhSK7+w2D7BslIL12udjO3AAAAAElFTkSuQmCC);
    height: 40px;
    width: 40px;
    position: absolute;
    cursor: pointer;
    top: 0px;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 10;
    margin-left: -20px;
    cursor: move;
  }

  .gpsRunTrace {
    height: 6px;
    top: 16px;
    position: absolute;
    /* background-color: #e0ce28; */
    background-color: #6bccfb;
  }

  .runPart {
    height: 6px;
    top: 16px;
    position: absolute;
    background-color: #2398ff;
    /* border: 1px solid #218eef */
  }


  .times {
    width: 68px;
    margin-left: 10px;
    float: left;
    margin-top: 15px;
    color: #2398ff;
  }

  .speed {
    float: left;
    margin-top: 20px;
    color: #2398ff;
    margin-left: 10px;
  }
</style>
