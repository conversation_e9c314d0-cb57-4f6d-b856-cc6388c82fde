<template>
  <div class="app-main-content">
    <searchbar ref="searchbar"
               :searchItems="searchItems"
               :pagination="pagination"
               @resizeSearchbar="resizeSearchbar"
               @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table"
              :data="list"
              highlight-current-row
              v-loading="listLoading"
              border
              ref="singleTable"
              style="width: 100%"
              :max-height="tableHeight"
              @current-change="handleSelectionChange"
              @sort-change="handleSort">
      <el-table-column fixed="left"
                       width="50">
        <template slot-scope="scope">
          <el-radio v-model="radio"
                    :label="scope.row.argmtWtPk">&nbsp;</el-radio>
        </template>
      </el-table-column>
      <el-table-column prop="argmtCd"
                       label="运单号"
                       width="220">
        <template slot-scope="scope">
          <el-button @click="showDetail(scope.row)"
                     slot="reference"
                     type="text">
            {{ scope.row.argmtCd }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="cd"
                       label="牵引车"
                       width="90"></el-table-column>
      <el-table-column prop="traiCd"
                       label="挂车号"
                       width="90"></el-table-column>
      <el-table-column prop="dvNm"
                       label="驾驶员"
                       width="90"></el-table-column>
      <el-table-column prop="scNm"
                       label="押运员"
                       width="90"></el-table-column>

      <el-table-column v-if="placeType === 'wb'"
                       prop="icCd"
                       label="充装类型"
                       align="center"
                       width="80">
        <template slot-scope="scope">
          <span>{{ icCdType(scope.row.icCd) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodsNm"
                       label="货物"
                       width="220"></el-table-column>
      <el-table-column prop="loadQty"
                       label="货物重量(KG)"
                       width="120"></el-table-column>
      <el-table-column prop="csnorWhseDist"
                       label="起运地"
                       width="150"></el-table-column>
      <el-table-column prop="csneeWhseDist"
                       label="目的地"
                       width="150"></el-table-column>
      <el-table-column prop="regPot"
                       label="记录地点"
                       width="180"></el-table-column>
      <el-table-column prop="wtTm"
                       label="登记时间"
                       width="150"></el-table-column>
      <el-table-column v-if="placeType === 'wb'"
                       label="装卸地址"
                       width="115">
        <template slot-scope="scope">
          <el-button type="primary"
                     plain
                     size="mini"
                     @click="loadAddr(scope.row)"
                     icon="el-icon-location">装卸地址</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf">
        <el-button type="primary"
                   size="small"
                   :disabled="radio == null"
                   v-on:click="histroyTra">历史轨迹</el-button>
      </div>
      <el-pagination background
                     layout="sizes, prev, next"
                     @current-change="handleCurrentChange"
                     @size-change="handleSizeChange"
                     :page-size="pagination.limit"
                     :page-sizes="[20, 30, 50, 100, 200]"
                     style="float: right">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { allListNoCount, goodsList } from "@/api/wb";
import { getFuzzyTracCd } from "@/api/vec";

import { getEntpLocList } from "@/api/prodLoad";

export default {
  components: {
    Searchbar,
  },
  data: function () {
    return {
      placeType: "",
      showList: true,
      tableHeight: 500,
      title: null,
      showQuery: false,
      currentRow: null,
      radio: null,
      listLoading: false,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      searchItems: {
        normal: [
          {
            name: "牵引车",
            field: "cd",
            type: "fuzzy",
            dbfield: "cd",
            dboper: "eq",
            api: this.getTracCd,
          },
          {
            name: "挂车号",
            field: "traiCd",
            type: "fuzzy",
            dbfield: "trai_cd",
            dboper: "eq",
            api: this.getTraiCd,
          }, // 省市区
          {
            name: "货物",
            field: "prodPkYzd",
            type: "filterselect",
            dbfield: "prod_pk_yzd",
            dboper: "nao",
            options: [],
          },
          {
            name: "登记时间",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            field: "wtTm",
            type: "daterange",
            dbfield: "wt_tm",
            dboper: "bt",
          },
          {
            name: "记录地点",
            field: "regPot",
            type: "filterselect",
            dbfield: "reg_pot",
            dboper: "eq",
            options: [],
          },
        ],
        more: [],
      },
      defaultSearchItems: [
        { field: "is_yzd", op: "nao", data: "1" },
        // { field: "ic_cd", op: "ne", data: "0" },
      ],
      pagination: {
        page: 1,
        limit: 15,
      },
      weightDate: "",
      pickerOptions2: {
        shortcuts: [
          {
            text: "今天",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created () {
    let filters = {
      groupOp: "AND",
      rules: [],
    };
    filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
    getEntpLocList(params)
      .then((response) => {
        if (response.code == 0) {
          // entpName
          this.searchItems.normal[this.searchItems.normal.length - 1].options =
            response.page.list.map((item, index) => {
              return {
                label: item.entpName,
                value: item.entpName,
              };
            });
        }
      })
      .catch((error) => {
        console.log(error);
      });
    goodsList()
      .then((response) => {
        if (response.code == 0) {
          console.log(response);
          this.searchItems.normal[2].options = response.data.map(
            (item, index) => {
              return {
                label: item.nm,
                // prodPk存在null的情况，前后端统一规定传unknow返回空list
                value: item.prodPk == null ? 'unknow' : item.prodPk,
              };
            }
          );
          console.log(this.searchItems.normal[2].options)
        }
      })
      .catch((error) => {
        console.log(error);
      });
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);

    let path = this.$route.path;
    this.setTableHeight();
    this.getList();
  },
  destroyed () {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //判断充装类型
    icCdType (iccdType) {
      var iccdMap = {
        0: "途径登记",
        1: "充装",
        "-1": "卸货",
      };
      return iccdMap[iccdType];
    },
    showQueryPanel: function () {
      this.showQuery = !this.showQuery;
    },
    delRows: function (index, row) {
      //删除过磅数据
      if (row && row.argmtWtPk) {
        var argmtWtPk = row.argmtWtPk;
        var that = this;
        this.$confirm("您是否确定删除该过磅数据？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
          .then(() => {
            $.ajax({
              url: baseURL + "argmtWt/del/",
              type: "GET",
              data: { pk: argmtWtPk },
              success: function (data) {
                if (data.pojo.json.success) {
                  this.$message({
                    type: "success",
                    message: "删除成功!",
                  });
                } else {
                  that.$message({
                    message: "删除数据失败",
                    type: "error",
                  });
                }
              },
              error: function (error) {
                that.$message({
                  message: "删除数据失败",
                  type: "error",
                });
              },
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    loadAddr (row) {
      this.$router.push({
        path: "/base/wb/form/" + row.argmtWtPk,
        query: { ipPk: row.ipPk },
      });
    },
    showDetail (row) {
      let argmtPk = row.argmtPk;
      this.$router.push({ path: "/base/rteplan/bills/" + argmtPk });
    },
    //历史轨迹
    histroyTra () {
      let location = window.location;
      let vehicleNo = this.currentRow.cd || this.currentRow.traiCd;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack?v=" +
        encodeURIComponent(vehicleNo) +
        "&t=" +
        Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },
    //单选事件
    handleSelectionChange (currentRow, oldCurrentRow) {
      this.radio = currentRow.argmtWtPk;
      this.currentRow = currentRow;
    },

    loadQtyFormatter: function (row, column) {
      var value = row.loadQty;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      }
      if (value > 1000) {
        return value;
      } else {
        return (value * 1000).toFixed(0);
      }
    },
    weighFormatter: function (row, column) {
      var value = row.weigh;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      } else {
        return value;
      }
    },

    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort (sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    getList (data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      this.radio = null; //清空单选
      //关闭loading
      this.listLoading = true;
      
      //查询列表
      allListNoCount(param)
        .then((response) => {
          if (response.code == 0) {

            _this.list = response.list;
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //牵引车模糊搜索
    async getTracCd (queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(
        (error) => {
          cb([]);
          console.log(error);
        }
      );
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map((item) => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd (queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(
        (error) => {
          cb([]);
          console.log(error);
        }
      );
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map((item) => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
  },
};
</script>

<style scoped>
.el-table .cell,
.el-table th > div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
