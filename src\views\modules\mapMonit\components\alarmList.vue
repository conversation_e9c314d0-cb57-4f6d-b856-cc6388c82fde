<template>
	<div class="mod-container" v-loading="detailLoading">
				<el-table
                :max-height="tableHeight"
                :data="dataList"
                border
                style="width: 100%;">
                    <el-table-column
                        prop="catNmCn"
                        header-align="center"
                        align="center"
                        fixed
                        label="报警状态">
                    </el-table-column>

                    <!-- <el-table-column
                        prop="catNmCn"
                        header-align="center"
                        align="center"
                        :formatter="typeFormatter"
                        label="报警状态">
                    </el-table-column> -->

                    <el-table-column
                        prop="entpNm"
                        header-align="center"
                        align="center"
                        width="150"
                        label="承运商">
                        <template slot-scope="scope">
                            <span :title="scope.row.entpNm">{{scope.row.entpNm || ''}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="tractorNo"
                        header-align="center"
                        align="center"
                        label="牵引车">
                    </el-table-column>
                    <el-table-column
                        prop="trailerNo"
                        header-align="center"
                        align="center"
                        label="挂车">
                        <template slot-scope="scope">
                            <span>{{scope.row.trailerNo || '无电子运单'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="driverNm"
                        header-align="center"
                        align="center"
                        label="驾驶员">
                        <template slot-scope="scope">
                            <span>{{scope.row.driverNm || '无电子运单'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="guardsNm"
                        header-align="center"
                        align="center"
                        label="押运员">
                        <template slot-scope="scope">
                            <span>{{scope.row.guardsNm || '无电子运单'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="alarmTime"
                        header-align="center"
                        align="center"
                        width="150"
                        label="报警时间">
                        <template slot-scope="scope">
                            <span>{{scope.row.alarmTime | FormatDate('yyyy-MM-dd HH:mm:ss')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="alarmLocation"
                        header-align="center"
                        align="center"
                        label="报警地点">
                        <template slot-scope="scope">
                            <span :title="scope.row.alarmLocation">{{scope.row.alarmLocation || ''}}</span>
                        </template>
                    </el-table-column>
                     <!-- <el-table-column
                        prop="threshold"
                        header-align="center"
                        align="center"
                        label="通行证线路">
                    </el-table-column> -->
                    <el-table-column
                        prop="descr"
                        header-align="center"
                        align="center"
                        label="报警详情">
                        <template slot-scope="scope">
                            <span :title="scope.row.descr">{{scope.row.descr || ''}}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[10,20, 30, 50, 100, 200]" style="float:right;" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
            </el-pagination>
	</div>
</template>
<script>
import * as $http from '@/api/violationAlarm';
import * as Tool from '@/utils/tool';
export default {
    name: 'alarmInfo',
    data() {
        return {
            detailLoading: false,
            tableHeight: Tool.getClientHeight()-200,
            dataList: null,
            pagination: {
                page: 1,
                limit: 10,
                total: 0
            }
        };
    },
    methods: {
		// 初始化
		initByPk(){
            let _this = this;
            let date = Tool.formatDate(new Date(),'yyyy-MM-dd')//今日日期
            this.detailLoading = true; 
            let filters = {"groupOp":"AND","rules":[{"field":"alarm_time","op":"bt","data":[date+" 00:00:00",date+" 23:59:59"]}]}
            let param = Object.assign({filters:filters},this.pagination);
			$http.getViolationAlarmList(param).then(response => {
                if (response && response.code === 0) {
                    this.dataList = response.page.list
                    this.pagination.total = response.page.totalCount;
                    
                } else {
                    this.dataList = []
                    this.pagination.total = 0
                }
                _this.detailLoading = false;
            }).catch(error => {
                console.log(error);
                _this.detailLoading = false;
            });
        },
        // 分页跳转
        handleCurrentChange: function(val) {
            this.pagination.page = val;
            this.initByPk();
        },

        // 分页条数修改
        handleSizeChange: function(val) {
            this.pagination.limit = val;
            this.initByPk();
        },
        typeFormatter: function(row, column) {
            switch (row.handler) {
            case 0:
                return row.catNmCn +"未推送";
                break;
            case 1:
                return "已推送";
                break;
            case 2:
                return "已受理";
                break;
            case 4:
                return "备案错误";
                break;
            case 5:
                return "填报错误";
                break;
            case 6:
                return "超载小于0.5吨";
                break;
            case 7:
                return "批评教育";
                break;
            case 8:
                return "纳入考核";
                break;
            case 9:
                return "约谈警示";
                break;
            case 10:
                return "行政处罚";
                break;
            case 11:
                return "停办通行证";
                break;
            case 12:
                return "其他";
                break;

            }
        }
    }
};
</script>
<style scoped>
.mod-container{
    padding-bottom: 40px;
}
</style>
