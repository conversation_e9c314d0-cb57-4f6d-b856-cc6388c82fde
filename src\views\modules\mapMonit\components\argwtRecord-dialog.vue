<template>
  <div>
    <el-table
      v-loading="loading"
      :data="dataList"
      max-height="500"
      style="width: 100%">
      <el-table-column prop="wtTm" label="时间" width="180"></el-table-column>
      <el-table-column prop="regPot" label="地点"></el-table-column>
      <el-table-column prop="weigh" label="过磅重量(KG)" width="180"></el-table-column>
    </el-table>
  </div>
</template>

<script>
  import * as $http from "@/api/mapMonit";

  export default {
    name: "argwtRecord-dialog",
    data() {
      return {
        loading: true,
        dataList: []
      }
    },
    methods: {
      getArgwtRecordList(cd) {
        let params = {
          cd: cd
        }
        this.loading = true
        $http.getArgwtRecordList(params).then(res => {

          this.loading = false
          if (res && res.code === 0) {
            this.dataList = res.data
          }else{
            this.dataList = []
          }
        }).catch(err=>{
          this.loading = false
        })
      }
    },
  }
</script>

<style scoped>

</style>
