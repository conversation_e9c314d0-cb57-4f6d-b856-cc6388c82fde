<template>
    <div>
         <simple-table :tableHeader="tableHeader" :tablePage="tablePage"></simple-table>       
    </div>
</template>
<script>
import {getChem} from '@/api/mapMonit'
import SimpleTable from '@/components/SimpleTable'
export default {
    data(){
        return {
            tableHeader:[
                {name:"货物名称",field:"nm"},
                {name:"危险品学名",field:"chemNm"},
                {name:"GB编码",field:"chemGb"},
                {name:"GB类别",field:"chemGbLv"}
            ],
            tablePage:{
                list:[],
                currPage:0,
                pageSize:20,
                totalPage:0
            }
        }
    },
    components:{
        SimpleTable
    },
    methods:{
        //获取周边场所设施信息
        getChemList(ipPk){
            getChem(ipPk).then( res => {
                if(res.code == 0){
                    this.tablePage.list = res.data;
                };
            });
        }
    }
}
</script>