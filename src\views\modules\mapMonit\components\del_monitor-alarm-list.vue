<template>
  <div class="float-panel noselect" :style="defaultStyle" ref="monitorAlarmList">
    <div class="float-panel-header" :style="{'border-radius':collapseIcon=='el-icon-arrow-up'?'5px 5px 0 0':'5px','border-bottom':collapseIcon=='el-icon-arrow-up'?'3px solid rgb(18, 69, 199)':'0'}">
      <!-- <svg-icon icon-class="notice" class-name="tooltip-icon"></svg-icon> -->
      <span>车辆报警：<span style="color:red;font-weight:600;font-size:18px;">{{vecListPage.count}}</span></span>
      <i class="header-icon" :class="collapseIcon" @click="collapseHandle"></i>
    </div>
    <collapse-transition>
      <div class="float-panel-body" v-show="collapseIcon=='el-icon-arrow-up'">
        <div class="track-content">
          <div class="alarmText" v-if="vecListPage.alarmText">{{vecListPage.alarmText}}</div>
          <ul class="vec-list-ul">
            <li v-for="item in vecListPage.list" :key="item._id" @click="showAlarmDialog(item.tractorNo,item.alarmPk)">
              <div class="clearfix">
                <div class="vec-no">{{item.tractorNo }}</div>
                <!-- <div :title="'当前定位'" style="float:left;margin-top:3px;" @click.stop="getPosition(item.vehicleNo)"><svg-icon icon-class="position"></svg-icon></div> -->
                <!-- <div style="float:right;margin-top:10px;color:#333;">报警({{item.handleRemark}}) </div> -->
              </div>
              <div class="vec-alarm">
                <div style="font-size:14px;color:#333;">
                  <svg-icon icon-class="alarmRed-1"></svg-icon>{{item.descr }}
                </div>
                <div>
                  {{item.alarmLocation }}
                </div>
                <div>
                  {{Tool.getDateDiff(item.alarmTime) }}
                </div>
              </div>
              <!-- <div class="vec-time">{{item.updateDate | FormatDate('MM-dd HH:mm')}}</div> -->
            </li>
          </ul>
        </div>
        <div class="track-footer">
          <span class="page-no">{{vecListPage.currPage || 0}}/{{vecListPage.totalPage || 0}} 页</span>
          <div class="prev-page" @click="prevPage" title="上一页" :class="[vecListPage.currPage<=1 ? 'disabled' : '']"><i class="el-icon-arrow-up"></i></div>
          <div class="next-page" @click="nextPage" title="下一页" :class="[vecListPage.currPage>=vecListPage.totalPage ? 'disabled' : '']"><i class="el-icon-arrow-down"></i></div>
        </div>
        <!-- <router-link :to="{'path':'/kpi/realtime/list'}" tag='a' target='_blank'> -->
        <a href="javascript:void(0)" @click="showEnforceLog">
          <div class="chrome">查看车辆实时处置记录</div>
        </a>
        <!-- </router-link> -->
        <router-link :to="{'path':'/chromeAudio'}" tag='a' target='_blank'>
          <div class="chrome">chrome浏览器如何打开提示音？</div>
        </router-link>
      </div>
    </collapse-transition>
    <!-- 获取地图位置 -->
    <!-- <map-dialog v-if="dialogPositionVisible" ref="getPos" :vecNo="vecNoPos"></map-dialog> -->
    <!-- 危化品信息详情 -->
    <el-dialog title="车辆实时处置记录" :visible.sync="visibleDg" append-to-body width="80%" class="detail-dialog">
      <realtime-list></realtime-list>
    </el-dialog>
  </div>
</template>

<script>
import collapseTransition from "@/components/CollapseTransition";
import * as Tool from "@/utils/tool";
import realtimeList from "@/views/modules/kpi/realtime/list";
// import AlarmInfo from '@/views/modules/monit/base/alarm/alarmList';
// import mapDialog from './monitor-map-dialog.vue'
export default {
  name: "monitorAlarmList",
  props: {
    // default style
    defaultStyle: {
      type: Object,
      default: function() {
        return {
          position: "absolute",
          top: "10px",
          left: "220px",
          // height:'600px',
          width: "200px",
          // 'margin-left':'10px',
          // 'margin-top':'60px'
          zIndex: 30
        };
      }
    },
    // vec data
    vecListPage: {
      type: Object,
      default: function() {
        return {
          list: [],
          currPage: 1,
          pageSize: 3,
          totalPage: 1,
          count: 0,
          alarmText: ""
        };
      }
    }
  },
  data() {
    return {
      collapseIcon: "el-icon-arrow-down",
      loading: null,
      visibleDg: false,
      dialogPositionVisible: false,
      vecNoPos: null,
      Tool: Tool
    };
  },
  watch: {
    vecListPage(newName, oldName) {
      if (newName.list && newName.list.length == 0) {
        this.collapseIcon = "el-icon-arrow-down";
      } else {
        this.collapseIcon = "el-icon-arrow-up";
      }
    }
  },
  components: {
    collapseTransition,
    // AlarmInfo,
    // mapDialog
    realtimeList
  },
  methods: {
    // collapse panel effect event
    collapseHandle() {
      if (this.collapseIcon == "el-icon-arrow-down") {
        this.collapseIcon = "el-icon-arrow-up";
      } else {
        this.collapseIcon = "el-icon-arrow-down";
      }
      if (this.vecListPage.list.length == 0) {
        this.collapseIcon = "el-icon-arrow-down";
      }
    },

    // prev page
    prevPage() {
      if (this.vecListPage.currPage > 1) {
        this.vecListPage.currPage--;
        this.getListHandle();
      }
    },

    // next page
    nextPage() {
      if (this.vecListPage.currPage < this.vecListPage.totalPage) {
        this.vecListPage.currPage++;
        this.getListHandle();
      }
    },
    // loading effect
    fullNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.monitorSearchBar
      });
      return loading;
    },
    // search vec list event
    getListHandle() {
      let _this = this;
      this.loading = this.fullNodeLoading();
      this.$emit(
        "alarmList",
        this.vecListPage.currPage,
        this.vecListPage.pageSize,
        function() {
          _this.loading.close();
        }
      );
    },
    // 报警信息弹窗
    showAlarmDialog(vecNo, alarmPk) {
      if (!vecNo) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.$emit("vecSite", vecNo, alarmPk);
    },
    // getPosition(vecNo){//查看当前位置
    //     this.dialogPositionVisible=true
    //     this.vecNoPos = vecNo
    //     this.$nextTick(() => {
    //         this.$refs.getPos.getLngLat();
    //     });
    // }
    showEnforceLog() {
      this.visibleDg = true;
    }
  }
};
</script>

<style scoped>
.float-panel .float-panel-header {
  position: relative;
  height: 42px;
  line-height: 32px;
  background-color: rgba(28, 91, 250, 0.7);
  box-shadow: 0 2px 2px #aaa;
  color: #fff;
  box-sizing: border-box;
  padding: 5px 8px;
  border-radius: 5px 5px 0 0;
  font-size: 14px;
  text-align: center;
}
.float-panel .float-panel-header .header-icon {
  float: right;
  line-height: 32px;
  cursor: pointer;
}

.float-panel .float-panel-body {
  background: #fff;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
}
.track-content {
  padding: 0;
  max-height: 50vh;
  overflow-y: auto;
}
.track-footer {
  box-sizing: border-box;
  box-sizing: border-box;
  line-height: 30px;
  font-size: 12px;
  color: #333;
  padding-right: 10px;
  text-align: right;
  padding-bottom: 10px;
}
.page-no {
  margin-right: 10px;
}
.prev-page,
.next-page {
  display: inline-block;
  border: 1px solid #eee;
  width: 30px;
  height: 25px;
  line-height: 25px;
  margin: 0;
  padding: 0;
  text-align: center;
  cursor: pointer;
  color: #2c81ff;
}
.prev-page:hover,
.next-page:hover {
  background-color: #f5f5f5;
}
.prev-page.disabled,
.next-page.disabled {
  color: #cecece;
  cursor: no-drop;
}
.vec-list-ul {
  /* color: #0e800c; */
  padding: 0;
  margin: 0;
}
.vec-list-ul > li {
  box-sizing: border-box;
  position: relative;
  /* height: 35px; */
  /* line-height: 34px; */
  border-bottom: 1px dashed #eee;
  font-size: 12px;
  color: #999;
  cursor: pointer;
  padding: 10px;
}
.vec-list-ul > li:hover {
  background-color: #f4f4f4;
}
.vec-list-ul .vec-no {
  width: 70px;
  text-align: center;
  float: left;
  background-color: #1671fd;
  border-radius: 2px;
  padding: 6px 5px;
  color: #fff;
}
.vec-alarm {
  padding: 5px;
  line-height: 20px;
  margin-top: 5px;
  background-color: #1672fd0a;
}
/* .vec-list-ul .vec-time{
    float:right;
} */
.svg-icon {
  width: 1.5em !important;
  height: 1.5em !important;
  vertical-align: -0.3em !important;
}
.alarmText {
  background-color: #fdf6ec;
  color: #e6a23c;
  font-size: 12px;
  padding: 10px;
}
.chrome {
  font-size: 12px;
  text-align: center;
  color: #1671fd;
  text-decoration: underline;
  padding-bottom: 10px;
  cursor: pointer;
}
</style>
<style>
.anchorBL {
  display: none;
}
</style>
