<template>
  <el-dialog
    :loading="loading"
    append-to-body
    :visible.sync="visible"
    title="风险管控"
    :close-on-click-modal="false"
    width="80%"
  >
    <el-form
      size="small"
      label-width="100px"
      :model="formData"
      ref="enforceForm"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="运输企业：">
            <span>{{ formData.carrierNm }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车牌：">
            <span>{{ formData.tracCd }}</span>
            <span v-if="formData.traiCd">/ {{ formData.traiCd }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="驾驶员：">
            <span>{{ formData.dvNm }}</span>
            <span v-if="formData.dvMob">( {{ formData.dvMob }} )</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="押运员：">
            <span>{{ formData.scNm }}</span>
            <span v-if="formData.scMob">( {{ formData.scMob }} )</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发货地：">
            <span>{{ formData.csnorWhseDist }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卸货地：">
            <span>{{ formData.csneeWhseDist }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前位置：">
            <span>{{ formData.alarmLocation }}</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="当前速度：">
            <span>{{ formData.speed }} km/小时</span>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="货物：">
            <span>{{ formData.goodsNm }}</span>
            <span>( {{ formData.loadQty }}吨 )</span>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="处置时间："
            prop="foundTm"
            :rules="$rulesFilter({ required: true })"
          >
            <el-date-picker
              v-model="formData.foundTm"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
            <!-- <el-input v-model="formData.foundTm" readonly></el-input> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="操作员："
            prop="oprNm"
            :rules="$rulesFilter({ required: true })"
          >
            <el-autocomplete
              class="inline-input"
              v-model="formData.oprNm"
              :fetch-suggestions="querySearch"
              placeholder="请输入内容"
              clearable
              @select="handleSelect"
            ></el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="处置操作："
            prop="isHandle"
            :rules="$rulesFilter({ required: true })"
          >
            <el-radio-group
              v-model="formData.isHandle"
              class="custom-radio-group"
            >
              <el-radio
                v-for="item in alarmDealOptions.realtime"
                :key="alarmDealActions[item].value"
                :label="alarmDealActions[item].value"
                >{{ alarmDealActions[item].label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="详情描述："
            prop="oprContent"
            :rules="$rulesFilter({ required: true })"
          >
            <el-input
              clearable
              v-model="formData.oprContent"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="submitbox">
      <el-button type="default" size="small" @click="visible = false"
        >取消</el-button
      >
      <el-button type="primary" size="small" @click="save">提交</el-button>
    </div>

    <el-table :data="todayRecordList" style="width: 100%;margin-bottom: 30px;">
      <el-table-column prop="time" label="处置时间"></el-table-column>
      <el-table-column prop="oprNm" label="操作员"></el-table-column>
      <el-table-column prop="oprType" label="处置操作">
        <template slot-scope="{ row }">
          {{
            alarmDealActions[row.oprType]
              ? alarmDealActions[row.oprType].label
              : row.oprType
          }}
        </template>
      </el-table-column>
      <el-table-column prop="oprDesc" label="详情描述"></el-table-column>
    </el-table>
  </el-dialog>
</template>
<script>
import { extendObj } from "@/utils/tool";
import { oprlist } from "@/api/mapMonit";
import { latestTime } from "@/api/common";
import { offsiteSave, getTodayOffsiteList } from "@/api/offsite";
import { mapGetters } from "vuex";
export default {
  name: "enforceOnLineDialog",
  props: {
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      // alarmDealActions: alarmDealActions,
      // alarmDealOptions: alarmDealOptions,
      lnglat: "",
      geoc: null,
      formData: {
        carrierNm: "", //运输企业名称
        carrierPk: "", //承运商主键
        traiPk: "", //挂车主键
        traiCd: "", //挂车号
        tracPk: "", //牵引车主键
        tracCd: "", //牵引车号
        dvPk: "", //驾驶员主键
        dvNm: "", //驾驶员名字
        dvMob: "", //手机号
        dvCd: "", //驾驶员身份证
        scPk: "", //押运员主键
        scNm: "", //押运员名字
        scMob: "", //押运员手机
        scCd: "", //押运员身份证
        csnorWhseDist: "", //发货地
        csnorWhseDistCd: "", //发货区域编码
        csneeWhseDist: "", //卸货地
        csneeWhseDistCd: "", //卸货区域
        enchPk: "", //货物主键
        goodsNm: "", //货物名称
        loadQty: "", //计划装运量
        prodPk: "", //
        dangGoodsNm: "", //
        alarmLocation: "", //报警点地址
        alarmLongitude: "", //报警点经度
        alarmLatitude: "", //报警点纬度
        speed: "", //车速
        foundTm: "", //发现时间
        oprNm: "", //操作员
        isHandle: "",
        oprContent: "" //操作反馈
      },
      opratorList: [],
      todayRecordList: []
    };
  },
  computed: {
    ...mapGetters([
      "alarmDealActions",
      "allAlarmDealOptions",
      "alarmDealOptions"
    ])
  },
  methods: {
    init(data) {
      this.resetForm();
      this.visible = true;
      let dataSource = extendObj({}, data.rteplan, data.selectVecInfo);

      oprlist()
        .then(res => {
          if (res.code == 0) {
            this.opratorList = res.list.map(item => {
              return { value: item, label: item };
            });
          } else {
            this.opratorList = [];
          }
        })
        .catch(err => {
          this.opratorList = [];
        });

      dataSource.speed = dataSource.speed ? dataSource.speed : 0;

      for (var k in this.formData) {
        if (dataSource[k] == null || dataSource[k] == undefined) continue;
        this.formData[k] = dataSource[k];
      }

      this.lnglat = dataSource["lonBd"] + "," + dataSource["latBd"];

      this.formData["alarmLongitude"] = dataSource["lonBd"];
      this.formData["alarmLatitude"] = dataSource["latBd"];

      this.geocoder(
        new BMap.Point(dataSource["lonBd"], dataSource["latBd"]),
        str => {
          if (str) this.$set(this.formData, "alarmLocation", str);
        }
      );

      latestTime()
        .then(res => {
          if (res.code == 0) this.formData.foundTm = res.nowStr;
          else this.formData.foundTm = "";
        })
        .catch(err => {
          this.formData.foundTm = "";
        });

      this.getTodayRecordList();
    },
    querySearch(queryString, cb) {
      var opratorList = this.opratorList;
      var results = queryString
        ? opratorList.filter(this.createFilter(queryString))
        : opratorList;

      cb(results);
    },
    createFilter(queryString) {
      return opratorList => {
        return (
          opratorList.value.toLowerCase().indexOf(queryString.toLowerCase()) !=
          -1
        );
      };
    },
    handleSelect(item) {},
    resetForm() {
      this.lnglat = "";
      // this.$refs.enforceForm && this.$refs.enforceForm.resetFields();
      for (var k in this.formData) {
        this.formData[k] = "";
      }
    },
    geocoder(point, cb) {
      if (!this.geoc) {
        this.geoc = new BMap.Geocoder();
      }

      this.geoc.getLocation(point, function(rs) {
        let addComp = rs.addressComponents;
        let str = "";

        /* if(addComp.province){
                    str += addComp.province
                }

                if(addComp.city){
                    str += addComp.city
                } */

        if (addComp.district) {
          str += addComp.district;
        }

        if (addComp.street) {
          str += addComp.street;
        }

        if (addComp.streetNumber) {
          str += addComp.streetNumber;
        }

        cb(str);
      });
    },
    save() {
      let _this = this;
      this.$refs.enforceForm.validate(valid => {
        if (valid) {
          this.$confirm("确定提交吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              _this.loading = true;
              let postData = Object.assign({}, this.formData);
              postData.catCd = "2550.180.150";
              offsiteSave(postData)
                .then(res => {
                  this.loading = false;
                  if (res.code == 0) {
                    this.$message({
                      type: "success",
                      message: "提交成功!"
                    });
                    this.visible = false;
                  } else {
                    this.$message.info(res.msg || "服务器错误，请联系管理员");
                  }
                })
                .catch(err => {
                  this.loading = false;
                });
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    },
    // 获取今日记录
    async getTodayRecordList() {
      const params = {
        vecNo: this.formData.tracCd
      };
      const res = await getTodayOffsiteList(params);
      if (res.code === 0) {
        this.todayRecordList = res.list;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/ {
  .custom-radio-group.el-radio-group {
    line-height: 28px;

    .el-radio {
      margin-right: 15px;
      min-width: 108px;
    }
    .el-radio + .el-radio {
      margin-left: 0;
    }
  }
}
.submitbox {
  margin: 10px 0 20px 0;
  text-align: right;
}
</style>
