<template>
  <div class="highway-posts-info-window">
    <div class="close-btn" @click="close">×</div>
    <div class="info-name">
        <span>收费站名称：</span>
        <span>{{ data.postname }}</span>
    </div>
    <div class="info-name">
        <span>状态：</span>
        <el-tag v-if="data.postStatus==1" type="success" size="mini">开启</el-tag>
        <el-tag v-else type="info" size="mini">关闭</el-tag>
    </div>
    <div class="info-name">
        <span style="vertical-align: top;">抓拍图片</span>
        <img v-if="data.url" :src="data.url" @click="showImage(data.url)" width="50" height="50">
        <!-- <el-image style="width: 50px; height: 50px" v-if="data.url" :src="data.url" :preview-src-list="[data.url]"></el-image> -->
    </div>
  </div>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.min.css";
export default {
  name: "",
  props:{
    data:{
        type:Object,
        default(){
            return {}
        }
    }
  },
  data() {
    return {

    };
  },
  methods: {
    // 图片预览
    showImage(url) {
      const divNode = document.createElement("div");
      divNode.style.display = "none";
      const imageNode = document.createElement("img");
      imageNode.setAttribute("src", url);
      imageNode.setAttribute("alt", "图片");
      divNode.appendChild(imageNode);
      document.body.appendChild(divNode);
      const viewer = new Viewer(divNode, {
        url(image) {
          var src = image.src;
          if (/\@\w+\.src$/.test(src)) {
            src = src.replace(/\@\w+\.src$/, "") + "";
          }
          if (/\@\w+$/.test(src)) {
            src = src.replace(/@\S+/, "") + "@2o";
          }
          return src;
        },
        zIndex: 2999,
        hidden() {
          viewer.destroy();
          divNode.remove();
        }
      });
      imageNode.click();
    },
    close() {
      this.$emit("close");
    }
  },
};
</script>

<style lang="scss" scoped>
.highway-posts-info-window{
  position: relative;
  width: 240px;
  height: 140px;
  padding: 10px;
  background: #ffffff;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  

  &::before,&::after{
    content: "";
    position: absolute;
    left: 50%;
    bottom: -20px;
    z-index: 2;
    width: 0;
    height: 0;
    margin-left: -5px;
    border-width: 10px 8px;
    border-style: solid;
    border-color: #f9f9f9 transparent transparent #f9f9f9;
  }

  &::after{
    border-width: 11px 9px;
    bottom: -23px;
    z-index: 1;
    margin-left: -6px;
    border-color: rgba(0,0,0,0.2) transparent transparent rgba(0,0,0,0.2);
  }

  .info-name{
    padding: 6px 0px;
  }

  .close-btn{
    position: absolute;
    right: 3px;
    top: 1px;
    cursor: pointer;
    font-size: 20px;
  }
}
::v-deep{
    .el-image-viewer__mask{
        z-index: 999;
    }
}
</style>