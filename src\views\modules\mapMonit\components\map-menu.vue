<template>
    <div class="mapMenu">
        <div class="mapMenu-btn" @click="menuShow" :style="{'border-radius':show?'5px 5px 0 0':'5px'}">
            <i class="el-icon-menu"></i>   操作菜单
            <div class="mapMenu-close" v-show="show"><i class="el-icon-close"></i></div>
        </div>
        <scale-transition>
            <div class="mapMenu-bag" v-show="show">
                <el-row>
                    <el-col class="mapMenu-item">
                        <div class="mapMenu-icon" @click="exportExcel()">
                            <svg-icon icon-class="export"></svg-icon>
                        </div>
                        <div>导出车辆</div>
                    </el-col>
                    <el-col class="mapMenu-item">
                        <div class="mapMenu-icon" @click="showAlarmDialog()">
                            <svg-icon icon-class="alarmRed"></svg-icon>
                        </div>
                        <div>今日违章</div>
                    </el-col>
                    <el-col class="mapMenu-item">
                        <div class="mapMenu-icon" @click="dialogMapVisible = true">
                            <svg-icon icon-class="map"></svg-icon>
                        </div>
                        <div>地图配置</div>
                    </el-col>
                </el-row>
            </div>
        </scale-transition>
        <!-- 今日违章信息详情 -->
        <el-dialog title="报警信息" :visible.sync="visibleOfAlarm" append-to-body width="80%" top="5vh" class="detail-dialog">
            <alarm-info ref="alarmInfo"></alarm-info>
        </el-dialog>
        <!-- 地图配置 -->
        <el-dialog title="地图配置" width="60%" :visible.sync="dialogMapVisible" append-to-body>
            <el-radio-group v-model="mapForm" size="small">
                <el-radio label="1" border>显示装卸企业</el-radio>
                <el-radio label="2" border>显示所有车辆</el-radio>
                <el-radio label="3" border>只显示区内运输企业车辆</el-radio>
                <el-radio label="4" border>只显示区外运输企业车辆</el-radio>
                <el-radio label="5" border>显示围栏</el-radio>
            </el-radio-group>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogMapVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="submit" size="small">确 定</el-button>
            </div>
        </el-dialog>
    </div>

</template>

<script>
import { ScaleTransition } from 'vue2-transitions'
import AlarmInfo from './alarmList'
import store from '@/store'

export default {
  components: {
    ScaleTransition,
    AlarmInfo
  },
  data() {
    return {
      show: false,
      visibleOfAlarm:false,
      dialogMapVisible:false,
      mapForm:null
    }
  },
  methods:{
      menuShow(){
          this.show = !this.show
      },
      exportExcel(){
          this.$confirm('确定要下载Excel文件么?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        //   var params = qs.stringify({
        //         'token': store.getters.token,
        //         ...this.pagination,
        //         'entpName':''
        //     })
            // console.log(process.env.BASE_API+'/gps/listExport')
            window.location.href = process.env.BASE_API+'/gps/listExport?token='+store.getters.token
        }).catch(() => {
          console.log("点击取消")
        });
      },
      // 报警信息弹窗
    showAlarmDialog() {
        this.visibleOfAlarm = true;
        this.$nextTick(() => {
            this.$refs.alarmInfo.initByPk();
        });
    },
    submit(){//地图配置确定
        this.dialogMapVisible = false
        if(this.mapForm){
            let type = this.mapForm == 3 ? 'in' : this.mapForm == 4 ? 'out':this.mapForm == 1 ?'entp':''
            this.$emit('mapDeploy',type)
        }
        this.$emit('mapMenuType', this.mapForm)

    },

  }
}
</script>
<style scoped>
.mapMenu{
    position: fixed;
    right: 10px;
    bottom: 15px;
    z-index: 999;
    font-size: 12px;
}
.mapMenu-btn{
    background-color: #409EFF;
    padding: 10px;
    color: #fff;
    cursor: pointer;
}
.mapMenu-close{
    float: right;
}
.mapMenu-bag{
    border: 2px solid #409EFF;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
}
.mapMenu-item{
    width:80px;
    text-align: center;
    color:#fff;
}
.mapMenu-icon{
    width:40px;
    height:40px;
    border-radius: 5px;
    background-color:#fff;
    margin: 0 0 5px 20px;
    cursor: pointer;
}
.svg-icon{
    width:34px!important;
    height:34px!important;
    margin-top: 3px!important;
    /* vertical-align:-0.3em; */
}
</style>
