<template>
    <div style="cursor: default;">
        <div class="map-popover">
            <div class="popover-header">
                <b>装卸企业信息</b>
                <span class="popover-close" title="关闭" @click="close">×</span>
            </div>
            <div class="popover-body" style="padding:5px;">
                <table style="width:325px">
                    <tbody>
                        <tr>
                            <th width="120">统一社会信用代码：</th>
                            <td colspan="3" >
                                {{selectEntpInfo.uscCd  ||''}}
                            </td>
                        </tr>
                        <tr>
                            <th width="70">企业名称：</th>
                            <td colspan="3" >
                                {{selectEntpInfo.entpName  ||''}}
                            </td>
                        </tr>
                        <tr>
                            <th width="70">公司类型：</th>
                            <td colspan="3" :title="selectEntpInfo.legalRepIdType">
                                {{selectEntpInfo.legalRepIdType  ||''}}
                            </td>
                        </tr>
                         <tr >
                            <th>企业业务分类：</th>
                            <td colspan="3" :title="selectEntpInfo.catNmCn">{{selectEntpInfo.catNmCn ||''}}</td>
                        </tr>
                        <tr >
                            <th>成立日期：</th>
                            <td colspan="3">{{selectEntpInfo.establishDate||''}}</td>
                        </tr>
                        <!-- <tr >
                            <th>营业期限</th>
                            <td colspan="3">{{selectEntpInfo.busiEndDate||''}}</td>
                        </tr> -->
                        <!-- <tr >
                            <th>企业登记注册地</th>
                            <td colspan="3">{{selectEntpInfo.entpDist||''}}</td>
                        </tr> -->
                        <!-- <tr >
                            <th>经营状态</th>
                            <td colspan="3">{{selectEntpInfo.regStat||''}}</td>
                        </tr> -->
                        <tr >
                            <th>法定代表人：</th>
                            <td colspan="3">{{selectEntpInfo.legalRepNm||''}}</td>
                        </tr>
                        <tr >
                            <th>核准日期：</th>
                            <td colspan="3">{{selectEntpInfo.aprvDate||''}}</td>
                        </tr>
                        <tr >
                            <th>注册资本：</th>
                            <td colspan="3">{{selectEntpInfo.regCaptital  ||''}}{{selectEntpInfo.regCaptitalUnit  ||''}}</td>
                        </tr>
                        <tr >
                            <th>登记机关：</th>
                            <td colspan="3" :title="selectEntpInfo.regDept">{{selectEntpInfo.regDept  ||''}}</td>
                        </tr>
                        <!-- <tr >
                            <th>紧急联系人</th>
                            <td colspan="3">{{selectEntpInfo.erNm}}</td>
                        </tr>
                        <tr >
                            <th>紧急联系人电话</th>
                            <td colspan="3">{{selectEntpInfo.erMob}}</td>
                        </tr> -->
                        <tr >
                            <th>企业地址：</th>
                            <td colspan="3" :title="selectEntpInfo.location">{{selectEntpInfo.location  ||''}}</td>
                        </tr>
                        <tr >
                            <th>营业执照经营范围：</th>
                            <td colspan="3" :title="selectEntpInfo.businessScope">{{selectEntpInfo.businessScope  ||''}}</td>
                        </tr>
                     </tbody>
                </table>
            </div>
            <div class="popover-footer">
                <a href="javascript:void(0);" @click="showStevedoringDialog(selectEntpInfo.ipPk)">装卸记录</a>
                <a href="javascript:void(0);" @click="showChemDialog(selectEntpInfo.ipPk)">货物列表</a>
            </div>
        </div>
        <el-dialog title="今日装卸记录信息" :visible.sync="visibleOfStevedoring" append-to-body width="80%"  top="5vh" class="detail-dialog">
            <stevedoring-info ref="stevedoringinfo" ></stevedoring-info>
        </el-dialog>
        <el-dialog title="货物列表信息" :visible.sync="visibleOfChem" append-to-body width="80%"  top="5vh" class="detail-dialog">
            <chem-info ref="cheminfo" ></chem-info>
        </el-dialog>
    </div>
</template>

<script>
import * as Tool from '@/utils/tool'

import StevedoringInfo from './stevedoring-info'
import ChemInfo from './chem-info'

export default {
    name:'MonitorCpInfoWindow',
    props:{
        selectEntpInfo:{
            type:Object
        }
    },
    components:{
        StevedoringInfo,
        ChemInfo
    },
    data (){
        return {
            visibleOfStevedoring:false,
            visibleOfChem:false
        }
    },
    methods:{
        handleResult(rqs) {
            if (rqs === undefined || rqs == null) {
                return "无";
            } else {
                return rqs;
            }
        },

        close(){
            this.$emit('close');
        },
        showStevedoringDialog(ipPk){
            this.visibleOfStevedoring = true;
            this.$nextTick(() => {
                this.$refs.stevedoringinfo.getStevedorList(ipPk)
            });
        },
        // 最近10条电子运单
        showChemDialog(ipPk){
            this.visibleOfChem = true;
            this.$nextTick(() => {
                this.$refs.cheminfo.getChemList(ipPk)
            });
        }
    }
}
</script>

<style scoped>
.map-popover{
    width: 400px !important;
}
</style>
