<template>
  <div style="cursor: default;">
    <div class="map-popover">
      <div class="popover-header">
        <b>车辆信息</b>
        <span class="popover-close" title="关闭" @click="close">×</span>
      </div>
      <div class="popover-body" style="padding:5px;">
        <table style="width:330px">
          <tbody>
            <tr v-if="rteplan">
              <th>运单号：</th>
              <td colspan="3">
                <a href="javascript:void(0)" @click="showCdDialog(rteplan.argmtPk)">{{ handleResult(rteplan.cd) }}</a>
                <span v-if="vecState">（{{ vecState }}）</span>
              </td>
            </tr>
            <tr>
              <th width="70">车辆：</th>
              <template v-if="rteplan">
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showVehicleDialog(rteplan.tracPk)">{{
                    handleResult(selectVecInfo.vehicleNo) }}</a>
                  <span v-if="rteplan.traiPk"> / </span>
                  <span v-if="rteplan.traiPk"><a href="javascript:void(0)" @click="showVehicleDialog(rteplan.traiPk)">{{
                    handleResult(rteplan.traiCd) }}</a>
                  </span>
                </td>
              </template>
              <td v-else>{{ handleResult(selectVecInfo.vehicleNo) }}</td>
            </tr>
            <template v-if="rteplan">
              <tr>
                <th width="70">时速：</th>
                <td colspan="3">
                  {{ selectVecInfo.state == 0 ? 0 : selectVecInfo.speed }} 公里/小时
                </td>
              </tr>
              <tr>
                <th>驾驶员：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showPersDialog(rteplan.dvPk)">{{ handleResult(rteplan.dvNm) }}</a>
                  <a href="javascript:void(0)" @click="call(rteplan.dvMob)">{{ handleResult(rteplan.dvMob, '') }}</a>
                </td>
              </tr>
              <tr>
                <th>押运员：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showPersDialog(rteplan.scPk)">{{ handleResult(rteplan.scNm) }}</a>
                  <a href="javascript:void(0)" @click="call(rteplan.scMob)">{{ handleResult(rteplan.scMob, '') }}</a>
                </td>
              </tr>
              <tr>
                <th>货物：</th>
                <td colspan="3">
                  <template v-if="rteplan.rtePlanItemList">
                    <template v-if="rteplan.rtePlanItemList.length > 0">
                      <template v-for="item in rteplan.rtePlanItemList">
                        <a :key="item.enchPk" href="javascript:void(0)" @click="showGoodsDialog(item.prodPk)"
                          :title="handleResult(item.goodsNm) + '（' + handleResult(item.dangGoodsNm) + '）' + '（' + handleResult(item.goodsNw || item.loadQty || '0', '--') + '吨）'">{{
                            handleResult(item.goodsNm) }}<span class="goods-nm-deputy">（{{ handleResult(item.dangGoodsNm)
  }}）</span></a>（{{ handleResult(item.goodsNw
  || item.loadQty || '0', '--') }}吨）
                      </template>
                    </template>
                    <template v-else>
                      <a :key="rteplan.enchPk" href="javascript:void(0)" @click="showGoodsDialog(rteplan.prodPk)"
                        :title="handleResult(rteplan.goodsNm) + '（' + handleResult(rteplan.dangGoodsNm) + '）' + '（' + handleResult(rteplan.goodsNw || rteplan.loadQty || '0', '--') + '吨）'">{{
                          handleResult(rteplan.goodsNm) }}<span class="goods-nm-deputy">（{{
    handleResult(rteplan.dangGoodsNm) }}）</span></a>（{{ handleResult(rteplan.goodsNw
    || rteplan.loadQty || '0', '--') }}吨）
                    </template>
                  </template>
                </td>
              </tr>
              <tr>
                <th>物流公司：</th>
                <td colspan="3">
                  <a href="javascript:void(0)" @click="showEntpDialog(rteplan.carrierPk)"
                    :title="handleResult(rteplan.carrierNm)">{{ handleResult(rteplan.carrierNm) }}</a>
                </td>
              </tr>
              <tr>
                <th>发货地：</th>
                <td colspan="3" :title="rteplan.csnorWhseDist">{{ rteplan.csnorWhseDist }}</td>
              </tr>
              <tr>
                <th>收货地：</th>
                <td colspan="3" :title="rteplan.csneeWhseDist">{{ rteplan.csneeWhseDist }}</td>
              </tr>
              <tr v-if="selectVecInfo.updateTime">
                <th>更新时间：</th>
                <td colspan="3" :title="selectVecInfo.updateTime">{{ selectVecInfo.updateTime }}</td>
              </tr>
            </template>
            <template v-else>
              <tr>
                <th>电子运单：</th>
                <td colspan="3" style="color: red">无电子运单信息</td>
              </tr>
              <tr>
                <th>时速：</th>
                <td colspan="3">{{ selectVecInfo.speed }}公里/小时</td>
              </tr>
              <tr>
                <th>定位时间：</th>
                <td colspan="3">{{ selectVecInfo.updateTime }}(卫星定位)</td>
              </tr>
            </template>
            <tr v-if="selectVecInfo.warningText">
              <td colspan="4" style="white-space: initial;color: red; font-weight: 800;"
                v-html="selectVecInfo.warningText"></td>
            </tr>
            <!-- <tr v-if="selectVecInfo.state === 1 && intoTime">
              <td colspan="4" style="white-space: initial;color: orange; font-weight: 800;" v-html="intoTime"></td>
            </tr> -->
            <!-- <template v-if="alarmInfo">
              <tr>
                <th>报警类型：</th>
                <td colspan="3" :title="alarmInfo.catNmCn" style="color: red">{{alarmInfo.catNmCn}}</td>
              </tr>
              <tr>
                <th>报警地点：</th>
                <td colspan="3" :title="alarmInfo.alarmLocation">{{alarmInfo.alarmLocation}}</td>
              </tr>
              <tr>
                <th>报警时间：</th>
                <td colspan="3" :title="alarmInfo.alarmTime">{{alarmInfo.alarmTime}}</td>
              </tr>
              <tr>
                <th>报警详情：</th>
                <td colspan="3" :title="alarmInfo.descr">{{alarmInfo.descr}}</td>
              </tr>
            </template> -->
          </tbody>
        </table>
      </div>
      <div class="popover-footer">
        <a href="javascript:void(0);" @click="enforceOnLine()">风险管控</a>
        <a href="javascript:void(0);" @click="showHisTrack(selectVecInfo.vehicleNo)">轨迹追踪</a>
        <a v-if="rteplan" href="javascript:void(0);" @click="showGoodsDialog(rteplan.prodPk)">应急救援</a>
        <a v-if="rteplan" href="javascript:void(0);" @click="showArgwtRecord(rteplan.tracCd)">过磅记录</a>
        <!-- <a href="javascript:void(0);" @click="showAlarmDialog(selectVecInfo.vehicleNo)">违章记录</a> -->
        <!-- <a href="javascript:void(0);" @click="showVecEventDialog(selectVecInfo.vehicleNo)">车辆事件</a> -->
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" append-to-body width="80%" top="5vh">
      <simple-table :tableTitle="tableTitle" :tableHeader="tableHeader" :tablePage="tablePage"
        @tableRefreshByPagination="tableRefreshByPaginationHandle"></simple-table>
    </el-dialog>

    <!-- 企业详情 -->
    <el-dialog title="企业详情" :visible.sync="visibleOfEntp" append-to-body width="80%" top="5vh" class="detail-dialog">
      <entp-info ref="entpInfo" :isCompn="true"></entp-info>
    </el-dialog>
    <!-- 人员详情 -->
    <el-dialog title="人员详情" :visible.sync="visibleOfPers" append-to-body width="80%" top="5vh" class="detail-dialog">
      <pers-info ref="persInfo" :isCompn="true"></pers-info>
    </el-dialog>
    <!-- 车辆详情 -->
    <el-dialog title="车辆详情" :visible.sync="visibleOfVec" append-to-body width="80%" top="5vh" class="detail-dialog">
      <vec-info ref="vecInfo" :isCompn="true"></vec-info>
    </el-dialog>
    <!-- 危化品信息详情 -->
    <el-dialog title="危化品信息详情" :visible.sync="visibleOfChemica" append-to-body width="80%" top="5vh"
      class="detail-dialog">
      <chemica-info ref="chemicaInfo" :isCompn="true"></chemica-info>
    </el-dialog>
    <el-dialog title="电子运单详情" :visible.sync="visibleOfRtePlanBills" append-to-body width="80%" top="5vh"
      class="detail-dialog">
      <rtePlan-bills-info ref="RtePlanBillsInfo" :isCompn="true"></rtePlan-bills-info>
    </el-dialog>
    <!--  过磅记录  -->
    <el-dialog :title="'【' + argwtRecordVecNo + '】 过磅记录列表'" :visible.sync="visibleOfArgwtRecord" append-to-body
      width="80%" top="5vh" class="detail-dialog">
      <argwtRecord-dialog ref="argwtRecord"></argwtRecord-dialog>
    </el-dialog>
    <!-- 在途可视 -->
    <!-- <histrack-info ref="histrackInfo" :isCompn="true"></histrack-info> -->
    <!-- 异常停车处置窗口 -->
    <enforce-on-line-dialog ref="enForceOnLineDialog"></enforce-on-line-dialog>
  </div>
</template>

<script>
import SimpleTable from "@/components/SimpleTable";
import * as Tool from "@/utils/tool";
import * as $httpAlarm from "@/api/violationAlarm";

import EntpInfo from "@/views/modules/base/entp/entp-info";
import PersInfo from "@/views/modules/base/pers/pers-info";
import VecInfo from "@/views/modules/base/vec/vec-info";
import ChemicaInfo from "@/views/modules/base/chemica/chemica-info";
import RtePlanBillsInfo from "@/views/modules/base/rtePlan/rtePlan-bills";
import EnforceOnLineDialog from "./enforce-on-line-dialog";
import argwtRecordDialog from "./argwtRecord-dialog";
// import HistrackInfo from '@/views/modules/mapMonit/histrack-info'
import dayjs from 'dayjs';

export default {
  name: "MonitorInfoWindow",
  props: {
    selectVecInfo: {
      type: Object
    },
    rteplan: {
      type: Object
    },
    selectedAlarmPk: {
      type: [Number, String, null],
      default: null
    }
  },
  components: {
    SimpleTable,
    EntpInfo,
    PersInfo,
    VecInfo,
    ChemicaInfo,
    EnforceOnLineDialog,
    RtePlanBillsInfo,
    argwtRecordDialog
  },
  watch: {
    //   "selectVecInfo.vehicleNo": {
    //     handler(newVal, oldVal) {
    //       if (!this.selectedAlarmPk && newVal) {
    //         this.getAlarmInfo(newVal); // 获取最新报警数据
    //       }
    //     },
    //     immediate: true
    //   },
    selectedAlarmPk: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getAlarmInfoPyPk(newVal);
        } else {
          this.alarmInfo = null;
        }
      },
      immediate: true
    },
    // rteplan() {
    //   this.getIntoTime()
    // },
    // reflash() {
    //   this.getIntoTime()
    // },
  },
  data() {
    return {
      dialogVisible: false,

      dialogTableVisible: false, // 弹窗是否可见
      dialogTitle: "", // 弹窗标题
      tableComponent: "simple-table", // table组件
      tableTitle: "", // table的标题
      tableHeader: null, // table的表头信息
      tablePage: null, // table的数据信息，包括list数据以及分页数据

      tableType: null,

      visibleOfEntp: false,
      visibleOfPers: false,
      visibleOfVec: false,
      visibleOfChemica: false,
      visibleOfRtePlanBills: false,
      visibleOfArgwtRecord: false, // 过磅记录弹窗
      argwtRecordVecNo: '',
      // alarmInfo: null,
      // intoTime: '',
      // reflash: 1,
      // timer: null
    };
  },
  computed: {
    vecState() {
      if (!this.rteplan) {
        return ''
      }
      if (this.rteplan.errBackStatus == 211) return '异常结束'
      if (this.rteplan.backTm) {
        return '回场'
      } else if (this.rteplan.unloadTm) {
        return '卸货'
      } else if (this.rteplan.loadTm) {
        return '装车'
      } else if (this.rteplan.goTm) {
        return '发车'
      } else {
        return '无'
      }
    }
  },
  methods: {
    call(phone) {
      //TODO 由某个地方发起调用来获得号码
      var value = {
        callee: phone,
        caller: "057422762615",
        publicKey: "",
        useVirNumCall: true,
        random: Math.random()
      };
      localStorage.setItem("workbenchSdkCall", JSON.stringify(value));
    },
    handleResult(rqs) {
      if (rqs === undefined || rqs == null) {
        return "无";
      } else {
        return rqs;
      }
    },

    close() {
      this.$emit("close");
    },
    //获取最近报警信息
    // getAlarmInfo(vecno) {
    //   let params = {
    //     vecno: vecno
    //   };
    //   $httpAlarm.getAlarmByVecNo(params).then(res => {
    //     if (res.code == 0 && res.data.length > 0) {
    //       this.alarmInfo = res.data[0];
    //     } else {
    //       this.alarmInfo = null;
    //     }
    //   });
    // },
    // 根据报警pk，获取报警信息
    getAlarmInfoPyPk(pk) {
      $httpAlarm.getAlarmByPk(pk).then(res => {
        if (res.code == 0) {
          this.alarmInfo = res.data;
        } else {
          this.alarmInfo = null;
        }
      });
    },

    // 车辆信息弹窗
    showVehicleDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfVec = true;
      this.$nextTick(() => {
        this.$refs.vecInfo.initByPk(pk);
      });
    },

    // 人员信息弹窗
    showPersDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/pers/info/'+pk,'_blank');
      this.visibleOfPers = true;
      this.$nextTick(() => {
        this.$refs.persInfo.initByPk(pk);
      });
    },

    // 货物信息弹窗
    showGoodsDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/chemica/info/'+pk,'_blank');
      this.visibleOfChemica = true;
      this.$nextTick(() => {
        // this.$refs.chemicaInfo.initByEnchPk(pk);
        this.$refs.chemicaInfo.getInfoByProdPk(pk);
      });
    },
    // 过磅记录列表
    showArgwtRecord(tracCd) {
      if (!tracCd) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.argwtRecordVecNo = tracCd
      this.visibleOfArgwtRecord = true
      this.$nextTick(() => {
        this.$refs.argwtRecord.getArgwtRecordList(tracCd);
      });
    },
    // 电子运单弹窗
    showCdDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.visibleOfRtePlanBills = true;
      this.$nextTick(() => {
        this.$refs.RtePlanBillsInfo.rtePlanNewByPk(pk);
      });
    },

    // 企业信息弹窗
    showEntpDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/entp/info/'+pk,'_blank');
      this.visibleOfEntp = true;
      this.$nextTick(() => {
        this.$refs.entpInfo.initByPk(pk);
      });
    },

    // 历史轨迹
    showHisTrack(vehicleNo) {
      let location = window.location;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack?v=" +
        encodeURIComponent(vehicleNo) +
        "&t=" +
        Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },

    getAlarmList(params) {
      let _this = this;
      $httpAlarm.getAlarmListBySimpleQuery(params).then(res => {
        if (res.code == 0) {
          _this.dialogTableVisible = true;
          _this.tablePage = res.data;
        } else {
          _this.tablePage = {
            list: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          _this.$message.error(res.msg);
        }
      });
    },

    // 违章记录弹窗
    showAlarmDialog(vehicleNo) {
      let _this = this;
      this.tableType = "alarm";
      this.dialogTitle = `车辆违章报警和预警详情`;
      this.tableTitle = `<h5>${vehicleNo}，30天内违章报警和预警数据</h5>`;
      this.tableHeader = [
        { name: "违章时间", field: "alarmTime", width: 200 },
        { name: "违章地点", field: "alarmLocation" },
        { name: "违章类型", field: "catNmCn" },
        { name: "违章细节", field: "descr" }
      ];
      let params = {
        page: 1,
        limit: 20,
        days: 30,
        tracNo: vehicleNo,
        notCatCd: "2550.160.180.150"
      };
      this.getAlarmList(params);
    },

    // 异常停车处置
    enforceOnLine() {
      this.$refs.enForceOnLineDialog.init({
        rteplan: this.rteplan,
        selectVecInfo: this.selectVecInfo,
        alarmInfo: this.alarmInfo
      });
    },

    getVecEventList(params) {
      let _this = this;
      $httpAlarm.getAlarmListBySimpleQuery(params).then(res => {
        if (res.code == 0) {
          _this.dialogTableVisible = true;
          _this.tablePage = res.data;
        } else {
          _this.tablePage = {
            list: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          _this.$message.error(res.msg);
        }
      });
    },

    // 车辆事件弹窗
    showVecEventDialog(vehicleNo) {
      let _this = this;
      this.tableType = "vecevent";
      this.dialogTitle = `车辆当天事件详情`;
      this.tableTitle = `<h1>${vehicleNo}</h1><span>车辆当天事件信息</span>`;
      this.tableHeader = [
        { name: "事件时间", field: "alarmTime", width: 200 },
        { name: "事件类型", field: "alarmLocation" },
        { name: "事件地址", field: "catNmCn" },
        { name: "事件细节", field: "descr" }
      ];
      let params = {
        page: 1,
        limit: 20,
        tracNo: vehicleNo
      };
      this.getVecEventList(params);
    },

    // table翻页事件
    tableRefreshByPaginationHandle(paginationData) {
      if (this.tableType == "alarm") {
        let params = {
          page: 1,
          limit: 20,
          days: 30,
          tracNo: this.selectVecInfo.vehicleNo
        };
        this.getAlarmList(Object.assign({}, params, paginationData));
      } else {
        let params = {
          page: 1,
          limit: 20,
          days: 30,
          tracNo: this.selectVecInfo.vehicleNo
        };
        this.getVecEventList(Object.assign({}, params, paginationData));
      }
    },
    showPathDialog(v) {
      this.$refs.pathModalRef.open(v)
    },
    // getDateDiff(date, diffDate = dayjs()) {
    //   const mapArr = [
    //     { sub: 365 * 24 * 60 * 60, t: '年' },
    //     { sub: 24 * 60 * 60, t: '天' },
    //     { sub: 60 * 60, t: '小时' },
    //     { sub: 60, t: '分钟' },
    //     { d: 60, sub: 1, t: '秒' }
    //   ]
    //   const diff = (dayjs(diffDate).valueOf() - dayjs(date).valueOf()) / 1000
    //   if (diff < 60) {
    //     return '0秒'
    //   }
    //   let t = ''
    //   let tempDiff = diff
    //   mapArr.forEach((item) => {
    //     const df = tempDiff / item.sub
    //     if (df > 1) {
    //       const num = Math.floor(df)
    //       t += `${Math.floor(df)}${item.t}`
    //       tempDiff = tempDiff - (item.sub * num)
    //     }
    //   })
    //   return t
    // },
    // reflashData() {
    //   clearTimeout(this.timer)
    //   this.timer = setTimeout(() => {
    //     this.reflash = Math.random()
    //   }, 1000);
    // },
    // getIntoTime() {
    //   this.reflashData()
    //   if (this.selectVecInfo.residenceTimeStart) {
    //     let text = ''
    //     if(this.rteplan.csneeWhseDistCd.includes('330211') && !this.rteplan.csnorWhseDistCd.includes('330211')) {
    //       text = '进入镇海卸货，已滞留'
    //     }
    //     if(!this.rteplan.csneeWhseDistCd.includes('330211') && this.rteplan.csnorWhseDistCd.includes('330211')) {
    //       text = '在镇海装货完成，已滞留'
    //     }
    //     this.intoTime = `${dayjs(this.selectVecInfo.residenceTimeStart).format('MM-DD HH:mm')} ${text}${this.getDateDiff(this.selectVecInfo.residenceTimeStart)}。`
    //   } else {
    //     this.intoTime = ''
    //   }
    // }
  },
  // destroyed() {
  //   clearTimeout(this.timer)
  // }
};
</script>

<style lang="scss" scoped>
.map-popover {
  /* position: absolute; */
  position: relative;
  left: initial;
  left: auto;
  top: 8px;
  z-index: 9;
  /* left: 5px; */
  border: 1px solid #aaa;
  border-radius: 4px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
  background-color: #fff;

  &:before,
  &:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -20px;
    z-index: 2;
    width: 0;
    height: 0;
    margin-left: -5px;
    border-width: 10px 8px;
    border-style: solid;
    border-color: #f9f9f9 transparent transparent #f9f9f9;
  }

  &:after {
    border-width: 11px 9px;
    bottom: -23px;
    z-index: 1;
    margin-left: -6px;
    border-color: rgba(0, 0, 0, 0.2) transparent transparent rgba(0, 0, 0, 0.2);
  }
}

.goods-nm-deputy {
  display: inline-block;
  overflow: hidden;
  max-width: 100px;
  text-overflow: ellipsis;
  vertical-align: bottom;
}
</style>
