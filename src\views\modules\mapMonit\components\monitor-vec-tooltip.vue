<template>
  <div class="vec-info-top-wape clearfix" :style="defaultStyle" ref="MonitorVecTooltip">
    <div class="vec-info-btn" @click="toggleShow">
      <svg-icon icon-class="notice" class-name="tooltip-icon"></svg-icon>
    </div>
    <ul class="vec-info-list clearfix" v-show="isShow">
      <li class="info-item">
        <img :src="vecImgConfig.run_0">
        <span class="info-item-desc">行驶空车</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.run_1">
        <span class="info-item-desc">行驶重车</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.run_2">
        <span class="info-item-desc">行驶未知</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.stop_0">
        <span class="info-item-desc">停止空车</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.stop_1">
        <span class="info-item-desc">停止重车</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.stop_2">
        <span class="info-item-desc">停止未知</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.offline_0">
        <span class="info-item-desc">离线空车</span>
      </li>
      <li class="info-item">
        <img :src="vecImgConfig.offline_1">
        <span class="info-item-desc">离线重车</span>
      </li>
      <!-- <li class="info-item">
                <img :src="vecImgConfig.heavy_1">
                <span class="info-item-desc">中危重车</span>
            </li>
            <li class="info-item">
                <img :src="vecImgConfig.heavy_2">
                <span class="info-item-desc">高危重车</span>
            </li> -->
      <li class="info-item">
        <img :src="vecImgConfig.offline_2">
        <span class="info-item-desc">离线未知</span>
      </li>
    </ul>
  </div>
</template>

<script>
import imgsConfig from 'static/jsonConfig/mapMonitorImagesConfig.json'
export default {
  name: 'MonitorVecTooltip',
  props: {
    // default style
    defaultStyle: {
      type: Object,
      default: function () {
        return {
          position: 'absolute',
          top: '10px',
          left: '250px',
          'z-index': 9
        }
      }
    }
  },
  data() {
    return {
      isShow: false,
      vecImgConfig: imgsConfig.vec
    }
  },
  methods: {
    toggleShow() {
      this.isShow = !this.isShow;
    }
  }
}
</script>

<style scoped>
.vec-info-top-wape {
  color: #5a4505;
  background-color: rgba(249, 212, 115, 0.7);
  box-shadow: 1px 2px 3px 1px #cbcbcb;
  border-radius: 5px;
  padding-left: 45px;
  min-height: 40px;
}

.vec-info-top-wape .vec-info-btn {
  position: absolute;
  cursor: pointer;
  padding: 0;
  margin: 0;
  left: 0;
  top: 50%;
  margin-top: -20px;
}

.vec-info-top-wape .tooltip-icon {
  font-size: 30px;
  width: 45px;
  height: 40px;
}

ul.vec-info-list {
  margin: 0;
  padding: 10px;
  border-left: 2px solid #f3b413;
  -webkit-transition: all 1s ease;
  transition: width 3s ease;
}

.vec-info-list>li.info-item:first-child {
  margin-left: 0;
}

.vec-info-list>li {
  float: left;
  margin: 0 5px;
  text-align: center;
}

.vec-info-list>li.info-item>img {
  width: 20px;
  height: 20px;
}

.vec-info-list>li.info-item .info-item-desc {
  display: block;
  text-align: center;
  font-size: 12px;
  line-height: 16px;
}
</style>
