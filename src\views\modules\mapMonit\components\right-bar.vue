<template>
  <div class="right-bar">
    <el-popover
      placement="right-start"
      width="240"
      trigger="hover"
      >
        <el-button slot="reference" type="primary" icon="el-icon-view" circle></el-button>
        <div class="noselect">
          <div v-for="(item, index) in oneMapList" :key="index" class="eyes-item">
            <span class="on" :style="{'background': 'url('+ (item.isShow ? onImg : offImg)+')'}" @click="checkEyes(item)"></span>
            <span>{{ item.title }}</span>
            <span>( {{ item.num }}个 <img v-if="item.type != 'map'" :src="imgsConfig.parkinglot[item.type]" style="width: 24px;height: 24px;" />)</span>
          </div>
        </div>
    </el-popover>
  </div>
</template>

<script>
import * as $http from "@/api/mapMonit";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";

export default {
  data() {
    return {
      imgsConfig,
      onImg: imgsConfig.rescue.eye_on,
      offImg: imgsConfig.rescue.eye_off,
      oneMapList: [
        { title: '全部', isShow: false, type: 'map', num: 0, value: [] },
        { title: '加油站', isShow: false, type: 'map.01', num: 0, value: [] },
        { title: '维修站', isShow: false, type: 'map.02', num: 0, value: [] },
        { title: '服务站', isShow: false, type: 'map.03', num: 0, value: [] },
        { title: '收费站', isShow: false, type: 'map.04', num: 0, value: [] },
        { title: '高速枢纽', isShow: false, type: 'map.06', num: 0, value: [] },
        { title: '企业停车场', isShow: false, type: 'map.07', num: 0, value: [] },
        { title: '公共停车场', isShow: false, type: 'map.08', num: 0, value: [] },
        { title: '其他', isShow: false, type: 'map.10', num: 0, value: [] },
        { title: '装卸区', isShow: false, type: 'map.99', num: 0, value: [] },
      ],
      oneMapData: [],
      map: null,
      fencesMap: {},
      fencesIconMap: {}
    }
  },
  created() {
    this.getOneMap()
  },
  methods: {
    setMap(map) {
      this.map = map
    },
    async getOneMap() {
      const res = await $http.getOneMap()
      if (res.code === 0) {
        this.oneMapData = res.list
        this.setOneMapList()
        this.drawMap()
      }
    },
    setOneMapList() {
      this.oneMapData.forEach(item => {
        this.oneMapList.forEach(config => {
          if (item.type.includes(config.type)) {
            config.num += 1
            config.value.push(item)
          }
        })
      })
    },
    checkEyes(item) {
      item.isShow = !item.isShow
      if (item.type === 'map') {
        this.oneMapList.forEach(config => config.isShow = item.isShow )
      }
      const temp = this.oneMapList.filter(config => config.type !== 'map')
      if (temp.filter(config => config.isShow).length === temp.length) {
        this.oneMapList[0].isShow = true
      }
      if (temp.filter(config => !config.isShow).length !== 0) {
        this.oneMapList[0].isShow = false
      }
      this.showFence()
    },
    drawMap() {
      this.oneMapList.filter(item => item.type !== 'map').forEach(config => {
        config.value.forEach(d => {
          const id = d.id + d.name
          try {
            const lnglat = JSON.parse(d.lnglat)
            const lnglatT = lnglat.map(item => `${item.lng},${item.lat}`).join(';')
            var ply = new BMap.Polygon(lnglatT, {
              strokeWeight: 2,
              strokeColor: '#409EFF',
            })
            this.map.addOverlay(ply)
            this.fencesMap[id] = ply

            const center = this.getCenterPoint(lnglat)
            const icon = new BMap.Icon(imgsConfig.parkinglot[config.type], new BMap.Size(24, 24), {anchor: new BMap.Size(12, 24)})
            icon.setImageSize(new BMap.Size(24, 24))
            this.drawMarker(id, center[0], center[1], icon, d.name)
          } catch(e) {}
        })
      })
      this.showFence()
    },
    showFence() {
      Object.keys(this.fencesMap).forEach(key => this.fencesMap[key].hide())
      Object.keys(this.fencesIconMap).forEach(key => this.fencesIconMap[key].hide())
      const temp = this.oneMapList.filter(config => config.isShow)

       temp.forEach(item => {
        item.value.forEach(ite => {
          const id = ite.id + ite.name
          this.fencesMap[id].show()
          this.fencesIconMap[id].show()
        })
      })
    },
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return [x, y];
    },
    drawMarker(id, lng, lat, icon, title) {
      const point = new BMap.Point(lng, lat)
      const marker = new BMap.Marker(point, {
        icon,
        title
      });

      this.map.addOverlay(marker); //添加覆盖物
      this.fencesIconMap[id] = marker;
    },
  }
}
</script>

<style lang="scss" scoped>
.right-bar {
  position: absolute;
  right: 10px;
  top: 75px;
}
.on {
  display: inline-block;
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.eyes-item {
  span{
    line-height: 35px;
    vertical-align: middle;
  }
}
  .noselect {

    -webkit-touch-callout: none; /* iOS Safari */

    -webkit-user-select: none; /* Chrome/Safari/Opera */

    -khtml-user-select: none; /* Konqueror */

    -moz-user-select: none; /* Firefox */

    -ms-user-select: none; /* Internet Explorer/Edge */

    user-select: none;
    }
</style>
