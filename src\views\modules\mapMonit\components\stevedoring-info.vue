<template>
    <div>
        <!-- <el-form :inline="true" class="demo-form-inline"> 
            <el-form-item label="区域：">
                <el-select v-model="wtFrom" placeholder="请选择区域"  size="small">
                <el-option label="镇海" value="ZH"></el-option>
                <el-option label="上虞" value="SY"></el-option>
            </el-select>
            </el-form-item>
            <el-form-item>
            <el-button @click="getStevedorList(ipPk)" type="primary" size="small"> 查询</el-button>
            </el-form-item>
        </el-form> -->
         <simple-table :tableHeader="tableHeader" :tablePage="tablePage"></simple-table>       
    </div>
</template>
<script>
import {getStevedor} from '@/api/mapMonit'
import SimpleTable from '@/components/SimpleTable'
export default {
    data(){
        return {
            tableHeader:[
              {name:"电子运单",field:"argmtCd"},
              {name:"承运商",field:"entpNmCn"},
              {name:"牵引车号",field:"cd"},
              {name:"挂车号",field:"traiCd"},
              {name:"驾驶员",field:"dvNm"},
              {name:"押运员",field:"scNm"},
              {name:"装货地",field:"csnorWhseDist"},
              {name:"卸货地",field:"csneeWhseDist"},
              {name:"货物",field:"goodsNm"},
              {name:"地磅称重",field:"loadQty"},
              {name:"过磅时间",field:"wtTm"},
              {name:"充装站",field:"regPot"}
            ],
            tablePage:{
                list:[],
                currPage:0,
                pageSize:20,
                totalPage:0
            },
            ipPk:''
        }
    },
    components:{
        SimpleTable
    },
    methods:{
        //获取装卸记录信息
        getStevedorList(ipPk){
            let data = {ipPk: ipPk}
            getStevedor(data).then( res => {
                if(res.code == 0){
                    this.tablePage.list = res.page.list;
                };
            });
        }
    }
}
</script>
