<template>
  <div
    class="app-main-content"
    style="position: relative; margin: 0; padding: 0"
  >
    <div
      class="map"
      ref="mapNode"
      :style="{ height: modelHeight + 'px' }"
    ></div>
    <div style="position: absolute; left: 10px; top: 10px; z-index: 999">
      <monitor-alarm-list-new
        v-if="typeOfTheShowVec === 'heavy'"
        ref="monitorAlarmList"
        :vecListPage="allData"
        @alarmList="alarmListHandle"
        @vecSite="vecHeavySite"
        :defaultStyle="{
          left: '10px'
        }"
      ></monitor-alarm-list-new>
      <monitor-searchbar-simple
        ref="monitorSearchbar"
        title="单车监控"
        :vecType="typeNmOfTheShowVec"
        :vecListPage="vecListPage"
        @searchVecNo="searchVecNoHandle"
        @selectVec="selectVecHandle"
        :defaultStyle="{
          left: '10px'
        }"
      ></monitor-searchbar-simple>

      <!-- 2021-08-27 沈莹说先去掉 <monitor-alarm-pre-list ref="monitorAlarmPreList" :vecListPage="alarmPreListPage" @alarmList="alarmPreListHandle" @vecSite="vecPreSite"></monitor-alarm-pre-list> -->
    </div>
    <monitor-vec-tooltip></monitor-vec-tooltip>
    <div class="refresh-tooltip" v-show="refreshNextTime > 0">
      {{ refreshNextTime }}秒后刷新
    </div>

    <!-- <div class="policecar-trace" v-if="isDLJC" @click="tracePoliceCar">
        稽查车辆定位
    </div> -->

    <div class="vec-total-wape">
      <el-tooltip
        placement="top"
        effect="light"
        v-for="gpsType in gpsTypes"
        :key="gpsType.nmEn"
        v-if="allCarObj[gpsType.nmEn]"
      >
        <div class="tipContent" slot="content" v-if="gpsType.nmEn === 'heavy'">
          <el-button
            class="inner-btn"
            size="small"
            v-for="item in heavyList"
            :key="item.title"
            :class="{
              active: typeOfTheShowVec === 'heavy' && heavyType === item.key,
              sp: item.sp
            }"
            @click="selectHeavy(item.key)"
          >
            {{ item.title }}{{ item.key === "all" ? heavyNum : item.num }}辆
          </el-button>
        </div>
        <div class="tipContent" slot="content" v-else>
          行驶 {{ allCarObj[gpsType.nmEn].online.length }} 辆<br />停止
          {{ allCarObj[gpsType.nmEn].stop.length }}辆<br />离线
          {{ allCarObj[gpsType.nmEn].offline.length }}辆
        </div>
        <el-button
          class="vec-total-btn"
          :class="[typeOfTheShowVec === gpsType.nmEn ? 'active' : '']"
          @click="displayVecMarkerByType(gpsType.nmEn)"
        >
          {{ gpsType.nmCn
          }}{{
            allCarObj[gpsType.nmEn].online.length +
              allCarObj[gpsType.nmEn].stop.length +
              allCarObj[gpsType.nmEn].offline.length
          }}辆
          <el-tooltip
            effect="light"
            placement="top-start"
            v-if="gpsTypesTips[gpsType.nmEn]"
          >
            <div
              class="tipContent"
              slot="content"
              style="width: 400px"
              v-html="gpsTypesTips[gpsType.nmEn]"
            ></div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-button>
      </el-tooltip>
    </div>
    <div v-show="false">
      <monitor-info-window
        ref="monitorInfoWindow"
        :selectVecInfo="selectVecInfo"
        :rteplan="rteplan"
        :selectedAlarmPk="selectedAlarmPk"
        @close="closeInfoWindow"
      ></monitor-info-window>
      <monitor-cp-info-window
        ref="monitorCpInfoWindow"
        :selectEntpInfo="selectEntpInfo"
        @close="closeInfoWindow"
      ></monitor-cp-info-window>
    </div>
    <map-menu @mapDeploy="mapDeploy"></map-menu>
    <audio :src="AudioAlarm" hidden ref="audio"></audio>

    <!-- <div style="position: absolute;
    width: 330px;
    height: 600px;
    right: 10px;
    top: 60px;">
      <aliyunccc ref="aliyunccc" ></aliyunccc>
    </div> -->
    <RightBar ref="rightBarRef" />
  </div>
</template>

<script>
import "@/styles/mapMonit.css";

import BMap from "BMap";
import BMapLib from "BMapLib";
import MonitorSearchbar from "./components/monitor-searchbar";
import MonitorSearchbarSimple from "./components/monitor-searchbar-simple";
import MonitorAlarmListNew from "./components/monitor-alarm-list-new";
import MonitorAlarmPreList from "./components/monitor-alarm-pre-list";
import MonitorVecTooltip from "./components/monitor-vec-tooltip";
import MonitorInfoWindow from "./components/monitor-info-window";
import MonitorCpInfoWindow from "./components/monitor-cp-info-window";
import MapMenu from "./components/map-menu";
import RightBar from "./components/right-bar";
import AudioAlarm from "static/alarm.mp3";
// import websocket from "@/mixins/websocket";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/mapMonit";
import * as $httpVec from "@/api/rtePlan";
import * as $httpAlarm from "@/api/violationAlarm";
import * as $httpAlarmPre from "@/api/violationAlarmPre";
import HashMap from "@/utils/hashmap";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";

import Aliyunccc from "@/components/aliyunccc";

import dayjs from "dayjs";
import { getToken } from "@/utils/auth";

/**
 *  goodsType, 1:易制毒，2：易制爆，3：重点车
 *  carType, 0:空车，1：重车，2：过境车
 *
 */
export default {
  name: "MapMonit",
  // mixins: [websocket],
  data() {
    return {
      gpsTypes: [], // 底部展示的车辆类型
      gpsTypesTips: {}, // 底部车辆类型的提示信息
      allCarObj: {},
      AudioAlarm: AudioAlarm,
      modelHeight: Tool.getClientHeight() - 50,
      vecListPage: {
        result: [],
        pageNo: 0,
        pageSize: 10,
        totalPage: 0
      },
      alarmPreListPage: {
        list: [],
        currPage: 1,
        pageSize: 3,
        totalPage: 1,
        count: 0,
        alarmText: ""
      },
      alarmListPage: {
        list: [],
        currPage: 1,
        pageSize: 3,
        totalPage: 1,
        count: 0,
        alarmText: ""
      },
      map: null,
      mapNodeLoading: null,

      entpPolygonMap: new HashMap(), //装卸企业 polygon
      entpIconMarkerMap: new HashMap(), //装卸企业icon
      carsMarkerMap: new HashMap(), // 所有车辆数据
      policeCarMap: new HashMap(),

      vecCarMap: new HashMap(),

      infoBox: null,
      infoBoxCache: null,
      _infoBox: null,
      _infoBoxCache: null,
      typeOfTheShowVec: "all", // 显示的当前车辆类型，默认显示重车
      selectVecInfo: { vehicleNo: "" }, // InfoBox:已选车辆信息
      selectEntpInfo: {}, // _InfoBox:已选企业信息
      selectedAlarmPk: "", // 选中的报警信息，点击左侧报警列表显示报警信息
      rteplan: null, // InfoBox：已选车辆电子路单信息

      refreshFlag: true, // 计时结束后是否请求的标志
      refreshTask: null, // 定时任务
      refreshNextTime: 0, // 地图刷新时间剩余时间（用于倒计时）
      refreshIntervalTime: 30, // 每次刷新间隔的时间 30秒

      websock: null, //建立的连接
      lockReconnect: false, //是否真正建立连接
      timeout: 28 * 1000, //30秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      iswebsocketclose: false,

      complexLabelMarkerMap: new HashMap(),
      isDLJC: false,

      heavyType: "all",
      heavyList: [
        { title: "高危重车", key: 12, num: 0, value: [], sp: true },
        { title: "一级报警", key: 91, num: 0, value: [], sp: true },
        { title: "二级报警", key: 92, num: 0, value: [], sp: true },
        { title: "镇海装货", key: 95, num: 0, value: [], sp: true },
        { title: "镇海卸货", key: 96, num: 0, value: [], sp: true },
        { title: "镇海到镇海", key: 97, num: 0, value: [], sp: true },
        { title: "途经镇海", key: 98, num: 0, value: [], sp: true }, // 沈莹说要加这个分类 2022-02-15
        { title: "全部", key: "all", num: 0, value: [] },
        { title: "高速", key: 3, num: 0, value: [] },
        { title: "地面道路", key: 1, num: 0, value: [] },
        { title: "化工企业内部", key: 2, num: 0, value: [] },
        { title: "企业停车场", key: 4, num: 0, value: [] },
        { title: "公共停车场", key: 5, num: 0, value: [] },
        { title: "加油站", key: 6, num: 0, value: [] },
        { title: "维修区", key: 7, num: 0, value: [] },
        { title: "服务区", key: 8, num: 0, value: [] },
        { title: "收费站", key: 9, num: 0, value: [] },
        { title: "装卸区", key: 10, num: 0, value: [] },
        { title: "其他", key: 11, num: 0, value: [] }
        // { title: '中危重车', key: 11, num: 0, value: [] },
      ],
      allData: [],
      loadingTimer: null
    };
  },
  components: {
    MonitorSearchbar,
    MonitorAlarmListNew,
    MonitorAlarmPreList,
    MonitorVecTooltip,
    MonitorInfoWindow,
    MonitorCpInfoWindow,
    MapMenu,
    Aliyunccc,
    MonitorSearchbarSimple,
    RightBar
  },
  computed: {
    // 显示的车辆类型名称
    typeNmOfTheShowVec: function() {
      let type = this.typeOfTheShowVec;
      let res = this.gpsTypes.filter(item => {
        return item.nmEn === type;
      });
      return res.length ? res[0].nmCn : "所有";
    },
    heavyNum() {
      const d = this.allCarObj["heavy"];
      return d.online.length + d.stop.length + d.offline.length;
    }
  },
  created() {
    //页面刚进入时开启长连接
    this.initWebSocket(); //获取报警车辆
    this.getGpsType(); //获取车辆类型
    this.getControlDetail(); //获取危化品信息
    this.startRefreshTask();
  },
  mounted: function() {
    window.addEventListener("resize", this.setModelHeight);

    this.$nextTick(() => {
      this.initMap(); // init map info
      this.getBoundary(); // load fence
      // this.addMainLoadPolyline();                  // load main load

      this.initSearchBar(); // init searchBar

      this.addRegistryMarker(); // 登记点信息
      this.refreshOnlineVec(this.typeOfTheShowVec, null, true); // default, show the full vehicle
      this.alarmList(); //获取报警车辆

      // 沈莹要求先去掉
      // this.alarmPreList(); //获取预警车辆
      this.showPoliceCar(); // 获取稽查大队车辆
    });
  },
  destroyed() {
    this.stopRefreshTask();
    window.removeEventListener("resize", this.setModelHeight);
    this.closeInfoWindow();
  },
  methods: {
    //获取车辆类型
    getGpsType() {
      let _this = this;
      $http.getGpsType().then(res => {
        if (res.code === 0) {
          _this.gpsTypes = res.data;
          let checkedType = null;
          _this.gpsTypes.forEach(item => {
            if (item.isDefault) {
              checkedType = item.nmEn;
            }
          });
          _this.typeOfTheShowVec = checkedType || "all";
        }
      });
    },
    // 获取车辆类型的信息提示
    getControlDetail() {
      let _this = this;
      $http.getControlDetail().then(res => {
        if (res && res.code === 0) {
          const d = res.data;
          Object.keys(d).forEach(key => {
            d[key] = d[key].join(", ");
          });
          this.$set(_this, "gpsTypesTips", d);
        }
      });
    },
    /* tracePoliceCar(){
            this.map.setViewport([this.policeCar.marker.getPosition()])
        }, */
    setModelHeight() {
      this.modelHeight = Tool.getClientHeight() - 50;
    },
    // loading effect
    getMapNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: "el-icon-loading"
      });
      return loading;
    },
    getTransLoading() {
      //无背景色
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: "el-icon-loading",
        background: "transparent"
      });
      return loading;
    },

    // map init
    initMap() {
      this.map = new BMap.Map(this.$refs.mapNode, { enableMapClick: false });
      let point = new BMap.Point(121.66386, 30.001186);
      this.map.centerAndZoom(point, 13);
      this.map.enableScrollWheelZoom();
      this.map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP]
        })
      );
      this.map.addEventListener("click", function(e) {
        console.log(e.point.lng + ", " + e.point.lat);
      });
      this.map.setMapType(BMAP_SATELLITE_MAP);
      this.$refs.rightBarRef.setMap(this.map);
    },

    //获取行政区域
    getBoundary() {
      var bdary = new BMap.Boundary();
      var map = this.map;

      bdary.get("宁波市镇海区", function(rs) {
        //获取行政区域
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域"
          });
          return;
        }
        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 3,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
        }
      });
    },

    createComplexLabelMarker(point, text) {
      let complexLabelMarker = null;
      /* if(complexLabelMarker){
                complexLabelMarker.setPosition(point,text);
            }else{ */
      // 复杂的自定义覆盖物
      function ComplexCustomOverlay(point, text) {
        this._point = point;
        this._text = text;
        this._closebtn = null;
      }

      ComplexCustomOverlay.prototype = new BMap.Overlay();

      ComplexCustomOverlay.prototype.setPosition = function(point, text) {
        this.hide();
        this._point = point;
        if (text) {
          this._text = text;
        }
        this.draw();
        this.show();
      };

      ComplexCustomOverlay.prototype.initialize = function(map) {
        this._map = map;
        var div = (this._div = document.createElement("div"));
        div.style.position = "absolute";
        div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
        div.style.backgroundColor = "#EE5D5B";
        div.style.border = "1px solid #BC3B3A";
        div.style.color = "white";
        div.style.height = "23px";
        div.style.padding = "2px";
        div.style.lineHeight = "18px";
        div.style.whiteSpace = "nowrap";
        div.style.MozUserSelect = "none";
        div.style.fontSize = "12px";
        var span = (this._span = document.createElement("span"));
        var btn = (this._closebtn = document.createElement("span"));
        btn.style.display = "none";
        btn.style.marginLeft = "10px";
        btn.style.fontSize = "14px";
        btn.style.cursor = "pointer";

        btn.innerHTML = "×";
        div.appendChild(span);
        div.appendChild(btn);
        span.appendChild(document.createTextNode(this._text));
        var that = this;

        var arrow = (this._arrow = document.createElement("div"));

        arrow.style.position = "absolute";
        arrow.style.width = "10px";
        arrow.style.height = "10px";
        arrow.style.border = "4px solid transparent";
        arrow.style.borderTopColor = "#EE5D5B";
        arrow.style.top = "22px";
        arrow.style.left = "24px";
        arrow.style.overflow = "hidden";
        div.appendChild(arrow);

        div.onmouseover = function() {
          that._closebtn.style.display = "inline-block";
        };

        div.onmouseout = function() {
          that._closebtn.style.display = "none";
        };

        btn.onclick = function() {
          div.style.display = "none";
          this.style.display = "none";
        };

        this._map.getPanes().labelPane.appendChild(div);

        return div;
      };

      ComplexCustomOverlay.prototype.draw = function() {
        var map = this._map;
        var pixel = map.pointToOverlayPixel(this._point);
        this._div.style.left =
          pixel.x - parseInt(this._arrow.style.left) + "px";
        this._div.style.top = pixel.y - 40 + "px";
      };

      complexLabelMarker = new ComplexCustomOverlay(point, text);
      this.map.addOverlay(complexLabelMarker);
      // }

      return complexLabelMarker;
    },

    // search vec number list for the left bar component
    // type, 0:空车，1：重车，2：过境车
    searchVecNoList(type, vecNo, pageNo, pageSize) {
      if (!vecNo) {
        return;
      }
      let param = {
        queryParam: vecNo || "", // vecNo
        pageNo: pageNo || 1,
        pageSize: pageSize || 10
      };
      if (!type || type === "all") {
      } else if (type == "empty" || type == "heavy") {
        param.type = type === "empty" ? 0 : 1;
      } else {
        param.goodsType = type;
      }
      $http.getGpsVecListByPage(param).then(res => {
        if (res.code == 0) {
          this.vecListPage = res.page;
        } else {
          this.vecListPage = {
            result: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          this.$message.error(res.msg);
        }
        this.$refs.monitorSearchbar.$data.loading = false;
      });
    },

    // init monitor searchbar on the left
    initSearchBar() {
      this.searchVecNoList(this.typeOfTheShowVec);
    },

    // function for left bar component
    searchVecNoHandle(vecNo, pageNo, pageSize) {
      this.searchVecNoList(this.typeOfTheShowVec, vecNo, pageNo, pageSize);
    },

    // select vec monitor line
    selectVecHandle(data) {
      let vecNo = data.vehicleNo || data._id;
      let marker = this.carsMarkerMap.get(vecNo);

      if (marker) {
        this.map.centerAndZoom(marker.getPosition(), 16);
        // marker.V.click();
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      }
      // else if (this.policeCar.key == vecNo) {
      //   this.map.setViewport([this.policeCar.marker.getPosition()]);
      // }
      else {
        this.$message.error("该车不在区内！");
      }
    },

    // show the registry pointer
    addRegistryMarker() {
      // var data = eval('[{"name":"登记点1","point":{"x":121.707595,"y":29.981604}},{"name":"登记点2","point":{"x":121.705529,"y":29.981541}},{"id":27,"name":"登记点5","point":{"x":121.613282,"y":30.047397}},{"name":"登记点6","point":{"x":121.613763,"y":30.047546}},{"name":"登记点4","point":{"x":121.645607,"y":29.968683}},{"name":"登记点3","point":{"x":121.661912,"y":29.991661}}]');
      // var regIcon = null;
      // for (var i = 0; i < data.length; i++) {
      //     var marker = null;
      //     var pt = new BMap.Point(data[i].point.x, data[i].point.y);
      //     if (data[i].name == "登记点1") {
      //         regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     if (data[i].name == "登记点2") {
      //        regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     if (data[i].name == "登记点3") {
      //         regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     if (data[i].name == "登记点4") {
      //         regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     if (data[i].name == "登记点5") {
      //         regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     if (data[i].name == "登记点6") {
      //         regIcon = new BMap.Icon(img_register1, new BMap.Size(48,48), {});
      //     }
      //     marker = new BMap.Marker(pt, {
      //         icon: regIcon
      //     });
      //     this.map.addOverlay(marker);
      //     marker.disableMassClear();
      // }
    },

    // 开启定时任务
    startRefreshTask() {
      let _this = this;
      this.refreshNextTime = this.refreshIntervalTime;
      this.refreshTask = setInterval(function() {
        _this.refreshNextTime -= 1;
        if (_this.refreshNextTime == 0) {
          if (_this.refreshFlag) {
            _this.refreshFlag = false;
            _this.refreshOnlineVec(
              _this.typeOfTheShowVec,
              _this.areaType,
              false
            );
            // 获取稽查大队车辆
            _this.showPoliceCar();
          }
          _this.resetRefreshTask(); // 重置定时任务
        }
      }, 1000);
    },

    // 重置定时任务
    resetRefreshTask() {
      this.refreshNextTime = this.refreshIntervalTime;
      window.clearInterval(this.refreshTask);
      this.startRefreshTask();
    },

    // 停止定时任务
    stopRefreshTask() {
      window.clearInterval(this.refreshTask);
      // window.clearTimeout(this.refreshTask);
    },
    // 获取稽查大队车辆
    showPoliceCar() {
      $http
        .policeCar()
        .then(res => {
          if (res.code == 0) {
            let data = res.data;
            if (data && data.length) {
              data.forEach(item => {
                let vecNo = item._id.replace(/警$/, "J");
                let marker = this.policeCarMap.get(vecNo);

                this.isDLJC = true;
                if (marker) {
                  let point = new BMap.Point(item.lonBd, item.latBd);
                  marker.setPosition(point);
                  marker.setRotation(item.direction);
                  let complexLabelMarker = this.complexLabelMarkerMap.get(
                    vecNo
                  );
                  complexLabelMarker.setPosition(point, item._id);
                } else {
                  let point = new BMap.Point(item.lonBd, item.latBd);
                  let icon = new BMap.Icon(
                    imgsConfig.vec["police_car"],
                    new BMap.Size(46, 46)
                  );
                  marker = new BMap.Marker(point, { icon: icon });
                  marker.setRotation(item.direction);
                  marker.setZIndex(new Date().getTime());

                  this.map.addOverlay(marker);

                  this.policeCarMap.put(vecNo, marker);

                  // this.$set(this.policeCar,"key",item._id.replace(/警$/,"J"));
                  // this.$set(this.policeCar,"marker",marker);
                  this.complexLabelMarkerMap.put(
                    vecNo,
                    this.createComplexLabelMarker(
                      marker.getPosition(),
                      item._id
                    )
                  );
                }
              });
            }
          }
        })
        .catch(err => {});
    },

    // according to the vehicle's type,  refresh online vehicle on the map
    refreshOnlineVec(gspType, areaType, flag) {
      let _this = this;
      this.areaType = areaType;
      if (flag) {
        this.mapNodeLoading = this.getTransLoading();
      }
      let API = areaType
        ? $http.getLocalCarList(areaType)
        : $http.getAllVecListWithGPS();
      API.then(async res => {
        // console.log(res)
        if (res.code == 0) {
          this.allData = res.data;
          if (flag) {
            this.mapNodeLoading.close();
          }
          // this.mapNodeLoading.close();
          let allCarObj = {};
          _this.gpsTypes.forEach(item => {
            allCarObj[item.nmEn] = {
              online: [],
              offline: [],
              stop: []
            };
          });
          if (!allCarObj.all) {
            allCarObj.all = {
              online: [],
              offline: [],
              stop: []
            };
          }

          this.heavyList.forEach(item => {
            item.num = 0;
            item.value = [];
          });

          // clear vec marker in the map

          // _this.carsMarkerMap.clear(); // 清空车辆数据
          // _this.carsMarkerMap = new HashMap();    // 清空车辆数据

          // _this.map.clearOverlays();
          // this.mapNodeLoading = this.getMapNodeLoading();
          // res.data.forEach((item, index) => {
          for (let i = 0; i < res.data.length; i++) {
            const item = res.data[i];
            const index = i;
            // carType：0：空车，1：重车，2：未知
            if (item.carType == 4) {
              item.carType = 0;
            }

            let runStatus = item.speed > 0 ? "run" : "stop";
            let vehicleNo = item.vehicleNo || item._id; // 车牌编号
            let updateTime = new Date().getTime() - item.updateTimeStamp;
            if (updateTime > 10 * 1000 * 60) {
              // 离线
              runStatus = "offline";
            }

            if (updateTime > 10 * 1000 * 60) {
              // 离线
              allCarObj.all.offline.push(vehicleNo);
            } else {
              if (item.speed > 0) {
                //在线
                allCarObj.all.online.push(vehicleNo);
              } else {
                //停止
                allCarObj.all.stop.push(vehicleNo);
              }
            }

            // carType, 0:空车，1：重车，2：过境车
            let checkCarTypeObj = null;
            if (item.carType == 0) {
              checkCarTypeObj = allCarObj.empty;
            } else if (item.carType == 1) {
              checkCarTypeObj = allCarObj.heavy;
            }
            // else if (item.carType == 2) {
            //   _this.passbyCars.push(vehicleNo);
            // }
            if (checkCarTypeObj) {
              if (updateTime > 10 * 1000 * 60) {
                // 离线
                checkCarTypeObj.offline.push(vehicleNo);
              } else {
                if (item.speed > 0) {
                  //在线
                  checkCarTypeObj.online.push(vehicleNo);
                } else {
                  //停止
                  checkCarTypeObj.stop.push(vehicleNo);
                }
              }
            }

            // 车辆类型：易制毒、易制爆、易燃气体、易燃液体…………
            // 根据后端需要展示的类型进行展示
            let carGoodsTypeArr =
              item.goodsDetail && item.goodsDetail.length > 0
                ? item.goodsDetail.split(",")
                : [];
            if (carGoodsTypeArr.length > 0) {
              carGoodsTypeArr.forEach(it => {
                let arrTemp = allCarObj[it];
                if (arrTemp) {
                  if (updateTime > 10 * 1000 * 60) {
                    // 离线
                    arrTemp.offline.push(vehicleNo);
                  } else {
                    if (item.speed > 0) {
                      //在线
                      arrTemp.online.push(vehicleNo);
                    } else {
                      //停止
                      arrTemp.stop.push(vehicleNo);
                    }
                  }
                }
              });
            }

            this.diffVecLoad(item, runStatus, vehicleNo, allCarObj, index);
            this.setHeavyList(item);
            // }); // end of the foreach
          }

          // this.mapNodeLoading.close();
          _this.$set(_this, "allCarObj", allCarObj);
          _this.displayVecMarkerByType(
            _this.typeOfTheShowVec,
            this.heavyType,
            res.data.length + 10
          );
          _this.refreshFlag = true;
          // window.clearInterval(_this.refreshTask);
          // _this.startRefreshTask();
        } else {
          this.$message.error("数据库链接异常，请刷新页面或者联系管理员！");
        }
        this.mapNodeLoading.close();
      }).catch(err => {
        this.mapNodeLoading.close();
      });
    },

    // carType, 0:空车，1：重车，2：过境车
    displayVecMarkerByType(gspType, childtype, t = 0) {
      let _this = this;
      let isShowInfoBox = gspType === this.typeOfTheShowVec;
      this.typeOfTheShowVec = gspType;
      // hide all vehicle marker
      this.carsMarkerMap.values().forEach(item => {
        item.hide();
      });
      let all = [];
      if (childtype && childtype != "all") {
        all = this.allCarObj[`${gspType}_${childtype}`] || [];
      } else {
        this.heavyType = "all";
        all = this.allCarObj[gspType];
        if (all) {
          all = new Set([...all.offline, ...all.online, ...all.stop]);
        }
      }
      all.forEach(vecNo => {
        let marker = _this.carsMarkerMap.get(vecNo);
        if (marker) {
          marker.show();
        }
      });
      setTimeout(() => {
        if (isShowInfoBox) {
          // 刷新后打开之前的infoBox 地图弹窗信息
          if (_this.infoBoxCache) {
            let vehicleNo = _this.selectVecInfo.vehicleNo;
            if (vehicleNo) {
              let markerTemp = _this.carsMarkerMap.get(vehicleNo);
              if (markerTemp && markerTemp.isVisible()) {
                // _this.infoBoxCache.open(markerTemp);
                const d = _this.allData.filter(ite => ite._id === vehicleNo);
                if (d.length) {
                  this.openInfoWindow(d[0], markerTemp);
                }
              } else {
                _this.closeInfoWindow();
                console.log("对不起，该车不存在围栏内，可能已经出了围栏");
              }
              markerTemp = null;
            }
          }
        } else if (gspType !== "all" && this.infoBoxCache) {
          this.closeInfoWindow();
        }
      }, t);
    },

    // 获取车辆地址
    getAddressOfVec(lng, lat) {
      console.log(`%c getAddressOfVec: ${lng}, ${lat}`, "color:blue");
      // $.get(window.actionPath + "/gps/getAddress?lat=" + lat + "&lng=" + lng + "&mapType=baiduMap", function (ret) {
      //     if (ret.code == 0) {
      //         $("#mapdreess").text(ret.msg);
      //     }
      // });
    },

    openInfoWindow(item, marker) {
      this.mapNodeLoading.close();
      let _this = this,
        vehicleNo = item.vehicleNo || item._id;
      this.map.panTo(marker.getPosition());
      this.mapNodeLoading = this.getMapNodeLoading();

      // 获取该车上一次电子路单记录
      $httpVec
        .getLastRtePlanByTracCd(vehicleNo)
        .then(res => {
          _this.mapNodeLoading.close();
          if (res.code == 0) {
            let rteplan = res.data;
            _this.$set(_this, "selectVecInfo", item);
            _this.$set(_this.selectVecInfo, "vehicleNo", vehicleNo);
            _this.$set(_this, "rteplan", rteplan);
            let infoBox = new BMapLib.InfoBox(
              _this.map,
              _this.$refs.monitorInfoWindow.$el,
              {
                boxStyle: {
                  width: "340px",
                  marginBottom: rteplan ? "28px" : "28px",
                  marginLeft: "4px"
                },
                closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
                //,enableAutoPan: true
                //,align: INFOBOX_AT_TOP
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
              _this.selectedAlarmPk = null;
            });
            _this.infoBoxCache.open(marker);
            _this.infoBoxCache.show();
            infoBox = null;
          }
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },

    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoBoxCache) {
        this.infoBoxCache.close();
        this.infoBoxCache = null;
      }
      if (this._infoBoxCache) {
        this._infoBoxCache.close();
        this._infoBoxCache = null;
      }
    },
    //地图配置
    mapDeploy(type) {
      // 装卸企业 marker
      this.entpIconMarkerMap.keySet().forEach(item => {
        let marker = this.entpIconMarkerMap.get(item);
        if (marker) {
          marker.removeEventListener("click");
          this.map.removeOverlay(marker);
          this.entpIconMarkerMap.remove(item);
        }
      });

      // 装卸企业 覆盖物
      this.entpPolygonMap.keySet().forEach(item => {
        let polygon = this.entpPolygonMap.get(item);
        if (polygon) {
          this.map.removeOverlay(polygon);
          this.entpPolygonMap.remove(item);
        }
      });

      if (type == "entp") {
        this.loadAreaEntp(); //装卸企业
      } else {
        this.refreshOnlineVec(this.typeOfTheShowVec, type, false); //  区内外的车
      }
    },
    //获取装卸企业
    loadAreaEntp() {
      let areaCode = 330211; //镇海区
      let data = { areaCode: areaCode };
      let _this = this;
      this.mapNodeLoading = this.getMapNodeLoading();

      $http
        .findAreaByEntp(data)
        .then(res => {
          if (res.code == 0) {
            _this.map.clearOverlays();
            let points = res.data;
            let marker = null;
            let lng = null;
            let lat = null;
            let pointsView = [];
            let icon = new BMap.Icon(
              imgsConfig.vec.entp,
              new BMap.Size(24, 24)
            );
            for (let i = 0, len = points.length; i < len; i++) {
              let pointsArr = [];
              if (!points[i].lnglat) {
                continue;
              }
              let lnglat = points[i].lnglat.split(";");
              for (let j = 0; j < lnglat.length; j++) {
                lng = Number(lnglat[j].split(",")[0]);
                lat = Number(lnglat[j].split(",")[1]);
                pointsArr.push(new BMap.Point(lng, lat));
              }
              // let pts = new BMap.Point( lng, lat);
              let pts = _this.getCenterPoint(pointsArr);
              // pointsView.push(new BMap.Point( lng, lat))
              pointsView.push(pts);
              marker = new BMap.Marker(pts, { icon: icon });
              _this.map.addOverlay(marker);

              var polygon = new BMap.Polygon(pointsArr, {
                strokeColor: "blue",
                strokeWeight: 2,
                strokeOpacity: 0.3
              }); //创建多边形
              _this.map.addOverlay(polygon); //增加多边形
              marker.addEventListener("click", function() {
                _this.openCpInfoWindow(points[i], this, areaCode);
              });

              _this.entpIconMarkerMap.put(points[i].id, marker);
              _this.entpPolygonMap.put(points[i].id, polygon);
            }
            _this.map.setViewport(pointsView); //将所有的点放置在最佳视野内
            window.clearTimeout(_this.refreshTask);
            _this.refreshNextTime = 0;
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    //百度地图多边形计算中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;
      return new BMap.Point(x, y);
    },
    //装卸企业详情
    openCpInfoWindow(item, marker, areaCode) {
      let _this = this,
        entpPk = item.entpPk;
      this.map.centerAndZoom(
        new BMap.Point(
          marker.getPosition().lng,
          marker.getPosition().lat + 0.0008
        ),
        18
      );
      this.mapNodeLoading = this.getMapNodeLoading();
      // 获取装卸企业详情
      $http
        .getEntpByCpPk(entpPk)
        .then(res => {
          if (res.code == 0) {
            _this.selectEntpInfo = res.data;
            _this.selectEntpInfo.wtFrom = areaCode;
            var _infoBox = new BMapLib.InfoBox(
              _this.map,
              _this.$refs.monitorCpInfoWindow.$el,
              {
                boxStyle: {
                  width: "340px",
                  marginBottom: "100px",
                  marginRight: "15px"
                },
                closeIconMargin: "-274px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn
              }
            );
            if (_this._infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this._infoBoxCache = _infoBox;
            _this._infoBoxCache.addEventListener("close", function() {
              _this._infoBoxCache = null;
            });
            _infoBox.open(marker);
            _infoBox.show();
            _infoBox = null;
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    //报警列表
    alarmList(pageNo, pageSize, alarmText) {
      let param = {
        page: pageNo || 1,
        limit: pageSize || 3,
        filters: {}
      };
      $httpAlarm.getRealtimeAlarmList(param).then(res => {
        if (res.code == 0) {
          this.alarmListPage = {
            list: res.page.list,
            totalPage: res.page.totalPage,
            currPage: pageNo || 1,
            pageSize: pageSize || 3,
            count: res.page.totalCount,
            alarmText: alarmText
          };
        } else {
          this.alarmListPage = {
            list: [],
            currPage: pageNo || 1,
            pageSize: pageSize || 3,
            totalPage: 1,
            count: 0,
            alarmText: alarmText
          };
          this.$message.error(res.msg);
        }
        let loading = this.$refs.monitorAlarmList.$data.loading;
        if (loading) {
          loading.close();
        }
      });
    },
    //预警列表
    alarmPreList(pageNo, pageSize, alarmText) {
      let param = {
        page: pageNo || 1,
        limit: pageSize || 3,
        filters: {}
      };
      $httpAlarmPre.getRealtimeAlarmList(param).then(res => {
        let _this = this;
        if (res.code == 0) {
          this.alarmPreListPage = {
            list: res.page.list,
            totalPage: res.page.totalPage,
            currPage: pageNo || 1,
            pageSize: pageSize || 3,
            count: res.page.totalCount,
            alarmText: alarmText
          };
        } else {
          this.alarmPreListPage = {
            list: [],
            currPage: pageNo || 1,
            pageSize: pageSize || 3,
            totalPage: 1,
            count: 0,
            alarmText: alarmText
          };
          this.$message.error(res.msg);
        }
        let loading = _this.$refs.monitorAlarmPreList.$data.loading;
        if (loading) {
          loading.close();
        }
      });
    },
    alarmListHandle(pageNo, pageSize) {
      this.alarmList(pageNo, pageSize);
    },
    alarmPreListHandle(pageNo, pageSize) {
      this.alarmPreList(pageNo, pageSize);
    },
    vecSite(vec, alarmPk) {
      this.closeInfoWindow();
      this.selectedAlarmPk = alarmPk;
      let marker = this.carsMarkerMap.get(vec);
      if (marker) {
        this.displayVecMarkerByType("all");
        // this.map.centerAndZoom(marker.getPosition(), 16);
        // marker.V.click();
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      } else {
        this.$message.error("该车不在区内！");
      }
    },
    vecHeavySite(vec, alarmPk) {
      this.closeInfoWindow();
      this.selectedAlarmPk = alarmPk;
      let marker = this.carsMarkerMap.get(vec);
      if (marker) {
        this.displayVecMarkerByType("heavy");
        // this.map.centerAndZoom(marker.getPosition(), 16);
        // marker.V.click();
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      } else {
        this.$message.error("该车不在区内！");
      }
    },
    vecPreSite(vec, alarmPk) {
      this.closeInfoWindow();
      this.selectedAlarmPk = alarmPk;
      let marker = this.carsMarkerMap.get(vec);
      if (marker) {
        this.displayVecMarkerByType("all");
        this.map.centerAndZoom(marker.getPosition(), 16);
        // marker.V.click();
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      } else {
        this.$message.error("该车不在区内！");
      }
    },
    audio() {
      let audio = this.$refs.audio;
      if (audio) {
        audio.currentTime = 0; //从头开始播放提示音
        audio.play(); //播放
      }
    },
    selectHeavy(key) {
      this.typeOfTheShowVec = "heavy";
      this.heavyType = key;
      if (key === "all") {
        this.displayVecMarkerByType("heavy");
      } else {
        this.displayVecMarkerByType("heavy", key);
      }
    },
    setHeavyList(d) {
      if (d.carType !== 1) {
        return;
      }
      this.heavyList.forEach(item => {
        if (item.key === d.roadType || item.key === d.roadType2) {
          item.num++;
          item.value.push(d);
        }
      });
    },
    removeVec(vehicleNo) {
      let markerTemp = this.carsMarkerMap.get(vehicleNo);
      if (markerTemp) {
        markerTemp.removeEventListener("click");
        this.map.removeOverlay(markerTemp);
        this.carsMarkerMap.remove(vehicleNo);
      }
    },
    async diffVecLoad(item, runStatus, vehicleNo, allCarObj, index) {
      const _this = this;
      this.removeVec(vehicleNo);

      let point = new BMap.Point(item.lonBd, item.latBd);
      let iconImg = imgsConfig.vec[`${runStatus}_${item.carType}`];
      if (item.carType == 1) {
        const tagMap = {
          "10": 2,
          "5": 1
        };
        iconImg = imgsConfig.vec[`heavy_${tagMap[item.warningGrade]}`];
        item.roadType2 = (tagMap[item.warningGrade] || 0) + 10;
        if (allCarObj[`heavy_${item.roadType}`]) {
          allCarObj[`heavy_${item.roadType}`].push(vehicleNo);
        } else {
          allCarObj[`heavy_${item.roadType}`] = [vehicleNo];
        }
        if (allCarObj[`heavy_${item.roadType2}`]) {
          allCarObj[`heavy_${item.roadType2}`].push(vehicleNo);
        } else {
          allCarObj[`heavy_${item.roadType2}`] = [vehicleNo];
        }
        const workTypeMap = {
          5: 96,
          10: 95,
          15: 97,
          20: 98
        };
        if (item.workType) {
          const firstworkType = item.workType;
          let workTypeKey = `heavy_${workTypeMap[firstworkType]}`;
          const heavyList = this.heavyList.find(
            ite => ite.key === workTypeMap[firstworkType]
          );
          heavyList.value.push(item);
          heavyList.num++;
          if (allCarObj[workTypeKey]) {
            allCarObj[workTypeKey].push(vehicleNo);
          } else {
            allCarObj[workTypeKey] = [vehicleNo];
          }
        }
      }
      let icon = new BMap.Icon(iconImg, new BMap.Size(24, 24));
      let marker = new BMap.Marker(point, { icon: icon });
      marker.setRotation(item.direction);
      this.carsMarkerMap.put(vehicleNo, marker);

      const warnings = (item.warningType || "").split(",");
      let t = 0;
      if (warnings.includes("5")) {
        t = dayjs().valueOf() - dayjs(item.updateTime).valueOf();
      }
      if (warnings.includes("10")) {
        const tempT = dayjs().valueOf() - dayjs(item.stopTime).valueOf();
        t = tempT > t ? tempT : t;
      }
      let markerState = 0;
      if (t > 0) {
        let heavyType;
        const mins = t / 1000 / 60;
        if (mins > 45 && mins < 120) {
          markerState = false;
          heavyType = 91;
        } else if (mins >= 120) {
          markerState = true;
          heavyType = 92;
        }
        if (item.carType == 1) {
          const heavyList = this.heavyList.find(ite => ite.key === heavyType);
          heavyList.value.push(item);
          heavyList.num++;
          if (allCarObj[`heavy_${heavyType}`]) {
            allCarObj[`heavy_${heavyType}`].push(vehicleNo);
          } else {
            allCarObj[`heavy_${heavyType}`] = [vehicleNo];
          }
        }
      }

      await this.sleep(index);

      this.map.addOverlay(marker);

      if (markerState !== 0) {
        this.createDot(marker, markerState);
      }

      marker.addEventListener("click", function() {
        _this.getAddressOfVec(item.lonBd, item.latBd);
        _this.openInfoWindow(item, this);
      });
    },
    async sleep(t) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(true);
        }, t);
      });
    },
    createDot(marker, f = false) {
      let divElement = document.createElement("div");
      divElement.className = "before";
      let divElement2 = document.createElement("div");
      divElement2.className = "after";
      marker.Dc.innerHtml = marker.Dc.childNodes[0];
      marker.Dc.appendChild(divElement);
      marker.Dc.appendChild(divElement2);
      marker.Dc.className = "";
      marker.Dc.className = f ? "dot dotf" : "dot";
      marker.Dc.style.overflow = "";
      marker.Dc.firstChild.style.position = "relative";
      marker.Dc.firstChild.style.zIndex = "5";
    }
    // websockset实时报警
    // websocketCallback(data) {
    //   if (data.catNm === "alarm") {
    //     let vecData = data.msg;
    //     let txt = vecData.tractorNo + vecData.catNmCn;
    //     this.playAudio();
    //     this.$message({
    //       message: `${txt}`,
    //       type: "warning"
    //     });
    //     // TODO 刷新报警
    //     this.alarmList(1, 3, txt); //刷新报警车辆
    //   }
    // },
    // // 播放警报
    // playAudio() {
    //   let audio = this.$refs.audio;
    //   if (audio) {
    //     audio.currentTime = 0; //从头开始播放提示音
    //     audio.play(); //播放
    //   }
    // }
  }
};
</script>

<style lang="scss" scoped>
.vec-total-wape {
  position: absolute;
  bottom: 10px;
  z-index: 1;
  margin: 0 auto;
  padding: 0px;
  text-align: center;
  width: 100%;
  text-align: center;
  overflow: hidden;
  padding-bottom: 5px;
}
.vec-total-btn {
  margin: 0;
  box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
  line-height: 30px;
  padding: 2px 10px;
  font-size: 14px;
  border: 1px solid #449d44;
  cursor: pointer;
  font-weight: bold;
  color: #449d44;
  border-radius: 3px;

  &.active {
    background-color: #449d44;
    border: 1px solid #449d44;
    color: #fff;
  }
}
/deep/ .vec-total-btn {
  & + .vec-total-btn {
    margin-left: 5px;
  }
}
.refresh-tooltip,
.policecar-trace {
  position: absolute;
  z-index: 10;
  right: 10px;
  top: 40px;
  box-shadow: rgba(0, 0, 0, 0.35) 2px 2px 3px;
  border-left: 1px solid rgb(139, 164, 220);
  border-top: 1px solid rgb(139, 164, 220);
  border-bottom: 1px solid rgb(139, 164, 220);
  background-color: rgba(28, 91, 250, 0.7);
  padding: 3px 6px;
  font: bold 12px/1.3em arial, sans-serif;
  text-align: center;
  white-space: nowrap;
  border-radius: 3px 0px 0px 3px;
  color: #fff;
}
.policecar-trace {
  top: 70px;
  cursor: pointer;
}
.tipContent {
  color: #333;
  line-height: 20px;
}

.inner-btn {
  display: block;
  border-radius: 0;
  margin: 0;
  width: 100%;
  border-color: #449d44;
  color: #449d44;
  font-weight: 800;
  &:nth-child(1) {
    border-radius: 5px 5px 0 0;
  }
  &:nth-last-child(1) {
    border-radius: 0 0 5px 5px;
  }
  &.active {
    background: #449d44;
    color: #fff;
  }
}
</style>

<style lang="scss">
.dot {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 50;
}
.dot:hover {
  z-index: 100;
}

/* 内环  */
.dot > .before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  border: 2px solid orange;
  border-radius: 100%;
  background-color: orange;
  z-index: 2;
  animation: color3 2s ease-out;
  animation-iteration-count: infinite;
}
/* 产生动画（向外扩散变大）的圆圈  */
.dot > .after {
  content: "";
  position: absolute;
  width: 40px;
  height: 40px;
  left: -10px;
  top: -10px;
  border: 3px solid orange;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  z-index: 1;
  opacity: 0;
  animation: color4 2s ease-out;
  animation-iteration-count: infinite;
}

.dotf > .before {
  animation: color1 2s ease-out;
  animation-iteration-count: infinite;
  border: 2px solid #ee0000;
  background-color: #ee0000;
}
/* 产生动画（向外扩散变大）的圆圈  */
.dotf > .after {
  animation: color2 2s ease-out;
  animation-iteration-count: infinite;
  border: 3px solid #ee0000;
}
// 动画效果
@keyframes color1 {
  0% {
    transform: scale(0.1);
    opacity: 0.5;
    border-color: #ee0000;
  }
  50% {
    transform: scale(1);
    opacity: 0.5;
    border-color: #ee0000;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
@keyframes color2 {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  49% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.5;
    border-color: #ee0000;
  }
  80% {
    transform: scale(1);
    opacity: 0.5;
    border-color: #ee0000;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes color3 {
  0% {
    transform: scale(0.1);
    opacity: 0.5;
    border-color: orange;
  }
  50% {
    transform: scale(1);
    opacity: 0.5;
    border-color: orange;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
@keyframes color4 {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  49% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(0.5);
    opacity: 0.5;
    border-color: orange;
  }
  80% {
    transform: scale(1);
    opacity: 0.5;
    border-color: orange;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.dot > div {
  z-index: 3;
  top: 0;
  left: 0;
}

.sp {
  background: #ddd;
}
</style>
