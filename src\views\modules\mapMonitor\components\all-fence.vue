<template>
  <div class="fence-wrapper" :style="defaultStyle">
    <el-popover placement="right-start" width="240" trigger="hover">
      <div slot="reference" class="fence-btn">围栏信息</div>
      <div>
        <el-autocomplete size="mini" popper-class="white-autocomplete" v-model="searchFenceNm" :fetch-suggestions="querySearch" placeholder="请输入查询的围栏名称" @select="searchFenceSelect"
          style="width:100%;">
          <template slot-scope="{ item }">
            <div class="name">{{ item.name }}</div>
            <span class="desc">
              <template>
                <span>{{typeDict[item.type]}}</span>
              </template>
            </span>
          </template>
        </el-autocomplete>
      </div>
      <div class="noselect">
        <div v-for="(item, index) in oneMapList" :key="index" class="eyes-item">
          <span class="on" :style="{'background': 'url('+ (item.isShow ? onImg : offImg)+')'}" @click="checkEyes(item)"></span>
          <span>{{ item.title }}</span>
          <span>( {{ item.num }}个 <img v-if="item.type != 'map'" :src="imgsConfig.parkinglot[item.type]" style="width: 24px;height: 24px;" />)</span>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import * as $http from "@/api/mapMonit";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";

export default {
  props: {
    // map: {
    //   type: Object
    // },
    defaultStyle: {
      type: Object,
      default() {
        return {
          position: "absolute",
          right: "10px",
          top: "75px"
        };
      }
    }
  },
  data() {
    return {
      map:null,
      imgsConfig,
      onImg: imgsConfig.rescue.eye_on,
      offImg: imgsConfig.rescue.eye_off,
      oneMapList: [
        { title: "全部", isShow: false, type: "map", num: 0, value: [] },
        { title: "加油站", isShow: false, type: "map.01", num: 0, value: [] },
        { title: "维修站", isShow: false, type: "map.02", num: 0, value: [] },
        { title: "服务站", isShow: false, type: "map.03", num: 0, value: [] },
        { title: "收费站", isShow: false, type: "map.04", num: 0, value: [] },
        { title: "高速枢纽", isShow: false, type: "map.06", num: 0, value: [] },
        {
          title: "企业停车场",
          isShow: false,
          type: "map.07",
          num: 0,
          value: []
        },
        {
          title: "公共停车场",
          isShow: false,
          type: "map.08",
          num: 0,
          value: []
        },
        { title: "其他", isShow: false, type: "map.10", num: 0, value: [] },
        { title: "装卸区", isShow: false, type: "map.99", num: 0, value: [] }
      ],
      typeDict: {},
      oneMapData: [],
      fencesMap: {},
      fencesIconMap: {},

      searchFenceNm: "",
      infoWin: null,
      aloneShowMarkers:[]
    };
  },
  created() {
    this.getOneMap();
    let res = {};
    this.oneMapList.forEach(it => {
      res[it.type] = it.title;
    });
    this.typeDict = res;
  },
  methods: {
    setMap(map){
      this.map = map
    },
    async getOneMap() {
      const res = await $http.getOneMap();
      if (res.code === 0) {
        this.oneMapData = res.list;
        this.setOneMapList();
        this.drawMap();
      }
    },
    setOneMapList() {
      this.oneMapData.forEach(item => {
        this.oneMapList.forEach(config => {
          if (item.type.includes(config.type)) {
            config.num += 1;
            config.value.push(item);
          }
        });
      });
    },
    checkEyes(item) {
      item.isShow = !item.isShow;
      if (item.type === "map") {
        this.oneMapList.forEach(config => (config.isShow = item.isShow));
      }
      const temp = this.oneMapList.filter(config => config.type !== "map");
      if (temp.filter(config => config.isShow).length === temp.length) {
        this.oneMapList[0].isShow = true;
      }
      if (temp.filter(config => !config.isShow).length !== 0) {
        this.oneMapList[0].isShow = false;
      }
      this.showFence();
    },
    drawMap() {
      this.oneMapList
        .filter(item => item.type !== "map")
        .forEach(config => {
          config.value.forEach(d => {
            const id = d.id + d.name;
            try {
              const lnglat = JSON.parse(d.lnglat);
              const lnglatT = lnglat
                .map(item => `${item.lng},${item.lat}`)
                .join(";");
              var ply = new BMap.Polygon(lnglatT, {
                strokeWeight: 2,
                strokeColor: "#409EFF"
              });
              this.map.addOverlay(ply);
              this.fencesMap[id] = ply;

              const center = this.getCenterPoint(lnglat);
              const icon = new BMap.Icon(
                imgsConfig.parkinglot[config.type],
                new BMap.Size(24, 24),
                { anchor: new BMap.Size(12, 24) }
              );
              icon.setImageSize(new BMap.Size(24, 24));
              this.drawMarker(id, center[0], center[1], icon, d.name);
            } catch (e) {}
          });
        });
      this.showFence();
    },
    showFence() {
      Object.keys(this.fencesMap).forEach(key => this.fencesMap[key].hide());
      Object.keys(this.fencesIconMap).forEach(key =>
        this.fencesIconMap[key].hide()
      );
      const temp = this.oneMapList.filter(config => config.isShow);

      temp.forEach(item => {
        item.value.forEach(ite => {
          const id = ite.id + ite.name;
          this.fencesMap[id] && this.fencesMap[id].show();
          this.fencesIconMap[id] && this.fencesIconMap[id].show();
        });
      });
    },
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return [x, y];
    },
    drawMarker(id, lng, lat, icon, title) {
      const point = new BMap.Point(lng, lat);
      const marker = new BMap.Marker(point, {
        icon,
        title
      });
      this.map.addOverlay(marker); //添加覆盖物
      this.fencesIconMap[id] = marker;
    },
    querySearch(queryString, cb) {
      var fenceAll = this.oneMapData;
      var results = queryString
        ? fenceAll.filter(fence => {
            return (
              fence.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0
            );
          })
        : fenceAll;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    searchFenceSelect(polygonData) {
      let _this = this;
      // 关闭所有之前单独显示的图标及围栏
      this.aloneShowMarkers.forEach(it=>{
        it&&it.hide();
      });
      this.aloneShowMarkers.length = 0;
      // let map = this.map;
      if (polygonData.lnglat) {
        let points = [];
        // this.simulateCheck(polygonData.type)
        points = JSON.parse(polygonData.lnglat);
        if (Array.isArray(points)) {
          // let pointRes = points.map(item => {
          //   return {
          //     longitude: item.lng,
          //     latitude: item.lat,
          //     lonBd: item.lng,
          //     latBd: item.lat
          //   };
          // });
          let lnglat = _this.getCenterPoint(points);
          _this.createInfoWin(
            lnglat[0],
            lnglat[1],
            _this.createInfo(polygonData.name)
          );
        } else {
          _this.createInfoWin(
            points.lng,
            points.lat,
            _this.createInfo(polygonData.name)
          );
        }
        const id = polygonData.id + polygonData.name;
        let selectedFenceItem = this.fencesMap[id];
        if(selectedFenceItem){
          selectedFenceItem.show();
          this.aloneShowMarkers.push(selectedFenceItem);
        }
        let selectedIconItem = this.fencesIconMap[id];
        if(selectedIconItem){
          selectedIconItem.show();
          this.aloneShowMarkers.push(selectedIconItem);
        }
      }
    },
    //创建详情框
    createInfoWin(lng, lat, _dom) {
      let map = this.map;
      if (!map) {
        return;
      }
      this.closeInfoWindow();
      let infoWin = (this.infoWin = new BMap.InfoWindow(_dom));
      this.map.openInfoWindow(infoWin, new BMap.Point(lng, lat));
      this.map.panTo(new BMap.Point(lng, lat));
    },
    //围栏详情
    createInfo(name) {
      let _dom = document.createElement("div");
      // _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;
      let _html = `
        <div>${name || ""}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    closeInfoWindow() {
      if (this.infoWin) {
        this.infoWin.close();
        this.infoWin = null;
      }
    }
  },
  destroyed() {
    this.closeInfoWindow();
  }
};
</script>

<style lang="scss" scoped>
.fence-wrapper {
  position: absolute;
  right: 10px;
  // top: 75px;

  .fence-btn {
    width: 80px;
    border-radius: 5px;
    background-color: rgba(28, 91, 250, 0.7);
    margin: 0 0 5px 20px;
    cursor: pointer;
    text-align: center;
    color: #f8f8f9;
    -webkit-box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25);
    vertical-align: middle;
    border: 0;
    padding: 3px 6px;
    font: bold 12px/1.3em arial, sans-serif;
  }
}
.on {
  display: inline-block;
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.eyes-item {
  span {
    line-height: 35px;
    vertical-align: middle;
  }
}
.noselect {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Chrome/Safari/Opera */
  -khtml-user-select: none; /* Konqueror */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}
</style>
<style lang="scss">
.white-autocomplete {
  &.el-autocomplete-suggestion {
    background-color: #fff;
    border-color: #fff;
    li {
      line-height: normal;
      color: #606266;
      padding: 5px;
      &:hover {
        background-color: #f5f7fa;
        color: #606266;
      }

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .desc {
        font-size: 12px;
        color: #b4b4b4;
      }
    }
  }
  .el-autocomplete-suggestion__wrap {
    background-color: #fff;
    border-color: #fff;
  }
  &.el-popper[x-placement^="top"] {
    .popper__arrow {
      bottom: 1px;
      margin-left: -6px;
      border-top-color: #ccc;
      border-bottom-width: 0;
      &::after {
        border-top-color: #ccc;
      }
    }
  }
  &.el-popper[x-placement^="bottom"] {
    .popper__arrow {
      background-color: transparent;
      border-bottom-color: #ccc;
      &::after {
        border-bottom-color: #ccc;
      }
    }
  }
}
</style>
