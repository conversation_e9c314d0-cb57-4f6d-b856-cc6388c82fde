<template>
    <div class="conceal">
		<h2 class="title">chrome浏览器如何打开提示音？</h2>
		<p>在chrome的地址栏中输入：chrome://flags/#autoplay-policy，回车打开设置页面，在该设置改页面会看到黄色方框标记的字体：Autoplay policy ，该选项默认设置为Default，改为：No user gesture is required ，修改选项后页面下方会弹出个提示框，点击提示框上的“RELAUNCH NOW”即可。
		</p>
        <img :src="chrome1" />
        <img :src="chrome2" />
	</div>
</template>
<script>
import chrome1 from 'static/img/chrome/chrome1.jpg';
import chrome2 from 'static/img/chrome/chrome2.jpg';
export default {
    name: 'chromeAudio',
    data() {
        return {
            chrome1,
            chrome2
        }
    }
}
</script>
<style scoped>
.conceal{
    width:800px;
    margin: 0 auto;
    padding:30px 0;
    font-size:14px;
    line-height:28px;
    margin-bottom:20px;
}
.title{
    font-size: 20px;
    text-align: center;
    margin-bottom: 20px;
}
.conceal img{
    width:100%;
}
</style>

