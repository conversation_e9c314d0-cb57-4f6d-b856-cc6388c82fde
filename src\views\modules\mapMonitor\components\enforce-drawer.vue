<template>
  <el-drawer
    title="风险管控"
    :visible.sync="visible"
    direction="rtl"
    :modal="false"
    size="50%"
    :wrapperClosable="false"
    :show-close="false"
    @closed="closedDrawer"
    custom-class="my-drawer"
    class="my-drawer-wrapper"
  >
    <div class="my-drawer-body">
      <div class="my-drawer-body-content">
        <div class="title">风险信息</div>
        <div class="content" style="line-height:22px;font-size:12px;">
          <el-row v-if="rteplan">
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">运单编号：</div>
              <div class="detail-desc">
                <a
                  href="javascript:void(0)"
                  @click="showCdDialog(rteplan.argmtPk)"
                  >{{ handleResult(rteplan.cd) }}</a
                >
                <span v-if="vecState">（{{ vecState }}）</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">运输公司：</div>
              <div class="detail-desc">
                <a
                  href="javascript:void(0)"
                  @click="showEntpDialog(rteplan.carrierPk)"
                  >{{ handleResult(rteplan.carrierNm) }}</a
                >
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">车牌号码：</div>
              <div class="detail-desc">
                <a
                  href="javascript:void(0)"
                  @click="showVehicleDialog(rteplan.tracPk)"
                  >{{ handleResult(rteplan.tracCd) }}</a
                >
                <span v-if="rteplan.traiPk"> / </span>
                <span v-if="rteplan.traiPk"
                  ><a
                    href="javascript:void(0)"
                    @click="showVehicleDialog(rteplan.traiPk)"
                    >{{ handleResult(rteplan.traiCd) }}</a
                  >
                </span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">当前速度：</div>
              <div class="detail-desc">
                {{ vecInfo.speed || "" }}km/小时<span v-show="vecInfo.gpsTime">
                  ，{{ vecInfo.gpsTime }}（卫星定位）</span
                >
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">驾驶人员：</div>
              <div class="detail-desc">
                <a
                  href="javascript:void(0)"
                  @click="showPersDialog(rteplan.dvPk)"
                  >{{ handleResult(rteplan.dvNm) }}</a
                >
                <a href="javascript:void(0)" @click="call(rteplan.dvMob)">{{
                  handleResult(rteplan.dvMob, "")
                }}</a>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">押运人员：</div>
              <div class="detail-desc">
                <a
                  href="javascript:void(0)"
                  @click="showPersDialog(rteplan.scPk)"
                  >{{ handleResult(rteplan.scNm) }}</a
                >
                <a href="javascript:void(0)" @click="call(rteplan.scMob)">{{
                  handleResult(rteplan.scMob, "")
                }}</a>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <div class="detail-label">货物信息：</div>
              <div class="detail-desc">
                <template
                  v-if="
                    rteplan.rtePlanItemList &&
                      rteplan.rtePlanItemList.length > 0
                  "
                >
                  <template v-for="item in rteplan.rtePlanItemList">
                    <a
                      :key="item.enchPk"
                      href="javascript:void(0)"
                      @click="showGoodsDialog(item.prodPk)"
                      :title="
                        handleResult(item.goodsNm) +
                          '（' +
                          handleResult(item.dangGoodsNm) +
                          '）' +
                          '（' +
                          handleResult(
                            item.goodsNw || item.loadQty || '0',
                            '--'
                          ) +
                          '吨）'
                      "
                      >{{ handleResult(item.goodsNm)
                      }}<span class="goods-nm-deputy"
                        >（{{ handleResult(item.dangGoodsNm) }}）</span
                      ></a
                    >（{{
                      handleResult(item.goodsNw || item.loadQty || "0", "--")
                    }}吨）
                  </template>
                </template>
                <template v-else>
                  <a
                    :key="rteplan.enchPk"
                    href="javascript:void(0)"
                    @click="showGoodsDialog(rteplan.prodPk)"
                    :title="
                      handleResult(rteplan.goodsNm) +
                        '（' +
                        handleResult(rteplan.dangGoodsNm) +
                        '）' +
                        '（' +
                        handleResult(
                          rteplan.goodsNw || rteplan.loadQty || '0',
                          '--'
                        ) +
                        '吨）'
                    "
                    >{{ handleResult(rteplan.goodsNm)
                    }}<span class="goods-nm-deputy"
                      >（{{ handleResult(rteplan.dangGoodsNm) }}）</span
                    ></a
                  >（{{
                    handleResult(
                      rteplan.goodsNw || rteplan.loadQty || "0",
                      "--"
                    )
                  }}吨）
                </template>
              </div>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <span class="detail-label">发货地址：</span>
              <span class="detail-desc" :title="rteplan.csnorWhseDist">{{
                rteplan.csnorWhseDist
              }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <span class="detail-label">卸货地址：</span>
              <span class="detail-desc" :title="rteplan.csneeWhseDist">{{
                rteplan.csneeWhseDist
              }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">空重车状态：</span>
              <span class="detail-desc" :title="riskInfo.carType == 0?'空车':'重车'">{{ riskInfo.carType == 0?'空车':'重车' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">是否在路上：</span>
              <span class="detail-desc" :title="riskInfo.inRoad?'在路上':'不在路上'">{{ riskInfo.inRoad?'在路上':'不在路上' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">停车状态：</span>
              <span class="detail-desc" :title="riskInfo.stopType == 0?'停车':'行驶'">{{ riskInfo.stopType == 0?'停车':'行驶' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">停车时长：</span>
              <span class="detail-desc" :title="riskInfo.stopSeconds">{{ formatTime(riskInfo.stopSeconds) }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">滞留状态：</span>
              <span class="detail-desc">
                <span v-if="riskInfo.remainType == 0">无</span>
                <span v-if="riskInfo.remainType == 1">装货滞留</span>
                <span v-if="riskInfo.remainType == 2">卸货滞留</span>
              </span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">滞留时长：</span>
              <span class="detail-desc" :title="riskInfo.remainSeconds">{{ formatTime(riskInfo.remainSeconds) }}</span>
            </el-col>
            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">当前地址：</div>
              <div class="detail-desc">{{ location }}</div>
            </el-col> -->
          </el-row>
          <el-row v-else>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">车牌号码：</div>
              <div class="detail-desc">{{ vecInfo.vecNo }}</div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">电子运单：</div>
              <div class="detail-desc font-red">无电子运单信息</div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">车辆速度：</div>
              <div class="detail-desc">{{ vecInfo.speed }}公里/小时</div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <div class="detail-label">定位时间：</div>
              <div class="detail-desc">{{ vecInfo.gpsTime }}（卫星定位）</div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">空重车状态：</span>
              <span class="detail-desc" :title="riskInfo.carType == 0?'空车':'重车'">{{ riskInfo.carType == 0?'空车':'重车' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">是否在路上：</span>
              <span class="detail-desc" :title="riskInfo.inRoad?'在路上':'不在路上'">{{ riskInfo.inRoad?'在路上':'不在路上' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">停车状态：</span>
              <span class="detail-desc" :title="riskInfo.stopType == 0?'停车':'行驶'">{{ riskInfo.stopType == 0?'停车':'行驶' }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">停车时长：</span>
              <span class="detail-desc" :title="riskInfo.stopSeconds">{{ formatTime(riskInfo.stopSeconds) }}</span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">滞留状态：</span>
              <span class="detail-desc">
                <span v-if="riskInfo.remainType == 0">无</span>
                <span v-if="riskInfo.remainType == 1">装货滞留</span>
                <span v-if="riskInfo.remainType == 2">卸货滞留</span>
              </span>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="12">
              <span class="detail-label">滞留时长：</span>
              <span class="detail-desc" :title="riskInfo.remainSeconds">{{ formatTime(riskInfo.remainSeconds) }}</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              v-if="vecInfo.warningText"
            >
              <!-- <div class="detail-label">车辆状态：</div> -->
              <div class="detail-label">风险信息：</div>
              <div
                class="detail-desc font-red"
                style="display:inline-block;white-space: initial;font-weight: 800;"
              >
                {{ vecInfo.warningTime }} 发现如下风险：
                <div v-html="vecInfo.warningText"></div>
              </div>
            </el-col>
            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" v-if="vecInfo.state === 1 && intoTime">
              <div class="detail-label">风险信息：</div>
              <div class="detail-desc font-red" style="display:inline-block;white-space: initial;font-weight: 800;" v-html="vecInfo.intoTime"></div>
            </el-col> -->
          </el-row>
        </div>
        <div class="title">处置记录</div>
        <div class="content">
          <simple-table
            :tableHeader="recordHeader"
            :tablePage="todayRecordPage"
            @tableRefreshByPagination="getTodayRecordList"
            size="mini"
          ></simple-table>
        </div>
      </div>
      <!-- <div class="my-drawer-body-footer" v-if="formData.id"> -->
      <div class="my-drawer-body-footer">
        <div class="title forcus">
          <span>风险管控——{{ vecInfo.vecNo }}</span>
          <span class="align-right ft-rt" style="margin-top:-3px;">
            <el-button type="default" size="mini" @click="visible = false"
              >关闭</el-button
            >
            <el-button type="primary" size="mini" @click="save">提交</el-button>
          </span>
        </div>
        <div class="content">
          <el-form
            size="mini"
            label-width="110px"
            :model="formData"
            ref="enforceForm"
          >
            <el-row>
              <!-- <el-col :xs="24" :sm="24" :md="24" :lg="12">
                <el-form-item label="处置时间：" prop="foundTm" :rules="$rulesFilter({required:true})">
                  <el-date-picker v-model="formData.foundTm" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col> -->
              <el-col :xs="24" :sm="24" :md="24" :lg="12">
                <el-form-item
                  label="操作员："
                  prop="person"
                  :rules="$rulesFilter({ required: true })"
                >
                  <el-autocomplete
                    class="inline-input"
                    v-model="formData.person"
                    :fetch-suggestions="querySearch"
                    placeholder="请输入内容"
                    clearable
                    @select="handleSelect"
                  ></el-autocomplete>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="24" :md="24" :lg="12">
                <el-form-item
                  label="处置操作："
                  prop="type"
                  :rules="$rulesFilter({ required: true })"
                >
                  <el-radio-group
                    v-model="formData.type"
                    class="custom-radio-group"
                  >
                    <el-radio
                      v-for="item in radioDealOptions"
                      :key="item.value"
                      :label="item.value"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="12">
                <el-form-item label="下次报警时间：" prop="nextAlarmTime">
                  <el-date-picker
                    v-model="formData.nextAlarmTime"
                    type="datetime"
                    placeholder="选择下次报警时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item
                  label="详情描述："
                  prop="opContent"
                  :rules="$rulesFilter({ required: true })"
                >
                  <el-input
                    clearable
                    v-model="formData.opContent"
                    type="textarea"
                  ></el-input>
                </el-form-item>
              </el-col>
              <!-- <div class="align-right">
                <el-button type="default" size="small" @click="visible = false">取消</el-button>
                <el-button type="primary" size="small" @click="save">提交</el-button>
              </div> -->
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
    <!-- 企业详情 -->
    <el-dialog
      title="企业详情"
      :visible.sync="visibleOfEntp"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <entp-info ref="entpInfo" :isCompn="true"></entp-info>
    </el-dialog>
    <!-- 人员详情 -->
    <el-dialog
      title="人员详情"
      :visible.sync="visibleOfPers"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <pers-info ref="persInfo" :isCompn="true"></pers-info>
    </el-dialog>
    <!-- 车辆详情 -->
    <el-dialog
      title="车辆详情"
      :visible.sync="visibleOfVec"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <vec-info ref="vecInfo" :isCompn="true"></vec-info>
    </el-dialog>
    <!-- 危化品信息详情 -->
    <el-dialog
      title="危化品信息详情"
      :visible.sync="visibleOfChemica"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <chemica-info ref="chemicaInfo" :isCompn="true"></chemica-info>
    </el-dialog>
    <el-dialog
      title="电子运单详情"
      :visible.sync="visibleOfRtePlanBills"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <rtePlan-bills-info
        ref="RtePlanBillsInfo"
        :isCompn="true"
      ></rtePlan-bills-info>
    </el-dialog>
  </el-drawer>
</template>
<script>
import * as Tool from "@/utils/tool";
import { oprlist } from "@/api/mapMonit";
// import { latestTime } from "@/api/common";
import * as $httpMonit from "@/api/mapMonitor";
import * as $httpRteplan from "@/api/rtePlan";

import SimpleTable from "@/components/SimpleTable";
import EntpInfo from "@/views/modules/base/entp/entp-info";
import PersInfo from "@/views/modules/base/pers/pers-info";
import VecInfo from "@/views/modules/base/vec/vec-info";
import ChemicaInfo from "@/views/modules/base/chemica/chemica-info";
import RtePlanBillsInfo from "@/views/modules/base/rtePlan/rtePlan-bills";

//  0无需处理 5已经发送短信 10需要打电话 15已经打过电话 20需要现场处置 25已经现场处置
const dealOptions = [
  { label: "短信通知", value: "5" },
  { label: "电话询问", value: "15" },
  { label: "现场处置", value: "25" }
];
// import dayjs from "dayjs";
export default {
  name: "enforceOnLineDialog",
  props: {
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    },
    riskInfo:{
      type: Object
    }
  },
  components: {
    SimpleTable,
    EntpInfo,
    PersInfo,
    VecInfo,
    ChemicaInfo,
    RtePlanBillsInfo
  },
  data() {
    return {
      loading: false,
      visible: false,

      visibleOfEntp: false,
      visibleOfPers: false,
      visibleOfVec: false,
      visibleOfChemica: false,
      visibleOfRtePlanBills: false,

      rteplan: null,
      vecInfo: {
        vecno: "", //挂车号
        speed: "", //车速
        gpsTime: ""
      },
      location: "",
      formData: {
        vecNo: "", // 报警id
        person: "", //操作员
        type: "", // 处置操作
        opContent: "", //处置结果
        nextAlarmTime: "" // 下次报警时间
      },
      opratorList: [],
      todayRecordPage: {
        list: [],
        currPage: 0,
        pageSize: 10,
        totalPage: 0
      }
      // intoTime: "",
      // reflash: 1,
      // timer: null
    };
  },
  computed: {
    vecState() {
      if (!this.rteplan) {
        return "";
      }
      if (this.rteplan.errBackStatus == 211) return "异常结束";
      if (this.rteplan.backTm) {
        return "回场";
      } else if (this.rteplan.unloadTm) {
        return "卸货";
      } else if (this.rteplan.loadTm) {
        return "装车";
      } else if (this.rteplan.goTm) {
        return "发车";
      } else {
        return "无";
      }
    },
    radioDealOptions() {
      return dealOptions.filter(it => it.value != 5);
    },
    recordHeader() {
      // 0无需处理 5已经发送短信 10需要打电话 15已经打过电话 20需要现场处置 25已经现场处置
      return [
        { name: "处置时间", field: "updTm" },
        { name: "操作员", field: "oprNm" },
        {
          name: "处置操作",
          field: "type",
          formatter(field, data, index) {
            let res = dealOptions.filter(it => it.value === data.isHandle);
            return res.length ? res[0].label : data.isHandle;
          }
        },
        { name: "详情描述", field: "oprContent" }
      ];
    }
  },
  watch: {
    // rteplan() {
    //   this.getIntoTime();
    // },
    // reflash() {
    //   this.getIntoTime();
    // },
    vecInfo: {
      handler(val) {
        if (val.lng && val.lat) {
          this.getAddressOfVec(val.lng, val.lat);
        }
      },
      //getList里面通过searchValue去搜索数据库
      immediate: true,
      deep: true
    }
  },
  methods: {
    formatTime(seconds) {
      if(seconds<=0){
        return '-'
      }
      let hours = Math.floor(seconds / 3600);
      let minutes = Math.floor((seconds % 3600) / 60);
      let remainingSeconds = seconds % 60;
      return hours.toString().padStart(2, '0') + '时' + minutes.toString().padStart(2, '0') + '分' + remainingSeconds.toString().padStart(2, '0') + '秒';
    },
    open(vecInfo) {
      if (!vecInfo.vecNo) {
        this.$message.error("无车牌号信息，无法查看弹窗内容！");
        return;
      }
      this.rteplan = null;
      this.visible = true;
      let enforceForm = this.$refs.enforceForm;
      enforceForm && enforceForm.resetFields();
      this.$set(this, "vecInfo", vecInfo);
      // this.formData.id = vecInfo.warningId;
      this.formData.vecNo = vecInfo.vecNo;
      this.getOprlist();
      this.getTodayRecordList();
      this.getRteplanByVecno(vecInfo.vecNo);
    },
    handleResult(rqs) {
      if (rqs === undefined || rqs == null) {
        return "无";
      } else {
        return rqs;
      }
    },
    async getOprlist() {
      let res = await oprlist();
      if (res.code == 0) {
        this.opratorList = res.list.map(item => {
          return { value: item, label: item };
        });
      } else {
        this.opratorList = [];
      }
    },
    querySearch(queryString, cb) {
      var opratorList = this.opratorList;
      var results = queryString
        ? opratorList.filter(this.createFilter(queryString))
        : opratorList;

      cb(results);
    },
    createFilter(queryString) {
      return opratorList => {
        return (
          opratorList.value.toLowerCase().indexOf(queryString.toLowerCase()) !=
          -1
        );
      };
    },
    handleSelect(item) {},
    // geocoder(point, cb) {
    //   if (!this.geoc) {
    //     this.geoc = new BMap.Geocoder();
    //   }
    //   this.geoc.getLocation(point, function(rs) {
    //     let addComp = rs.addressComponents;
    //     let str = "";
    //     if (addComp.district) {
    //       str += addComp.district;
    //     }

    //     if (addComp.street) {
    //       str += addComp.street;
    //     }
    //     if (addComp.streetNumber) {
    //       str += addComp.streetNumber;
    //     }
    //     cb(str);
    //   });
    // },
    getAddressOfVec(lng, lat) {
      let _this = this;
      console.log(`%c getAddressOfVec: ${lng}, ${lat}`, "color:blue");
      $.get(
        window.actionPath +
          "/gps/getAddress?lat=" +
          lat +
          "&lng=" +
          lng +
          "&mapType=baiduMap",
        function(ret) {
          if (ret.code == 0) {
            console.log(ret.msg);
            _this.location = ret.msg;
          }
        }
      );
    },

    // 获取该车电子路单记录
    async getRteplanByVecno(tracCd) {
      let res = await $httpRteplan.getLastRtePlanByTracCd(tracCd);
      if (res.code === 0) {
        this.$set(this, "rteplan", res.data);
      }
    },
    // 获取车辆今日处置记录
    async getTodayRecordList(params) {
      if (!this.vecInfo.vecNo) {
        return;
      }
      let today = Tool.formatDate(new Date(), "yyyy-MM-dd");
      const postdata = {
        filters: {
          groupOp: "AND",
          rules: [
            { field: "trac_cd", op: "eq", data: this.vecInfo.vecNo },
            {
              field: "upd_tm",
              op: "bt",
              data: [today + " 00:00:00", today + " 23:59:59"]
            }
          ]
        },
        page: params && params.page ? params.page : 1,
        limit: params && params.limit ? params.limit : 20
      };
      const res = await $httpMonit.getPageOffsite(postdata);
      if (res.code === 0) {
        let data = res.page;
        this.todayRecordPage = data;
      } else {
        this.todayRecordPage = {
          list: [],
          currPage: 0,
          pageSize: 10,
          totalPage: 0
        };
      }
    },
    closedDrawer() {},
    save() {
      let _this = this;
      this.$refs.enforceForm.validate(valid => {
        if (valid) {
          this.$confirm("确定提交吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              _this.loading = true;
              let postData = Object.assign({}, this.formData);
              // postData.catCd = "2550.180.150";
              $httpMonit
                .handle(postData)
                .then(res => {
                  this.loading = false;
                  if (res.code == 0) {
                    this.$message({
                      type: "success",
                      message: "提交成功!"
                    });
                    this.visible = false;
                  } else {
                    this.$message.info(res.msg || "服务器错误，请联系管理员");
                  }
                })
                .catch(err => {
                  this.loading = false;
                });
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    },
    // 车辆信息弹窗
    showVehicleDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/vec/info/'+pk,'_blank');
      this.visibleOfVec = true;
      this.$nextTick(() => {
        this.$refs.vecInfo.initByPk(pk);
      });
    },

    // 人员信息弹窗
    showPersDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/pers/info/'+pk,'_blank');
      this.visibleOfPers = true;
      this.$nextTick(() => {
        this.$refs.persInfo.initByPk(pk);
      });
    },

    // 货物信息弹窗
    showGoodsDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      // let location = window.location;
      // window.open(location.origin+location.pathname+'#/base/chemica/info/'+pk,'_blank');
      this.visibleOfChemica = true;
      this.$nextTick(() => {
        // this.$refs.chemicaInfo.initByEnchPk(pk);
        this.$refs.chemicaInfo.getInfoByProdPk(pk);
      });
    },
    // 电子运单弹窗
    showCdDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.visibleOfRtePlanBills = true;
      this.$nextTick(() => {
        this.$refs.RtePlanBillsInfo.rtePlanNewByPk(pk);
      });
    },
    // 企业信息弹窗
    showEntpDialog(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.visibleOfEntp = true;
      this.$nextTick(() => {
        this.$refs.entpInfo.initByPk(pk);
      });
    },
    reflashData() {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.reflash = Math.random();
      }, 1000);
    },
    getIntoTime() {
      this.reflashData();
      // if (this.selectVecInfo.residenceTimeStart) {
      //   let text = "";
      //   if (
      //     this.rteplan.csneeWhseDistCd.includes("330211") &&
      //     !this.rteplan.csnorWhseDistCd.includes("330211")
      //   ) {
      //     text = "进入镇海卸货，已滞留";
      //   }
      //   if (
      //     !this.rteplan.csneeWhseDistCd.includes("330211") &&
      //     this.rteplan.csnorWhseDistCd.includes("330211")
      //   ) {
      //     text = "在镇海装货完成，已滞留";
      //   }
      //   this.intoTime = `${dayjs(this.selectVecInfo.residenceTimeStart).format(
      //     "MM-DD HH:mm"
      //   )} ${text}${this.getDateDiff(this.selectVecInfo.residenceTimeStart)}。`;
      // } else {
      //   this.intoTime = "";
      // }
    }
  },
  destroyed() {
    clearTimeout(this.timer);
  }
};
</script>
<style lang="scss" scoped>
.font-red {
  color: #d00;
}
/deep/ {
  .custom-radio-group.el-radio-group {
    .el-radio {
      margin-right: 15px;
      min-width: 108px;
    }
    .el-radio + .el-radio {
      margin-left: 0;
    }
  }
  .forcus {
    .el-button.el-button--mini {
      padding: 4px 15px;
    }
  }
}
.my-drawer-wrapper /deep/ {
  position: relative;
  .el-drawer__open {
    .el-drawer__header {
      display: none;
    }
    .el-drawer.my-drawer {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      overflow: auto;
      margin: 0;
      z-index: 2006;
      &.rtl {
        left: auto;
      }
    }
    .my-drawer-body {
      display: flex;
      flex-direction: column-reverse;
      width: 100%;
      height: 100vh;
      .my-drawer-body-content {
        // width: 100%;
        flex: 1 auto;
        overflow-y: auto;
        font-size: 12px;
        width: auto;
        border: 1px solid #f6f6f6;
        margin: 4px 6px 10px;
        border-radius: 8px;
      }
      .my-drawer-body-footer {
        overflow-y: auto;
        width: 100%;
        height: 200px;
        min-height: 200px;
        background-color: rgb(255, 246, 231);
        box-shadow: #e2e2e0 2px 3px 10px;
        margin: 4px 6px 10px;
        width: auto;
        border-radius: 8px;
      }
      .title {
        box-sizing: border-box;
        padding: 8px 10px;
        border-bottom: 1px solid #ebeef5;
        font-weight: bolder;
        // background-color: #56a2ff;
        // color: #fff;
        background-color: #ebeef5;
        color: #5c5c5c;
        &.forcus {
          // background-color: #2b8aff;
          // background-color: #ffa156;
          background-color: #e6a23c;
          color: #fff;
        }
        font-size: 14px;
      }
      .content {
        padding: 10px;
        box-sizing: border-box;
        color: #333;
        .detail-label,
        .detail-desc {
          display: inline-block;
          vertical-align: top;
        }
        .detail-label {
          color: #9a9a9a;
        }
      }
    }
  }
}
</style>
