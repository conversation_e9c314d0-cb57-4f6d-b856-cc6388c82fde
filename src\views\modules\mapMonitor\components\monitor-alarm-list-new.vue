<template>
  <div
    class="monitor-panel noselect"
    :style="defaultStyle"
    ref="monitorAlarmList"
  >
    <div
      class="monitor-panel-header"
      :style="{
        'border-radius':
          collapseIcon == 'el-icon-arrow-up' ? '5px 5px 0 0' : '5px',
        'border-bottom':
          collapseIcon == 'el-icon-arrow-up'
            ? '3px solid rgb(18, 69, 199)'
            : '0'
      }"
    >
      <span
        >重点监管车辆：<span
          style="color:red;font-weight:600;font-size:18px;"
          >{{ myList.length }}</span
        >&nbsp;&nbsp;<i
          class="el-icon-refresh header-icon"
          @click="refresh"
          title="刷新"
        ></i
        >&nbsp;</span
      >
      <i
        class="header-icon ft-rt"
        :class="collapseIcon"
        @click="collapseHandle"
      ></i>
    </div>
    <div class="monitor-panel-body" v-show="collapseIcon == 'el-icon-arrow-up'">
      <collapse-transition>
        <ul class="vec-list-ul">
          <li
            v-for="item in myList"
            :key="item.vecNo"
            @click="showVecInfo(item.vecNo)"
          >
            <div class="clearfix">
              <div class="vec-no">{{ item.vecNo }}</div>
              <!-- <el-tag effect="dark" :type="tag.color" v-for="tag in item.tags" :key="tag.text" size="small" style="margin: 0 0 0 5px;">{{tag.text}}</el-tag> -->
              <el-tag
                v-if="item.isStop"
                size="small"
                style="margin: 0 0 0 5px;"
                >违停</el-tag>
              <el-tag
                v-if="item.isRemain"
                type="warning"
                size="small"
                style="margin: 0 0 0 5px;"
                >滞留</el-tag>
              <!-- 0无需处理 5已经发送短信 10需要打电话 15已经打过电话 20需要现场处置 25已经现场处置 -->
              <!-- <div
                class="dealwith"
                :class="{
                  highlight: item.opStatus === 10 || item.opStatus === 20
                }"
              >
                <template v-if="item.opStatus === 5">短信通知</template>
                <template
                  v-else-if="item.opStatus === 10 || item.opStatus === 15"
                  >电话询问</template
                >
                <template
                  v-else-if="item.opStatus === 20 || item.opStatus === 25"
                  >现场处置</template
                >
              </div> -->
            </div>
          </li>
        </ul>
      </collapse-transition>
    </div>
    <div
      class="monitor-panel-footer"
      v-show="collapseIcon == 'el-icon-arrow-up'"
    >
      <a href="javascript:void(0)" @click="showEnforceLog">
        <div class="chrome">查看车辆实时处置记录</div>
      </a>
      <router-link :to="{ path: '/chromeAudio' }" tag="a" target="_blank">
        <div class="chrome">chrome浏览器如何打开提示音？</div>
      </router-link>
    </div>
    <!-- 危化品信息详情 -->
    <el-dialog
      title="车辆实时处置记录"
      :visible.sync="visibleDg"
      append-to-body
      width="80%"
      class="detail-dialog"
    >
      <realtime-list :isPage="false" ref="realtimeList"></realtime-list>
    </el-dialog>
  </div>
</template>

<script>
import collapseTransition from "@/components/CollapseTransition";
import * as Tool from "@/utils/tool";
import realtimeList from "@/views/modules/kpi/realtime-zero/list";
import { debounce } from "lodash";
export default {
  name: "monitorAlarmList",
  props: {
    // default style
    defaultStyle: {
      type: Object,
      default: function() {
        return {
          position: "absolute",
          top: "10px",
          left: "220px",
          width: "200px",
          zIndex: 30
        };
      }
    },
    // vec data
    vecListPage: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      collapseIcon: "el-icon-arrow-up",
      loading: null,
      visibleDg: false,
      dialogPositionVisible: false,
      vecNoPos: null,
      Tool: Tool
    };
  },
  // watch: {
  //   vecListPage(newName, oldName) {
  //     if (newName && newName.length == 0) {
  //       this.collapseIcon = "el-icon-arrow-down";
  //     } else {
  //       this.collapseIcon = "el-icon-arrow-up";
  //     }
  //   }
  // },
  computed: {
    myList() {
      const arr = [];
      this.vecListPage.forEach(item => {
        const tagMap = {
          违停: {
            text: "违停",
            color: ""
          },
          滞留: {
            text: "滞留",
            color: "warning"
          },
          违章: {
            text: "违章",
            color: "danger"
          }
        };
        // 2024.7.8  字段更改为isRemain 和 isStop 布尔值
        // let riskInfoShort = item.riskInfoShort;
        // let tags = riskInfoShort.length ? riskInfoShort.split(",") : [];
        // tags = new Set(tags);
        // item.tags = [...tags].map(t => {
        //   let temp = tagMap[t];
        //   if (temp) {
        //     return temp;
        //   } else {
        //     return {
        //       text: t,
        //       color: "info"
        //     };
        //   }
        // });
        arr.push(item);
      });
      // console.log(arr,'>>>>>>>>>> 重点监管车辆')
      return arr;
    }
  },
  components: {
    collapseTransition,
    // AlarmInfo,
    // mapDialog
    realtimeList
  },
  methods: {
    // collapse panel effect event
    collapseHandle() {
      if (this.collapseIcon == "el-icon-arrow-down") {
        this.collapseIcon = "el-icon-arrow-up";
      } else {
        this.collapseIcon = "el-icon-arrow-down";
      }
      if (this.vecListPage.length == 0) {
        this.collapseIcon = "el-icon-arrow-down";
      }
    },

    // loading effect
    fullNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.monitorSearchBar
      });
      return loading;
    },
    // 报警信息弹窗
    showAlarmDialog(vecNo, alarmPk) {
      if (!vecNo) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.$emit("vecSite", vecNo, alarmPk);
    },
    // getPosition(vecNo){//查看当前位置
    //     this.dialogPositionVisible=true
    //     this.vecNoPos = vecNo
    //     this.$nextTick(() => {
    //         this.$refs.getPos.getLngLat();
    //     });
    // }
    showEnforceLog() {
      this.visibleDg = true;
      this.$nextTick(() => {
        this.$refs.realtimeList.init();
      });
    },
    showVecInfo(vec) {
      this.$emit("vecSite", vec, "");
    },
    refresh: debounce(
      function(query) {
        // console.log("$crefresh", "color:red");
        this.$emit("refresh");
      },
      1000,
      { leading: true, trailing: false }
    )
  }
};
</script>

<style lang="scss" scoped>
.monitor-panel {
  min-width: 220px;
  max-height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;
  z-index: 1;

  .monitor-panel-header {
    position: relative;
    height: 42px;
    line-height: 32px;
    background-color: rgba(28, 91, 250, 0.7);
    box-shadow: 0 2px 2px #aaa;
    color: #fff;
    box-sizing: border-box;
    padding: 5px 8px;
    border-radius: 5px 5px 0 0;
    font-size: 14px;
    text-align: center;

    .header-icon {
      // float: right;
      line-height: 32px;
      cursor: pointer;
    }
  }
  .monitor-panel-body {
    background: #fff;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
    // display: flex;
    // flex-direction: column;
    flex: 1 auto;
    overflow-y: auto;
  }
  .monitor-panel-footer {
    background: #fff;
    padding-top: 10px;
    border-top: 1px dashed #eee;
    border-radius: 0 0 5px 5px;
  }
}

.vec-list-ul {
  padding: 0;
  margin: 0;
  > li {
    box-sizing: border-box;
    position: relative;
    /* height: 35px; */
    /* line-height: 34px; */
    border-bottom: 1px dashed #eee;
    font-size: 12px;
    color: #999;
    cursor: pointer;
    padding: 10px;
    &:hover {
      background-color: #f4f4f4;
    }
  }
  .vec-no {
    width: 75px;
    text-align: center;
    float: left;
    background-color: #1671fd;
    border-radius: 2px;
    padding: 6px 5px;
    color: #fff;
  }
  .dealwith {
    width: 70px;
    text-align: center;
    float: right;
    background-color: #ccc;
    border-radius: 2px;
    padding: 6px 5px;
    color: #fff;
    margin-left: 5px;
    &.highlight {
      background-color: #d00;
    }
  }
}
.chrome {
  font-size: 12px;
  text-align: center;
  color: #1671fd;
  text-decoration: underline;
  padding-bottom: 10px;
  cursor: pointer;
}
</style>
<style>
.anchorBL {
  display: none;
}
</style>
