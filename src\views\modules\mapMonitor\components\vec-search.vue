<template>
  <div class="vec-search-wrpper" :style="defaultStyle" ref="searchbar">
    <div class="label">车牌号</div>
    <!-- <el-select v-model="vehicleNo" class="searchInputMonitor" size="mini" filterable remote reserve-keyword clearable placeholder="输入车牌号搜索车辆" :remote-method="searchHandle" :loading="loading">
      <el-option v-for="(item) in vecList" :key="item.vecNo" :label="item.vecNo" :value="item.vecNo"></el-option>
    </el-select> -->

    <el-select
      v-model="vehicleNo"
      :loading="loading"
      class="searchInputMonitor"
      size="mini"
      filterable
      placeholder="输入车牌号搜索车辆"
      clearable
    >
      <el-option
        v-for="(vecno, index) in list"
        :key="index"
        :label="vecno"
        :value="vecno"
        @click.native="selectVec(vecno)"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
// import { debounce } from "lodash";
// import collapseTransition from "@/components/CollapseTransition";
export default {
  name: "search-vec-bar",
  props: {
    // default style
    defaultStyle: {
      type: Object,
      default: function() {
        return {
          float: "left",
          zIndex: "10"
        };
      }
    },
    // vec data
    vecList: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      vehicleNo: "",
      loading: null
    };
  },
  computed: {
    list() {
      return this.vecList.map(it => it.vecNo);
    }
  },
  // watch: {
  //   vehicleNo() {
  //     if (this.vehicleNo) {
  //       const d = this.vecList.find(item => item.vecNo === this.vehicleNo);
  //       this.selectVec(d);
  //     }
  //   }
  // },
  methods: {
    // search vec list event
    // searchHandle: debounce(
    //   function(query) {
    //     let _this = this;
    //     this.loading = true;
    //     let vecno = query.trim();
    //     // this.$emit(
    //     //   "searchVecNo",
    //     //   vecno,
    //     //   this.vecListPage.pageNo,
    //     //   this.vecListPage.pageSize,
    //     //   function() {
    //     //     _this.loading = false;
    //     //   }
    //     // );

    //     // if (this.vecListPage.result.length == 1) {
    //     //   _this.selectVec(this.vecListPage.result[0]);
    //     // }
    //   },
    //   1000,
    //   { leading: false, trailing: true }
    // ),
    // select vec
    selectVec(vecNo) {
      // console.log('@c>>>>>','color:red')
      // console.log(vecNo)
      this.$emit("selected", vecNo);
    }
  }
};
</script>

<style lang="scss" scoped>
.vec-search-wrpper {
  display: flex;
  line-height: 28px;
  z-index: 1;
  .label {
    width: 60px;
    background-color: #306ff6;
    display: inline-block;
    text-align: center;
    color: #fff;
    border-radius: 5px 0 0 5px;
    font-size: 13px;
  }
  /deep/ {
    .el-input__inner {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
</style>
