<template>
  <div class="app-main-content">
    <bmap :param="mapSetting" @ready="mapReadyCallback">
      <monitor-alarm-list-new
        v-show="vecTypeShow === 'heavy' || vecTypeShow === 'all'"
        ref="monitorAlarmList"
        :vecListPage="superFocusVecList"
        :defaultStyle="{ position: 'absolute', top: '10px', left: '10px' }"
        @vecSite="vecHeavySite"
        @refresh="refreshDoTask(true)"
      ></monitor-alarm-list-new>
      <div class="refresh-tooltip" v-show="refreshNextTime > 0">
        {{ refreshNextTime }}秒后刷新
      </div>
      <div class="vec-total-wape">
        <el-tooltip
          placement="top"
          effect="light"
          v-for="vecType in vecTypes"
          :key="vecType.nmEn"
          v-if="allCarObj[vecType.nmEn]"
        >
          <div class="tipContent" slot="content">
            <el-button
              class="inner-btn"
              size="small"
              v-for="(item, index) in [
                '全部车辆',
                '重点监管',
                '重大风险',
                '较大风险',
                '一般风险',
                '低风险',
                '未知风险'
              ]"
              :key="item"
              :class="{
                active:
                  vecTypeShow === vecType.nmEn &&
                  subvecTypeShow ===
                    (index === 0 ? 'total' : 'warningGrade' + (6 - index))
              }"
              @click="
                showVecByType(
                  vecType.nmEn,
                  index === 0 ? 'total' : 'warningGrade' + (6 - index)
                )
              "
            >
              {{ item }}
              {{
                allCarObj[vecType.nmEn][
                  index === 0 ? "total" : "warningGrade" + (6 - index)
                ].length
              }}辆
            </el-button>
            <el-button
              class="inner-btn"
              size="small"
              :class="{
                active:
                  vecTypeShow === vecType.nmEn && subvecTypeShow === 'offline'
              }"
              @click="showVecByType(vecType.nmEn, 'offline')"
            >
              离线
              {{ allCarObj[vecType.nmEn].offline.length }}辆
            </el-button>
          </div>
          <el-button
            class="vec-total-btn"
            :class="[vecTypeShow === vecType.nmEn ? 'active' : '']"
            @click="displayVecMarkerByType(vecType.nmEn)"
          >
            {{ vecType.nmCn
            }}{{
              allCarObj[vecType.nmEn].total
                ? allCarObj[vecType.nmEn].total.length
                : 0
            }}辆
            <el-tooltip
              effect="light"
              placement="top-start"
              v-if="vecTypesTips[vecType.nmEn]"
            >
              <div
                class="tipContent"
                slot="content"
                style="width: 400px"
                v-html="vecTypesTips[vecType.nmEn]"
              ></div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-button>
        </el-tooltip>
        <vec-search
          ref="monitorSearchbar"
          :vecList="allData"
          @selected="selectVecHandle"
          :defaultStyle="{ position: 'absolute', top: '-40px', left: 0 }"
        >
        </vec-search>
      </div>
    </bmap>
    <div v-show="false">
      <monitor-info-window
        ref="monitorInfoWindow"
        :selectVecInfo="selectVecInfo"
        :rteplan="rteplan"
        :selectedAlarmPk="selectedAlarmPk"
        @close="closeInfoWindow"
      ></monitor-info-window>
      <monitor-cp-info-window
        ref="monitorCpInfoWindow"
        :selectEntpInfo="selectEntpInfo"
        @close="closeInfoWindow"
      ></monitor-cp-info-window>
    </div>
    <audio :src="AudioAlarmSrc" hidden ref="audio"></audio>
    <enforce-drawer ref="enforceDrawer" :riskInfo="currentRiskInfo"></enforce-drawer>
    <all-fence
      ref="allFence"
      :map="map"
      :defaultStyle="{ top: '85px' }"
    ></all-fence>
  </div>
</template>

<script>
import bmap from "@/components/BMapComp";
import websocket from "@/mixins/websocket";

// import MonitorSearchbarSimple from "./components/monitor-searchbar-simple";
import vecSearch from "./components/vec-search";
import MonitorAlarmListNew from "./components/monitor-alarm-list-new";
import MonitorInfoWindow from "./components/monitor-info-window";
import MonitorCpInfoWindow from "./components/monitor-cp-info-window";
import enforceDrawer from "./components/enforce-drawer";
import allFence from "./components/all-fence";

import * as $http from "@/api/mapMonit";
import * as $httpMonit from "@/api/mapMonitor";
import * as $httpVec from "@/api/rtePlan";

import HashMap from "@/utils/hashmap";
import * as Tool from "@/utils/tool";
import dayjs from "dayjs";

// import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import imgsConfig from "static/jsonConfig/mapMonitorConfig.json";
import AudioAlarmSrc from "static/alarm.mp3";

const offlineTime = 1200000; // 离线时间的判断标准

export default {
  name: "MapMonitor",
  mixins: [websocket],
  components: {
    bmap,
    vecSearch,
    MonitorAlarmListNew,
    MonitorInfoWindow,
    MonitorCpInfoWindow,
    enforceDrawer,
    allFence
  },
  data() {
    return {
      map: null,
      mapSetting: {
        scrollWheelZoom: true,
        type: BMAP_SATELLITE_MAP,
        mapHeight: "calc(100vh - 50px)",
        toolStyle: {}
      },
      AudioAlarmSrc: AudioAlarmSrc,

      vecTypes: [], // 底部展示的车辆类型
      vecTypesTips: {}, // 底部车辆类型的提示信息
      allCarObj: {},

      superFocusVecList: [],

      refreshFlag: true, // 计时结束后是否请求的标志
      refreshTask: null, // 定时任务
      refreshNextTime: 0, // 地图刷新时间剩余时间（用于倒计时）
      refreshIntervalTime: 30, // 每次刷新间隔的时间 30秒

      carsMarkerMap: new HashMap(), // 所有车辆数据
      policeCarMap: new HashMap(),

      // infoBox: null,
      infoBoxCache: null,

      vecTypeShow: "all", // 显示的当前车辆类型，默认显示重车
      subvecTypeShow: "total", // 车辆类型下的子类

      selectVecInfo: { vehicleNo: "" }, // InfoBox:已选车辆信息
      selectEntpInfo: {}, // _InfoBox:已选企业信息
      selectedAlarmPk: "", // 选中的报警信息，点击左侧报警列表显示报警信息
      rteplan: null, // InfoBox：已选车辆电子路单信息

      // heavyList: [
      //   { title: "高危重车", key: 12, num: 0, value: [], sp: true },
      //   { title: "一级报警", key: 91, num: 0, value: [], sp: true },
      //   { title: "二级报警", key: 92, num: 0, value: [], sp: true },
      //   { title: "镇海装货", key: 95, num: 0, value: [], sp: true },
      //   { title: "镇海卸货", key: 96, num: 0, value: [], sp: true },
      //   { title: "镇海到镇海", key: 97, num: 0, value: [], sp: true },
      //   { title: "途经镇海", key: 98, num: 0, value: [], sp: true }, // 沈莹说要加这个分类 2022-02-15
      //   { title: "全部", key: "all", num: 0, value: [] },
      //   { title: "高速", key: 3, num: 0, value: [] },
      //   { title: "地面道路", key: 1, num: 0, value: [] },
      //   { title: "化工企业内部", key: 2, num: 0, value: [] },
      //   { title: "企业停车场", key: 4, num: 0, value: [] },
      //   { title: "公共停车场", key: 5, num: 0, value: [] },
      //   { title: "加油站", key: 6, num: 0, value: [] },
      //   { title: "维修区", key: 7, num: 0, value: [] },
      //   { title: "服务区", key: 8, num: 0, value: [] },
      //   { title: "收费站", key: 9, num: 0, value: [] },
      //   { title: "装卸区", key: 10, num: 0, value: [] },
      //   { title: "其他", key: 11, num: 0, value: [] }
      //   // { title: '中危重车', key: 11, num: 0, value: [] },
      // ],
      allData: [],
      loadingTimer: null,
      currentRiskInfo:{}, //已选车辆的riskInfo
      // 稽查大队
      complexLabelMarkerMap: new HashMap()
      // isDLJC: false,
    };
  },
  // watch: {
  //   allCarObj: {
  //     handler(val) {
  //       console.log(val, ">>>>>>>allCarObj");
  //     },
  //     deep: true
  //   }
  // },
  created() {
    this.getGpsType(); // 获取车辆类型
    this.getControlDetail(); // 获取危化品信息
  },
  mounted: function() {},
  destroyed() {
    this.stopRefreshTask();
    this.closeInfoWindow();
  },
  methods: {
    getTransLoading() {
      //无背景色
      const loading = this.$loading({
        lock: true,
        spinner: "el-icon-loading",
        background: "transparent"
      });
      return loading;
    },
    // 地图初始化完成后的回调
    mapReadyCallback(map) {
      // this.map = map;
      this.$set(this, "map", map);
      this.$refs.allFence.setMap(map);
      let _this = this;
      this.$nextTick(() => {
        setTimeout(function() {
          _this.refreshDoTask(true); // 重新开启任务
        }, 1000);
        // this.refreshDoTask(true); // 重新开启任务
      });
    },
    // websockset实时报警
    websocketCallback(data) {
      // console.log("%cwebsocketCallback>>>>>>>>>>>>>>>>", "color:red");
      // console.log(data);
      if (data.catNm === "alarm") {
        let vecData = data.msg;
        let txt = vecData.tractorNo + vecData.catNmCn;
        this.playAudio();
        this.$message({
          message: `${txt}`,
          type: "warning"
        });
        // TODO 刷新报警
        // this.alarmList(1, 3, txt); //刷新报警车辆
      }
    },
    // 播放警报
    playAudio() {
      let audio = this.$refs.audio;
      if (audio) {
        audio.currentTime = 0; //从头开始播放提示音
        audio.play(); //播放
      }
    },
    //获取车辆类型
    getGpsType() {
      let _this = this;
      this.vecTypes = [
        {
          id: 1778,
          pcd: null,
          cd: "gps.01",
          nmEn: "all",
          nmCn: "全部",
          type: "在途监测车辆类型",
          isDefault: 0
        },
        {
          id: 1779,
          pcd: null,
          cd: "gps.02",
          nmEn: "heavy",
          nmCn: "重车",
          type: "在途监测车辆类型",
          isDefault: 1
        },
        {
          id: 1780,
          pcd: null,
          cd: "gps.03",
          nmEn: "empty",
          nmCn: "空车",
          type: "在途监测车辆类型",
          isDefault: 0
        },
        {
          id: 1781,
          pcd: null,
          cd: "gps.23",
          nmEn: "passby",
          nmCn: "过境",
          type: "在途监测车辆类型",
          isDefault: 0
        },
        {
          id: 1782,
          pcd: null,
          cd: "gps.24",
          nmEn: "2",
          nmCn: "易燃气体",
          type: "在途监测车辆类型-2类",
          isDefault: 0
        },
        {
          id: 1783,
          pcd: null,
          cd: "gps.25",
          nmEn: "3",
          nmCn: "易燃液体",
          type: "在途监测车辆类型-3类",
          isDefault: 0
        },
        {
          id: 1784,
          pcd: null,
          cd: "gps.26",
          nmEn: "4",
          nmCn: "易燃固体",
          type: "在途监测车辆类型-4类",
          isDefault: 0
        },
        {
          id: 1783,
          pcd: null,
          cd: "gps.27",
          nmEn: "6-1",
          nmCn: "毒性物质",
          type: "在途监测车辆类型-6.1类",
          isDefault: 0
        },
        {
          id: 1784,
          pcd: null,
          cd: "gps.28",
          nmEn: "8",
          nmCn: "腐蚀性物质",
          type: "在途监测车辆类型-8类",
          isDefault: 0
        },
        {
          id: 1785,
          pcd: null,
          cd: "gps.29",
          nmEn: "5",
          nmCn: "氧化性物质",
          type: "在途监测车辆类型-5类",
          isDefault: 0
        },
        {
          id: 1786,
          pcd: null,
          cd: "gps.30",
          nmEn: "6-2",
          nmCn: "感染性物质",
          type: "在途监测车辆类型-6.2类",
          isDefault: 0
        },
        {
          id: 1787,
          pcd: null,
          cd: "gps.31",
          nmEn: "10",
          nmCn: "危险废物",
          type: "在途监测车辆类型-危险废物",
          isDefault: 0
        },
        {
          id: 1788,
          pcd: null,
          cd: "gps.32",
          nmEn: "other",
          nmCn: "其他物质",
          type: "在途监测车辆类型-其他",
          isDefault: 0
        }
      ];

      // $http.getGpsType().then(res => {
      //   if (res.code === 0) {
      //     _this.vecTypes = res.data;
      let checkedType = null;
      _this.vecTypes.forEach(item => {
        if (item.isDefault) {
          checkedType = item.nmEn;
        }
      });
      _this.vecTypeShow = checkedType || "all";
      //   }
      // });
    },
    // 获取车辆类型的信息提示
    getControlDetail() {
      let _this = this;
      $http.getControlDetail().then(res => {
        if (res && res.code === 0) {
          const d = res.data;
          Object.keys(d).forEach(key => {
            d[key] = d[key].join(", ");
          });
          this.$set(_this, "vecTypesTips", d);
        }
      });
    },
    // 获取重点监管车辆
    getSuperFocusVecList() {
      let _this = this;
      $httpMonit.getSuperFocusVecList().then(res => {
        if (res && res.code === 0) {
          const d = res.data;
          _this.superFocusVecList = d;
        }
      });
    },
    // 单车监控选中车辆
    selectVecHandle(vecNo) {
      // let vecNo = data.vecNo;
      if (!vecNo) {
        return;
      }
      let marker = this.carsMarkerMap.get(vecNo);
      if (marker) {
        this.map.centerAndZoom(marker.getPosition(), 16);
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      } else {
        this.$message.error("该车不在区内！");
      }
    },
    refreshDoTask(isShowLoading) {
      this.resetRefreshTask(); // 开启定时任务
      this.doTask(null, isShowLoading); // 开启任务
    },
    // 执行任务
    doTask(areaType = null, isShowLoading = false) {
      let _this = this;
      this.refreshOnlineVec(areaType, isShowLoading); // default, show the full vehicle
      setTimeout(function() {
        _this.showPoliceCar(); // 获取稽查大队车辆
      }, 1000);
      this.getSuperFocusVecList(); // 获取重点监管车辆列表
    },
    // 开启定时任务
    startRefreshTask() {
      let _this = this;
      this.refreshNextTime = this.refreshIntervalTime;
      this.refreshTask = setInterval(function() {
        _this.refreshNextTime -= 1;
        if (_this.refreshNextTime == 0) {
          if (_this.refreshFlag) {
            _this.refreshFlag = false;
            _this.doTask(_this.areaType);
          }
          _this.resetRefreshTask(); // 重置定时任务
        }
      }, 1000);
    },
    // 重置定时任务
    resetRefreshTask() {
      this.refreshNextTime = this.refreshIntervalTime;
      window.clearInterval(this.refreshTask);
      this.startRefreshTask();
    },
    // 停止定时任务
    stopRefreshTask() {
      window.clearInterval(this.refreshTask);
    },
    createComplexLabelMarker(point, text) {
      let complexLabelMarker = null;
      // 复杂的自定义覆盖物
      function ComplexCustomOverlay(point, text) {
        this._point = point;
        this._text = text;
        this._closebtn = null;
      }
      ComplexCustomOverlay.prototype = new BMap.Overlay();
      ComplexCustomOverlay.prototype.setPosition = function(point, text) {
        this.hide();
        this._point = point;
        if (text) {
          this._text = text;
        }
        this.draw();
        this.show();
      };
      ComplexCustomOverlay.prototype.initialize = function(map) {
        this._map = map;
        var div = (this._div = document.createElement("div"));
        div.style.position = "absolute";
        div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
        div.style.backgroundColor = "#EE5D5B";
        div.style.border = "1px solid #BC3B3A";
        div.style.color = "white";
        div.style.height = "23px";
        div.style.padding = "2px";
        div.style.lineHeight = "18px";
        div.style.whiteSpace = "nowrap";
        div.style.MozUserSelect = "none";
        div.style.fontSize = "12px";
        var span = (this._span = document.createElement("span"));
        var btn = (this._closebtn = document.createElement("span"));
        btn.style.display = "none";
        btn.style.marginLeft = "10px";
        btn.style.fontSize = "14px";
        btn.style.cursor = "pointer";

        btn.innerHTML = "×";
        div.appendChild(span);
        div.appendChild(btn);
        span.appendChild(document.createTextNode(this._text));
        var that = this;

        var arrow = (this._arrow = document.createElement("div"));

        arrow.style.position = "absolute";
        arrow.style.width = "10px";
        arrow.style.height = "10px";
        arrow.style.border = "4px solid transparent";
        arrow.style.borderTopColor = "#EE5D5B";
        arrow.style.top = "22px";
        arrow.style.left = "24px";
        arrow.style.overflow = "hidden";
        div.appendChild(arrow);

        div.onmouseover = function() {
          that._closebtn.style.display = "inline-block";
        };

        div.onmouseout = function() {
          that._closebtn.style.display = "none";
        };

        btn.onclick = function() {
          div.style.display = "none";
          this.style.display = "none";
        };

        this._map.getPanes().labelPane.appendChild(div);

        return div;
      };

      ComplexCustomOverlay.prototype.draw = function() {
        var map = this._map;
        var pixel = map.pointToOverlayPixel(this._point);
        this._div.style.left =
          pixel.x - parseInt(this._arrow.style.left) + "px";
        this._div.style.top = pixel.y - 40 + "px";
      };

      complexLabelMarker = new ComplexCustomOverlay(point, text);
      this.map.addOverlay(complexLabelMarker);
      // }

      return complexLabelMarker;
    },
    // 获取稽查大队车辆
    showPoliceCar() {
      let _this = this;
      $http
        .policeCar()
        .then(res => {
          if (res.code == 0) {
            let data = res.data;
            if (data && data.length) {
              data.forEach(item => {
                let vecNo = item._id.replace(/警$/, "J");
                let marker = _this.policeCarMap.get(vecNo);

                // this.isDLJC = true;
                if (marker) {
                  let point = new BMap.Point(item.lonBd, item.latBd);
                  marker.setPosition(point);
                  marker.setRotation(item.direction);
                  let complexLabelMarker = _this.complexLabelMarkerMap.get(
                    vecNo
                  );
                  complexLabelMarker.setPosition(point, item._id);
                } else {
                  let point = new BMap.Point(item.lonBd, item.latBd);
                  let icon = new BMap.Icon(
                    imgsConfig.vec["police_car"],
                    new BMap.Size(46, 46)
                  );
                  marker = new BMap.Marker(point, { icon: icon });
                  marker.setRotation(item.direction);
                  marker.setZIndex(new Date().getTime());

                  _this.map.addOverlay(marker);

                  _this.policeCarMap.put(vecNo, marker);

                  // this.$set(this.policeCar,"key",item._id.replace(/警$/,"J"));
                  // this.$set(this.policeCar,"marker",marker);
                  _this.complexLabelMarkerMap.put(
                    vecNo,
                    _this.createComplexLabelMarker(
                      marker.getPosition(),
                      item._id
                    )
                  );
                }
              });
            }
          }
        })
        .catch(err => {});
    },
    // 创建5个风险等级数组对象{all:[],warningGrade1:[],warningGrade2:[]....}
    createObjFactory() {
      let res = {};
      (res.total = []),
        Array.apply(null, { length: 6 }).forEach((it, index) => {
          // res[`warningGrade${index + 1}`] = [];
          res[`warningGrade${index}`] = [];
        });
      res.offline = [];
      return res;
    },
    // according to the vehicle's type,  refresh online vehicle on the map
    refreshOnlineVec(areaType, flag) {
      let _this = this;
      this.areaType = areaType;
      if (flag) {
        this.mapNodeLoading = this.getTransLoading();
      }
      let API = areaType
        ? $httpMonit.getLocalCarList(areaType)
        : $httpMonit.getAllVecListWithGPS();
      API.then(async response => {
        if (response.code == 0) {
          let res = (this.allData = response.data);
          let allCarObj = {};
          _this.vecTypes.forEach(item => {
            allCarObj[item.nmEn] = _this.createObjFactory();
          });
          if (!allCarObj.all) {
            allCarObj.all = _this.createObjFactory();
          }
          // 车辆类型下的细分类初始化
          // this.heavyList.forEach(item => {
          //   item.num = 0;
          //   item.value.length = 0;
          // });
          let currentTime = new Date().getTime();
          for (let i = 0, len = res.length; i < len; i++) {
            let item = res[i];
            let index = i;
            // carType：0空车 1重车 2过境车
            if (item.carType == 4) {
              item.carType = 0;
            }
            let vehicleNo = item.vecNo; // 车牌编号
            // let warningGrade = item.warningGrade; // 风险等级
            let warningGrade = item.riskLevel; // 风险等级
            let gspIntervalTime = item.gpsTime
              ? currentTime - new Date(item.gpsTime).getTime()
              : 9999999; // gps距离现在的时间间隔

            // 全部下的存储
            allCarObj.all.total.push(vehicleNo);
            allCarObj.all[`warningGrade${warningGrade}`].push(vehicleNo);

            // 离线
            if (gspIntervalTime >= offlineTime) {
              allCarObj.all.offline.push(vehicleNo);
            }

            // carType, 0:空车，1：重车，2：过境车
            let checkCarTypeObj = null;
            if (item.carType == 0) {
              checkCarTypeObj = allCarObj.empty;
            } else if (item.carType == 1) {
              checkCarTypeObj = allCarObj.heavy;
            } else if (item.carType == 2) {
              checkCarTypeObj = allCarObj.passby;
            }
            if (checkCarTypeObj) {
              checkCarTypeObj.total.push(vehicleNo);
              checkCarTypeObj[`warningGrade${warningGrade}`].push(vehicleNo);
              // 离线
              if (gspIntervalTime >= offlineTime) {
                checkCarTypeObj.offline.push(vehicleNo);
              }
            }

            // 车辆类型：易制毒、易制爆、易燃气体、易燃液体…………
            // 按货物类型1~9类及其他匹配，货物类型字段catCd
            let ismatch = false;
            let catCd = item.catCd;
            if (catCd) {
              this.vecTypes.forEach(it => {
                if (it.nmEn) {
                  if (
                    it.nmEn !== "all" &&
                    it.nmEn !== "heavy" &&
                    it.nmEn !== "empty" &&
                    it.nmEn !== "passby"
                  ) {
                    var reg;
                    if (it.nmEn.indexOf("-") >= 0) {
                      // 单独匹配6.1，6.2
                      // reg = new RegExp(
                      //   "(\\[|,)" + it.nmEn.replace("-", "\\.") + "(,|\\])"
                      // );
                      reg = new RegExp(
                        "(^|,)" + it.nmEn.replace("-", "\\.") + "(,|$)"
                      );
                    } else {
                      // reg = new RegExp(
                      //   "(\\[|,)" + it.nmEn + "(,|(\\.\\d+,)|(\\.\\d+\\])|\\])"
                      // );
                      reg = new RegExp(
                        "(^|,)" + it.nmEn + "(,|(\\.\\d+(,|$))|$)"
                      );
                    }
                    let is = reg.test(catCd);
                    if (is) {
                      let checkObj = allCarObj[it.nmEn];
                      ismatch = true;
                      checkObj.total.push(vehicleNo);
                      checkObj[`warningGrade${warningGrade}`].push(vehicleNo);

                      // 离线
                      if (gspIntervalTime >= offlineTime) {
                        checkObj.offline.push(vehicleNo);
                      }
                    }
                  }
                }
              });
            }
            if (!ismatch) {
              // 都不匹配则放到其他类型下
              allCarObj.other.total.push(vehicleNo);
              allCarObj.other[`warningGrade${warningGrade}`].push(vehicleNo);
              // console.log(item.catCd,'其他物质')
              // 离线
              if (gspIntervalTime >= offlineTime) {
                allCarObj.other.offline.push(vehicleNo);
              }
            }
            this.diffVecLoad(item, warningGrade, vehicleNo, allCarObj, index);
            // this.setHeavyList(item);
          }
          _this.$set(_this, "allCarObj", allCarObj);
          _this.displayVecMarkerByType(
            _this.vecTypeShow,
            _this.subvecTypeShow,
            res.length + 10
          );
          _this.refreshFlag = true;
        } else {
          this.$message.error("数据库链接异常，请刷新页面或者联系管理员！");
        }
        this.mapNodeLoading && this.mapNodeLoading.close();
      }).catch(err => {
        this.mapNodeLoading && this.mapNodeLoading.close();
      });
    },
    // carType, 0:空车，1：重车，2：过境车
    displayVecMarkerByType(vecType, vecSubvecType, t = 0) {
      let subvecType = vecSubvecType ? vecSubvecType : this.subvecTypeShow;
      let _this = this;
      let isShowInfoBox = vecType === this.vecTypeShow;
      this.vecTypeShow = vecType;
      // hide all vehicle marker
      this.carsMarkerMap.values().forEach(item => {
        item.hide();
      });
      let all = this.allCarObj[vecType];
      if (subvecType) {
        all = all[subvecType] || [];
        // console.log(`%c${vecType}-${subvecType}`, "color:#d00");
      } else {
        all = all["total"] || [];
        console.log(`%c${vecType}-all`, "color:red");
      }
      all.forEach(vecNo => {
        let marker = _this.carsMarkerMap.get(vecNo);
        if (marker) {
          marker.show();
        }
      });
      setTimeout(() => {
        if (isShowInfoBox) {
          // 刷新后打开之前的infoBox 地图弹窗信息
          if (_this.infoBoxCache) {
            let vehicleNo = _this.selectVecInfo.vehicleNo;
            if (vehicleNo) {
              let markerTemp = _this.carsMarkerMap.get(vehicleNo);
              if (markerTemp && markerTemp.isVisible()) {
                const d = _this.allData.filter(ite => ite.vecNo === vehicleNo);
                if (d.length) {
                  this.openInfoWindow(d[0], markerTemp);
                }
              } else {
                _this.closeInfoWindow();
                console.log("对不起，该车不存在围栏内，可能已经出了围栏");
              }
              markerTemp = null;
            }
          }
        } else if (vecType !== "all" && this.infoBoxCache) {
          this.closeInfoWindow();
        }
      }, t);
    },
    openInfoWindow(item, marker, isOpenDrawer = false) {
      this.$set(this, "currentRiskInfo", JSON.parse(item.riskInfo));
      this.mapNodeLoading && this.mapNodeLoading.close();
      let _this = this,
        vehicleNo = item.vecNo;
      let isLeftSide = this.$refs.enforceDrawer.visible;
      if (isOpenDrawer || isLeftSide) {
        var bs = this.map.getBounds(); //获取可视区域
        var bssw = bs.getSouthWest();
        var bsne = bs.getNorthEast();
        let lng = (bsne.lng - bssw.lng) / 6;
        let lat = (bsne.lat - bssw.lat) / 8;
        let position = marker.getPosition();
        this.map.panTo(new BMap.Point(position.lng + lng, position.lat + lat));
      } else {
        this.map.panTo(marker.getPosition());
      }

      if (this.$refs.enforceDrawer.isVisible) {
        let containSize = this.map.getSize();
        console.log(containSize);
      }
      this.mapNodeLoading = this.getTransLoading();

      // 获取该车上一次电子路单记录
      $httpVec
        .getLastRtePlanByTracCd(vehicleNo)
        .then(res => {
          _this.mapNodeLoading && _this.mapNodeLoading.close();
          if (res.code == 0) {
            let rteplan = res.data;
            _this.$set(_this, "selectVecInfo", item);
            _this.$set(_this.selectVecInfo, "vehicleNo", vehicleNo);
            _this.$set(_this, "rteplan", rteplan);
            let infoBox = new BMapLib.InfoBox(
              _this.map,
              _this.$refs.monitorInfoWindow.$el,
              {
                boxStyle: {
                  width: "340px",
                  marginBottom: rteplan ? "28px" : "28px",
                  marginLeft: "4px"
                },
                closeIconMargin: "-9999px 0 0 0",
                closeIconUrl: imgsConfig.vec.close_btn,
                enableAutoPan: true
                //,align: INFOBOX_AT_TOP
              }
            );
            if (_this.infoBoxCache) {
              _this.closeInfoWindow();
            }
            _this.infoBoxCache = infoBox;
            _this.infoBoxCache.addEventListener("close", function() {
              _this.infoBoxCache = null;
              _this.selectedAlarmPk = null;
            });
            _this.infoBoxCache.open(marker);
            _this.infoBoxCache.show();
            infoBox = null;
          }
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading && _this.mapNodeLoading.close();
        });
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoBoxCache) {
        this.infoBoxCache.close();
        this.infoBoxCache = null;
      }
    },
    vecHeavySite(vec, alarmPk) {
      this.closeInfoWindow();
      this.selectedAlarmPk = alarmPk;
      let marker = this.carsMarkerMap.get(vec);
      if (marker) {
        if (
          !(
            (this.vecTypeShow === "all" || this.vecTypeShow === "heavy") &&
            (this.subvecTypeShow == "total" ||
              this.subvecTypeShow === "warningGrade5")
          )
        ) {
          this.showVecByType("all", "warningGrade5");
        }
        this.$nextTick(() => {
          marker.dispatchEvent("click");
        });
      } else {
        this.$message.error("该车不在区内！");
      }
    },
    // selectHeavy(key) {
    //   this.vecTypeShow = "heavy";
    //   this.subvecTypeShow = key;
    //   if (key === "all") {
    //     this.displayVecMarkerByType("heavy");
    //   } else {
    //     this.displayVecMarkerByType("heavy", key);
    //   }
    // },
    showVecByType(type, subType) {
      this.vecTypeShow = type;
      this.subvecTypeShow = subType ? subType : "total";
      this.displayVecMarkerByType(type, subType);
    },
    // setHeavyList(d) {
    //   if (d.carType !== 1) {
    //     return;
    //   }
    //   this.heavyList.forEach(item => {
    //     if (item.key === d.roadType || item.key === d.roadType2) {
    //       item.num++;
    //       item.value.push(d);
    //     }
    //   });
    // },
    removeVec(vehicleNo) {
      let markerTemp = this.carsMarkerMap.get(vehicleNo);
      if (markerTemp) {
        markerTemp.removeEventListener("click");
        this.map.removeOverlay(markerTemp);
        this.carsMarkerMap.remove(vehicleNo);
      }
    },
    async diffVecLoad(item, warningGrade, vehicleNo, allCarObj, index) {
      const _this = this;
      this.removeVec(vehicleNo);

      let point = new BMap.Point(item.lonBd, item.latBd);
      let catType = item.carType === 2 ? 0 : item.carType; // 单独处理过境车
      // let iconImg = imgsConfig.vec[`${catType}_${warningGrade}`];
      let iconImg = imgsConfig.newVec[`${warningGrade}`];
      // if (warningGrade === 5) {
      //   iconImg = imgsConfig.vec[`1_4`];
      // }
      // 判断是否违停时间超过最高级别（重车超过30分钟，空车超过60分钟）
      if (item.warningTextShort && item.warningTextShort.indexOf("违停") >= 0) {
        // carType：0空车 1重车 2过境车
        if (item.carType === 1) {
          // 如果重车超过30分钟
          iconImg = imgsConfig.vec[`1_5`];
        } else {
          // 空车|过境车，超过60分钟
          iconImg = imgsConfig.vec[`0_5`];
        }
      }

      // if (item.carType == 1) {
      //   const tagMap = {
      //     "10": 2,
      //     "5": 1
      //   };
      //   iconImg = imgsConfig.vec[`heavy_${tagMap[item.warningGrade]}`];
      //   item.roadType2 = (tagMap[item.warningGrade] || 0) + 10;
      //   if (allCarObj[`heavy_${item.roadType}`]) {
      //     allCarObj[`heavy_${item.roadType}`].push(vehicleNo);
      //   } else {
      //     allCarObj[`heavy_${item.roadType}`] = [vehicleNo];
      //   }
      //   if (allCarObj[`heavy_${item.roadType2}`]) {
      //     allCarObj[`heavy_${item.roadType2}`].push(vehicleNo);
      //   } else {
      //     allCarObj[`heavy_${item.roadType2}`] = [vehicleNo];
      //   }
      //   const workTypeMap = {
      //     5: 96,
      //     10: 95,
      //     15: 97,
      //     20: 98
      //   };
      //   if (item.workType) {
      //     const firstworkType = item.workType;
      //     let workTypeKey = `heavy_${workTypeMap[firstworkType]}`;
      //     const heavyList = this.heavyList.find(
      //       ite => ite.key === workTypeMap[firstworkType]
      //     );
      //     heavyList.value.push(item);
      //     heavyList.num++;
      //     if (allCarObj[workTypeKey]) {
      //       allCarObj[workTypeKey].push(vehicleNo);
      //     } else {
      //       allCarObj[workTypeKey] = [vehicleNo];
      //     }
      //   }
      // }
      if (!iconImg) {
        console.log(
          `%c车辆图标不存在...${vehicleNo}，icon：${catType}_${warningGrade}`,
          "color:red"
        );
        iconImg = imgsConfig.vec.offline;
        // let currentTime = new Date().getTime();
        // let gspIntervalTime = item.gpsTime
        //   ? currentTime - new Date(item.gpsTime).getTime()
        //   : 9999999; // gps距离现在的时间间隔

        // // 离线（大于10分钟）
        // if (gspIntervalTime >= offlineTime) {
        //   iconImg = imgsConfig.vec.offline;
        // } else {
        //   iconImg = imgsConfig.vec.star;
        // }
      }
      let icon = new BMap.Icon(iconImg, new BMap.Size(24, 24));
      let marker;
      if (!iconImg) {
        marker = new BMap.Marker(point);
      } else {
        marker = new BMap.Marker(point, { icon: icon });
        item.direction && marker.setRotation(item.direction);
      }
      this.carsMarkerMap.put(vehicleNo, marker);

      // const warnings = (item.warningType || "").split(",");
      // let t = 0;
      // if (warnings.includes("5")) {
      //   t = dayjs().valueOf() - dayjs(item.updateTime).valueOf();
      // }
      // if (warnings.includes("10")) {
      //   const tempT = dayjs().valueOf() - dayjs(item.stopTime).valueOf();
      //   t = tempT > t ? tempT : t;
      // }
      // let markerState = 0;
      // if (t > 0) {
      //   let subvecTypeShow;
      //   const mins = t / 1000 / 60;
      //   if (mins > 45 && mins < 120) {
      //     markerState = false;
      //     subvecTypeShow = 91;
      //   } else if (mins >= 120) {
      //     markerState = true;
      //     subvecTypeShow = 92;
      //   }
      //   if (item.carType == 1) {
      //     const heavyList = this.heavyList.find(ite => ite.key === subvecTypeShow);
      //     heavyList.value.push(item);
      //     heavyList.num++;
      //     if (allCarObj[`heavy_${subvecTypeShow}`]) {
      //       allCarObj[`heavy_${subvecTypeShow}`].push(vehicleNo);
      //     } else {
      //       allCarObj[`heavy_${subvecTypeShow}`] = [vehicleNo];
      //     }
      //   }
      // }

      await this.sleep(index);
      this.map.addOverlay(marker);

      // if (markerState !== 0) {
      //   this.createDot(marker, markerState);
      // }
      if (warningGrade === 5) {
        this.createDot(marker, true);
      }

      marker.addEventListener("click", function() {
        _this.openInfoWindow(item, this, true);
        _this.$refs.enforceDrawer.open(item);
      });
    },
    async sleep(t) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(true);
        }, t);
      });
    },
    createDot(marker, f = false) {
      let divElement = document.createElement("div");
      divElement.className = "before";
      let divElement2 = document.createElement("div");
      divElement2.className = "after";
      // console.log(marker.Dc);
      marker.ca.innerHtml = marker.ca.childNodes[0];
      marker.ca.appendChild(divElement);
      marker.ca.appendChild(divElement2);
      marker.ca.className = "";
      marker.ca.className = f ? "dot dotf" : "dot";
      marker.ca.style.overflow = "";
      marker.ca.firstChild.style.position = "relative";
      marker.ca.firstChild.style.zIndex = "5";
    },

    // 异常停车处置
    showEnforceDrawer() {
      // this.$refs.enForceOnLineDialog.init({
      //   rteplan: this.rteplan,
      //   selectVecInfo: this.selectVecInfo,
      //   alarmInfo: this.alarmInfo
      // });
    }
  }
};
</script>
<style lang="scss" scoped>
.app-main-content {
  position: relative;
  margin: 0;
  padding: 0;
  width: 100%;
  height: calc(100vh - 50px);
  border: 0;
  border-radius: 0;
}
.vec-total-wape {
  z-index: 2;
  position: absolute;
  bottom: 4px;
  z-index: 1;
  margin: 0 auto;
  padding: 0px;
  text-align: center;
  padding: 5px 0;
  display: grid;
  width: calc(100vw - 600px);
  left: 300px;
  margin: 0 auto;
  grid-gap: 6px 4px;
  grid-template-columns: repeat(7, auto);
  justify-items: normal;

  /deep/ > .el-button:first-child {
    grid-row-start: 1;
    grid-row-end: 3;
  }
}
.vec-total-btn {
  margin: 0;
  box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
  line-height: 28px;
  // padding: 2px 10px;
  padding: 0px 7px;
  font-size: 14px;
  border: 1px solid #449d44;
  cursor: pointer;
  font-weight: bold;
  color: #449d44;
  border-radius: 3px;

  &.active {
    background-color: #449d44;
    border: 1px solid #449d44;
    color: #fff;
  }
}
/deep/ .vec-total-btn {
  & + .vec-total-btn {
    margin-left: 5px;
  }
}
.refresh-tooltip {
  position: absolute;
  z-index: 10;
  right: 10px;
  top: 50px;
  width: 80px;
  border-radius: 5px;
  // background-color: #3f7bce;
  background-color: rgba(28, 91, 250, 0.7);
  margin: 0 0 5px 20px;
  cursor: pointer;
  text-align: center;
  color: #f8f8f9;
  -webkit-box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25);
  vertical-align: middle;
  border: 0;
  padding: 3px 6px;
  font: bold 12px/1.3em arial, sans-serif;
}
.tipContent {
  color: #333;
  line-height: 20px;
}

.inner-btn {
  display: block;
  border-radius: 0;
  margin: 0;
  width: 100%;
  border-color: #449d44;
  color: #449d44;
  font-weight: 800;
  &:nth-child(1) {
    border-radius: 5px 5px 0 0;
  }
  &:nth-last-child(1) {
    border-radius: 0 0 5px 5px;
  }
  &.active {
    background: #449d44;
    color: #fff;
  }
}
</style>

<style lang="scss">
.dot {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 50;
}
.dot:hover {
  z-index: 100;
}

/* 内环  */
.dot > .before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 0;
  left: 0;
  border: 2px solid orange;
  border-radius: 100%;
  background-color: orange;
  z-index: 2;
  animation: color3 2s ease-out;
  animation-iteration-count: infinite;
}
/* 产生动画（向外扩散变大）的圆圈  */
.dot > .after {
  content: "";
  position: absolute;
  width: 40px;
  height: 40px;
  left: -10px;
  top: -10px;
  border: 3px solid orange;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  z-index: 1;
  opacity: 0;
  animation: color4 2s ease-out;
  animation-iteration-count: infinite;
}

.dotf > .before {
  animation: color1 2s ease-out;
  animation-iteration-count: infinite;
  border: 2px solid #ee0000;
  background-color: #ee0000;
}
/* 产生动画（向外扩散变大）的圆圈  */
.dotf > .after {
  animation: color2 2s ease-out;
  animation-iteration-count: infinite;
  border: 1px solid #ee0000;
}
// 动画效果
@keyframes color1 {
  0% {
    transform: scale(0.1);
    opacity: 0.1;
    border-color: #ee0000;
  }
  30% {
    transform: scale(1);
    opacity: 0.3;
    border-color: #ee0000;
  }
  60% {
    transform: scale(1.6);
    opacity: 0.4;
    border-color: rgba(238, 0, 0, 0.8);
  }
  100% {
    transform: scale(2);
    opacity: 0;
    border-color: rgba(238, 0, 0, 0.1);
  }
}
@keyframes color2 {
  0% {
    transform: scale(0.1);
    opacity: 0;
    border-color: #ee0000;
  }
  30% {
    transform: scale(0.6);
    opacity: 0;
    border-color: #ee0000;
  }
  55% {
    transform: scale(0.6);
    opacity: 0.1;
    border-color: rgba(238, 0, 0, 0.8);
  }
  80% {
    transform: scale(1);
    opacity: 0.1;
    border-color: rgba(238, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes color3 {
  0% {
    transform: scale(0.1);
    opacity: 0.1;
    border-color: orange;
  }
  30% {
    transform: scale(1);
    opacity: 0.3;
    border-color: orange;
  }
  60% {
    transform: scale(1.6);
    opacity: 0.4;
    border-color: orange;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}
@keyframes color4 {
  0% {
    transform: scale(0.1);
    opacity: 0;
  }
  30% {
    transform: scale(0.6);
    opacity: 0;
    border-color: orange;
  }
  55% {
    transform: scale(0.6);
    opacity: 0.1;
    border-color: orange;
  }
  80% {
    transform: scale(1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.dot > div {
  z-index: 3;
  top: 0;
  left: 0;
}

.sp {
  background: #ddd;
}
</style>
