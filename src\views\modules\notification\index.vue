<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <template slot="button">
        <el-button size="small" type="success" @click="add()">新增</el-button>
      </template>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" highlight-current-row border style="width: 100%" ref="singleTable" v-loading="listLoading"
      :height="tableHeight" :data="list" @sort-change="handleSort">
      <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
      <el-table-column width="220" prop="title" label="标题"></el-table-column>
      <!-- <el-table-column prop="classify" width="100" label="企业类型" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.classify === 0">化工企业</span>
          <span v-if="scope.row.classify === 1">运输企业</span>
        </template>
      </el-table-column> -->
      <el-table-column show-overflow-tooltip prop="noticeCont" label="通报内容">
        <template slot-scope="scope">
          <div class="descr" ref="imgHandelSecond">
            <span @click="showImageSecond('imgHandelSecond')" v-html="scope.row.noticeCont"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="150" prop="entpNms" label="通报对象">
        <template slot-scope="scope">
          <el-tooltip content="点击查看详情" placement="top">
            <!-- style="
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              " -->
            <div>
              <!-- <span style="cursor: pointer" @click="showDetail(scope.row)">{{
                scope.row.entpNms
              }}</span> -->
              <span style="cursor: pointer" @click="showDetail(scope.row)">通报对象一共{{ scope.row.entpNms.split(",").length >
                0 ? scope.row.entpNms.split(",").length : 0 }}家</span>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column
        width="180"
        prop="crtTm"
        label="创建时间"
      ></el-table-column> -->
      <el-table-column width="180" prop="noticeTm" label="生效时间"></el-table-column>
      <el-table-column prop="typeCd" width="100" label="类型" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.typeCd == 'notice.01'">通知</span>
          <span v-if="scope.row.typeCd == 'notice.02'">通报</span>
        </template>
      </el-table-column>
      <el-table-column width="180" prop="type" label="是否需要回函" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.type == 0">不需回函</span>
          <span v-if="scope.row.type == 1">需回函</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" align="center" prop="action" width="200" label="操作">
        <template slot-scope="scope">
          <!--          <el-button @click="editRow(scope.row)" type="primary"  size="mini">编辑</el-button>-->
          <el-button @click="deleteRow(scope.row)" type="danger" size="mini">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="pagination-wrapper">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange"></el-pagination>
    </div>
    <el-dialog top="5vh" :title="title" :close-on-click-modal="false" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
        <el-form-item label="标题" prop="title">
          <el-input style="width: 400px" v-model="dataForm.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <!-- <el-form-item label="企业类型" prop="classify">
          <el-select style="width: 400px" v-model="dataForm.classify" placeholder="请选择" @change="submitClassify">
            <el-option v-for="item in classifyOption" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="对象" prop="entp">
          <el-select style="width: 400px" multiple collapse-tags filterable v-model="dataForm.entp" @change="changeSelect"
            @remove-tag="removeTag" placeholder="请选择">
            <el-checkbox style="padding-left: 20px; width: 100%" v-model="checked" @change="selectAll">全选</el-checkbox>
            <el-option v-for="item in entpList" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生效时间" prop="noticeTm">
          <el-date-picker style="width: 400px" v-model="dataForm.noticeTm" type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
        </el-form-item>
        <el-form-item label="类型" prop="typeCd">
          <el-select style="width: 400px" v-model="dataForm.typeCd" placeholder="请选择">
            <el-option v-for="item in typeCdList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否需要回复" prop="type">
          <el-radio-group v-model="dataForm.type">
            <el-radio :label="0">不需要</el-radio>
            <el-radio :label="1">需要</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通报内容" prop="noticeCont">
          <wangeditor ref="wangeditor" v-model="dataForm.noticeCont" placeholder="请输入内容"></wangeditor>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog top="5vh" title="企业详情" width="600px" height="70vh" :close-on-click-modal="false"
      :visible.sync="entpVisible">
      <el-table class="el-table" highlight-current-row border style="width: 100%" :max-height="400" :data="replyList">
        <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
        <el-table-column prop="entpNm" label="企业名称"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import searchbar from "@/components/searchbar";
import * as $http from "@/api/notication";
import wangeditor from "@/components/editor/wangeditor";
import * as Tool from "@/utils/tool";
import Viewer from "viewerjs";
export default {
  name: "NoticationList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 278,
      list: [],
      entpList: [],
      cpList: [],
      // transportEntpList: [],
      replyList: [],
      typeCdList: [],
      // classifyOption: [
      //   { label: "运输企业", value: 1 },
      //   { label: "化工企业", value: 0 },
      // ],
      checked: false,
      listLoading: false,
      title: "新增",
      visible: false,
      entpVisible: false,
      searchItems: {
        normal: [
          {
            name: "标题",
            field: "title",
            type: "text",
            dbfield: "title",
            dboper: "cn",
          },
          {
            name: "类型",
            field: "typeCd",
            type: "select",
            options: [{ label: "全部", value: "" }],
            dbfield: "type_cd",
            dboper: "cn",
            default: "",
          },
          // {
          //   name: "企业类型",
          //   field: "classify",
          //   type: "select",
          //   options: [
          //     { label: "全部", value: "" },
          //     { label: "化工企业", value: 0 },
          //     { label: "运输企业", value: 1 },
          //   ],
          //   dbfield: "classify",
          //   dboper: "eq",
          //   default: "",
          // },
          {
            name: "是否需要回函",
            field: "type",
            type: "select",
            options: [
              { label: "全部", value: "" },
              { label: "不需回函", value: "0" },
              { label: "需回函", value: 1 },
            ],
            dbfield: "type",
            dboper: "cn",
            default: "",
          },
          {
            name: "通报对象",
            field: "entpNms",
            type: "text",
            dbfield: "entp_nms",
            dboper: "cn",
          },

        ],
        more: [{
          name: "生效时间",
          field: "noticeTm",
          type: "date",
          dbfield: "notice_tm",
          dboper: "cn",
          valueFormat: "yyyy-MM-dd",
        },],
      },
      pagination: {
        total: 0,
        page: 3,
        limit: 20,
      },
      dataForm: {
        id: -1,
        title: "",
        entp: [],
        noticeCont: "",
        noticeTm: "",
        type: 0,
        typeCd: "",
        entpNms: "",
        entpIds: "",
        classify: 1,
      },
      dataRule: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        entp: [{ required: true, message: "企业对象不能为空", trigger: "change" }],
        // classify: [{ required: true, message: "企业类型不能为空", trigger: "change" }],
        typeCd: [{ required: true, message: "类型不能为空", trigger: "change" }],
        noticeCont: [{ required: true, message: "内容不能为空", trigger: "blur" }],
        noticeTm: [{ required: true, message: "生效时间不能为空", trigger: "change" }],
      },
    };
  },
  components: {
    searchbar,
    wangeditor,
  },
  created() {
    $http.getTypeCdList().then(({ data: res }) => {
      if (res.code == 0) {
        this.typeCdList = res.data.map(item => {
          return { label: item.nmCn, value: item.cd };
        });
        this.searchItems.normal[1].options = [...this.searchItems.normal[1].options, ...this.typeCdList];
        // console.log(this.typeCdList);
      } else {
        this.typeCdList = [];
      }
      // console.log(res)
    });
    //获取运输企业列表
    this.getEntpList();
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    setTimeout(function () {
      _this.setTableHeight();
    }, 1000);

    // this.getList();
    //化工企业列表
    // $http
    //   .getEntpList()
    //   .then(({ data: response }) => {
    //     if (response.code == 0) {
    //       _this.cpList = response.data;
    //       _this.entpList = _this.cpList;
    //     }
    //   })
    //   .catch(error => {
    //     console.log(error);
    //   });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //获取运输企业列表
    getEntpList() {
      $http.getTransportEntpList()
        .then(({ data: response }) => {
          if (response.code === 0) {
            this.entpList = response.data;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //切换企业类型
    // submitClassify(e) {
    //   if (e) {
    //     this.entpList = this.transportEntpList;
    //   } else {
    //     this.entpList = this.cpList;
    //   }
    // },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 175 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名
      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },

    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      sortParam = sortParam || {};
      this.listLoading = true;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        // if (data.searchData) {
        filters = data;
        // }
      } else {
        filters = this.$refs.searchbar.get();
      }

      let param = Object.assign({}, sortParam, { filters: filters }, this.pagination);
      delete param.total;

      this.listLoading = true;
      $http
        .getList(param)
        .then(({ data: response }) => {
          if (response.code == 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getList();
    },
    selectAll() {
      this.dataForm.entp = [];
      if (this.checked) {
        this.entpList.map(item => {
          this.dataForm.entp.push(item.value);
        });
      } else {
        this.dataForm.entp = [];
      }
    },
    changeSelect(val) {
      if (val.length === this.entpList.length) {
        this.checked = true;
      } else {
        this.checked = false;
      }
    },
    removeTag() { },
    add() {
      this.title = "新增";
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.$refs["wangeditor"].setContent("");
      });
      // this.dataForm.id = -1;
    },
    editRow(item) {
      this.title = "编辑";
      this.visible = true;
      this.$nextTick(() => {
        this.dataForm.id = item.id;
        this.dataForm.title = item.title;
        if (item.entpIds) {
          this.dataForm.entp = item.entpIds.split(",").map(item => {
            return String(item);
          });
        } else {
          this.dataForm.entp = [];
        }
        this.dataForm.entpNms = item.entpNms;
        this.dataForm.entpIds = item.entpIds;
        this.dataForm.type = item.type;
        this.dataForm.typeCd = item.typeCd;
        this.dataForm.noticeTm = item.noticeTm;
        this.$refs["wangeditor"].setContent(item.noticeCont);
        if (this.dataForm.entp.length == this.entpList.length) {
          this.checked = true;
        } else {
          this.checked = false;
        }
      });
    },
    deleteRow(item) {
      let _this = this;
      this.$confirm(`确定删除吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .del(item.id)
          .then(({ data: response }) => {
            if (response && response.code === 0) {
              _this.$message({
                message: "删除成功",
                type: "success",
                duration: 200,
                onClose: () => {
                  _this.getList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    // 提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          let arr = []; //拿到企业对象的所有
          if (this.dataForm.entp.length == this.entpList.length) {
            arr = this.entpList;
          } else {
            for (let i = 0; i < this.dataForm.entp.length; i++) {
              for (let j = 0; j < this.entpList.length; j++) {
                if (this.dataForm.entp[i] == this.entpList[j].value) {
                  arr.push(this.entpList[j]);
                  break;
                }
              }
            }
          }
          this.dataForm.entpNms = arr
            .map(item => {
              return item.name;
            })
            .join(",");
          this.dataForm.entpIds = arr
            .map(item => {
              return item.value;
            })
            .join(",");
          //   delete this.dataForm.entp;
          // console.log(this.dataForm);
          // this.dataForm.entp = '1468'
          if (this.dataForm.id == -1) {
            $http.add(this.dataForm).then(({ data: res }) => {
              if (res && res.code === 0) {
                this.$message.success("新增成功");
                this.visible = false;
                this.getList();
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            $http.edit(this.dataForm).then(({ data: res }) => {
              if (res && res.code === 0) {
                this.$message.success("编辑成功");
                this.visible = false;
                this.getList();
              } else {
                this.$message.error(res.msg);
              }
            });
          }
        }
      });
    },
    showDetail(item) {
      if (item.type == 0) {
        this.replyList = item.entpNms.split(",").map(item => {
          return { entpNm: item };
        });
        this.entpVisible = true;
      } else if (item.type == 1) {
        this.$router.push({
          path: "/notification/info/" + item.id,
        });
      }
    },
    showImageSecond() {
      var viewer = new Viewer(document.body, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas = viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft = parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
/*/deep/ .el-table__row p {
  margin: 0;
}
/deep/ .el-dialog__body {
  padding: 30px 20px !important;
  // margin-bottom: 15px;
}*/
.descr>>>img {
  width: 60px;
  vertical-align: middle;
  margin: 0 10px;
}
</style>
<style scoped>
.descr>>>img {
  width: 60px;
  vertical-align: middle;
  margin: 0 10px;
}
</style>
