<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getInfo" class="grid-search-bar"></searchbar>
    <!--列表-->
    <p><span style="margin-right:20px;">待回复企业数：{{ oneEntp }}</span><span style="margin-right:20px;">已回复企业数：{{ twoEntp
    }}</span><span>已完结企业数：{{ fiveEntp }}</span></p>
    <el-table class="el-table" highlight-current-row border style="width: 100%" ref="singleTable" v-loading="listLoading"
      :max-height="tableHeight" :data="list">
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="entpNm" label="企业名称"> </el-table-column>
      <el-table-column align="center" width="120" prop="status" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 0 || scope.row.status == 1">待回复</span>
          <span v-if="scope.row.status == 2">已回复</span>
          <span v-if="scope.row.status == 5">已完结</span>
        </template>
      </el-table-column>
      <el-table-column prop="respTm" label="回函时间"></el-table-column>
      <el-table-column width="180" align="center" prop="respAudit" label="回函内容">
        <template slot-scope="scope">
          <div v-if="scope.row.respAudit">
            <el-button @click="showContent(scope.row)" type="success" size="mini">查看详情</el-button>
            <!-- <p
              v-for="item in JSON.parse(scope.row.respAudit)"
              :key="item.content"
              v-html="item.content"
            ></p> -->
          </div>
          <span v-else>暂无回函内容</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" prop="action" width="180" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == 5" @click="unfinish(scope.row)" type="success" size="mini">取消完结</el-button>
          <el-button v-if="scope.row.status != 5" @click="reply(scope.row)" type="success" size="mini">回复</el-button>
          <el-button v-if="scope.row.status != 5" @click="finish(scope.row)" type="success" size="mini">完结</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page" :total="pagination.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
    <el-dialog title="回函内容详情" top="5vh" append-to-body :close-on-click-modal="false" :visible.sync="visible" width="60%">
      <div style="height: 60vh; overflow-y: auto" class="detail-container aralmInfo">
        <div class="descr" ref="imgHandelSecond">
          <div class="handleDescSecond" v-for="item in contentArr" :key="item.time">
            <div>名称：{{ item.name }}</div>
            <div>回函时间：{{ item.time }}</div>
            <div>
              回函内容：<span v-html="item.content" @click="showImage()"></span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="内容回复" top="5vh" append-to-body :close-on-click-modal="false" :visible.sync="replyVisible"
      width="60%">
      <wangeditor ref="wangeditor" v-model="wangeditorContent" placeholder="请输入内容"></wangeditor>
      <span slot="footer" class="dialog-footer">
        <el-button @click="replyVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSent()">发送</el-button>
      </span>
    </el-dialog>
    <!-- <viewer :images="[imgSrc]" v-if="imgVisible">
      <img :key="src" :src="imgSrc">
    </viewer> -->
  </div>
</template>

<script>
import * as $http from "@/api/notication";
import * as Tool from "@/utils/tool";
import wangeditor from "@/components/editor/wangeditor";
import searchbar from "@/components/searchbar";
import Viewer from "viewerjs";
export default {
  name: "NoticationList",
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 230,
      list: [],
      entpList: [],
      checked: false,
      listLoading: false,
      title: "新增",
      replyId: 0,
      replyVisible: false,
      visible: false,
      defaultSearchItems: [
        { field: "notice_id", op: "eq", data: this.$route.params.id || "" },
      ],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      id: -1,
      contentArr: [],
      searchItems: {
        normal: [
          {
            name: "企业名称",
            field: "entpNm",
            type: "text",
            dbfield: "entp_nm",
            dboper: "cn",
          },
          {
            name: "状态",
            field: "status",
            type: "select",
            options: [
              { label: "全部", value: "" },
              { label: "待回复", value: "0" },
              { label: "已回复", value: 2 },
              { label: "已完结", value: 5 },
            ],
            dbfield: "status",
            dboper: "cn",
            default: "",
          },
          {
            name: "回函时间",
            field: "respTm",
            type: "date",
            dbfield: "resp_tm",
            dboper: "cn",
            valueFormat: "yyyy-MM-dd",
          },
        ],
        more: [],
      },
      oneEntp: 0,
      twoEntp: 0,
      fiveEntp: 0,
      imgVisible: false,
      imgSrc: ''
    };
  },
  components: { searchbar, wangeditor },
  created() { },
  mounted() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);
    this.id = this.$route.params.id;
    if (this.id) {
      let query = this.$route.query;
      this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
      this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
      this.pagination.total = this.pagination.page * this.pagination.limit;
      this.$refs.searchbar.init(query);
      this.setTableHeight();
      this.getInfo();
    } else {
      this.$message.error("对不起，页面数据无法查询");
    }
    setTimeout(function () {
      _this.setTableHeight();
    }, 1000);
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210 - this.$refs.searchbar.$el.offsetHeight;
        // Tool.getClientHeight() - 190;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getInfo();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getInfo();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序

    // 获取数据
    getInfo() {
      let _this = this;
      let filters = this.$refs.searchbar.get();
      let param = Object.assign({}, { filters: filters }, this.pagination);
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      delete param.total;
      this.listLoading = true;
      $http
        .detail(param)
        .then(({ data: response }) => {
          if (response.code == 0) {
            this.oneEntp = response.notReplay || 0
            this.twoEntp = response.replay || 0
            this.fiveEntp = response.finish || 0
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          _this.listLoading = false;
        });
    },
    // 修改审核状态
    refreshGrid: function () {
      this.pagination.page = 1;
      this.getInfo();
    },
    finish(item) {
      let _this = this;
      this.$confirm(`确定完结该条记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .finish({ id: item.id })
          .then(({ data: response }) => {
            if (response && response.code === 0) {
              _this.$message({
                message: "完结成功",
                type: "success",
                duration: 200,
                onClose: () => {
                  _this.getInfo(_this.id);
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      });
    },
    handleSent() {
      let _this = this;
      _this
        .$confirm("是否发送?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "success",
        })
        .then(() => {
          let param = {
            id: this.replyId,
            respCont: this.$refs["wangeditor"].getContent()
          }
          $http.sendMessage(param).then((res) => {
            if (res.data.code === 0) {
              this.$message({
                type: "success",
                message: "发送成功!",
              });
              this.replyVisible = false
              this.$refs["wangeditor"].setContent("");
              this.getInfo()
            } else {
              this.$message.error("发送失败");
            }
          });
        })
        .catch(() => { });
    },
    reply(item) {
      this.replyId = item.id
      this.replyVisible = true
    },
    unfinish(item) {
      let _this = this;
      this.$confirm(`确定取消完结该条记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .unfinish({ id: item.id })
          .then(({ data: response }) => {
            if (response && response.code === 0) {
              _this.$message({
                message: "取消完结成功",
                type: "success",
                duration: 200,
                onClose: () => {
                  _this.getInfo(_this.id);
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      });
    },
    showContent(item) {
      this.visible = true;
      this.contentArr = JSON.parse(item.respAudit);
    },
    showImage() {

      // this.imgSrc = $event.target.currentSrc
      // if (this.imgSrc) this.imgVisible = true
      var viewer = new Viewer(document.body, {
        zIndex: 2099,
        url(image) {
          return image.src.replace(/\@\w+\.src$/, "");
        },
        ready() {
          viewer.viewer.className += " custom-lic-viewer-container-right";
        },
        viewed() {
          const viewCanvas =
            viewer.viewer.getElementsByClassName("viewer-canvas");
          if (viewCanvas.length > 0) {
            const imgTags = viewCanvas[0].getElementsByTagName("img");
            if (imgTags.length > 0) {
              imgTags[0].style.marginLeft =
                parseFloat(imgTags[0].style.marginLeft) + "px";
            }
          }
        },
        hidden() {
          viewer.destroy();
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
/*/deep/ .el-table__row p {
  margin: 0;
}*/
.descr {
  margin-bottom: 10px;
  // color: #fff;
  line-height: 20px;
}
</style>
<style scoped>
.handleDescSecond {
  margin-top: 10px;
  border-bottom: 1px dashed #31618c;
  padding-top: 10px;
}

.handleDescSecond>>>img {
  width: 60px;
  vertical-align: middle;
  margin: 0 10px;
}
</style>
