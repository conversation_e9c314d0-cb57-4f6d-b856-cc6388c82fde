<template>
	<div class="detail-container chart-container" >
		<el-row :gutter="10">
			<el-col :sm="7" v-bind:style="{'height':contHeight+'px','overflow-y':'auto'}">
				<!-- 基本信息统计 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>基本信息统计</span>
					</div>
					<el-row>
						<el-col :sm="12">
							<!-- 备案挂车 -->
							<pie-comp :id="leftPie1.id" ref="leftPie1" :styles='leftPie1.styles'></pie-comp>
						</el-col>
						<el-col :sm="12">
							<!-- 符合资质挂车 -->
							<pie-comp :id="leftPie2.id" ref="leftPie2" :styles='leftPie2.styles'></pie-comp>
						</el-col>
					</el-row>
					<el-row>
						<el-col :sm="8">
							<!-- 运输企业 -->
							<pie-comp :id="leftPie3.id" ref="leftPie3" :styles='leftPie3.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 符合资质企业 -->
							<pie-comp :id="leftPie4.id" ref="leftPie4" :styles='leftPie4.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 区内装卸企业 -->
							<pie-comp :id="leftPie5.id" ref="leftPie5" :styles='leftPie5.styles'></pie-comp>
						</el-col>
					</el-row>
					<el-row>
						<el-col :sm="8">
							<!-- 备案牵引车 -->
							<pie-comp :id="leftPie6.id" ref="leftPie6" :styles='leftPie6.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 符合资质牵引车 -->
							<pie-comp :id="leftPie7.id" ref="leftPie7" :styles='leftPie7.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 备案GPS车辆 -->
							<pie-comp :id="leftPie8.id" ref="leftPie8" :styles='leftPie8.styles'></pie-comp>
						</el-col>
					</el-row>
				</el-card>
				<!-- //人员年龄分布 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>人员年龄分布</span>
					</div>
					<pie-comp :id="staffAgeStatChart.id" ref="staffAgeStatChart" :styles='staffAgeStatChart.styles'></pie-comp>
				</el-card>
				<!-- //人员区域分布 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>人员区域分布</span>
					</div>
					<pie-comp :id="staffDistribution.id" ref="staffDistribution" :styles='staffDistribution.styles'></pie-comp>
				</el-card>
			</el-col>
			<el-col :sm="10">
				<!-- 车辆区域分布 -->
				<el-card style="background:none;border:none;">
					<map-comp :id="map.id" ref="map" :styles='map.styles'></map-comp>
				</el-card>
			</el-col>
			<el-col :sm="7" v-bind:style="{'height':contHeight+'px','overflow-y':'auto'}">
				<!-- 人员备案信息 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>人员登记信息</span>
					</div>
					<el-row>
						<el-col :sm="12">
							<!-- 备案常压罐 -->
							<pie-comp :id="rightPie1.id" ref="rightPie1" :styles='rightPie1.styles'></pie-comp>
						</el-col>
						<el-col :sm="12">
							<!-- 备案压力罐 -->
							<pie-comp :id="rightPie2.id" ref="rightPie2" :styles='rightPie2.styles'></pie-comp>
						</el-col>
					</el-row>
					<el-row>
						<el-col :sm="8">
							<!-- 备案驾驶员 -->
							<pie-comp :id="rightPie3.id" ref="rightPie3" :styles='rightPie3.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 符合资质驾驶员 -->
							<pie-comp :id="rightPie4.id" ref="rightPie4" :styles='rightPie4.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 备案押运员 -->
							<pie-comp :id="rightPie5.id" ref="rightPie5" :styles='rightPie5.styles'></pie-comp>
						</el-col>
					</el-row>
					<el-row>
						<el-col :sm="8">
							<!-- 符合资质押运员 -->
							<pie-comp :id="rightPie6.id" ref="rightPie6" :styles='rightPie6.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 备案双证人员 -->
							<pie-comp :id="rightPie7.id" ref="rightPie7" :styles='rightPie7.styles'></pie-comp>
						</el-col>
						<el-col :sm="8">
							<!-- 符合资质双证员 -->
							<pie-comp :id="rightPie8.id" ref="rightPie8" :styles='rightPie8.styles'></pie-comp>
						</el-col>
					</el-row>
				</el-card>
				<!-- 人员换岗次数情况统计 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>人员换岗次数情况</span>
					</div>
					<bar-comp :id="personnelFlow.id" ref="personnelFlow" :styles='personnelFlow.styles'></bar-comp>
				</el-card>
				<!-- 牵引车类型 -->
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>牵引车类型</span>
					</div>
					<pie-comp :id="unloadStructureChartRight.id" ref="unloadStructureChartRight" :styles='unloadStructureChartRight.styles'></pie-comp>
				</el-card>
			</el-col>
		</el-row>

	</div>
</template>
<script>
	import * as Tool from "@/utils/tool"
	import PieComp from "@/views/modules/report/charts/pie-comp"
	import BarComp from "@/views/modules/report/charts/bar-comp"
	import MapComp from "@/views/modules/report/charts/map-comp"
	import * as $http from "@/api/stati"
	export default {
		name:'basicSta',
		components:{
			PieComp,
			BarComp,
			MapComp
		},
		mounted() {
			let _this = this;
			let contHeight = Tool.getClientHeight();
			this.contHeight = contHeight-80;

			//基本信息统计
			this.getBasicData(function(result){
				let gczs = result["挂车总数"];
				let zzgc = result["审核通过挂车总数"];
				let ysqyzs  = result["运输企业总数"];
				let zzqy = result["审核通过运输企业总数"];
				let qnzxqy = result["装卸企业总数"];
				let baqyc = result["牵引车总数"];
				let zzqyc = result["审核通过牵引车总数"];
				let gpscl = result["有GPS牵引车总数"];
				let bacyg = result["常压罐总数"];
				let ylg = result["压力罐总数"];
				let bajsy = result["驾驶员总数"];
				let zzjsy = result["审核通过驾驶员总数"];
				let bayyy = result["押运员总数"];
				let zzyyy = result["审核通过押运员总数"];
				let zzszry = result["审核通过双证总数"];
				let baszry = result["双证总数"];


				//备案挂车
				_this.leftPie1.options = Tool.extendObj(true, _this.leftPie1.options, {series:[{data:[{value:gczs,name:gczs}]}]});
				_this.$refs.leftPie1.setInstanceOption(_this.leftPie1.options);

				//符合资质挂车
				_this.leftPie2.options = Tool.extendObj(true, _this.leftPie2.options, {series:[{data:[{value:zzgc,name:zzgc}]}]});
				_this.$refs.leftPie2.setInstanceOption(_this.leftPie2.options);

				//运输企业
				_this.leftPie3.options = Tool.extendObj(true, _this.leftPie3.options, {series:[{data:[{value:ysqyzs,name:ysqyzs}]}]});
				_this.$refs.leftPie3.setInstanceOption(_this.leftPie3.options);

				//符合资质企业
				_this.leftPie4.options = Tool.extendObj(true, _this.leftPie4.options, {series:[{data:[{value:zzqy,name:zzqy}]}]});
				_this.$refs.leftPie4.setInstanceOption(_this.leftPie4.options);

				//区内装卸企业
				_this.leftPie5.options = Tool.extendObj(true, _this.leftPie5.options, {series:[{data:[{value:qnzxqy,name:qnzxqy}]}]});
				_this.$refs.leftPie5.setInstanceOption(_this.leftPie5.options);

				//备案牵引车
				_this.leftPie6.options = Tool.extendObj(true, _this.leftPie6.options, {series:[{data:[{value:baqyc,name:baqyc}]}]});
				_this.$refs.leftPie6.setInstanceOption(_this.leftPie6.options);

				//符合资质牵引车
				_this.leftPie7.options = Tool.extendObj(true, _this.leftPie7.options, {series:[{data:[{value:zzqyc,name:zzqyc}]}]});
				_this.$refs.leftPie7.setInstanceOption(_this.leftPie7.options);

				//备案GPS车辆
				_this.leftPie8.options = Tool.extendObj(true, _this.leftPie8.options, {series:[{data:[{value:gpscl,name:gpscl}]}]});
				_this.$refs.leftPie8.setInstanceOption(_this.leftPie8.options);

				//备案常压罐
				_this.rightPie1.options = Tool.extendObj(true, _this.rightPie1.options, {series:[{data:[{value:bacyg,name:bacyg}]}]});
				_this.$refs.rightPie1.setInstanceOption(_this.rightPie1.options);

				//备案压力罐
				_this.rightPie2.options = Tool.extendObj(true, _this.rightPie2.options, {series:[{data:[{value:ylg,name:ylg}]}]});
				_this.$refs.rightPie2.setInstanceOption(_this.rightPie2.options);

				//备案驾驶员
				_this.rightPie3.options = Tool.extendObj(true, _this.rightPie3.options, {series:[{data:[{value:bajsy,name:bajsy}]}]});
				_this.$refs.rightPie3.setInstanceOption(_this.rightPie3.options);

				//符合资质驾驶员
				_this.rightPie4.options = Tool.extendObj(true, _this.rightPie4.options, {series:[{data:[{value:zzjsy,name:zzjsy}]}]});
				_this.$refs.rightPie4.setInstanceOption(_this.rightPie4.options);

				//备案押运员
				_this.rightPie5.options = Tool.extendObj(true, _this.rightPie5.options, {series:[{data:[{value:bayyy,name:bayyy}]}]});
				_this.$refs.rightPie5.setInstanceOption(_this.rightPie5.options);

				//符合资质押运员
				_this.rightPie6.options = Tool.extendObj(true, _this.rightPie6.options, {series:[{data:[{value:zzyyy,name:zzyyy}]}]});
				_this.$refs.rightPie6.setInstanceOption(_this.rightPie6.options);

				//备案双证人员
				_this.rightPie7.options = Tool.extendObj(true, _this.rightPie7.options, {series:[{data:[{value:zzszry,name:zzszry}]}]});
				_this.$refs.rightPie7.setInstanceOption(_this.rightPie7.options);

				//符合资质双证员
				_this.rightPie8.options = Tool.extendObj(true, _this.rightPie8.options, {series:[{data:[{value:baszry,name:baszry}]}]});
				_this.$refs.rightPie8.setInstanceOption(_this.rightPie8.options);
			});



			//人员年龄分布
			this.getPersAgeStat(function(result){
				var data = [];
				var names = [];

				for(var i in result){
					var item = result[i];
					if(item.key>0&&item.key<80){
						var name ="";
						if( item.key>=10&& item.key<20)
							name = "20岁以下";
						if( item.key>=20&& item.key<30)
							name = "20-29岁";
						if( item.key>=30&& item.key<40)
							name = "30-39岁";
						if( item.key>=40&& item.key<50)
							name = "40-49岁";
						if( item.key>=50&& item.key<60)
							name = "50-59岁";
						if( item.key>=60&& item.key<70)
							name = "60-69岁";
						names.push(name);
						data.push({value:item.value, name:name})
					}
				}
				var documentSum = {
					labelLine:{
						show:true
					},
					color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
					tooltip:{
						show:true,
						trigger: 'item',
                		formatter: "{b}:{c}"
					},
					series:[{
						label: {
							normal: {
								position: 'outeside',
								formatter: "{b}\n{c}\n({d}%)"
							}
						},
						labelLine: {
							normal: {
								show: true
							}
						},
						radius: ['40%', '60%'],
						center: ["55%", "55%"],
						data:data
					}]
				};

				_this.staffAgeStatChart.options = Tool.extendObj(true, _this.staffAgeStatChart.options, documentSum);
				_this.$refs.staffAgeStatChart.setInstanceOption(_this.staffAgeStatChart.options);

			});

			//人员区域分布
			this.gePersDis(function(result){
				var data = [];
				var names = [];
				var otherTotal =0;

				for(var i in result){
					var item = result[i];
					if(item['value']>=500){
						names.push( item['key']);
						data.push({value: item['value'], name: item['key']});
					}else{
						otherTotal+=item['value'];
					}
				}
				names.push( "其他");
				data.push({value: otherTotal, name: "其他"});
				var documentSum = {
					color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
					tooltip:{
						show:true,
						trigger: 'item',
                		formatter: "{b}:{c}"
					},
					series:[{
						label: {
							show:true,
							normal: {
								position: 'outeside',
								formatter: "{b}\n{c}\n({d}%)"
							}
						},
						labelLine: {
							normal: {
								show: true
							}
						},
						radius: ['40%', '60%'],
						center: ["50%", "55%"],
						hoverAnimation:true,
						data:data
					}]
				};

				_this.staffDistribution.options = Tool.extendObj(true,_this.staffDistribution.options,documentSum);
				_this.$refs.staffDistribution.setInstanceOption(_this.staffDistribution.options);
			});

			//车辆区域分布统计
			this.geVecDistribute(function(result){

				if(result&&result.length>0){
					var provinceAlias = [{ '京':'北京' },{ '津':'天津' },{ '冀':'河北' },{ '晋':'山西' },{ '内蒙古':'内蒙古' },{ '辽':'辽宁' },{ '吉':'吉林' },{ '黑':'黑龙江' },{ '沪':'上海' },{ '苏':'江苏' },{ '浙':'浙江' },{ '皖':'安徽' },{ '闽':'福建' },{ '赣':'江西' },{ '鲁':'山东' },{ '豫':'河南' },{ '鄂':'湖北' },{ '湘':'湖南' },{ '粤':'广东' },{ '桂':'广西' },{ '琼':'海南' },{ '川':'四川' },{ '贵':'贵州' },{ '云':'云南' },{ '渝':'重庆' },{ '藏':'西藏' },{ '陕':'陕西' },{ '甘':'甘肃' },{ '青':'青海' },{ '宁':'宁夏' },{ '新':'新疆' },{ '港':'香港' },{ '澳':'澳门' },{ '台':'台湾' }];
					var pieData = [],
						mapData = [];

					for(var i in result){
						var item = result[i];
						for(var j in provinceAlias){
							var o = provinceAlias[j];
							for(var attr in o){
								if(item.x == attr){
									if(item.y>50){
										pieData.push({value:item.y,name:o[attr]});
									}
									mapData.push({name:o[attr], value:item.y, selected:true});
									break;
								}
							}
						}
					}
					pieData = pieData.sort(function (a, b) { return a.value - b.value});
					_this.map.setInstanceOption = Tool.extendObj(true,_this.map.options,{
						title:{
							text:"车辆区域分布",
							textStyle:{
                                color:"#ffffff",
                                fontWeight:100,
                                fontSize:14
                            }
						},
						series:[
							{
								name:'',
								type:'pie',
								radius : '30%',
								center: ['48%', '20%'],
								data:pieData,	//传入的数据
                startAngle:0, //起始角度
								color: ['#f71621','#ee183b','#f20229','#e40549','#f62963'],
								label: {
									normal: {
										textStyle: {
											fontSize:'16'
										}
									}
								},
								itemStyle: {
									normal: {
										shadowBlur: 10,
										shadowColor: 'rgba(0, 0, 0, 0.5)'
									}
								},
								animationType: 'scale',
								animationEasing: 'elasticOut',
								animationDelay: function (idx) {
									return Math.random() * 200;
								}
							},
							{
								data:mapData
							}
						]
					});

					_this.$refs.map.setInstanceOption(_this.map.options);

				}
			});

			//人员换岗次数情况
			this.getEntpPersFlow(function(result){
				var data = [];
				var names = ["1次","2次","3次","4次","5次以上"];
				data.push(result.one);
				data.push(result.two);
				data.push(result.three);
				data.push(result.four);
				data.push(result.aboveFive);

				_this.personnelFlow.options = Tool.extendObj(true,_this.personnelFlow.options,{
					xAxis : [{
						type : 'category',
						data : names,
						splitLine: {show: false},
						axisLabel: {
							interval:0,
							formatter: function (val) {
								if (val.length > 4) {
									var _val = val.substring(0, 5) + "\n"+val.substring(5);  // 让series 中的文字超出5个显示...
									return _val;
								}
								return val;
							}
						}
					}],
					series:[{
						data:data,
					}]
				});
				_this.$refs.personnelFlow.setInstanceOption(_this.personnelFlow.options);
			});

			//牵引车类型
			this.getTracTypeStat(function(result){
				var data = [];
				var names = [];
				var otherTotal =0;

				for(var i in result){
					var item = result[i];

					if(item['value']>=100){
						names.push( item['key']);
						data.push({value: item['value'], name: item['key']});
					}else{
						otherTotal+=item['value'];
					}
				}
				names.push( "其他");
				data.push({value: otherTotal, name: "其他"});
				var documentSum = {
					color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
					tooltip:{
						show:true,
						trigger: 'item',
                		formatter: "{b}:{c}"
					},
					series:[{
						label: {
							normal: {
								position: 'outeside',
								formatter: "{b}\n{c}\n({d}%)"
							}
						},
						labelLine: {
							normal: {
								show: true
							}
						},
						radius: ['40%', '60%'],
						center: ["50%", "50%"],
						data:data
					}]
				};
				_this.unloadStructureChartRight.options = Tool.extendObj(true,_this.unloadStructureChartRight.options,documentSum);
				_this.$refs.unloadStructureChartRight.setInstanceOption(_this.unloadStructureChartRight.options);
			})
		},
		methods:{
			//获取基本信息
			getBasicData(callback){
				$http.getBasicData().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			},
			//人员年龄分布
			getPersAgeStat(callback){
				$http.getPersAgeStat().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			},
			//人员区域分布
			gePersDis(callback){
				$http.gePersDis().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			},
			//车辆区域分布统计
			geVecDistribute(callback){
				$http.geVecDistribute().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			},
			//人员换岗次数情况
			getEntpPersFlow(callback){
				$http.getEntpPersFlow().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			},
			//牵引车类型
			getTracTypeStat(callback){
				$http.getTracTypeStat().then( res => {
					if(res){
						callback && callback(res);
					}
				})
				.catch( err => {

				});
			}
		},
		data(){
			let pieCommonStyle = {
				"width":"107px",
				"height":"107px"
			}
			return {
				contHeight:500,
				//left基本信息统计
				//备案挂车数量
				leftPie1:{
					id:"leftPie1",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记挂车",
							top:"-5px"
						},
						color:["#ffaabd"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质挂车数量
				leftPie2:{
					id:"leftPie2",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质挂车",
							top:"-5px"
						},
						color:["#ffaabd"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//运输企业数量
				leftPie3:{
					id:"leftPie3",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"运输企业",
							top:"-5px"
						},
						color:["#1b9deb"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质企业数量
				leftPie4:{
					id:"leftPie4",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质企业",
							top:"-5px"
						},
						color:["#1b9deb"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//区内装卸企业数量
				leftPie5:{
					id:"leftPie5",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"区内装卸企业",
							top:"-5px"
						},
						color:["#1b9deb"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//备案牵引车数量
				leftPie6:{
					id:"leftPie6",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记牵引车",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质牵引车数量
				leftPie7:{
					id:"leftPie7",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质牵引车",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//备案GPS车辆数量
				leftPie8:{
					id:"leftPie8",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记卫星定位车辆",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//right人员备案信息
				//备案常压罐数量
				rightPie1:{
					id:"rightPie1",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记常压罐",
							top:"-5px"
						},
						color:["#5dbdf7"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//备案压力罐数量
				rightPie2:{
					id:"rightPie2",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记压力罐",
							top:"-5px"
						},
						color:["#5dbdf7"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//备案驾驶员数量
				rightPie3:{
					id:"rightPie3",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记驾驶员",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质驾驶员数量
				rightPie4:{
					id:"rightPie4",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质驾驶员",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//备案押运员数量
				rightPie5:{
					id:"rightPie5",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记押运员",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质押运员数量
				rightPie6:{
					id:"rightPie6",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质押运员",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//数量备案双证人员
				rightPie7:{
					id:"rightPie7",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"登记双证人员",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//符合资质双证数量
				rightPie8:{
					id:"rightPie8",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"符合资质双证",
							top:"-5px"
						},
						color:["#db2f65"],
						series:[{
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//人员年龄分布
				staffAgeStatChart:{
					id:"staffAgeStatChart",
					styles:{
						"width":"100%",
						"height":"200px"
					},
					options:{
					}
				},
				//人员区域分布
				staffDistribution:{
					id:"staffDistribution",
					styles:{
						"width":"100%",
						"height":"200px"
					},
					options:{
						color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
						series:[]
					}
				},
				//人员换岗情况
				personnelFlow:{
					id:"personnelFlow",
					styles:{
						"width":"100%",
						"height":"200px"
					},
					options:{
						series:[{
							itemStyle: {
								normal: {
									color: new this.$echarts.graphic.LinearGradient(
										0, 1, 0, 0,
										[
											{offset: 0, color: '#1287b3'},//颜色参数
											{offset: 1, color: '#0ed1f1'}
										]
									)
								}
							}
						}]
					}
				},
				//牵引车类型
				unloadStructureChartRight:{
					id:"unloadStructureChartRight",
					styles:{
						"width":"100%",
						"height":"200px"
					},
					options:{
						color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
						series:[{
							hoverAnimation:true,
							data:[{value:0, name:'0'}]
						}]
					}
				},
				//车辆区域分布
				map:{
					id:"map",
					styles:{
						"width":"100%",
						"height":"600px"
					},
					options:{
					}
				}
			}
		}
	}
</script>
<style >
	.box-card{
		margin-bottom: 10px;
	}
	.chart-container{
		background: url('../../../../assets/dashboard-img/backgroundimage.v1.2.jpg');
		background-size: 100% 100%;
	}
	.box-card{
		background: none;
		border:none;
	}
	.box-card .el-card__body{
		background: rgba(41, 54, 93, 0.52);
		border: 1px solid #43738e;
		border-top: 0;
	}
	.box-card .el-card__header{
		background: rgba(72, 153, 241, 0.25);
    	border: 1px solid #43738e;
		color:#fff;
	}
</style>
