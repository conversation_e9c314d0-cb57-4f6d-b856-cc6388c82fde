<template>
    <div :id="id" v-bind:style="styles"></div>
</template>
<script>
    import * as Tool from "@/utils/tool"
    export default{
        name:'barComp',
        data(){
            return {
                instance:null
            }
        },
        props:{
            id:{
                type:String,
                default:"pie"
            },
            styles:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        mounted() {
            this.init(this.$props.id)
        },
        methods:{
            init(id){
                var barChart = this.$echarts.init(document.getElementById(id));
                barChart.setOption(
                    {
                        title:{
                            textStyle:{
                                color:"#fff",
                                fontWeight:100,
                                fontSize:14,
                                align:"center"
                            }
                        },
                        color: ['#5e6d90', '#ee4a6c', '#10a8ad', '#7ccab8', '#83577a', '#cfbd97', '#363e62', '#d7eb6e', '#d58471', '#d59420', '#52848b'],
                        tooltip : {
                            trigger: 'axis',
                            axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                                type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                            }
                        },
                        legend:{
                            textStyle:{
                                color:'#fff'
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis : [
                            {
                                type : 'category',
                                data : [''],
                                axisTick: {
                                    alignWithLabel: true
                                },
                                axisLabel:{
                                    color:"#fff"
                                },
                                splitLine:{
                                    show:false
                                },
                                axisLine:{
                                    show:true,
                                    lineStyle:{
                                        color:"#fff"
                                    }
                                }
                            }
                        ],
                        yAxis : [
                            {
                                type : 'value',
                                axisLabel:{
                                    color:"#fff"
                                },
                                splitLine:{
                                    show:false
                                },
                                axisLine:{
                                    show:true,
                                    lineStyle:{
                                        color:"#fff"
                                    }
                                }
                            }
                        ],
                        series : [
                            {
                                name:'',
                                type:'bar',
                                barWidth: '60%',
                                data:[10, 52, 200, 334, 390, 330, 220]
                            }
                        ]
                    }
                );

                this.instance = barChart;
            },
            //图表实例的 setOption 方法
            setInstanceOption(options){
                let oldOption = this.instance.getOption();
                let newOption = Tool.extendObj(true,{},oldOption,options);
                this.instance.setOption(newOption);
            },
            //图表响应式缩放
            resize(){
                this.instance.resize();
            }
        }
    }
</script>
<style>

</style>
