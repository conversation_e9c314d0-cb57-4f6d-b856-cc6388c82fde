<template>
    <div :id="id" v-bind:style="styles"></div>
</template>
<script>
    import * as Tool from "@/utils/tool"
    export default{
        name:'dbBarComp',
        data(){
            return {
                instance:null
            }
        },
        props:{
            id:{
                type:String,
                default:"pie"
            },
            styles:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        mounted() {
            this.init(this.$props.id)
        },
        methods:{
            init(id){
                var barChart = this.$echarts.init(document.getElementById(id));
                var option = {
                        legend: {
                            data: [],
                            right: 0,
                            align: 'right'
                        },
                        title:{
                            textStyle:{
                                color:"#333",
                                fontWeight:100,
                                fontSize:14,
                                align:"center"
                            }
                        },
                        color:['#ffbb2c', '#8360d9', '#8360d9', '#66ff00'],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true,
                        },
                        xAxis: [{
                            type: 'category',
                            data: [''],
                            splitLine: {show: false},
                            axisTick: {
                                alignWithLabel: true
                            }
                        }],
                        yAxis: {
                            type: 'value',
                            min: 0,
                            splitLine: {show: false}
                        },
                        dataZoom: [{
                            type: 'inside'
                        }],
                        series: [{
                            name: '',
                            type: 'bar',
                            data: []
                        }, {
                            name: '',
                            type: 'bar',
                            data: []
                        }]
                    };
                barChart.setOption(option);

                this.instance = barChart;
            },
            //图表实例的 setOption 方法
            setInstanceOption(options){
                let oldOption = this.instance.getOption();
                let newOption = Tool.extendObj(true,{},oldOption,options);
                this.instance.setOption(newOption);
            },
            //图表响应式缩放
            resize(){
                this.instance.resize();
            }
        }
    }
</script>
<style>

</style>
