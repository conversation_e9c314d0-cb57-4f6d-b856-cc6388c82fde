<template>
    <div :id="id" v-bind:style="styles"></div>
</template>
<script>
    import * as Tool from "@/utils/tool"
    export default{
        name:'lineComp',
        data(){
            return {
                instance:null
            }
        },
        props:{
            id:{
                type:String,
                default:"pie"
            },
            styles:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        mounted() {
            this.init(this.$props.id)
        },
        methods:{
            init(id){
                var lineChart = this.$echarts.init(document.getElementById(id));
                var option = {
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        orient: 'horizontal',
                        right: '1%',
                        data: ['超载', '超速', '超经营范围', '无卫星定位', '未备案']
                    },
                    title:{
                        textStyle:{
                            color:"#fff",
                            fontWeight:100,
                            fontSize:14,
                            align:"center"
                        }
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '1%',
                        top: '50px',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        axisTick: {
                            show: false
                        },
                        boundaryGap: true,
                        data: ['']
                    },
                    yAxis: {
                        type: 'value',
                    },
                    series: [
                        {
                            name: '超载',
                            type: 'line',
                            stack: 'alram',
                            smooth: true,
                            data: ['']
                        }, {
                            name: '超速',
                            type: 'line',
                            stack: 'alram',
                            smooth: true,
                            data: ['']
                        }, {
                            name: '超经营范围',
                            type: 'line',
                            stack: 'alram',
                            smooth: true,
                            data: ['']
                        }, {
                            name: '无卫星定位',
                            type: 'line',
                            stack: 'alram',
                            smooth: true,
                            data: ['']
                        }, {
                            name: '未备案',
                            type: 'line',
                            stack: 'alram',
                            smooth: true,
                            data: ['']
                        }]
                };
                lineChart.setOption(option);

                this.instance = lineChart;
            },
            //图表实例的 setOption 方法
            setInstanceOption(options){
                let oldOption = this.instance.getOption();
                let newOption = Tool.extendObj(true,{},oldOption,options);
                this.instance.setOption(newOption);
            },
            //图表响应式缩放
            resize(){
                this.instance.resize();
            }
        }
    }
</script>
<style>

</style>
