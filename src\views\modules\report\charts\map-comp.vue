<template>
    <div :id="id" v-bind:style="styles"></div>
</template>
<script>
    import * as Tool from "@/utils/tool"
    export default{
        name:'mapComp',
        data(){
            return {
                instance:null
            }
        },
        props:{
            id:{
                type:String,
                default:"pie"
            },
            styles:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        mounted() {
            this.init(this.$props.id)
        },
        methods:{
            init(id){
                var mapChart = this.$echarts.init(document.getElementById(id));
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function(param){
                            if(param.value){
                                return param.name+':<br/>'+param.value+' 辆';
                            }else{
                                return param.name+':<br/>0 辆';
                            }
                        }
                    },
                    series: [
                        {
                            type:'pie'
                        },
                        {
                            name: '中国',
                            type: 'map',
                            mapType: 'china',
                            roam:true,
                            zoom:1.3,
                            center:[104.946777,41.406572],
                            bottom:"20",
                            label:{
                                normal:{
                                    show: true,
                                    position: 'top',
                                    textStyle: {
                                        fontSize: 13,
                                        color: '#fff',
                                        textShadow:'10px 10px 10px rgba(255, 0, 0, .1)'
                                    }
                                },
                                emphasis: {
                                    show: true
                                }
                            },
                            itemStyle:{
                                normal:{
                                    borderColor:'#4880d5',
                                    areaColor:'#263ba6',
                                    color:'#fff'
                                },
                                emphasis:{
                                    color:'#333',
                                    areaColor:'#fffc1d'
                                }
                            },
                            selectedMode : 'multiple',
                            data:[] //传入的数据
                        }
                    ]
                };
                
                mapChart.setOption(option);
                this.instance = mapChart;
            },
            //图表实例的 setOption 方法
            setInstanceOption(options){
                let oldOption = this.instance.getOption();
                let newOption = Tool.extendObj(true,{},oldOption,options);
                console.log(newOption)
                this.instance.setOption(newOption);
            },
            //图表响应式缩放
            resize(){
                this.instance.resize();
            }
        }
    }
</script>
<style>

</style>
