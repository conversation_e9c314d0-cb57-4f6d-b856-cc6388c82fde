<template>
    <div :id="id" v-bind:style="styles"></div>
</template>
<script>
    import * as Tool from "@/utils/tool"
    export default{
        name:'pieComp',
        data(){
            return {
                instance:null
            }
        },
        props:{
            id:{
                type:String,
                default:"pie"
            },
            styles:{
                type:Object,
                default(){
                    return {}
                }
            }
        },
        mounted() {
            this.init(this.$props.id)
        },
        methods:{
            init(id){
                var pieChart = this.$echarts.init(document.getElementById(id));
                pieChart.setOption( {
                    title:{
                        textStyle:{
                            color:"#fff",
                            fontWeight:100,
                            fontSize:12,
                            align:"center"
                        }
                    },
                    tooltip: {
                        show:false,
                        trigger: 'item',
                        formatter: "{a} <br/>{b}: {c} ({d}%)"
                    },
                    legend: {
                        orient: 'vertical',
                        x: 'left',
                        data:[],
                        textStyle:{
                            color:'#fff'
                        }
                    },
                    series: [
                        {
                            hoverAnimation:false,
                            name:'',
                            type:'pie',
                            radius: ['50%', '70%'],
                            avoidLabelOverlap: false,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'center',
                                    color:'#fff'
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false,
                                    lineStyle:{
                                        color:'#fff'
                                    }
                                }
                            },
                            data:[]
                        }
                    ]
                });

                this.instance = pieChart;
            },
            //图表实例的 setOption 方法
            setInstanceOption(options){
                let oldOption = this.instance.getOption();
                let newOption = Tool.extendObj(true,{},oldOption,options);

                this.instance.setOption(newOption);
            },
            //图表响应式缩放
            resize(){
                this.instance.resize();
            }

        }
    }
</script>
<style>

</style>
