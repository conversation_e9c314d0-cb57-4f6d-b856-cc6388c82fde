<template>
  <div class="app-main-content">
    <div class="grid-search-bar clearfix" ref="searchbar">
      <el-row>
        <el-col :span="4" class="toolbar" style="padding-bottom: 0px">
          <span class="el-icon-date">路线车辆台账</span>
        </el-col>
        <el-col
          :span="20"
          class="toolbar text-right"
          style="padding-bottom: 0px"
        >
          <el-form
            ref="form"
            :model="queryForm"
            :inline="true"
            label-width="100px"
            size="mini"
          >
            <el-form-item prop="vehicle_no" label="车牌号:">
              <el-input
                v-model="queryForm.vehicle_no"
                placeholder="请输入车牌号"
              ></el-input>
            </el-form-item>
            <el-form-item prop="name" label="路线名:">
              <el-input
                v-model="queryForm.name"
                placeholder="请输入路线名"
              ></el-input>
            </el-form-item>
            <el-form-item prop="searchDateRange" label="查询日期:">
              <el-date-picker
                type="daterange"
                @change="getDataList"
                v-model="queryForm.searchDateRange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期(不跨月)"
                end-placeholder="结束日期(不跨月)"
                clearable
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getDataList">查询</el-button>
              <el-button type="success" @click="exportTable">导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <el-table
      :data="dataList"
      border
      :height="tableHeight"
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="name"
        label="路线名"
        min-width="150px"
      ></el-table-column>
      <el-table-column
        prop="entpName"
        label="企业"
        min-width="150px"
      ></el-table-column>
      <el-table-column
        prop="vehicleNo"
        label="车牌号"
        width="90px"
      ></el-table-column>
      <el-table-column prop="dvNm" label="驾驶员" width="160px">
        <template slot-scope="scope">
          {{ scope.row.dvNm
          }}<span v-show="scope.row.dvMob">({{ scope.row.dvMob }})</span>
        </template>
      </el-table-column>
      <el-table-column prop="scNm" label="押运员" width="160px">
        <template slot-scope="scope">
          {{ scope.row.scNm
          }}<span v-show="scope.row.scMob">({{ scope.row.scMob }})</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="dvMob" label="驾驶员电话" width="110px"></el-table-column>
      <el-table-column prop="scMob" label="押运员电话" width="110px"></el-table-column> -->
      <el-table-column
        prop="startTime"
        label="进入时间"
        width="150px"
      ></el-table-column>
      <el-table-column
        prop="endTime"
        label="离开时间"
        width="150px"
      ></el-table-column>

      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="histroyTrak(scope.row)"
            >历史轨迹</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="sizes, prev, next"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :page-size="pagination.size"
      :current-page.sync="pagination.page"
      :page-sizes="[20, 30, 50, 100, 200]"
      style="float: right"
    >
    </el-pagination>
  </div>
</template>

<script>
import * as API from "@/api/linestat";

import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
export default {
  data () {
    return {
      // searchItems: {
      //   normal: [
      //     {
      //       name: "车牌号",
      //       field: "vehicleNo",
      //       type: "text",
      //       dbfield: "vehicle_no",
      //       dboper: "cn"
      //     },
      //     {
      //       name: "路线名",
      //       field: "name",
      //       type: "text",
      //       dbfield: "name",
      //       dboper: "cn"
      //     },
      //     {
      //       name: "查询时间",
      //       field: "startTime ",
      //       type: "daterange",
      //       dbfield: "start_time",
      //       dboper: "bt",
      //       valueFormat: "yyyy-MM-dd HH:mm:ss",
      //       default: [Tool.formatDate(new Date(), "yyyy-MM-dd") + ' 00:00:00', Tool.formatDate(new Date(), "yyyy-MM-dd") + ' 23:59:59']
      //     }
      //   ]
      // },
      tableHeight: Tool.getClientHeight() - 210,
      queryForm: {
        vehicle_no: "",
        name: "",
        searchDateRange: [
          Tool.formatDate(new Date(new Date().getFullYear(), new Date().getMonth(), 1), "yyyy-MM-dd") + " 00:00:00",
          Tool.formatDate(new Date(), "yyyy-MM-dd") + " 23:59:59"
        ]
      },
      dataList: [],
      pagination: {
        page: 1,
        size: 15,
      },
      dataListLoading: false,
      dataListSelections: []
    };
  },
  components: {
    Searchbar
  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
    this.getDataList();
  },
  methods: {
    // 改变搜索框的高度
    resizeSearchbar () {
      this.setTableHeight();
    },
    setTableHeight () {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 210;
      });
    },
    //历史轨迹
    histroyTrak (row) {
      let location = window.location;
      window.open(location.origin + location.pathname + '#/monit/hisTrack?v=' + encodeURIComponent(row.vehicleNo) + '&t=' + row.start.substring(0, 10), '_blank');
    },
    // 获取数据列表
    getDataList () {
      let _this = this;
      this.listLoading = true;
      let postData = {
        ...this.queryForm,
        ...this.pagination
      };
      postData.start = this.queryForm.searchDateRange[0] || ""
      postData.end = this.queryForm.searchDateRange[1] || ""
      delete postData.searchDateRange;

      this.listLoading = true;
      API.list(postData).then(response => {
        if (response.code == 0) {
          _this.pagination.page = response.page.currPage;
          _this.dataList = response.page.list;
        } else {
          _this.dataList = [];
        }
        _this.listLoading = false;
      }).catch(error => {
        console.log(error);
        _this.listLoading = false;
      });
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getDataList();
      // this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.size = val;
      this.getDataList();
      // this.$refs.searchbar.searchHandle(true);
    },
    // 导出表格
    exportTable () {
      let postData = {
        ...this.queryForm,
        ...this.pagination
      };
      postData.start = this.queryForm.searchDateRange[0] || ""
      postData.end = this.queryForm.searchDateRange[1] || ""
      delete postData.searchDateRange;
      API.download(postData)
        .then(res => {
          let a = document.createElement("a");
          let blob = new Blob([res]);
          let url = window.URL.createObjectURL(blob);
          var _date = new Date();

          a.href = url;
          a.download =
            "路线台账_" +
            (_date.getFullYear() +
              "-" +
              (_date.getMonth() + 1) +
              "-" +
              _date.getDate()) +
            ".xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(err => { });
    }
  }
};
</script>
