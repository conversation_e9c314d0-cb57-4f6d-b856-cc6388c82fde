<template>
	<div class="detail-container chart-container">
		<el-row :gutter="10">
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 装卸总量统计 -->
					<db-bar-comp :id="loadUnloadGoods.id" ref="loadUnloadGoods" :styles='loadUnloadGoods.styles'></db-bar-comp>
				</el-card>
			</el-col>
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 区内登记点统计 -->
					<db-bar-comp :id="regVehicle.id" ref="regVehicle" :styles='regVehicle.styles'></db-bar-comp>
				</el-card>
			</el-col>
		</el-row>
		<el-row :gutter="10">
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 区内主要企业装货量统计 -->
					<bar-comp :id="compLoadGoods.id" ref="compLoadGoods" :styles='compLoadGoods.styles'></bar-comp>
				</el-card>
			</el-col>
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 区内主要企业卸货量统计 -->
					<bar-comp :id="compUnloadGoods.id" ref="compUnloadGoods" :styles='compUnloadGoods.styles'></bar-comp>
				</el-card>
			</el-col>
		</el-row>
		<el-row :gutter="10">
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 区内装货种类统 -->
					<pie-comp :id="loadGoodsTop.id" ref="loadGoodsTop" :styles='loadGoodsTop.styles'></pie-comp>
				</el-card>
			</el-col>
			<el-col :sm="12">
				<el-card class="box-card">
					<!-- 区内卸货种类统计 -->
					<pie-comp :id="unloadGoodsTop.id" ref="unloadGoodsTop" :styles='unloadGoodsTop.styles'></pie-comp>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>
<script>
import PieComp from "@/views/modules/report/charts/pie-comp"
import BarComp from "@/views/modules/report/charts/bar-comp"
import DbBarComp from "@/views/modules/report/charts/dbbar-comp"
import * as Tool from "@/utils/tool"
import * as $http from "@/api/stati"

export default {
	name: "loadAndUnload",
	components: {
		PieComp,
		BarComp,
		DbBarComp
	},
	data() {
		return {
			//装卸总量统计
			loadUnloadGoods: {
				id: "loadUnloadGoods",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
				}
			},
			//区内登记点统计
			regVehicle: {
				id: "regVehicle",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
				}
			},
			//区内主要企业装货量统计
			compLoadGoods: {
				id: "compLoadGoods",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
					title: {
						text: "区内主要企业装货量统计",
						top: "-5px"
					}
				}
			},
			//区内主要企业卸货量统计
			compUnloadGoods: {
				id: "compUnloadGoods",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
					title: {
						text: "区内主要企业卸货量统计",
						top: "-5px"
					}
				}
			},
			//区内装货种类统计
			loadGoodsTop: {
				id: "loadGoodsTop",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
					title: {
						text: "区内装货种类统计",
						top: "-5px"
					},
					color: ['#ffcf02', '#00a7f8', '#157dd0', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00']
				}
			},
			//区内卸货种类统计
			unloadGoodsTop: {
				id: "unloadGoodsTop",
				styles: {
					width: "100%",
					height: "320px"
				},
				options: {
					title: {
						text: "区内卸货种类统计",
						top: "-5px"
					},
					color: ['#ffcf02', '#00a7f8', '#157dd0', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00']
				}
			}
		}
	},
	created() {
		var _this = this;
		/*
		 * 装卸总量统计
		 */
		getLoadAndUnloadCntByMonth();
		function getLoadAndUnloadCntByMonth() {
			var distance = [],
				discharging,
				loading,
				results = { "loading": [{ "key": null, "value": 11634.120000 }, { "key": "2016-06", "value": 69015.648190 }, { "key": "2016-07", "value": 558793.756700 }, { "key": "2016-08", "value": 625519.092000 }, { "key": "2016-09", "value": 603580.780000 }, { "key": "2016-10", "value": 706178.716000 }, { "key": "2016-11", "value": 666744.195890 }, { "key": "2016-12", "value": 677752.249390 }, { "key": "2017-01", "value": 620682.360710 }, { "key": "2017-02", "value": 620430.526650 }, { "key": "2017-03", "value": 761602.840000 }, { "key": "2017-04", "value": 724479.308890 }, { "key": "2017-05", "value": 749240.317320 }, { "key": "2017-06", "value": 718818.302950 }, { "key": "2017-07", "value": 731029.001860 }, { "key": "2017-08", "value": 744371.589460 }, { "key": "2017-09", "value": 668015.678000 }, { "key": "2017-10", "value": 510853.053260 }, { "key": "2017-11", "value": 480642.624420 }, { "key": "2017-12", "value": 660381.079760 }, { "key": "2018-01", "value": 716059.240860 }, { "key": "2018-02", "value": 529582.097850 }, { "key": "2018-03", "value": 738811.667960 }, { "key": "2018-04", "value": 694585.379820 }, { "key": "2018-05", "value": 636016.151680 }], "discharging": [{ "key": "2016-06", "value": 2039.070960 }, { "key": "2016-07", "value": 22210.093030 }, { "key": "2016-08", "value": 47926.353000 }, { "key": "2016-09", "value": 39645.994000 }, { "key": "2016-10", "value": 39747.800000 }, { "key": "2016-11", "value": 45433.603380 }, { "key": "2016-12", "value": 51826.875580 }, { "key": "2017-01", "value": 40681.659380 }, { "key": "2017-02", "value": 40235.773610 }, { "key": "2017-03", "value": 40435.504000 }, { "key": "2017-04", "value": 36033.600420 }, { "key": "2017-05", "value": 43842.680000 }, { "key": "2017-06", "value": 38746.442270 }, { "key": "2017-07", "value": 59504.941690 }, { "key": "2017-08", "value": 46796.522280 }, { "key": "2017-09", "value": 45937.920000 }, { "key": "2017-10", "value": 57812.961000 }, { "key": "2017-11", "value": 62077.954460 }, { "key": "2017-12", "value": 59370.337000 }, { "key": "2018-01", "value": 42437.575000 }, { "key": "2018-02", "value": 30956.838480 }, { "key": "2018-03", "value": 61224.374560 }, { "key": "2018-04", "value": 60772.232000 }, { "key": "2018-05", "value": 52500.700000 }] };

			for (var i in results) {
				var amount = [];
				var u = results[i];
				switch (i) {
					case 'discharging':
						u.filter((u, i) => {
							distance.push(
								parseInt(String(u.key).replace(/\d{4}-(\d{1,2})/g, '$1')) + 0 + '月'
							);
							amount.push(u.value);
						});
						discharging = amount;
						break;
					case 'loading':
						u.filter((u, i) => {
							amount.push(u.value);
						});
						loading = amount;
						break;
				}
			}

			var options = {
				legend: [{
					name: '卸装总量',
					textStyle: {
						color: "#fff",
						fontSize: "12"
					}
				}, {
					name: '装货总量',
					textStyle: {
						color: "#fff",
						fontSize: "12"
					}
				}],
				title: {
					text: "装货总量统计",
					textStyle: {
						color: '#fff'
					}
				},
				xAxis: [{
					data: distance,
					axisLine: {
						show: true,
						lineStyle: {
							color: "#fff"
						}
					}
				}],
				yAxis: [
					{
						axisLine: {
							show: true,
							lineStyle: {
								color: "#fff"
							}
						}
					}
				],
				series: [{
					data: discharging,
					itemStyle: {
						color: "#fac259"
					}
				}, {
					data: loading,
					itemStyle: {
						color: "#32d2c1"
					}
				}]
			}

			_this.loadUnloadGoods.options = Tool.extendObj(true, _this.loadUnloadGoods.options, options);
		}

		/*
		 *区内登记点统计 
		 */
		getRegPointVecCnt();
		function getRegPointVecCnt() {
			var empty = [], full = [], entpNm = [];
			var results = [{ "'empty'": "137115", "entpNm": "镇海登记点1", "full": "94018" }, { "'empty'": "176063", "entpNm": "镇海登记点2", "full": "109861" }, { "'empty'": "210720", "entpNm": "镇海登记点3", "full": "59443" }, { "'empty'": "56932", "entpNm": "镇海登记点4", "full": "187262" }, { "'empty'": "30412", "entpNm": "镇海登记点5", "full": "150850" }, { "'empty'": "148836", "entpNm": "镇海登记点6", "full": "22075" }];
			for (var i in results) {
				var item = results[i];
				for (var j in item) {
					var iitem = item[j];
					j = j.match(/(\w+)/gi)[0];
					switch (j) {
						case 'empty':
							empty.push(String(parseFloat(iitem)).replace(/(?=\.[0]+$)/, ''));
							break;
						case 'entpNm': {
							iitem = iitem.replace("镇海登记点1", "海天1号");
							iitem = iitem.replace("镇海登记点2", "海天2号");
							iitem = iitem.replace("镇海登记点3", "蛟川1号");
							iitem = iitem.replace("镇海登记点4", "蛟川2号");
							iitem = iitem.replace("镇海登记点5", "澥浦1号");
							iitem = iitem.replace("镇海登记点6", "澥浦2号");
							entpNm.push(iitem);
						}
							break;
						default:
							full.push(String(parseFloat(iitem)).replace(/(?=\.[0]+$)/, ''));
					}
				}
			}

			var options = {
				legend: {
					data: [{
						name: '空车总量', textStyle: {
							color: "#fff",
							fontSize: "12"
						}
					}, {
						name: '重车总量', textStyle: {
							color: "#fff",
							fontSize: "12"
						}
					}]
				},
				title: {
					text: "区内登记点统计",
					textStyle: {
						color: '#fff'
					}
				},
				xAxis: [{
					data: entpNm,
					axisLine: {
						show: true,
						lineStyle: {
							color: "#fff"
						}
					}
				}],
				yAxis: [
					{
						axisLine: {
							show: true,
							lineStyle: {
								color: "#fff"
							}
						}
					}
				],
				series: [{
					data: empty,
					itemStyle: {
						color: "#ffbb2c"
					}
				}, {
					data: full,
					itemStyle: {
						color: "#8360d9"
					}
				}]
			}

			_this.regVehicle.options = Tool.extendObj(true, _this.regVehicle.options, options)
		}

		/*
		 *区内主要企业装货量统计 
		 */
		getEntpLoadGoodsCnt();
		function getEntpLoadGoodsCnt() {
			var result = [
				["宁波中金石化有限公司", 33780126.891020],
				["镇海石化海达发展有限责任公司", 2764798.430070],
				["宁波镇洋化工发展有限公司", 1522513.943800],
				["宁波巨化化工科技有限公司", 574820.060000],
				["镇海石化工业贸易有限责任公司", 481522.685200]
			];
			var entploadNm = [],
				loadAmn = [],
				loadCnt = 0;
			result.filter((item, indx) => {
				if (item[1] > 0) {
					entploadNm.push(_this.shortCorp(item[0]));
					loadAmn.push(parseFloat(item[1] / 10000).toFixed(2));
					loadCnt += parseFloat(item[1] / 10000);
				}
			});
			var options = {
				color: '#fbe603',
				title: {
					text: "区内主要企业装货量统计",
					subtext: '总量：' + loadCnt.toFixed(2) + ' 万吨',
					subtextStyle: {
						color: "#fbe603"
					}
				},
				series: [{
					data: loadAmn
				}],
				xAxis: [{
					data: entploadNm
				}],
				unit: "万吨",
				xName: '时间',
				yName: '数量'
			}

			_this.compLoadGoods.options = Tool.extendObj(true, _this.compLoadGoods.options, options)
		}

		/*
		 *区内主要企业卸货量统计 
		 */
		getEntpUnloadGoodsCnt();
		function getEntpUnloadGoodsCnt() {
			var result = [
				["宁波中金石化有限公司", 453746.247220],
				["阿克苏诺贝尔", 156220.760000],
				["宁波金海晨光化学股份有限公司", 101745.197500],
				["宁波浙铁江宁化工有限公司", 97312.660000],
				["宁波甬兴化工有限公司", 67506.768000]
			];
			var entploadNm = [],
				loadAmn = [],
				loadCnt = 0;
			result.filter((item, indx) => {
				if (item[1] > 0) {
					entploadNm.push(_this.shortCorp(item[0]));
					loadAmn.push(parseFloat(item[1] / 10000).toFixed(2));
					loadCnt += parseFloat(item[1] / 10000);
				}
			});
			var options = {
				color: '#fc8b19',
				title: {
					text: "区内主要企业卸货量统计",
					subtext: '总量：' + loadCnt.toFixed(2) + ' 万吨',
					subtextStyle: {
						color: "#fc8b19"
					}
				},
				series: [{
					data: loadAmn
				}],
				xAxis: [{
					data: entploadNm
				}],
				unit: "万吨",
				xName: '时间',
				yName: '数量'
			}

			_this.compUnloadGoods.options = Tool.extendObj(true, _this.compUnloadGoods.options, options);
		}

		/*
		 * 区内装货种类统计
		 */
		getLoadCnt();
		function getLoadCnt() {
			var res = [{ "key": "二甲苯", "value": 1087419 }, { "key": "石油混合二甲苯", "value": 404946 }, { "key": "工业己烷", "value": 298446 }, { "key": "石油苯", "value": 156172 }, { "key": "液化石油气", "value": 135996 }, { "key": "异丁烷", "value": 124105 }, { "key": "工业用碳十粗芳烃", "value": 99933 }, { "key": "戊烷发泡剂", "value": 73765 }, { "key": "液碱", "value": 37940 }, { "key": "对二甲苯 ", "value": 34856 }];
			var data = [];
			var names = [];
			var total = 0;
			res.filter((item, index) => {
				data.push({ value: item['value'], name: item['key'] })
				names.push({ name: item['key'], icon: "circle", textStyle: { fontSize: "10" } });
				total += item['value'];
			});
			var options = {
				legend: {
					data: names,
					orient: "horizontal",
					bottom: 0
				},
				tooltip: {
					show: true,
					trigger: 'item',
					formatter: "{b}:{c}"
				},
				color: ['#ffcf02', '#00a7f8', '#157dd0', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00'],
				center: ["70%", "45%"],
				radius: ["25%", "40%"],
				series: [{
					label: {
						normal: {
							position: 'outeside',
							formatter: "{b}\n{c}\n({d}%)"
						}
					},
					labelLine: {
						normal: {
							show: true
						}
					},
					data: data
				}],
				total: total
			};

			_this.loadGoodsTop.options = Tool.extendObj(true, _this.loadGoodsTop.options, options);
		}

		/*
		 * 区内卸货种类统计
		 */
		getUnloadCnt();
		function getUnloadCnt() {
			var res = [{ "key": "液化石油气", "value": 16989 }, { "key": "丙烯", "value": 3242 }, { "key": "液碱", "value": 3168 }, { "key": "碳五", "value": 2906 }, { "key": "丙烯腈", "value": 2618 }, { "key": "液氯", "value": 2387 }, { "key": "正丁烷", "value": 2292 }, { "key": "液化气", "value": 2084 }, { "key": "冰醋酸", "value": 1536 }, { "key": "烧碱", "value": 1474 }];
			var data = [];
			var names = [];
			var total = 0;
			res.filter((item, index) => {
				data.push({ value: item['value'], name: item['key'] })
				names.push({ name: item['key'], icon: "circle", textStyle: { fontSize: "10" } });
				total += item['value'];
			});
			var options = {
				legend: {
					data: names,
					orient: "horizontal",
					bottom: 0
				},
				tooltip: {
					show: true,
					trigger: 'item',
					formatter: "{b}:{c}"
				},
				color: ['#ffcf02', '#00a7f8', '#157dd0', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00', '#f87309', '#a4a5a6', '#ffc200', '#157dd0', '#54b645', '#155c94', '#b35c24', '#a27f00'],
				center: ["70%", "45%"],
				radius: ["25%", "40%"],
				series: [{
					data: data,
					label: {
						normal: {
							position: 'outeside',
							formatter: "{b}\n{c}\n({d}%)"
						}
					},
					labelLine: {
						normal: {
							show: true
						}
					},
				}],
				total: total
			};

			_this.unloadGoodsTop.options = Tool.extendObj(true, _this.unloadGoodsTop.options, options);
		}

	},
	mounted() {
		//装卸总量统计
		this.$refs.loadUnloadGoods.setInstanceOption(this.loadUnloadGoods.options);
		//区内登记点统计
		this.$refs.regVehicle.setInstanceOption(this.regVehicle.options);
		//区内主要企业装货量统计
		this.$refs.compLoadGoods.setInstanceOption(this.compLoadGoods.options);
		//区内主要企业卸货量统计
		this.$refs.compUnloadGoods.setInstanceOption(this.compUnloadGoods.options);
		//区内装货种类统计
		this.$refs.loadGoodsTop.setInstanceOption(this.loadGoodsTop.options);
		//区内卸货种类统计
		this.$refs.unloadGoodsTop.setInstanceOption(this.unloadGoodsTop.options);
	},
	methods: {
		shortCorp(entpName) {

			entpName = entpName.replace("宁波市", "");
			entpName = entpName.replace("宁波", "");
			entpName = entpName.replace("浙江", "");
			entpName = entpName.replace("浙江省", "");
			entpName = entpName.replace("发展", "");
			entpName = entpName.replace("（）", "");
			entpName = entpName.replace("化工", "");
			entpName = entpName.replace("化学", "");
			entpName = entpName.replace("工业", "");
			entpName = entpName.replace("码头", "");
			entpName = entpName.replace("储运", "");
			entpName = entpName.replace("中国", "");
			entpName = entpName.replace("石油", "");
			entpName = entpName.replace("镇海", "");
			entpName = entpName.replace("中外合资", "");
			entpName = entpName.replace("石化", "");
			entpName = entpName.replace("科技", "");
			entpName = entpName.replace("城市", "");
			entpName = entpName.replace("渣土", "");
			entpName = entpName.replace("渣运", "");
			entpName = entpName.replace("运输", "");
			entpName = entpName.replace("物流", "");
			entpName = entpName.replace("施工", "");
			entpName = entpName.replace("乙烯胺", "");
			entpName = entpName.replace("液化", "");
			entpName = entpName.replace("液体", "");
			entpName = entpName.replace("仓储", "");
			entpName = entpName.replace("制造", "");
			entpName = entpName.replace("诺贝尔", "");
			entpName = entpName.replace("服务有限公司", "");
			entpName = entpName.replace("技术有限公司", "");
			entpName = entpName.replace("股份有限公司", "");
			entpName = entpName.replace("有限责任公司", "");
			entpName = entpName.replace("责任有限公司", "");
			entpName = entpName.replace("有限公司", "");
			entpName = entpName.replace("责任公司", "");
			entpName = entpName.replace("分公司", "");
			entpName = entpName.replace("公司", "");

			return entpName;

		}
	}

}
</script>
<style >
.box-card {
	margin-bottom: 10px;
}

.chart-container {
	background: url('../../../../assets/dashboard-img/backgroundimage.v1.2.jpg');
	background-size: 100% 100%;
}

.box-card {
	background: none;
	border: none;
}

.box-card .el-card__body {
	background: rgba(41, 54, 93, 0.52);
	border: 1px solid #43738e;
	border-top: 1px solid #43738e;
	overflow: hidden;
}

.box-card .el-card__header {
	background: rgba(72, 153, 241, 0.25);
	border: 1px solid #43738e;
	color: #fff;
}</style>
