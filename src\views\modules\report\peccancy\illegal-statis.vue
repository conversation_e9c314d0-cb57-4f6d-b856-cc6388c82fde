<template>
	<div class="detail-container chart-container">
		 <el-row :gutter="10">
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- 违章趋势图统计 -->
                    <line-comp :id="violationCase.id" ref="violationCase" :styles='violationCase.styles'></line-comp>
                </el-card>
            </el-col>
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- 违章类型统计 -->
                    <pie-comp :id="alarmChart.id" ref="alarmChart" :styles='alarmChart.styles'></pie-comp>
                </el-card>
            </el-col>
        </el-row>
          <el-row :gutter="10">
            <el-col :sm="12">
                <el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>主要超速路段</span>
					</div>
					<el-row :gutter="20">
						<el-col :sm="12" class="gridCell">道路</el-col>
						<el-col :sm="12" class="gridCell">超速次数</el-col>
					</el-row>
					<div style="height:320px;overflow-y:auto;overflow-x:hidden;">
						<el-row v-for="(item,index) in list1" :key="index">
							<el-col :sm="12" class="gridCell">{{item.name}}</el-col>
							<el-col :sm="12" class="gridCell">{{item.value}}</el-col>
						</el-row>
					</div>
					
                </el-card>
            </el-col>
            <el-col :sm="12" >
                <el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>主要停车地点</span>
					</div>
					<el-row :gutter="20">
						<el-col :sm="12" class="gridCell">地点
						</el-col>
						<el-col :sm="12" class="gridCell">停车次数
						</el-col>
					</el-row>
					<div style="height:320px;overflow-y:auto;overflow-x:hidden;">
						<el-row :gutter="20" v-for="(item,index) in list2" :key="index">
							<el-col :sm="12" class="gridCell">{{item.name}}</el-col>
							<el-col :sm="12" class="gridCell">{{item.value}}</el-col>
						</el-row>
					</div>
                </el-card>
            </el-col>
        </el-row>
	</div>
</template>
<script>
	import PieComp from "@/views/modules/report/charts/pie-comp"
	import LineComp from "@/views/modules/report/charts/line-comp"
	import * as Tool from "@/utils/tool"
	import * as $http from "@/api/stati"
	export default {
		name:"illegalStatis",
		components:{
			PieComp,
			LineComp
		},
		data(){
			return {
				//主要超速路段
				list1:[],
				//主要停车路段
				list2:[],
				//违章趋势图统计
				violationCase:{
					id:"violationCase",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
						title:{
							text:"违章趋势图统计",
							top:"-5px"
						}
					}
				},
				//违章类型统计
				alarmChart:{
					id:"alarmChart",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
						title:{
							text:"违章类型统计",
							top:"-5px"
						}
					}
				}
			}
		},
		created() {
			var _this = this;
			/*
			 *违章趋势图统计 
			 */
			getAlarmCntByMonth();
			function getAlarmCntByMonth(){
				var res = {"overload":[{"key":"2016-07","value":414},{"key":"2016-08","value":935},{"key":"2016-09","value":268},{"key":"2016-10","value":316},{"key":"2016-11","value":282},{"key":"2016-12","value":288},{"key":"2017-01","value":188},{"key":"2017-02","value":204},{"key":"2017-03","value":161},{"key":"2017-04","value":70},{"key":"2017-05","value":49},{"key":"2017-06","value":66},{"key":"2017-07","value":60},{"key":"2018-02","value":93},{"key":"2018-03","value":281},{"key":"2018-04","value":99},{"key":"2018-05","value":88}],"overSpeed":[{"key":"2016-07","value":30},{"key":"2016-08","value":867},{"key":"2016-09","value":547},{"key":"2016-10","value":106},{"key":"2016-11","value":105},{"key":"2016-12","value":187},{"key":"2017-01","value":122},{"key":"2017-02","value":11},{"key":"2017-03","value":279},{"key":"2017-04","value":309},{"key":"2017-05","value":237},{"key":"2017-06","value":165},{"key":"2017-07","value":174},{"key":"2017-08","value":143},{"key":"2017-09","value":61},{"key":"2017-10","value":33},{"key":"2017-11","value":43},{"key":"2017-12","value":53},{"key":"2018-01","value":62},{"key":"2018-02","value":390},{"key":"2018-03","value":103},{"key":"2018-04","value":21},{"key":"2018-05","value":14}],"overScope":[{"key":"2016-07","value":78},{"key":"2016-08","value":727},{"key":"2016-09","value":941},{"key":"2016-10","value":660},{"key":"2016-11","value":592},{"key":"2016-12","value":373},{"key":"2017-01","value":216},{"key":"2017-02","value":276},{"key":"2017-03","value":188},{"key":"2017-04","value":202},{"key":"2017-05","value":163},{"key":"2017-06","value":95},{"key":"2017-07","value":149},{"key":"2018-02","value":112},{"key":"2018-03","value":125},{"key":"2018-04","value":128},{"key":"2018-05","value":12}],"notGps":[{"key":"2016-07","value":2141},{"key":"2016-08","value":3924},{"key":"2016-09","value":3337},{"key":"2016-10","value":2617},{"key":"2016-11","value":1170},{"key":"2016-12","value":749},{"key":"2017-01","value":572},{"key":"2017-02","value":371},{"key":"2017-03","value":113},{"key":"2017-04","value":59},{"key":"2017-05","value":36},{"key":"2017-06","value":30},{"key":"2017-07","value":26},{"key":"2018-02","value":5},{"key":"2018-03","value":11},{"key":"2018-04","value":7},{"key":"2018-05","value":7}],"notRecord":[{"key":"2016-07","value":5981},{"key":"2016-08","value":11514},{"key":"2016-09","value":6649},{"key":"2016-10","value":3132},{"key":"2016-11","value":1397},{"key":"2016-12","value":545},{"key":"2017-01","value":265},{"key":"2017-02","value":52},{"key":"2017-03","value":29},{"key":"2017-04","value":11},{"key":"2017-05","value":18},{"key":"2017-06","value":12},{"key":"2017-07","value":27},{"key":"2018-02","value":6},{"key":"2018-03","value":9},{"key":"2018-04","value":20},{"key":"2018-05","value":1}]};
				var distance = [],
					notGps,
					notRecord,
					overScope,
					overSpeed,
					overload;
				for( var i in res){
					var amount = [];
					var item  = res[i];
					switch (i) {
						case 'notGps':
							item.filter( (item,index) => {
								distance.push(
									parseInt(String(item.key).replace(/\d{4}-(\d{1,2})/g, '$1')) + 0 + '月'
								);
								amount.push(item.value);
							});
							notGps = amount;
							break;
						case 'notRecord':
							item.filter( (item,index) => {
								amount.push(item.value);
							});
							notRecord = amount;
							break;
						case 'overScope':
							item.filter( (item,index) => {
								amount.push(item.value);
							});
							overScope = amount;
							break;
						case 'overSpeed':
							item.filter( (item,index) => {
								amount.push(item.value);
							});
							overSpeed = amount;
							break;
						default:
							item.filter( (item,index) => {
								amount.push(item.value);
							});
							overload = amount;
					}
				}

				var options = {
					xAxis:[{
						data:distance,
						axisLine: {
							lineStyle: {
								width: 2,
								color:'#fff'
							}
						},
						splitLine: {show: false}
					}],
					yAxis:[{
						axisLine: {
							lineStyle: {
								width: 2,
								color:'#fff'
							}
						},
						splitLine: {show: false}
					}],
					legend: {
						textStyle:{
							color:'#fff'
						}
					},
					series: [
					{
						name: '超载',
						type: 'line',
						stack: 'alram',
						smooth: true,
						data: overload
					}, {
						name: '超速',
						type: 'line',
						stack: 'alram',
						smooth: true,
						data: overSpeed
					}, {
						name: '超经营范围',
						type: 'line',
						stack: 'alram',
						smooth: true,
						data: overScope
					}, {
						name: '无卫星定位',
						type: 'line',
						stack: 'alram',
						smooth: true,
						data: notGps
					}, {
						name: '未备案',
						type: 'line',
						stack: 'alram',
						smooth: true,
						data: notRecord
					}]
				}

				_this.violationCase.options = Tool.extendObj(true,_this.violationCase.options,options);
			};

			/*
			 *违章类型统计 
			 */
			$http.getAlarmType().then( res => {
				if(res){
					if (res && res.length > 0) {
						var alarmData = [];
						var totalAlarmCnt = 0;

						res.filter( (item,index) => {
							totalAlarmCnt += (item.y);
						});

						res.filter( (item,index) => {
							var name = item.x;
							name = name.replace("报警","");
							alarmData.push({name: name, value: item.y});
						});

						var options = {
							color: ['#fff100', '#63c727', '#1fa9f4', '#e9733f'],
							tooltip: {
								trigger: 'item',
								formatter: "{b}:{c}"
							},
							series:[{
								tooltip:{
									show:true,
									trigger: 'item',
									formatter: "{b}:{c}"
								},
								data: alarmData,
								radius: ['40%', '60%'],
								center: ["50%", "55%"],
								label: {
									normal: {
										show: true,
										position: 'outside',
										formatter:  "{b} :\n  {c}"
									},
									emphasis: {
										show: true
									}
								},
								labelLine: {
									normal: {
										show: true
									}
								}
							}]
						};


						_this.alarmChart.options = Tool.extendObj(true,_this.alarmChart.options,options);
						//章类型统
						_this.$refs.alarmChart.setInstanceOption(_this.alarmChart.options);
					}
				}
			})
			.catch( err => {

			});

			/*
			 *主要超速路段 
			 */
			$http.getOverSpeedAddress().then( res => {
				_this.list1 = res;
			})
			.catch( err => {

			});

			/*
			 *主要停车地点 
			 */
			$http.getStopAlarmAddress().then( res => {
				_this.list2 = res;
			})
			.catch( err => {

			});
		},
		mounted() {
			//违章趋势图统计
			this.$refs.violationCase.setInstanceOption(this.violationCase.options);
			
			
		},
		methods:{

		}

	}
</script>
<style >
	.box-card{
		margin-bottom: 10px;
	}
	.chart-container{
		background: url('../../../../assets/dashboard-img/backgroundimage.v1.2.jpg');
		background-size: 100% 100%;
	}
	.box-card{
		background: none;
		border:none;
	}
	.box-card .el-card__body{
		background: rgba(41, 54, 93, 0.52);
		border: 1px solid #43738e;
		border-top: 1px solid #43738e;
        overflow: hidden;
	}
	.box-card .el-card__header{
		background: rgba(72, 153, 241, 0.25);
    	border: 1px solid #43738e;
		color:#fff;
	}
	.gridCell{
		color:#fff;
		padding:10px;
	}
</style>
