<!--
 * @Desc: 统计分析-- 登记点统计
-->

<template>
  <div class="app-main-content">
    <searchbar
      ref="searchbar"
      :searchItems="searchItems"
      :pagination="pagination"
      @resizeSearchbar="resizeSearchbar"
      @search="getList"
    >
    </searchbar>
    <!--列表-->
    <el-table
      class="el-table"
      :data="list"
      highlight-current-row
      v-loading="listLoading"
      border
      ref="singleTable"
      style="width: 100%;"
      :max-height="tableHeight"
      @sort-change="handleSort"
    >
      <el-table-column type="index" label="序号" width="50" align="center">
      </el-table-column>
      <el-table-column prop="date" label="日期" align="center"> </el-table-column>
      <el-table-column prop="zhInCnt" label="镇海1点(进)" align="center">
      </el-table-column>
      <el-table-column prop="zhOutCnt" label="镇海2点(出)" align="center" />

      <el-table-column prop="jcInCnt" label="蛟川1点(进)" align="center">
      </el-table-column>
      <el-table-column prop="jcOutCnt" label="蛟川2点(出)" align="center" />

      <el-table-column prop="xpOutCnt" label="澥浦1点(出)" align="center" />
      <el-table-column prop="xpInCnt" label="澥浦2点(进)" align="center">
      </el-table-column>
      <el-table-column prop="shInCnt" label="石化1点(进)" align="center" />
      <el-table-column prop="shOutCnt" label="石化2点(出)" align="center" />
    </el-table>

    <!--工具条-->
    <div class="toolbar clearfix">
      <div class="grid-operbar ft-lf"></div>
      <el-pagination
        background
        layout="sizes, prev, pager, next, total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]"
        style="float:right;"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/registrationStatistics";
import { mapGetters } from "vuex";

export default {
  components: {
    Searchbar
  },
  data: function() {
    return {
      tableHeight: 500,
      listLoading: false,
      list: [],
      searchItems: {
        normal: [
          {
            name: "日期",
            field: "date",
            type: "daterange",
            dbfield: "date",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd",
            default: []
          }
        ]
      },

      pagination: {
        page: 1,
        limit: 15,
        total: 0
      }
    };
  },
  computed: {
    ...mapGetters(["username"])
  },
  mounted: function() {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    getList: function(data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .ckrecordPage(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          _this.listLoading = false;
          console.log(error);
        });
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function(val) {
      this.pagination.page = val;
      this.getList();
    },

    // 分页条数修改
    handleSizeChange: function(val) {
      this.pagination.limit = val;
      this.getList();
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    }
  }
};
</script>

<style scoped></style>
