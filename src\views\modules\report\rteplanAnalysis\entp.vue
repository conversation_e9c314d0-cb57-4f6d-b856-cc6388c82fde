<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList">
      <el-button slot="button" icon="el-icon-document" type="primary" size="small" @click="exportList">导出
      </el-button>
    </searchbar>
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable"
      style="width: 100%;" :height="tableHeight">
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column prop="entpNm" label="企业名称"></el-table-column>
      <el-table-column prop="crtTm" label="创建时间"></el-table-column>
      <el-table-column prop="crtNm" label="创建人"></el-table-column>
      <el-table-column prop="cd" label="文件名称" width="500"></el-table-column>
      <el-table-column label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="download(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :total="pagination.total"
        :page-sizes="[20, 30, 50, 100, 200]" style="float:right;">
      </el-pagination>
    </div>
    <el-dialog append-to-body title="导出" :visible.sync="exportVisible" width="30%">
      <div style="margin-bottom: 10px;">
        <div style="color: rgb(250, 0, 0);margin-bottom: 10px;">导出需要等待片刻，您点击提交后可以关闭弹窗，随后到列表页面点击下载</div>
        <div>
          <div class="el-form-item__label">企业名称</div>
          <el-select style="width: 300px;" filterable size="small" v-model="params.entpName" placeholder="请选择企业名称"
            clearable>
            <el-option v-for="op in entpList" :key="op.value" :label="op.label" :value="op.value"></el-option>
          </el-select>
          <!-- <el-input v-model="params.entpName" placeholder="请输入企业名称" clearable style="width: 300px;"></el-input> -->
        </div>
        <div style="margin-top: 15px;">
          <div class="el-form-item__label" style="width: 68px;">年份</div>
          <el-date-picker type="year" format="yyyy" size="small" v-model="params.date" placeholder="请选择年份" clearable
            style="width: 300px;"></el-date-picker>
        </div>
      </div>
      <el-button @click="exportFunc" type="primary" size="small">提交</el-button>

    </el-dialog>
  </div>
</template>
<script>
import * as Tool from "@/utils/tool";
import Searchbar from "@/components/Searchbar";
import * as $http from '@/api/rtePlan'
import * as $httpE from '@/api/entp'

import dayjs from 'dayjs'
export default {
  name: "",
  components: {
    Searchbar,
  },
  data: function () {
    return {
      tableHeight: 500,
      listLoading: false,
      list: [],
      dataForm: {},
      searchItems: {
        normal: [
          {
            name: "创建日期",
            field: "crtTm",
            type: "daterange",
            dbfield: "crt_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd"
          }
        ]
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      detailVisable: false,
      exportVisible: false,
      params: {
        date: '',
        entpName: ''
      },
      entpList: []
    };
  },
  created() {
    this.getEntp()

  },
  mounted: function () {
    window.addEventListener("resize", this.setTableHeight);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    exportFunc() {
      this.params.date = dayjs(this.params.date).format('YYYY')
      $http.rteplanStatExport(this.params)
        .then((res) => {
          // let a = document.createElement("a");
          // let blob = new Blob([res]);
          // let url = window.URL.createObjectURL(blob);
          // a.href = url;
          // a.download = this.params.entpName + '_' + this.params.date + ".xlsx";
          // a.click();
          // window.URL.revokeObjectURL(url);
          if (res.code == 0) {
            this.$message.success('导出成功，请到列表页面点击下载')
            this.getList();
            this.exportVisible = false
          }
        })
        .catch(err => { });
    },
    async getEntp() {
      //获取企业列表
      let filters = {
        groupOp: "AND",
        rules: [],
      };
      // filters.rules.push();
      let params = Object.assign(
        {},
        { filters: filters },
        { limit: 999, page: 1 }
      );
      // console.log(params);
      $httpE
        .getEntpList(params)
        .then((response) => {
          if (response.code == 0) {
            let options = response.page.list.map((item, index) => {
              return { label: item.entpName, value: item.entpName };
            });
            let search = {
              name: "企业名称",
              field: "entpNm",
              type: "filterselect",
              dbfield: "entp_nm",
              dboper: "eq",
              options: options,
            }
            this.searchItems.normal.push(search)
            this.entpList = options
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    exportList() {
      this.exportVisible = true
      this.params = {
        date: '',
        entpName: ''
      }
    },


    download(item) {
      if (!item.fileUrl) return;
      window.open(item.fileUrl, "_blank");
    },
    getList: function (data, sortParam) {

      let _this = this;
      this.listLoading = true;
      sortParam = sortParam || {};

      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      delete param.total;
      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getRteplanStatList(param)
        .then((response) => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.pagination.total = response.page.totalCount;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 220 - this.$refs.searchbar.$el.offsetHeight;
      });
    },
    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    detail(id) {
      this.detailVisable = true;
      $http
        .readReceive(id)
        .then((response) => {
          if (response.code == 0) {
            this.getList()
          } else {
          }
        })
        .catch(error => {
          console.log(error);

        });
      this.$nextTick(() => {
        this.$refs.receiveDetail.init(id);
      });
    }
  }
};
</script>

<style  scoped>
::v-deep .el-badge__content.is-fixed {
  top: 2px;
}
</style>
