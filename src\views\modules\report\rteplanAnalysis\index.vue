<template>
  <div class="app-main-content">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="运输企业统计" name="entp">
        <entp v-if="activeName == 'entp'"></entp>
      </el-tab-pane>
      <el-tab-pane label="车辆统计" name="vec">
        <send v-if="activeName == 'vec'"></send>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import entp from './entp.vue'
import vec from './vec.vue'
export default {
  name: "vecTabs",
  components: {
    entp,
    vec,
  },
  data() {
    return {
      activeName: "entp",
    };
  },
  methods: {
    handleClick(tab, event) {
    },
  },
};
</script>
