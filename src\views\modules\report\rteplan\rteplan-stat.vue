<template>
    <div class="detail-container chart-container">
        <el-row :gutter="10">
            <el-col :sm="12">
                <!-- 车辆进出镇海统计 -->
                <el-card class="box-card">
                    <el-col :sm="6">
                        <pie-comp :id="attendanceChart.id" ref="attendanceChart" :styles='attendanceChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="6">
                        <pie-comp :id="resumptionChart.id" ref="resumptionChart" :styles='resumptionChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="6">
                        <pie-comp :id="disciplineChart.id" ref="disciplineChart" :styles='disciplineChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="6">
                        <pie-comp :id="notLocVecOutNum.id" ref="notLocVecOutNum" :styles='notLocVecOutNum.styles'></pie-comp>
                    </el-col>
                </el-card>
            </el-col>
            <el-col :sm="12">
                <!-- 电子运单统计 -->
                <el-card class="box-card">
                    <el-col :sm="5">
                        <pie-comp :id="rteplanNum.id" ref="rteplanNum" :styles='rteplanNum.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="5">
                        <pie-comp :id="rteplanTotalQtyChart.id" ref="rteplanTotalQtyChart" :styles='rteplanTotalQtyChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="5">
                        <pie-comp :id="yzdTotalQtyChart.id" ref="yzdTotalQtyChart" :styles='yzdTotalQtyChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="5">
                        <pie-comp :id="yzbTotalQtyChart.id" ref="yzbTotalQtyChart" :styles='yzbTotalQtyChart.styles'></pie-comp>
                    </el-col>
                    <el-col :sm="4">
                        <pie-comp :id="zdTotalQtyChart.id" ref="zdTotalQtyChart" :styles='zdTotalQtyChart.styles'></pie-comp>
                    </el-col>
                </el-card>
            </el-col>
        </el-row>
         <el-row :gutter="10">
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- 进出镇海统计 -->
                    <db-bar-comp :id="inoutVecChart.id" ref="inoutVecChart" :styles='inoutVecChart.styles'></db-bar-comp>
                </el-card>
            </el-col>
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- 镇海区货物类别统计 -->
                    <pie-comp :id="rtePlanGoodsType.id" ref="rtePlanGoodsType" :styles='rtePlanGoodsType.styles'></pie-comp>
                </el-card>
            </el-col>
        </el-row>
         <el-row :gutter="10">
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- 镇海区装运货物情况统计 -->
                    <bar-comp :id="rtePlanGoodsChart.id" ref="rtePlanGoodsChart" :styles='rtePlanGoodsChart.styles'></bar-comp>
                </el-card>
            </el-col>
            <el-col :sm="12">
                <el-card class="box-card">
                    <!-- //镇海区装运车次统计 -->
                    <bar-comp :id="rtePlanVecDespChart.id" ref="rtePlanVecDespChart" :styles='rtePlanVecDespChart.styles'></bar-comp>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>
<script>
    import PieComp from "@/views/modules/report/charts/pie-comp"
	import BarComp from "@/views/modules/report/charts/bar-comp"
    import DbBarComp from "@/views/modules/report/charts/dbbar-comp"
    import * as Tool from "@/utils/tool"
    import * as $http from "@/api/stati"
    export default{
        name:'rteplanStat',
        components:{
            PieComp,
            BarComp,
            DbBarComp
        },
        data(){
            let pieCommonStyle = {
				"width":"107px",
				"height":"107px"
			}
            return {
                //车辆进出镇海统计
                attendanceChart:{
                    id:"attendanceChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"本地车进",
							top:"-5px"
						},
						color:["#1c8fd8"],
						series:[{
							data:[{value:145005, name:'145005'}]
						}]
					}
                },
                resumptionChart:{
                    id:"resumptionChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"本地车出",
							top:"-5px"
						},
						color:["#1c8fd8"],
						series:[{
							data:[{value:142371, name:'142371'}]
						}]
					}
                },
                disciplineChart:{
                    id:"disciplineChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"非本地车进",
							top:"-5px"
						},
						color:["#1c8fd8"],
						series:[{
							data:[{value:581699, name:'581699'}]
						}]
					}
                },
                notLocVecOutNum:{
                    id:"notLocVecOutNum",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"非本地车出",
							top:"-5px"
						},
						color:["#1c8fd8"],
						series:[{
							data:[{value:590850, name:'590850'}]
						}]
					}
                },
                //电子运单统计
                 rteplanNum:{
                    id:"rteplanNum",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"电子路单总数",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:453643, name:'453643'}]
						}]
					}
                },
                rteplanTotalQtyChart:{
                    id:"rteplanTotalQtyChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"货物总量(万吨)",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:1191.44, name:'1191.44'}]
						}]
					}
                },
                yzdTotalQtyChart:{
                    id:"yzdTotalQtyChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"易制毒(万吨)",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:68.79, name:'68.79'}]
						}]
					}
                },
                yzbTotalQtyChart:{
                    id:"yzbTotalQtyChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"易制爆(万吨)",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:24.76, name:'24.76'}]
						}]
					}
                },
                zdTotalQtyChart:{
                    id:"zdTotalQtyChart",
					styles:Object.assign({},pieCommonStyle),
					options:{
						title:{
							text:"重点货物(万吨)",
							top:"-5px"
						},
						color:["#ebd20d"],
						series:[{
							data:[{value:63.89, name:'63.89'}]
						}]
					}
                },
                //进出镇海统计
                inoutVecChart:{
                    id:"inoutVecChart",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
                        color:['#ffbb2c', '#8360d9', '#8360d9', '#66ff00'],
                        title:{
                            text:"进出镇海",
							textStyle:{
								color:'#fff'
							}
                        },
                        legend: {
                            data: [{
                                name: '进镇海车次'
                            }, {
                                name: '出镇海车次'
                            }],
                            textStyle:{
                                color:'#fff'
                            }
                        },
                        yAxis : [
                            {
                                axisLine:{
                                    show:true,
                                    lineStyle:{
                                        color:"#fff"
                                    }
                                }
                            }
                        ],
                        xAxis: [{
                            type: 'category',
                            data: [],
                            axisLine: {
                                lineStyle: {
                                    width: 2,
                                    color:'#fff'
                                }
                            },
                            splitLine: {show: false},
                            axisTick: {
                                alignWithLabel: true
                            }
                        }],
						color:["#ffaabd"],
						series: [{
                            name: '进镇海车次',
                            type: 'bar',
                            data: []
                        }, {
                            name: '出镇海车次',
                            type: 'bar',
                            data: []
                        }]
					}
                },
                //镇海区货物类别统计
                rtePlanGoodsType:{
                    id:"rtePlanGoodsType",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
						title:{
							text:"镇海区货物类别统计"
						},
						color:['#fff100', '#63c727', '#1fa9f4', '#e9733f']
					}
                },
                //镇海区装运货物情况统计
                rtePlanGoodsChart:{
                    id:"rtePlanGoodsChart",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
                        color:["#03b4f8"],
						title:{
							text:"镇海区装运货物情况统计"
						}
					}
                },
                //镇海区装运车次统计
                rtePlanVecDespChart:{
                    id:"rtePlanVecDespChart",
					styles:{
                        width:"100%",
                        height:"320px"
                    },
					options:{
                        color:["#fbe603"],
						title:{
							text:"镇海区装运车次统计"
						}
					}
                }
            }
        },
        created() {
            var _this = this;
            /*
             *进出镇海 
             */
            getInoutCnt();
            function getInoutCnt(){
                    var results = [
                        {
                            "date": "2017-01",
                            "inLocalVecCnt": 11378,
                            "outLocalVecCnt": 11008,
                            "inNotLocalVecCnt": 38093,
                            "outNotLocalVecCnt": 38284
                        },
                        {
                            "date": "2017-02",
                            "inLocalVecCnt": 10732,
                            "outLocalVecCnt": 10660,
                            "inNotLocalVecCnt": 37652,
                            "outNotLocalVecCnt": 37974
                        },
                        {
                            "date": "2017-03",
                            "inLocalVecCnt": 14000,
                            "outLocalVecCnt": 13830,
                            "inNotLocalVecCnt": 50623,
                            "outNotLocalVecCnt": 51336
                        },
                        {
                            "date": "2017-04",
                            "inLocalVecCnt": 12258,
                            "outLocalVecCnt": 11970,
                            "inNotLocalVecCnt": 49406,
                            "outNotLocalVecCnt": 50105
                        },
                        {
                            "date": "2017-05",
                            "inLocalVecCnt": 12667,
                            "outLocalVecCnt": 12617,
                            "inNotLocalVecCnt": 51378,
                            "outNotLocalVecCnt": 52090
                        },
                        {
                            "date": "2017-06",
                            "inLocalVecCnt": 12333,
                            "outLocalVecCnt": 12119,
                            "inNotLocalVecCnt": 52992,
                            "outNotLocalVecCnt": 53878
                        },
                        {
                            "date": "2017-07",
                            "inLocalVecCnt": 12589,
                            "outLocalVecCnt": 12715,
                            "inNotLocalVecCnt": 51882,
                            "outNotLocalVecCnt": 53109
                        },
                        {
                            "date": "2017-08",
                            "inLocalVecCnt": 11475,
                            "outLocalVecCnt": 11362,
                            "inNotLocalVecCnt": 48660,
                            "outNotLocalVecCnt": 49477
                        },
                        {
                            "date": "2017-09",
                            "inLocalVecCnt": 12056,
                            "outLocalVecCnt": 11473,
                            "inNotLocalVecCnt": 51926,
                            "outNotLocalVecCnt": 52290
                        },
                        {
                            "date": "2017-10",
                            "inLocalVecCnt": 11025,
                            "outLocalVecCnt": 10746,
                            "inNotLocalVecCnt": 47425,
                            "outNotLocalVecCnt": 48546
                        },
                        {
                            "date": "2017-11",
                            "inLocalVecCnt": 11340,
                            "outLocalVecCnt": 11067,
                            "inNotLocalVecCnt": 48227,
                            "outNotLocalVecCnt": 49356
                        },
                        {
                            "date": "2017-12",
                            "inLocalVecCnt": 13152,
                            "outLocalVecCnt": 12804,
                            "inNotLocalVecCnt": 53435,
                            "outNotLocalVecCnt": 54405
                        },
                        {
                            "date": "2018-01",
                            "inLocalVecCnt": 13314,
                            "outLocalVecCnt": 12994,
                            "inNotLocalVecCnt": 51937,
                            "outNotLocalVecCnt": 52539
                        },
                        {
                            "date": "2018-02",
                            "inLocalVecCnt": 2800,
                            "outLocalVecCnt": 2771,
                            "inNotLocalVecCnt": 41580,
                            "outNotLocalVecCnt": 40645
                        }
                    ]
                    var dis = [],
                        inVehicle = [],
                        outVehicle = [];
                    
                    results.filter(item => {
                        dis.push(item.date);
                        outVehicle.push(item.outLocalVecCnt+item.outNotLocalVecCnt);
                        inVehicle.push(item.inLocalVecCnt+item.inNotLocalVecCnt);
                    });

                    //设置坐标数据
                    _this.inoutVecChart.options.xAxis[0].data = dis;
                    //进镇海数据
                    _this.inoutVecChart.options.series[0].data = inVehicle;
                    //出镇海数据
                    _this.inoutVecChart.options.series[1].data = outVehicle;
               
                }
            /*
             *镇海区货物类别 
             */
            getInAndOutCntByMonth();
            function getInAndOutCntByMonth(){
                        var names = [];
                        var data = [];
                        var result = [{"key":".","value":24.500000},{"key":"0","value":66063.250000},{"key":"2","value":4518388.346000},{"key":"3","value":11048153.402000},{"key":"4","value":282295.102000},{"key":"5","value":158250.106000},{"key":"6","value":886811.180000},{"key":"8","value":3680232.068000},{"key":"9","value":4170.220000}]

                        result.filter( (obj,index) =>{
                            var name="";
                            if(obj.key==1){
                                name="一类";
                            }
                            if(obj.key==2){
                                name="二类";
                            }
                            if(obj.key==3){
                                name="三类";
                            }
                            if(obj.key==4){
                                name="四类";
                            }
                            if(obj.key==5){
                                name="五类";
                            }
                            if(obj.key==6){
                                name="六类";
                            }
                            if(obj.key==7){
                                name="七类";
                            }
                            if(obj.key==8){
                                name="八类";
                            }
                            if(obj.key==9){
                                name="九类";
                            }
                            names.push(name);
                            data.push({"value": (obj.value/10000).toFixed(2), "name": name});
                        });

                        var documentSum = {
                            legend: {
                                orient:"horizontal",
                                data:names,
                                bottom:10
                            },
                            tooltip:{
                                show:true,
                                trigger: 'item',
                                formatter: "{b}:{c}"
                            },
                            color: ['#fff100', '#63c727', '#1fa9f4', '#e9733f'],
                            series:[{
                                label: {
                                    normal: {
                                        position: 'outeside',
                                        formatter: "{b}\n{c}\n({d}%)"
                                    }
                                },
                                labelLine: {
                                    normal: {
                                        show: true
                                    }
                                },
                                data: data
                            }],
                            radius: ['40%', '60%'],
                            center: ["50%", "50%"],
                            toolTip:{
                                formatter: "{b}\n{c}万吨\n({d}%)"
                            }
                        };
                        _this.rtePlanGoodsType.options = Tool.extendObj(true,_this.rtePlanGoodsType.options,documentSum);
                        
                }

            /*
             *镇海区装运货物情况统计 
             */
            getMonthRteplanGoodsQty();
            function getMonthRteplanGoodsQty(){
                var xdata = [],ydata = [],total=0,
                result = [{"key":"2017-06","value":38819},{"key":"2017-07","value":41053},{"key":"2017-08","value":42045},{"key":"2017-09","value":38958},{"key":"2017-10","value":35234},{"key":"2017-11","value":34623},{"key":"2017-12","value":40432},{"key":"2018-01","value":44299},{"key":"2018-02","value":30356},{"key":"2018-03","value":45578},{"key":"2018-04","value":42826}];
                result.filter( (obj,index) => {
                    xdata.push(obj.key);
                    ydata.push( (obj.value).toFixed(2));
                    total+= obj.value;
                });
                var firstData = {
                    title:{
                        text:"镇海区装运货物情况统计",
                        subtext:'总量：'+total.toFixed(2)+'万吨',
                        subtextStyle: {
                            color: '#03b8fd',
                            fontSize: 12,
                            fontWeight: 'normal'
                        }
                    },
                    series:[{
                        data:ydata
                    }],
                    xAxis:[{
                        data:xdata
                    }]/* ,
                    unit: "万吨",
                    xName: '时间',
                    yName: '数量' */
                }
                _this.rtePlanGoodsChart.options = Tool.extendObj(true,_this.rtePlanGoodsChart.options,firstData);
            }

            /*
             *镇海区装运车次统计 
             */
            getMonthRteplanVecDesp();
            function getMonthRteplanVecDesp(){
                var xdata = [],ydata = [],total=0,
                result = [{"key":"2017-06","value":101.935502},{"key":"2017-07","value":109.747205},{"key":"2017-08","value":115.569735},{"key":"2017-09","value":102.184390},{"key":"2017-10","value":92.005159},{"key":"2017-11","value":88.970309},{"key":"2017-12","value":106.892536},{"key":"2018-01","value":122.163355},{"key":"2018-02","value":102.206563},{"key":"2018-03","value":122.921171},{"key":"2018-04","value":117.623585}];
                result.filter((obj,index) => {
                    xdata.push(obj.key);
                    ydata.push(obj.value);
                    total+= obj.value;
                });
                var firstData = {
                    title:{
                        text:"镇海区装运车次统计",
                        subtext:'总量：'+total.toFixed(0)+' 车',
                        textStyle:{
                            color:'#fff'
                        },
                        subtextStyle: {
                            color: '#fff',
                            fontSize: 12,
                            fontWeight: 'normal'
                        }
                    },
                    series:[{
                        data:ydata
                    }],
                    xAxis:[{
                        data:xdata
                    }],
                    unit: "车",
                    xName: '时间',
                    yName: '数量'
                };

                _this.rtePlanVecDespChart.options = Tool.extendObj(true,_this.rtePlanVecDespChart.options,firstData);
            }
        },
        mounted() {
            //车辆进出镇海统计
			this.$refs.attendanceChart.setInstanceOption(this.attendanceChart.options);
			this.$refs.resumptionChart.setInstanceOption(this.resumptionChart.options);
			this.$refs.disciplineChart.setInstanceOption(this.disciplineChart.options);
            this.$refs.notLocVecOutNum.setInstanceOption(this.notLocVecOutNum.options);
            
            //电子运单统计
			this.$refs.rteplanNum.setInstanceOption(this.rteplanNum.options);
			this.$refs.rteplanTotalQtyChart.setInstanceOption(this.rteplanTotalQtyChart.options);
			this.$refs.yzdTotalQtyChart.setInstanceOption(this.yzdTotalQtyChart.options);
			this.$refs.yzbTotalQtyChart.setInstanceOption(this.yzbTotalQtyChart.options);
            this.$refs.zdTotalQtyChart.setInstanceOption(this.zdTotalQtyChart.options);
            
            //进出镇海统计
            this.$refs.inoutVecChart.setInstanceOption(this.inoutVecChart.options);

             //镇海区货物类别统计
            this.$refs.rtePlanGoodsType.setInstanceOption(this.rtePlanGoodsType.options);

            //镇海区装运货物情况统计
            this.$refs.rtePlanGoodsChart.setInstanceOption(this.rtePlanGoodsChart.options);

             //镇海区装运车次统计
            this.$refs.rtePlanVecDespChart.setInstanceOption(this.rtePlanVecDespChart.options);

        },
        methods:{

        }
    }
</script>
<style >
	.box-card{
		margin-bottom: 10px;
	}
    .chart-container{
		background: url('../../../../assets/dashboard-img/backgroundimage.v1.2.jpg');
		background-size: 100% 100%;
	}
	.box-card{
		background: none;
		border:none;
	}
	.box-card .el-card__body{
		background: rgba(41, 54, 93, 0.52);
		border: 1px solid #43738e;
        border-top: 1px solid #43738e;
        overflow: hidden;
	}
	.box-card .el-card__header{
		background: rgba(72, 153, 241, 0.25);
    	border: 1px solid #43738e;
		color:#fff;
	}
</style>
