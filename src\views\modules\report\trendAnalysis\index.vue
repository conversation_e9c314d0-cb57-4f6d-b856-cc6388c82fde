<template>
  <div class="detail-container chart-container">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="module" id="stop" style="position: relative">
          <div class="title" style="margin-bottom: 5px; display: inline-block">
            停车热力图
          </div>
          <!-- <div
            class="ele-out"
            style="right: 230px"
            :class="{ active: hotActive }"
            @click="hotActive = !hotActive"
          >
            热力点位
          </div>
          <div
            class="ele-out"
            :class="{ active: outActive }"
            @click="outActive = !outActive"
          >
            电子围栏
          </div> -->
          <!-- <el-select
            v-if="outActive"
            v-model="selectOut"
            :remote-method="querySearchOut"
            filterable
            remote
            placeholder="请输入企业名称搜索围栏"
            size="small"
            clearable
            required
            @change="outSelectHandle"
            class="ele-out"
            style="top: 40px; right: 10px; z-index: 99; width: 200px"
          >
            <el-option
              v-for="(item, index) in outOptions"
              :key="index"
              :label="item.name"
              :value="index"
            ></el-option>
          </el-select> -->
          <div class="stopDays">
            <div
              class="stopDaysItem"
              :class="[stopDays == 'week' ? 'active' : '']"
              @click="stopDaysChange('week')"
            >
              7天
            </div>
            <div
              class="stopDaysItem"
              :class="[stopDays == 'month' ? 'active' : '']"
              @click="stopDaysChange('month')"
            >
              一个月
            </div>
          </div>
          <div
            id="stopMap"
            ref="echartsStop"
            :style="{ height: contHeight - 140 + 'px' }"
          ></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="module">
          <div class="title" style="margin-bottom: 5px; display: inline-block">
            超速热力图
          </div>
          <div class="stopDays">
            <div
              class="stopDaysItem"
              :class="[speedDays == 'week' ? 'active' : '']"
              @click="speedDaysChange('week')"
            >
              7天
            </div>
            <div
              class="stopDaysItem"
              :class="[speedDays == 'month' ? 'active' : '']"
              @click="speedDaysChange('month')"
            >
              一个月
            </div>
          </div>
          <div
            id="speedMap"
            ref="echartsSpeed"
            :style="{ height: contHeight - 140 + 'px' }"
          ></div>
        </div>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="20">
      <el-col :span="24">
        <div class="module-tile module">
          <el-radio-group
            v-model="radio3"
            :disabled="loading1"
            @change="change"
          >
            <el-radio-button label="超速报警"></el-radio-button>
            <el-radio-button label="疲劳驾驶报警"></el-radio-button>
            <el-radio-button label="闯禁行报警"></el-radio-button>
            <el-radio-button label="未登记报警"></el-radio-button>
            <el-radio-button label="无电子运单报警"></el-radio-button>
            <el-radio-button label="卫星定位异常报警"></el-radio-button>
            <el-radio-button label="超载报警"></el-radio-button>
            <el-radio-button label="超经营范围报警"></el-radio-button>
          </el-radio-group>
          <el-radio-group v-model="typeValue" size="mini" @change="typeChange">
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
          </el-radio-group>
        </div>
        <div
          class="module"
          v-loading="loading1"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        >
          <div class="title" style="margin-bottom: 5px">{{ radio3 }}</div>
          <line-echarts
            :labelShow="labelShow"
            :echartsHeight="'200px'"
            ref="TrendChart1"
          ></line-echarts>
        </div>
      </el-col>
    </el-row> -->
  </div>
</template>
<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/trendAnalysis";
// import lineEcharts from "@/components/Echarts/line-echarts"; // 折线图
import "echarts/extension/bmap/bmap";
import BMapStyle from "./mapStyleJSON.json";
export default {
  name: "trendSta",
  components: {
    // lineEcharts
  },
  mounted() {
    //加载停车热力图
    this._initStopRoute();
    //加载超速热力图
    this._initSpeedRoute();
    // 围栏
    // this.getOutList();
  },
  data() {
    return {
      contHeight: Tool.getClientHeight(),
      myChart: null,
      map: null,
      option: null,
      myChart1: null,
      option1: null,
      loading1: true,
      radio3: "超速报警",
      CanvasOver: null,
      // 控制折现的文字显示
      labelShow: false,
      //echarts缓存数据，第一次向服务器请求数据，后续从这里取
      echartsData: {
        over_speed_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        tired_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        no_through_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        no_record_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        no_rteplan_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        no_gps_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        ab_offline_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        over_load_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        },
        over_business_cnt: {
          day: {
            categoriesData: [],
            seriesData: []
          },
          month: {
            categoriesData: [],
            seriesData: []
          }
        }
      },
      stopDays: "week", //停车热力图显示天数
      speedDays: "week", //超速热力图显示天数
      outList: [],
      outActive: false,
      outbmap: null,
      outPoint: [],
      hotActive: true,
      stopPoints: [],
      selectOut: "",
      outOptions: [],
      selectivePoints: null,
      selectiveName: "",
      selectivePly: null,
      selectiveMarker: null,

      typeValue: "day"
    };
  },
  watch: {
    outActive() {
      if (this.outActive) {
        this.setOut();
      } else {
        this.removeOut();
      }
    },
    hotActive() {
      if (this.hotActive) {
        this.setStopPoints(this.stopPoints);
      } else {
        this.setStopPoints([]);
      }
    },
    selectivePoints() {
      let point = this.getCenterPoint(this.selectivePoints);
      let label = new BMap.Label(`${this.selectiveName}`, {
        offset: new BMap.Size(-40, -25),
        position: point
      });
      label.setStyle({
        backgroundColor: "rgba(0,0,0,0)",
        border: "none",
        color: "red",
        fontWeight: "bold"
      });
      this.selectiveMarker = new BMap.Marker(point);
      this.selectiveMarker.setLabel(label);
      this.outbmap.addOverlay(this.selectiveMarker);
    }
  },
  methods: {
    _initSpeedRoute() {
      const loading = this.$loading({
        lock: true,
        text: "正在加载中，请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.8)"
      });
      $http
        .alarmspeedthermal({ type: this.speedDays })
        .then(res => {
          //超速
          // let points = [[{"coord":[120.14322240845,30.236064370321],"elevation":10001},{"coord":[120.14322240845,30.236064370321],"elevation":21}]]
          if (res.code == 0 && res.data) {
            // let points = [[]];
            // res.data.forEach(item => {
            //   points[0].push({
            //     coord: [item.lng, item.lat],
            //     elevation: item.count
            //   });
            // });
            // points = [].concat.apply(
            //   [],
            //   points.map(function(track) {
            //     return track.map(function(seg) {
            //       return seg.coord.concat([1]);
            //     });
            //   })
            // );
            // this.SpeedOption(points);
            this.SpeedOption(res.data);
            loading.close();
          }
        })
        .catch(error => {
          loading.close();
          console.log(error);
        });
    },
    SpeedOption(points) {
      var map = new BMap.Map("speedMap", { minZoom: 8, maxZoom: 18 });
      map.centerAndZoom(new BMap.Point(121.66386, 30.001186), 11);
      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP]
        })
      );
      map.setMapStyleV2({
        styleJson: BMapStyle
      });
      map.enableScrollWheelZoom();
      let heatmapOverlay = new BMapLib.HeatmapOverlay({
        radius: 8,
        visible: true,
        opacity: 70
      });
      map.addOverlay(heatmapOverlay);
      heatmapOverlay.setDataSet({ data: points, max: 3 });

      // this.myChart = this.$echarts.init(this.$refs.echartsSpeed);
      // this.option = {
      //   animation: false,
      //   bmap: {
      //     center: [121.66386, 30.001186],
      //     zoom: 10,
      //     roam: true
      //   },
      //   visualMap: {
      //     show: false,
      //     top: "top",
      //     min: 0,
      //     max: 5,
      //     seriesIndex: 0,
      //     calculable: true,
      //     inRange: {
      //       color: ["blue", "blue", "green", "yellow", "red"]
      //     }
      //   },
      //   series: [
      //     {
      //       type: "heatmap",
      //       coordinateSystem: "bmap",
      //       data: points,
      //       pointSize: 5,
      //       blurSize: 6
      //     }
      //   ]
      // };
      // this.myChart.setOption(this.option);
      // 从echarts对象中获取bmap对象
      // var bmap = this.myChart
      //   .getModel()
      //   .getComponent("bmap")
      //   .getBMap();
      // bmap.setMapStyle({ style: "midnight" });
      // bmap.setMapStyleV2({
      //     styleJson: BMapStyleV2
      // })
    },
    _initStopRoute() {
      const loading = this.$loading({
        lock: true,
        text: "正在加载中，请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.8)"
      });
      $http
        .alarmstopthermal({ type: this.stopDays })
        .then(res => {
          //停车
          if (res.code == 0 && res.data) {
            // let points = [[]];
            // res.data.forEach(item => {
            //   points[0].push({
            //     coord: [item.lng, item.lat],
            //     elevation: item.count
            //   });
            // });
            // points = [].concat.apply(
            //   [],
            //   points.map(function(track) {
            //     return track.map(function(seg) {
            //       return seg.coord.concat([1]);
            //     });
            //   })
            // );
            // console.log(points);
            // this.StopOption(points);
            this.StopOption(res.data);
          }
          loading.close();
        })
        .catch(error => {
          loading.close();
          console.log(error);
        });
    },
    StopOption(points) {
      var map = new BMap.Map("stopMap", { minZoom: 8, maxZoom: 18 });
      map.centerAndZoom(new BMap.Point(121.66386, 30.001186), 11);
      map.addControl(
        new BMap.MapTypeControl({
          mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP]
        })
      );
      map.setMapStyleV2({
        styleJson: BMapStyle
      });
      map.enableScrollWheelZoom();
      let heatmapOverlay = new BMapLib.HeatmapOverlay({
        radius: 8,
        visible: true,
        opacity: 70
      });
      map.addOverlay(heatmapOverlay);
      heatmapOverlay.setDataSet({ data: points, max: 3 });
      // heatmapOverlay.setDataSet(points);

      // this.stopPoints = points;
      // this.myChart1 = this.$echarts.init(this.$refs.echartsStop);
      // this.option1 = {
      //   animation: false,
      //   bmap: {
      //     center: [121.66386, 30.001186],
      //     zoom: 10,
      //     roam: true
      //   },
      //   visualMap: {
      //     show: false,
      //     top: "top",
      //     min: 0,
      //     max: 5,
      //     seriesIndex: 0,
      //     calculable: true,
      //     inRange: {
      //       color: ["blue", "blue", "green", "yellow", "red"]
      //     }
      //   },
      //   series: [
      //     {
      //       type: "heatmap",
      //       coordinateSystem: "bmap",
      //       data: points,
      //       pointSize: 5,
      //       blurSize: 6
      //     }
      //   ]
      // };
      // this.myChart1.setOption(this.option1);
      // this.outbmap = this.myChart1
      //   .getModel()
      //   .getComponent("bmap")
      //   .getBMap();
      // this.outbmap.setMapStyle({ style: "midnight" });
    },
    setStopPoints(p) {
      this.myChart1.setOption({
        series: [
          {
            type: "heatmap",
            coordinateSystem: "bmap",
            data: p,
            pointSize: 5,
            blurSize: 6
          }
        ]
      });
    },

    //停车热力图天数切换
    stopDaysChange(type) {
      this.stopDays = type;
      this._initStopRoute();
    },
    speedDaysChange(type) {
      this.speedDays = type;
      this._initSpeedRoute();
    },
    async getOutList() {
      const res = await $http.getAllOutList();
      if (res.code === 0) {
        this.outList = res.data;
        this.outOptions = res.data;
      }
    },
    setOut() {
      this.outList.forEach(item => {
        if ([4, 6].includes(item.type)) {
          try {
            let t = "";
            const d = JSON.parse(item.bdLnglat);
            d.forEach(ite => {
              t += `${ite.lng},${ite.lat};`;
            });
            const ply = new BMap.Polygon(t, {
              strokeWeight: 2,
              strokeColor: "#fff"
            });
            this.outPoint.push(ply);
            this.outbmap.addOverlay(ply);
          } catch (e) {}
        }
      });
    },
    removeOut() {
      this.outPoint.forEach(item => {
        this.outbmap.removeOverlay(item);
      });
      this.outPoint = [];
    },
    querySearchOut(e) {
      this.outOptions = this.outList.filter(item => item.name.includes(e));
    },
    outSelectHandle(e) {
      const d = this.outOptions[e];
      if (!d) return;
      try {
        const points = (JSON.parse(d.bdLnglat) || []).map(item => ({
          lng: Number(item.lng),
          lat: Number(item.lat)
        }));
        if (points.length) {
          this.outbmap.removeOverlay(this.selectivePly); //
          this.outbmap.removeOverlay(this.selectiveMarker); //
          this.selectiveName = d.name;
          this.selectivePoints = points;
          let pointArr = points.map(iitem => {
            return new BMap.Point(iitem.lng, iitem.lat);
          });
          this.outbmap.setViewport(pointArr);
          //
          let t = "";
          points.forEach(ite => {
            t += `${ite.lng},${ite.lat};`;
          });
          this.selectivePly = new BMap.Polygon(t, {
            strokeWeight: 2,
            strokeColor: "red"
          });
          this.outPoint.push(this.selectivePly);
          this.outbmap.addOverlay(this.selectivePly);
          //
        } else {
          this.$message({
            message: "当前企业无围栏数据",
            type: "warning"
          });
        }
      } catch (e) {}
    },
    getCenterPoint(path) {
      let x = 0.0;
      let y = 0.0;
      for (let i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;
      return new BMap.Point(x, y);
    }
  }
};
</script>
<style lang="scss" scoped>
.detail-container {
  height: calc(100vh - 50px);
  background: url("../../../../assets/dashboard-img/backgroundimage.v1.2.jpg");
  background-size: 100% 100%;

  .module-tile {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.module {
  padding: 10px;
  background: url("~@/assets/dashboard-img/border2.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  #stopMap {
    width: 100%;
  }
  #speedMap {
    width: 100%;
  }
}
.title {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
}
.isChart {
  overflow: hidden;
}
.stopDays {
  float: right;
  margin-bottom: 5px;
  position: relative;
  display: inline-flex;
  flex-direction: row;
  margin-left: 10px;
  border-radius: 3px;
  box-shadow: 0px 1px 4px #828282;

  .stopDaysItem {
    width: 60px;
    line-height: 23px;
    font-size: 14px;
    color: #333;
    background: #fff;
    text-align: center;
    cursor: pointer;
    position: relative;
    border-right: 1px solid #cccccc;

    &.active {
      background: #85a7dc;
      color: #fff;
    }
    &:first-child::after {
      content: "";
      width: 1px;
      height: 100%;
      position: absolute;
      right: -0.5px;
      top: 0px;
      background: #b7b7b7;
    }
  }
}
.ele-out {
  position: absolute;
  right: 140px;
  top: 10px;
  width: 80px;
  line-height: 23px;
  font-size: 14px;
  color: #333;
  background: #fff;
  text-align: center;
  cursor: pointer;
  box-shadow: 0px 1px 4px #828282;
  & .active {
    background: #85a7dc;
    color: #fff;
  }
}
</style>
