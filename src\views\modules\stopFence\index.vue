<template>
  <div class="detail-container" v-loading="loading">
    <div id="locMap" v-bind:style="{'height':maxHeight+'px'}"></div>
    <div class="refresh-tooltip" v-show="refreshNextTime>0">{{refreshNextTime}}秒后刷新</div>
    <el-card class="box-card draw-panel" :body-style="{'background':'#ffffff','border':'none'}">
      <el-form :inline="true" class="demo-form-inline">
        <!-- <el-form-item>
          <el-select size="small" v-model="searchNm" placeholder="请输入企业名称" filterable clearable >
            <el-option v-for="op in options" :key="op.entpPk" :label="op.entpNm" :value="op.entpPk"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-input size="small" type="text" suffix-icon="el-icon-search" placeholder="请输入要查询的企业名称" v-model="searchNm" @input="fuzzySearch"></el-input>
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="searchLocal">搜索</el-button>
        </el-form-item> -->
      </el-form>
      <!-- <div style="line-height:24px;">
        <div>停车场总数：{{stopSum}}</div>
        <div>超限停车场数：{{overrun}}</div>
        <div>有重车停车场数：{{heavyCarsum}}</div>
      </div>-->
      <ul class="parking-list">
        <!-- <li v-for="op in this[this.typeOfTheShow]"
            :key="op.id"
            @click="searchLocal(op.id)"
            v-show="!op.visible"
            :class='[ op.nowCarsum > op.maxCarsum ? "danger" : op.heavyCarsum > 0? "warn": "normal"]'
        >{{op.name}}</li> -->
        <li v-for="op in this[this.typeOfTheShow]" :key="op.id" @click="searchLocal(op.id)" v-show="!op.visible" :class='[op.heavyCarsum > 0? "warn": "normal"]'>{{op.name}}</li>
      </ul>
    </el-card>
    <div class="vec-total-wape">
      <el-button class="vec-total-btn-all" :class="[typeOfTheShow==='stop'?'active':'']" @click="displayMarkerByType('stop')">停车场总数 {{stop.length}}</el-button>
      <!-- <el-button
        class="vec-total-btn"
        :class="[typeOfTheShow==='overrun'?'active':'']"
        @click="displayMarkerByType('overrun')"
      >超限停车场数 {{overrun.length}}</el-button> -->
      <el-button class="vec-total-btn-heavy" :class="[typeOfTheShow==='heavyCar'?'active':'']" @click="displayMarkerByType('heavyCar')">有重车停车场数 {{heavyCar.length}}</el-button>
      <el-button class="vec-total-btn-normal" :class="[typeOfTheShow==='normalCar'?'active':'']" @click="displayMarkerByType('normalCar')">正常停车场数 {{normalCar.length}}</el-button>
    </div>
    <div v-show="false">
      <info-window ref="InfoWindow" :selectInfo="selectInfo" @emeHandle="emeHandle"></info-window>
    </div>
    <div v-show="false">
      <monitor-info-window ref="monitorInfoWindow" :selectVecInfo="selectVecInfo" :rteplan="rteplan" @close="closeInfoBox"></monitor-info-window>
      <monitor-cp-info-window ref="monitorCpInfoWindow" :selectEntpInfo="selectEntpInfo" @close="closeInfoBox"></monitor-cp-info-window>
    </div>
    <el-dialog :title="chemName" :visible.sync="messageDialog" width="1000px">
      <chemica-info :isCompn="true" :messageIpPk="messageIpPk"></chemica-info>
    </el-dialog>
    <!-- <term ref="term" :posiStyle="termStyle" @loadStop="loadStop" @loadAreaEntp="loadAreaEntp" @loadAreaVec="loadAreaVec" @messageDialog="messageDialogHandle" @nearbyVec="nearbyVec" @poiList="poiList" @addPonit="addPonit"></term> -->
  </div>
</template>


<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/stopFence";
import * as $httpEme from "@/api/emergency";
import InfoWindow from "./info-window";
import MonitorCpInfoWindow from "../emergency/components/monitor-cp-info-window";
import MonitorInfoWindow from "../emergency/components/monitor-info-window";
import imgsConfigPark from "static/jsonConfig/parking.icon.json";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import HashMap from "@/utils/hashmap";
// import Term from "@/components/emergency/term";

export default {
  data() {
    return {
      map: null,
      infoWin: null,
      infobox: null,
      maxHeight: 500,
      loading: false,
      searchNm: "",

      refreshFlag: true, // 计时结束后是否请求的标志
      refreshTask: null, // 定时任务
      refreshNextTime: 0, // 地图刷新时间剩余时间（用于倒计时）
      refreshIntervalTime: 60, // 每次刷新间隔的时间 60秒

      selectInfo: {},

      stop: [],
      overrun: [],
      heavyCar: [],
      normalCar: [],
      typeOfTheShow: "stop",
      options: [],
      parkingMarkerList: new HashMap(),
      controlStyle: {
        position: "fixed",
        right: "30px",
        top: "300px"
      },
      termStyle: {
        position: "fixed",
        right: "30px",
        top: "100px"
      },
      messageDialog: false,
      messageIpPk: "",
      chemName: "基本信息", //mdsd信息弹框title

      selectVecInfo: { vehicleNo: "" }, // InfoBox:已选车辆信息
      selectEntpInfo: {}, // _InfoBox:已选企业信息
      rteplan: null // InfoBox：已选车辆电子路单信息
    };
  },
  components: {
    InfoWindow,
    MonitorInfoWindow,
    MonitorCpInfoWindow
    // Term
  },
  mounted() {
    let maxHeight = Tool.getClientHeight();
    let _this = this;
    this.maxHeight = maxHeight - 80;
    window.addEventListener("resize", function() {
      _this.maxHeight = Tool.getClientHeight() - 80;
    });
    this.$nextTick(() => {
      this.loadMap();
    });
  },
  destroyed() {
    this.stopRefreshTask();
  },
  methods: {
    fuzzySearch() {
      let searchNm = this.searchNm;
      this[this.typeOfTheShow].forEach(item => {
        if (item.entpNm&&item.entpNm.indexOf(searchNm) < 0) {
          this.$set(item, "visible", true);
        } else {
          this.$set(item, "visible", false);
        }
      });
    },
    loadMap() {
      let _this = this;
      let map = new BMap.Map("locMap", { projection: "EPSG:4326" });
      map.centerAndZoom(new BMap.Point(121.652673, 29.986648), 13); // 初始化地图,设置中心点坐标和地图级别
      map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

      this.map = map;
      //添加控件
      let control = new BMap.MapTypeControl({
        type: BMAP_MAPTYPE_CONTROL_MAP,
        mapTypes: [BMAP_SATELLITE_MAP, BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
      });
      control.setAnchor(BMAP_ANCHOR_BOTTOM_RIGHT);
      this.map.addControl(control); //地图类型;
      var config = {
        pageCapacity: 10, //每页显示的数量
        onSearchComplete: this.localSearchResult //接收数据的回调函数
      };
      //创建搜索对象
      this.local = new BMap.LocalSearch(this.map, config);
      this.getAreaList();

      this.map.addEventListener("dblclick", function(e) {
        _this.displayMarkerByType(_this.typeOfTheShow);
        // _this.$refs.term.termForm.lng = e.point.lng;
        // _this.$refs.term.termForm.lat = e.point.lat;
        // if (_this.$refs.term.termForm.type == "2") {
        //   var marker = new BMap.Marker(e.point); // 创建标注
        //   _this.map.addOverlay(marker); // 将标注添加到地图中
        // }
      });
    },

    getCenterOfGravityPoint(mPoints) {
      //计算多边形重心
      let area = 0.0; //多边形面积
      let Gx = 0.0,
        Gy = 0.0; // 重心的x、y

      for (let i = 1; i <= mPoints.length; i++) {
        let iLat = mPoints[i % mPoints.length].lat;
        let iLng = mPoints[i % mPoints.length].lng;
        let nextLat = mPoints[i - 1].lat;
        let nextLng = mPoints[i - 1].lng;
        let temp = (iLat * nextLng - iLng * nextLat) / 2.0;
        area += temp;
        Gx += (temp * (iLat + nextLat)) / 3.0;
        Gy += (temp * (iLng + nextLng)) / 3.0;
      }

      Gx = Gx / area;
      Gy = Gy / area;
      return { lat: Gx, lng: Gy };
    },

    getAreaList() {
      let _this = this;
      this.loading = true;
      this.clearOverLays();
      $http
        .getList()
        .then(res => {
          if (res.code == 0) {
            let data = res.data;
            this.overrun = [];
            this.heavyCar = [];
            this.normalCar=[];
            this.stop = data;
            this.options = this.stop;

            this.parkingMarkerList.clear();

            data.forEach((item, i) => {
              // if (item.nowCarsum > item.maxCarsum) {
              //   //超限
              //   _this.overrun.push(item);
              // }
              if (item.heavyCarsum) {
                _this.heavyCar.push(item);
              } else {
                _this.normalCar.push(item);
              }
              _this.drawPolygon(item);
            });
            if (this.infoWin) {
              this.infoWin.update();
              this.map.addOverlay(this.infoWin);
            }
            _this.refreshFlag = true;
            window.clearInterval(_this.refreshTask);
            _this.startRefreshTask();
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
        });
    },
    displayMarkerByType(type) {
      let _this = this;
      this.typeOfTheShow = type;
      this.options = this[type];
      this.searchNm = "";
      this.clearOverLays();
      this.parkingMarkerList.clear();
      this[type].forEach(item => {
        _this.drawPolygon(item);
      });
      this.fuzzySearch();
    },
    //画围栏
    drawPolygon(item) {
      let _this = this;
      let pointArr = item.lnglat.split(";").map(iitem => {
        let point = iitem.split(",");
        return new BMap.Point(point[0], point[1]);
      });
      let point = this.getCenterOfGravityPoint(pointArr);
      // this.addMarker(
      //   point,
      //   imgsConfigPark["parkinglot"][
      //     item.nowCarsum > item.maxCarsum
      //       ? "danger"
      //       : item.heavyCarsum > 0
      //       ? "warn"
      //       : "normal"
      //   ],
      //   item
      // );
      this.addMarker(
        point,
        imgsConfigPark["parkinglot"][item.heavyCarsum > 0 ? "parking_orange" : "parking_green"],
        item
      );

      // let polygon = new BMap.Polygon(pointArr, {
      //   strokeWeight: 2,
      //   // name: "disableMassClear",
      //   strokeColor:
      //     item.nowCarsum > item.maxCarsum
      //       ? "#e12828"
      //       : item.heavyCarsum > 0
      //       ? "#f07528"
      //       : "#4030df" //超限红色，有重车黄色，默认蓝色
      // });
      let polygon = new BMap.Polygon(pointArr, {
        strokeWeight: 2,
        // name: "disableMassClear",
        strokeColor: item.heavyCarsum > 0 ? "#f07528" : "#449d44" //有重车橙色，默认绿色
      });
      _this.map.addOverlay(polygon); //添加覆盖物
      let latlngItem = item;
      polygon.addEventListener("click", function() {
        _this.selectInfo = item;
        let points = _this.getCenterPoint(pointArr);
        _this.createInfoWin(points, _this.$refs.InfoWindow.$el);
        _this.map.setViewport(pointArr); //调整地图视口
      });
      return polygon;
    },
    //添加marker
    addMarker(point, iconurl, item) {
      let _this = this;
      let icon = new BMap.Icon(iconurl, new BMap.Size(32, 32));
      //向地图上添加自定义标注
      let marker = new BMap.Marker(new BMap.Point(point.lng, point.lat), {
        icon: icon
      });

      marker.addEventListener("click", function() {
        _this.selectInfo = item;
        _this.createInfoWin(
          new BMap.Point(point.lng, point.lat),
          _this.$refs.InfoWindow.$el
        );
        // _this.map.panTo(this); //调整地图视口
      });

      this.parkingMarkerList.put(item.id, marker);

      this.map.addOverlay(marker);
    },
    //展示车辆
    loadAreaVec(points, name, poiList) {
      let _this = this;
      let marker = null;
      let icon = new BMap.Icon(
        imgsConfig.rescue.claimcar,
        new BMap.Size(24, 24)
      );
      points = points.split(",");
      let pts = new BMap.Point(Number(points[1]), Number(points[0]));
      _this.map.panTo(pts, 13);
      marker = new BMap.Marker(pts, { icon: icon });
      marker.addEventListener("click", function() {
        _this.openInfoWindow(name, this, poiList, true);
      });
      _this.map.addOverlay(marker);
      this.getRoud(Number(points[1]), Number(points[0]));
    },
    // 附近车辆
    nearbyVec(veclist, radius) {
      if (!veclist) return false;
      let _this = this;
      veclist.forEach((item, i) => {
        let point = new BMap.Point(item.longitude, item.latitude);
        let runStatus = item.speed > 0 ? "run" : "stop";
        let updateTime = new Date().getTime() - item.updateTimeStamp;
        if (item.state == 0 || updateTime > 10 * 1000 * 60) {
          // 离线
          runStatus = "offline";
        }
        // carType：0：空车，1：重车，2：未知
        let icon = new BMap.Icon(
          imgsConfig.vec[
            `${runStatus}_${item.carType == 4 ? 2 : item.carType}`
          ],
          new BMap.Point(24, 24)
        );
        let marker = new BMap.Marker(point, { icon: icon });
        // marker.setRotation(item.direction);
        _this.map.addOverlay(marker);
        marker.addEventListener("click", function() {
          _this.openInfoWindow(item._id, this, radius);
        });
      });
    },
    poiIconImg(data) {
      switch (data) {
        case data.indexOf("公司") > -1:
          return imgsConfig.poi.entp;
          break;
        case data.indexOf("教育") > -1:
          return imgsConfig.poi.teach;
          break;
        case data.indexOf("交通") > -1:
          return imgsConfig.poi.traffic;
          break;
        case data.indexOf("政府") > -1:
          return imgsConfig.poi.government;
          break;
        case data.indexOf("医") > -1:
          return imgsConfig.poi.hospital;
          break;
        case data.indexOf("广场") > -1:
          return imgsConfig.poi.arder;
          break;
        default:
          return imgsConfig.poi.weizhi;
          break;
      }
    },
    //展示poi
    poiList(data) {
      let _this = this;
      data.forEach((item, i) => {
        if (item) {
          if (item.location && item.location.lng && item.location.lat) {
            let point = new BMap.Point(item.location.lng, item.location.lat);
            let icon = new BMap.Icon(
              _this.poiIconImg(item.name),
              new BMap.Size(30, 30)
            );
            let marker = new BMap.Marker(point, { icon: icon });
            _this.map.addOverlay(marker);
            marker.addEventListener("click", function() {
              let points = this.getPosition();
              let _dom = _this.createPoiInfo(item);
              _this.createInfoWin(points, _dom);
            });
          }
        }
      });
    },
    openInfoWindow(name, marker, radius, ixInfoBox) {
      let _this = this;
      this.map.panTo(marker.getPosition());
      this.mapNodeLoading && this.mapNodeLoading.close();
      this.mapNodeLoading = this.getMapNodeLoading();
      // 获取车辆详情
      $httpEme
        .getVecInfo(name)
        .then(res => {
          if (res.code == 0) {
            let rteplan = res.data;
            _this.selectVecInfo.vehicleNo = name;
            if (radius) {
              _this.selectVecInfo.radius = radius;
            }
            _this.rteplan = rteplan;
            if (ixInfoBox) {
              // 自定义信息窗口
              _this.createInfoBox(
                marker.getPosition(),
                _this.$refs.monitorInfoWindow.$el
              );
            } else {
              // 百度原生信息窗口
              _this.createInfoWin(
                marker.getPosition(),
                _this.$refs.monitorInfoWindow.$el
              );
            }
          } else {
            this.$message({
              message: "对不起，无法查看该车信息",
              type: "error"
            });
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    // poi信息详情
    createPoiInfo(item) {
      let _dom = document.createElement("div");
      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;
      let _html = `
        <div>名称 : ${item.name}</div>
        <div>距离 : ${item.distants.toFixed(2)}米</div>
        <div>地区 : ${item.province}${item.city}${item.area}</div>
        <div>详细地址 : ${item.address}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    //创建详情框
    createInfoWin(points, _dom) {
      this.closeInfoWindow();
      this.infoWin = new BMap.InfoWindow();
      // this.infoWin.setLngLat(points);
      //设置信息窗口要显示的内容
      this.infoWin.setContent(_dom);
      this.map.addOverlay(this.infoWin);
      this.map.openInfoWindow(this.infoWin, points);
    },
    // 关闭弹窗信息
    closeInfoWindow() {
      if (this.infoWin) {
        this.map.closeInfoWindow();
        this.infoWin = null;
      }
    },
    // 自定义信息窗口
    createInfoBox(points, _dom) {
      let _this = this;
      var infoBox = new BMapLib.InfoBox(_this.map, _dom, {
        boxStyle: {
          width: "330px",
          marginBottom: "28px",
          marginRight: "15px"
        },
        closeIconMargin: "-274px 0 0 0",
        closeIconUrl: imgsConfig.vec.close_btn
      });
      this.infobox = infoBox;
      infoBox.open(points);
    },
    //关闭自定义信息窗口
    closeInfoBox() {
      if (this.infobox) {
        this.infobox.close();
        this.infobox = null;
      }
    },
    //设置围栏中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    //搜索区域
    searchLocal(searchNm) {
      searchNm = searchNm || this.searchNm;

      this.options.forEach((item, i) => {
        if (item.id == searchNm) {
          let pointArr = item.lnglat.split(";").map(iitem => {
            let point = iitem.split(",");
            return new BMap.Point(point[0], point[1]);
          });
          this.map.setViewport(pointArr);
          let marker = this.parkingMarkerList.get(item.id);
          marker && marker.ca.click();
        }
      });
    },
    // clear() {
    //   let _this = this;
    //   let overLays = this.map.getOverlays(); //获取地图上所有覆盖物
    //   if (overLays && overLays.length > 0) {
    //     overLays.forEach(item => {
    //       if (item.options.name !== "disableMassClear") {
    //         _this.map.removeOverLay(item);
    //       }
    //     });
    //   }
    // },
    // 开启定时任务
    startRefreshTask() {
      let _this = this;
      this.refreshNextTime = this.refreshIntervalTime;
      this.refreshTask = setInterval(function() {
        _this.refreshNextTime -= 1;
        if (_this.refreshNextTime == 0) {
          if (_this.refreshFlag) {
            _this.refreshFlag = false;
            _this.getAreaList();
            // _this.$refs.term.submit(true)
          }
          _this.resetRefreshTask(); // 重置定时任务
        }
      }, 1000);
    },

    // 重置定时任务
    resetRefreshTask() {
      this.refreshNextTime = this.refreshIntervalTime;
      window.clearInterval(this.refreshTask);
      this.startRefreshTask();
    },

    // 停止定时任务
    stopRefreshTask() {
      window.clearInterval(this.refreshTask);
    },

    // 创建医院信息
    createHospitalMap(hospitalTeam) {
      let _this = this;
      let hospitalTeamMap = this.$refs.control.hospitalTeamMap;
      hospitalTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.hospital,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm,
            name: "disableMassClear"
          });

          // marker.hide();
          // marker.disableMassClear();
          marker.addEventListener("click", function() {
            let points = this.getLngLat();
            let _dom = _this.createHospitalInfo(item);
            _this.createInfoWin(points, _dom);
          });
          hospitalTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建公安信息
    createPoliceMap(policeTeam) {
      let _this = this;
      let policeTeamMap = this.$refs.control.policeTeamMap;
      policeTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.police,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm,
            name: "disableMassClear"
          });
          // marker.hide();
          // marker.disableMassClear();
          marker.addEventListener("click", function() {
            let points = this.getLngLat();
            let _dom = _this.createPoliceTeamMapInfo(item);
            _this.createInfoWin(points, _dom);
          });
          policeTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建救援信息
    createRescueMap(rescueTeam) {
      let _this = this;
      let rescueTeamMap = this.$refs.control.rescueTeamMap;
      rescueTeam.forEach(item => {
        if (item.lng && item.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.entpFire,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(item.lng, item.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: item.teamNm,
            name: "disableMassClear"
          });
          // marker.hide();
          // marker.disableMassClear();
          marker.addEventListener("click", function() {
            let points = this.getLngLat();
            let _dom = _this.createEntpRescueInfo(item);
            _this.createInfoWin(points, _dom);
          });
          rescueTeamMap.put(item.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    // 创建消防信息
    createFireMap(subordinateUnits) {
      let _this = this;
      let fireTeamMap = this.$refs.control.fireTeamMap;
      subordinateUnits.forEach(subitem => {
        if (subitem.lng && subitem.lat) {
          let icon = new BMap.Icon(
            imgsConfig.rescue.fire,
            new BMap.Size(35, 36)
          );
          let point = new BMap.Point(subitem.lng, subitem.lat);
          let marker = new BMap.Marker(point, {
            icon: icon,
            title: subitem.teamNm,
            name: "disableMassClear"
          });

          // marker.hide();
          // marker.disableMassClear();
          marker.addEventListener("click", function() {
            _this.fireTeam = subitem.unitShortNm;
            let points = this.getLngLat();
            let _dom = _this.createFireInfo(subitem);

            _this.createInfoWin(points, _dom);
          });
          fireTeamMap.put(subitem.teamNm, marker);
          _this.map.addOverlay(marker);
          marker.show();
        }
      });
    },
    //创建左侧详情框
    createInfoWin(points, _dom) {
      this.closeInfoWindow();
      this.infoWin = new BMap.InfoWindow();
      // this.infoWin.setLngLat(points);
      //设置信息窗口要显示的内容
      this.infoWin.setContent(_dom);
      this.map.addOverlay(this.infoWin);
      this.map.openInfoWindow(this.infoWin, points);
    },
    // 消防队信息
    createFireInfo(subitem) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;
      `;

      let _html = `
        <div>消防队 : ${subitem.teamNm || ""}</div>
        <div>分管领导 : ${subitem.manager || ""}</div>
        <div>联系电话 : ${subitem.tel}/${subitem.mobile}</div>
        <div>地址 : ${subitem.location || ""}</div>
        <div>队员 : ${subitem.teamNum || ""} 人</div>
        <div>装备 : ${subitem.equipment || ""}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },

    // 救援队伍
    createEntpRescueInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;
      let _html = `
        <div>单位名称 : ${item.teamNm || ""}</div>
        <div>负责人 : ${item.manager}</div>
        <div>联系电话 : ${item.tel}/${item.mobile}</div>
        <div>地址 : ${item.location || ""}</div>
        <div>队员 : ${item.teamNum || ""} 人</div>
        <div>装备 : ${item.equipment || ""}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    // 医院信息
    createHospitalInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;

      let _html = `
        <div>医院名称 : ${item.teamNm || ""}</div>
        <div>院长 : ${item.manager || ""}</div>
        <div>联系电话 : ${item.tel || ""}</div>
        <div>地址 : ${item.location || ""}</div>
        <div>医务人员 : ${item.teamNum || ""} 人</div>

      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    //罐区详情
    createTankInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;

      let _html = `
        <div>企业名称 : ${item.entpNm || ""}</div>
        <div>储罐区编号 : ${item.tankAreaCd || ""}</div>
        <div>存储物质 : ${item.storeGoods || ""}</div>
        <div>物质状态 : ${item.storeStatus || ""} </div>
        <div>储罐数量 : ${item.tankCnt || ""}</div>
        <div>储罐容积 : ${item.tankVol || ""}</div>

      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    // 公安信息
    createPoliceTeamMapInfo(item) {
      let _dom = document.createElement("div");

      _dom.style.cssText = `width:320px;font-size:13px;line-height: 1.6;`;

      let _html = `
        <div>单位名称 : ${item.teamNm || ""}</div>
        <div>负责人 : ${item.manager || ""}</div>
        <div>联系电话 : ${item.tel || ""}</div>
        <div>队员 : ${item.teamNum || ""} 人</div>
        <div>地址 : ${item.location || ""}</div>
      `;
      _dom.innerHTML = _html;
      return _dom;
    },
    // 展示企业围栏
    loadAreaEntp(points, name, radius) {
      let _this = this;
      let marker = null;
      let lng = null;
      let lat = null;
      let pointsView = [];
      let icon = new BMap.Icon(imgsConfig.vec.entp, new BMap.Size(24, 24));

      points = points.split(";");
      let pointsArr = [];
      for (let i = 0, len = points.length; i < len; i++) {
        let item = points[i].split(",");
        if (!item[0]) {
          continue;
        }
        lng = Number(item[0]);
        lat = Number(item[1]);
        pointsArr.push(new BMap.Point(lng, lat));
      }
      var polygon = new BMap.Polygon(pointsArr, {
        color: "blue",
        weight: 2,
        opacity: 0.3
      }); //创建多边形
      this.map.addOverlay(polygon); //增加多边形
      let pts = this.getCenterPoint(pointsArr);
      pointsView.push(pts);

      marker = new BMap.Marker(pts, { icon: icon });
      marker.addEventListener("click", function() {
        _this.openCpInfoWindow(_this.$refs.term.termForm.pk, this, radius);
      });
      this.map.addOverlay(marker);
      this.map.setViewport(pointsView); //将所有的点放置在最佳视野内
      this.getRoud(pts.lng, pts.lat);
    },
    //企业详情
    openCpInfoWindow(pk, marker, radius) {
      let _this = this;
      this.mapNodeLoading = this.getMapNodeLoading();
      this.map.panTo(marker.getPosition());
      // 获取企业详情
      $httpEme
        .getEntpInfoBypk(pk)
        .then(res => {
          if (res.code == 0 && res.data) {
            _this.selectEntpInfo = res.data.entp;
            _this.selectEntpInfo.radius = radius;
            _this.createInfoBox(
              marker.getPosition(),
              _this.$refs.monitorCpInfoWindow.$el
            );
          } else {
            this.$message({
              message: "对不起，无法查看该企业信息",
              type: "error"
            });
          }
          _this.mapNodeLoading.close();
        })
        .catch(error => {
          console.log(error);
          _this.mapNodeLoading.close();
        });
    },
    //mads弹框
    messageDialogHandle(value) {
      this.messageDialog = true;
      this.messageIpPk = value;
    },
    // 取点搜索加标注
    addPonit(lng, lat) {
      this.getRoud(lng, lat);
      var marker = new BMap.Marker(new BMap.Point(lng, lat));
      //向地图上添加标注
      this.map.addOverlay(marker);
      this.map.panTo(new BMap.Point(lng, lat), 13);
    },
    // 画搜索范围
    getRoud(lng, lat) {
      // 圆形覆盖物
      var point = new BMap.Point(lng, lat);
      var circle = new BMap.Circle(
        point,
        Number(this.$refs.term.termForm.distance) * 1000,
        {
          fillColor: "blue",
          strokeWeight: 1,
          fillOpacity: 0.3
        }
      );
      this.map.addOverlay(circle);
    },
    //多边形计算中心点
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;
      return new BMap.Point(x, y);
    },
    getMapNodeLoading() {
      const loading = this.$loading({
        lock: true,
        target: this.$refs.mapNode,
        spinner: "el-icon-loading"
      });
      return loading;
    },
    //清除地图覆盖物
    clearOverLays() {
      // let _this = this
      // let overLays = this.map.getOverlays()//获取地图上所有覆盖物
      // if(overLays && overLays.length > 0){
      //   overLays.forEach(item => {
      //     // if(item.options.name !== 'disableMassClear'){
      //       _this.map.removeOverlay(item)
      //     // }
      //   });
      // }
      this.map.clearOverlays();
    },
    // 加载停车场围栏
    loadStop() {
      this.displayMarkerByType(this.typeOfTheShow);
    },
    //停车场应急救援
    emeHandle(point) {
      if (point) {
        this.$refs.term.termForm.type = "2";
        this.$refs.term.termForm.lng = point.lng;
        this.$refs.term.termForm.lat = point.lat;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
#locMap {
  width: 100%;
  height: 600px;
}
.draw-panel {
  position: absolute;
  left: 30px;
  top: 30px;
  // width:200px;
  z-index: 1000;
}
.refresh-tooltip {
  position: absolute;
  z-index: 1000;
  right: 30px;
  top: 30px;
  box-shadow: rgba(0, 0, 0, 0.35) 2px 2px 3px;
  border-left: 1px solid rgb(139, 164, 220);
  border-top: 1px solid rgb(139, 164, 220);
  border-bottom: 1px solid rgb(139, 164, 220);
  background-color: rgba(28, 91, 250, 0.7);
  padding: 3px 6px;
  font: bold 12px/1.3em arial, sans-serif;
  text-align: center;
  white-space: nowrap;
  border-radius: 3px 0px 0px 3px;
  color: #fff;
}
.draw-panel /deep/.el-input__inner {
  background-color: #fff;
  color: #606266;
  border-color: #dcdfe6;
}
.draw-panel /deep/.el-form-item {
  margin-bottom: 0px;
}
.vec-total-wape {
  position: absolute;
  bottom: 20px;
  z-index: 1000;
  margin: 0 auto;
  padding: 0px;
  text-align: center;
  width: 100%;
  text-align: center;
  overflow: hidden;
  padding-bottom: 5px;
}
.vec-total-btn-all,
.vec-total-btn-heavy,
.vec-total-btn-normal {
  margin: 0;
  box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 2px;
  line-height: 30px;
  padding: 2px 10px;
  font-size: 14px;
  cursor: pointer;
  font-weight: bold;
  border: 1px solid #449d44;
  color: #449d44;
  border-radius: 3px;
  background-color: #fff;
}
.vec-total-btn-all {
  border: 1px solid #409eff;
  color: #409eff;
}
.vec-total-btn-heavy {
  border: 1px solid #f07528;
  color: #f07528;
}
.vec-total-btn-normal {
  border: 1px solid #449d44;
  color: #449d44;
}
.vec-total-btn-all.active {
  background-color: #409eff;
  border: 1px solid #409eff;
  color: #fff;
}
.vec-total-btn-normal.active {
  background-color: #449d44;
  border: 1px solid #449d44;
  color: #fff;
}
.vec-total-btn-heavy.active {
  background-color: #f07528;
  border: 1px solid #f07528;
  color: #fff;
}
.parking-list {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 10px;

  li {
    font-size: 13px;
    line-height: 1.6;
    margin-bottom: 10px;
    cursor: pointer;
    font-weight: 600;
    &.danger {
      color: #e12828;
    }
    &.warn {
      color: #f07528;
    }
    &.normal {
      color: #1a992f;
    }
    &:last-child {
      margin-bottom: 0px;
    }
  }
}
</style>
<style>
.tdt-touch .tdt-control-copyright {
  display: none !important;
}
</style>

