<template>
  <div>
    <div
      style="
        min-width: 200px;
        font-size: 16px;
        line-height: 28px;
        overflow: hidden;
      "
    >
      <div style="font-weight: 600">{{ selectInfo.name || "" }}</div>
      <div v-show="selectInfo.maxCarsum">
        可容纳车辆：{{ selectInfo.maxCarsum || 0 }}
      </div>
      <div>
        目前已停车辆：<a
          style="cursor: pointer"
          @click="showVecList(selectInfo, false)"
          >{{ selectInfo.nowCarsum || 0 }}</a
        >
      </div>
      <div>
        重车数：<a
          style="cursor: pointer"
          @click="showVecList(selectInfo, true)"
          >{{ selectInfo.heavyCarsum || 0 }}</a
        >
      </div>
      <!-- <div v-if="selectInfo.nowCarsum > selectInfo.maxCarsum" style="color:#f56c6c">目前已停车辆已超限</div> -->
      <!-- <el-button type="text" style="float:right;color:#f56c6c" @click="emeHandle">应急救援</el-button> -->
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogTableVisible"
      append-to-body
      width="80%"
      top="5vh"
    >
      <!-- <simple-table :tableTitle="tableTitle" :tableHeader="tableHeader" :tablePage="tablePage">
          <template slot="customColum" slot-scope="scope">
            <el-button type="text" @click="showHisTrack(scope.row['_id'])">{{scope.row['_id']}}</el-button>
          </template>
        </simple-table> -->
      <div v-html="tableTitle"></div>
      <el-table
        v-loading="loading"
        v-show="!isHeavy"
        class="el-table"
        :data="tablePage.list"
        highlight-current-row
        style="width: 100%"
        max-height="400"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="_id" label="车牌号"> </el-table-column>
        <el-table-column prop="carType" label="车辆类型">
          <template slot-scope="scope">
            <span v-if="scope.row.carType == 0">空车</span>
            <span v-if="scope.row.carType == 1">重车</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="卫星定位更新时间">
        </el-table-column>
        <el-table-column prop="enterDate" label="进场时间"> </el-table-column>
        <el-table-column prop="stopRemark" label="备注"> </el-table-column>
        <el-table-column
          fixed="right"
          align="center"
          prop="action"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              @click="showHisTrack(scope.row['_id'])"
              type="text"
              title="查看轨迹"
              >查看轨迹</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-loading="loading"
        v-show="isHeavy"
        class="el-table"
        :data="tablePage.list"
        highlight-current-row
        style="width: 100%"
        max-height="400"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>

        <el-table-column width="180" prop="enterDate" label="进场时间">
        </el-table-column>
        <el-table-column width="220" prop="cd" label="电子运单号">
          <template slot-scope="scope">
            <el-button
              @click="showCd(scope.row['argmtPk'])"
              type="text"
              :title="scope.row.cd"
              >{{ scope.row.cd }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column width="90" prop="_id" label="牵引车">
        </el-table-column>
        <el-table-column width="90" prop="traiCd" label="挂车">
        </el-table-column>
        <el-table-column width="250" prop="carrierNm" label="承运商">
        </el-table-column>
        <el-table-column width="280" prop="goodsNm" label="货物">
        </el-table-column>
        <el-table-column width="100" prop="loadQty" label="吨数（吨）">
        </el-table-column>
        <!-- <el-table-column prop="csnorWhseDist" label="装货地"> </el-table-column>
        <el-table-column prop="csneeWhseDist" label="卸货地"> </el-table-column>
        <el-table-column prop="dvNm" label="驾驶员"> </el-table-column>
        <el-table-column prop="dvMob" label="驾驶员联系方式"> </el-table-column>
        <el-table-column prop="scNm" label="押运员"> </el-table-column>
        <el-table-column prop="scMob" label="押运员联系方式"> </el-table-column> -->
        <el-table-column
          fixed="right"
          align="center"
          prop="action"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              @click="showHisTrack(scope.row['_id'])"
              type="text"
              title="查看轨迹"
              >查看轨迹</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      title="电子运单详情"
      :visible.sync="visibleOfRtePlanBills"
      append-to-body
      width="80%"
      top="5vh"
      class="detail-dialog"
    >
      <rtePlan-bills-info
        ref="RtePlanBillsInfo"
        :isCompn="true"
      ></rtePlan-bills-info>
    </el-dialog>
  </div>
</template>
<script>
import * as $http from "@/api/stopFence";
import SimpleTable from "@/components/SimpleTable";
import RtePlanBillsInfo from "@/views/modules/base/rtePlan/rtePlan-bills";
import * as Tool from "@/utils/tool";
export default {
  props: {
    selectInfo: {
      type: Object,
    },
  },
  data() {
    return {
      dialogTableVisible: false, // 弹窗是否可见
      dialogTitle: "", // 弹窗标题
      loading: false,
      tableComponent: "simple-table", // table组件
      tableTitle: "", // table的标题
      tableHeader: null, // table的表头信息
      isHeavy: false,
      tablePage: {
        // table的数据信息，包括list数据以及分页数据
        list: [],
        pageNo: 0,
        pageSize: 0,
        totalPage: 0,
      },
      visibleOfRtePlanBills: false,
    };
  },
  components: {
    SimpleTable,
    RtePlanBillsInfo,
  },
  methods: {
    showHisTrack(vehicleNo) {
      let location = window.location;
      window.open(
        location.origin +
          location.pathname +
          "#/monit/hisTrack?v=" +
          encodeURIComponent(vehicleNo) +
          "&t=" +
          Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },
    showCd(pk) {
      if (!pk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      this.visibleOfRtePlanBills = true;
      this.$nextTick(() => {
        this.$refs.RtePlanBillsInfo.rtePlanNewByPk(pk);
      });
      // let location = window.location;
      // window.open(
      //   location.origin +
      //     location.pathname +
      //     "#/base/rteplan/bills/"+cd,
      //   "_blank"
      // );
    },
    showVecList(item, isHeavy) {
      if (!item.nowCarsum) {
        this.$message.error("对不起，停车场内暂无车辆");
        return;
      }
      if (isHeavy) {
        if (!item.heavyCarsum) {
          this.$message.error("对不起，停车场内暂无重车");
          return;
        }
      }
      let _this = this;
      this.tableType = "vec";
      this.isHeavy = isHeavy;
      this.dialogTitle = `停车场车辆详情`;
      if (!isHeavy) {
        this.tableTitle = `<h5 style="color:#fff;margin-bottom:5px;">${item.name}，目前已停${item.nowCarsum}辆车</h5>`;
        this.tableHeader = [
          { name: "车牌号", field: "_id", slot: true },
          {
            name: "车辆类型",
            field: "carType",
            formatter: (val) => {
              if (val == 0) {
                return "空车";
              } else {
                return "重车";
              }
            },
          },
          { name: "卫星定位更新时间", field: "updateTime" },
          { name: "进场时间", field: "enterDate" },
          { name: "备注", field: "stopRemark" },
        ];
      } else {
        this.tableTitle = `<h5 style="color:#fff;margin-bottom:5px;">${item.name}，目前已停${item.heavyCarsum}辆重车</h5>`;
        this.tableHeader = [
          { name: "进场时间", field: "enterDate", width: "140" },
          { name: "牵引车", field: "_id", width: "90", slot: true },
          { name: "挂车", field: "traiCd", width: "90" },
          { name: "承运商", field: "carrierNm", width: "180" },
          { name: "电子运单号", field: "cd", width: "190" },
          { name: "货物", field: "goodsNm" },
          { name: "吨数", field: "loadQty" },
          { name: "装货地", field: "csnorWhseDist", width: "140" },
          { name: "卸货地", field: "csneeWhseDist", width: "140" },
          // { name: "装货单位", field: "csnorNmCn" },
          // { name: "卸货单位", field: "csneeNmCn" },
          { name: "驾驶员", field: "dvNm" },
          { name: "驾驶员联系方式", field: "dvMob", width: "100" },
          { name: "押运员", field: "scNm" },
          { name: "押运员联系方式", field: "scMob", width: "100" },
        ];
      }

      let params = {
        // page:1,
        // limit:20,
        id: item.id,
      };
      if (isHeavy) {
        params.carType = 1;
      }
      this.getVecList(params);
    },
    getVecList(params) {
      let _this = this;
      _this.dialogTableVisible = true;
      _this.loading = true;
      $http
        .getentpcarbyareapk(params)
        .then((res) => {
          if (res.code == 0) {
            let list = res.data;
            if (params.carType) {
              list = list
                .filter((item) => {
                  return item.carType == 1;
                })
                .map((item) => {
                  return Object.assign({}, item, item.rtePlanLastestEntity);
                });
            } else {
              list.forEach((item) => {
                if (item.carType == 1) {
                  item.rowClassName = "paint-row";
                  item.isargmt = 0;
                }
              });
            }

            _this.$set(_this.tablePage, "list", list);
            _this.loading = false;
          } else {
            _this.$set(_this.tablePage, "list", []);
            _this.$message.error(res.msg);
            _this.loading = false;
          }
        })
        .catch((err) => {
          console.log(err);
          _this.loading = false;
        });
    },
    emeHandle() {
      let pointsArr = [];
      let points = this.selectInfo.lnglat;
      points = points.split(";");
      for (let i = 0, len = points.length; i < len; i++) {
        let item = points[i].split(",");
        if (!item[0]) {
          continue;
        }
        let lng = Number(item[0]);
        let lat = Number(item[1]);
        pointsArr.push(new BMap.Point(lng, lat));
      }
      let pts = this.$parent.getCenterPoint(pointsArr);
      this.$emit("emeHandle", pts);
    },
  },
};
</script>
