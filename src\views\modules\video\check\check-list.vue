<!--
 * @Author: your name
 * @Date: 2020-09-23 17:23:46
 * @LastEditTime: 2020-09-25 13:48:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \gov\src\views\modules\video\check\check-list.vue
-->

<template>
  <div class="checkList">
    <el-row :gutter="10">
      <template v-for="(item,index) in srcList">
        <el-col :span="8" :key="index">
          <el-image style="width: 100%;height:300px;padding:10px;" :src="item" :preview-src-list="[item]" fit="fill">
          </el-image>
        </el-col>
      </template>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "checkLlist",
  data() {
    return {
      // url: require("./../../../../assets/jcdd-img/jcdd1.png"),
      srcList: [
        require("./../../../../assets/jcdd-img/jcdd1.png"),
        require("./../../../../assets/jcdd-img/jcdd2.png"),
        require("./../../../../assets/jcdd-img/jcdd3.jpg"),
        require("./../../../../assets/jcdd-img/jcdd4.jpg"),
        require("./../../../../assets/jcdd-img/jcdd5.jpg"),
        require("./../../../../assets/jcdd-img/jcdd6.jpg")
      ]
    };
  }
};
</script>
<style>
/* .checkList {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.checkList div {
  width: 33.33%;
  height: 460px;
  float: left;
}
.checkList div img {
  width: 100%;
  height: 100%;
} */
</style>