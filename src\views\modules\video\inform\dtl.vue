<template>
  <el-dialog :visible.sync="dialogVisible" title="" width="960px">
    <div class="inform-dtl">
      <h3 class="headline">
        危化车辆登记点告知单（存根联）
      </h3>
      <p style="text-indent:38px;">
        <span>驾驶员:</span>
        <span class="dvNm">
          <span class="underline" style="padding:0 10px"></span>
          <template v-if="dtlData.dvNm">
            <span class="underline highlight" v-for="(word,index) in dtlData.dvNm" :key="index">{{ word }}</span>
          </template>
          <template v-else>
            <span class="underline highlight" v-for="word in 4" :key="word" style="padding:0 6px"></span>
          </template><span class="underline" style="padding:0 10px"></span>
        </span>
        <span>所驾驶的（<span style="position:relative;" class="unloaded" v-if="wbType==='unloaded'">空车</span><span v-if="wbType==='loaded'" style="position:relative;" class="loaded highlight" >重车<span class="highlight">{{dtlData.goodsNm ? '(' + dtlData.goodsNm + ')' : ''}}</span></span>）车牌号:</span>
        <span class="tracNo">
          <span class="underline" style="padding:0 10px"></span>
          <template v-if="dtlData.tracNo">
            <span class="underline highlight " v-for="(word,index) in dtlData.tracNo" :key="index">{{ word }}</span>
          </template><template v-else>
            <span class="underline highlight" v-for="word in 7" :key="word" style="padding:0 6px"></span>
          </template><span class="underline" style="padding:0 10px"></span>
          <span v-if="dtlData.company">({{ dtlData.company }})</span>
        </span>
        <span>
        在镇海区危化车辆登记点（
        <span class="highlight regPot">{{ dtlData.regPot }}</span>
        ）登记时，因
        </span>
        <span class="reason">
          <span class="underline" style="padding:0 10px"></span>
          <template v-if="dtlData.reason">
            <span class="underline highlight" v-for="(word,index) in dtlData.reason" :key="index">{{ word }}</span>
          </template>
          <template v-else>
            <span class="underline highlight" v-for="word in 48" :key="word" style="padding:0 6px"></span>
          </template>
          <span class="underline" style="padding:0 10px"></span>
          ，被告知
        </span>
        <span class="content">
          <span class="underline" style="padding:0 10px"></span>
          <template v-if="dtlData.content">
            <span class="underline highlight" v-for="(word,index) in dtlData.content" :key="index">{{ word }}</span>
          </template><template v-else>
            <span class="underline highlight" v-for="word in 42" :key="word" style="padding:0 6px"></span>
          </template><span class="underline" style="padding:0 10px"></span>
        </span>。
      </p>
      <div class="clearfix">
        <div class="contact ft-lf">
          <div class="sign">
            当事人签名:
            <el-image
              v-if="dtlData.sign"
              style="height: 156px;vertical-align: top;"
              :src="dtlData.sign"
            >
            </el-image>
            <span v-else style="font-size: 14px;">未签名</span>
          </div>
        </div>
        <div class="stamp ft-lf">
          <div class="mobile">联系号码:{{ dtlData.dvMob }}</div>
          <div style="margin-top:30px;">
            <div>镇海区道路运输安全稽查大队</div>
            <div>{{ formatDate(dtlData.noticeTm) }}</div>
            <img class="e-stamp" :src="JCDDSTAMP" alt="" />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/inform";
import { formatDate } from "@/utils/tool";
import JCDDSTAMP from "@/assets/jcdd-img/jcddstamp.png";

export default {
  name: "",
  data() {
    return {
      dialogVisible: false,
      dtlData: {},
      JCDDSTAMP: JCDDSTAMP
    };
  },
  computed:{
    wbType(){
      let dtlData = this.dtlData;
      if(dtlData.vecType === 'loaded'){
        return 'loaded'
      }else if(dtlData.vecType === 'unloaded'){
        return 'unloaded'
      }else{
        return ''
      }
    }
  },
  methods: {
    show(id) {
      this.getDtl(id);
    },
    formatDate(tm) {
      return formatDate(tm, "yyyy年MM月dd日 HH时mm分");
    },
    getDtl(id) {
      $http
        .noticeSheetInfo(id)
        .then((res) => {
          if (res && res.data) {
            this.dtlData = res.data;
            this.dialogVisible = true;
          } else {
            this.dtlData = {};
            this.$message.error(res.msg || "请求错误");
          }
        })
        .catch((err) => {
          this.dtlData = {};
          this.$message.error(err.msg || "请求错误");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.inform-dtl{
  font-size: 16px;
  width: 800px;
  height: 510px;
  margin: 0 auto;
  letter-spacing: 4px;
  vertical-align: middle;

  .headline{
    font-size: 22px; 
    padding: 14px 0px; 
    font-weight: 600;
    margin: 0;
    text-align: center;
  }

  .dvNm{
    // display: table-cell;
    // width: 90px;
    // padding-left: 20px;
    // padding-right: 20px;
    letter-spacing: normal;
  }

  .tracNo{
    // display: inline-block;
    // width: 116px;
    // padding-left: 20px;
    // padding-right: 20px;
    letter-spacing: normal;
  } 
  .regPot{
    // display: inline-block;
    width: 441px;
    text-align: center;
    letter-spacing: normal;
  } 
  .reason{
    // display: inline-block;
    // width: 779px;
    // padding-left: 20px;
    // padding-right: 20px;
    text-align: center;
    letter-spacing: normal;
  } 
  .content{
    // display: inline-block;
    // width: 570px;
    // padding-left: 20px;
    // padding-right: 20px;
    text-align: left;
    letter-spacing: normal;

    >span{
      position: relative;
    }
  }

  p{
    margin: 0px;
    line-height: 42px;
  }

  .contact{
    margin-top: 16px;
    overflow: hidden;
    width: 50%;

    .sign,.mobile{
      width: 100%;
    }
    .mobile{
      text-align: right;
      letter-spacing: normal;
      position: relative;
      z-index: 2;
    }
  }
  .stamp{
    text-align: right;
    margin-top: 20px;
    letter-spacing: normal;
    line-height: 30px;
    position: relative;
    color: #000000;
    width: 50%;

    > div{
      position: relative;
      z-index: 2;
    }
  }

  .e-stamp{
    position: absolute;
    right: 0;
    top: -24px;
    opacity: .76;
  }
  .check-icon{
    position: absolute;
    z-index: 1;
    color: red;
    top: -3px;
    font-size: 35px;
  }

  .unloaded {
    // display: inline-block;
    width: 244px;
  .check-icon{
    left: -40px;
  }
}
.loaded {
  // display: inline-block;
  width: 244px;
  .check-icon{
    left: -40px;
  }
}
}
.underline{
    position: relative;
    text-align: center;

    &::after{
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right:0;
      height: 1px;
      background: #000000;
    }
}
.highlight{
    color: red;
}
</style>