<template>
  <div class="app-main-content">
    <transition name="el-fade-in-linear">
      <div class="chart-main">
        <div>
          <searchbar
            ref="searchbar"
            :searchItems="searchItems"
            @search="getList"
          ></searchbar>
          <!--列表-->
          <el-table
            class="el-table"
            :data="list"
            highlight-current-row
            v-loading="listLoading"
            border
            style="width: 100%"
            :max-height="tableHeight"
          >
            <el-table-column prop="tracNo" label="车牌"></el-table-column>
            <el-table-column prop="dvNm" label="驾驶员姓名"></el-table-column>
            <el-table-column prop="company" label="所属企业"></el-table-column>
            <el-table-column
              prop="dvMob"
              label="驾驶员手机号"
            ></el-table-column>
            <el-table-column prop="sign" label="签名">
               <template slot-scope="scope">
                <el-image 
                  v-if="scope.row.sign"
                  style="width: 30px; height: 30px"
                  :src="scope.row.sign" 
                  :preview-src-list="[scope.row.sign]">
                </el-image>
                <span v-else style="font-size: 14px;">未签名</span>
               </template>
            </el-table-column>
            <el-table-column prop="regPot" label="记录地点"></el-table-column>
            <!-- <el-table-column
              prop="regNumber"
              label="记录地点编号"
            ></el-table-column> -->
            <el-table-column prop="goodsNm" label="货物名称"></el-table-column>
            <el-table-column prop="reason" label="告知原因"></el-table-column>
            <el-table-column prop="content" label="告知内容"></el-table-column>
            <el-table-column prop="state" label="告知单状态">
                <template slot-scope="scope" >
                    <el-tag v-if="scope.row.state == 1" type="info">草稿</el-tag>
                    <el-tag v-if="scope.row.state == 2" type="primary">正式</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="smsStatus" label="短信是否发送">
                <template slot-scope="scope">
                    <el-tag type="success" v-if="scope.row.smsStatus > 0">
                       已发送
                    </el-tag>
                    <el-tag v-else type="info">未发送</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="noticeTm" label="告知时间"></el-table-column>
            <el-table-column prop="" label="操作" width="130px">
              <template slot-scope="scope">
                <!-- <ElButton
                  icon="el-icon-delete"
                  type="text"
                  @click="removeRecord(scope.row)"
                  >删除</ElButton
                >
                <ElButton
                  icon="el-icon-edit"
                  type="text"
                  @click="editRecord(scope.row)"
                  >修改</ElButton
                > -->
                <ElButton icon="el-icon-tickets" type="text" @click="getDtl(scope.row)"
                  >详情</ElButton
                >
                <!-- <ElButton
                  icon="el-icon-message"
                  type="text"
                  @click="sendMsg(scope.row)"
                  >发送短信</ElButton
                > -->
                <ElButton
                  icon="el-icon-download"
                  type="text"
                  @click="exportNoticeToWord(scope.row)"
                  >导出</ElButton
                >
              </template>
            </el-table-column>
          </el-table>

          <!--工具条-->
          <el-col :span="24" class="toolbar" style="text-align: center">
            <!-- <div class="grid-operbar ft-lf">
                <el-button icon="el-icon-plus" size="small" type="primary" @click="addInform">新增</el-button>
            </div> -->
            <el-pagination
              background
              layout="sizes, prev, pager, next, total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
              :total="pagination.total"
              :page-size="pagination.limit"
              :page-sizes="[20, 30, 40, 50, 60]"
            >
            </el-pagination>
          </el-col>
        </div>
      </div>
    </transition>

    <el-dialog
      title="稽查大队危险品运输车辆登记点告知单"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        ref="formRef"
        size="small"
        label-width="140px"
      >
        <!-- 驾驶员：<el-input></el-input>所驾驶的（重车/空车）车牌号：<el-input></el-input>，在镇海区危险化学品运输车辆登记点（{{}}）登记时，因<el-input></el-input>
          被告知<el-input></el-input> -->
        <el-form-item label="驾驶员姓名" prop="dvNm" :rules="$rulesFilter({required:true})">
          <el-input placeholder="" v-model="formData.dvNm"></el-input>
        </el-form-item>
        <el-form-item label="驾驶员手机号" prop="dvMob" :rules="$rulesFilter({required:true, type:'mobile'})">
          <el-input placeholder="" v-model="formData.dvMob"></el-input>
        </el-form-item>
        <el-form-item label="(重车/空车)车牌号" prop="tracNo" :rules="$rulesFilter({required:true, type:'LPN'})">
          <el-input placeholder="" v-model="formData.tracNo"></el-input>
        </el-form-item>
        <el-form-item label="告知原因" prop="reason" :rules="$rulesFilter({required:true})">
          <el-input
            type="textarea"
            placeholder=""
            v-model="formData.reason"
          ></el-input>
        </el-form-item>
        <el-form-item label="告知内容" prop="content" :rules="$rulesFilter({required:true})">
          <el-input
            type="textarea"
            placeholder=""
            v-model="formData.content"
          ></el-input>
        </el-form-item>
        <el-form-item align="right">
          <el-button
            icon="el-icon-check"
            :loading="btnLoading"
            type="primary"
            @click="submit"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <DtlModel ref="dtlModel" />
  </div>
</template>
    
    <script>
import { mapGetters } from "vuex";
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import * as $http from "@/api/inform";
import DtlModel from "./dtl"

export default {
  components: {
    Searchbar,
    DtlModel
  },
  data: function () {
    return {
      currargmtPk: "",
      tableHeight: 500,
      list: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 20,
      },
      formData:{
        dvNm: '',
        dvMob: '',
        tracNo: '',
        reason: '',
        content: '',
        regNumber: '',
        regPot: ''
      },
      btnLoading:false,
      listLoading: false,
      weightDate: "",
      currRow: null,
      dialogVisible: false,
      regPotInfo:null,
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "tracNo",
            type: "text",
            dbfield: "trac_no",
            dboper: "cn",
          },
          {
            name: "驾驶员姓名",
            field: "dvNm",
            type: "text",
            dbfield: "dv_nm",
            dboper: "cn",
          },
          {
            name: "驾驶员手机号",
            field: "dvMob",
            type: "text",
            dbfield: "dv_mob",
            dboper: "cn",
          },
        ],
        more: [
            {
              name: "日期",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              field: "updTm",
              type: "daterange",
              dbfield: "upd_tm",
              dboper: "bt",
            }
        ],
      },
      pickerOptions2: {
        shortcuts: [
          {
            text: "今天",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime());
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick: function (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["appRegionNm","token"]),
  },
  mounted: function () {
    const that = this;
    let wh = Tool.getTableHeight();

    window.addEventListener("resize", function () {
      let wh = Tool.getTableHeight();
      that.tableHeight = wh;
    });
    //执行调用参照调用下拉框函数,初始化下拉数据
    this.$nextTick(function () {
      let query = this.$route.query;
      this.$refs.searchbar.init(query);
      this.getList();
      this.tableHeight = wh;
    });
  },
  methods: {
    // 导出告知单
    exportNoticeToWord(row){
      const token = this.token;
      window.open(window.location.origin+'/whjk-gov/zhcknoticesheet/exportWord/'+row.id+'?token='+token,'_blank');
    },
    removeRecord() {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await $http.noticeSheetDelete([row.id]);
          if (res && res.code === 0) {
            this.$message({
              message: "删除成功!",
              type: "success",
            });
          } else {
            this.$message({
              message: "删除失败!",
              type: "error",
            });
          }
        })
        .catch(() => {});
    },
    addInform (){
        this.currRow = null;
        for(var f in this.formData){
            this.formData[f] = '';
        }
        this.dialogVisible = true
    },
    editRecord(row) {
      this.currRow = row;
      this.dialogVisible = true;
      this.formData = Object.assign(this.formData, row)
    },
    sendMsg(row) {
      this.$confirm("是否发送短信?", "提示", {
        confirmButtonText: "发送",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await $http.officialSend([row.id]);
          if (res && res.code === 0) {
            this.$message({
              message: "短信发送成功!",
              type: "success",
            });
          } else {
            this.$message({
              message: "短信发送失败!",
              type: "error",
            });
          }
        })
        .catch(() => {});
    },
     submit() {
      this.$refs.formRef.validate( async (valid) => {
        if (valid) {
            this.btnLoading = true;
            let param = {};
            if (this.currRow) {
               Object.assign(param, this.currRow);
            } else {
                Object.assign(param, this.formData);
            }

            Object.assign(param, {
              regNumber: regPotInfoWithNb.value,
              regPot: regPotInfoWithNm.value,
            });

            const res = await $http.saveOrUpdate(param);
            this.btnLoading = false;
            if (res.code == 0) {
            this.currRow = null;
            dialogVisible.value = false;
            
            this.$message({
                message: "新增成功!",
                type: "success",
            });
            } else {
                this.$message({
                message: "新增失败!",
                type: "error",
            });
            }
        }
      })
      
    },
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.getList();
    },
    getList: function (data) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let param = Object.assign({}, { filters: filters }, this.pagination);
      delete param.total;

      //关闭loading
      this.listLoading = true;
      //查询列表
      $http
        .getList(param)
        .then((response) => {
          if (response.code == 0) {
            _this.list = response.page.list;
            this.pagination.total = response.page.totalCount
          } else {
            _this.list = [];
            this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch((error) => {
          throw new Error(error);
        });
    },
    getDtl(row){
      this.$refs.dtlModel.show(row.id)
    }
  },
};
</script>
    
    <style scoped>
.webDoc-app-main-content {
  padding: 20px;
  background: #fff;
  webkit-box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
  border-radius: 4px;
  border: 1px solid rgb(235, 238, 245);
  margin: 10px;
  overflow: hidden;
}
.el-table .cell,
.el-table th > div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
    