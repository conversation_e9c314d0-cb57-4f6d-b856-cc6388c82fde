<!--卡口监控-视频监控-->
<template>
  <div class="app-main-content">
    <el-row :gutter="20" style="margin-top: 10px;">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        :lg="6"
        v-for="(item, index) in videoOptionsList"
        :key="index"
        style="margin-bottom: 20px"
      >
        <player
          :API="videoHttp"
          :data-source="item"
          ajax-name="getVideoStreamUrl"
          getStreamDataFieldName="streamUrl"
          :posterHeight="340"
        >
          <template slot="append">
            <div
              class="video-title"
              style="background: #f1f1f1;cursor:pointer;"
              @click="jumpTo(item.title)"
            >
              <div :title="item.title">{{ item.title }}</div>
            </div>
          </template>
        </player>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import player from "@/components/flvPlayer/index";
import * as $http from "@/api/video";

export default {
  name: "monitorList",
  data() {
    return {
      videoList: [],
      videoHttp: $http
    };
  },
  components: {
    player
  },
  computed: {
    videoOptionsList() {
      let _this = this;
      let res = this.videoList.map((item, index) => {
        return {
          title: item.channelName,
          isOnLine: "在线",
          number: item.channelId,
          playerOption: {
            // poster: item.snapUrl
            poster: item.snapUrl
              ? item.snapUrl.split("?")[0] + "?t=" + new Date().getTime()
              : ""
          },
          streamingAddress: item.streamingAddress
        };
      });
      return res;
    }
  },
  created() {
    // this.getVideoList();
    this.getCheckVideo();
  },
  methods: {
    getCheckVideo() {
      let params = { filters: { groupOp: "AND", rules: [] } };
      $http.getCheckVideo(params).then(res => {
        if (res && res.code === 0) {
          this.videoList = res.list;
        } else {
          this.$message.error("视频列表获取失败，请联系管理员");
        }
      });
    },
    getVideoList() {
      let _this = this;
      $http
        .getVideoList({
          filters: {
            groupOp: "AND",
            rules: [{ field: "type_cd", op: "eq", data: "video.01" }]
          },
          page: 1,
          limit: 999
        })
        .then(res => {
          if (res && res.code === 0) {
            _this.videoList = res.page.list;
          } else {
            _this.$message.error("视频列表获取失败，请联系管理员");
          }
        })
        .catch(error => {
          console.log(error);
          _this.$message.error("视频列表获取失败，请联系管理员");
        });
    },
    jumpTo(name) {
      this.$router.push({
        path: "/video/wb/list",
        query: {
          regPot: encodeURIComponent(name)
        }
      });
    }
  }
};
</script>

<style scoped>
.video-title {
  width: 100%;
  color: #093161;
  font-size: 18px;
  line-height: 35px;
  text-align: center;
}
</style>
