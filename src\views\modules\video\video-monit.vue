<template>
  <div class="container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(item,index) in videoOptionsList" :key="index">
        <player :API="videoHttp" :data-source="item" ajax-name="getVideoStreamUrl" getStreamDataFieldName="streamUrl">
          <template slot="append">
            <div class="video-title" style="background: #f1f1f1;">
              <div :title="item.title" class="video-title">{{item.title}}</div>
            </div>
          </template>
        </player>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import player from "@/components/flvPlayer/index";
import * as $http from "@/api/video";
export default {
  name: "videoMonit",
  data() {
    return {
      videoList: [],
      videoHttp:$http,
    };
  },
  components: {
    player
  },
  computed: {
    videoOptionsList() {
      let _this = this;
      let res = this.videoList.map((item, index) => {
        return {
          title: item.routeName,
          isOnLine: "在线",
          number: item.number,
          playerOption: {
            poster: item.screenShotUrl
          }
        };
      });
      return res;
    }
  },
  created() {
    this.getVideoList();
  },
  methods: {
    getVideoList() {
      let _this = this;
      $http
        .getVideoList({ page: 1, limit: 999 })
        .then(res => {
          if (res && res.code === 0) {
            _this.videoList = res.page.list;
          } else {
            _this.$message.error("视频列表获取失败，请联系管理员");
          }
        })
        .catch(error => {
          console.log(error);
          _this.$message.error("视频列表获取失败，请联系管理员");
        });
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20px 10px;
  background-color: #212224;
}
.video-title {
  color: #fff;
  text-align: center;
  font-size: 15px;
  padding: 5px;
  margin-bottom: 10px;
}
</style>
