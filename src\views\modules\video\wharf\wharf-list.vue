<template>
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList"></searchbar>

    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" border ref="singleTable"
      style="width: 100%" :max-height="tableHeight" @current-change="handleSelectionChange" @sort-change="handleSort">
      <!-- <el-table :data="dataList" highlight-current-row v-loading="listLoading" :height="tableHeight" :size="size" border> -->
      <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
      <el-table-column prop="cd" label="牵引车" align="center"></el-table-column>
      <el-table-column prop="entpNmCn" label="运输企业" align="center"></el-table-column>
      <el-table-column prop="argmtCd" label="电子运单号" align="center"></el-table-column>
      <el-table-column prop="goodsNm" label="货物" align="center"></el-table-column>
      <el-table-column prop="loadQty" label="货物质量(KG)" align="center"></el-table-column>
      <el-table-column prop="regPot" label="记录地点" align="center"></el-table-column>
      <el-table-column prop="wtTm" label="记录时间" align="center"></el-table-column>
    </el-table>

    <!--工具条-->
    <div ref="paginationbar" class="pagination-wrapper">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" :page-size="pagination.limit" :page-sizes="[20, 30, 50, 100, 200]"
        :current-page.sync="pagination.page" :total="total" style="float: right">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import * as Tool from "@/utils/tool";
import { getFuzzyTracCd } from "@/api/vec";
import { getWharfList } from "@/api/video";

export default {
  components: {
    Searchbar
  },
  data: function () {
    return {
      placeType: "",
      showList: true,
      tableHeight: 500,
      title: null,
      showQuery: false,
      currentRow: null,
      radio: null,
      listLoading: false,
      list: [],
      tracList: [], //牵引车模糊搜索列表
      traiList: [], //挂车模糊搜索列表
      searchItems: {
        normal: [
          {
            name: "车牌号",
            field: "cd",
            type: "fuzzy",
            dbfield: "cd",
            dboper: "cn",
            api: this.getTracCd
          },
          {
            name: "运单号",
            field: "argmtCd",
            type: "text",
            dbfield: "argmt_cd",
            dboper: "cn"
          },
          {
            name: "货物",
            field: "goodsNm",
            type: "text",
            dbfield: "goods_nm",
            dboper: "cn"
          },
          {
            name: "登记日期",
            field: "wtTm",
            type: "daterange",
            dbfield: "wt_tm",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss"
          }
        ],
        more: []
      },
      defaultSearchItems: [],
      pagination: {
        page: 1,
        limit: 15
      },
      total: 0
    };
  },
  created() {
    let filters = {
      groupOp: "AND",
      rules: []
    };
    filters.rules.push({ field: "cat_cd", op: "eq", data: "2100.185.150.150" });
    let params = Object.assign(
      {},
      { filters: filters },
      { limit: 999, page: 1 }
    );
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.$refs.searchbar.init(query);

    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    //判断充装类型
    icCdType(iccdType) {
      var iccdMap = {
        0: "途径登记",
        1: "充装",
        "-1": "卸货"
      };
      return iccdMap[iccdType];
    },
    showQueryPanel: function () {
      this.showQuery = !this.showQuery;
    },
    delRows: function (index, row) {
      //删除过磅数据
      if (row && row.argmtWtPk) {
        var argmtWtPk = row.argmtWtPk;
        var that = this;
        this.$confirm("您是否确定删除该过磅数据？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消"
        })
          .then(() => {
            $.ajax({
              url: baseURL + "argmtWt/del/",
              type: "GET",
              data: { pk: argmtWtPk },
              success: function (data) {
                if (data.pojo.json.success) {
                  this.$message({
                    type: "success",
                    message: "删除成功!"
                  });
                } else {
                  that.$message({
                    message: "删除数据失败",
                    type: "error"
                  });
                }
              },
              error: function (error) {
                that.$message({
                  message: "删除数据失败",
                  type: "error"
                });
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除"
            });
          });
      }
    },
    loadAddr(row) {
      this.$router.push({
        path: "/base/wb/form/" + row.argmtWtPk,
        query: { ipPk: row.ipPk }
      });
    },

    // 详情
    showDetail(row) {
      if (row.checkStatus === null) {
        // 人工查验展示一单四状态页面
        this.$router.push({ path: "/base/rteplan/bills/" + row.argmtPk });
      } else {
        // 智慧登记
        this.$nextTick(() => {
          this.$refs.ckModalRef.open(row);
        });
      }
    },
    //历史轨迹
    histroyTra() {
      let location = window.location;
      let vehicleNo = this.currentRow.cd || this.currentRow.traiCd;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack?v=" +
        encodeURIComponent(vehicleNo) +
        "&t=" +
        Tool.formatDate(new Date(), "yyyy-MM-dd"),
        "_blank"
      );
    },
    //单选事件
    handleSelectionChange(currentRow, oldCurrentRow) {
      this.radio = currentRow.argmtWtPk;
      this.currentRow = currentRow;
    },

    loadQtyFormatter: function (row, column) {
      var value = row.loadQty;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      }
      if (value > 1000) {
        return value;
      } else {
        return (value * 1000).toFixed(0);
      }
    },
    weighFormatter: function (row, column) {
      var value = row.weigh;
      if (value == 0 || value == "" || value == null) {
        return "空车";
      } else {
        return value;
      }
    },

    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight =
          Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    //后端排序
    handleSort(sort) {
      let sortParam;
      let orderType = sort.order == "ascending" ? "ASC" : "DESC"; //升序或降序
      let fieldNm = sort.prop; //排序字段名

      sortParam = { sidx: fieldNm, order: orderType };
      this.getList(null, sortParam);
    },
    // 获取列表
    getList: function (data, sortParam) {
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      sortParam = sortParam || {};

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );
      if (this.defaultSearchItems.length) {
        param.filters.rules = param.filters.rules.concat(
          this.defaultSearchItems
        );
      }
      this.radio = null; //清空单选
      //关闭loading
      this.listLoading = true;
      //查询列表
      getWharfList(param)
        .then(response => {
          if (response.code == 0) {
            _this.list = response.page.list;
            _this.total = response.page.totalCount;
          } else {
            _this.list = [];
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    //牵引车模糊搜索
    async getTracCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.154", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.tracList = [];
          return;
        }
        this.tracList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.tracList);
      }
    },
    //挂车模糊搜索
    async getTraiCd(queryString, cb) {
      if (queryString.length <= 2) {
        cb([]);
        return;
      }
      const res = await getFuzzyTracCd("1180.155", queryString).catch(error => {
        cb([]);
        console.log(error);
      });
      if (res) {
        if (res.code !== 0) {
          this.traiList = [];
          return;
        }
        this.traiList = res.data.map(item => {
          return { value: item.name };
        });
        cb(this.traiList);
      }
    },
    // 判断进出镇海
    isInOrOut(num) {
      switch (num) {
        case "ZHCK0001":
        case "ZHCK0003":
        case "ZHCK0006":
        case "ZHCK0007":
          return true;
        default:
          return false;
      }
    }
  }
};
</script>

<style scoped>
.el-table .cell,
.el-table th>div {
  padding-left: 4px;
  padding-right: 4px;
  box-sizing: border-box;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
</style>
