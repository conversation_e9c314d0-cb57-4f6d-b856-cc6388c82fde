<template>
  <div class="timeline-wrap visible" v-show="isShow" ref="dialog">
    <div class="timeline-header">
      <span>当前车辆：{{selectVecInfo.vehicleNo}}，轨迹时间：{{selectVecInfo.searchDate}}</span>
      <div class="oper-bar">
        <span class="setting-content">
          <el-checkbox-group v-model="trackSetting" @change="settingChangeHandle" class="checkbox-has-no-checkbox" style="display:inline;">
            <el-checkbox v-for="item in settingsOptions" :label="item.value" :key="item.value">
              <span class="on" :style="{'padding-left':'25px','background': 'url('+ (trackSetting.indexOf(item.value)>=0 ? onImg : offImg)+') no-repeat'}"> {{item.label}}</span>
            </el-checkbox>
          </el-checkbox-group>
        </span>
        <span @click="hide(true)" style="margin:5px;margin-left:20px;font-size:16px;" title="关闭播放进度条">
          <svg-icon icon-class="close" class-name="svg-icon"></svg-icon>
        </span>
      </div>
    </div>
    <div class="timeline">
      <div class="timelineControl">
        <div class="ward" @click="backwardHandle()" title="减速">
          <svg-icon icon-class="backward" class="wardIcon" class-name="svg-icon"></svg-icon>
        </div>
        <div ref="timelinePlay" class="timelinePlay" :class="{'timelinePause':timelineStatus==='stop'}" @click="timelineHandle()"></div>
        <div class="ward" @click="forwardHandle" title="加速">
          <svg-icon icon-class="forward" class="wardIcon" class-name="svg-icon"></svg-icon>
        </div>
        <!-- <div class="timeClock">
          <span>
            <svg-icon icon-class="time" class-name="svg-icon"></svg-icon>{{timeClock}}
          </span>
          <span>
            <svg-icon icon-class="speed" class-name="svg-icon"></svg-icon>{{speedInTheTime}} km/h
          </span>
        </div> -->

        <!-- <div class="timelineSlow"></div>
            <div class="timelineQuick"></div> -->
      </div>
      <div class="timelineMain" @mouseover="showTimeTips($event)" @mouseleave="hideTimeTips($event)" @click="renderCurrentTimeTrace">
        <div class="clearfix timeHour-wrapper" ref="timeHourWrapper">
          <div class="timeHour timeHourFirst"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour"></div>
          <div class="timeHour timeHourFinal"></div>
        </div>
        <div @mouseleave="hideTimeTips($event)">
          <div class="timeNumber" style="left: -2px;">0</div>
          <div class="timeNumber" style="left: 28px;">1</div>
          <div class="timeNumber" style="left: 58px;">2</div>
          <div class="timeNumber" style="left: 88px;">3</div>
          <div class="timeNumber" style="left: 118px;">4</div>
          <div class="timeNumber" style="left: 148px;">5</div>
          <div class="timeNumber" style="left: 178px;">6</div>
          <div class="timeNumber" style="left: 208px;">7</div>
          <div class="timeNumber" style="left: 238px;">8</div>
          <div class="timeNumber" style="left: 268px;">9</div>
          <div class="timeNumber" style="left: 292px;">10</div>
          <div class="timeNumber" style="left: 322px;">11</div>
          <div class="timeNumber" style="left: 352px;">12</div>
          <div class="timeNumber" style="left: 382px;">13</div>
          <div class="timeNumber" style="left: 412px;">14</div>
          <div class="timeNumber" style="left: 442px;">15</div>
          <div class="timeNumber" style="left: 472px;">16</div>
          <div class="timeNumber" style="left: 502px;">17</div>
          <div class="timeNumber" style="left: 532px;">18</div>
          <div class="timeNumber" style="left: 562px;">19</div>
          <div class="timeNumber" style="left: 592px;">20</div>
          <div class="timeNumber" style="left: 622px;">21</div>
          <div class="timeNumber" style="left: 652px;">22</div>
          <div class="timeNumber" style="left: 682px;">23</div>
          <div class="timeNumber" style="left: 712px;">24</div>
          <div ref="timelineProgress" class="timelineProgress" :style="{'left':progressLeft+'px'}" @mousedown="mousedownHandle($event)">
          </div>
          <div class="gpsRunTrace" title="当前速度是0" :style="{'left':gpsRunTrace.left+'px','width':gpsRunTrace.width+'px'}"></div>
          <template v-for="(runpartItem,runpartIndex) in runpartList">
            <div class="runPart" :key="runpartIndex" :title="runpartItem.title" :style="{left:runpartItem.left+'px','width':runpartItem.width+'px'}">
            </div>
          </template>
        </div>
        <div class="timelineLabel" :style="{display:isShowTimeLineLabel?'block':'none',left:timeLineLabelLeft?timeLineLabelLeft+'px':0}">
          <div class="timelineLabelcontent">
            {{timeLineLabel}}
          </div>
          <div class="timelineLabelpointer"></div>
        </div>
        <div class="caliperA" style="left: 0px;">
          <div class="caliperLine"></div>
          <div class="caliperPointerA"></div>
        </div>
        <div class="caliperB" style="left: 721px;">
          <div class="caliperLine"></div>
          <div class="caliperPointerB"></div>
        </div>
        <div class="caliperPartA" style="width: 0px;"></div>
        <div class="caliperPartB" style="width: 0px;"></div>
      </div>
      <div class="times">x{{times}}</div>
    </div>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
export default {
  props: {
    dataStyle: {
      type: Object,
      default() {
        return {
          bottom: 0,
          left: 0
        };
      }
    },
    settings: {
      type: Array,
      default() {
        return ["speed", "time"];
      }
    }
  },
  inject: ["getMap"],
  data() {
    return {
      map: null,
      isShow: false,
      trackSetting: this.settings,
      trackSettingResult: this.settings,
      settingsOptions: [
        { label: "速度", value: "speed", icon: "view" },
        { label: "时间", value: "time", icon: "view" },
        { label: "日期", value: "date", icon: "view" }
        // { label: "停车时长", value: "parkingtime", icon: "view" }
      ],
      onImg: imgsConfig.rescue.eye_on,
      offImg: imgsConfig.rescue.eye_off,

      selectVecInfo: {
        vehicleNo: null,
        searchDate: null
      },

      timelineStatus: null,
      timelineTask: null,
      // timeClock: "00:00:00", // 时间轴上某时刻
      speedInTheTime: 0, // 时间轴上某时刻的速度
      progressLeft: 0, // 播放器播放的距离
      startDrag: false,
      gpsRunTrace: {
        // 有gsp的时间轨道长度
        left: 0,
        width: 0
      },
      runpartList: [], // 有速度的播放长度
      vecMarker: null,
      traceCache: null,
      vecIcon: new BMap.Icon(
        "static/img/monitor-img/_icon.png",
        new BMap.Size(73, 73)
      ),
      stopIcon: new BMap.Icon(
        "static/img/monitor-img/stop.png",
        new BMap.Size(30, 70)
      ),
      startIcon: new BMap.Icon(
        "static/img/monitor-img/start.png",
        new BMap.Size(30, 60)
      ),
      endIcon: new BMap.Icon(
        "static/img/monitor-img/end.png",
        new BMap.Size(30, 60)
      ),
      overlay: null, // 轨迹线路覆盖物
      timer: null,
      interv: 15,
      marker: null,

      timeSpeed: 500, //定时器时间
      times: 1, //倍数

      isShowTimeLineLabel: false,
      timeLineLabelLeft: 0,
      timeLineLabel: "00:00"
    };
  },
  destroyed() {
    if (this.timelineTask) {
      clearInterval(this.timelineTask);
    }
  },
  methods: {
    show() {
      this.isShow = true;
    },
    hide(hasCheck, callback) {
      let fun = function(callback) {
        this.isShow = false;
        this.reset();
        if (callback) {
          callback.call();
        }
      };
      if (hasCheck) {
        this.$confirm("是否确认关闭进度条?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          fun.call(this, callback);
        });
      } else {
        fun.call(this, callback);
      }
    },
    // moveDialog() {
    //   let _this = this;
    //   let dialog = this.$refs.dialog;
    //   document.onmousemove = function(event) {
    //     console.log("onmousemove");
    //     if (_this.isDrop) {
    //       let e = e || window.event;
    //       let moveX = e.clientX - _this._clientX;
    //       let moveY = e.clientY - _this._clientY;

    //       let maxX = document.documentElement.clientWidth - dialog.offsetWidth;
    //       let maxY =
    //         document.documentElement.clientHeight - dialog.offsetHeight;

    //       moveX = Math.min(maxX, Math.max(0, moveX));

    //       moveY = Math.min(maxY, Math.max(0, moveY));
    //       dialog.style.left = moveX + "px";
    //       dialog.style.top = moveY + "px";
    //     }

    //     return false;
    //   };
    // },
    // mouseupHandle() {
    //   let _this = this;
    //   document.onmouseup = function() {
    //     console.log("onmouseup");
    //     _this.isDrop = false;
    //   };
    // },
    // mouseDownHandle(event) {
    //   let dialog = this.$refs.dialog;
    //   let e = e || window.event;
    //   this._clientX = e.clientX - dialog.offsetLeft;
    //   this._clientY = e.clientY - dialog.offsetTop;

    //   this.isDrop = true;

    //   this.moveDialog();
    //   this.mouseupHandle();
    // },

    reset() {
      if (this.map) {
        this.vecMarker && this.map.removeOverlay(this.vecMarker);
        this.startMarker && this.map.removeOverlay(this.startMarker);
        this.endMarker && this.map.removeOverlay(this.endMarker);
        this.overlay && this.map.removeOverlay(this.overlay);
      }
      this.clearVecTrace();
      this.times = 1;
      this.timeSpeed = 500;
      this.timer = null;
      this.interv = 15;
      this.marker = null;

      this.selectVecInfo.vehicleNo = null;
      this.selectVecInfo.searchDate = null;

      this.timelineStatus = null;
      this.timelineTask = null;
      // this.timeClock = "00:00:00"; // 时间轴上某时刻
      this.speedInTheTime = 0; // 时间轴上某时刻的速度
      this.progressLeft = 0; // 播放器播放的距离
      this.startDrag = false;
      this.gpsRunTrace.left = 0;
      this.gpsRunTrace.width = 0;
      this.runpartList = []; // 有速度的播放长度
      this.vecMarker = null;
      this.traceCache = [];
    },
    settingChangeHandle() {
      let res = [];
      this.settingsOptions.forEach(item => {
        if (this.trackSetting.indexOf(item.value) >= 0) {
          res.push(item.value);
        }
      });
      this.trackSettingResult = res;
    },
    showTrace(vehicleNo, searchDate, traceData) {
      this.show();
      this.createVecGpsTrace(vehicleNo, searchDate, traceData);
    },
    // 播放器点击
    timelineHandle() {
      if (this.timelineStatus == "stop") {
        clearInterval(this.timelineTask);
        this.timelineStatus = "start";
      } else {
        this.playVecTrace();
        this.timelineStatus = "stop";
      }
    },

    indexOfSmallest(arr) {
      let lowest = 0;
      for (let i = 1; i < arr.length; i++) {
        if (arr[i] < arr[lowest]) lowest = i;
      }
      return lowest;
    },

    // 播放历史轨迹
    playVecTrace() {
      let _this = this;
      let todayTime = Date.parse(
        new Date(this.selectVecInfo.searchDate + " 00:00:00")
      );
      this.timelineTask = setInterval(function() {
        if (_this.progressLeft >= 720) {
          _this.timelineStatus == "stop";
          clearInterval(_this.timelineTask);
        } else {
          _this.progressLeft = _this.progressLeft + 0.2;
          let currentTime = todayTime + 120 * _this.progressLeft * 1000;
          let timeString = new Date(currentTime);
          let valueDiff = [];
          //找到当前时间点附近的坐标
          if (_this.traceCache != null && _this.traceCache.length > 0) {
            for (let i = _this.traceCache.length - 1; i >= 0; i--) {
              let preTime = _this.traceCache[i].updateTimeStamp;
              valueDiff[i] = Math.abs(currentTime - preTime);
            }

            var minIndex = _this.indexOfSmallest(valueDiff);
            // _this.timeClock = Tool.formatDate(timeString, "HH:mm:ss");
            _this.speedInTheTime = _this.traceCache[minIndex].speed;
            _this.vecMarker.setPosition(
              new BMap.Point(
                _this.traceCache[minIndex].lonBd,
                _this.traceCache[minIndex].latBd
              )
            );
            _this.vecMarker.setRotation(
              _this.traceCache[minIndex].direction - 90
            );

            if (_this.trackSettingResult.length > 0) {
              let msg = [];
              let currentTraceData = _this.traceCache[minIndex];
              _this.trackSettingResult.forEach(item => {
                if (item === "time" || item === "date") {
                  if (currentTraceData.updateTime) {
                    let date = new Date(currentTraceData.updateTime);
                    msg.push(
                      item === "time"
                        ? Tool.formatDate(date, "HH:mm:ss")
                        : Tool.formatDate(date, "yyyy-MM-dd")
                    );
                  }
                } else if (item === "speed") {
                  msg.push(currentTraceData.speed + "km/h");
                }
                // else if (item === "parkingtime") {
                //   if (currentTraceData.minutes) {
                //     msg.push("停了" + currentTraceData.minutes + "分钟");
                //   }
                // }
              });
              let label = _this.vecMarker.getLabel();
              if (msg.length > 0) {
                if (label) {
                  label.setContent(msg.join("，"));
                  label.setStyle({
                    //给label设置样式，任意的CSS都是可以的
                    display: "block",
                    zIndex: "9999"
                  });
                }
              } else {
                if (label) {
                  label.setContent("");
                  label.setStyle({
                    //给label设置样式，任意的CSS都是可以的
                    display: "none"
                  });
                }
              }
            }

            let point = new BMap.Point(
              _this.traceCache[minIndex].lonBd,
              _this.traceCache[minIndex].latBd
            );
            if (_this.speedInTheTime > 0) {
              // 为了避免gps数据首尾都有速度值的情况，当移动的点在gsp数据时间之外时，以上获取的最近的点时gps的首位数据
              if (
                currentTime >= _this.traceCache[0].updateTimeStamp &&
                currentTime <=
                  _this.traceCache[_this.traceCache.length - 1].updateTimeStamp
              ) {
                _this.map.setCenter(point);
              }
            }
          }
        }
      }, _this.timeSpeed);
    },

    // 清空车辆历史轨迹播放
    clearVecTrace() {
      this.gpsRunTrace.left = 0;
      this.gpsRunTrace.width = 0;

      this.runpartList = [];
      this.progressLeft = 0;
      if (this.timelineTask) {
        clearInterval(this.timelineTask);
      }
      this.timelineStatus = "start";
      // this.timeClock = "00:00:00"; // 重置时间轴时间
      this.speedInTheTime = 0; // 重置时间轴速度
    },

    // 绘制历史轨迹
    createVecGpsTrace(vehicleNo, searchDate, data) {
      this.reset();

      this.selectVecInfo.vehicleNo = vehicleNo;
      this.selectVecInfo.searchDate = searchDate;
      this.map = this.getMap();
      this.traceCache = data;

      // 清理老的轨迹数据
      this.progressLeft = 0;

      this.runpartList = []; // 清空车辆行驶时间轴
      this.gpsRunTrace.left = 0; // 清空有gps时间轨道
      this.gpsRunTrace.width = 0;

      clearInterval(this.timelineTask); // 清除播放历史轨迹的定时器
      this.timelineStatus = "start";

      let startTime,
        endTime,
        todayTime,
        startPosition,
        endPosition,
        widthPosition,
        backgroundColor;

      //当前日期时间戳
      todayTime = Date.parse(new Date(searchDate + " 00:00:00")) / 1000;

      // 默认gsp时间间隔内数据都为0
      startTime = data[0].updateTimeStamp / 1000;
      endTime = data[data.length - 1].updateTimeStamp / 1000;
      //把时间转换为位置
      startPosition = Math.round((startTime - todayTime) / 120);
      endPosition = Math.round((endTime - todayTime) / 120);
      widthPosition = endPosition - startPosition;
      this.$set(this.gpsRunTrace, "left", startPosition);
      this.$set(this.gpsRunTrace, "width", widthPosition);

      //对每段进行渲染
      for (var i = 0; i < data.length - 1; i++) {
        if (data[i].speed == 0) {
          continue;
        }

        let j = i,
          minSpeed,
          maxSpeed,
          title;
        minSpeed = maxSpeed = data[i].speed;
        while (data[j + 1] && data[j + 1].speed > 0) {
          // 多个具有速度的时间段进行叠加绘制
          if (minSpeed > data[j + 1].speed) {
            minSpeed = data[j + 1].speed;
          }
          if (maxSpeed < data[j + 1].speed) {
            maxSpeed = data[j + 1].speed;
          }
          j++;
        }
        if (i == j) {
          startTime = data[i].updateTimeStamp / 1000;
          endTime = data[i + 1].updateTimeStamp / 1000;
          title = `${data[i].updateTime}，速度为${data[i].speed}km/h`;
        } else {
          startTime = data[i].updateTimeStamp / 1000;
          endTime = data[j].updateTimeStamp / 1000;
          title = `"${data[i].updateTime}至${data[j].updateTime}"时间内，速度为${minSpeed}km/h ~ ${maxSpeed}km/h`;
        }
        //把时间转换为位置
        startPosition = Math.round((startTime - todayTime) / 120);
        widthPosition = ((endTime - startTime) / 120).toFixed(2);

        // 只绘制具有速度的片段
        this.runpartList.push({
          left: startPosition,
          width: widthPosition,
          title: title
        });

        i = j;
      }
      // 绘制车辆在地图上的行驶路线
      this.drawDrivingTrace(data);
    },

    // 绘制车辆在地图上的行驶路线
    drawDrivingTrace(data) {
      let points = [];
      data.forEach(item => {
        points.push(item.lonBd + "," + item.latBd);
      });

      let track = points.join(";");
      let pointArray = [];
      let boundaries = [track];
      let ply = new BMap.Polyline(track, {
        strokeColor: "#0000FF",
        strokeOpacity: 0.7,
        strokeWeight: 5
      }); //建立多边形覆盖物
      this.overlay = ply;
      this.map.addOverlay(ply); //添加覆盖物
      pointArray = pointArray.concat(ply.getPath());
      this.map.setViewport(pointArray);
      //添加起始点
      if (data.length > 0) {
        //起点
        this.startMarker = new BMap.Marker(
          new BMap.Point(data[0].lonBd, data[0].latBd),
          { icon: this.startIcon }
        );
        this.map.addOverlay(this.startMarker);
        //重点
        this.endMarker = new BMap.Marker(
          new BMap.Point(
            data[data.length - 1].lonBd,
            data[data.length - 1].latBd
          ),
          { icon: this.endIcon }
        );
        this.map.addOverlay(this.endMarker);

        //移动点
        this.vecMarker = new BMap.Marker(
          new BMap.Point(data[0].lonBd, data[0].latBd),
          { icon: this.vecIcon }
        );
        let label = new BMap.Label("", {
          offset: new BMap.Size(55, 25)
        });
        label.setStyle({
          color: "#000000",
          borderWidth: "1px",
          borderColor: "#cdcdcd",
          backgroundColor: "#ffffff",
          borderRadius: "3px",
          padding: "1px 3px"
        });
        this.vecMarker.setLabel(label);
        this.map.addOverlay(this.vecMarker);
        // this.map.setCenter(new BMap.Point(data[0].lonBd, data[0].latBd));     // 设置起始点为中心
      }
    },

    // 鼠标点击
    mousedownHandle(e) {
      // 当时间轴是否可点击开始的标识为false时，不允许拖拽
      let _this = this,
        x = e.clientX,
        el = e.target;

      let oldLeft = parseFloat(el.style.left);
      this.startDrag = true;
      document.onmousemove = function(e) {
        if (_this.startDrag) {
          let nx = e.clientX - x;
          let distance = nx + oldLeft;
          if (distance < 0) {
            distance = 0;
          } else if (distance > 720) {
            distance = 720;
          }
          el.style.left = distance + "px";

          // 渲染当前时间，显示当前车辆位置
          _this.renderDragedTrace(distance);
        }
        e.preventDefault();
      };
      document.onmouseup = function(e) {
        _this.mouseupHandle(e);
      };
      e.stopPropagation();
    },

    // 鼠标松开的时候
    mouseupHandle(e) {
      this.startDrag = false;
      document.onmousemove = document.onmouseup = null;
    },

    renderDragedTrace(distance) {
      this.progressLeft = distance;
      // 设置当前时间
      let todayTime = Date.parse(
        new Date(this.selectVecInfo.searchDate + " 00:00:00")
      ); // 获取毫秒数
      let currentTime = todayTime + 120 * distance * 1000;
      let timeString = new Date(currentTime);
      // if (!isNaN(currentTime)) {
      //     timeString.setTime(currentTime * 1000);
      // }
      let valueDiff = [];
      //找到当前时间点附近的坐标
      if (this.traceCache != null && this.traceCache.length > 0) {
        for (let i = this.traceCache.length - 1; i >= 0; i--) {
          let preTime = this.traceCache[i].updateTimeStamp;
          valueDiff[i] = Math.abs(currentTime - preTime);
        }

        var minIndex = this.indexOfSmallest(valueDiff);
        // this.timeClock = Tool.formatDate(timeString, "HH:mm:ss");
        this.speedInTheTime = this.traceCache[minIndex].speed;
        this.vecMarker.setPosition(
          new BMap.Point(
            this.traceCache[minIndex].lonBd,
            this.traceCache[minIndex].latBd
          )
        );
        this.vecMarker.setRotation(this.traceCache[minIndex].direction - 90);

        if (this.trackSettingResult.length > 0) {
          let msg = [];
          let currentTraceData = this.traceCache[minIndex];
          this.trackSettingResult.forEach(item => {
            if (item === "time" || item === "date") {
              if (currentTraceData.updateTime) {
                let date = new Date(currentTraceData.updateTime);
                msg.push(
                  item === "time"
                    ? Tool.formatDate(date, "HH:mm:ss")
                    : Tool.formatDate(date, "yyyy-MM-dd")
                );
              }
            } else if (item === "speed") {
              msg.push(currentTraceData.speed + "km/h");
            }
            // else if (item === "parkingtime") {
            //   if (currentTraceData.minutes) {
            //     msg.push("停了" + currentTraceData.minutes + "分钟");
            //   }
            // }
          });
          let label = this.vecMarker.getLabel();
          if (msg.length > 0) {
            if (label) {
              label.setContent(msg.join("，"));
              label.setStyle({
                //给label设置样式，任意的CSS都是可以的
                display: "block",
                zIndex: "9999"
              });
            }
          } else {
            if (label) {
              label.setContent("");
              label.setStyle({
                //给label设置样式，任意的CSS都是可以的
                display: "none"
              });
            }
          }
        }

        let point = new BMap.Point(
          this.traceCache[minIndex].lonBd,
          this.traceCache[minIndex].latBd
        );
        if (this.speedInTheTime > 0) {
          this.map.setCenter(point);
        }
      }
    },
    // 加速
    forwardHandle() {
      if (this.timeSpeed <= 10) {
        this.timeSpeed = 10;
        this.$message({
          message: "已经最快喽",
          type: "warning",
          center: true
        });
        return;
      } else {
        this.timeSpeed = this.timeSpeed / 2;
        this.times = this.times * 2;
        this.$message({
          message: "加速成功 x" + this.times,
          center: true
        });
      }
      clearInterval(this.timelineTask);
      if (this.timelineStatus == "stop") {
        this.playVecTrace();
      }
    },
    //减速
    backwardHandle() {
      if (this.timeSpeed >= 500) {
        this.timeSpeed = 500;
        this.$message({
          message: "已经最慢喽",
          type: "warning",
          center: true
        });
        return;
      } else {
        this.timeSpeed = this.timeSpeed * 2;
        this.times = this.times / 2;
        this.$message({
          message: "减速成功 x" + this.times,
          center: true
        });
      }
      clearInterval(this.timelineTask);
      if (this.timelineStatus == "stop") {
        this.playVecTrace();
      }
    },

    // 显示时间轴上的时间信息
    showTimeTips(e) {
      let distance =
        e.clientX - this.$refs.timeHourWrapper.getBoundingClientRect().left;
      if (distance < 0) {
        distance = 0;
      } else if (distance > 720) {
        distance = 720;
      }
      this.timeLineLabelLeft = distance;
      // 设置当前时间
      let todayTime = Date.parse(
        new Date(this.selectVecInfo.searchDate + " 00:00:00")
      ); // 获取毫秒数
      let currentTime = todayTime + 120 * distance * 1000;
      // let timeString = new Date(currentTime);
      this.timeLineLabel = Tool.formatDate(new Date(currentTime), "HH:mm");
      this.isShowTimeLineLabel = true;
    },
    // 显示时间轴上的时间信息
    hideTimeTips(e) {
      this.isShowTimeLineLabel = false;
    },
    renderCurrentTimeTrace(e) {
      let distance =
        e.clientX - this.$refs.timeHourWrapper.getBoundingClientRect().left;
      if (distance < 0) {
        distance = 0;
      } else if (distance > 720) {
        distance = 720;
      }
      this.timeLineLabelLeft = distance;
      // 渲染当前时间，显示当前车辆位置
      this.renderDragedTrace(distance);
      this.isShowTimeLineLabel = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.timeline-wrap {
  z-index: 999;
  position: absolute;
  width: 900px;
  bottom: 10px;
  margin-left: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.8);

  .timeline-header {
    position: relative;
    background: #12305a;
    font-size: 12px;
    line-height: 28px;
    padding: 0 5px;
    color: #fff;
    border-radius: 5px 5px 0 0;

    .oper-bar {
      float: right;
      cursor: pointer;

      .checkbox-has-no-checkbox /deep/ {
        .el-checkbox__input {
          display: none;
        }
        .el-checkbox {
          margin-right: 10px;
        }
      }
    }
  }

  .timeline {
    position: relative;
    margin-left: 0;
    left: 0;
    bottom: auto;
    width: 100%;
    height: 4vw;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    -webkit-box-shadow: 1px 1px 3px 1px #cbcbcb;
    box-shadow: 1px 1px 3px 1px #cbcbcb;

    .timelineControl {
      width: 130px;
      height: 100%;
      float: left;

      .ward {
        float: left;
        cursor: pointer;
        margin-top: 14px;
      }

      .wardIcon {
        width: 30px !important;
        height: 30px !important;
      }

      .timelinePlay,
      .timelineSlow,
      .timelineQuick,
      .timelinePause {
        float: left;
        height: 100%;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        opacity: 1;
      }

      .timelinePlay {
        width: 50px;
        /*width:4vw;*/
        /* background-image: url('~/static/img/monitor-img/play_2x_fa25689.png'); */
        background-image: url(data:image/png;base64,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);
        background-size: 40px 40px;

        &:hover {
          /* background-image: url('~/static/img/monitor-img/playhover_2x_f591e77.png') */
          background-image: url(data:image/png;base64,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);
        }
      }

      // .timeClock {
      //   position: relative;
      //   float: right;
      //   height: 100%;
      //   /* line-height: 4vw; */
      //   background-position: center;
      //   background-repeat: no-repeat;
      //   opacity: 1;
      //   width: 90px;
      //   font-size: 13px;

      //   > span {
      //     display: inline-block;
      //     line-height: 2vw;
      //     vertical-align: top;
      //     position: relative;
      //     padding-left: 20px;
      //   }
      //   .svg-icon {
      //     font-size: 16px;
      //     position: absolute;
      //     top: 1vw;
      //     margin-top: -8px;
      //     left: 0;
      //     color: #2398ff;
      //   }
      // }

      .timelinePause {
        width: 50px;
        /* background-image: url('~/static/img/monitor-img/pause_2x_f2c7329.png'); */
        background-image: url(data:image/png;base64,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);
        background-size: 40px 40px;

        &:hover {
          /* background-image: url('~/static/img/monitor-img/pausehover_2x_8e2341e.png') */
          background-image: url(data:image/png;base64,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);
        }
      }

      .timelineSlow {
        width: 35px;
        background-image: url("~/static/img/monitor-img/slow_2x_2d13d8b.png");
        background-size: 28px 28px;

        &:hover {
          background-image: url("~/static/img/monitor-img/slowhover_2x_e9ab905.png");
        }
      }

      .timelineQuick {
        width: 35px;
        background-image: url("~/static/img/monitor-img/quick_2x_2507c60.png");
        background-size: 28px 28px;

        &:hover {
          background-image: url("~/static/img/monitor-img/quickhover_2x_ee12a70.png");
        }
      }
    }

    .timelineMain {
      width: 730px;
      float: left;
      height: 100%;
      position: relative;
      cursor: pointer;

      .timelineLabel {
        position: absolute;
        height: 30px;
        width: 44px;
        // top: -33px;
        margin-left: -22px;
        z-index: 20;
        top: -16px;
        transition: all 0.1s ease;

        .timelineLabelpointer:after {
          content: "";
          display: block;
          height: 6px;
          width: 1px;
          background: #494848;
          margin-left: -0.5px;
        }
      }

      .caliperPartA,
      .caliperPartB {
        position: absolute;
        height: 100%;
        top: 0;
        background-color: rgba(40, 40, 40, 0.21);
      }

      .caliperPartA {
        left: 0;
      }

      .caliperPartB {
        right: 0;
      }

      .timelineLabelcontent {
        width: 100%;
        height: 25px;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 2px;
        color: #efefef;
        line-height: 27px;
        text-align: center;
        font-size: 12px;
      }

      .timelineLabelpointer {
        height: 0;
        width: 0;
        border: 6px solid rgba(0, 0, 0, 0.7);
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        margin-left: 16px;
      }

      .timeHour {
        box-sizing: border-box;
        height: 6px;
        border-bottom: 1px solid #c2c2c4;
        border-top: 1px solid #c2c2c4;
        border-left: 1px solid #a3a3a4;
        width: 30px;
        float: left;
        margin-top: 16px;
        background-color: #c2c2c4;
      }

      .caliperA,
      .caliperB {
        height: 60px;
        position: absolute;
        width: 20px;
        margin-left: -10px;
      }

      .caliperPointerA,
      .caliperPointerB {
        height: 0;
        width: 0;
        border: 10px solid #45a1f3;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-left-width: 10px;
        border-right-width: 10px;
        cursor: pointer;
      }

      .caliperPointerA {
        margin-left: -9px;
        border-right-color: #45a1f3;
      }

      .caliperPointerB {
        margin-left: 9px;
        border-left-color: #45a1f3;
      }

      .caliperLine {
        height: 50px;
        width: 2px;
        margin: 0 auto;
        background-color: #45a1f3;
      }

      .caliperA {
        left: 0;
        display: none;
      }

      .caliperB {
        left: 721px;
        display: none;
      }

      .timeHourFirst {
        border-bottom-left-radius: 3px;
        border-top-left-radius: 3px;
      }

      .timeHourFinal {
        border-bottom-right-radius: 3px;
        border-top-right-radius: 3px;
        border-right: 1px solid #a3a3a4;
      }

      .timeNumber {
        position: absolute;
        height: 15px;
        width: 10px;
        font-size: 11px;
        bottom: 8px;
        color: #222;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .timelineProgress {
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAACxMAAAsTAQCanBgAAAGcSURBVDjLnZTNTsJAEMcb4wVPihw46M3EhMRX8arRxDciQQqojyGacG6hJz48GTAGvVBOQoyFGkNhnH+3pdvSKrrJdCednV9mZ3ZGISIFousNWQ5Z8iwtFkfXWISe92zR84oSAaVwuNls02BgkmVZtFgsXIGOf7B5wFQS6Jil1u+/0Hw+p6QFG85oWqPm+QQghFuvGzQej2ndhbPwYd8DCWSoCPuvCz4MUV0Qf47a7Y6bB3mZH0QXd0T7N0R716zfEw2tMAg+rVYHsBxAhWg0T3zD7RLRRlHsO2XWL1nnvfsWG1Ve4dL2bNsOGc+qRJvsmKkEslsW/06rYdB0OgXoERF9Ra+1pRKly8I54+2QdEXA5DWbOQC9J4NKHkiKKg7kOAzSGhZAz7b9GTKe3LJDMYjEBwICW8LVDNU0hyFjdyQS6ydYTnZvFJvsgsIvNL78XOpzTmyWS5+9EgWIKz988YT8l70S1foP0lDlFsn9p0UYQmIaGHFN+7pW0+LsStMmjZHJZLIEQBdjpPPrGAlEWw62B9bhSK7+w2D7BslIL12udjO3AAAAAElFTkSuQmCC);
        height: 40px;
        width: 40px;
        position: absolute;
        cursor: pointer;
        top: 0px;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 10;
        margin-left: -20px;
        cursor: move;
      }

      .gpsRunTrace {
        height: 6px;
        top: 16px;
        position: absolute;
        /* background-color: #e0ce28; */
        background-color: #6bccfb;
      }
      .runPart {
        height: 6px;
        top: 16px;
        position: absolute;
        background-color: #2398ff;
        /* border: 1px solid #218eef */
      }
    }

    .times {
      width: 30px;
      margin-left: 5px;
      float: left;
      margin-top: 15px;
      color: #2398ff;
    }
  }
}
</style>