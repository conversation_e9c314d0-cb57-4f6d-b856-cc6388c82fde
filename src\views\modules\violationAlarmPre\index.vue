<template>
  <div style="padding: 10px">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="resizeSearchbar"
      @search="getList" class="grid-search-bar">
      <!-- <template slot="ratio-suffix"
                slot-scope="scope">
        <el-tooltip v-if="scope.scope.value"
                    class="item"
                    effect="dark"
                    :content="alarmTypeDescMap[scope.scope.value]"
                    placement="right">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </template> -->
      <template slot="button">
        <el-button type="primary" size="small">
          <router-link tag="span" :to="{ path: '/kpi/offsite/list' }">处置台账</router-link>
        </el-button>
        <el-button type="primary" size="small">
          <router-link tag="span" :to="{ path: '/queryVecByRegion/query.html' }">区域查车</router-link>
        </el-button>
      </template>
      <template slot="normalChildSlot" slot-scope="{ scope: item }">
        <el-form-item label="疲劳驾驶等级" v-if="alarmGrade.show && item.field == 'catCd'">
          <el-radio-group v-model="alarmGrade.value" size="small" @change="selectAalarmGradeHandle">
            <el-radio-button label="" :value="''">全部</el-radio-button>
            <el-radio-button label="1" :value="1">1级</el-radio-button>
            <el-radio-button label="2" :value="2">2级</el-radio-button>
            <el-radio-button label="3" :value="3">3级</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </template>
    </searchbar>

    <!--工具条-->
    <div class="toolbar clearfix" style="margin-bottom: 8px">
      <div class="grid-operbar ft-lf">
        <el-button type="primary" icon="el-icon-document" size="small" @click="handleDownload">导出</el-button>
        <el-button type="warning" icon="el-icon-document" size="small" @click="jzVisible = true">高危介质导出</el-button>
      </div>
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float: right" :page-size="pagination.limit" :current-page.sync="pagination.page"
        :total="pagination.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <el-row :gutter="10" v-loading="listLoading">
      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="item in list" :key="item.cd" style="height: 360px">
        <div class="info-card-wape">
          <div class="info-card-content">
            <h3 style="margin: 10px 0">
              <b>{{ item.catNmCn.replace(/GPS/g, '卫星定位') }}
                <span v-if="item.catCd == '2550.7120.180' && item.grade">{{ item.grade }}级</span>
              </b>
              <div class="rightTop-tag">
                <template v-if="item.govHandlerType <= 2">
                  <span>已受理</span>
                </template>
                <span v-else>
                  {{
                    (item.govHandlerType &&
                      alarmDealActions[item.govHandlerType] &&
                      alarmDealActions[item.govHandlerType].label) ||
                    ""
                  }}
                </span>
                <!-- <template v-else-if="item.isHandle==4">备案错误</template>
                  <template v-else-if="item.isHandle==5">填报错误</template>
                  <template v-else-if="item.isHandle==6">超载小于0.5吨</template>
                  <template v-else-if="item.isHandle==7">批评教育</template>
                  <template v-else-if="item.isHandle==8">纳入考核</template>
                  <template v-else-if="item.isHandle==9">约谈警示</template>
                  <template v-else-if="item.isHandle==10">行政处罚</template>
                  <template v-else-if="item.isHandle==11">停办通行证</template>
                  <template v-else-if="item.isHandle==99">已撤销</template>
                  <template v-else-if="item.isHandle==12">其他</template> -->
              </div>
              <span class="driver-sign" v-if="item.catCd === '2550.7120.180' && item.isDp === 1">双驾双押</span>
            </h3>
            <div>
              <p class="list clearfix nowrap">
                <span class="col-xs-12 col-sm-12 col-md-12 col-lg-12 item" v-if="reminder(item) || rest(item)">
                  <label for="" class="icon-red-tag" title="提示" style="padding: 0 6px;">!</label>
                  <span>
                    <el-tag v-if="reminder(item) == '空载'" size="small">空载</el-tag>
                    <el-tag v-if="reminder(item) == '重载'" type="warning" size="small">重载</el-tag>
                  </span>
                </span>

                <span class="col-xs-12 col-sm-12 col-md-12 col-lg-12 item">
                  <label for="" class="icon-green-tag" title="承运商">承</label>
                  <span>
                    <a :title="'查看' + item.entpNm" href="javascript:void(0)" @click="goToEntpPage(item.entpPk)">{{
                      item.entpNm }}</a>
                  </span>
                </span>
                <span class="col-xs-12 col-sm-6 col-md-6 col-lg-6 item">
                  <label for="" class="icon-red-tag" title="牵引车号">牵</label>
                  <span>
                    <a class="emphsis" :title="'查看牵引车' + item.tractorNo" href="javascript:void(0)"
                      @click="goToVecPage(item.tractorPk)">
                      <b>{{ item.tractorNo }}</b>
                    </a>
                  </span>
                </span>
                <span class="col-xs-12 col-sm-6 col-md-6 col-lg-6 item">
                  <label for="" class="icon-purple-tag" title="挂车号">挂</label>
                  <span>
                    <a v-if="item.trailerNo" :title="'查看挂车' + item.trailerNo" href="javascript:void(0)"
                      @click="goToVecPage(item.trailerPk)">{{ item.trailerNo }}</a>
                    <span v-else>无</span>
                  </span>
                </span>
                <span class="col-xs-12 col-sm-6 col-md-6 col-lg-6 item">
                  <label for="" class="icon-blue-tag" title="驾驶员">驾</label>
                  <span>
                    <a v-if="item.driverNm" :title="'查看驾驶员' + item.driverNm" href="javascript:void(0)"
                      @click="goToPersPage(item.driverPk)">{{ item.driverNm }}</a>
                    <span v-else>无</span>
                  </span>
                </span>
                <span class="col-xs-12 col-sm-6 col-md-6 col-lg-6 item">
                  <label for="" class="icon-blue-tag" title="押运员">押</label>
                  <span>
                    <a v-if="item.guardsNm" :title="'查看押运员' + item.guardsNm" href="javascript:void(0)"
                      @click="goToPersPage(item.guardsPk)">{{ item.guardsNm }}</a>
                    <span v-else>无</span>
                  </span>
                </span>
                <span class="col-xs-12 col-sm-6 col-md-6 col-lg-6 item">
                  <label for="" class="icon-blue-tag" title="电子运单号">电</label>
                  <span>
                    <a v-if="item.argmtCd" :title="'查看电子运单' + item.argmtCd" href="javascript:void(0)"
                      @click="goToRteplanPage(item.argmtPk)">{{ item.argmtCd }}</a>
                    <span v-else>无</span>
                  </span>
                </span>
              </p>
            </div>
            <div class="dash"></div>
            <div style="height: 130px; overflow-y: auto; text-overflow: ellipsis">
              <!-- #ZHYS-2013 卡口监控》预警查询 夜间停车预警（需要显示车辆的载重情况） -->
              <div class="need clearfix" v-if="item.catCd === '2550.160.281'">
                <p class="list">
                  <label for="" class="icon-orange-tag" title="报警时间">载重状态</label>
                  <span>{{ getGradeStatusLabel(item.grade) }}</span>
                </p>
              </div>
              <div class="need clearfix">
                <p class="list">
                  <label for="" class="icon-orange-tag" title="报警时间">时间</label>
                  <span>{{ item.alarmTime | FormatDate("yyyy-MM-dd HH:mm:ss") }}</span>
                </p>
              </div>
              <div class="need clearfix">
                <p class="list">
                  <label for="" class="icon-orange-tag" title="报警地点">地点</label>
                  <span>{{ item.alarmLocation }}</span>
                </p>
              </div>
              <div v-if="item.catCd == '2550.170.170'" class="need clearfix">
                <p class="list">
                  <label for="" class="icon-orange-tag" title="通行证线路">通行证线路</label>
                  <span>{{ item.threshold }}</span>
                </p>
              </div>
              <p class="list clearfix" style="margin-bottom: 8px">
                <label for="" class="icon-orange-tag" title="报警详情">详情</label>
                <span>{{ item.descr.replace(/GPS/g, '卫星定位') }}</span>
              </p>
            </div>
            <div class="text-center" style="margin: 0 -15px">
              <el-button-group>
                <!-- <el-button type="warning" size="mini" title="导出" v-if="item.catCd == '2550.7120.180' || item.catCd == '2550.160.150'" @click="exportExcel(item)">导出</el-button> -->
                <el-button type="info" size="mini" title="查看报警地址"
                  @click.native.prevent="showAlarmLocation(item)">报警地</el-button>
                <el-button type="success" size="mini" title="查看历史轨迹"
                  @click.native.prevent="showAlarmHistry(item)">历史轨迹</el-button>
                <!-- <el-button type="danger" v-if="item.catNmEn === 'TractorNotRecord'" size="mini" title="卡口抓拍图片" @click.native.prevent="cameraShot(item)">抓拍图片</el-button> -->
                <!-- <el-button type="primary" size="mini" title="处理" @click="dealHandle(item.alarmPrePk)">处理</el-button> -->
                <!-- 卡口监控》预警查询 夜间停车预警（需要进行处置功能） -->
                <el-button v-if="item.catCd === '2550.160.281'" type="primary" size="mini" title="处置"
                  @click="nightParkingFeedback(item)">处置</el-button>
                <!-- <el-dropdown class="custom-dropdown">
                  <span class="el-dropdown-link">
                    <el-button type="primary" size="mini">处理</el-button>
                  </span>
                  <el-dropdown-menu slot="dropdown" class="custom-dropdown-menu">
                    <el-dropdown-item @click.native="dealHandle(item.alarmPrePk)">违章报警处理</el-dropdown-item>
                    <el-dropdown-item @click.native="feedback(item)">处置反馈</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown> -->
                <!-- <el-button type="warning" size="mini" title="撤销" @click="revokeHandle(item.alarmPrePk)">撤销</el-button> -->
              </el-button-group>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div v-if="list.length == 0"
      style="padding: 60px 5px;color: #333;text-align: center;background: #fff;font-size: 13px;">
      暂无数据
    </div>
    <el-dialog title="违章报警处理" :visible.sync="dialogDealVisible" width="50%">
      <div>
        <el-radio-group v-model="dealDialog.isHandle" size="small">
          <el-radio label="7">批评教育</el-radio>
          <el-radio label="8">纳入考核</el-radio>
          <el-radio label="9">约谈警示</el-radio>
          <el-radio label="10">行政处罚</el-radio>
          <el-radio label="11">停办通行证</el-radio>
          <el-radio label="12">其他</el-radio>
        </el-radio-group>
        <div>
          <el-input v-model="dealDialog.remark" placeholder="请输入处理结果" v-if="dealDialog.isHandle == 12"
            size="small"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDealVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogDealSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="违章报警撤销" :visible.sync="dialogRevokeVisible" width="50%">
      <div>
        <el-radio-group v-model="revokeDialog.isHandle" size="small">
          <el-radio label="4">备案错误</el-radio>
          <el-radio label="5">填报错误</el-radio>
          <el-radio label="6">超载小于0.5吨</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogRevokeVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogRevokeSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="报警地理位置" :visible.sync="dialogAlarmVisible" :fullscreen="true">
      <div class="info-card-wape" style="line-height:20px;padding:0 10px;">
        <div class="need clearfix">
          <div class="list">
            <label for="" class="icon-orange-tag" title="报警时间">时间</label>
            <span>{{ selectedAlarmInfo.alarmTime | FormatDate("yyyy-MM-dd HH:mm:ss") }}</span>
          </div>
        </div>
        <div class="need clearfix">
          <div class="list">
            <label for="" class="icon-orange-tag" title="报警地点">地点</label>
            <span>{{ selectedAlarmInfo.alarmLocation }}</span>
          </div>
        </div>
        <div class="list clearfix" style="margin-bottom: 8px">
          <label for="" class="icon-orange-tag" title="报警详情">详情</label>
          <span>{{ selectedAlarmInfo.descr.replace(/GPS/g, '卫星定位') }}</span>
        </div>
      </div>
      <div style="position:relative;">
        <div class="fiexd-alarm-point">
          <el-checkbox border size="mini" @change="alarmPointLockHandle" v-model="fiexdAlarmPoint">锁定报警点</el-checkbox>
        </div>
        <div v-loading="dialogAlarmLoading" ref="mapWape" id="mapWape" :style="{ height: 0.6 * clientHeight + 'px' }">
        </div>
      </div>
    </el-dialog>

    <!-- 异常停车处置窗口 -->
    <feedback-dialog ref="feedbackDialog" @refresh="refreshGrid"></feedback-dialog>
    <!-- 夜间停车预警处置窗口 -->
    <night-parking-dialog ref="nightParkingDialog" @refresh="refreshGrid"></night-parking-dialog>

    <el-dialog :visible.sync="jzVisible" title="高危介质导出" :close-on-click-modal="true" width="450px">
      <el-form ref="jzForm" :model="jzForm" label-width="100px" :size="size">
        <el-form-item label="导出时间" prop="alarmTime" :rules="$rulesFilter({ required: true })">
          <el-date-picker v-model="jzForm.alarmTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            placeholder="请选择导出时间" clearable></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="jzVisible = false" :size="size">取 消</el-button>
        <el-button type="primary" @click="preWeightSubmitHandler" :size="size">导 出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Searchbar from "@/components/Searchbar";
import feedbackDialog from "./feedback-dialog";
import nightParkingDialog from "./component/night-parking-dialog";
import * as $http from "@/api/violationAlarmPre";
import * as Tool from "@/utils/tool";
import Cookies from "js-cookie";
import { mapGetters } from "vuex";

export default {
  name: "ViolationAlarm",
  data() {
    return {
      size: "small",
      clientHeight: Tool.getClientHeight(),
      list: [],
      listLoading: false,
      dialogAlarmLoading: false,

      alarmGrade: {
        field: "grade",
        value: "",
        show: false
      },

      pagination: {
        total: 0,
        page: 1,
        limit: 20
      },
      alarmTypeDescMap: {
        "2550.160.150":
          "超过规定速度行驶违法行为信息,应当包括卫星定位系统记录的限速数值、行驶速度，连续性时间间隔应不高于30秒，定时报送的持续超过道路限速信息数据应不少于4次(点);",
        "2550.7120.180":
          "白天连续行驶4小时，夜间（22:00-06:00）连续行驶2小时，连续是指一直有速度",
        "2550.7120.155": "卡口摄像头拍到车辆在系统没有备案",
        "2550.7120.190":
          "1.卡口摄像头拍到车辆在系统没有电子运单  2.车辆进入茂名电子围栏，且有收费站进出记录但没有电子运单",
        "2550.7120.160": "1.卫星定位未接入系统 2.充装站记录装卸单时该车辆卫星定位已离线1小时以上",
        "2550.7120.170": "5分钟内车辆没有定位数据上报",
        "2550.170.275":
          "停车地不是停车场，不是装卸地，不是加油站等可以停车的地方超过30分钟",
        "2550.160.180": "1.货物重量超过核载质量  2.货物+挂车重量超过准牵引质量",
        "2550.160.185": "装卸货物类别和车辆经营类型不一致",
        "2550.7120.171": "重车停放10分钟报警",
        "2550.170.170": "运行路线超出通行证规定的路线"
      },
      map: null,
      setMapCenterEvnListener: null,
      alarmPoint: null,
      fiexdAlarmPoint: true,

      dialogDealVisible: false, // 违章报警处理弹窗显示
      dealDialog: {
        alarmPrePks: "",
        isHandle: "",
        remark: ""
      },
      dialogRevokeVisible: false, // 违章报警撤销弹窗显示
      revokeDialog: {
        alarmPrePks: "",
        isHandle: ""
      },

      dialogAlarmVisible: false, // 报警地点弹窗
      baseAPI: process.env.BASE_API,

      selectedAlarmInfo: {
        alarmTime: "",
        alarmLocation: "",
        descr: ""
      },
      jzVisible: false,
      // 高危导出介质form：时间
      jzForm: {
        alarmTime: this.get30Date(7)
      }
    };
  },
  computed: {
    ...mapGetters(["alarmDealActions", "allAlarmDealOptions", "alarmDealOptions"]),
    searchItems() {
      let rolesName = localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
      if (rolesName.indexOf("gov_dljc") >= 0) {
        return {
          normal: [
            {
              name: "报警类型",
              field: "catCd",
              type: "radio",
              options: [
                { label: "全部违章", value: "" }
                //{ label: "超速报警", value: "2550.160.150" },
                // { label: "超速预警", value: "2550.170.150" },
                //{ label: "偏离路线预警", value: "2550.170.170" },
                //{ label: "超载", value: "2550.160.180" },
                //{ label: "疲劳驾驶", value: "2550.7120.180" },
                // { label: "超经营范围报警", value: "2550.160.185" },
                // { label: '无电子运单报警', value: '2550.7120.190' },
                //{ label: "异常停车预警", value: "2550.170.275" },
                //{ label: "无GPS", value: "2550.7120.160" },
                // { label: 'GPS离线', value: '2550.7120.170' },
                //{ label: "未备案报警", value: "2550.7120.155" },
                //{ label: "GPS轨迹缺失报警", value: "2550.7120.165" },
                //{ label: "临江互通闯禁报警", value: "2550.7120.200" },
              ],
              dbfield: "cat_cd",
              dboper: "eq",
              // default: "2550.160.150"
            },
            {
              name: "企业名称",
              field: "entpNm",
              type: "text",
              dbfield: "entp_nm",
              dboper: "cn"
            },
            {
              name: "牵引车",
              field: "tractorNo",
              type: "text",
              dbfield: "tractor_no",
              dboper: "cn"
            },
            {
              name: "挂车号",
              field: "trailerNo",
              type: "text",
              dbfield: "trailer_no",
              dboper: "cn"
            },

            {
              name: "驾驶员",
              field: "driverNm",
              type: "text",
              dbfield: "driver_nm",
              dboper: "cn"
            }
          ],
          more: [
            {
              name: "处理类型",
              field: "isHandle",
              type: "select",
              postdifferent: true,
              options: [
                {
                  label: "全部处理类型",
                  value: "",
                  dboper: "eq",
                  postData: ""
                },
                { label: "已受理", value: "2", dboper: "eq", postData: "2" },
                { label: "已处置", value: "20", dboper: "eq", postData: "20" },
                { label: "已撤销", value: "99", dboper: "eq", postData: "99" }
                // { label: "备案错误", value: "4", dboper: "eq", postData: "4" },
                // { label: "填报错误", value: "5", dboper: "eq", postData: "5" },
                // {
                //   label: "超载小于0.5吨",
                //   value: "6",
                //   dboper: "eq",
                //   postData: "6"
                // },
                // { label: "批评教育", value: "7", dboper: "eq", postData: "7" },
                // { label: "纳入考核", value: "8", dboper: "eq", postData: "8" },
                // { label: "约谈警示", value: "9", dboper: "eq", postData: "9" },
                // {
                //   label: "行政处罚",
                //   value: "10",
                //   dboper: "eq",
                //   postData: "10"
                // },
                // {
                //   label: "停办通行证",
                //   value: "11",
                //   dboper: "eq",
                //   postData: "11"
                // },
                // { label: "其他", value: "12", dboper: "eq", postData: "12" }
              ],
              dbfield: "is_handle_gov",
              dboper: "nao"
            },
            {
              name: "违章时间",
              field: "alarmTime ",
              type: "daterange",
              dbfield: "alarm_time",
              dboper: "bt",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              default: this.get30Date()
            }
          ]
        };
      }
      return {
        normal: [
          {
            name: "报警类型",
            field: "catCd",
            type: "radio",
            options: [
              { label: "全部违章", value: "" }
              //{ label: "超速报警", value: "2550.160.150" },
              // { label: "超速预警", value: "2550.170.150" },
              //{ label: "偏离路线预警", value: "2550.170.170" },
              //{ label: "超载", value: "2550.160.180" },
              //{ label: "疲劳驾驶", value: "2550.7120.180" },
              //{ label: "超经营范围报警", value: "2550.160.185" },
              // { label: '无电子运单报警', value: '2550.7120.190' },
              //{ label: "异常停车预警", value: "2550.170.275" },
              //{ label: "无GPS", value: "2550.7120.160" },
              // { label: 'GPS离线', value: '2550.7120.170' },
              //{ label: "未备案报警", value: "2550.7120.155" },
              //{ label: "GPS轨迹缺失报警", value: "2550.7120.165" },
              //{ label: "临江互通闯禁报警", value: "2550.7120.200" },
            ],
            dbfield: "cat_cd",
            dboper: "eq",
            // default: "2550.160.150"
          },
          {
            name: " 企业名称",
            field: "entpNm",
            type: "text",
            dbfield: "entp_nm",
            dboper: "cn"
          },
          {
            name: "牵引车",
            field: "tractorNo",
            type: "text",
            dbfield: "tractor_no",
            dboper: "cn"
          },
          {
            name: "挂车号",
            field: "trailerNo",
            type: "text",
            dbfield: "trailer_no",
            dboper: "cn"
          },

          {
            name: "驾驶员",
            field: "driverNm",
            type: "text",
            dbfield: "driver_nm",
            dboper: "cn"
          }
        ],
        more: [
          {
            name: "处理类型",
            field: "isHandle",
            type: "select",
            postdifferent: true,
            options: [
              { label: "全部处理类型", value: "", dboper: "eq", postData: "" },
              { label: "已受理", value: "2", dboper: "nao", postData: "2" },
              { label: "已处置", value: "98", dboper: "nao", postData: "98" }
              // { label: "已撤销", value: "99", dboper: "eq", postData: "99" }
              // { label: "备案错误", value: "4", dboper: "eq", postData: "4" },
              // { label: "填报错误", value: "5", dboper: "eq", postData: "5" },
              // {
              //   label: "超载小于0.5吨",
              //   value: "6",
              //   dboper: "eq",
              //   postData: "6"
              // },
              // { label: "批评教育", value: "7", dboper: "eq", postData: "7" },
              // { label: "纳入考核", value: "8", dboper: "eq", postData: "8" },
              // { label: "约谈警示", value: "9", dboper: "eq", postData: "9" },
              // { label: "行政处罚", value: "10", dboper: "eq", postData: "10" },
              // {
              //   label: "停办通行证",
              //   value: "11",
              //   dboper: "eq",
              //   postData: "11"
              // },
              // { label: "其他", value: "12", dboper: "eq", postData: "12" }
            ],
            dbfield: "is_handle_gov",
            dboper: "nao"
          },
          {
            name: "违章时间",
            field: "alarmTime",
            type: "daterange",
            dbfield: "alarm_time",
            dboper: "bt",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            default: this.get30Date()
            // default: this.getFirstAndNowDate()
          }
        ]
      };
    }
  },
  components: {
    Searchbar,
    feedbackDialog,
    nightParkingDialog,
  },
  mounted: function () {
    const _this = this;
    window.addEventListener("resize", this.setTableHeight);

    // localStorage.removeItem("roleName");
    // Cookies.set("WHJK-ROLENAME","gov_admin#gov_check_lic")
    // Cookies.set("WHJK-ROLENAME","gov_dljc")

    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;

    $http
      .getAlarmType()
      .then(response => {
        let list = response.data;
        if (response.code == 0 && list) {
          let alarmList = list.map(item => {
            return { label: item.nmCn, value: item.cd };
          });
          //判断角色是否为稽查大队gov_dljc，否则删除"超经营范围报警"
          let rolesName = localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
          if (!rolesName || rolesName.indexOf("gov_dljc") == -1) {
            alarmList.forEach((item, index) => {
              if (item.label.indexOf("超经营范围") >= 0) {
                alarmList.splice(index, 1);
              }
            });
          }

          this.searchItems.normal[0].options = this.searchItems.normal[0].options.concat(
            alarmList
          );
          this.$refs.searchbar.init(query);
          this.getList();
        }
      })
      .catch(error => {
        console.log(error);
      });

    // this.$refs.searchbar.init(query);

    this.setTableHeight();
    // this.getList();

    // this.$nextTick(() => {
    //     // 初始化地图信息
    //     this.initMap();
    // });
  },
  destroyed() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    setTableHeight() {
      this.$nextTick(() => {
        this.clientHeight = Tool.getClientHeight();
      });
    },

    // 改变搜索框的高度
    resizeSearchbar() {
      this.setTableHeight();
    },

    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      // this.getList();
      this.$refs.searchbar.searchHandle(true);
    },
    //获取近30天日期
    get30Date(day = 30) {
      var TimeNow = new Date();
      var startDay = new Date(TimeNow - 1000 * 60 * 60 * 24 * day);
      var endDay = TimeNow;
      return [
        Tool.formatDate(startDay, "yyyy-MM-dd") + " 00:00:00",
        Tool.formatDate(endDay, "yyyy-MM-dd") + " 23:59:59"
      ];
    },

    selectAalarmGradeHandle(val) {
      this.getList();
    },

    insertAalrmGrade(param) {
      // 拦截过滤条件数据，并插入疲劳驾驶等级
      let rules = null;
      let isFatigue = false;
      try {
        rules = param.filters.rules || [];
        if (rules.length) {
          for (var i = 0, len = rules.length; i < len; i++) {
            if (
              rules[i]["field"] == "cat_cd" &&
              rules[i]["data"] == "2550.7120.180"
            ) {
              isFatigue = true;
              break;
            } else {
              continue;
            }
          }

          if (isFatigue && this.alarmGrade.value != "") {
            rules.push({
              field: "grade",
              op: "eq",
              data: this.alarmGrade.value
            });
          }
        }
      } catch (e) { }
      this.$set(this.alarmGrade, "show", isFatigue);
      return param;
    },
    getFirstAndNowDate() {
      var TimeNow = new Date();
      var startDay = new Date(TimeNow.getFullYear(), TimeNow.getMonth(), 1); //当月1号
      var endDay = new Date(
        TimeNow.getFullYear(),
        TimeNow.getMonth(),
        TimeNow.getDate(),
        23,
        59,
        59
      );
      return [
        Tool.formatDate(startDay, "yyyy-MM-dd HH:mm:ss"),
        Tool.formatDate(endDay, "yyyy-MM-dd HH:mm:ss")
      ];
    },
    // 获取数据
    getList: function (data, sortParam) {
      let _this = this;
      let filters;

      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }

      let param = Object.assign(
        {},
        sortParam,
        { filters: filters },
        this.pagination
      );

      delete param.total;

      param = this.insertAalrmGrade(param);

      this.listLoading = true;
      $http
        .getViolationAlarmList(param)
        .then(response => {
          if (response && response.code === 0) {
            _this.pagination.total = response.page.totalCount;
            _this.list = response.page.list;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
          }
          _this.listLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.listLoading = false;
        });
    },

    // 修改查询
    refreshGrid: function () {
      this.getList();
    },
    drawOverlay(data, type) {
      let _this = this;
      let allPoints = [];
      if (type === "polygon") {
        let polygons = [];
        for (var i = 0, len = data.length; i < len; i++) {
          var lnglat = data[i];
          var lines = lnglat.match(/(\b(\w+\.?\w{0,})\b(?!['":]))/g);
          var lnglatLen = lines.length;
          var j = 0;
          var points = [];
          for (; j < lnglatLen; j += 2) {
            var point = new BMap.Point(lines[j], lines[j + 1]);
            points.push(point);
            allPoints.push(point);
          }
          polygons.push(
            new BMap.Polygon(points, {
              strokeColor: "#e12828",
              fillColor: "#e12828",
              strokeWeight: 2,
              fillOpacity: 0.3
            })
          );
        }
        polygons.filter(item => {
          this.map.addOverlay(item);
        });
      } else if (type === "polyline") {
        let drawLines = [];
        for (var i = 0, len = data.length; i < len; i++) {
          var lnglat = data[i];
          var createRes = Tool.createPolylineByJSON({
            map: _this.map,
            lines: lnglat
          });
          if (createRes && createRes.lines.length) {
            allPoints = allPoints.concat(createRes.pointes);
            drawLines = drawLines.concat(createRes.lines);
          }
        }
        drawLines.forEach(item => {
          _this.map.addOverlay(item);
        });
        allPoints.length && _this.map.setViewport(allPoints);
      }
    },
    // 绘制线路
    addAlarmPolygon(item) {
      let _this = this;
      this.dialogAlarmLoading = true;
      // 高峰期闯禁行报警
      if (item.catCd === "2550.170.200") {
        if (item.alarmValue === null) {
          console.log("alarmValue不存在，无法显示禁行路线！");
          return;
        }
        // 绘制禁行路线
        $http
          .getNoEntryRouteByRteLinePk(item.alarmValue)
          .then(res => {
            if (res.code == 0) {
              let rteLine = res.rteLine;
              if (rteLine) {
                // 线路
                if (rteLine.catCd === '1107.150') {
                  rteLine.line && this.drawOverlay([rteLine.line], 'polyline');
                } else if (rteLine.catCd === '1107.160') {
                  // 区域
                  rteLine.line && this.drawOverlay([rteLine.line], 'polygon');
                }
              }
            }
            this.dialogAlarmLoading = false;
          })
          .catch(err => {
            this.dialogAlarmLoading = false;
          });
      } else {
        // 绘制通行证线路
        let alarmTime = item.alarmTime.slice(0, 10) || '';
        $http
          .getRouteByVecNo({ vecNo: item.tractorNo, activeDay: alarmTime })
          .then(res => {
            if (res.code == 0) {
              let data = res.data;
              let routes = [];
              for (var i = 0, len = data.length; i < len; i++) {
                if (data[i] && data[i].line) {
                  var lnglat = data[i].line;
                  if (lnglat) {
                    routes.push(lnglat);
                  }
                }
              }
              this.drawOverlay(routes, 'polyline');
            }
            this.dialogAlarmLoading = false;
          })
          .catch(err => {
            this.dialogAlarmLoading = false;
          });
      }
    },

    initMap() {
      if (!this.map) {
        this.map = new BMap.Map("mapWape"); // 创建Map实例
        this.map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP, BMAP_HYBRID_MAP],
            anchor: BMAP_ANCHOR_BOTTOM_RIGHT
          })
        );
        this.map.disableAutoResize(false); //禁用自动适应容器尺寸变化
        this.map.setCurrentCity("镇海"); // 设置地图显示的城市 此项是必须设置的
        this.map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
        // 设置中心点
        let point = new BMap.Point(121.66386, 30.001186);
        this.map.centerAndZoom(point, 12);

        /************************镇海区域***************************/
        this.getBoundary();
        this.map.removeEventListener("zoomend");
      }
    },

    //获取行政区域
    getBoundary() {
      var bdary = new BMap.Boundary();
      var map = this.map;

      bdary.get("宁波市镇海区", function (rs) {
        //获取行政区域
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域"
          });
          return;
        }
        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 3,
            fillOpacity: 0.0,
            fillColor: "none",
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed"
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
        }
      });
    },

    setMapCenterByPoint(point) {
      this.map.setCenter(point);
    },
    alarmPointLockHandle() {
      if (this.fiexdAlarmPoint) {
        this.alarmPoint && this.map.setCenter(this.alarmPoint);
        this.setMapCenterEvnListener &&
          this.map.addEventListener("zoomend", this.setMapCenterEvnListener);
      } else {
        this.setMapCenterEvnListener &&
          this.map.removeEventListener("zoomend", this.setMapCenterEvnListener);
      }
    },
    //导出疲劳驾驶 excel
    exportExcel(item) {
      let catCd = item.catCd;
      let alarmPrePk = item.alarmPrePk;
      let tractorNo = item.tractorNo;
      let catNmCn = item.catNmCn.replace(/GPS/g, "卫星定位");
      let xhrNm = null;
      if (catCd === "2550.7120.180") {
        // 疲劳驾驶
        // xhrNm = "exportTiredDrive"
        window.open(
          process.env.BASE_API +
          "/alarm/exportovertimebyday?id=" +
          alarmPrePk +
          "&token=" +
          this.$store.getters.token
        );
      } else if (catCd === "2550.160.150") {
        // 超速报警
        // xhrNm = "exportOverSpeed"
        window.open(
          process.env.BASE_API +
          "/alarm/overSpeed/export?id=" +
          alarmPrePk +
          "&token=" +
          this.$store.getters.token
        );
      }
      // if(xhrNm && alarmPrePk){
      //   $http[xhrNm](alarmPrePk).then( response => {
      //     if(response && response.code===0){
      //       this.$message.error(response.msg);
      //       return;
      //     }
      //     let a = document.createElement('a');
      //     let blob = new Blob([response]);
      //     let url = window.URL.createObjectURL(blob);
      //     a.href = url;
      //      var _date = new Date();
      //     a.download = "牵引车号"+tractorNo+"-"+catNmCn+"信息_"+(_date.getFullYear()+'-'+(_date.getMonth()+1)+'-'+_date.getDate())+'.xlsx';
      //     a.click();
      //     window.URL.revokeObjectURL(url);
      //   })
      //   .catch( error =>{
      //       throw new Error(error);
      //   });
      // }
    },
    // 显示报警地点
    showAlarmLocation(item) {
      let _this = this;
      this.dialogAlarmVisible = true;
      this.selectedAlarmInfo = item;
      this.map &&
        this.setMapCenterEvnListener &&
        this.map.removeEventListener("zoomend", this.setMapCenterEvnListener);
      this.map = null;
      this.$nextTick(() => {
        // 初始化地图信息
        this.initMap();
        this.addAlarmPolygon(item);
        if (item.alarmLongitude === null || item.alarmLatitude === null) {
          this.$message({
            message: "对不起，该报警地点无法显示",
            type: "error"
          });
          this.map.clearOverlays();
          return;
        }
        let point = new BMap.Point(item.alarmLongitude, item.alarmLatitude);
        let setMapCenterEvnListener = function () {
          _this.setMapCenterByPoint(point);
        };

        this.setMapCenterEvnListener = setMapCenterEvnListener;
        this.alarmPoint = point;

        this.map.clearOverlays();
        this.map.centerAndZoom(point, 13);
        let marker = new BMap.Marker(point);
        this.map.addOverlay(marker);

        marker.addEventListener("click", function () {
          _this.getUnfinishLast(item);
        });

        this.map.addEventListener("zoomend", setMapCenterEvnListener);
      });
    },

    cameraShot(item) {
      let url = item.url;
      if (url) {
        let location = window.location;
        window.open(url, "_blank");
      } else {
        this.$message({
          type: "info",
          message: "无抓拍图片"
        });
      }
    },

    getUnfinishLast(alarmData) {
      let _this = this;
      $http.getUnfinishLast({ tracCd: alarmData.tractorNo }).then(res => {
        _this.addInfoWin(alarmData, data.result.item);
      });
    },

    addInfoWin(alarmData, data) {
      let _this = this;
      let pt = new BMap.Point(
        alarmData.alarmLongitude,
        alarmData.alarmLatitude
      );
      let geo = new BMap.Geocoder();
      geo.getLocation(pt, function (result) {
        if (result) {
          let address = result.address;
          let opts1 = {
            width: 270, // 信息窗口宽度
            height: 240, // 信息窗口高度
            title: alarmData.tractorNo, // 信息窗口标题
            enableMessage: false, //设置允许信息窗发送短息
            message: ""
          };
          let win = "<div class='maptable'><table><tbody>";
          if (data) {
            win +=
              "<tr><td align='right'><span>牵引车号：</span></td><td ><a onclick='showVehicleDialog(this)' value='" +
              data.tracPk +
              "'>" +
              data.tracCd +
              "</a></td></tr>" +
              "<tr><td align='right'><span>车挂号：</span></td><td ><a onclick='showVehicleDialog(this )'  value='" +
              data.traiPk +
              "'>" +
              data.traiCd +
              "</a></td></tr>" +
              "<tr><td align='right'><span>承运方：</span><td><a onclick='showEntpDialog(this)' value='" +
              data.carrierPk +
              "'>" +
              data.carrierNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>货物：</span><td><a>" +
              data.goodsNm +
              "</a>" +
              "|" +
              data.loadQty +
              "吨" +
              "</td></tr>" +
              "<tr><td align='right'><span>驾驶员：</span><td><a onclick='showPersDialog(this)' value='" +
              data.dvPk +
              "'>" +
              data.dvNm +
              "/" +
              data.dvMob +
              "</a></td></tr>" +
              "<tr><td align='right'><span>押运员：</span><td><a onclick='showPersDialog(this)' value='" +
              data.scPk +
              "'>" +
              data.scNm +
              "/" +
              data.scMob +
              "</a></td></tr>" +
              "<tr><td align='right'><span>收货方：</span><td>" +
              data.csnorWhseDist +
              "</td></tr>" +
              "<tr><td align='right'><span>发货方：</span><td>" +
              data.csneeWhseDist +
              "</td></tr>" +
              "<tr><td align='right'><span>发运日期：</span><td>" +
              data.vecDespTm +
              "</td></tr>";
          } else {
            win +=
              "<tr><td align='right'><span>牵引车号：</span></td><td ><a onclick='showVehicleDialog(this)' value='" +
              alarmData.tractorPk +
              "'>" +
              alarmData.tractorNo +
              "</a></td></tr>" +
              "<tr><td align='right'><span>车挂号：</span></td><td ><a onclick='showVehicleDialog(this )'  value='" +
              alarmData.trailerPk +
              "'>" +
              alarmData.trailerNo +
              "</a></td></tr>" +
              "<tr><td align='right'><span>企业：</span><td><a onclick='showEntpDialog(this)' value='" +
              alarmData.entpPk +
              "'>" +
              alarmData.entpNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>驾驶员：</span><td><a onclick='showPersDialog(this)' value='" +
              alarmData.driverPk +
              "'>" +
              alarmData.driverNm +
              "</a></td></tr>" +
              "<tr><td align='right'><span>押运员：</span><td><a onclick='showPersDialog(this)' value='" +
              alarmData.guardsPk +
              "'>" +
              alarmData.guardsNm +
              "</a></td></tr>";
            win +=
              "<tr><td align='right'><span>货物信息：</span></td><td>" +
              "无货物信息" +
              "</td></tr>";
          }
          let infowindow = new BMap.InfoWindow(win, opts1); // 创建信息窗口对象
          _this.map.openInfoWindow(infowindow, pt); //开启信息窗口
          infowindow.enableAutoPan();
          infowindow.addEventListener("clickclose", function () {
            infowindow = null;
          });
        }
      });
      //  map.panTo(pt);
    },

    // 历史轨迹
    showAlarmHistry(data) {
      if (!data.tractorNo) {
        this.$message({
          message: "对不起，该车辆历史轨迹无法查看",
          type: "error"
        });
        return;
      }
      let location = window.location;
      // window.open(
      //   location.origin +
      //   location.pathname +
      //   "#/monit/hisTrack?v=" +
      //   encodeURIComponent(data.tractorNo) +
      //   "&t=" +
      //   Tool.formatDate(data.alarmTime, "yyyy-MM-dd"),
      //   "_blank"
      // );
      window.open(
        process.env.NEW_APP_URL
        +
        "#/monitMap/hisTrack?v=" +
        encodeURIComponent(data.tractorNo) +
        "&t=" + data.alarmTime,
        "_blank"
      );
    },

    // 处理
    dealHandle(alarmPrePk) {
      this.dealDialog.alarmPrePks = alarmPrePk;
      this.dealDialog.isHandle = "";
      this.dealDialog.remark = "";
      this.dialogDealVisible = true;
    },

    // 处理提交
    dialogDealSubmit() {
      let _this = this;
      if (this.dealDialog.isHandle != 12) {
        this.dealDialog.remark = "";
      }
      $http
        .dealSubmit(this.dealDialog)
        .then(response => {
          if (response.code == 0) {
            _this.dialogDealVisible = false;
            _this.$message({
              message: "提交成功",
              type: "success"
            });
            _this.refreshGrid();
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 撤销
    revokeHandle(alarmPrePk) {
      this.revokeDialog.alarmPrePks = alarmPrePk;
      this.revokeDialog.isHandle = "";
      this.dialogRevokeVisible = true;
    },

    // 撤销提交
    dialogRevokeSubmit() {
      let _this = this;
      $http
        .dealSubmit(this.revokeDialog)
        .then(response => {
          if (response.code == 0) {
            _this.dialogRevokeVisible = false;
            _this.$message({
              message: "提交成功",
              type: "success"
            });
            _this.refreshGrid();
          } else {
            _this.$message({
              message: response.msg,
              type: "error"
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 查看承运商
    goToEntpPage(pk) {
      if (!pk) {
        this.$message({
          message: "对不起，该承运商信息无法查看",
          type: "error"
        });
        return;
      }
      window.open(
        window.location.origin +
        window.location.pathname +
        "#/base/entp/info/" +
        pk,
        "_blank"
      );
    },

    // 查看牵引车
    goToVecPage(pk) {
      if (!pk) {
        this.$message({
          message: "对不起，该牵引车信息无法查看",
          type: "error"
        });
        return;
      }
      window.open(
        window.location.origin +
        window.location.pathname +
        "#/base/vec/info/" +
        pk,
        "_blank"
      );
    },

    // 查看人员
    goToPersPage(pk) {
      if (!pk) {
        this.$message({
          message: "对不起，该人员信息无法查看",
          type: "error"
        });
        return;
      }
      window.open(
        window.location.origin +
        window.location.pathname +
        "#/base/pers/info/" +
        pk,
        "_blank"
      );
    },

    goToRteplanPage(argmtPk) {
      if (!argmtPk) {
        this.$message({
          message: "对不起，该电子运单信息无法查看",
          type: "error"
        });
        return;
      }
      window.open(
        window.location.origin +
        window.location.pathname +
        "#/base/rteplan/bills/" +
        argmtPk,
        "_blank"
      );
    },

    //导出
    handleDownload() {
      let _this = this;
      let filters = this.$refs.searchbar.get();
      let param = Object.assign({}, { filters: filters }, this.pagination);
      delete param.total;

      $http
        .downloadExcel(param)
        .then(response => {
          if (!response) {
            return;
          }
          let url = window.URL.createObjectURL(new Blob([response]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;

          let alarmTime = filters.rules.find(item => {
            return item.field === "alarm_time";
          });
          if (alarmTime) {
            let startDate = alarmTime.data[0];
            let endDate = alarmTime.data[1];
            alarmTime = `${startDate}至${endDate}`;
          } else {
            alarmTime = Tool.formatDate(new Date(), "yyyy-MM-dd_HHmmss");
          }
          link.setAttribute("download", `违章报警数据${alarmTime}.xlsx`);
          document.body.appendChild(link);
          link.click();
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 处置反馈
    nightParkingFeedback(data) {
      this.$refs.nightParkingDialog.init(data);
    },
    // 处置反馈
    feedback(data) {
      this.$refs.feedbackDialog.init(data);
    },

    // 获取车辆载重状态文本
    getGradeStatusLabel(grade) {
      if (grade === 10) {
        return "空车"
      } else if (grade === 30) {
        return "重车"
      } else {
        return "未知"
      }
    },

    // 高危介质导出
    preWeightSubmitHandler() {
      let _this = this;
      let time = this.jzForm.alarmTime;
      this.$refs.jzForm.validate(valid => {
        if (valid) {
          let param = Object.assign({
            page: 1,
            limit: 999
          }, { filters: { "groupOp": "AND", "rules": [{ "field": "alarm_time", "op": "bt", "data": time }] } });
          _this.downloadPreWeight(param, time);
        }
      })
    },
    // 高危介质导出
    downloadPreWeight(param, time) {
      let _this = this;
      $http
        .downloadPreWeight(param)
        .then(response => {
          if (!response) {
            return;
          }
          let url = window.URL.createObjectURL(new Blob([response]));
          let link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          if (time && time.length == 2) {
            let startDate = time[0];
            let endDate = time[1];
            time = `${startDate}至${endDate}`;
          } else {
            time = Tool.formatDate(new Date(), "yyyy-MM-dd_HHmmss");
          }
          link.setAttribute("download", `高危介质导出数据${time}.xlsx`);
          document.body.appendChild(link);
          link.click();
          _this.jzVisible = false;
        })
        .catch(error => {
          console.log(error);
        });
    },
    /**
     * @Date: 2023-07-17 11:08:48
     * @Author: SangShuaiKang
     * @description: 判断报警车辆是否是空重车
     * @param {*} info 报警详情数据
     * @return {*}
     */
    reminder(info) {
      if (!info.grade || info.grade === 0) return false;
      let value = info.grade;
      if (info.grade > 1000) {
        value = Number(info.grade.toString().slice(0, 2));
      }
      if (value === 10) {
        return "空载";
      } else if (value === 30) {
        return "重载";
      }
      return false;
    },
  }
};
</script>

<style scoped>
.emphsis {
  color: #d00;
}

.dash {
  border-bottom: 1px dashed #e3e3e3;
  margin: 8px 0;
}

.grid-search-bar {
  background-color: #fff;
  padding: 8px;
  margin-bottom: 8px;
}

.info-card-wape {
  font-size: 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 8px;
  -ms-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -webkit-border-radius: 8px;
  border: 1px solid #e3e3e3;
}

.info-card-wape a:hover {
  color: #d00;
}

.info-card-wape h3 {
  font-size: 20px;
}

.info-card-wape .list {
  margin: 0;
}

.info-card-wape .list .item {
  padding-left: 0;
}

.info-card-wape .list a {
  color: #666;
}

.info-card-content {
  padding: 0 16px 5px;
  line-height: 24px;
}

@media (max-width: 992px) {
  .info-card-content {
    min-height: auto;
  }
}

.info-card-content h2 label {
  background: #ff8101;
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label[class^='icon-bg'],
.info-card-wape label[class*=' icon-bg'] {
  color: #fff;
  padding: 0px 2px;
  font-size: 12px;
}

.info-card-wape label.icon-bg-orange,
.info-card-wape label.icon-bg-green,
.info-card-wape label.icon-bg-gray {}

.info-card-wape label.icon-bg-orange {
  background: #ff8101;
}

.info-card-wape label.icon-bg-green {
  background: #23b7e5;
}

.info-card-wape label.icon-bg-gray {
  background: #a3a3a373;
}

.info-card-wape label.icon-red-tag,
.info-card-wape label.icon-orange-tag,
.info-card-wape label.icon-yellow-tag,
.info-card-wape label.icon-green-tag,
.info-card-wape label.icon-blue-tag,
.info-card-wape label.icon-purple-tag,
.info-card-wape label.icon-gray-tag {
  padding: 0 2px;
  border: 1px solid #eee;
  border-radius: 2px;
  margin-right: 5px;
}

.info-card-wape label.icon-red-tag {
  color: red;
  border-color: red;
}

.info-card-wape label.icon-orange-tag {
  color: #ff8101;
  border-color: #ff8101;
}

.info-card-wape label.icon-yellow-tag {
  color: #fff07a;
  border-color: #fff07a;
}

.info-card-wape label.icon-green-tag {
  color: #5cb85c;
  border-color: #5cb85c;
}

.info-card-wape label.icon-blue-tag {
  color: #1e9fff;
  border-color: #1e9fff;
}

.info-card-wape label.icon-purple-tag {
  color: #6a65d8;
  border-color: #6a65d8;
}

.info-card-wape label.icon-gray-tag {
  color: #f4f4f4;
  border-color: #f4f4f4;
}

.tag-btn {
  vertical-align: middle;
  text-decoration: none !important;
  font-size: 12px;
  background: lavender;
  color: #333;
  padding: 3px 5px;
  border-radius: 5px;
  margin-right: 5px;
}

.tag-btn:hover {
  color: #d00 !important;
}

.rightTop-tag {
  display: inline-block;
  margin-left: 10px;
  opacity: 0.7;
  font-weight: bold;
  font-size: 12px;
  padding: 3px 5px;
  border-radius: 10px;
  line-height: 14px;
}

.rightTop-tag>span {
  color: #d00;
  background-color: yellow;
}

.driver-sign {
  color: #d00;
  font-weight: bold;
  background-color: #ffb307;
  font-size: 13px;
  line-height: 18px;
  padding: 2px 5px;
  border-radius: 4px;
  float: right;
}

.toolbar {
  background-color: #fff;
  padding: 10px 5px;
  border-radius: 5px;
}

/* 不换行 */
.nowrap {
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.fiexd-alarm-point {
  position: absolute;
  right: 18px;
  top: 20px;
  z-index: 9;
  border-radius: 4px;
  background-color: i#fff;
}
</style>
<style lang="scss" scoped>
.custom-dropdown {
  float: left;

  .el-button {
    border-radius: 0;
    margin-right: -1px;
  }
}

.custom-dropdown-menu /deep/ {
  background-color: #097aef;
  color: #fff;

  .popper__arrow {
    border-top-color: #097aef;
    border-bottom-color: #097aef;
    border-left-color: #097aef;
    border-right-color: #097aef;

    &::after {
      border-top-color: #097aef;
    }
  }

  .el-dropdown-menu__item {
    color: #fff;

    &:focus,
    &:not(.is-disabled):hover {
      background-color: #409eff;
      color: #ecf5ff;
    }
  }
}

.cicle {
  position: relative;
  top: 3px;
  display: inline-block;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin-right: 3px;

  &.fill {
    background: #ccc;
    border: 1px solid #ccc;
  }

  &.stroke {
    background: none;
    border: 1px solid #ccc;
  }

  // 半圆形
  &.semi {
    &:after {
      position: absolute;
      content: '';
      height: 16px;
      width: 8px;
      border-radius: 20px 0 0 20px;
      background: #ccc;
    }
  }

  &.green-edge {
    border: 1px solid #5daf34;

    &.fill {
      background: #5daf34;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #5daf34;
      }
    }
  }

  &.yellow-edge {
    border: 1px solid #ffd04b;

    &.fill {
      background: #ffd04b;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #ffd04b;
      }
    }
  }

  &.red-edge {
    border: 1px solid #d00;

    &.fill {
      background: #d00;
    }

    &.stroke {
      background: none;
    }

    &.semi {
      &:after {
        background: #d00;
      }
    }
  }
}
</style>
