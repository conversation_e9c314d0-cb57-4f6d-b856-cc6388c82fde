<template>
  <div class="violation-container">
    <div class="left-module" :class="[isCollapseOfLeftBar ? 'collapse' : '']">
      <div class="sidebar-wape">
        <div class="sidebar-head">筛查可疑</div>
        <div class="sidebar-content">
          <el-form :model="searchForm" ref="searchForm" label-width="0px">
            <ul class="el-timeline">
              <li class="el-timeline-item">
                <div class="el-timeline-item-tail"></div>
                <div
                  class="el-timeline-item-head el-timeline-item-head-green"
                ></div>
                <div class="el-timeline-item-content">
                  <h2>S1：设置查询时间</h2>
                  <div class="block">
                    <el-form-item
                      prop="searchRangeDate"
                      :rules="{
                        required: true,
                        message: '请选择查询时间',
                        trigger: 'blur',
                      }"
                      style="margin-bottom: 0"
                    >
                      <el-date-picker
                        v-model="searchForm.searchRangeDate"
                        type="datetimerange"
                        size="small"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                        :picker-options="datePickerOptions"
                        style="width: 100%"
                        popper-class="custom-white-picker-panel"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </div>
                </div>
              </li>
              <li class="el-timeline-item">
                <div class="el-timeline-item-tail"></div>
                <div
                  class="el-timeline-item-head el-timeline-item-head-green"
                ></div>
                <div class="el-timeline-item-content">
                  <h2>
                    S2：设置查询区域 &nbsp;&nbsp;<el-button
                      size="small"
                      type="text"
                      icon="el-icon-delete"
                      @click="reset(true)"
                      title="清空绘制区域"
                      >清空区域</el-button
                    >
                  </h2>
                  <template v-if="fenceList.length > 0">
                    <div>
                      <el-select
                        v-model="fenceSearchInput"
                        placeholder="选择企业围栏区域"
                        filterable
                        size="small"
                        no-data-text="无围栏数据"
                        no-match-text="无匹配数据，回车可查询定位"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in fenceList"
                          :key="index"
                          :label="item.entpNm + '-' + item.catNmCn"
                          :value="item.entpNm"
                          @click.native="drawFence(item.lnglat)"
                        >
                          <span v-if="item.catNmCn.includes('停')">【停】</span>
                          <span v-else-if="item.catNmCn.includes('装')"
                            >【装】</span
                          >
                          <span>{{ item.entpNm }}</span>
                        </el-option>
                      </el-select>
                    </div>
                    <div class="align-center" style="margin: 10px 0">或</div>
                  </template>
                  <div>
                    <el-button
                      size="small"
                      style="
                        background: #3ca19d;
                        border-color: #3ca19d;
                        width: 100%;
                      "
                      type="warning"
                      icon="el-icon-edit-outline"
                      @click="openDrawing"
                      >手动绘制区域</el-button
                    >
                    <!-- <el-button size="small" style="padding-left:10px;padding-right:10px;" type="warning" icon="el-icon-delete" @click="clearDrawing(true)" title="清空绘制区域">清空绘制区域</el-button> -->
                  </div>
                  <el-form-item
                    prop="fencePath"
                    :rules="{
                      required: true,
                      message: '请设置查询区域',
                      trigger: 'blur',
                    }"
                  >
                    <el-input
                      v-model="searchForm.fencePath"
                      class="hidden"
                    ></el-input>
                  </el-form-item>
                </div>
              </li>
              <li class="el-timeline-item">
                <div class="el-timeline-item-tail"></div>
                <div
                  class="el-timeline-item-head el-timeline-item-head-green"
                ></div>
                <div class="el-timeline-item-content">
                  <h2>S3：点击智能查询</h2>
                  <el-button
                    size="small"
                    style="
                      background: #3c42a0;
                      border-color: #3c42a0;
                      width: 100%;
                    "
                    type="primary"
                    icon="el-icon-search"
                    @click="search"
                    :loading="searchLoading"
                    >开始智能查询</el-button
                  >
                  <!-- <el-button style="padding-left:10px;padding-right:10px;" type="warning" icon="el-icon-download" title="导出车辆信息Excel" v-if="carList.length>0" @click="downloadExcel"></el-button> -->
                </div>
              </li>
            </ul>
          </el-form>
        </div>
      </div>
    </div>
    <div
      class="sidebar-collapse"
      @click="isCollapseOfLeftBar = !isCollapseOfLeftBar"
    >
      <i class="el-icon-d-arrow-left" v-show="!isCollapseOfLeftBar"></i>
      <i class="el-icon-d-arrow-right" v-show="isCollapseOfLeftBar"></i>
    </div>
    <div class="map-module" :loading="loading">
      <div id="map" style="height: 100%"></div>
      <div class="map-search-wape">
        <el-input
          v-model="locationSearchInput"
          placeholder="搜索地名、路名、位置"
          size="small"
          style="width: 230px"
          @keyup.enter.native="showLocation"
          clearable
        ></el-input>
        <!-- <el-select v-model="locationSearchInput" placeholder="搜索地名、路名、位置" filterable size="small" style="width:230px;" @keyup.enter.native="showLocation" no-data-text="无围栏数据" clearable>
          <el-option v-for="(item,index) in fenceList" :key="index" :label="item.entpNm+'-'+item.catNmCn" :value="item.entpNm" @click.native="drawFence(item.bdLnglat)">
            <span v-if="item.catNmCn.includes('停')">【停】</span>
            <span v-else-if="item.catNmCn.includes('装')">【装】</span>
            <span>{{ item.entpNm }}</span>
          </el-option>
        </el-select> -->
        <el-button
          type="primary"
          size="small"
          style="margin-left: 10px"
          @click="showLocation"
          >地点定位</el-button
        >
      </div>
      <rteplan-dialog
        ref="rteplanDialog"
        :data-source="rteplanList"
        :date-range="searchForm.searchRangeDate"
        :vec-no="selectedVec"
        @tabClick="getVehicleTraceData"
      ></rteplan-dialog>
      <timeline ref="timeline"></timeline>
    </div>
    <div class="right-module" v-show="carList && carList.length > 0">
      <div class="box-wrap" v-loading="carListLoading">
        <div class="box-wrap-header">
          <el-row>
            <el-col :span="24">车牌号{{ carList.length }}</el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-input
                type="text"
                placeholder="车牌号过滤"
                v-model="vecFilter"
                size="mini"
                class="custom-filter-input"
                clearable
              ></el-input>
            </el-col>
          </el-row>
        </div>
        <div class="box-wrap-body">
          <el-row
            v-for="(item, index) in carListFilter"
            :key="index"
            :class="[selectedVec === item ? 'active' : '']"
          >
            <el-col :span="24">
              <div class="ranking-vecno" @click="hanldeCarRowClick(item)">
                {{ item }}
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as Tool from "@/utils/tool";
import * as $http from "@/api/quyu/violationAlarm";
import { getRtePlanList } from "@/api/quyu/rtePlan";
import BMapLib from "BMapLib";
import rteplanDialog from "./component/rteplan-dialog";
import timeline from "./component/timeline";

export default {
  name: "violation",
  components: {
    rteplanDialog,
    timeline,
  },
  data() {
    return {
      loading: false,
      searchLoading: false,
      searchForm: {
        searchRangeDate: [
          Tool.formatDate(new Date(), "yyyy-MM-dd") + " 00:00:00",
          Tool.formatDate(new Date(), "yyyy-MM-dd") + " 23:59:59",
        ],
        fencePath: null,
      },
      datePickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          /*
           * 当点击第一个时间时，参数中只有minDate表示选中时间，maxDate为null
           * 当选中日期区间后，minDate 表示左区间的日期，maxDate 表示右区间的日期
           */
          this.min = minDate && minDate.getTime(); // 将第一个选中的日期赋值给 this.min
          // 如果选择了时间段，则清空 this.min
          if (maxDate) {
            this.min = "";
          }
        },
        disabledDate: (time) => {
          let today = Tool.formatDate(Date.now(), "yyyy-MM-dd") + " 23:59:59";

          let timeInterval = 3 * 24 * 60 * 60 * 1000; // 设定3天日期  3天 * 24小时 * 60分钟 * 60秒 * 1000 = 3天的时间戳
          // 如果开始日期已选中，则返回需求中需要禁用的时间
          if (this.min !== "") {
            // 大于选中时间后3天 || 小于选中时间后3天 || 大于今天 的所有日期都禁用
            return (
              time.getTime() > this.min + timeInterval ||
              time.getTime() < this.min - timeInterval ||
              time.getTime() > new Date(today).getTime()
            );
          } else {
            // 什么都没选，只禁用大于今天的所有日期
            return time.getTime() > new Date(today).getTime();
          }
        },
      },
      minutes: 3, // 停留时长

      map: null,
      drawingManager: null, // 绘制工具
      isCollapseOfLeftBar: false,

      LocalSearch: null, // 地图检索
      fenceSearchInput: "", // 搜索企业围栏
      locationSearchInput: "",
      fenceList: [],

      overlayOfFence: null, // 绘制围栏覆盖物
      fencePath: null, // 围栏的线路

      vecFilter: null, // 车辆列表过滤
      carListLoading: false,
      carList: [], // 车辆列表
      selectedVec: null, // 选中的车辆列表

      // overlayTemporaryArray: [],
      rteplanList: [],
      selectedVecRteplan: null,

      vecIcon: new BMap.Icon(
        "static/img/monitor-img/_icon.png",
        new BMap.Size(73, 73)
      ),
      stopIcon: new BMap.Icon(
        "static/img/monitor-img/stop.png",
        new BMap.Size(30, 70)
      ),
      startIcon: new BMap.Icon(
        "static/img/monitor-img/start.png",
        new BMap.Size(30, 60)
      ),
      endIcon: new BMap.Icon(
        "static/img/monitor-img/end.png",
        new BMap.Size(30, 60)
      ),
    };
  },
  provide() {
    return {
      getMap: this.getMap,
    };
  },
  watch: {
    vecFilter: {
      handler(newName, oldName) {
        if (this.carList.length > 0) {
        }
      },
      immediate: true,
    },
    searchForm: {
      handler(newName, oldName) {
        if (this.$refs.timeline) {
          this.$refs.timeline.hide();
        }
        if (this.$refs.rteplanDialog) {
          this.$refs.rteplanDialog.hide();
        }
        this.carList = [];
      },
      deep: true,
    },
  },
  computed: {
    carListFilter() {
      if (this.vecFilter) {
        let vecFilter = this.vecFilter;
        return this.carList.filter((it) => {
          return it.includes(vecFilter);
        });
      } else {
        return this.carList;
      }
    },
  },
  created() {
    this.getFenceList();
  },
  mounted() {
    let _this = this;

    this.$nextTick(function () {
      this._initBMap(function (map) {
        _this._initDrawing(map);
      });
    });
  },
  destroyed() {},
  methods: {
    getMap() {
      return this.map;
    },
    // 修改table tr行的背景色
    tableRowStyle({ row, rowIndex }) {
      if (row && row.hasQyz === 0) {
        // 同步失败
        return "background-color:rgb(251, 215, 215)";
      }
    },
    // 获取围栏列表数据
    getFenceList() {
      $http
        .getFenceList()
        .then((res) => {
          if (res.code == 0) {
            this.fenceList = res.list || [];
          } else {
            this.fenceList = [];
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //获取行政区域
    getBoundary(callback) {
      let bdary = new BMap.Boundary();
      let map = this.map;
      let _this = this;

      this.bdary = bdary;
      bdary.get("宁波市镇海区", function (rs) {
        //获取行政区域
        // map.clearOverlays();        //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        if (count === 0) {
          this.$message({
            type: "error",
            message: "未能获取当前输入行政区域",
          });
          return;
        }

        var pointArray = [];
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polyline(rs.boundaries[i], {
            strokeWeight: 3,
            strokeColor: "#FF6919",
            strokeOpacity: 0.8,
            strokeStyle: "dashed",
          }); //建立多边形覆盖物
          ply.name = "regionBoundary";
          map.addOverlay(ply); //添加覆盖物
          ply.disableMassClear();
          pointArray = pointArray.concat(ply.getPath());
        }
        if (callback) {
          callback.call();
        } else {
          map.setViewport(pointArray);
        }
      });
    },
    // 初始化地图
    _initBMap(callback) {
      let map = new BMap.Map("map"); // 创建Map实例
      var mapType1 = new BMap.MapTypeControl({
        mapTypes: [BMAP_NORMAL_MAP, BMAP_SATELLITE_MAP],
        anchor: BMAP_ANCHOR_TOP_LEFT,
      });
      map.enableAutoResize(); //地图自适应容器宽高变化，手动启用
      map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
      map.addControl(mapType1); //添加地图类型控件

      this.map = map;
      let point = new BMap.Point(121.66386, 30.001186);
      map.centerAndZoom(point, 13);
      this.getBoundary();
      callback && callback(map);
    },
    // 清除车辆轨迹
    clearOverlayOfVecTrace() {
      var allOverlay = this.map.getOverlays();
      allOverlay.map((item) => {
        if (item.name === "vecTrace") {
          item.enableMassClear();
          this.map.removeOverlay(item);
        }
      });
    },
    // 清除绘制的围栏
    clearOverlayOfFence() {
      var allOverlay = this.map.getOverlays();
      allOverlay.map((item) => {
        if (item.name === "fence") {
          item.enableMassClear();
          this.map.removeOverlay(item);
        }
      });
    },
    // 初始化绘制模式
    _initDrawing(map) {
      var _this = this;
      var styleOptions = {
        strokeColor: "#d00", //边线颜色。
        fillColor: "#d00", //填充颜色。当参数为空时，圆形将没有填充效果。
        strokeWeight: 2, //边线的宽度，以像素为单位。
        strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
        strokeStyle: "solid", //边线的样式，solid或dashed。
      };
      //实例化鼠标绘制工具
      this.drawingManager = new BMapLib.DrawingManager(map, {
        isOpen: false, //是否开启绘制模式
        enableDrawingTool: true, //是否显示工具栏
        drawingToolOptions: {
          anchor: BMAP_ANCHOR_TOP_RIGHT, //位置
          offset: new BMap.Size(10, 25), //偏离值
          drawingModes: [
            BMAP_DRAWING_POLYGON, // 多边形
          ],
        },
        polygonOptions: styleOptions,
      });
      this.drawingManager.addEventListener("overlaycomplete", function (e) {
        let overlay = e.overlay;
        overlay.name = "fence";
        overlay.disableMassClear();
        let path = overlay.getPath();
        _this.overlayOfFence = overlay;
        _this.fencePath = JSON.stringify(path);
        _this.searchForm.fencePath = JSON.stringify(path);
        _this.closeDrawing();
        _this.$message({
          message: "区域绘制完成请开始查询",
          type: "success",
        });
      });
    },
    // 打开绘制功能
    openDrawing() {
      if (this.overlayOfFence) {
        this.clearDrawing(false);
      }
      if (this.drawingManager) {
        this.drawingManager.open();
        this.drawingManager.setDrawingMode(BMAP_DRAWING_POLYGON);
      } else {
        this._initDrawing(this.map);
      }
    },
    // 恢复初始情况：清空围栏，清空轨迹，清空所有覆盖物，所有数据恢复初始值
    reset(hasCheck, callback) {
      let fun = function (callback) {
        this.clearOverlayOfVecTrace(); // 清除车辆轨迹
        this.clearOverlayOfFence(); // 清除绘制的围栏
        this.map.clearOverlays();

        this.fenceSearchInput = "";
        this.locationSearchInput = "";
        this.overlayOfFence = null; // 绘制的覆盖物
        this.fencePath = null; // 绘制的线路
        this.searchForm.fencePath = null; // 绘制的线路
        this.vecFilter = "";
        this.carListLoading = false;
        this.carList = [];
        this.selectedVec = null;
        this.rteplanList = [];
        this.selectedVecRteplan = null;

        this.$refs.timeline.hide();

        if (callback) {
          callback.call(this);
        }
      };

      if (hasCheck) {
        if (this.overlayOfFence) {
          this.$confirm("是否删除当前的查询区域?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            fun.call(this, callback);
          });
        }
      } else {
        fun.call(this, callback);
      }
    },
    /**
     * 清空围栏绘制数据
     * callback:清空函数的回调函数
     * hasCheck：是否需要确认删除提示
     */
    clearDrawing(hasCheck, callback) {
      let _self = this;
      let fun = function (callback) {
        this.clearOverlayOfVecTrace(); // 清除车辆轨迹
        this.clearOverlayOfFence(); // 清除绘制的围栏

        // this.overlay.enableMassClear();
        // this.map.removeOverlay(this.overlay);
        // this.clearOverlayOfVecTrace();

        this.overlayOfFence = null;
        this.fencePath = null;
        this.searchForm.fencePath = null;

        this.selectedVecRteplan = null;
        this.rteplanList = [];
        if (callback) {
          callback.call(this);
        }
      };
      if (hasCheck) {
        if (this.overlayOfFence) {
          this.$confirm("是否删除当前的查询区域?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            fun.call(this, callback);
          });
          // .catch((err) => {console.log(err)});
        }
      } else {
        // fun.bind(_self);
        fun.call(this, callback);
      }
    },
    // 关闭绘制功能
    closeDrawing() {
      if (this.drawingManager) {
        this.drawingManager.close();
      }
    },
    // 查询进出围栏车辆数据
    search() {
      let _this = this;
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          let params = {
            // minutes: this.minutes,
            polygon: this.searchForm.fencePath,
            beginTime: this.searchForm.searchRangeDate[0],
            endTime: this.searchForm.searchRangeDate[1],
          };
          this.searchLoading = true;
          this.selectedVecRteplan = null;
          this.rteplanList = [];
          this.carListLoading = true;
          // 获取经过区域内的车辆信息
          $http
            .getCarListOfPassingArea(params)
            .then((res) => {
              _this.searchLoading = false;
              _this.carListLoading = false;
              if (res.code == 0) {
                _this.carList = res.data;
                if (res.data.length == 0) {
                  _this.$message({
                    message: "当前无车辆经过该区域",
                    type: "error",
                  });
                }
              } else {
                _this.$message.error(res.msg);
              }
            })
            .catch((error) => {
              console.log(error);
              _this.searchLoading = false;
              _this.carListLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    // 绘制围栏
    drawFence(lnglat) {
      console.log(lnglat);
      if (lnglat) {
        var styleOptions = {
          strokeColor: "#d00", //边线颜色。
          fillColor: "#d00", //填充颜色。当参数为空时，圆形将没有填充效果。
          strokeWeight: 2, //边线的宽度，以像素为单位。
          strokeOpacity: 0.8, //边线透明度，取值范围0 - 1。
          fillOpacity: 0.2, //填充的透明度，取值范围0 - 1。
          strokeStyle: "solid", //边线的样式，solid或dashed。
        };
        if (this.overlayOfFence) {
          this.clearDrawing();
          this.fencePath = null;
          this.searchForm.fencePath = null;
        }
        let polygon = new BMap.Polygon(lnglat, styleOptions); //建立多边形覆盖物
        polygon.name = "fence";
        polygon.disableMassClear();
        this.overlayOfFence = polygon;
        let path = this.overlayOfFence.getPath();
        this.fencePath = JSON.stringify(path);
        this.searchForm.fencePath = JSON.stringify(path);
        this.map.addOverlay(polygon); //添加覆盖物
        this.map.setViewport(path); //调整地图视口
        this.closeDrawing();
      } else {
        this.$message({
          message: "无围栏坐标数据",
          type: "error",
        });
      }
    },
    // 定位
    showLocation() {
      if (!this.LocalSearch) {
        this.LocalSearch = new BMap.LocalSearch(this.map, {
          renderOptions: { map: this.map },
        });
      }
      this.LocalSearch.search(this.locationSearchInput);
    },
    /**
     * 判断点是否在区域内
     * lng：经度
     * lat：纬度
     * polygon:围栏
     */
    checkPointIsInTheArea(lng, lat, polygon) {
      var pt = new BMap.Point(lng, lat);
      if (BMapLib.GeoUtils.isPointInPolygon(pt, polygon)) {
        //如果点在区域内，返回true
        return true;
      } else {
        return false;
      }
    },
    // 点击车辆列表，获取车辆电子运单及车辆轨迹信息
    hanldeCarRowClick(vehicleNo) {
      this.selectedVec = vehicleNo;
      this.clearOverlayOfVecTrace(); // 清除车辆轨迹
      this.map.clearOverlays();
      // this.clearOverlayOfFence(); // 清除绘制的围栏
      this.getRteplan(vehicleNo); // 获取车辆电子运单信息
      this.getVehicleTraceData(
        // 获取第一个日期的轨迹
        vehicleNo,
        this.searchForm.searchRangeDate[0].slice(0, 10)
      );
      // this.createVecGpsTrace(vehicleNo);
    },
    // 获取电子运单
    getRteplan(vehicleNo) {
      let _this = this;
      let param = {
        filters: {
          groupOp: "AND",
          rules: [
            { field: "trac_cd", op: "cn", data: vehicleNo },
            {
              field: "vec_desp_tm",
              op: "bt",
              data: [
                this.searchForm.searchRangeDate[0],
                this.searchForm.searchRangeDate[1],
              ],
            },
          ],
        },
        page: 1,
        limit: 999,
      };
      getRtePlanList(param)
        .then((response) => {
          if (response.code == 0) {
            _this.rteplanList = response.list || [];
          } else {
            _this.rteplanList = [];
          }
          this.$refs.rteplanDialog.show();
        })
        .catch((error) => {
          _this.rteplanList = [];
          console.log(error);
        });
    },

    // 获取车辆轨迹数据，并绘制车辆轨迹
    getVehicleTraceData(vehicleNo, date) {
      let _this = this;
      let param = {
        startTime: date + " 00:00:00",
        endTime: date + " 23:59:59",
        vehicleNo: vehicleNo,
        // minutes: this.minutes
      };
      this.loading = true;
      // 获取轨迹
      $http
        .getTrack(param)
        .then((response) => {
          this.loading = false;
          if (response.code == 0) {
            _this.createVecGpsTrace(vehicleNo, date);
            // _this.drawDrivingTrace(response.data);
            // if (response.data.runPoints.length > 0) {
            //   _this.drawDrivingTrace(response.data.runPoints);
            // }
            // if (response.data.stayPoints.length > 0) {
            //   _this.drawStayPoint(response.data.stayPoints);
            // }
          } else {
            _this.$message.error(response.msg);
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log(error);
        });
    },

    // 绘制车辆在地图上的行驶路线
    drawDrivingTrace(data) {
      let points = [];
      data.forEach((item) => {
        points.push(item.lonBd + "," + item.latBd);
      });

      let track = points.join(";");
      let pointArray = [];
      let boundaries = [track];
      // let symbol = new BMap.Symbol(
      //   BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW, //百度预定义的 箭头方向向下的非闭合箭头
      //   {
      //     fillColor: "#0000FF", //设置矢量图标的填充颜色。支持颜色常量字符串、十六进制、RGB、RGBA等格式
      //     fillOpacity: 1, //设置矢量图标填充透明度,范围0~1
      //     scale: 0.5, //设置矢量图标的缩放比例
      //     rotation: 90, //设置矢量图标的旋转角度,参数为角度
      //     strokeColor: "#FFF", //设置矢量图标的线填充颜色,支持颜色常量字符串、十六进制、RGB、RGBA等格式
      //     strokeOpacity: 1, //设置矢量图标线的透明度,opacity范围0~1
      //     strokeWeight: 2 //旋设置线宽。如果此属性没有指定，则线宽跟scale数值相
      //   }
      // );
      // let iconSequence = new BMap.IconSequence(
      //   symbol, //symbol为符号样式
      //   "5%", //offset为符号相对于线起点的位置，取值可以是百分比也可以是像素位置，默认为"100%"
      //   "5%", //repeat为符号在线上重复显示的距离，可以是百分比也可以是距离值，同时设置repeat与offset时，以repeat为准
      //   false //fixedRotation设置图标的旋转角度是否与线走向一致，默认为true
      // );
      // var sy = new BMap.Symbol(BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW, {
      //   scale: 0.6, //图标缩放大小
      //   strokeColor: "#fff", //设置矢量图标的线填充颜色
      //   strokeWeight: "2" //设置线宽
      // });
      // var icons = new BMap.IconSequence(sy, "10", "30");

      for (let i = 0; i < boundaries.length; i++) {
        let ply = new BMap.Polyline(boundaries[i], {
          // icons: [iconSequence], //创建折线
          strokeColor: "#0000FF",
          strokeOpacity: 0.7,
          strokeWeight: 8,
        }); //建立多边形覆盖物
        // let ply = new BMap.Polyline(boundaries[i], {
        //   enableEditing: false, //是否启用线编辑，默认为false
        //   enableClicking: true, //是否响应点击事件，默认为true
        //   icons: [icons],
        //   strokeWeight: "8", //折线的宽度，以像素为单位
        //   strokeOpacity: 0.8, //折线的透明度，取值范围0 - 1
        //   strokeColor: "#18a45b" //折线颜色
        // });
        ply.name = "vecTrace";
        // ply.enableMassClear();
        this.map.addOverlay(ply); //添加覆盖物
        // this.overlayTemporaryArray.push(ply);
        pointArray = pointArray.concat(ply.getPath());
      }
      this.map.setViewport(pointArray);
      pointArray = [];
      //添加起始点
      if (data.length > 0) {
        var vecMarkerStart = new BMap.Marker(
          new BMap.Point(data[0].lonBd, data[0].latBd),
          { icon: this.startIcon }
        );
        vecMarkerStart.name = "vecTrace";
        this.map.addOverlay(vecMarkerStart);
        // this.overlayTemporaryArray.push(vecMarkerStart);
        // this.map.setCenter(new BMap.Point(data[0].lonBd, data[0].latBd));     // 设置起始点为中心
      }
      if (data.length >= 2) {
        var vecMarkerEnd = new BMap.Marker(
          new BMap.Point(
            data[data.length - 1].lonBd,
            data[data.length - 1].latBd
          ),
          { icon: this.endIcon }
        );
        vecMarkerEnd.name = "vecTrace";
        this.map.addOverlay(vecMarkerEnd);
        // this.overlayTemporaryArray.push(vecMarkerEnd);
      }
    },

    // 绘制停靠点，画出区域内的停靠点
    drawStayPoint(stayPoints) {
      // let _this = this;
      // // 绘制停靠点
      // stayPoints.forEach(item => {
      //   // if(_this.checkPointIsInTheArea(item.lon,item.lat,_this.overlayOfFence.getBounds())){
      //   if (
      //     _this.checkPointIsInTheArea(item.lon, item.lat, _this.overlayOfFence)
      //   ) {
      //     var point = new BMap.Point(item.lon, item.lat);
      //     var marker = new BMap.Marker(point, { icon: _this.stopIcon });
      //     _this.map.addOverlay(marker);
      //     // _this.overlayTemporaryArray.push(marker);
      //     // 判断点在区域内
      //     var opts = {
      //       position: point, // 指定文本标注所在的地理位置
      //       offset: new BMap.Size(5, -5) //设置文本偏移量
      //     };
      //     var label = new BMap.Label(
      //       Tool.formatDate(item.gpsTime, "MM-dd HH:mm:ss"),
      //       opts
      //     ); // 创建文本标注对象
      //     label.setStyle({
      //       color: "red",
      //       fontSize: "12px",
      //       height: "20px",
      //       lineHeight: "20px",
      //       fontFamily: "微软雅黑"
      //     });
      //     _this.map.addOverlay(label);
      //     // _this.overlayTemporaryArray.push(label);
      //   }
      // });
    },
    // 下载
    downloadExcel() {
      let _this = this;
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          let params = {
            minutes: _this.minutes,
            polygon: _this.searchForm.fencePath,
            beginTime: _this.searchForm.searchRangeDate[0],
            endTime: _this.searchForm.searchRangeDate[1],
          };
          // $http
          //   .downloadPolygonExcel(params)
          //   .then(res => {
          //     let a = document.createElement("a");
          //     let blob = new Blob([res]);
          //     let url = window.URL.createObjectURL(blob);
          //     let D = new Date();
          //     let _date =
          //       D.getFullYear() + "-" + (D.getMonth() + 1) + "-" + D.getDate();

          //     a.href = url;
          //     a.download = "区域查询结果_" + _date + ".xls";
          //     a.click();
          //     window.URL.revokeObjectURL(url);
          //   })
          //   .catch(err => {
          //     console.log(err);
          //   });
        } else {
          return false;
        }
      });
    },

    createVecGpsTrace(vehicleNo, traceDate) {
      let _this = this;
      let param = {
        startTime: traceDate + " 00:00:00",
        endTime: traceDate + " 23:59:59",
        vehicleNo: vehicleNo,
        // minutes: this.minutes
      };
      this.loading = true;
      // 获取轨迹
      $http
        .getTrack(param)
        .then((response) => {
          this.loading = false;
          if (response.code == 0) {
            _this.$refs.timeline.showTrace(vehicleNo, traceDate, response.data);
          } else {
            _this.$message.error(response.msg);
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.violation-container {
  display: flex;
  // width: 100%;
  width: 100vw;
  height: 100%;
  height: calc(100vh - 50px);
  /* background-color: #999; */
  /* padding: 10px; */
  background: rgba(12, 21, 48, 0.5);
  -webkit-box-shadow: 0 0 40px 0 rgba(12, 21, 48, 0.3);
  box-shadow: 0 0 40px 0 rgba(12, 21, 48, 0.3);
  /* border-radius: 8px; */
  border: 1px solid #062147;
  overflow: hidden;
  position: relative;
  /* background-image: url(http://www.njzhgd.cn/images/divbg.png);
  background-image: url("~@/assets/login-imgs/repeat.png"); */
  background-size: 100% 100%;

  /deep/ {
    .el-input__inner,
    .el-date-editor .el-range-input {
      background-color: #fff;
      color: #606266;
      border-color: #dcdfe6;
    }
    .el-date-editor .el-range-separator {
      color: #606266;
      width: 20px;
    }
  }

  .custom-filter-input /deep/ .el-input__inner {
    padding: 0 5px;
    border-radius: 0;
  }

  .left-module {
    flex: 0 0 400px;
    position: relative;
    overflow-y: auto;
    /* margin-right: 10px; */
    /* margin-left: 10px; */
    background-color: #fff;
    transition: all 1s ease;

    &.collapse {
      margin-left: -335px;
    }

    .sidebar-wape {
      background: #fff;

      .sidebar-head {
        background: #3c42a0;
        text-align: center;
        color: #fff;
        line-height: 38px;
        cursor: pointer;
      }

      .sidebar-content {
        margin-top: 10px;
        // padding: 10px;

        .demonstration {
          /* margin-bottom:10px; */
          line-height: 30px;
        }

        .el-timeline {
          list-style: none;
          margin: 0;
          padding: 10px;
        }

        .el-timeline-item {
          margin: 0 !important;
          padding: 0 0 12px;
          list-style: none;
          position: relative;
        }

        .el-timeline-item-tail {
          height: 100%;
          border-left: 1px solid #e8eaec;
          position: absolute;
          left: 6px;
          top: 0;
        }

        .el-timeline-item-head {
          width: 13px;
          height: 13px;
          background-color: #fff;
          border-radius: 50%;
          border: 1px solid transparent;
          position: absolute;
        }

        .el-timeline-item-head-green {
          border-color: #19be6b;
          color: #19be6b;
        }

        .el-timeline-item-head-red {
          border-color: #ed4014;
          color: #ed4014;
        }

        .el-timeline-item-head-gray {
          border-color: #888;
          color: #ed4014;
        }

        .el-timeline-item-content {
          padding: 1px 1px 10px 24px;
          font-size: 12px;
          position: relative;
          top: -3px;

          > h2 {
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: bold;
            color: #555;
          }
        }
      }
    }
  }

  .sidebar-collapse {
    flex: 0 0 16px;
    z-index: 10;
    cursor: pointer;

    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
    }
  }

  .map-module {
    flex: 1 1 auto;
    overflow: hidden;
    position: relative;
    transition: all 1s ease;

    .map-search-wape {
      position: absolute;
      z-index: 10;
      left: 10px;
      top: 40px;
      font: bold 12px/1.3em arial, sans-serif;
      color: #fff;
    }
  }

  .right-module {
    flex: 0 0 100px;
    // position: absolute;
    // width: 100px;
    // z-index: 10;
    // right: 0;
    // top: 0;
    // bottom: 0;
    overflow-y: auto;
    font: bold 12px/1.3em arial, sans-serif;
    background-color: #0a284a;
    transition: all 1s ease;
  }

  .box-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%;
    .box-wrap-header {
      color: #fff;
      flex: 0 0 28px;
      // color: rgba(255, 255, 255, 0.6);
      font-size: 13px;
      line-height: 28px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
      // background: rgba(179, 131, 131, 0.2);
      background: rgba(0, 0, 0, 0.5);
    }
    .box-wrap-body {
      flex: 1;
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      font-size: 13px;
      line-height: 28px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 0px;
      }
      /* 滚动条的滑轨背景颜色 */
      &::-webkit-scrollbar-track {
        background-color: transparent;
        border-radius: 0px;
      }

      /* 滑块颜色 */
      &::-webkit-scrollbar-thumb {
        background-color: #003c7c;
        border-radius: 0px;
      }

      /*内层轨道的颜色*/
      &::-webkit-scrollbar-track-piece {
        background-color: transparent;
        border-radius: 5px;
      }
      .active {
        background-color: #1764bb;
      }
      .ranking-vecno {
        margin: 5px 0;
        cursor: pointer;
        &:first-letter {
          background: #f78c44;
          // width: 20px;
          // height: 20px;
          border-radius: 53px;
          display: inline-block;
          // background: #878787;
          color: #fff;
          line-height: 20px;
          text-align: center;
          margin-right: 5px;
          padding: 5px;
        }
      }
    }
  }
}
</style>