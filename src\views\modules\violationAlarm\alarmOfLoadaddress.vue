<template>
  <!--
    @desc:异常装卸地功能
    @jira:http://jira.dacyun.com/browse/ZHYS-2202
  -->
  <div class="app-main-content">
    <searchbar ref="searchbar" :searchItems="searchItems" :pagination="pagination" @resizeSearchbar="setTableHeight"
      @search="getList">
      <span class="el-form-item">
        <el-button size="mini" type="primary" @click="goBack" icon="el-icon-back">返回</el-button>
        <div class="el-form-item__label" style="padding: 0px;font-size: 16px;font-weight: bold;color: #2563ff;">
          异常装卸地维护列表》</div>
      </span>
      <el-button slot="button" icon="el-icon-plus" type="primary" size="small" @click="addHandle">新增</el-button>
    </searchbar>
    <!--列表-->
    <el-table class="el-table" :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;"
      :max-height="tableHeight" :row-class-name="tableRowClassName">
      <el-table-column prop="entpName" label="企业名称"></el-table-column>
      <el-table-column prop="entpAddr" label="企业地址"></el-table-column>
      <el-table-column prop="entpKey" label="企业关键词"></el-table-column>
      <el-table-column prop="whiteGoods" label="白名单货物" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <formTag size="mini" v-model="scope.row.whiteGoods" separator="----" :edit="false">
          </formTag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="upd(scope.row)">编辑</el-button>
          <el-button type="text" @click="delet(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--工具条-->
    <div class="toolbar clearfix">
      <el-pagination background layout="sizes, prev, pager, next, total" :page-sizes="[20, 30, 50, 100, 200]"
        style="float:right;" :page-size="pagination.limit" :current-page.sync="pagination.page"
        :total="pagination.total" @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>

    <el-dialog :visible.sync="visible">
      <el-form ref="form" label-width="120px" :model="info" size="small">
        <el-form-item label="企业名称:" prop="entpName" :rules="$rulesFilter({ required: true })">
          <el-input v-model.trim="info.entpName" type="text"></el-input>
        </el-form-item>
        <el-form-item label="企业地址:" prop="entpAddr" :rules="$rulesFilter({ required: true })">
          <el-input v-model.trim="info.entpAddr" type="text"></el-input>
        </el-form-item>
        <el-form-item label="企业关键词:" prop="entpKey" :rules="$rulesFilter({ required: true })">
          <el-input v-model.trim="info.entpKey" type="text"></el-input>
        </el-form-item>
        <el-form-item label="白名单货物:" prop="whiteGoods">
          <formTag v-model="info.whiteGoods" placeholder="白名单货物并回车" separator="----"></formTag>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <el-button type="primary" size="small" @click="saveHandle">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>
import * as $http from '@/api/violationAlarm.js';
import * as Tool from '@/utils/tool';
import Searchbar from '@/components/Searchbar';
import formTag from "@/components/formTag";
const INFO = {
  // private Long id;   //主键
  // private String entpName;   // 企业名称
  // private Integer entpAddr;   // 企业地址
  // private String entpKey;   // 企业关键词
  // private String entpKey;   // 白名单货物,多个货物之间用 ---- 连接
  id: '',
  entpName: '',
  entpAddr: '',
  entpKey: '',
  whiteGoods: '',
}
export default {
  name: 'AlarmOfLoadaddress',
  data() {
    return {
      listLoading: false,
      list: [],
      tableHeight: 520,
      searchItems: {
        normal: [
          { name: '企业名称', field: 'entpName', type: 'text', dbfield: 'entp_name', dboper: 'cn' },
          { name: '企业关键词', field: 'entpKey', type: 'text', dbfield: 'entp_key', dboper: 'cn' },
        ],
        more: []
      },
      pagination: {
        total: 0,
        limit: 15,
        page: 1
      },

      visible: false,
      info: { ...INFO },

    };
  },
  components: {
    Searchbar,
    formTag
  },
  mounted() {
    const _this = this;
    window.addEventListener('resize', this.setTableHeight);
    let query = this.$route.query;
    this.pagination.page = query.currPage ? parseInt(query.currPage) : 1;
    this.pagination.limit = query.pageSize ? parseInt(query.pageSize) : 20;
    this.pagination.total = this.pagination.page * this.pagination.limit;
    this.$refs.searchbar.init(query);
    this.setTableHeight();
    this.getList();
  },
  destroyed() {
    window.removeEventListener('resize', this.setTableHeight);
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = Tool.getClientHeight() - 160 - this.$refs.searchbar.$el.offsetHeight;
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.status === 1) {
        return 'warning-row';
      } else if (rowIndex === 0) {
        return 'success-row';
      }
      return '';
    },
    getList(data) {
      this.listLoading = true;
      let _this = this;
      let filters;
      if (data) {
        if (data.resetCurrentPage) {
          this.pagination.page = 1;
        }
        if (data.searchData) {
          filters = data.searchData;
        }
      } else {
        filters = this.$refs.searchbar.get();
      }
      let params = Object.assign({}, { filters: filters }, this.pagination);
      delete params.total;
      $http.getEntpAlarmAddrPage(params)
        .then(response => {
          if (response.code == 0 && response.page) {
            _this.list = response.page.list || [];
            _this.pagination.total = response.page.totalCount || 0;
            _this.pagination.page = response.page.currPage || 1;
          } else {
            _this.list = [];
            _this.pagination.total = 0;
            _this.pagination.page = 1;
          }
          this.listLoading = false;
        })
        .catch(error => {
          this.listLoading = false;
        });
    },
    addHandle() {
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
      this.$set(this, 'info', { ...INFO })
      this.visible = true;
    },
    saveHandle() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let API = this.info.id ? $http.updEntpAlarmAddr : $http.addEntpAlarmAddr;
          let params = Object.assign({}, this.info, { id: this.info.id })
          this.$confirm('确认提交吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            API(params).then(res => {
              if (res.code == 0) {
                this.$message.success("提交成功!")
                this.visible = false;
                this.getList();
              } else {
                this.$message.error(res.msg || "提交失败！")
              }
            })
          }).catch(err => {
            console.log(err)
          })
        } else {
          this.$message.error("请填写完整信息！")
        }
      })
    },
    delet(row) {
      let id = row.id;
      this.$confirm('确认删除该条数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        $http.deleteEntpAlarmAddr(id).then(res => {
          if (res.code == 0) {
            this.$message.success("删除成功!");
            this.getList();
          }
        })
      }).catch(err => {

      })
    },
    upd(row) {
      if (row) {
        this.$set(this, "info", { ...row });
        this.visible = true;
      }
      // if (row.id) {
      //   $http.getPersrmksInfo(row.id).then(res => {
      //     if (res.code === 0) {
      //       this.info = res.data;
      //       this.visible = true;
      //     } else {
      //       this.$message.error(res.msg || "获取数据失败，无法编辑，请查看网络是否正常！")
      //     }
      //   })
      // }
    },
    // 分页跳转
    handleCurrentChange: function (val) {
      this.pagination.page = val;
      this.$refs.searchbar.searchHandle(true);
    },

    // 分页条数修改
    handleSizeChange: function (val) {
      this.pagination.limit = val;
      this.$refs.searchbar.searchHandle(true);
    },
  }
};
</script>
<style lang="scss" scoped></style>