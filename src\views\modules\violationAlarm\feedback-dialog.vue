<template>
  <el-dialog :loading="loading" append-to-body :visible.sync="visible" title="车辆报警信息违章处置" :fullscreen="true">
    <div class="show-box">
      <div class="show-box-header">
        <span class="show-box-heading-inner">报警信息</span>
      </div>
      <div class="show-box-body" v-loading="showBoxLoading" v-if="alarmInfo">
        <div class="flex" v-if="alarmInfo.catCd === '1060.8020.110'">
          <div style="flex:0 0 400px;">
            <el-row class="detail-container">
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">报警类型：</div>
                <div class="detail-area">
                  {{ handleResult(alarmInfo, "catNmCn") }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">报警时间：</div>
                <div class="detail-area">
                  {{ handleResult(alarmInfo, "alarmTime") }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">违规记录：</div>
                <div class="detail-area">
                  <el-button type="primary" size="mini" @click="showAlarmDialog(alarmInfo.tractorNo)">查看</el-button>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="flex:1 auto;">
            <el-row class="detail-container">
              <el-col :xs="24" v-if="alarmInfo.perCatCd !== '2100.205.190'" :sm="8" :md="8" :lg="8">
                <el-row class="detail-container">
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">驾驶人员：</div>
                    <div class="detail-area">
                      <a v-if="alarmInfo.driverPk" href="javascript:void(0)"
                        @click="openPage('pers', alarmInfo.driverPk)">{{ handleResult(alarmInfo, "driverNm") }}</a>
                      <span v-else>{{ handleResult(alarmInfo, "driverNm") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">驾驶员手机号：</div>
                    <div class="detail-area">
                      <span>{{ handleResult(alarmInfo, "dvMob") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">驾驶员身份证号：</div>
                    <div class="detail-area">
                      <span>{{ handleResult(alarmInfo, "dvCd") }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :xs="24" v-if="alarmInfo.perCatCd !== '2100.205.150'" :sm="8" :md="8" :lg="8">
                <el-row class="detail-container">
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">押运人员：</div>
                    <div class="detail-area">
                      <a v-if="alarmInfo.guardsPk" href="javascript:void(0)"
                        @click="openPage('pers', alarmInfo.guardsPk)">{{ handleResult(alarmInfo, "guardsNm") }}</a>
                      <span v-else>{{ handleResult(alarmInfo, "guardsNm") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">押运员手机号：</div>
                    <div class="detail-area">
                      <span>{{ handleResult(alarmInfo, "scMob") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">押运员身份证号：</div>
                    <div class="detail-area">
                      <span>{{ handleResult(alarmInfo, "scCd") }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :lg="8">
                <el-row class="detail-container">
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">牵引车号：</div>
                    <div class="detail-area">
                      <a v-if="alarmInfo.tractorPk" href="javascript:void(0)"
                        @click="openPage('vec', alarmInfo.tractorPk)">{{ handleResult(alarmInfo, "tractorNo") }}</a>
                      <span v-else>{{ handleResult(alarmInfo, "tractorNo") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">企业联系：</div>
                    <div class="detail-area">
                      <span>{{ handleResult(alarmInfo, "erNm") }}</span>
                      <span v-show="alarmInfo.erMob">_{{ handleResult(alarmInfo, "erMob") }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="24" :md="24" :lg="24">
                    <div class="detail-desc">运输企业：</div>
                    <div class="detail-area">
                      <a v-if="alarmInfo.entpPk" href="javascript:void(0)"
                        @click="openPage('entp', alarmInfo.entpPk)">{{ handleResult(alarmInfo, "entpNm") }}</a>
                      <span v-else>{{ handleResult(alarmInfo, "entpNm") }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="flex" v-else>
          <div style="flex:0 0 400px;">
            <el-row class="detail-container">
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">报警类型：</div>
                <div class="detail-area">
                  {{ handleResult(alarmInfo, "catNmCn") }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">报警时间：</div>
                <div class="detail-area">
                  {{ handleResult(alarmInfo, "alarmTime") }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">报警位置：</div>
                <div class="detail-area">
                  {{ handleResult(alarmInfo, "alarmLocation") }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <div class="detail-desc">运单信息：</div>
                <div class="detail-area">
                  <template v-if="alarmInfo && alarmInfo.argmtCd">
                    <a v-if="alarmInfo.argmtPk" href="javascript:void(0)" @click="showBills(alarmInfo.argmtPk)">{{
                      alarmInfo.argmtCd }}</a>
                    <template v-else>
                      {{ alarmInfo.argmtCd }}
                    </template>
                  </template>
                  <template v-else>
                    无
                  </template>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="flex:1 auto;">
            <el-row class="detail-container">
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">牵引车号：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.tractorPk" href="javascript:void(0)"
                    @click="openPage('vec', alarmInfo.tractorPk)">{{
                      handleResult(alarmInfo, "tractorNo") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "tractorNo") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">企业联系：</div>
                <div class="detail-area">
                  <span>{{ handleResult(alarmInfo, "erNm") }}</span>
                  <span v-show="alarmInfo.erMob">_{{ handleResult(alarmInfo, "erMob") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">挂车牌号：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.trailerPk" href="javascript:void(0)"
                    @click="openPage('vec', alarmInfo.trailerPk)">{{
                      handleResult(alarmInfo, "trailerNo") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "trailerNo") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">驾驶人员：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.driverPk" href="javascript:void(0)"
                    @click="openPage('pers', alarmInfo.driverPk)">{{
                      handleResult(alarmInfo, "driverNm") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "driverNm") }}</span>
                  <span v-show="alarmInfo.dvMob">_{{ handleResult(alarmInfo, "dvMob") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">运输企业：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.entpPk" href="javascript:void(0)" @click="openPage('entp', alarmInfo.entpPk)">{{
                    handleResult(alarmInfo, "entpNm") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "entpNm") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">押运人员：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.guardsPk" href="javascript:void(0)"
                    @click="openPage('pers', alarmInfo.guardsPk)">{{
                      handleResult(alarmInfo, "guardsNm") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "guardsNm") }}</span>
                  <span v-show="alarmInfo.scMob">_{{ handleResult(alarmInfo, "scMob") }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">货物重量：</div>
                <div class="detail-area">
                  <a v-if="alarmInfo.chemica" href="javascript:void(0)"
                    @click="openPage('chemica', alarmInfo.prodPk)">{{
                      handleResult(alarmInfo, "chemica") }}</a>
                  <span v-else>{{ handleResult(alarmInfo, "chemica") }}</span>
                  <span v-show="alarmInfo.loadQty != undefined &&
                    alarmInfo.loadQty != null
                    ">&nbsp;&nbsp;{{
                      handleResult(alarmInfo, "loadQty")
                    }}吨</span>
                </div>
              </el-col>
              <!-- <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">发货地址：</div>
                <div class="detail-area">{{handleResult(alarmInfo,'csnorWhseDist')}}</div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">卸货地址：</div>
                <div class="detail-area">{{handleResult(alarmInfo, 'csneeWhseDist')}}</div>
              </el-col> -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12">
                <div class="detail-desc">违规记录：</div>
                <div class="detail-area">
                  <el-button type="primary" size="mini" @click="showAlarmDialog(alarmInfo.tractorNo)">查看</el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- show-box-body <<<<<<<<<<<<<<<<<<<<<<<<<< -->
      <div class="show-box-footer">
        <el-row class="detail-container">
          <el-col :xs="24" :sm="24" :md="24" :lg="24">
            <div class="detail-desc">情况描述：</div>
            <div class="detail-area" style="white-space: normal;">
              {{ handleResult(alarmInfo, "descr") }}
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- show-box <<<<<<<<<<<<<<<<<<<<<<<<<< -->
    <div class="flex">
      <div style="flex:1 auto;">
        <div class="show-box" style="height:375px;background:#fff;">
          <div class="show-box-body" v-loading="showBoxLoading">
            <el-row :gutter="10">
              <el-col :xs="12" :sm="12" :md="12" :lg="12">
                <div style="width:100%;height:calc(380px - 20px);background:#ccc;position:relative;">
                  <el-button type="primary" size="mini" class="estimate-btn" @click="showLabel">距离测算</el-button>
                  <div ref="mapNode" style="width:100%;height:100%;" />
                  <div class="tooltip">
                    <div>
                      <svg-icon icon-class="alarmLocal"></svg-icon>
                      报警位置
                    </div>
                    <!-- <div><svg-icon icon-class="alarmLocal-1"></svg-icon>前后5分钟其他报警</div> -->
                    <div>
                      <svg-icon icon-class="guiji"></svg-icon>
                      前后半小时轨迹
                    </div>
                    <div style="text-align:center;">
                      <a href="javascript:void(0)" @click="showHistry()">查看全部轨迹</a>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="12" :sm="12" :md="12" :lg="12">
                <div style="background:#ccc;">
                  <!--列表-->
                  <el-table :data="tableData" height="360px" border style="width: 100%" @row-click="rowClickHandle">
                    <el-table-column type="index"> </el-table-column>
                    <el-table-column prop="updateTime" label="当前时间" width="140">
                    </el-table-column>
                    <el-table-column prop="speed" label="速度">
                    </el-table-column>
                    <el-table-column prop="latBd" label="经纬度">
                      <template slot-scope="scope">
                        {{ scope.row.lonBd }},{{ scope.row.latBd }}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div style="width:500px;padding-left:15px;" v-if="alarmInfo">
        <div v-if="!noticeShow" class="title">处置信息</div>
        <div v-if="noticeShow && showReplyLetter" style="display:flex;align-items:center">
          <div class="title">处置信息</div>
          <div style="margin:0 20px;font-weight: bold" :style="{ color: alarmInfo.isReply ? '#67c23a' : '#e6a23c' }">
            当前状态：{{
              alarmInfo.isReply === 2
                ? "已回函"
                : alarmInfo.isReply === 1
                  ? "已下发举证"
                  : "未下发"
            }}
          </div>
          <el-button type="primary" size="mini" v-if="!alarmInfo.isReply" @click="sentMsg">下发举证</el-button>
          <el-button type="success" size="mini" v-if="alarmInfo.isReply === 2" @click="replyShow = !replyShow">
            {{ replyShow ? "隐藏举证" : "查看举证" }}
          </el-button>
        </div>
        <div v-if="alarmInfo.isReply === 2 && replyShow && showReplyLetter" style="margin: 8px 0 8px 8px">
          <el-card class="reply-letter-box-card">
            <div v-if="alarmInfo.replyTime">时间：{{ alarmInfo.replyTime }}</div>
            <div style="margin-bottom:8px">内容：{{ alarmInfo.replyRemark }}</div>
            <div style="overflow: hidden;">
              <span class="ft-lf">附件:</span>
              <filePreview class="ft-lf" fileWidth="40px" inline="true" :files="alarmInfo.replyUrl"
                @clickHandle="clickHandle" :imageShow="true">
              </filePreview>
            </div>

            <div style="float: right; margin-bottom:15px;" v-if="!alarmInfo.replyStatus">
              <el-button type="success" size="mini" @click="giveEvidence(1)">回函通过</el-button>
              <el-button type="danger" size="mini" @click="giveEvidence(2)">回函驳回</el-button>
            </div>
          </el-card>
        </div>
        <el-form size="small" label-width="95px" :model="dealForm" ref="dealForm" @submit.native.prevent>
          <el-row class="detail-container">
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="处置时间：" style="margin-bottom:0;">
                {{ dealForm.foundTm }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <!-- <el-form-item
                label="操作人员："
                :rules="$rulesFilter({ required: true })"
                prop="oprNm"
              >
                <el-autocomplete
                  class="inline-input"
                  v-model="dealForm.oprNm"
                  :fetch-suggestions="querySearch"
                  placeholder="请输入内容"
                  clearable
                  @select="handleSelect"
                ></el-autocomplete>
              </el-form-item> -->
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="处置操作：" prop="isHandle" :rules="$rulesFilter({ required: true })">
                <!-- <el-select v-model="dealForm.isHandle" placeholder="请选择">
                  <el-option v-for="item in dealTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-radio-group v-model="dealForm.isHandle" class="custom-radio-group">
                  <el-radio v-for="(item, index) in alarmDealOptions[alarmInfo.catCd] ||
                    alarmDealOptions['default']" :key="index" :label="alarmDealActions[item].value">
                    {{ alarmDealActions[item].label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="车辆荷载：" prop="grade" :rules="$rulesFilter({ required: true })">
                <el-radio-group v-model="dealForm.grade" class="custom-radio-group">
                  <el-radio v-for="(item, index) in gradeOptions" :key="index" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <el-form-item label="加入关注：">
                <el-button type="primary" size="small" @click="addFollow">加入关注</el-button>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24">
              <multiple-editor v-model="dealForm.oprContent" />
              <!-- <el-form-item label="详情描述：" prop="oprContent">
                <wangeditor
                  ref="wangeditor"
                  v-model="dealForm.oprContent"
                  placeholder="请输入详情描述"
                  class="wangeditor"
                ></wangeditor>
              </el-form-item> -->
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="align-right">
              <el-button type="default" size="small" @click="visible = false">取消</el-button>
              <el-button type="primary" size="small" @click="save">提交</el-button>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="notice" v-if="noticeShow">
              <file-upload v-model="ccLetter.sendUrl" ref="ccLetterRef"></file-upload>
              <el-button type="warning" size="small" @click="submit">抄告函提交</el-button>
            </el-col>
          </el-row>
          <!-- <div class="notice" v-if="noticeShow">
            <file-upload
              v-model="ccLetter.sendUrl"
              ref="ccLetterRef"
            ></file-upload>
            <el-button type="warning" size="small" @click="submit"
              >抄告函提交</el-button
            >
          </div> -->
        </el-form>
      </div>
    </div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" append-to-body width="80%" top="5vh">
      <simple-table :tableTitle="tableTitle" :tableHeader="tableHeader" :tablePage="tablePage"
        @tableRefreshByPagination="tableRefreshByPaginationHandle"></simple-table>
    </el-dialog>
    <el-dialog title="举证视频" :visible.sync="videoDialogVisible" width="500px" append-to-body @close="closeCbkHandle">
      <video v-if="videoUrl" width="100%" height="300px" controls autoplay>
        <source :src="videoUrl" type="video/mp4">
        您的浏览器不支持 HTML5 video 标签。
      </video>
    </el-dialog>
  </el-dialog>
</template>
<script>
import SimpleTable from "@/components/SimpleTable";
import fileUpload from "@/components/FileUpload";
import { extendObj, formatDate } from "@/utils/tool";
import { oprlist } from "@/api/mapMonit";
import { latestTime } from "@/api/common";
import * as $httpAlarm from "@/api/violationAlarm";
import { getLastRtePlanByTracCd } from "@/api/rtePlan";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
import Cookies from "js-cookie";
import { mapGetters } from "vuex";
// import wangeditor from "@/components/editor/wangeditor";
import filePreview from "@/components/FilesPreview";
import MultipleEditor from "@/components/MultipleEditor";
import * as Tool from "@/utils/tool"
import * as $http from "@/api/violationAlarm"

export default {
  name: "enforceOnLineDialog",
  props: {
    dataSource: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: {
    SimpleTable,
    fileUpload,
    // wangeditor,
    filePreview,
    MultipleEditor
  },
  data() {
    return {
      noticeShow: false, //显示提交抄告函的条件
      loading: false,
      visible: false,
      opratorList: [], // 操作员列表

      dialogTableVisible: false, // 弹窗是否可见
      dialogTitle: "", // 弹窗标题
      tableComponent: "simple-table", // table组件
      tableTitle: "", // table的标题
      tableHeader: null, // table的表头信息
      tablePage: null, // table的数据信息，包括list数据以及分页数据

      showBoxLoading: false,
      alarmInfo: {
        alarmPk: null,
        alarmLongitude: null,
        alarmLatitude: null
      },
      dealForm: {
        foundTm: "", //发现时间
        oprNm: "", //操作员

        isHandle: "",
        grade: null,// 车辆荷载
        oprContent: "" //处理结果
      },
      gradeOptions: [
        {
          label: "空载",
          value: 10,
        },
        {
          label: "重载",
          value: 30,
        },
      ],
      ccLetter: {
        sendUrl: ""
      },
      map: null,
      alarmIcon: new BMap.Icon(
        "static/img/monitor-img/map_alarm.png",
        new BMap.Size(48, 48)
      ),
      tableData: [],
      replyShow: false,
      labelList: [],
      videoUrl: "",
      videoDialogVisible: false
    };
  },
  computed: {
    ...mapGetters([
      "alarmDealActions",
      "allAlarmDealOptions",
      "alarmDealOptions"
    ]),
    showReplyLetter() {
      let initValue = true;
      let state = this.alarmInfo.govHandlerType;
      let replyLetterAudit = this.alarmInfo.replyStatus;
      if (state == "14" || replyLetterAudit === 2) {
        initValue = false;
      }
      return initValue;
    },
  },
  created() {
  },
  mounted() {
    oprlist()
      .then(res => {
        if (res.code == 0) {
          this.opratorList = res.list.map(item => {
            return { value: item, label: item };
          });
        } else {
          this.opratorList = [];
        }
      })
      .catch(err => {
        this.opratorList = [];
      });
  },
  methods: {
    addFollow() {
      const alarmPk = this.alarmInfo.alarmPk;

      $http.addToFollowList(alarmPk).then(res => {
        if (res && res.code == 0) {
          this.$message.success("加入关注列表成功")
        }
      })
    },
    showLabel() {
      this.labelList.forEach(label => {
        let isVisible = label.isVisible()
        if (isVisible) {
          label.hide();
        } else {
          label.show();
        }
      })
    },
    getCenterPoint(path) {
      var x = 0.0;
      var y = 0.0;
      for (var i = 0; i < path.length; i++) {
        x = x + parseFloat(path[i].lng);
        y = y + parseFloat(path[i].lat);
      }
      x = x / path.length;
      y = y / path.length;

      return new BMap.Point(x, y);
    },
    // 获取偏离路线
    getAlarmRoute(alarmPk) {
      $httpAlarm.alarmRoute(alarmPk).then(res => {

        if (res && res.code == 0 && res.rteLine && res.rteLine.length) {
          res.rteLine.forEach(item => {
            const getDistance = this.map.getDistance;
            const line = JSON.parse(item.line);
            const len = line[0].length - 1;
            let routeDistance = 0;

            let points = line[0].map((it, i, arr) => {
              let currPoint = new BMap.Point(it.lng, it.lat);
              let nextPoint = null;

              if (i < len) {
                nextPoint = new BMap.Point(arr[i + 1].lng, arr[i + 1].lat);
                routeDistance += getDistance(currPoint, nextPoint)
              }

              return currPoint;
            });

            routeDistance = Tool.numDiv(routeDistance, 1000)
            const labelPosition = this.getCenterPoint(line[0]);
            const label = new BMap.Label(item.label + ',测算距离:' + routeDistance.toFixed(3) + " km", { position: labelPosition });

            this.map.addOverlay(label);
            label.hide();
            this.labelList.push(label);

            let polyline = new BMap.Polyline(points, {
              strokeColor: "#ff0000",
              strokeWeight: 6,
              strokeOpacity: 0.5
            });

            this.map.addOverlay(polyline); //增加折线
          })

        }
      })
    },
    // 抄告函提交
    submit() {
      let data = {
        alarmPks: this.alarmInfo.alarmPk, //报警主键
        sendUrl: this.ccLetter.sendUrl, //抄告函url
        entpPk: this.alarmInfo.entpPk, //企业主键
        entpName: this.alarmInfo.entpNm, //企业名称
        sendTm: this.dealForm.foundTm,
        status: 0 //发函状态0代表发函
      };
      // this.$refs.ccLetterRef.$refs.upload.clearFiles()
      $httpAlarm.noticeSave(data).then(res => {
        if (res.code === 0) {
          this.$message({
            type: "success",
            message: "提交成功!"
          });
          this.$set(this.ccLetter, "sendUrl", "");
        }
      });
    },
    init(alarmInfo) {
      //审核账号账号且类型为路线偏移显示按钮
      let rolesName =
        localStorage.getItem("roleName") || Cookies.get("WHJK-ROLENAME");
      if (rolesName.indexOf("gov_dljc") >= 0) {
        this.noticeShow = true;
      }
      let _this = this;
      this.visible = true;
      this.$set(this, "alarmInfo", alarmInfo);
      this.getAlarmInfoPyPk(alarmInfo.alarmPk);
      if (alarmInfo.tractorNo) {
        getLastRtePlanByTracCd(alarmInfo.tractorNo)
          .then(res => {
            if (res.code == 0) {
              let lastRtePlan = res.data;
              if (lastRtePlan) {
                this.alarmInfo.csnorWhseDist = lastRtePlan.csnorWhseDist || ""; //发货地
                this.alarmInfo.csnorWhseDistCd =
                  lastRtePlan.csnorWhseDistCd || ""; //发货区域编码
                this.alarmInfo.csneeWhseDist = lastRtePlan.csneeWhseDist || ""; //卸货地
                this.alarmInfo.csneeWhseDistCd =
                  lastRtePlan.csneeWhseDistCd || ""; //卸货区域
                this.alarmInfo.enchPk = lastRtePlan.enchPk || ""; //货物主键
                this.alarmInfo.goodsNm = lastRtePlan.goodsNm || ""; //货物名称
                this.alarmInfo.loadQty = lastRtePlan.loadQty || ""; //计划装运量
                this.alarmInfo.prodPk = lastRtePlan.prodPk || ""; //
                this.alarmInfo.dangGoodsNm = lastRtePlan.dangGoodsNm || ""; //
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
      this.$nextTick(() => {
        this.$refs.dealForm && this.$refs.dealForm.resetFields();
        this.gradeStatus(alarmInfo.grade)
        this.dealForm.oprContent = "";
        this.initMap();
        latestTime()
          .then(res => {
            if (res.code == 0) this.dealForm.foundTm = res.nowStr;
            else
              this.dealForm.foundTm = formatDate(
                new Date(),
                "yyyy-MM-dd HH:mm:ss"
              );
          })
          .catch(err => {
            this.dealForm.foundTm = formatDate(
              new Date(),
              "yyyy-MM-dd HH:mm:ss"
            );
          });
      });
    },
    initMap() {
      if (!this.map) {
        let map = (this.map = new BMap.Map(this.$refs.mapNode, {
          enableMapClick: false
        }));
        map.centerAndZoom(new BMap.Point(121.613391, 29.966183), 11); // 初始化地图,设置中心点坐标和地图级别
        map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
          })
        );
        map.setCurrentCity("宁波市"); // 设置地图显示的城市 此项是必须设置的
        map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
      }
      this.clearMap();

      if (this.alarmInfo.alarmLongitude && this.alarmInfo.alarmLatitude) {
        var pt = new BMap.Point(
          this.alarmInfo.alarmLongitude,
          this.alarmInfo.alarmLatitude
        );
        var myIcon = new BMap.Icon(
          imgsConfig.vec.alarmLocal,
          new BMap.Size(30, 30)
        );
        var marker = new BMap.Marker(pt, { icon: myIcon }); // 创建标注
        let label = new BMap.Label("报警时间：" + this.alarmInfo.alarmTime, {
          offset: new BMap.Size(20, -10)
        });
        marker.setLabel(label);
        this.map.addOverlay(marker);

      }
      this.getRouteByHour();
    },
    handleResult(rqs, key) {
      if (!rqs) return "";
      if (rqs[key] === undefined || rqs[key] == null) {
        return "无";
      } else if (key === "catNmCn") {
        var str = rqs[key];
        str = str.replace(/GPS/g, "卫星定位");
        return str;
      } else if (key === "descr") {
        var str = rqs[key];
        str = str.replace(/GPS/g, "卫星定位");
        return str;
      } else {
        return rqs[key];
      }
    },
    /**
     * @Date: 2023-07-19 10:08:25
     * @Author: SangShuaiKang
     * @description: 将报警初始荷载赋值给荷载选项
     * @param {*} grade
     * @return {*}
     */
    gradeStatus(grade) {
      if (!grade || grade < 10) return;
      if (grade === 10 || grade === 30) {
        this.dealForm.grade = grade;
      } else if (grade > 1000) {
        this.dealForm.grade = Number(grade.toString().slice(0, 2));
      }
    },
    // 根据报警pk，获取报警信息
    getAlarmInfoPyPk(pk) {
      if (pk == null || pk === undefined) {
        this.alarmInfo = null;
        return;
      }
      $httpAlarm.getAlarmByPk(pk).then(res => {
        if (res.code == 0) {
          this.alarmInfo = res.data;
        } else {
          this.alarmInfo = null;
        }
      });
    },
    // 清空地图上的所有内容
    clearMap() {
      this.map && this.map.clearOverlays();
    },
    getRouteByHour() {
      let _this = this;
      const alarmPk = this.alarmInfo.alarmPk;
      //前后半小时轨迹
      if (alarmPk) {
        $httpAlarm
          .getRouteByHour(alarmPk)
          .then(response => {
            if (response && response.code === 0) {
              _this.tableData = response.data;
              let pointArr = [];
              response.data.forEach(item => {
                pointArr.push(new BMap.Point(item.lonBd, item.latBd));
              });

              let polyline = new BMap.Polyline(pointArr, {
                strokeColor: "blue",
                strokeWeight: 6,
                strokeOpacity: 0.5
              });

              _this.map.addOverlay(polyline); //增加折线
              _this.map.setViewport(pointArr);

            }
          })
          .catch(error => {
            console.log(error);
          });

        _this.getAlarmRoute(alarmPk)
      }
    },
    removeOverlayByName(name) {
      var allOverlay = this.map.getOverlays();
      allOverlay.map(item => {
        if (item.name === name) {
          this.map.removeOverlay(item);
        }
      });
    },
    rowClickHandle(alarmInfo, column, event) {
      this.removeOverlayByName("alarmPoint");
      // 在地图上显示经纬度
      if (alarmInfo.lonBd && alarmInfo.latBd) {
        var pt = new BMap.Point(alarmInfo.lonBd, alarmInfo.latBd);
        var myIcon = new BMap.Icon(
          imgsConfig.vec.alarmLocal,
          new BMap.Size(30, 30)
        );
        var marker = new BMap.Marker(pt, { icon: myIcon }); // 创建标注
        marker.name = "alarmPoint";
        let label = new BMap.Label(alarmInfo.updateTime, {
          offset: new BMap.Size(20, -10)
        });
        marker.setLabel(label);
        this.map.addOverlay(marker);
        this.map.panTo(pt);
      }
    },
    querySearch(queryString, cb) {
      var opratorList = this.opratorList;
      var results = queryString
        ? opratorList.filter(this.createFilter(queryString))
        : opratorList;

      cb(results);
    },
    createFilter(queryString) {
      return opratorList => {
        return (
          opratorList.value.toLowerCase().indexOf(queryString.toLowerCase()) !=
          -1
        );
      };
    },
    handleSelect(item) { },
    geocoder(point, cb) {
      if (!this.geoc) {
        this.geoc = new BMap.Geocoder();
      }

      this.geoc.getLocation(point, function (rs) {
        let addComp = rs.addressComponents;
        let str = "";

        /* if(addComp.province){
                      str += addComp.province
                  }

                  if(addComp.city){
                      str += addComp.city
                  } */

        if (addComp.district) {
          str += addComp.district;
        }

        if (addComp.street) {
          str += addComp.street;
        }

        if (addComp.streetNumber) {
          str += addComp.streetNumber;
        }

        cb(str);
      });
    },
    openPage(type, pk) {
      window.open(
        location.origin + location.pathname + "#/base/" + type + "/info/" + pk,
        "_blank"
      );
    },
    // 违章记录弹窗
    showAlarmDialog(vehicleNo) {
      let _this = this;
      this.dialogTitle = `车辆违章报警和预警详情`;
      this.tableTitle = `<h5>${vehicleNo}，30天内违章报警和预警数据</h5>`;
      this.tableHeader = [
        { name: "违章时间", field: "alarmTime", width: 200 },
        { name: "违章地点", field: "alarmLocation" },
        { name: "违章类型", field: "catNmCn" },
        { name: "违章细节", field: "descr" }
      ];
      let params = {
        page: 1,
        limit: 20,
        days: 30,
        tracNo: vehicleNo,
        notCatCd: "2550.160.180.150"
      };
      this.getAlarmList(params);
    },
    // 查看违章页面
    getAlarmList(params) {
      let _this = this;
      this.loading = true;
      $httpAlarm
        .getAlarmListBySimpleQuery(params)
        .then(res => {
          _this.loading = false;
          if (res.code == 0) {
            _this.dialogTableVisible = true;
            _this.tablePage = res.data;
          } else {
            _this.tablePage = {
              list: [],
              pageNo: 0,
              pageSize: 10,
              totalPage: 0
            };
            _this.$message.error(res.msg);
          }
        })
        .catch(() => {
          _this.loading = false;
        });
    },
    // table翻页事件
    tableRefreshByPaginationHandle(paginationData) {
      let params = {
        page: 1,
        limit: 20,
        days: 30,
        tracNo: this.alarmInfo.tractorNo
      };
      this.getVecEventList(Object.assign({}, params, paginationData));
    },
    getVecEventList(params) {
      let _this = this;
      $httpAlarm.getAlarmListBySimpleQuery(params).then(res => {
        if (res.code == 0) {
          _this.dialogTableVisible = true;
          _this.tablePage = res.data;
        } else {
          _this.tablePage = {
            list: [],
            pageNo: 0,
            pageSize: 10,
            totalPage: 0
          };
          _this.$message.error(res.msg);
        }
      });
    },
    // 历史轨迹
    showHistry() {
      if (!this.alarmInfo.tractorNo) {
        this.$message({
          message: "对不起，该车辆历史轨迹无法查看",
          type: "error"
        });
        return;
      }
      let location = window.location;
      window.open(
        location.origin +
        location.pathname +
        "#/monit/hisTrack??v=" +
        encodeURIComponent(this.alarmInfo.tractorNo) +
        "&t=" +
        formatDate(this.alarmInfo.alarmTime, "yyyy-MM-dd"),
        "_blank"
      );
    },
    save() {
      this.$refs.dealForm.validate(valid => {
        if (valid) {
          this.$confirm("确定提交吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              this.loading = true;
              let postData = Object.assign({}, this.dealForm, {
                alarmPk: this.alarmInfo.alarmPk,
                catCd: this.alarmInfo.catCd,
                entpPk: this.alarmInfo.entpPk
              });
              if (this.dealForm.isHandle === 18 && !this.alarmInfo.entpPk) {
                this.$message.error("企业未备案，无法发送通告函！");
                console.log("企业entpPk不存在，无法发送通告函！");
                return;
              }
              $httpAlarm
                .feedbackSave(postData)
                .then(res => {
                  this.loading = false;
                  if (res.code == 0) {
                    this.$message({
                      type: "success",
                      message: "提交成功!"
                    });
                    this.$emit("refresh");
                    this.visible = false;
                  } else {
                    this.$message.info(res.msg || "服务器错误，请联系管理员");
                  }
                })
                .catch(err => {
                  this.loading = false;
                });
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.$message.error(
            "填写信息不全，请填写完所有打*号的必填项再提交！"
          );
        }
      });
    },
    //下发举证
    sentMsg() {
      let dvNm = this.alarmInfo.driverNm;
      let dvMob = this.alarmInfo.dvMob;
      let alarmPk = this.alarmInfo.alarmPk;
      this.$confirm(`确认下发给驾驶员【${dvNm}】:${dvMob}`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          $httpAlarm.downAlarmInfo(alarmPk).then(res => {
            if (res && res.code === 0) {
              this.alarmInfo.isReply = 1;
              this.$message({
                message: "下发举证成功",
                type: "success"
              });
            }
          });
        })
        .catch(() => { });
    },
    /**
     * @Date: 2023-06-07 10:57:35
     * @Author: SangShuaiKang
     * @description: 举证回函审核
     * @param {*} type 1 审核通过 2 审核不通过
     * @return {*}
     */
    giveEvidence(type) {
      let params = {
        pk: this.alarmInfo.alarmPk,
        type: type,
      };
      $httpAlarm.apprDowningAlarmInfo(params).then(res => {
        if (res.code == 0) {
          this.$set(this.alarmInfo, "replyStatus", type);
        }
      });
    },
    clickHandle(res) {
      if (res.type == 'video' && res.data) {
        this.videoDialogVisible = true;
        this.videoUrl = res.data;
      }
    },
    closeCbkHandle() {
      this.videoUrl = "";
    },
    // 电子运单
    showBills: function (argmtPk) {
      if (!argmtPk) {
        this.$message.error("对不起，该信息不能打开");
        return;
      }
      window.open(
        location.origin + location.pathname + "#/base/rteplan/bills/" + argmtPk
      );
    },
  }
};
</script>
<style lang="scss" scoped>
.notice {
  position: relative;
  top: -35px;
  margin-left: 96px;
  width: 98px;
}

.flex {
  display: flex;

  .reply-letter-box-card {
    & /deep/ .el-card__body {
      overflow: hidden;
    }

  }
}

.title {
  color: #091733;
  font-size: 16px;
  /*margin-bottom: 10px;*/
  font-weight: bolder;
  padding-left: 10px;
}

.show-box {
  box-sizing: border-box;
  background-color: #f7f9fe;
  // box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  border: 1px solid #c6cee0;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 14px;
  line-height: 28px;

  .show-box-header {
    color: #091733;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: bolder;
  }

  .show-box-body {}

  .show-box-footer {
    padding: 15px 0;
    border-top: 1px solid #c6cee0;
    margin-top: 20px;
  }
}

.detail-container {
  position: relative;
  padding: 0;
  margin: 0 auto;
  font-size: 14px;

  a {
    color: #296ae3;
  }

  .detail-desc {
    box-sizing: content-box;
    min-width: 70px;
    width: auto;
    padding: 0 5px;
    color: #7d8187;
    float: left;
    text-align: left;
  }

  .detail-area {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #091733;
  }
}

.tooltip {
  position: absolute;
  right: 5px;
  bottom: 5px;
  padding: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  line-height: 20px;
}

.wangeditor {
  border: 1px solid #ccc;
}

/deep/ {
  .el-form-item {
    .el-form-item__label {
      color: #7d8187;
    }

    .el-form-item__content {
      color: #091733;
    }
  }

  .custom-radio-group.el-radio-group {
    line-height: 28px;

    .el-radio {
      margin-right: 15px;
      min-width: 108px;

      .el-radio__label {
        font-size: 14px;
      }
    }

    .el-radio+.el-radio {
      margin-left: 0;
    }
  }

  .estimate-btn {
    position: absolute;
    right: 10px;
    top: 40px;
    z-index: 999;
  }
}
</style>
