<template>
    <div class="follow-list" :class="[collapse && 'collapse']">
      <div class="follow-container">
         <div class="follow-list-name">
           <span class="el-icon-refresh fresh-btn" title="刷新" @click="rereshHandle"></span>
           关注车辆列表
           <span class="el-icon-date history-btn" @click="showHistoryList" title="历史关注列表"></span>
        </div>
         <div class="follow-list-content" v-loading="listLoading1">
            <div class="follow-list-vec" v-for="(item,index) in dataList" :key="index">
              <div class="clearfix">
                <div class="lic-plate">
                  <div class="lic-plate-border">
                    {{ item.vecNo }}
                  </div>
                </div>
                <div class="collect-btn" title="移除关注" @click="removeFollow(item.alarmPk)">
                  <svg-icon icon-class="del" class-name="top-menu-svg-icon"></svg-icon>
                </div>
              </div>
              <div class="time-line" :class="[item.collapse && 'collapse']">
                <div class="time-line-box">
                  <div class="time-line-item">
                    <span>关注时长</span>
                    <span style="font-weight: bold;" :style="{'color':overAnHour(item.focusTm) ? 'red' : ''}">{{ calcRetentionTime(item.focusTm) }}</span>
                    <el-button type="primary" @click="trackEvent(item)" size="mini" style="padding:2px;margin-left: 4px;">事件跟踪</el-button>
                  </div>
                  <div class="time-line-item" v-for=" (subitem,subindex) in item.oprRecord" :key="subindex">
                    <span>{{ subitem.time }}</span>
                    <span>{{ subitem.content }}</span>
                  </div>
                </div>
                <div class="expand-btn" @click="collapseHandle(item)" >
                  <span :class="[item.collapse ? 'el-icon-caret-bottom' : 'el-icon-caret-top']" ></span>
                  <span class="expand-text">{{item.collapse ? '显示全部' : '折叠'}}</span>
                </div>
              </div>
            </div>
         </div>
         <div class="history-follow">
          <el-pagination
            align="right"
            :current-page="pageNumber"
            background
            :page-size="pageSize"
            layout="total, prev, next"
            @prev-click="prevClickHandle"
            @next-click="nextClickHandle"
            :total="pageTotal">
          </el-pagination>
          <!-- 查看历史关注 -->
         </div>
      </div>
      <div class="slide-btn" @click="collapseFollowList" title="关注车辆列表">
        <span class="wait-handle-task" v-if="waitHandleTaskCount && collapse"></span>
      </div>

      <el-dialog :visible.sync="visible" title="关注历史列表" append-to-body>
        <div>
          <el-form inline size="small">
            <el-form-item label="车牌号">
               <el-input v-model="sVecNo" clearable placeholder="请输入车牌号"></el-input>
            </el-form-item>
            <el-form-item label="关注时间">
              <el-date-picker
                v-model="sFocusTm"
                type="date"
                value-format="yyyy-MM-dd"
                clearable
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button @click="searchHanle" type="primary">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="historyList" max-height="500px" v-loading="listLoading2">
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in props.row.oprRecord"
                  :key="index"
                  :timestamp="activity.time">
                  {{activity.content}}
                </el-timeline-item>
              </el-timeline>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="车牌号" prop="vecNo"></el-table-column>
          <el-table-column label="关注时间" prop="focusTm"></el-table-column>
          <el-table-column label="状态" prop="vecState">
            <template slot-scope="scope">
              <el-tag type="warning" v-if="scope.row.vecState == 1">已关注</el-tag>
              <el-tag type="success" v-else-if="scope.row.vecState == 2">关注后撤离</el-tag>
              <el-tag type="info" v-else>未关注</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
             <template slot-scope="scope">
                <el-button type="text" @click="revertCollect(scope.row)">恢复关注</el-button>
             </template>
          </el-table-column>
        </el-table>
        <div slot="footer">
          <el-pagination
            align="right"
            :current-page="historyPageNumber"
            background
            :page-size="historyPageSize"
            layout="total, prev, pager, next"
            @prev-click="prevHisClickHandle"
            @next-click="nextHisClickHandle"
            @current-change="currentChangeHandle"
            :total="historyPageTotal">
          </el-pagination>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="visible2" append-to-body title="事件跟踪" height="420px" :close-on-click-modal="false">
         <el-form size="small" :model="updForm" ref="updForm">
            <el-form-item label="车牌号:">
              {{ currFollow.vecNo }}
            </el-form-item>
            <el-form-item :rules="$rulesFilter({required:true})" prop="content">
              <el-input placeholder="请输入反馈内容" v-model="updForm.content" type="textarea" rows="5" length="4389"></el-input>
            </el-form-item>
            <el-form-item align="right">
              <el-button :loading="submitLoading" type="success" icon="el-icon-check" @click="subUpdFollow">提交</el-button>
            </el-form-item>
         </el-form>
      </el-dialog>
    </div>
</template>

<script>
import * as $http from "@/api/violationAlarm"

export default {
  name: "",
  data() {
    return {
      collapse:true,
      visible:false,
      visible2:false,
      pageNumber:1,
      sVecNo:"",
      sFocusTm:"",
      pageTotal:0,
      pageSize: 10,
      waitHandleTaskCount: false,
      historyList:[],
      dataList:[],
      historyPageTotal:0,
      historyPageNumber:1,
      historyPageSize: 15,
      listLoading1:false,
      listLoading2:false,
      content:"",
      updForm:{
        content:""
      },
      currFollow:{},
      submitLoading:false
    };
  },
  created(){
    let collapseSession = sessionStorage.getItem("collapse");
    if(collapseSession == 1){
      this.collapse = true;
    }else if(collapseSession == -1){
      this.collapse = false;
    }
    this.getList();
    this.interValTask();
  },
  methods: {
    revertCollect(row){
      const alarmPk = row.alarmPk;
    
      $http.addToFollowList(alarmPk).then( res => {
        if(res && res.code == 0){
          this.$message.success("加入关注列表成功");
          this.getList();
          this.showHistoryList();
        }
      })
    },
    searchHanle(){
      this.historyPageNumber = 1;
      this.showHistoryList()
    },
    prevClickHandle(page){
      this.pageNumber = page;
      this.getList();
    },
    nextClickHandle(page){
      this.pageNumber = page;
      this.getList();
    },
    getList(loading=true){
      let param = {
        filters: {
          "groupOp":"AND","rules":[{"field":"vec_state","op":"eq","data":"1"}]
        },
        page:this.pageNumber,
        limit:this.pageSize
      };

      if(loading){
        this.listLoading1 = true;
      }

      $http.vecFocusPage(param).then( res => {
        this.listLoading1 = false;
        this.waitHandleTaskCount = false;
        if(res && res.code == 0){
          res.page.list.forEach( item => {
            item.oprRecord = JSON.parse(item.oprRecord);
            if(item.oprRecord.length > 1){
              item.collapse = true;
            }
          })
          this.dataList = JSON.parse(JSON.stringify(res.page.list));
          this.pageTotal = res.page.totalCount;
        }
      })
      .catch( err => {
        this.waitHandleTaskCount = false;
        this.listLoading1 = false;
        this.dataList = [];
      })
    },
    interValTask(){
       let timer = null;
       const time = 5 * 60 *1000;
       timer = setTimeout(() => {
          this.getList();
          this.interValTask();
          clearTimeout(timer);
       }, time);
    },
    overAnHour(time){
      const milliseconds = new Date().getTime() - new Date(time).getTime();
      if(milliseconds >= 60 * 60 * 1000){
        return true
      }
      return false;
    },
    calcRetentionTime(time){
      if(!time) return '';
      const milliseconds = new Date().getTime() - new Date(time).getTime();
      const result = this.formatTime(milliseconds);
      return result;
    },
    formatTime(milliseconds) {
      const msPerDay = 24 * 60 * 60 * 1000;
      const msPerHour = 60 * 60 * 1000;
      const msPerMinute = 60 * 1000;
      let result = "";

      const days = Math.floor(milliseconds / msPerDay);
      const hours = Math.floor((milliseconds % msPerDay) / msPerHour);
      const minutes = Math.floor((milliseconds % msPerHour) / msPerMinute);
      if(days){
        result += `${days}天 `;
      }

      if(hours){
        result += `${hours}小时 `;
      }

      if(minutes){
        result += `${minutes}分钟`;
      }

      // 超过十分钟未处理则提醒
      if(milliseconds >= 600000 && !this.waitHandleTaskCount){
        this.waitHandleTaskCount = true;
      }

      return result || "1分钟";
    },

    collapseFollowList(){
      this.collapse = !this.collapse;
      sessionStorage.setItem("collapse", this.collapse ? 1 : -1);
      if(!this.collapse){
        this.getList(false);
      }
    },
    collapseHandle(item){
      this.$set(item, 'collapse', !item.collapse)
    },
    removeFollow(alarmPk){
      this.$confirm('此操作将该车辆从关注列表移除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        
        $http.removeFocus(alarmPk).then( res => {
           this.getList()
        })
      }).catch(() => {
                
      });
    },
    prevHisClickHandle(page){
      this.historyPageNumber = page;
      this.showHistoryList();
    },
    nextHisClickHandle(page){
      this.historyPageNumber = page;
      this.showHistoryList();
    },
    currentChangeHandle(page){
      this.historyPageNumber = page;
      this.showHistoryList();
    },
    showHistoryList(){
      this.visible = true;
      this.listLoading2 = true;
      let param = {
        filters: {
          "groupOp":"AND","rules":[{"field":"vec_state","op":"gt","data":"0"}]
        },
        page:this.historyPageNumber,
        limit:this.historyPageSize
      };

      if(this.sVecNo){
        param.filters.rules.push({"field":"vec_no","op":"cn","data":this.sVecNo})
      }

      if(this.sFocusTm){
        param.filters.rules.push({"field":"focus_tm","op":"bt","data":[this.sFocusTm+" 00:00:00", this.sFocusTm+" 23:59:59"]})
      }

      $http.vecFocusPage(param).then( res => {
         this.listLoading2 = false;
         if(res && res.code == 0){
          res.page.list.forEach( item => {
            item.oprRecord = JSON.parse(item.oprRecord);
          })
          this.historyList = res.page.list;
          this.historyPageTotal = res.page.totalCount;
         }
      })
      .catch( err => {
        this.listLoading2 = false;
      })
    },
    rereshHandle(){
       this.getList()
    },
    trackEvent(data){
      this.visible2 = true;
      this.currFollow = data;
    },
    subUpdFollow(){

      this.$refs.updForm.validate(valid => {
        if(valid){
          const currFollow = this.currFollow;
          this.submitLoading = true;
          $http.updFocus(currFollow.alarmPk, this.updForm.content).then( res => {
            this.submitLoading = false;
            if(res && res.code == 0){
              this.visible2 = false;
              this.currFollow = {};
              this.updForm.content = null;
              this.getList();
            }
          })
          .catch( err => {
            this.submitLoading = false;
          })
        }
      })
      
    }
  },
};
</script>

<style lang="scss" scoped>
.follow-list{
  position: fixed;
  right: 0px;
  bottom: 0px;
  z-index: 999;
  width: 275px;
  height: 793px;
  background: #ffffff;
  box-shadow: 0px 0px 13px #8c8c8c ;
  transition: all .35s linear;

  &.collapse{
    right: -275px;
    box-shadow:none;

    .slide-btn{

      &::before{
        left: -3px;
        width: 0px;
        height: 0px;
        margin-top: -6px;
        border: 6px solid transparent;
        border-left-color: transparent;
        border-right-color: #ffffff;
        background: none;
      }

      &::after{
        left: 11px;
        top: 50%;
        width: 2px;
        height: 10px;
        margin-top: -5px;
        background: #ffffff;
        border-radius: 1px;
        border: none;
      }

    }
  }
}

.slide-btn{
  position: absolute;
  left: -17px;
  top: 50%;
  width: 17px;
  height: 114px;
  margin-top: -57px;
  background-color: #3385ff;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  cursor: pointer;
  box-shadow: -1.5px 0px 5px #a0a6af ;

  &:hover{
    background-color: #5a9bfc;
  }

  &::before{
    content: "";
    position: absolute;
    left: 4px;
    top: 50%;
    width: 2px;
    height: 10px;
    margin-top: -5px;
    background: #ffffff;
    border-radius: 1px;
  }

  &::after{
    content: "";
    position: absolute;
    left: 8px;
    top: 50%;
    width: 0px;
    height: 0px;
    margin-top: -6px;
    border: 6px solid transparent;
    border-left-color: #ffffff;
  }
}

.follow-list-name{
  position: absolute;
  left:0px;
  top: 0px;
  width: 100%;
  text-align: center;
  background-color: #D7EDFF;
  color: #000000;
  font-size: 16px;
  line-height: 40px;

  .fresh-btn{
    position: absolute;
    left: 10px;
    top: 50%;
    margin-top: -10px;
    font-size: 20px;
    cursor: pointer;
    &:hover{
      color: #3385ff;
    }
  }

  .history-btn{
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -10px;
    font-size: 20px;
    cursor: pointer;
    &:hover{
      color: #3385ff;
    }
  }
}

.follow-container{
  position: relative;
  padding-top: 40px;
  padding-bottom: 40px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.history-follow{
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  padding-top: 6px;
  // line-height: 40px;
  // background-color: #80C2FF;
  background-color: #e3e8ed;
  color: #ffffff;
  font-size: 16px;
  text-align: center;
  cursor: pointer;

  // &:hover{
  //   background-color: #6db5f8;
  // }
}
.follow-list-content{
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
}

.follow-list-vec{
  text-align: center;
  padding: 6px;
  box-shadow: 0px 0px 4px #a0a6af;
  margin-bottom: 10px;
}

.lic-plate{
  position: relative;
  background-color: #F9C700;
  width: 185px;
  height: 48px;
  // line-height: 41px;        
  color: #000000;
  font-size: 24px;
  text-align: center;
  margin: 0 auto;
  display: inline-block;
  vertical-align: middle;
  border-radius: 5px;
  padding: 4px;
  box-sizing: border-box;

  &::before,&::after{
    content: "";
    position: absolute;
    top: 8px;
    width: 14px;
    height: 2px;
    background: #ffffff;
    border-radius: 1px;
  }

  &::before{
    left: 47px;
  }

  &::after{
    right: 47px;
  }

  .lic-plate-border{
    width: 100%;
    height: 100%;
    line-height: 36px;
    border: 2px solid #ffffff;
    display: inline-block;
    box-sizing: border-box;
    border-radius: 3px;

    &::before,&::after{
      content: "";
      position: absolute;
      bottom: 7px;
      width: 14px;
      height: 2px;
      background: #ffffff;
      border-radius: 1px;
    }

    &::before{
      left: 47px;
    }

    &::after{
      right: 47px;
    }
  }
}

.collect-btn{
  color: #ff3e3e;
  font-size: 24px;
  cursor: pointer;
  margin: 0 auto;
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
.time-line{
  padding: 6px;
  text-align: left;

  .time-line-box{
    transition: all .3s linear;
  }
  &.collapse{
    .time-line-box{
      height: 22px;
      transition: all .3s linear;
      overflow: hidden;
    }
  }
}

.time-line-item{
  padding-left: 20px;
  position: relative;
  padding-bottom: 20px;

  &::before{
    content: "";
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 1;
    width: 15.9px;
    height: 15.9px;
    background: #ffffff;
    border: 2px solid #409EFF;
    box-sizing: border-box;
    border-radius: 50%;
  }

  &::after{
    content: "";
    position: absolute;
    left: 6.75px;
    top: 0px;
    width: 2px;
    height: 100%;
    background: #409EFF;
  }

  &:last-child{
    &::after{
      background: #d4d4d4;
    }
  }

  &:last-child:first-child{
    &::after{
      display: none;
    }
  }

}

.expand-btn{
  width: 100%;
  background: #eeeeee;
  text-align: center;
  cursor: pointer;
  line-height: 20px;
  color: #a0a6af;

  .expand-text{
    display: inline-block;
    vertical-align: middle;
    display: none;
    transform: translateX(10px);
    transition: all 2s linear;
  }

  &:hover{
    color: #409EFF;
    .expand-text{
      display: inline-block;
      transform: translateX(0px);
      transition: all 1s linear;
    }
  }

}


@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.wait-handle-task{
  position: absolute;
  left: -11px;
  top: -4px;
  font-size: 14px;
  text-align: center;
  line-height: 19px;
  color: #ffffff;
  background: red;
  width: 19px;
  height: 19px;
  border-radius: 50%;
  animation: blink 1s infinite; 
}
</style>