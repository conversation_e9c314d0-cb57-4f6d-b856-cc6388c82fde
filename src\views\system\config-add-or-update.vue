<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="参数名" prop="paramKey">
        <el-input v-model="dataForm.paramKey" placeholder="参数名"></el-input>
      </el-form-item>
      <el-form-item label="参数值" prop="paramValue">
        <el-input type="textarea" rows="5" v-model="dataForm.paramValue" placeholder="参数值"></el-input>
        <el-button type="success" round size="small" @click="openEditDialog('json')">开启JSON格式化编辑</el-button>
        <el-button type="success" round size="small" @click="openEditDialog('richText')">开启富文本编辑</el-button>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="submitLoading">确定</el-button>
    </span>
    <el-dialog :title="`${editTitle}编辑参数值`" :close-on-click-modal="false" :visible.sync="editVisible"
      :append-to-body="true">
      <div v-if="editType === 'json'">
        <!-- <vue-json-editor
        v-model="resultInfo"  // 双向绑定数据
         :showBtns="false"   // 是否展示保存按钮
         :mode="'tree'"   // 默认模式tree,模式类型：tree,code,form,text,view
         lang="zh"  // zh(中文),en(英文)默认英文
         :expandedOnStart="true" // 在开始时结构为’tree’，‘view’和’form’，是否展开json编辑器
         @json-change="onJsonChange"  // 改变调用事件
         @json-save="onJsonSave"  // 保存调用事件
      /> -->
        <vue-json-editor v-model="editValue" :showBtns="false" :mode="'code'" @json-change="onJsonChange"
          class="json-editor" @json-save="onJsonSave" @has-error="onError" />

        <fieldset style="margin-top: 5px;" v-show="editError">
          <legend>编辑器错误提示如下：</legend>
          <div class="error">
            {{ editError }}
          </div>
        </fieldset>
      </div>
      <div v-else-if="editType === 'richText'">
        <wangeditor ref="wangeditor" v-model="editValue" placeholder="请输入参数值"></wangeditor>
        <fieldset style="margin-top: 5px;">
          <legend>参数值：</legend>
          <div class="error">
            {{ editValue }}
          </div>
        </fieldset>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :size="size" @click="editVisible = false">取消</el-button>
        <el-button v-if="editType" :size="size" type="primary"
          @click="editSubmit()">提&nbsp;&nbsp;交&nbsp;&nbsp;修&nbsp;&nbsp;改</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/system/config";
import wangeditor from "@/components/editor/wangeditor";
import vueJsonEditor from "vue-json-editor";
export default {
  data() {
    return {
      size: "small",
      visible: false,
      submitLoading: false,
      dataForm: {
        id: 0,
        paramKey: "",
        paramValue: "",
        remark: ""
      },
      richTextMode: false,
      dataRule: {
        paramKey: [{ required: true, message: "参数名不能为空", trigger: "blur" }],
        paramValue: [{ required: true, message: "参数值不能为空", trigger: "blur" }]
      },

      editType: "",
      editTitle: "",
      editVisible: false, // 编辑弹窗
      editValue: "", // 编辑值
      isEditValid: true,  // 编辑是否验证通过
      editError: ""
    };
  },
  watch: {
    editVisible(val) {
      if (!val) {
        // 关闭弹窗
        this.editType = null;
        this.editTitle = "";
        this.editValue = "";
        this.isEditValid = true;
        this.editError = "";


      }
    }
  },
  components: {
    wangeditor,
    vueJsonEditor
  },
  methods: {
    init(id) {
      let _this = this;
      this.dataForm.id = id || 0;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (this.dataForm.id) {
          $http
            .getConfigInfo(this.dataForm.id)
            .then(response => {
              if (response && response.code === 0) {
                _this.dataForm.paramKey = response.sysConfig.paramKey;
                _this.dataForm.paramValue = response.sysConfig.paramValue;
                _this.dataForm.remark = response.sysConfig.remark;
              }
            });
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      let _this = this;
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.submitLoading = true;
          let postData = Object.assign({}, this.dataForm);
          postData.id = this.dataForm.id || undefined;
          $http[`${this.dataForm.id ? "updConfig" : "addConfig"}`](postData).then(res => {
            _this.submitLoading = false;
            if (res && res.code === 0) {
              _this.visible = false;
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.$emit("refreshDataList");
                }
              });
            } else {
              _this.$message.error(res.msg);
            }
          }).catch(error => {
            _this.submitLoading = false;
          });
        }
      });
    },
    // 开启参数的编辑功能
    openEditDialog(type) {
      this.editType = type;
      if (type === "json") {
        this.openJSONEdit();
        this.editTitle = "JSON格式化";
      } else if (type === "richText") {
        this.openRichText();
        this.editTitle = "富文本";
      } else {
        this.$message.error(`很抱歉，目前尚未开放${type}的编辑功能。`);
      }
    },
    // 开启富文本编辑
    openRichText() {
      let val = this.dataForm.paramValue || "";
      let reg = /<[^>]+>/g;
      let isRichText = true;
      if (val) {
        if (!reg.test(val)) {
          isRichText = false;
        }
      }
      if (isRichText) {
        this.editVisible = true;
        this.$nextTick(() => {
          this.$refs["wangeditor"].editor.txt.html(this.dataForm.paramValue);
        });
      } else {
        this.$message.error("很抱歉，当前参数值无法使用富文本编辑");
      }
    },
    // 开启json编辑
    openJSONEdit() {
      let val = this.dataForm.paramValue;
      try {
        if (val && val.length) {
          this.editValue = JSON.parse(val);
        } else {
          this.editValue = {};
        }
        this.editVisible = true;
      } catch (error) {
        console.log(error);
        this.$message.error("很抱歉，当前参数值无法使用JSON格式化编辑");
      }
    },
    // json修改
    onJsonChange(value) {
      // console.log('更改value:', value);
      // 实时保存
      this.onJsonSave(value);
    },
    // json保存
    onJsonSave(value) {
      // console.log('保存value:', value);
      this.editValue = value;
      this.isEditValid = true;
      this.editError = "";
    },
    // json错误
    onError(value) {
      console.log("onError:" + value);
      this.editError = value;
      this.isEditValid = false;
    },
    // 提交json
    editSubmit() {
      if (this.isEditValid == false) {
        // console.log("json验证失败")
        // this.$message.error("json验证失败")
        this.$message.error("json验证失败，无法保存");
        return false;
      } else {
        // console.log("json验证成功")
        if (this.editType === "json") {
          try {
            this.dataForm.paramValue = JSON.stringify(this.editValue);
            this.editVisible = false;
          } catch (error) {
            this.$message.error("很抱歉无法保存json！");
          }
        } else if (this.editType === "richText") {
          this.dataForm.paramValue = this.editValue;
          this.editVisible = false;
        }

      }
    },
  }
};
</script>
<style lang="scss" scoped>
.error {
  color: #d00;
  font-size: 12px;
  line-height: 20px;
}

.json-editor {
  ::v-deep {
    .jsoneditor-vue {
      height: 600px;
    }
  }
}
</style>