<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="dataForm.type">
          <el-radio v-for="(type, index) in dataForm.typeList" :label="index" :key="index">{{ type }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所属模块" prop="sysName">
        <el-select @change="sysNameChange" v-model="dataForm.sysName" clearable placeholder="所属模块">
          <el-option v-for="item in moduleList" :key="item.id" :label="item.nmCn" :value="item.cd" />
          <!-- <el-option label="政府端" value="WHJK-PORTAL" />
          <el-option label="充装端" value="WHJK-CP" />
          <el-option label="运营端" value="WHJK-ADMIN" /> -->
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="menuNbr">
        <el-input @blur="menuNbrBlur" v-model="dataForm.menuNbr" placeholder="版本号"></el-input>
      </el-form-item>
      <el-form-item :label="dataForm.typeList[dataForm.type] + '名称'" prop="name">
        <el-input v-model="dataForm.name" :placeholder="dataForm.typeList[dataForm.type] + '名称'"></el-input>
      </el-form-item>
      <el-form-item label="上级菜单" prop="parentName" v-show="dataForm.type !== 0">
        <el-popover ref="menuListPopover" placement="bottom-start" trigger="click">
          <el-tree :data="menuList" :props="menuListTreeProps" node-key="menuId" ref="menuListTree"
            @current-change="menuListTreeCurrentChangeHandle" :default-expand-all="false" :highlight-current="true"
            :expand-on-click-node="false" style="height: 300px; overflow-y: auto">
          </el-tree>
        </el-popover>
        <el-input v-model="dataForm.parentName" v-popover:menuListPopover :readonly="true" placeholder="点击选择上级菜单"
          class="menu-list__input"></el-input>
      </el-form-item>
      <el-form-item label="菜单路由" prop="url">
        <el-input v-model="dataForm.url" placeholder="菜单路由"></el-input>
      </el-form-item>
      <el-form-item label="component" prop="component">
        <el-input v-model="dataForm.component" placeholder="component"></el-input>
      </el-form-item>
      <el-form-item label="授权标识" prop="perms">
        <el-input v-model="dataForm.perms" placeholder="多个用逗号分隔, 如: user:list,user:create"></el-input>
      </el-form-item>
      <el-form-item v-if="dataForm.type !== 2" label="排序号" prop="orderNum">
        <el-input-number v-model="dataForm.orderNum" controls-position="right" :min="0" label="排序号"></el-input-number>
      </el-form-item>
      <el-form-item v-if="dataForm.type === 1 || dataForm.type === 0" label="菜单图标" prop="icon">
        <el-row>
          <el-col :span="22">
            <el-popover ref="iconListPopover" placement="bottom-start" trigger="click"
              popper-class="mod-menu__icon-popover">
              <div class="mod-menu__icon-list">
                <el-button v-for="(item, index) in iconList" :key="index" @click="iconActiveHandle(item)"
                  :class="{ 'is-active': item === dataForm.icon }">
                  <svg-icon :icon-class="item"></svg-icon>
                </el-button>
              </div>
            </el-popover>
            <el-input v-model="dataForm.icon" v-popover:iconListPopover placeholder="菜单图标名称"
              class="icon-list__input"></el-input>
          </el-col>
          <el-col :span="2" class="icon-list__tips">
            <el-tooltip placement="top" effect="light">
              <div slot="content">
                全站推荐使用SVG Sprite, 详细请参考:<a href="//github.com/daxiongYang/renren-fast-vue/blob/master/src/icons/index.js"
                  target="_blank">icons/index.js</a>描述
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { treeDataTranslate } from "@/utils/tool";
import * as $http from "@/api/system/menu";
import Icon from "@/icons";
export default {
  data() {
    let validateUrl = (rule, value, callback) => {
      if (this.dataForm.type === 1 && !/\S/.test(value)) {
        callback(new Error("菜单URL不能为空"));
      } else {
        callback();
      }
    };

    let validateParentName = (rule, value, callback) => {
      if (this.dataForm.type == 0) {
        callback();
      } else {
        if (!/\S/.test(value)) callback(new Error("顶级菜单不能为空!"));
        else callback();
      }
    };

    return {
      visible: false,
      dataForm: {
        id: 0,
        type: 1,
        typeList: ["顶级菜单", "左侧菜单", "按钮", '路由'],
        name: "",
        sysName: "",
        parentId: 0,
        parentName: "",
        url: "",
        menuNbr: "",
        component: "",
        perms: "",
        orderNum: 0,
        icon: "",
        iconList: [],
        // areaId: "",
      },
      dataRule: {
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        parentName: [
          {
            /* required: true, message: '上级菜单不能为空', */ validator:
              validateParentName,
            trigger: "change",
          },
        ],
        url: [{ validator: validateUrl, trigger: "blur" }],
      },
      menuList: [],
      moduleList: [],
      menuListTreeProps: {
        label: "name",
        children: "children",
      },
      iconList: [],
      // areaOptions: [
      //   {
      //     label: "镇海区",
      //     value: "330211"
      //   },
      //   {
      //     label: "上虞区",
      //     value: "330604"
      //   }
      // ]
    };
  },
  // props: {
  //   regionOptions: {
  //     type: Array,
  //     default: [],
  //   },
  // },
  created() {
    this.iconList = Icon.getNameList();
    $http.getModuleList().then(res => {
      if (res.code == 0) {
        this.moduleList = res.data;
      } else {
        this.$message.error(res.msg || "请求失败");
      }
    });
  },
  methods: {
    init(row) {
      let param = {};
      param.areaId = "330211";
      param.sysName = "WHJK-PORTAL";
      param.menuNbr = "1.0";
      if (row) {
        this.dataForm.id = row.menuId || 0;
      } else {
        this.dataForm.id = 0;
      }
      $http
        .getMenuList(param)
        .then(response => {
          this.menuList = treeDataTranslate(response || [], "menuId");
        })
        .then(() => {
          this.visible = true;
          this.$nextTick(() => {
            this.$refs["dataForm"].resetFields();
          });
        })
        .then(() => {
          if (this.dataForm.id == 0) {
            // 新增
            this.menuListTreeSetCurrentNode();
          } else {
            // 修改
            $http.getMenuInfo(this.dataForm.id).then(data => {
              // this.dataForm.areaId = data.menu.areaId;
              this.dataForm.id = data.menu.menuId;
              this.dataForm.type = data.menu.type;
              this.dataForm.name = data.menu.name;
              this.dataForm.sysName = data.menu.sysName;
              this.dataForm.parentId = data.menu.parentId;
              this.dataForm.url = data.menu.url;
              this.dataForm.menuNbr = data.menu.menuNbr;
              this.dataForm.component = data.menu.component;
              this.dataForm.perms = data.menu.perms;
              this.dataForm.orderNum = data.menu.orderNum;
              this.dataForm.icon = data.menu.icon;
              this.menuListTreeSetCurrentNode();
            });
          }
        });
      // 获取菜单
    },

    // 菜单树选中
    menuListTreeCurrentChangeHandle(data, node) {
      this.dataForm.parentId = data.menuId;
      this.dataForm.parentName = data.name;
    },
    // 菜单树设置当前选中节点
    menuListTreeSetCurrentNode() {
      this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId);
      this.dataForm.parentName = (this.$refs.menuListTree.getCurrentNode() ||
        {})["name"];
    },
    // 图标选中
    iconActiveHandle(iconName) {
      this.dataForm.icon = iconName;
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          let postData = Object.assign({}, this.dataForm);
          postData.menuId = this.dataForm.id || undefined;
          delete postData.id;
          delete postData.typeList;
          delete postData.parentName;
          delete postData.iconList;

          if (postData.type === 0) {
            postData.parentId = 0;
            postData.parentName = "";
          }
          $http[`${this.dataForm.id ? "updMenu" : "addMenu"}`](postData).then(
            data => {
              if (data && data.code === 0) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  duration: 1500,
                  onClose: () => {
                    this.visible = false;
                    this.$emit("refreshDataList");
                  },
                });
              } else {
                this.$message.error(data.msg);
              }
            }
          );
        }
      });
    },
    // 根据版本号和所属模块获取上级菜单
    getTopMenu() {
      let param = {
        areaId: "330211",
        sysName: this.dataForm.sysName || "",
        menuNbr: this.dataForm.menuNbr || "",
      };

      $http.getMenuList(param).then(response => {
        this.menuList = treeDataTranslate(response || [], "menuId");
      });
    },
    sysNameChange() {
      this.getTopMenu();
    },
    menuNbrBlur() {
      this.getTopMenu();
    },
  },
};
</script>

<style lang="scss">
.mod-menu {

  .menu-list__input,
  .icon-list__input {
    >.el-input__inner {
      cursor: pointer;
    }
  }

  &__icon-popover {
    max-width: 370px;
  }

  &__icon-list {
    max-height: 180px;
    overflow-y: auto;
    padding: 0;
    margin: -8px 0 0 -8px;

    >.el-button {
      padding: 8px;
      margin: 8px 0 0 8px;

      >span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }

  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
