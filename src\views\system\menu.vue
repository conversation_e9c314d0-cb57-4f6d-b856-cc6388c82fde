<template>
  <div class="app-main-content">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()" @submit.native.prevent>
      <el-form-item prop="info" label="所属系统">
        <el-select size="small" v-model="dataForm.info" placeholder="所属系统" clearable @change="changeSystem">
          <el-option v-for="item in sysNameOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="name" label="菜单名称">
        <el-input size="small" v-model="dataForm.name" placeholder="菜单名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="type" label="菜单类型">
        <el-select size="small" v-model="dataForm.type" placeholder="菜单类型" clearable>
          <el-option v-for="(item, index) in menuTypeOption" :key="item.value + index" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="float: right;">
        <el-button size="small" @click="getDataList()">查询</el-button>
        <el-button size="small" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :height="tableHeight" :data="dataList" border style="width: 100%">
      <el-table-column prop="menuId" header-align="center" align="center" width="80" label="ID"></el-table-column>
      <table-tree-column prop="name" header-align="center" treeKey="menuId" width="150" label="名称"></table-tree-column>
      <el-table-column prop="parentName" header-align="center" align="center" width="120" label="上级菜单"></el-table-column>
      <el-table-column header-align="center" align="center" label="图标">
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" v-if="scope.row.icon" style="font-size: 20px"></svg-icon>
        </template>
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 0" size="small">目录</el-tag>
          <el-tag v-else-if="scope.row.type === 1" size="small" type="success">菜单</el-tag>
          <el-tag v-else-if="scope.row.type === 2" size="small" type="info">按钮</el-tag>
          <el-tag v-else-if="scope.row.type === 3" size="small" type="warning">路由</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" header-align="center" align="center" label="排序号"></el-table-column>
      <el-table-column prop="url" header-align="center" align="center" width="150" :show-overflow-tooltip="true"
        label="菜单URL"></el-table-column>
      <el-table-column prop="perms" header-align="center" align="center" width="150" :show-overflow-tooltip="true"
        label="授权标识"></el-table-column>
      <el-table-column prop="sysName" header-align="center" align="center" :show-overflow-tooltip="true" label="所属模块">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sysName === 'WHJK-ENTP'" size="small">企业端</el-tag>
          <el-tag v-else-if="scope.row.sysName === 'WHJK-PORTAL'" size="small" type="success">政府端</el-tag>
          <el-tag v-else-if="scope.row.sysName === 'WHJK-CP'" size="small" type="info">充装端</el-tag>
          <el-tag v-else-if="scope.row.sysName === 'WHJK-ADMIN'" size="small" type="warning">运营端</el-tag>
          <el-tag v-else-if="scope.row.sysName === 'WHJK-CK'" size="small" type="danger">登记点</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="menuNbr" header-align="center" align="center" label="版本号"></el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.menuId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"
      :regionOptions="regionOptions"></add-or-update>
  </div>
</template>

<script>
import { getZJDCProjectRegions, getProject, reSubmit } from "@/api/common";
import TableTreeColumn from "./components/table-tree-column";
import AddOrUpdate from "./menu-add-or-update";
import * as $http from "@/api/system/menu";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      regionOptions: [],
      tableHeight: Tool.getClientHeight() - 200,
      dataForm: {
        areaId: "330211",
        info: '',
      },
      dataList: [],
      dataListLoading: false,
      addOrUpdateVisible: false,
      sysNameOption: [],
      menuTypeOption: [
        { value: 0, label: "顶级菜单" },
        { value: 1, label: "左侧菜单" },
        { value: 2, label: "按钮" },
        { value: 3, label: "路由" },
        { value: 4, label: "tab" },
      ],
    };
  },

  components: {
    TableTreeColumn,
    AddOrUpdate,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created() {
    const that = this;
    window.addEventListener("resize", function () {
      that.tableHeight = Tool.getClientHeight() - 200;
    });

    this.$nextTick(function () {
      this.getProjectOption();
    });
  },
  mounted() { },
  methods: {
    // 获取数据列表
    getDataList() {
      let _this = this,
        postData = Object.assign({}, this.dataForm, {
          page: this.pageIndex,
          limit: this.pageSize,
          areaId: this.dataForm.areaId,
        });
      this.dataListLoading = true;
      $http
        .getMenuList(postData)
        .then(response => {
          _this.dataList = Tool.treeDataTranslate(response || [], "menuId");
          _this.dataListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.dataListLoading = false;
        });
    },

    changeSystem() {
      this.getDataList();
    },
    // 获取项目列表
    getProjectOption() {
      $http.getSysList(this.dataForm.areaId).then(res => {
        console.log(res)
        this.sysNameOption = res.map(item => {
          return { label: item.name + item.menuNbr, value: item.code + ' ' + item.menuNbr }
        });
        this.dataForm.info = this.sysNameOption[0].value;
        this.getDataList()


      })
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(row);
      });
    },
    // 删除
    deleteHandle(id) {
      let _this = this;
      this.$confirm(`确定对[id=${id}]进行[删除]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .deleteMenu(id)
          .then(response => {
            if (response && response.code === 0) {
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
  },
};
</script>
