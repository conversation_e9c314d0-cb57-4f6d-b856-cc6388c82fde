<template>
  <el-dialog :title="!dataForm.roleId ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible" width="80%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="dataForm.roleName" placeholder="角色名称"></el-input>
      </el-form-item>
      <span style="display:inline-block;margin:0 0 10px 80px;">注：政府端开头gov_。</span>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>

      <el-form-item label="所属模块" prop="sysName">
        <el-select v-model="sysName" clearable placeholder="所属模块" @change="sysNameChangeHandle()">
          <el-option v-for="item in moduleList" :key="item.id" :label="item.nmCn" :value="item.cd" />
          <!-- <el-option label="企业端" value="WHJK-ENTP" />
          <el-option label="政府端" value="WHJK-PORTAL" />
          <el-option label="充装端" value="WHJK-CP" />
          <el-option label="运营端" value="WHJK-ADMIN" /> -->
        </el-select>
      </el-form-item>

      <el-form-item size="mini" label="授权">
        <!-- <el-tree :data="menuList" :props="menuListTreeProps" node-key="menuId" ref="menuListTree" :default-expand-all="true" show-checkbox>
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }} --- <span v-if="areaDict[data.areaId]">{{areaDict[data.areaId]}}</span><span v-else>{{data.areaId}}</span></span>
            <span v-id="data.type===2"> --- 权限（{{data.perms}}）</span>
          </span>
        </el-tree> -->
        <el-tabs v-model="activeName">
          <el-tab-pane v-for="item in dataArr" :key="item.code" :label="item.code" :name="item.code">
            <el-checkbox style="padding-left:24px;"
              @change="handleCheckAllChange($event, item.data, item.code)">全选</el-checkbox>
            <el-tree :data="item.data" :props="menuListTreeProps" node-key="menuId" :ref="'menuListTree' + item.code"
              :default-expand-all="true" show-checkbox>
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }} ---
                  <span v-if="areaDict[data.areaId]">{{
                    areaDict[data.areaId]
                  }}</span><span v-else>{{ data.areaId }}</span></span>
                <span v-if="data.type === 2">
                  --- 权限（{{ data.perms }}）</span>
              </span>
            </el-tree>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { treeDataTranslate } from "@/utils/tool";
import * as $httpMenu from "@/api/system/menu";
import * as $http from "@/api/system/role";
export default {
  data() {
    return {
      visible: false,
      menuList: [],
      moduleList: [],
      menuListTreeProps: {
        label: "name",
        children: "children",
      },
      sysName: "",
      menuIdList: "",
      dataForm: {
        roleId: 0,
        roleName: "",
        remark: "",
      },
      dataRule: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
      },
      areaDict: {},
      tempKey: -666666, // 临时key, 用于解决tree半选中状态项不能传给后台接口问题. # 待优化
      dataObject: {},
      dataArr: [],
      activeName: "",
    };
  },
  created() {
    // this.getRegionInfo();
    $httpMenu.getModuleList().then((res) => {
      if (res.code == 0) {
        this.moduleList = res.data;
      } else {
        this.$message.error(res.msg || "请求失败");
      }
    });
  },
  methods: {
    getRegionInfo() {
      let _this = this;
      $httpCommon.getZJDCProjectRegions().then((res) => {
        console.log("区域信息：");
        console.log(res);
        if (res.code == 0) {
          res.data.forEach((it) => {
            _this.areaDict[it.key] = it.value;
          });
        }
      });
    },
    init(row) {
      this.dataForm.roleId = row && row.roleId ? row.roleId : 0;
      let param = {};
      if (row && row.roleName) {
        if (row.roleName.indexOf("gov_") >= 0) {
          param = { sysName: "WHJK-PORTAL" };
        }
        if (row.roleName.indexOf("checkPoint_") >= 0) {
          param = { sysName: "WHJK-CP" };
        }
        if (row.roleName.indexOf("entp_") >= 0) {
          param = { sysName: "WHJK-ENTP" };
        }
        if (row.roleName.indexOf("yy_") >= 0) {
          param = { sysName: "WHJK-ADMIN" };
        }
      } else if (row && row.sysName == "") {
        param = { sysName: row.sysName };
        console.log(param);
      }
      this.sysName = param.sysName;
      if (this.sysName == "") {
        this.dataArr = [];
        this.visible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].resetFields();
        });
      } else {
        $httpMenu
          .getMenuList(param)
          .then((response) => {
            let obj = {};
            // console.log(response)
            response.forEach((item) => {
              if (obj[item.menuNbr]) {
                obj[item.menuNbr] = obj[item.menuNbr].concat(item);
              } else {
                obj[item.menuNbr] = [].concat(item);
              }
            });
            this.dataObject = obj;
            this.dataArr = [];
            this.activeName = Object.keys(this.dataObject)[0];
            Object.keys(this.dataObject).forEach((key) => {
              this.dataArr.push({
                code: key,
                data: treeDataTranslate(this.dataObject[key] || [], "menuId"),
              });
            });
            // this.menuList = treeDataTranslate(response || [], "menuId");
          })
          .then(() => {
            this.visible = true;
            this.$nextTick(() => {
              this.$refs["dataForm"].resetFields();
              Object.keys(this.$refs).forEach((key) => {
                if (key.indexOf("menuListTree") > -1) {
                  if (this.$refs[key].length > 0) {
                    this.$refs[key][0].setCheckedKeys([]);
                  }
                }
              });
              // this.$refs.menuListTree.setCheckedKeys([]);
            });
          })
          .then(() => {
            if (this.dataForm.roleId) {
              $http.getRoleInfo(this.dataForm.roleId).then((data) => {
                if (data && data.code === 0) {
                  this.dataForm.roleName = data.role.roleName;
                  this.dataForm.remark = data.role.remark;
                  // var idx = data.role.menuIdList.indexOf(this.tempKey);
                  // if (idx !== -1) {
                  //   data.role.menuIdList.splice(
                  //     idx,
                  //     data.role.menuIdList.length - idx
                  //   );
                  // }
                  // this.menuIdList = data.role.menuIdList;
                  // console.log(data)
                  if (data.role.menuIdMap && data.role.menuIdMap.length > 0) {
                    data.role.menuIdMap.forEach((item) => {
                      if (item.menuIdList && item.menuIdList.length > 0) {
                        item.menuIdList.forEach((id) => {
                          this.$nextTick(() => {
                            if (this.$refs["menuListTree" + item.menuNbr]) {
                              let node =
                                this.$refs[
                                  "menuListTree" + item.menuNbr
                                ][0].getNode(id);
                              if (node && node.isLeaf) {
                                this.$refs[
                                  "menuListTree" + item.menuNbr
                                ][0].setChecked(node, true);
                              }
                            }
                          });
                        });
                      }
                    });
                  }
                  // this.$refs.menuListTree.setCheckedKeys(data.role.menuIdList);
                }
              });
            }
          });
      }
    },
    sysNameChangeHandle() {
      let param = { sysName: this.sysName };
      $httpMenu
        .getMenuList(param)
        .then((response) => {
          let obj = {};
          response.forEach((item) => {
            if (obj[item.menuNbr]) {
              obj[item.menuNbr] = obj[item.menuNbr].concat(item);
            } else {
              obj[item.menuNbr] = [].concat(item);
            }
          });
          this.dataObject = obj;
          this.dataArr = [];
          this.activeName = Object.keys(this.dataObject)[0];
          Object.keys(this.dataObject).forEach((key) => {
            this.dataArr.push({
              code: key,
              data: treeDataTranslate(this.dataObject[key] || [], "menuId"),
            });
          });
          // this.menuList = treeDataTranslate(response || [], "menuId");
        })
        .then(() => {
          this.visible = true;
          this.$nextTick(() => {
            Object.keys(this.$refs).forEach((key) => {
              if (key.indexOf("menuListTree") > -1) {
                if (this.$refs[key].length > 0) {
                  this.$refs[key][0].setCheckedKeys([]);
                }
              }
            });
            // this.$refs.menuListTree.setCheckedKeys(this.menuIdList);
            // this.$refs.menuListTree.setCheckedKeys([]);
          });
        });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          let postData = Object.assign({}, this.dataForm);
          postData.menuIdList = [];
          Object.keys(this.$refs).forEach((key) => {
            if (key.indexOf("menuListTree") > -1) {
              if (this.$refs[key].length > 0) {
                postData.menuIdList = postData.menuIdList.concat(
                  this.$refs[key][0].getCheckedKeys(),
                  this.$refs[key][0].getHalfCheckedKeys()
                );
              }
            }
          });
          $http[`${this.dataForm.roleId ? "updRole" : "addRole"}`](
            postData
          ).then((data) => {
            if (data && data.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    handleCheckAllChange(e, data, code) {
      if (e) {
        // 全选
        this.$refs["menuListTree" + code][0].setCheckedNodes(data);
      } else {
        // 取消选中
        this.$refs["menuListTree" + code][0].setCheckedKeys([]);
      }
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  height: 65vh;
  overflow: auto;
}
</style>