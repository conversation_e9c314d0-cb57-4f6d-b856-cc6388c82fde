<template>
  <div class="app-main-content">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        全区域角色
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="dataForm.roleName" placeholder="角色名称" clearable></el-input>
      </el-form-item>
      <el-form-item style="float: right;">
        <el-button size="small" @click="getDataList()">查询</el-button>
        <!-- <el-button size="small" v-permission="'sys:role:save'" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button size="small" v-permission="'sys:role:delete'" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
        <el-button size="small" type="primary" @click="addDialogHandle()">新增</el-button>
        <el-button size="small" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :height="tableHeight - 40" :data="dataList" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="roleId" header-align="center" align="center" width="80" label="ID">
      </el-table-column>
      <el-table-column prop="roleName" header-align="center" align="center" label="角色名称">
      </el-table-column>
      <el-table-column prop="remark" header-align="center" align="center" label="备注">
      </el-table-column>
      <el-table-column prop="createTime" header-align="center" align="center" width="180" label="创建时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="250" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.roleId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="currentChangeHandle"
        @size-change="sizeChangeHandle" :current-page="pageIndex" :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pageSize" :total="totalPage" style="float: right">
      </el-pagination>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <el-dialog title="请选择新增角色所属板块" :visible.sync="dialogVisible" width="30%">
      <el-button type="primary" @click="submitAddModeHandle('WHJK-ENTP')">企业端</el-button>
      <el-button type="primary" @click="submitAddModeHandle('WHJK-PORTAL')">政府端</el-button>
      <el-button type="primary" @click="submitAddModeHandle('WHJK-CP')">充装端</el-button>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from "./role-add-or-update";
import * as $http from "@/api/system/role";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      tableHeight: Tool.getClientHeight() - 200,
      dataForm: {
        roleName: "",
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dialogVisible: false,
    };
  },
  components: {
    AddOrUpdate,
  },
  created() {
    const that = this;
    window.addEventListener("resize", function () {
      that.tableHeight = Tool.getClientHeight() - 200;
    });

    this.$nextTick(function () {
      this.getDataList();
    });
  },
  methods: {
    // 获取数据列表
    getDataList() {
      let _this = this,
        postData = Object.assign({}, this.dataForm, {
          page: this.pageIndex,
          limit: this.pageSize,
        });
      this.dataListLoading = true;
      $http
        .getRoleList(postData)
        .then(response => {
          if (response && response.code === 0) {
            _this.dataList = response.page.list;
            _this.totalPage = response.page.totalCount;
          } else {
            _this.dataList = [];
            _this.totalPage = 0;
          }
          _this.dataListLoading = false;
        })
        .catch((error) => {
          console.log(error);
          _this.dataListLoading = false;
        });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(row);
      });
    },

    addDialogHandle() {
      // this.dialogVisible = true;
      this.addOrUpdateHandle({ sysName: "" });
    },

    submitAddModeHandle(mode) {
      this.dialogVisible = false;
      this.addOrUpdateHandle({ sysName: mode });
    },
    // 删除
    deleteHandle(id) {
      let _this = this;
      let ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
          return item.roleId;
        });
      this.$confirm(
        `确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        $http
          .deleteRole(ids)
          .then((response) => {
            if (response && response.code === 0) {
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.toolbar {
  height: 32px;
  padding: 3px;
}
</style>