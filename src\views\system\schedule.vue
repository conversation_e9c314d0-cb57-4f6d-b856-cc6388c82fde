<template>
  <div class="app-main-content">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <!--<el-form-item>
        <el-select v-model="dataForm.areaId" placeholder="区域" @change="areaChangeHandle" >
          <el-option label="镇海" value="330211" />
          <el-option label="上虞" value="330604" />
        </el-select>
      </el-form-item>-->
      <!-- <el-form-item prop="areaCode" :label="'行政区域'">
        <el-select clearable size="small" v-model="selectedRegion.areaCode" :placeholder="'请选择行政区域'"  @change="areaSelectChange">
          <el-option v-for="(item,index) in regionOptions" :key="index" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-input size="small" v-model="dataForm.beanName" placeholder="bean名称" clearable></el-input>
      </el-form-item>
      <el-form-item style="float: right;">
        <el-button size="small" @click="getDataList()">查询</el-button>
        <el-button size="small" v-permission="'sys:schedule:save'" type="primary"
          @click="addOrUpdateHandle()">新增</el-button>
        <el-button size="small" v-permission="'sys:schedule:delete'" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button size="small" v-permission="'sys:schedule:pause'" type="danger" @click="pauseHandle()"
          :disabled="dataListSelections.length <= 0">批量暂停</el-button>
        <el-button size="small" v-permission="'sys:schedule:resume'" type="danger" @click="resumeHandle()"
          :disabled="dataListSelections.length <= 0">批量恢复</el-button>
        <el-button size="small" v-permission="'sys:schedule:run'" type="danger" @click="runHandle()"
          :disabled="dataListSelections.length <= 0">批量立即执行</el-button>
        <el-button size="small" v-permission="'sys:schedule:log'" type="success" @click="logHandle()">日志列表</el-button>
      </el-form-item>
    </el-form>
    <el-table :height="tableHeight - 40" :data="dataList" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="jobId" header-align="center" align="center" width="80" label="ID"></el-table-column>
      <el-table-column prop="beanName" header-align="center" align="center" label="bean名称"></el-table-column>
      <el-table-column prop="methodName" header-align="center" align="center" label="方法名称"></el-table-column>
      <el-table-column prop="params" header-align="center" align="center" label="参数"></el-table-column>
      <el-table-column prop="cronExpression" header-align="center" align="center" label="cron表达式"></el-table-column>
      <el-table-column prop="remark" header-align="center" align="center" label="备注"></el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small">正常</el-tag>
          <el-tag v-else size="small" type="danger">暂停</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button v-permission="'sys:schedule:update'" type="text" size="small"
            @click="addOrUpdateHandle(scope.row.jobId)">修改</el-button>
          <el-button v-permission="'sys:schedule:delete'" type="text" size="small"
            @click="deleteHandle(scope.row.jobId)">删除</el-button>
          <el-button v-permission="'sys:schedule:pause'" type="text" size="small"
            @click="pauseHandle(scope.row.jobId)">暂停</el-button>
          <el-button v-permission="'sys:schedule:resume'" type="text" size="small"
            @click="resumeHandle(scope.row.jobId)">恢复</el-button>
          <el-button v-permission="'sys:schedule:run'" type="text" size="small"
            @click="runHandle(scope.row.jobId)">立即执行</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="currentChangeHandle"
        @size-change="sizeChangeHandle" :current-page="pageIndex" :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pageSize" :total="totalPage" style="float: right"></el-pagination>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 日志列表 -->
    <log v-if="logVisible" ref="log"></log>
  </div>
</template>

<script>
import { getZJDCProjectRegions, reSubmit } from "@/api/common";
import AddOrUpdate from "./schedule-add-or-update";
import * as $http from "@/api/system/schedule";
import Log from "./schedule-log";
import * as Tool from "@/utils/tool";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      regionOptions: [],
      tableHeight: Tool.getClientHeight() - 200,
      dataForm: {
        beanName: "",
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      logVisible: false,
    };
  },
  components: {
    AddOrUpdate,
    Log,
  },
  computed: {
    ...mapGetters(["selectedRegionCode", "ZJDCProjectRegions"]),
  },
  created() {
    const that = this;
    window.addEventListener("resize", function () {
      that.tableHeight = Tool.getClientHeight() - 200;
    });

    this.$nextTick(function () {
      this.getDataList();
    });
  },
  mounted() { },
  methods: {
    areaChangeHandle() {
      // this.$store.state.area.id = this.dataForm.areaId;
      this.getDataList();
      // console.log(this.dataForm.areaId)
      // console.log(this.$store.state.area.id)
    },
    // 获取数据列表
    getDataList() {
      let _this = this,
        postData = Object.assign({}, this.dataForm, {
          areaId: '330211',
          page: this.pageIndex,
          limit: this.pageSize,
        });
      console.log($http.getJobList);
      this.dataListLoading = true;
      $http
        .getJobList(postData)
        .then(response => {
          if (response && response.code === 0) {
            _this.dataList = response.page.list;
            _this.totalPage = response.page.totalCount;
          } else {
            _this.dataList = [];
            _this.totalPage = 0;
          }
          _this.dataListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.dataListLoading = false;
        });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 删除
    deleteHandle(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.jobId;
        });
      this.$confirm(`确定对[id=${ids.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.deleteJob(ids).then(data => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    // 暂停
    pauseHandle(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.jobId;
        });
      this.$confirm(`确定对[id=${ids.join(",")}]进行[${id ? "暂停" : "批量暂停"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.pauseJob(ids).then(data => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    // 恢复
    resumeHandle(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.jobId;
        });
      this.$confirm(`确定对[id=${ids.join(",")}]进行[${id ? "恢复" : "批量恢复"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.resumeJob(ids).then(data => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    // 立即执行
    runHandle(id) {
      let ids = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.jobId;
        });
      this.$confirm(`确定对[id=${ids.join(",")}]进行[${id ? "立即执行" : "批量立即执行"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http.runJob(ids).then(data => {
          if (data && data.code === 0) {
            this.$message({
              message: "操作成功",
              type: "success",
              duration: 1500,
              onClose: () => {
                this.getDataList();
              },
            });
          } else {
            this.$message.error(data.msg);
          }
        });
      });
    },
    // 日志列表
    logHandle() {
      this.logVisible = true;
      this.$nextTick(() => {
        this.$refs.log.init();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.toolbar {
  height: 32px;
  padding: 3px;
}
</style>