<template>
  <el-dialog :title="!dataForm.userId ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="dataForm.username" placeholder="登录帐号"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password" :class="{ 'is-required': !dataForm.userId }">
        <el-input v-model="dataForm.password" type="password" placeholder="密码"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="comfirmPassword" :class="{ 'is-required': !dataForm.userId }">
        <el-input v-model="dataForm.comfirmPassword" type="password" placeholder="确认密码"></el-input>
      </el-form-item>
      <el-form-item label="用户真名" prop="userNm">
        <el-input v-model="dataForm.userNm" placeholder="用户真名"></el-input>
      </el-form-item>
      <el-form-item label="中文名" prop="ipName">
        <el-input v-model="dataForm.ipName" placeholder="中文名"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
      </el-form-item>
      <!-- <el-form-item label="所属系统" prop="sysName">
        <el-select v-model="dataForm.sysName" placeholder="所属系统" @change="sysNameChange">
          <el-option v-for="item in moduleList" :key="item.id" :label="item.nmCn" :value="item.cd" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="角色" size="mini" prop="roleIdList">
        <el-checkbox-group v-model="dataForm.roleIdList">
          <el-checkbox v-for="role in roleList" :key="role.roleId" :label="role.roleId">{{ role.remark }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
	  <el-form-item label="有效期至" size="mini" prop="expireTm">
        <el-date-picker v-model="dataForm.expireTm" type="date" :picker-options="pickerOptions"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" size="mini" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">正常</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmail, isMobile } from "@/utils/validate";
import * as $http from "@/api/system/user";
import * as $httpRole from "@/api/system/role";

export default {
  data() {
    var validatePassword = (rule, value, callback) => {
      if (!this.dataForm.userId && !/\S/.test(value)) {
        callback(new Error("密码不能为空"));
      } else {
        callback();
      }
    };
    var validateComfirmPassword = (rule, value, callback) => {
      if (!this.dataForm.userId && !/\S/.test(value)) {
        callback(new Error("确认密码不能为空"));
      } else if (this.dataForm.password !== value) {
        callback(new Error("确认密码与密码输入不一致"));
      } else {
        callback();
      }
    };

    var validateMobile = (rule, value, callback) => {
      if (!isMobile(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    return {
      visible: false,
      roleList: [],
      dataForm: {
        userId: 0,
        username: "",
        password: "",
        userNm: "",
        sysName: "WHJK-PORTAL",
        comfirmPassword: "",
        salt: "",
        ipName: "",
        mobile: "",
        roleIdList: [],
        status: 1,
		expireTm: null
      },
      dataRule: {
        username: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
        userNm: [{ required: true, message: "用户真名不能为空", trigger: "blur" }],
        password: [{ validator: validatePassword, trigger: "blur" }],
        comfirmPassword: [{ validator: validateComfirmPassword, trigger: "blur" }],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          { validator: validateMobile, trigger: "blur" },
        ],
        sysName: [{ required: true, message: "所属系统不能为空", trigger: "blur" }],
        roleIdList: [{ required: true, message: "角色不能为空", trigger: "blur" }],
      },
      moduleList: [], // 系统列表
	  pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now();
        },
        shortcuts: [
          {
            text: "1天",
            onClick(picker) {
              const end = new Date();
              end.setDate(end.getDate() + 1);
              picker.$emit("pick", end);
            }
          },
          {
            text: "7天",
            onClick(picker) {
              const end = new Date();
              end.setDate(end.getDate() + 7);
              picker.$emit("pick", end);
            }
          },
          {
            text: "1个月",
            onClick(picker) {
              const end = new Date();
              end.setMonth(end.getMonth() + 1);
              picker.$emit("pick", end);
            }
          },
          {
            text: "3个月",
            onClick(picker) {
              const end = new Date();
              end.setMonth(end.getMonth() + 3);
              picker.$emit("pick", end);
            }
          },
          {
            text: "6个月",
            onClick(picker) {
              const end = new Date();
              end.setMonth(end.getMonth() + 6);
              picker.$emit("pick", end);
            }
          },
          {
            text: "1年",
            onClick(picker) {
              const end = new Date();
              end.setFullYear(end.getFullYear() + 1);
              picker.$emit("pick", end);
            }
          },
          {
            text: "永久",
            onClick(picker) {
              const end = new Date("9999-12-31");
              picker.$emit("pick", end);
            }
          }
        ]
      }
    };
  },
  created() {
    this.sysNameChange()
  },
  methods: {
    init(id) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        this.dataForm.roleIdList = [];
      });
      this.dataForm.userId = id || 0;
      let _this = this;
      if (this.dataForm.userId) {
        $http.getUserInfo(this.dataForm.userId).then(response => {
          if (response && response.code === 0) {
            _this.dataForm.username = response.user.username;
            _this.dataForm.userNm = response.user.userNm;
            _this.dataForm.salt = response.user.salt;
            _this.dataForm.ipName = response.user.ipName;
            _this.dataForm.mobile = response.user.mobile;
            _this.dataForm.status = response.user.status;
            _this.dataForm.sysName = response.user.sysName;
            _this.dataForm.roleIdList = response.user.roleIdList;
			_this.dataForm.expireTm = response.user.expireTm;
            _this.sysNameChange()
          }
        });
      }
    },
    // 切换所属系统获取角色列表
    sysNameChange() {
      let params = {
        sysName: this.dataForm.sysName,
      };
      $httpRole.getSelectRole(params).then(response => {
        this.roleList = response && response.code === 0 ? response.list : [];
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          let postData = Object.assign({}, this.dataForm);
          postData.userId = this.dataForm.userId || undefined;
          delete postData.id;
          delete postData.comfirmPassword;
          $http[`${this.dataForm.userId ? "updUser" : "addUser"}`](postData).then(res => {
            if (res && res.code === 0) {
              this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  this.visible = false;
                  this.$emit("refreshDataList");
                },
              });
            }
          });
        }
      });
    },
  },
};
</script>
