<template>
  <div class="app-main-content">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()" @submit.native.prevent>
      <el-form-item>
        <el-input size="small" v-model="dataForm.username" placeholder="用户名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="dataForm.ipName" placeholder="名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item>
        <el-input
          size="small"
          v-model="dataForm.token"
          placeholder="token"
          clearable
        ></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-input size="small" v-model="dataForm.mobile" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <el-form-item style="float: right;">
        <el-button size="small" @click="getDataList()">查询</el-button>
        <el-button size="small" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button size="small" type="danger" @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table :height="tableHeight - 40" :data="dataList" border v-loading="dataListLoading"
      @selection-change="selectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" min-width="50"></el-table-column>
      <el-table-column prop="userId" header-align="center" align="center" min-width="80" label="ID"></el-table-column>
      <el-table-column prop="username" header-align="center" align="center" label="用户名"></el-table-column>
      <el-table-column prop="userNm" header-align="center" align="center" label="用户真名"></el-table-column>
      <el-table-column prop="ipName" header-align="center" align="center" label="名称"></el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="手机号"></el-table-column>
      <el-table-column prop="createTime" header-align="center" align="center" width="140" label="创建时间"></el-table-column>
      <el-table-column prop="loginTime" header-align="center" align="center" min-width="140"
        label="登录时间"></el-table-column>
      <el-table-column prop="loginIp" header-align="center" align="center" label="登录IP"></el-table-column>
      <!-- <el-table-column prop="token" header-align="center" align="center" label="登录token"
        min-width="180"></el-table-column> -->
      <!-- <el-table-column prop="sysId" header-align="center" align="center" label="区域ID" min-width="70"></el-table-column> -->
      <el-table-column prop="status" header-align="center" align="center" label="状态" min-width="60">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="140" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.userId)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.userId)">删除</el-button>
          <el-button type="text" size="small" @click="unlockRole(scope.row)">解锁登陆限制</el-button>
          <el-button type="text" size="small" @click="resetPwdHandle(scope.row.userId)">重置密码</el-button>
          <el-button type="text" size="small" @click="forceLoginOut(scope.row.username)">强制注销</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="toolbar">
      <el-pagination background layout="sizes, prev, pager, next, total" @current-change="currentChangeHandle"
        @size-change="sizeChangeHandle" :current-page="pageIndex" :page-sizes="[20, 30, 50, 100, 200]"
        :page-size="pageSize" :total="totalPage" style="float: right"></el-pagination>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import { getZJDCProjectRegions, reSubmit } from "@/api/common";
import AddOrUpdate from "./user-add-or-update";
import * as $http from "@/api/system/user";
import * as $httpMenu from "@/api/system/menu";
import * as Tool from "@/utils/tool";
export default {
  data() {
    return {
      regionOptions: [],
      selectedRegion: { areaCode: "" },
      tableHeight: Tool.getClientHeight() - 200,
      dataForm: {
        username: "",
        // areaId: "",
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 20,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      moduleList: [],
    };
  },
  components: {
    AddOrUpdate,
  },
  created() {
    const that = this;
    window.addEventListener("resize", function () {
      that.tableHeight = Tool.getClientHeight() - 200;
    });
    $httpMenu.getModuleList().then(res => {
      console.log(res)
      if (res.code == 0) {
        this.moduleList = res.data;
      } else {
        this.$message.error(res.msg || "请求失败");
      }
    });

    this.$nextTick(function () {
      this.getDataList();
    });
  },
  mounted() {
    //获取行政区域
    // getZJDCProjectRegions().then(res => {
    //   if (res.code === 0) {
    //     this.regionOptions = res.data.map(item => {
    //       return { label: item.value, value: item.key };
    //     });
    //   }
    // });
  },
  methods: {
    //行政区域选择
    systemSelectChange(val) {
      this.getDataList();
    },
    // 获取数据列表
    getDataList() {
      let _this = this,
        postData = Object.assign({}, this.dataForm, {
          page: this.pageIndex,
          limit: this.pageSize,
        });
      this.dataListLoading = true;
      $http
        .getUserList(postData)
        .then(res => {
          if (res && res.code === 0) {
            _this.dataList = res.page.list;
            _this.totalPage = res.page.totalCount;
          } else {
            _this.dataList = [];
            _this.totalPage = 0;
          }
          _this.dataListLoading = false;
        })
        .catch(error => {
          console.log(error);
          _this.dataListLoading = false;
        });
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      this.getDataList();
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val;
      this.getDataList();
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id);
      });
    },
    // 删除
    deleteHandle(id) {
      let _this = this;
      let userIds = id
        ? [id]
        : this.dataListSelections.map(item => {
          return item.userId;
        });
      this.$confirm(`确定对[id=${userIds.join(",")}]进行[${id ? "删除" : "批量删除"}]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .deleteUser(userIds)
          .then(response => {
            if (response && response.code === 0) {
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    // 重置密码
    resetPwdHandle(id) {
      let _this = this;
      this.$confirm(`确定进行[重置密码]操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .resetPassword(id)
          .then(response => {
            if (response && response.code === 0) {
              _this.getDataList();
              _this.$message({
                message: "操作成功，重置后的密码：" + response.data,
                type: "success",
                duration: 0,
                showClose: true,
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    forceLoginOut(name) {
      let _this = this;

      this.$confirm(`确定对[用户=${name}]进行用户强制注销操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .LogOutRole(name)
          .then(response => {
            if (response && response.code === 0) {
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    unlockRole(row) {
      let _this = this;
      this.$confirm(`确定对用户${row.username}解锁登陆限制?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        $http
          .unlockRole(row.username)
          .then(response => {
            if (response && response.code === 0) {
              _this.$message({
                message: "操作成功",
                type: "success",
                duration: 1500,
                onClose: () => {
                  _this.getDataList();
                },
              });
            } else {
              _this.$message.error(response.msg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.toolbar {
  height: 32px;
  padding: 3px;
}
</style>
